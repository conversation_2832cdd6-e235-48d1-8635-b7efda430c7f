{"ast": null, "code": "import { SimService } from \"src/app/service/sim/SimService\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { GroupSimService } from \"src/app/service/group-sim/GroupSimService\";\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\nimport { RatingPlanService } from \"src/app/service/rating-plan/RatingPlanService\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/service/account/ProvinceService\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"../../common-module/table/table.component\";\nimport * as i10 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i11 from \"primeng/splitbutton\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/calendar\";\nimport * as i14 from \"primeng/dialog\";\nimport * as i15 from \"primeng/card\";\nimport * as i16 from \"primeng/togglebutton\";\nimport * as i17 from \"primeng/inputtextarea\";\nimport * as i18 from \"primeng/panel\";\nimport * as i19 from \"primeng/skeleton\";\nimport * as i20 from \"src/app/service/sim/SimService\";\nimport * as i21 from \"src/app/service/group-sim/GroupSimService\";\nimport * as i22 from \"src/app/service/customer/CustomerService\";\nimport * as i23 from \"src/app/service/rating-plan/RatingPlanService\";\nfunction AppSimListComponent_ng_template_10_Template(rf, ctx) {}\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction AppSimListComponent_ng_template_11_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"input\", 77);\n    i0.ɵɵlistener(\"keydown.enter\", function AppSimListComponent_ng_template_11_div_0_Template_input_keydown_enter_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.quickSearch());\n    })(\"ngModelChange\", function AppSimListComponent_ng_template_11_div_0_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.quickSearchValue = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 78);\n    i0.ɵɵlistener(\"click\", function AppSimListComponent_ng_template_11_div_0_Template_p_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.quickSearch());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"placeholder\", ctx_r17.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r17.quickSearchValue)(\"ngModelOptions\", i0.ɵɵpureFunction0(3, _c0));\n  }\n}\nfunction AppSimListComponent_ng_template_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.tranService.translate(\"global.text.advanceSearch\"));\n  }\n}\nfunction AppSimListComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AppSimListComponent_ng_template_11_div_0_Template, 3, 4, \"div\", 74);\n    i0.ɵɵtemplate(1, AppSimListComponent_ng_template_11_div_1_Template, 2, 1, \"div\", 75);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchPanelCollaps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchPanelCollaps);\n  }\n}\nfunction AppSimListComponent_p_button_84_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 80);\n    i0.ɵɵlistener(\"click\", function AppSimListComponent_p_button_84_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      ctx_r23.isShowDialogCreateGroup = true;\n      return i0.ɵɵresetView(ctx_r23.isShowDialogPushGroup = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"pTooltip\", ctx_r2.tranService.translate(\"global.button.add\"));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 86)(1, \"div\", 87)(2, \"div\", 88)(3, \"div\", 89)(4, \"span\", 90);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 91)(9, \"span\", 90);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 92);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 93)(14, \"span\", 90);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 56);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 93)(19, \"span\", 90);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 56);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 93)(24, \"span\", 90);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 56);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 91)(29, \"span\", 90);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 94);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 88)(34, \"div\", 89)(35, \"span\", 90);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 56);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 93)(41, \"span\", 90);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 92);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r25.tranService.translate(\"sim.text.simInfo\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.sothuebao\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.detailSim.msisdn);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.trangthaisim\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r25.getClassStatus(ctx_r25.detailSim.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.getNameStatus(ctx_r25.detailSim.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.imsi\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.detailSim.imsi);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.imeiDevice\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.detailSim.imei);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.maapn\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.detailSim.apnId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.trangthaiketnoi\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r25.getSimStatus(ctx_r25.detailSim.connectionStatus) == \"ON\" ? \"text-green-800 bg-green-100\" : \"text-50 surface-500\")(\"pTooltip\", ctx_r25.getTooltipStatus(ctx_r25.detailSim.connectionStatus));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.getSimStatus(ctx_r25.detailSim.connectionStatus));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.startDate\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(39, 21, ctx_r25.detailSim.startDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"sim.label.serviceType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.getServiceType(ctx_r25.detailSim.serviceType));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 86)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"p-skeleton\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r26.tranService.translate(\"sim.text.simInfo\"));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-card\", 96)(1, \"div\", 89)(2, \"div\", 97)(3, \"p-toggleButton\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.detailStatusSim.statusData = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 97)(7, \"p-toggleButton\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.detailStatusSim.statusReceiveCall = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 97)(11, \"p-toggleButton\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.detailStatusSim.statusSendCall = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 89)(15, \"div\", 97)(16, \"p-toggleButton\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.detailStatusSim.statusWorldCall = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 97)(20, \"p-toggleButton\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.detailStatusSim.statusReceiveSms = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 97)(24, \"p-toggleButton\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.detailStatusSim.statusSendSms = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r27.tranService.translate(\"sim.text.simStatusInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.detailStatusSim.statusData)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"sim.status.service.data\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.detailStatusSim.statusReceiveCall)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"sim.status.service.callReceived\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.detailStatusSim.statusSendCall)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"sim.status.service.callSent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.detailStatusSim.statusWorldCall)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"sim.status.service.callWorld\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.detailStatusSim.statusReceiveSms)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"sim.status.service.smsReceived\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.detailStatusSim.statusSendSms)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"sim.status.service.smsSent\"));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 99)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"p-skeleton\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r28.tranService.translate(\"sim.text.simStatusInfo\"));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 99)(1, \"div\", 89)(2, \"span\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 93)(7, \"span\", 100);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r29.tranService.translate(\"sim.text.ratingPlanInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"sim.label.tengoicuoc\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.detailSim.ratingPlanName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"sim.label.dataUseInMonth\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(11, 6, ctx_r29.utilService.bytesToMegabytes(ctx_r29.detailRatingPlan.dataUseInMonth)), \" \", ctx_r29.detailRatingPlan.unit ? ctx_r29.detailRatingPlan.unit : \"MB\", \"\");\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 99)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"p-skeleton\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r30.tranService.translate(\"sim.text.ratingPlanInfo\"));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 86)(1, \"div\", 101)(2, \"span\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 93)(7, \"span\", 100);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 93)(13, \"span\", 100);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 102);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 93)(18, \"span\", 100);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 56);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 93)(23, \"span\", 100);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 56);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 93)(28, \"span\", 100);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 56);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 93)(33, \"span\", 100);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 102);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 93)(38, \"span\", 100);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\", 56);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 93)(43, \"span\", 100);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\", 56);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r31.tranService.translate(\"sim.text.contractInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.mahopdong\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.contractCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.ngaylamhopdong\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 19, ctx_r31.detailContract.contractDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.nguoilamhopdong\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.contractorInfo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.matrungtam\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.centerCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.dienthoailienhe\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.contactPhone);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.diachilienhe\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.contactAddress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.paymentName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.paymentName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.paymentAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.paymentAddress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"sim.label.routeCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.detailContract.routeCode);\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 86)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"p-skeleton\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r32.tranService.translate(\"sim.text.contractInfo\"));\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 99)(1, \"div\", 89)(2, \"span\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 93)(7, \"span\", 100);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r33.tranService.translate(\"sim.text.customerInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r33.tranService.translate(\"sim.label.khachhang\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r33.detailCustomer.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r33.tranService.translate(\"sim.label.customerCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r33.detailCustomer.code);\n  }\n}\nfunction AppSimListComponent_p_dialog_89_p_card_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 99)(1, \"div\", 95);\n    i0.ɵɵelement(2, \"p-skeleton\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"header\", ctx_r34.tranService.translate(\"sim.text.customerInfo\"));\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nfunction AppSimListComponent_p_dialog_89_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 41);\n    i0.ɵɵlistener(\"visibleChange\", function AppSimListComponent_p_dialog_89_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.isShowModalDetailSim = $event);\n    });\n    i0.ɵɵelementStart(1, \"div\", 81)(2, \"div\", 82);\n    i0.ɵɵtemplate(3, AppSimListComponent_p_dialog_89_p_card_3_Template, 45, 24, \"p-card\", 83);\n    i0.ɵɵtemplate(4, AppSimListComponent_p_dialog_89_p_card_4_Template, 3, 1, \"p-card\", 83);\n    i0.ɵɵtemplate(5, AppSimListComponent_p_dialog_89_p_card_5_Template, 27, 19, \"p-card\", 84);\n    i0.ɵɵtemplate(6, AppSimListComponent_p_dialog_89_p_card_6_Template, 3, 1, \"p-card\", 85);\n    i0.ɵɵtemplate(7, AppSimListComponent_p_dialog_89_p_card_7_Template, 12, 8, \"p-card\", 85);\n    i0.ɵɵtemplate(8, AppSimListComponent_p_dialog_89_p_card_8_Template, 3, 1, \"p-card\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 82);\n    i0.ɵɵtemplate(10, AppSimListComponent_p_dialog_89_p_card_10_Template, 47, 22, \"p-card\", 83);\n    i0.ɵɵtemplate(11, AppSimListComponent_p_dialog_89_p_card_11_Template, 3, 1, \"p-card\", 83);\n    i0.ɵɵtemplate(12, AppSimListComponent_p_dialog_89_p_card_12_Template, 11, 5, \"p-card\", 85);\n    i0.ɵɵtemplate(13, AppSimListComponent_p_dialog_89_p_card_13_Template, 3, 1, \"p-card\", 85);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(17, _c1));\n    i0.ɵɵproperty(\"header\", ctx_r3.tranService.translate(\"sim.text.detailSim\"))(\"visible\", ctx_r3.isShowModalDetailSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailSim).length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailSim).length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailStatusSim).length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailStatusSim).length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailRatingPlan).length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailRatingPlan).length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailContract).length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailContract).length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailCustomer).length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.Object.keys(ctx_r3.detailCustomer).length === 0);\n  }\n}\nfunction AppSimListComponent_span_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"groupSim.scope.admin\"));\n  }\n}\nfunction AppSimListComponent_span_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"groupSim.scope.province\"));\n  }\n}\nfunction AppSimListComponent_span_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"groupSim.scope.customer\"));\n  }\n}\nfunction AppSimListComponent_small_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c2 = function () {\n  return {\n    len: 16\n  };\n};\nfunction AppSimListComponent_small_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppSimListComponent_small_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    type: a0\n  };\n};\nfunction AppSimListComponent_small_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c3, ctx_r10.tranService.translate(\"sim.label.groupKey\").toLowerCase())));\n  }\n}\nfunction AppSimListComponent_small_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c4 = function () {\n  return {\n    len: 255\n  };\n};\nfunction AppSimListComponent_small_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c4)));\n  }\n}\nfunction AppSimListComponent_small_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction AppSimListComponent_div_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"label\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"sim.label.khachhang\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r14.customerName, \" - \", ctx_r14.customerCode, \" \");\n  }\n}\nfunction AppSimListComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"label\", 103);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r15.provinceName, \" (\", ctx_r15.provinceCode, \") \");\n  }\n}\nfunction AppSimListComponent_small_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c4)));\n  }\n}\nconst _c5 = function () {\n  return {\n    type: \"groupSim\"\n  };\n};\nconst _c6 = function () {\n  return {\n    top: \"40px\",\n    right: \"0px\",\n    \"min-width\": \"100%\"\n  };\n};\nconst _c7 = function () {\n  return {\n    type: \"ratingPlan\"\n  };\n};\nconst _c8 = function () {\n  return {\n    type: \"contract\"\n  };\n};\nconst _c9 = function () {\n  return {\n    type: \"customer\"\n  };\n};\nconst _c10 = function () {\n  return {\n    width: \"500px\"\n  };\n};\nconst _c11 = function (a0) {\n  return [a0];\n};\nexport class AppSimListComponent extends ComponentBase {\n  // tranService: TranslateService;\n  constructor(simService, formBuilder, groupSimService, customerService, ratingPlanService, provinceService, injector) {\n    super(injector);\n    this.simService = simService;\n    this.formBuilder = formBuilder;\n    this.groupSimService = groupSimService;\n    this.customerService = customerService;\n    this.ratingPlanService = ratingPlanService;\n    this.provinceService = provinceService;\n    this.injector = injector;\n    this.maxDateFrom = new Date();\n    this.minDateTo = null;\n    this.maxDateTo = new Date();\n    this.isShowDialogCreateGroup = false;\n    this.isShowDialogPushGroup = false;\n    this.groupScopeObjects = CONSTANTS.GROUP_SCOPE;\n    this.allPermissions = CONSTANTS.PERMISSIONS;\n    this.paramSearchGroupSim = {};\n    this.listProvince = [];\n    this.isShowModalDeleteSim = false;\n    this.isShowModalDetailSim = false;\n    this.detailSim = {};\n    this.detailStatusSim = {};\n    this.detailCustomer = {};\n    this.detailRatingPlan = {};\n    this.detailContract = {};\n    this.detailAPN = {};\n    this.searchPanelCollaps = true;\n    this.quickSearchValue = null;\n    this.isSearchAfterOpenChange = false;\n    this.isSearchAfterCollapChange = false;\n    this.Object = Object;\n  }\n  addTooltip(event) {\n    setTimeout(() => {\n      const toggler = event.originalEvent.target.closest('.p-panel-titlebar-toggler');\n      if (toggler) {\n        toggler.setAttribute('pTooltip', 'Toggle Panel');\n        toggler.setAttribute('tooltipPosition', 'top');\n      }\n    });\n  }\n  ngOnInit() {\n    let me = this;\n    this.userType = this.sessionService.userInfo.type;\n    this.optionuserType = CONSTANTS.USER_TYPE;\n    this.selectItems = [];\n    this.detailSim = {};\n    this.searchInfoStandard = {\n      msisdn: null,\n      imsi: null,\n      ratingPlanId: null,\n      contractCode: null,\n      contractor: null,\n      status: null,\n      simGroupId: null,\n      customer: null,\n      dateFrom: null,\n      dateTo: null,\n      apnId: null\n    };\n    this.searchInfo = {\n      msisdn: null,\n      imsi: null,\n      ratingPlanId: null,\n      contractCode: null,\n      contractor: null,\n      status: null,\n      simGroupId: null,\n      customer: null,\n      dateFrom: null,\n      dateTo: null,\n      apnId: null,\n      simType: null,\n      provinceCode: null,\n      userId: null,\n      loggable: null\n    };\n    this.dataCreateGroupSim = {\n      groupKey: null,\n      name: null,\n      description: null\n    };\n    this.quickSearchValue = \"\";\n    this.formSearchSim = this.formBuilder.group(this.searchInfo);\n    this.formCreateGroupSim = this.formBuilder.group(this.dataCreateGroupSim);\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.simmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listsim\")\n    }];\n    this.itemExports = this.itemExports = [{\n      label: this.tranService.translate(\"global.button.exportExelSelect\"),\n      command: () => {\n        if (me.selectItems.length == 0 || me.selectItems.length > CONSTANTS.MAX_ROW_EXCEL_EXPORT) {\n          me.helpExelExport(0);\n          return;\n        }\n        me.messageCommonService.onload();\n        me.simService.exportExelSelected({\n          lstMsisdn: me.selectItems.map(el => el.msisdn)\n        });\n      }\n    }, {\n      label: this.tranService.translate(\"global.button.exportExelFilter\"),\n      command: () => {\n        if (me.checkDisabledExportExelFilter()) {\n          me.helpExelExport(1);\n          return;\n        }\n        me.messageCommonService.onload();\n        let dataParams = {};\n        me.updateParams(dataParams);\n        delete dataParams.page;\n        delete dataParams.size;\n        delete dataParams.sort;\n        if (!this.isSearchAfterOpenChange && !this.searchPanelCollaps || this.isSearchAfterCollapChange && this.searchPanelCollaps && this.quickSearchValue != null && this.quickSearchValue != undefined) dataParams = {\n          'keySearch': this.quickSearchValue\n        };\n        me.simService.exportExels(dataParams);\n      }\n    }, {\n      label: this.tranService.translate(\"global.button.exportSelect\"),\n      command: () => {\n        if (me.selectItems.length == 0 || me.selectItems.length > CONSTANTS.MAX_ROW_EXPORT) {\n          me.helpExport(0);\n          return;\n        }\n        me.messageCommonService.onload();\n        me.simService.exportSimSelected({\n          lstMsisdn: me.selectItems.map(el => el.msisdn)\n        });\n      }\n    }, {\n      label: this.tranService.translate(\"global.button.exportFilter\"),\n      command: () => {\n        if (me.checkDisabledExportFilter()) {\n          me.helpExport(1);\n          return;\n        }\n        me.messageCommonService.onload();\n        let dataParams = {};\n        me.updateParams(dataParams);\n        delete dataParams.page;\n        delete dataParams.size;\n        delete dataParams.sort;\n        if (!this.isSearchAfterOpenChange && !this.searchPanelCollaps || this.isSearchAfterCollapChange && this.searchPanelCollaps && this.quickSearchValue != null && this.quickSearchValue != undefined) dataParams = {\n          'keySearch': this.quickSearchValue\n        };\n        me.simService.exportSim(dataParams);\n      }\n    }];\n    this.itemPushGroups = [{\n      label: this.tranService.translate(\"groupSim.scope.admin\"),\n      command: () => {\n        if (me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN)) {\n          me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_ADMIN;\n          me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN);\n          me.isShowDialogPushGroup = true;\n        }\n      },\n      visible: me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN)\n    }, {\n      label: this.tranService.translate(\"groupSim.scope.province\"),\n      command: () => {\n        if (me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE)) {\n          me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE;\n          me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE);\n          me.isShowDialogPushGroup = true;\n        } else {\n          me.messageCommonService.info(me.tranService.translate(\"sim.text.sameProvince\"), me.tranService.translate(\"sim.text.pushSim\"));\n        }\n      },\n      visible: me.userType == CONSTANTS.USER_TYPE.PROVINCE\n    }, {\n      label: this.tranService.translate(\"groupSim.scope.customer\"),\n      command: () => {\n        if (me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER)) {\n          me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER;\n          me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER);\n          me.isShowDialogPushGroup = true;\n        } else {\n          me.messageCommonService.info(me.tranService.translate(\"sim.text.sameCustomer\"), me.tranService.translate(\"sim.text.pushSim\"));\n        }\n      }\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.statuSims = [\n    // {\n    //     value: CONSTANTS.SIM_STATUS.READY,\n    //     name: this.tranService.translate(\"sim.status.ready\")\n    // },\n    {\n      value: [CONSTANTS.SIM_STATUS.ACTIVATED],\n      name: this.tranService.translate(\"sim.status.activated\")\n    }, {\n      value: [CONSTANTS.SIM_STATUS.INACTIVED],\n      name: this.tranService.translate(\"sim.status.inactivated\")\n    }, {\n      value: [CONSTANTS.SIM_STATUS.DEACTIVATED],\n      name: this.tranService.translate(\"sim.status.deactivated\")\n    }, {\n      value: [CONSTANTS.SIM_STATUS.PURGED],\n      name: this.tranService.translate(\"sim.status.purged\")\n    }, {\n      value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],\n      name: this.tranService.translate(\"sim.status.processingChangePlan\")\n    }, {\n      value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],\n      name: this.tranService.translate(\"sim.status.processingRegisterPlan\")\n    }, {\n      value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],\n      name: this.tranService.translate(\"sim.status.waitingCancelPlan\")\n    }];\n    this.typeSims = [{\n      value: [CONSTANTS.SIM_TYPE.UNKNOWN],\n      name: this.tranService.translate(\"sim.type.unknown\")\n    }, {\n      value: [CONSTANTS.SIM_TYPE.ESIM],\n      name: this.tranService.translate(\"sim.type.esim\")\n    }, {\n      value: [CONSTANTS.SIM_TYPE.SIM],\n      name: this.tranService.translate(\"sim.type.sim\")\n    }];\n    this.columns = [{\n      name: this.tranService.translate(\"sim.label.sothuebao\"),\n      key: \"msisdn\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcClick(id, item) {\n        me.simId = id.toString();\n        me.getDetailSim();\n        me.isShowModalDetailSim = true;\n      },\n      funcGetClassname() {\n        return \"cursor-pointer\";\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.imsi\"),\n      key: \"imsi\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.dungluong\"),\n      key: \"usagedData\",\n      size: \"150px\",\n      align: \"right\",\n      isShow: true,\n      isSort: true,\n      funcConvertText: function (value) {\n        return me.utilService.bytesToMegabytes(value);\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.tengoicuoc\"),\n      key: \"ratingPlanName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.nhomsim\"),\n      key: \"groupName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.trangthaiketnoi\"),\n      key: \"connectionStatus\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == 'ON') {\n          return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else {\n          return ['p-2', 'text-50', \"surface-500\", \"border-round\", \"inline-block\"];\n        }\n      },\n      funcConvertText: function (value) {\n        return value == null ? 'UNKNOWN' : value;\n      },\n      funcCustomizeToolTip: function (value, item) {\n        if (item.originalConnectionStatus == 0) {\n          return me.tranService.translate(\"sim.label.statusDetach\");\n        } else if (item.originalConnectionStatus == 1) {\n          return me.tranService.translate(\"sim.label.statusNotAttach\");\n        } else if (item.originalConnectionStatus == 2) {\n          return me.tranService.translate(\"sim.label.statusAttach\");\n        } else if (item.originalConnectionStatus == 3) {\n          return me.tranService.translate(\"sim.label.statusNotConnect\");\n        } else if (item.originalConnectionStatus == 4) {\n          return me.tranService.translate(\"sim.label.statusConnect\");\n        } else if (item.originalConnectionStatus == 5) {\n          return me.tranService.translate(\"sim.label.statusNetwork\");\n        } else {\n          return item.originalConnectionStatus;\n        }\n      },\n      isShowTooltip: true\n    }, {\n      name: this.tranService.translate(\"sim.label.trangthaisim\"),\n      key: \"status\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == 0) {\n          return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n          return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n        }\n        return [];\n      },\n      funcConvertText: value => {\n        if (value == 0) {\n          return me.tranService.translate(\"sim.status.inventory\");\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return me.tranService.translate(\"sim.status.ready\");\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return me.tranService.translate(\"sim.status.deactivated\");\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return me.tranService.translate(\"sim.status.purged\");\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return me.tranService.translate(\"sim.status.inactivated\");\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingChangePlan\");\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n        }\n        return \"\";\n      },\n      style: {\n        color: \"white\"\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.ngaykichhoat\"),\n      key: \"activatedDate\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText: function (value) {\n        return me.utilService.convertLongDateToString(value);\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.startDate\"),\n      key: \"startDate\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true,\n      funcConvertText: function (value) {\n        return me.utilService.convertLongDateToString(value);\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.maapn\"),\n      key: \"apnId\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.khachhang\"),\n      key: \"customerName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.makhachhang\"),\n      key: \"customerCode\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.mahopdong\"),\n      key: \"contractCode\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.ngaylamhopdong\"),\n      key: \"contractDate\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true,\n      funcConvertText: value => {\n        return me.utilService.convertLongDateToString(value);\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.nguoilamhopdong\"),\n      key: \"contractInfo\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.simType\"),\n      key: \"simType\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: false,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == CONSTANTS.SIM_TYPE.UNKNOWN) {\n          return ['p-1', \"text-orange-600\", \"bg-orange-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_TYPE.ESIM) {\n          return ['p-2', \"text-blue-600\", \"bg-blue-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_TYPE.SIM) {\n          return ['p-2', 'text-green-600', \"bg-green-100\", \"border-round\", \"inline-block\"];\n        }\n        return [];\n      },\n      funcConvertText: value => {\n        if (value == CONSTANTS.SIM_TYPE.UNKNOWN) {\n          return me.tranService.translate(\"sim.type.unknown\");\n        } else if (value == CONSTANTS.SIM_TYPE.ESIM) {\n          return me.tranService.translate(\"sim.type.esim\");\n        } else if (value == CONSTANTS.SIM_TYPE.SIM) {\n          return me.tranService.translate(\"sim.type.sim\");\n        }\n        return \"\";\n      },\n      style: {\n        color: \"white\"\n      }\n    }];\n    this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: true\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"msisdn,asc\";\n    let contractCodeDefault = this.route.snapshot.paramMap.get(\"contractCode\");\n    let searchPanelCollapsRoute = this.route.snapshot.paramMap.get(\"searchPanelCollapsRoute\");\n    if ((contractCodeDefault || \"\").length > 0) {\n      this.searchInfo.contractCode = contractCodeDefault;\n    }\n    if ((searchPanelCollapsRoute || \"\").length > 0 && searchPanelCollapsRoute == 'false') {\n      this.searchPanelCollaps = false;\n    }\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n    this.getListProvince();\n  }\n  getListProvince() {\n    let me = this;\n    this.provinceService.getListProvince(response => {\n      me.listProvince = response;\n    });\n  }\n  onCollapsedChange(collapsed) {\n    // this.isSearchAfterOpenChange = false\n    // this.isSearchAfterCollapChange = false\n    console.log('Panel collapsed state:', collapsed);\n    this.searchPanelCollaps = collapsed;\n  }\n  resetField() {\n    this.formSearchSim.reset();\n  }\n  ngAfterContentChecked() {\n    let me = this;\n    if (this.isShowDialogPushGroup == false) {\n      this.groupSimSelected = null;\n    }\n    if (this.isShowDialogCreateGroup == false) {\n      this.formCreateGroupSim.reset();\n    }\n  }\n  quickSearch() {\n    event.preventDefault();\n    let params = {};\n    if (this.quickSearchValue != null && this.quickSearchValue != undefined) params = {\n      keySearch: this.quickSearchValue.trim()\n    };\n    console.log(params);\n    this.search(0, this.pageSize, this.sort, params);\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.searchInfo.loggable = true;\n    this.search(0, this.pageSize, this.sort, this.searchInfo);\n  }\n  updateParams(dataParams) {\n    let me = this;\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        if (key == \"dateFrom\") {\n          dataParams[\"contractDateFrom\"] = this.searchInfo.dateFrom.getTime();\n        } else if (key == \"dateTo\") {\n          dataParams[\"contractDateTo\"] = this.searchInfo.dateTo.getTime();\n        } else if (key == \"contractCode\") {\n          dataParams[\"contractCode\"] = me.utilService.stringToStrBase64(this.searchInfo.contractCode);\n        } else {\n          dataParams[key] = this.searchInfo[key];\n        }\n      }\n    });\n  }\n  search(page, limit, sort, params) {\n    if (this.searchPanelCollaps) {\n      this.isSearchAfterCollapChange = true;\n      this.isSearchAfterOpenChange = false;\n    } else {\n      this.isSearchAfterCollapChange = false;\n      this.isSearchAfterOpenChange = true;\n    }\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let me = this;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    this.updateParams(dataParams);\n    me.messageCommonService.onload();\n    if (this.searchPanelCollaps) {\n      params[\"page\"] = page;\n      params[\"size\"] = limit;\n      params[\"sort\"] = sort;\n      params[\"loggable\"] = true;\n      params[\"keySearch\"] = this.quickSearchValue.trim();\n      console.log(params);\n      this.simService.quickSearch(params, response => {\n        // console.log(response.content)\n        me.dataSet = {\n          content: response.content,\n          total: response.totalElements\n        };\n        me.searchInfoStandard = {\n          ...me.searchInfo\n        };\n        let list = [];\n        for (const sim of me.dataSet.content) {\n          list.push(sim.msisdn);\n        }\n        // lấy trạng thái kết nối\n        this.simService.getConnectionStatus(list, resp => {\n          let data = [...resp];\n          for (const sim of me.dataSet.content) {\n            for (let el of data) {\n              if (sim.msisdn == el.msisdn) {\n                sim.connectionStatus = this.getSimStatus(el.userstate);\n                sim.originalConnectionStatus = el.userstate;\n              }\n            }\n          }\n          // console.log(data)\n        }, null, () => {});\n        this.simService.getDataUsed(list, resp => {\n          for (const sim of me.dataSet.content) {\n            for (let el of resp) {\n              if (sim.msisdn == el.msisdn) {\n                sim.usagedData = el.usedData;\n              }\n            }\n          }\n        });\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      return;\n    }\n    dataParams[\"page\"] = page;\n    dataParams[\"size\"] = limit;\n    dataParams[\"sort\"] = sort;\n    this.simService.search(dataParams, response => {\n      console.log(response.content);\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      me.searchInfoStandard = {\n        ...me.searchInfo\n      };\n      let list = [];\n      for (const sim of me.dataSet.content) {\n        list.push(sim.msisdn);\n      }\n      // lấy trạng thái kết nối\n      this.simService.getConnectionStatus(list, resp => {\n        let data = [...resp];\n        for (const sim of me.dataSet.content) {\n          for (let el of data) {\n            if (sim.msisdn == el.msisdn) {\n              sim.connectionStatus = this.getSimStatus(el.userstate);\n              sim.originalConnectionStatus = el.userstate;\n            }\n          }\n        }\n        // console.log(data)\n      }, null, () => {});\n      this.simService.getDataUsed(list, resp => {\n        for (const sim of me.dataSet.content) {\n          for (let el of resp) {\n            if (sim.msisdn == el.msisdn) {\n              sim.usagedData = el.usedData;\n            }\n          }\n        }\n      });\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getSimStatus(status) {\n    if (status == null || status == undefined) {\n      return 'UNKNOWN';\n    }\n    if (status !== null && status !== undefined) {\n      if (status == '0' || status == '5') {\n        return 'OFF';\n      } else if (status == '1' || status == '2' || status == '3' || status == '4') {\n        return 'ON';\n      } else {\n        return 'NOT FOUND';\n      }\n    } else return 'NOT FOUND';\n  }\n  getTooltipStatus(status) {\n    if (status == 0) {\n      return this.tranService.translate(\"sim.label.statusDetach\");\n    } else if (status == 1) {\n      return this.tranService.translate(\"sim.label.statusNotAttach\");\n    } else if (status == 2) {\n      return this.tranService.translate(\"sim.label.statusAttach\");\n    } else if (status == 3) {\n      return this.tranService.translate(\"sim.label.statusNotConnect\");\n    } else if (status == 4) {\n      return this.tranService.translate(\"sim.label.statusConnect\");\n    } else if (status == 5) {\n      return this.tranService.translate(\"sim.label.statusNetwork\");\n    } else {\n      return status;\n    }\n  }\n  checkInValidPushToGroupSim() {\n    if (this.selectItems.length == 0) {\n      return true;\n    }\n    let flag = false;\n    for (let i = 0; i < this.selectItems.length; i++) {\n      if ((this.selectItems[i].groupName || \"\") != \"\") {\n        flag = true;\n        break;\n      }\n    }\n    return flag;\n  }\n  getListGroupSimForSelectPush(type) {\n    let me = this;\n    let params = {\n      scope: type\n    };\n    if (type == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN) {} else if (type == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE) {\n      params['provinceCode'] = this.provinceCode;\n    } else if (type == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER) {\n      params['customerCode'] = this.customerCode;\n    }\n    this.paramSearchGroupSim = {\n      ...params\n    };\n  }\n  onChangeDateFrom(value) {\n    if (value) {\n      this.minDateTo = value;\n    } else {\n      this.minDateTo = null;\n    }\n  }\n  onChangeDateTo(value) {\n    if (value) {\n      this.maxDateFrom = value;\n    } else {\n      this.maxDateFrom = new Date();\n    }\n  }\n  pushGroupSim(type) {\n    let me = this;\n    if (type == 0) {\n      //push group available\n      if (this.groupSimSelected == null || this.groupSimSelected == undefined) {\n        return;\n      }\n      this.messageCommonService.onload();\n      this.simService.pushSimToGroup(this.selectItems.map(el => el.msisdn), {\n        id: this.groupSimSelected\n      }, response => {\n        setTimeout(function () {\n          me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        });\n        me.messageCommonService.success(me.tranService.translate(\"global.message.addGroupSuccess\"));\n        me.selectItems = [];\n      });\n      this.isShowDialogPushGroup = false;\n    } else {\n      //create new group\n      let group = {\n        id: null,\n        groupKey: this.dataCreateGroupSim.groupKey,\n        name: this.dataCreateGroupSim.name,\n        customerCode: this.customerCode,\n        description: this.dataCreateGroupSim.description,\n        provinceCode: this.provinceCode,\n        scope: this.groupScope\n      };\n      this.messageCommonService.onload();\n      this.simService.pushSimToGroup(this.selectItems.map(el => el.msisdn), group, response => {\n        setTimeout(function () {\n          me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        });\n        me.messageCommonService.success(me.tranService.translate(\"global.message.addGroupSuccess\"));\n        me.selectItems = [];\n      });\n      this.isShowDialogCreateGroup = false;\n    }\n  }\n  checkExistGroupKey() {\n    if (this.dataCreateGroupSim.groupKey?.trim().length > 0) {\n      let me = this;\n      this.debounceService.set(\"groupKey\", me.groupSimService.groupkeyCheckExisted.bind(me.groupSimService), {}, {\n        query: me.dataCreateGroupSim.groupKey\n      }, response => {\n        me.isExistsGroupKey = response == 1;\n      });\n    }\n  }\n  helpExport(type) {\n    if (this.selectItems.length == 0 && type == 0) {\n      this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportFilterEmpty\"), this.tranService.translate(\"global.button.export\"));\n    } else if (this.selectItems.length > CONSTANTS.MAX_ROW_EXPORT || this.dataSet.total > CONSTANTS.MAX_ROW_EXPORT) {\n      this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportFilter\"), this.tranService.translate(\"global.button.export\"));\n    }\n  }\n  helpExelExport(type) {\n    if (this.selectItems.length == 0 && type == 0) {\n      this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportFilterEmpty\"), this.tranService.translate(\"global.button.export\"));\n    } else if (this.selectItems.length > CONSTANTS.MAX_ROW_EXCEL_EXPORT || this.dataSet.total > CONSTANTS.MAX_ROW_EXCEL_EXPORT) {\n      this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportExelFilter\"), this.tranService.translate(\"global.button.export\"));\n    }\n  }\n  checkDisabledExportFilter() {\n    return JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoStandard) || this.dataSet.total == null || this.dataSet.total != null && (this.dataSet.total == 0 || this.dataSet.total > CONSTANTS.MAX_ROW_EXPORT);\n  }\n  checkDisabledExportExelFilter() {\n    return JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoStandard) || this.dataSet.total == null || this.dataSet.total != null && (this.dataSet.total == 0 || this.dataSet.total > CONSTANTS.MAX_ROW_EXCEL_EXPORT);\n  }\n  checkDisablePushSimToGroup(type) {\n    if (type == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN) {\n      return this.userType == CONSTANTS.USER_TYPE.ADMIN;\n    }\n    if ((this.selectItems || []).length == 0) return false;\n    if (type == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE) {\n      if ([CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT].includes(this.userType)) {\n        this.provinceCode = this.selectItems[0]['provinceCode'];\n        this.provinceName = null;\n        for (let i = 0; i < this.listProvince.length; i++) {\n          if (this.listProvince[i].code == this.provinceCode) {\n            this.provinceName = this.listProvince[i].name;\n            break;\n          }\n        }\n        if (this.provinceName == null) {\n          this.provinceName = this.selectItems[0]['provinceName'];\n        }\n        for (let i = 1; i < this.selectItems.length; i++) {\n          if (this.selectItems[i]['provinceCode'] != this.provinceCode) {\n            return false;\n          }\n        }\n        return true;\n      }\n    } else if (type == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER) {\n      if ([CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER].includes(this.userType)) {\n        this.customerCode = this.selectItems[0]['customerCode'];\n        this.customerName = this.selectItems[0]['customerName'];\n        this.provinceCode = this.selectItems[0]['provinceCode'];\n        for (let i = 1; i < this.selectItems.length; i++) {\n          if (this.selectItems[i]['customerCode'] != this.customerCode) {\n            return false;\n          }\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n  deleteSim() {\n    let me = this;\n    let listMsisdn = this.selectItems.map(e => e.msisdn);\n    this.isShowModalDeleteSim = false;\n    // TODO : call api xóa sim\n    me.messageCommonService.onload();\n    this.simService.deleteListSim(listMsisdn, response => {\n      me.isShowModalDeleteSim = false;\n      me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\n      me.selectItems = [];\n    }, null, () => {\n      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n    });\n  }\n  showModalDeleteSim() {\n    if (this.selectItems.length === 0) return;\n    this.isShowModalDeleteSim = true;\n  }\n  getDetailSim() {\n    let me = this;\n    me.resetDetailSim();\n    this.messageCommonService.onload();\n    me.simService.getById(me.simId, response => {\n      me.detailSim = {\n        ...response\n      };\n      me.getDetailCustomer();\n      me.getDetailApn();\n      me.getStatusSim();\n      me.getDetailRatingPlan();\n      me.getDetailContract();\n      // this.messageCommonService.onload();\n      // Promise.all([\n      //     this.getStatusSim(),\n      //     this.getDetailRatingPlan(),\n      //     this.getDetailContract()\n      // ])\n      //     .then(([statusSim,ratingPlan, contract]) => {\n      //     })\n      //     .catch(error => {\n      //         this.messageCommonService.error(this.tranService.translate(\"global.message.errorLoading\"));\n      //         console.error(\"Error loading:\", error);\n      //         this.isShowModalDetailSim = false;\n      //     })\n      //     .finally(() => {\n      //         this.messageCommonService.offload();\n      //     });\n      me.getDetailApn();\n      me.simService.getConnectionStatus([me.simId], resp => {\n        me.detailSim.connectionStatus = resp[0].userstate;\n      }, () => {});\n    }, null, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  resetDetailSim() {\n    let me = this;\n    me.detailContract = {};\n    me.detailCustomer = {};\n    me.detailRatingPlan = {};\n    me.detailStatusSim = {};\n    me.detailSim = {};\n  }\n  getStatusSim() {\n    return new Promise((resolve, reject) => {\n      this.simService.getDetailStatus(this.detailSim.msisdn, response => {\n        this.detailStatusSim = {\n          statusData: response.gprsStatus == 1,\n          statusReceiveCall: response.icStatus == 1,\n          statusSendCall: response.ocStatus == 1,\n          statusWorldCall: response.iddStatus == 1,\n          statusReceiveSms: response.smtStatus == 1,\n          statusSendSms: response.smoStatus == 1\n        };\n        resolve(response);\n      }, error => {\n        reject(error);\n      }, () => {});\n    });\n  }\n  getDetailCustomer() {\n    this.detailCustomer = {\n      name: this.detailSim.customerName,\n      code: this.detailSim.customerCode\n    };\n  }\n  getDetailRatingPlan() {\n    return new Promise((resolve, reject) => {\n      this.simService.getDetailPlanSim(this.detailSim.msisdn, response => {\n        this.detailRatingPlan = {\n          ...response\n        };\n        resolve(response);\n      }, error => {\n        reject(error);\n      }, () => {});\n    });\n  }\n  getDetailContract() {\n    return new Promise((resolve, reject) => {\n      this.simService.getDetailContract(this.utilService.stringToStrBase64(this.detailSim.contractCode), response => {\n        this.detailContract = response;\n        resolve(response);\n      }, error => {\n        reject(error);\n      }, () => {});\n    });\n  }\n  getDetailApn() {\n    this.detailAPN = {\n      code: this.detailSim.apnCode,\n      type: \"Kết nối bằng 3G\",\n      ip: 0,\n      rangeIp: this.detailSim.ip\n    };\n  }\n  getNameStatus(value) {\n    if (value == 0) {\n      return this.tranService.translate(\"sim.status.inventory\");\n    } else if (value == CONSTANTS.SIM_STATUS.READY) {\n      // return this.tranService.translate(\"sim.status.ready\");\n      return this.tranService.translate(\"sim.status.activated\");\n    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n      return this.tranService.translate(\"sim.status.activated\");\n    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n      return this.tranService.translate(\"sim.status.deactivated\");\n    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n      return this.tranService.translate(\"sim.status.purged\");\n    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n      return this.tranService.translate(\"sim.status.inactivated\");\n    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.processingChangePlan\");\n    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n    }\n    return \"\";\n  }\n  getClassStatus(value) {\n    if (value == 0) {\n      return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.READY) {\n      // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n      return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n      return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n      return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n      return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n      return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n    }\n    return [];\n  }\n  getServiceType(value) {\n    if (value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate(\"sim.serviceType.prepaid\");else if (value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate(\"sim.serviceType.postpaid\");else return \"\";\n  }\n  static {\n    this.ɵfac = function AppSimListComponent_Factory(t) {\n      return new (t || AppSimListComponent)(i0.ɵɵdirectiveInject(SimService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(GroupSimService), i0.ɵɵdirectiveInject(CustomerService), i0.ɵɵdirectiveInject(RatingPlanService), i0.ɵɵdirectiveInject(i2.ProvinceService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSimListComponent,\n      selectors: [[\"app-sim-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 149,\n      vars: 163,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"responsive-button-container\", \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\", \"gap-2\"], [\"icon\", \"pi pi-minus-circle\", \"styleClass\", \"p-button p-button-danger equal-button\", 3, \"disabled\", \"label\", \"click\"], [\"styleClass\", \"mr-2 p-button-info\", \"styleClass\", \"equal-button\", \"icon\", \"pi pi-download\", 3, \"label\", \"model\", \"onClick\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"collapsed\", \"collapsedChange\"], [\"pTemplate\", \"icons\"], [\"pTemplate\", \"header\"], [1, \"grid\", \"search-grid-2\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"msisdn\", \"formControlName\", \"msisdn\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"msisdn\"], [\"pInputText\", \"\", \"id\", \"imsi\", \"formControlName\", \"imsi\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"imsi\"], [\"styleClass\", \"w-full\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"status\"], [1, \"relative\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name} - ${groupKey}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"floatLabel\", \"paramDefault\", \"stylePositionBoxSelect\", \"isMultiChoice\", \"valueChange\"], [\"pInputText\", \"\", \"id\", \"apnId\", \"formControlName\", \"apnId\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"apnId\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name} - ${code}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"paramDefault\", \"floatLabel\", \"isMultiChoice\", \"valueChange\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"contractCode\", \"displayPattern\", \"${contractCode}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"paramDefault\", \"floatLabel\", \"isMultiChoice\", \"valueChange\"], [\"pInputText\", \"\", \"id\", \"contractor\", \"formControlName\", \"contractor\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"contractor\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"customerCode\", \"displayPattern\", \"${customerName} - ${customerCode}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"paramDefault\", \"floatLabel\", \"isMultiChoice\", \"valueChange\"], [1, \"col-3\", \"pb-0\"], [\"styleClass\", \"w-full\", \"id\", \"dateFrom\", \"formControlName\", \"dateFrom\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateFrom\"], [\"styleClass\", \"w-full\", \"id\", \"dateTo\", \"formControlName\", \"dateTo\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"minDate\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateTo\"], [\"styleClass\", \"w-full\", \"id\", \"simType\", \"formControlName\", \"simType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"styleClass\", \"w-full\", \"id\", \"province\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"optionValue\", \"code\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"filter\", \"placeholder\", \"emptyFilterMessage\", \"ngModelChange\"], [\"for\", \"provinceCode\"], [\"objectKey\", \"dropdownListUser\", \"paramKey\", \"username\", \"keyReturn\", \"id\", \"displayPattern\", \"${username}\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"floatLabel\", \"valueChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"tableId\", \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"groupSim\", 1, \"col-fixed\", 2, \"width\", \"100px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 140px)\"], [\"objectKey\", \"groupSim\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name} - ${groupKey}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"paramDefault\", \"valueChange\"], [2, \"width\", \"40px\"], [\"styleClass\", \"p-button-info\", \"icon\", \"pi pi-plus\", 3, \"pTooltip\", \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\"], [\"styleClass\", \"mr-2 p-button-secondary p-button-outlined\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"disabled\", \"click\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"header\", \"visible\", \"modal\", \"style\", \"draggable\", \"resizable\", \"visibleChange\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"dialog-create-group\"], [3, \"formGroup\", \"ngSubmit\"], [\"htmlFor\", \"customer\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [1, \"col\"], [4, \"ngIf\"], [\"htmlFor\", \"groupKey\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"groupKey\", \"formControlName\", \"groupKey\", \"pattern\", \"[a-zA-Z0-9\\\\-_]*\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keyup\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"groupName\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"name\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [\"pInputText\", \"\", \"id\", \"name\", \"formControlName\", \"name\", \"pattern\", \"[^~`!@#\\\\$%^&*()=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>/?]*\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [\"rows\", \"3\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"placeholder\", \"maxLength\", \"ngModelChange\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [1, \"flex\", \"justify-content-center\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"click\"], [\"class\", \"flex flex-row gap-3\", 4, \"ngIf\"], [\"class\", \"font-bold text-lg\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"gap-3\"], [\"type\", \"text\", \"pInputText\", \"\", 2, \"min-width\", \"35vw\", 3, \"placeholder\", \"ngModel\", \"ngModelOptions\", \"keydown.enter\", \"ngModelChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"button\", 3, \"click\"], [1, \"font-bold\", \"text-lg\"], [\"styleClass\", \"p-button-info\", \"icon\", \"pi pi-plus\", 3, \"pTooltip\", \"click\"], [1, \"grid\", \"grid-1\", \"mt-1\", \"h-auto\", 2, \"width\", \"calc(100% + 16px)\"], [1, \"col\", \"sim-detail\", \"pr-0\"], [3, \"header\", 4, \"ngIf\"], [\"styleClass\", \"mt-3 sim-status\", 3, \"header\", 4, \"ngIf\"], [\"styleClass\", \"mt-3\", 3, \"header\", 4, \"ngIf\"], [3, \"header\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"custom-card\"], [1, \"w-6\"], [1, \"grid\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"150px\", \"max-width\", \"200px\"], [1, \"sim-status-block\", \"mt-1\", \"grid\"], [1, \"w-auto\", \"ml-3\"], [1, \"mt-1\", \"grid\"], [1, \"ml-3\", \"p-2\", \"border-round\", \"inline-block\", 3, \"ngClass\", \"pTooltip\"], [1, \"rounded\", \"border\", \"border-surface-200\", \"dark:border-surface-700\", \"p-6\", \"bg-surface-0\", \"dark:bg-surface-900\"], [\"styleClass\", \"mt-3 sim-status\", 3, \"header\"], [1, \"col-4\", \"text-center\"], [\"onLabel\", \"ON\", \"offLabel\", \"OFF\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"styleClass\", \"mt-3\", 3, \"header\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\"], [1, \"grid\", \"mt-0\"], [1, \"col\", \"uppercase\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"140px\"]],\n      template: function AppSimListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function AppSimListComponent_Template_p_button_click_6_listener() {\n            return ctx.showModalDeleteSim();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p-splitButton\", 6);\n          i0.ɵɵlistener(\"onClick\", function AppSimListComponent_Template_p_splitButton_onClick_7_listener() {\n            return ctx.helpExport(1);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function AppSimListComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(9, \"p-panel\", 8);\n          i0.ɵɵlistener(\"collapsedChange\", function AppSimListComponent_Template_p_panel_collapsedChange_9_listener($event) {\n            return ctx.onCollapsedChange($event);\n          });\n          i0.ɵɵtemplate(10, AppSimListComponent_ng_template_10_Template, 0, 0, \"ng-template\", 9);\n          i0.ɵɵtemplate(11, AppSimListComponent_ng_template_11_Template, 2, 2, \"ng-template\", 10);\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"span\", 13)(15, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.searchInfo.msisdn = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"label\", 15);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 12)(19, \"span\", 13)(20, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_input_ngModelChange_20_listener($event) {\n            return ctx.searchInfo.imsi = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"label\", 17);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"span\", 13)(25, \"p-dropdown\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_p_dropdown_ngModelChange_25_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"label\", 19);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"div\", 20)(30, \"vnpt-select\", 21);\n          i0.ɵɵlistener(\"valueChange\", function AppSimListComponent_Template_vnpt_select_valueChange_30_listener($event) {\n            return ctx.searchInfo.simGroupId = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 12)(32, \"span\", 13)(33, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.searchInfo.apnId = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"label\", 23);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 12)(37, \"div\", 20)(38, \"vnpt-select\", 24);\n          i0.ɵɵlistener(\"valueChange\", function AppSimListComponent_Template_vnpt_select_valueChange_38_listener($event) {\n            return ctx.searchInfo.ratingPlanId = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 12)(40, \"div\", 20)(41, \"vnpt-select\", 25);\n          i0.ɵɵlistener(\"valueChange\", function AppSimListComponent_Template_vnpt_select_valueChange_41_listener($event) {\n            return ctx.searchInfo.contractCode = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 12)(43, \"span\", 13)(44, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_input_ngModelChange_44_listener($event) {\n            return ctx.searchInfo.contractor = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"label\", 27);\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 12)(48, \"div\", 20)(49, \"vnpt-select\", 28);\n          i0.ɵɵlistener(\"valueChange\", function AppSimListComponent_Template_vnpt_select_valueChange_49_listener($event) {\n            return ctx.searchInfo.customer = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 29)(51, \"span\", 13)(52, \"p-calendar\", 30);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_p_calendar_ngModelChange_52_listener($event) {\n            return ctx.searchInfo.dateFrom = $event;\n          })(\"onSelect\", function AppSimListComponent_Template_p_calendar_onSelect_52_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          })(\"onInput\", function AppSimListComponent_Template_p_calendar_onInput_52_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"label\", 31);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 29)(56, \"span\", 13)(57, \"p-calendar\", 32);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_p_calendar_ngModelChange_57_listener($event) {\n            return ctx.searchInfo.dateTo = $event;\n          })(\"onSelect\", function AppSimListComponent_Template_p_calendar_onSelect_57_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          })(\"onInput\", function AppSimListComponent_Template_p_calendar_onInput_57_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"label\", 33);\n          i0.ɵɵtext(59);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 12)(61, \"span\", 13)(62, \"p-dropdown\", 34);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_p_dropdown_ngModelChange_62_listener($event) {\n            return ctx.searchInfo.simType = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"label\", 19);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 12)(66, \"span\", 13)(67, \"p-dropdown\", 35);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_p_dropdown_ngModelChange_67_listener($event) {\n            return ctx.searchInfo.provinceCode = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"label\", 36);\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 12)(71, \"div\", 20)(72, \"vnpt-select\", 37);\n          i0.ɵɵlistener(\"valueChange\", function AppSimListComponent_Template_vnpt_select_valueChange_72_listener($event) {\n            return ctx.searchInfo.userId = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 29);\n          i0.ɵɵelement(74, \"p-button\", 38);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"table-vnpt\", 39);\n          i0.ɵɵlistener(\"selectItemsChange\", function AppSimListComponent_Template_table_vnpt_selectItemsChange_75_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 40)(77, \"p-dialog\", 41);\n          i0.ɵɵlistener(\"visibleChange\", function AppSimListComponent_Template_p_dialog_visibleChange_77_listener($event) {\n            return ctx.isShowDialogPushGroup = $event;\n          });\n          i0.ɵɵelementStart(78, \"div\", 42)(79, \"label\", 43);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 44)(82, \"vnpt-select\", 45);\n          i0.ɵɵlistener(\"valueChange\", function AppSimListComponent_Template_vnpt_select_valueChange_82_listener($event) {\n            return ctx.groupSimSelected = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 46);\n          i0.ɵɵtemplate(84, AppSimListComponent_p_button_84_Template, 1, 1, \"p-button\", 47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 48)(86, \"p-button\", 49);\n          i0.ɵɵlistener(\"click\", function AppSimListComponent_Template_p_button_click_86_listener() {\n            ctx.isShowDialogPushGroup = false;\n            return ctx.groupSimSelected = null;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"p-button\", 50);\n          i0.ɵɵlistener(\"click\", function AppSimListComponent_Template_p_button_click_87_listener() {\n            return ctx.pushGroupSim(0);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(88, \"div\", 51);\n          i0.ɵɵtemplate(89, AppSimListComponent_p_dialog_89_Template, 14, 18, \"p-dialog\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"div\", 53)(91, \"p-dialog\", 41);\n          i0.ɵɵlistener(\"visibleChange\", function AppSimListComponent_Template_p_dialog_visibleChange_91_listener($event) {\n            return ctx.isShowDialogCreateGroup = $event;\n          });\n          i0.ɵɵelementStart(92, \"form\", 54);\n          i0.ɵɵlistener(\"ngSubmit\", function AppSimListComponent_Template_form_ngSubmit_92_listener() {\n            return ctx.pushGroupSim(1);\n          });\n          i0.ɵɵelementStart(93, \"div\", 42)(94, \"label\", 55);\n          i0.ɵɵtext(95);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"div\", 56);\n          i0.ɵɵtemplate(97, AppSimListComponent_span_97_Template, 2, 1, \"span\", 57);\n          i0.ɵɵtemplate(98, AppSimListComponent_span_98_Template, 2, 1, \"span\", 57);\n          i0.ɵɵtemplate(99, AppSimListComponent_span_99_Template, 2, 1, \"span\", 57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"div\", 42)(101, \"label\", 58);\n          i0.ɵɵtext(102);\n          i0.ɵɵelementStart(103, \"span\", 59);\n          i0.ɵɵtext(104, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(105, \"div\", 56)(106, \"input\", 60);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_input_ngModelChange_106_listener($event) {\n            return ctx.dataCreateGroupSim.groupKey = $event;\n          })(\"keyup\", function AppSimListComponent_Template_input_keyup_106_listener() {\n            return ctx.checkExistGroupKey();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"div\", 61);\n          i0.ɵɵelement(108, \"label\", 62);\n          i0.ɵɵelementStart(109, \"div\", 56);\n          i0.ɵɵtemplate(110, AppSimListComponent_small_110_Template, 2, 1, \"small\", 63);\n          i0.ɵɵtemplate(111, AppSimListComponent_small_111_Template, 2, 2, \"small\", 63);\n          i0.ɵɵtemplate(112, AppSimListComponent_small_112_Template, 2, 1, \"small\", 63);\n          i0.ɵɵtemplate(113, AppSimListComponent_small_113_Template, 2, 3, \"small\", 63);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(114, \"div\", 42)(115, \"label\", 64);\n          i0.ɵɵtext(116);\n          i0.ɵɵelementStart(117, \"span\", 59);\n          i0.ɵɵtext(118, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(119, \"div\", 56)(120, \"input\", 65);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_input_ngModelChange_120_listener($event) {\n            return ctx.dataCreateGroupSim.name = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(121, \"div\", 61);\n          i0.ɵɵelement(122, \"label\", 64);\n          i0.ɵɵelementStart(123, \"div\", 56);\n          i0.ɵɵtemplate(124, AppSimListComponent_small_124_Template, 2, 1, \"small\", 63);\n          i0.ɵɵtemplate(125, AppSimListComponent_small_125_Template, 2, 2, \"small\", 63);\n          i0.ɵɵtemplate(126, AppSimListComponent_small_126_Template, 2, 1, \"small\", 63);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(127, AppSimListComponent_div_127_Template, 5, 3, \"div\", 66);\n          i0.ɵɵtemplate(128, AppSimListComponent_div_128_Template, 5, 3, \"div\", 66);\n          i0.ɵɵelementStart(129, \"div\", 42)(130, \"label\", 67);\n          i0.ɵɵtext(131);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"div\", 56)(133, \"textarea\", 68);\n          i0.ɵɵlistener(\"ngModelChange\", function AppSimListComponent_Template_textarea_ngModelChange_133_listener($event) {\n            return ctx.dataCreateGroupSim.description = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(134, \"div\", 61);\n          i0.ɵɵelement(135, \"label\", 62);\n          i0.ɵɵelementStart(136, \"div\", 56);\n          i0.ɵɵtemplate(137, AppSimListComponent_small_137_Template, 2, 2, \"small\", 63);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(138, \"div\", 48)(139, \"p-button\", 49);\n          i0.ɵɵlistener(\"click\", function AppSimListComponent_Template_p_button_click_139_listener() {\n            return ctx.isShowDialogCreateGroup = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(140, \"p-button\", 69);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(141, \"div\", 70)(142, \"p-dialog\", 41);\n          i0.ɵɵlistener(\"visibleChange\", function AppSimListComponent_Template_p_dialog_visibleChange_142_listener($event) {\n            return ctx.isShowModalDeleteSim = $event;\n          });\n          i0.ɵɵelementStart(143, \"div\", 48)(144, \"p\");\n          i0.ɵɵtext(145);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(146, \"div\", 71)(147, \"p-button\", 72);\n          i0.ɵɵlistener(\"click\", function AppSimListComponent_Template_p_button_click_147_listener() {\n            return ctx.isShowModalDeleteSim = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"p-button\", 73);\n          i0.ɵɵlistener(\"click\", function AppSimListComponent_Template_p_button_click_148_listener() {\n            return ctx.deleteSim();\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listsim\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"label\", ctx.tranService.translate(\"global.button.delete\"));\n          i0.ɵɵproperty(\"disabled\", ctx.selectItems.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.export\"))(\"model\", ctx.itemExports);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchSim);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"collapsed\", ctx.searchPanelCollaps);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.msisdn);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.sothuebao\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.imsi);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.imsi\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"options\", ctx.statuSims);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.trangthaisim\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.simGroupId)(\"placeholder\", ctx.tranService.translate(\"sim.label.nhomsim\"))(\"floatLabel\", true)(\"paramDefault\", i0.ɵɵpureFunction0(153, _c5))(\"stylePositionBoxSelect\", i0.ɵɵpureFunction0(154, _c6))(\"isMultiChoice\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.apnId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.maapn\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.ratingPlanId)(\"placeholder\", ctx.tranService.translate(\"sim.label.goicuoc\"))(\"paramDefault\", i0.ɵɵpureFunction0(155, _c7))(\"floatLabel\", true)(\"isMultiChoice\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.contractCode)(\"placeholder\", ctx.tranService.translate(\"sim.label.mahopdong\"))(\"paramDefault\", i0.ɵɵpureFunction0(156, _c8))(\"floatLabel\", true)(\"isMultiChoice\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contractor);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.nguoilamhopdong\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.customer)(\"placeholder\", ctx.tranService.translate(\"sim.label.khachhang\"))(\"paramDefault\", i0.ɵɵpureFunction0(157, _c9))(\"floatLabel\", true)(\"isMultiChoice\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateFrom)(\"showIcon\", true)(\"showClear\", true)(\"maxDate\", ctx.maxDateFrom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.ngaylamhopdongtu\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateTo)(\"showIcon\", true)(\"showClear\", true)(\"minDate\", ctx.minDateTo)(\"maxDate\", ctx.maxDateTo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.ngaylamhopdongden\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.simType)(\"options\", ctx.typeSims);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.simType\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.userType == ctx.optionuserType.ADMIN ? \"\" : \"hidden\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.provinceCode)(\"options\", ctx.listProvince)(\"filter\", true)(\"placeholder\", ctx.tranService.translate(\"account.text.selectProvince\"))(\"emptyFilterMessage\", ctx.tranService.translate(\"global.text.nodata\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.province\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.userId)(\"placeholder\", ctx.tranService.translate(\"account.text.account\"))(\"isMultiChoice\", false)(\"floatLabel\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"tableId\", \"tableSimList\")(\"fieldId\", \"msisdn\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.listsim\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(158, _c10));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.button.pushGroupSim\"))(\"visible\", ctx.isShowDialogPushGroup)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.nhomsim\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.groupSimSelected)(\"placeholder\", ctx.tranService.translate(\"sim.text.selectGroupSim\"))(\"isMultiChoice\", false)(\"paramDefault\", ctx.paramSearchGroupSim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(159, _c11, ctx.allPermissions.GROUP_SIM.CREATE)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.groupSimSelected == null || ctx.groupSimSelected == undefined);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowModalDetailSim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(161, _c10));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.text.createGroupSimAndPushSimToGroup\"))(\"visible\", ctx.isShowDialogCreateGroup)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formCreateGroupSim);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.label.groupScope\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_ADMIN);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_PROVINCE);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_CUSTOMER);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.groupKey\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.dataCreateGroupSim.groupKey)(\"required\", true)(\"maxLength\", 16)(\"placeholder\", ctx.tranService.translate(\"sim.text.inputGroupKey\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.groupKey.dirty && (ctx.formCreateGroupSim.controls.groupKey.errors == null ? null : ctx.formCreateGroupSim.controls.groupKey.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.groupKey.errors == null ? null : ctx.formCreateGroupSim.controls.groupKey.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.groupKey.errors == null ? null : ctx.formCreateGroupSim.controls.groupKey.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.formCreateGroupSim.controls.groupKey.errors == null ? null : ctx.formCreateGroupSim.controls.groupKey.errors.required) && ctx.isExistsGroupKey);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.groupName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.dataCreateGroupSim.name)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx.tranService.translate(\"sim.text.inputGroupName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.name.dirty && (ctx.formCreateGroupSim.controls.name.errors == null ? null : ctx.formCreateGroupSim.controls.name.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.name.errors == null ? null : ctx.formCreateGroupSim.controls.name.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.name.errors == null ? null : ctx.formCreateGroupSim.controls.name.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_CUSTOMER);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_PROVINCE);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.description\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx.dataCreateGroupSim.description)(\"placeholder\", ctx.tranService.translate(\"sim.text.inputDescription\"))(\"maxLength\", 255);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formCreateGroupSim.controls.description.errors == null ? null : ctx.formCreateGroupSim.controls.description.errors.maxLength);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.formCreateGroupSim.invalid || ctx.isExistsGroupKey);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(162, _c10));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.label.deleteSim\"))(\"visible\", ctx.isShowModalDeleteSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.text.deleteSim\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.agree\"));\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf, i4.Breadcrumb, i5.Tooltip, i6.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.PatternValidator, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i7.InputText, i8.Button, i9.TableVnptComponent, i10.VnptCombobox, i11.SplitButton, i12.Dropdown, i13.Calendar, i14.Dialog, i15.Card, i16.ToggleButton, i17.InputTextarea, i18.Panel, i19.Skeleton, i3.DecimalPipe, i3.DatePipe]\n    });\n  }\n}", "map": {"version": 3, "names": ["SimService", "CONSTANTS", "GroupSimService", "CustomerService", "RatingPlanService", "ComponentBase", "i0", "ɵɵelementStart", "ɵɵlistener", "AppSimListComponent_ng_template_11_div_0_Template_input_keydown_enter_1_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "ɵɵresetView", "quickSearch", "AppSimListComponent_ng_template_11_div_0_Template_input_ngModelChange_1_listener", "$event", "ctx_r21", "quickSearchValue", "ɵɵelementEnd", "AppSimListComponent_ng_template_11_div_0_Template_p_button_click_2_listener", "ctx_r22", "ɵɵadvance", "ɵɵproperty", "ctx_r17", "tranService", "translate", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵtextInterpolate", "ctx_r18", "ɵɵtemplate", "AppSimListComponent_ng_template_11_div_0_Template", "AppSimListComponent_ng_template_11_div_1_Template", "ctx_r1", "searchPanelCollaps", "AppSimListComponent_p_button_84_Template_p_button_click_0_listener", "_r24", "ctx_r23", "isShowDialogCreateGroup", "isShowDialogPushGroup", "ctx_r2", "ctx_r25", "detailSim", "msisdn", "ɵɵclassMap", "getClassStatus", "status", "getNameStatus", "imsi", "imei", "apnId", "getSimStatus", "connectionStatus", "getTooltipStatus", "ɵɵpipeBind2", "startDate", "getServiceType", "serviceType", "ɵɵelement", "ctx_r26", "AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_3_listener", "_r36", "ctx_r35", "detailStatusSim", "statusData", "AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_7_listener", "ctx_r37", "statusReceiveCall", "AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_11_listener", "ctx_r38", "statusSendCall", "AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_16_listener", "ctx_r39", "statusWorldCall", "AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_20_listener", "ctx_r40", "statusReceiveSms", "AppSimListComponent_p_dialog_89_p_card_5_Template_p_toggleButton_ngModelChange_24_listener", "ctx_r41", "statusSendSms", "ctx_r27", "ctx_r28", "ctx_r29", "ratingPlanName", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "utilService", "bytesToMegabytes", "detailRatingPlan", "dataUseInMonth", "unit", "ctx_r30", "ctx_r31", "detailContract", "contractCode", "contractDate", "contractorInfo", "centerCode", "contactPhone", "contactAddress", "paymentName", "paymentAddress", "routeCode", "ctx_r32", "ctx_r33", "detailCustomer", "name", "code", "ctx_r34", "AppSimListComponent_p_dialog_89_Template_p_dialog_visibleChange_0_listener", "_r43", "ctx_r42", "isShowModalDetailSim", "AppSimListComponent_p_dialog_89_p_card_3_Template", "AppSimListComponent_p_dialog_89_p_card_4_Template", "AppSimListComponent_p_dialog_89_p_card_5_Template", "AppSimListComponent_p_dialog_89_p_card_6_Template", "AppSimListComponent_p_dialog_89_p_card_7_Template", "AppSimListComponent_p_dialog_89_p_card_8_Template", "AppSimListComponent_p_dialog_89_p_card_10_Template", "AppSimListComponent_p_dialog_89_p_card_11_Template", "AppSimListComponent_p_dialog_89_p_card_12_Template", "AppSimListComponent_p_dialog_89_p_card_13_Template", "ɵɵstyleMap", "_c1", "ctx_r3", "Object", "keys", "length", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r8", "_c2", "ctx_r9", "ctx_r10", "ɵɵpureFunction1", "_c3", "toLowerCase", "ctx_r11", "ctx_r12", "_c4", "ctx_r13", "ctx_r14", "customerName", "customerCode", "ctx_r15", "provinceName", "provinceCode", "ctx_r16", "AppSimListComponent", "constructor", "simService", "formBuilder", "groupSimService", "customerService", "ratingPlanService", "provinceService", "injector", "maxDateFrom", "Date", "minDateTo", "maxDateTo", "groupScopeObjects", "GROUP_SCOPE", "allPermissions", "PERMISSIONS", "paramSearchGroupSim", "listProvince", "isShowModalDeleteSim", "detailAPN", "isSearchAfterOpenChange", "isSearchAfterCollapChange", "addTooltip", "event", "setTimeout", "toggler", "originalEvent", "target", "closest", "setAttribute", "ngOnInit", "me", "userType", "sessionService", "userInfo", "type", "optionuserType", "USER_TYPE", "selectItems", "searchInfoStandard", "ratingPlanId", "contractor", "simGroupId", "customer", "dateFrom", "dateTo", "searchInfo", "simType", "userId", "loggable", "dataCreateGroupSim", "groupKey", "description", "formSearchSim", "group", "formCreateGroupSim", "items", "label", "itemExports", "command", "MAX_ROW_EXCEL_EXPORT", "helpExelExport", "messageCommonService", "onload", "exportExelSelected", "lstMsisdn", "map", "el", "checkDisabledExportExelFilter", "dataParams", "updateParams", "page", "size", "sort", "undefined", "exportExels", "MAX_ROW_EXPORT", "helpExport", "exportSimSelected", "checkDisabledExportFilter", "exportSim", "itemPushGroups", "checkDisablePushSimToGroup", "GROUP_ADMIN", "groupScope", "getListGroupSimForSelectPush", "visible", "GROUP_PROVINCE", "info", "PROVINCE", "GROUP_CUSTOMER", "home", "icon", "routerLink", "statuSims", "value", "SIM_STATUS", "ACTIVATED", "INACTIVED", "DEACTIVATED", "PURGED", "READY", "typeSims", "SIM_TYPE", "UNKNOWN", "ESIM", "SIM", "columns", "key", "align", "isShow", "isSort", "style", "cursor", "color", "funcClick", "id", "item", "simId", "toString", "getDetailSim", "funcGetClassname", "funcConvertText", "funcCustomizeToolTip", "originalConnectionStatus", "isShowTooltip", "convertLongDateToString", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "pageNumber", "pageSize", "contractCodeDefault", "route", "snapshot", "paramMap", "get", "searchPanelCollapsRoute", "dataSet", "content", "total", "search", "getListProvince", "response", "onCollapsedChange", "collapsed", "console", "log", "reset<PERSON>ield", "reset", "ngAfterContentChecked", "groupSimSelected", "preventDefault", "params", "keySearch", "trim", "onSubmitSearch", "for<PERSON>ach", "getTime", "stringToStrBase64", "limit", "totalElements", "list", "sim", "push", "getConnectionStatus", "resp", "data", "userstate", "getDataUsed", "usagedData", "usedData", "offload", "checkInValidPushToGroupSim", "flag", "i", "groupName", "scope", "onChangeDateFrom", "onChangeDateTo", "pushGroupSim", "pushSimToGroup", "success", "checkExistGroupKey", "debounceService", "set", "groupkeyCheckExisted", "bind", "query", "isExistsGroupKey", "JSON", "stringify", "ADMIN", "DISTRICT", "includes", "CUSTOMER", "deleteSim", "listMsisdn", "e", "deleteListSim", "showModalDeleteSim", "resetDetailSim", "getById", "getDetailCustomer", "getDetailApn", "getStatusSim", "getDetailRatingPlan", "getDetailContract", "Promise", "resolve", "reject", "getDetailStatus", "gprsStatus", "icStatus", "ocStatus", "iddStatus", "smtStatus", "smoStatus", "error", "getDetailPlanSim", "apnCode", "ip", "rangeIp", "SERVICE_TYPE", "PREPAID", "POSTPAID", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProvinceService", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppSimListComponent_Template", "rf", "ctx", "AppSimListComponent_Template_p_button_click_6_listener", "AppSimListComponent_Template_p_splitButton_onClick_7_listener", "AppSimListComponent_Template_form_ngSubmit_8_listener", "AppSimListComponent_Template_p_panel_collapsedChange_9_listener", "AppSimListComponent_ng_template_10_Template", "AppSimListComponent_ng_template_11_Template", "AppSimListComponent_Template_input_ngModelChange_15_listener", "AppSimListComponent_Template_input_ngModelChange_20_listener", "AppSimListComponent_Template_p_dropdown_ngModelChange_25_listener", "AppSimListComponent_Template_vnpt_select_valueChange_30_listener", "AppSimListComponent_Template_input_ngModelChange_33_listener", "AppSimListComponent_Template_vnpt_select_valueChange_38_listener", "AppSimListComponent_Template_vnpt_select_valueChange_41_listener", "AppSimListComponent_Template_input_ngModelChange_44_listener", "AppSimListComponent_Template_vnpt_select_valueChange_49_listener", "AppSimListComponent_Template_p_calendar_ngModelChange_52_listener", "AppSimListComponent_Template_p_calendar_onSelect_52_listener", "AppSimListComponent_Template_p_calendar_onInput_52_listener", "AppSimListComponent_Template_p_calendar_ngModelChange_57_listener", "AppSimListComponent_Template_p_calendar_onSelect_57_listener", "AppSimListComponent_Template_p_calendar_onInput_57_listener", "AppSimListComponent_Template_p_dropdown_ngModelChange_62_listener", "AppSimListComponent_Template_p_dropdown_ngModelChange_67_listener", "AppSimListComponent_Template_vnpt_select_valueChange_72_listener", "AppSimListComponent_Template_table_vnpt_selectItemsChange_75_listener", "AppSimListComponent_Template_p_dialog_visibleChange_77_listener", "AppSimListComponent_Template_vnpt_select_valueChange_82_listener", "AppSimListComponent_p_button_84_Template", "AppSimListComponent_Template_p_button_click_86_listener", "AppSimListComponent_Template_p_button_click_87_listener", "AppSimListComponent_p_dialog_89_Template", "AppSimListComponent_Template_p_dialog_visibleChange_91_listener", "AppSimListComponent_Template_form_ngSubmit_92_listener", "AppSimListComponent_span_97_Template", "AppSimListComponent_span_98_Template", "AppSimListComponent_span_99_Template", "AppSimListComponent_Template_input_ngModelChange_106_listener", "AppSimListComponent_Template_input_keyup_106_listener", "AppSimListComponent_small_110_Template", "AppSimListComponent_small_111_Template", "AppSimListComponent_small_112_Template", "AppSimListComponent_small_113_Template", "AppSimListComponent_Template_input_ngModelChange_120_listener", "AppSimListComponent_small_124_Template", "AppSimListComponent_small_125_Template", "AppSimListComponent_small_126_Template", "AppSimListComponent_div_127_Template", "AppSimListComponent_div_128_Template", "AppSimListComponent_Template_textarea_ngModelChange_133_listener", "AppSimListComponent_small_137_Template", "AppSimListComponent_Template_p_button_click_139_listener", "AppSimListComponent_Template_p_dialog_visibleChange_142_listener", "AppSimListComponent_Template_p_button_click_147_listener", "AppSimListComponent_Template_p_button_click_148_listener", "ɵɵpropertyInterpolate", "_c5", "_c6", "_c7", "_c8", "_c9", "_c10", "<PERSON><PERSON><PERSON><PERSON>", "_c11", "GROUP_SIM", "CREATE", "controls", "dirty", "errors", "required", "max<PERSON><PERSON><PERSON>", "pattern", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\list\\app.sim.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\list\\app.sim.list.component.html"], "sourcesContent": ["import { Component, Inject, OnInit, AfterContentChecked, Injector } from \"@angular/core\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { ColumnInfo, OptionTable } from \"../../common-module/table/table.component\";\r\nimport { SimService } from \"src/app/service/sim/SimService\";\r\nimport { FormBuilder, FormControl } from \"@angular/forms\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { GroupSimService } from \"src/app/service/group-sim/GroupSimService\";\r\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\r\nimport { RatingPlanService } from \"src/app/service/rating-plan/RatingPlanService\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { ProvinceService } from \"src/app/service/account/ProvinceService\";\r\n\r\n@Component({\r\n    selector: \"app-sim-list\",\r\n    templateUrl: './app.sim.list.component.html'\r\n})\r\nexport class AppSimListComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        msisdn: string|null,\r\n        imsi: string|null,\r\n        ratingPlanId:number|null,\r\n        contractCode: string|null,\r\n        contractor: string|null,\r\n        status: any,\r\n        simGroupId: number|null,\r\n        customer: string|null,\r\n        dateFrom: Date|null,\r\n        dateTo: Date|null,\r\n        apnId: string|null,\r\n        simType: number|null,\r\n        provinceCode : null,\r\n        userId : null,\r\n        loggable : boolean | null\r\n    };\r\n    searchInfoStandard:any;\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    dataStore: Array<any>;\r\n    selectItems: Array<{imsi:number,msisdn: any,groupName:string|null,[key:string]:any}>;\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    itemExports: Array<MenuItem>;\r\n    itemPushGroups: Array<MenuItem>;\r\n    formSearchSim: any;\r\n    formCreateGroupSim: any;\r\n    dataCreateGroupSim: {\r\n        name: string|null,\r\n        groupKey: string|null,\r\n        description: string|null\r\n    };\r\n    isExistsGroupKey: boolean|false;\r\n    statuSims: Array<any>;\r\n    typeSims: Array<any>;\r\n    listRatingPlan: Array<any>;\r\n    listGroupSim: Array<any>;\r\n    listCustomer: Array<any>;\r\n    listGroupSimToSelect: Array<any>;\r\n    maxDateFrom: Date|number|string|null = new Date();\r\n    minDateTo: Date|number|string|null = null;\r\n    maxDateTo: Date|number|string|null = new Date();\r\n    isShowDialogCreateGroup:boolean = false;\r\n    isShowDialogPushGroup:boolean = false;\r\n    groupSimSelected: number|null;\r\n    userType: number;\r\n    provinceCode: any;\r\n    provinceName: any;\r\n    customerCode: any;\r\n    customerName: any;\r\n    groupScope: number;\r\n    groupScopeObjects: any = CONSTANTS.GROUP_SCOPE;\r\n    allPermissions = CONSTANTS.PERMISSIONS;\r\n    paramSearchGroupSim: any = {};\r\n    listProvince = [];\r\n    isShowModalDeleteSim : boolean = false;\r\n    isShowModalDetailSim : boolean = false;\r\n    optionuserType : any;\r\n    simId: string;\r\n    detailSim:any = {};\r\n    detailStatusSim: any={};\r\n    detailCustomer:any={};\r\n    detailRatingPlan: any={};\r\n    detailContract: any={};\r\n    detailAPN: any={};\r\n    searchPanelCollaps = true;\r\n    quickSearchValue = null\r\n    isSearchAfterOpenChange = false\r\n    isSearchAfterCollapChange = false\r\n    // tranService: TranslateService;\r\n    constructor(@Inject(SimService) private simService: SimService,\r\n                private formBuilder: FormBuilder,\r\n                @Inject(GroupSimService) private groupSimService: GroupSimService,\r\n                @Inject(CustomerService) private customerService: CustomerService,\r\n                @Inject(RatingPlanService) private ratingPlanService: RatingPlanService,\r\n                private provinceService: ProvinceService,\r\n                private injector: Injector) {\r\n        super(injector);\r\n    }\r\n    addTooltip(event: any) {\r\n        setTimeout(() => {\r\n            const toggler = event.originalEvent.target.closest('.p-panel-titlebar-toggler');\r\n            if (toggler) {\r\n                toggler.setAttribute('pTooltip', 'Toggle Panel');\r\n                toggler.setAttribute('tooltipPosition', 'top');\r\n            }\r\n        });\r\n    }\r\n    ngOnInit(){\r\n        let me = this;\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.optionuserType = CONSTANTS.USER_TYPE;\r\n        this.selectItems = [];\r\n        this.detailSim = {};\r\n        this.searchInfoStandard = {\r\n            msisdn: null,\r\n            imsi: null,\r\n            ratingPlanId: null,\r\n            contractCode: null,\r\n            contractor: null,\r\n            status: null,\r\n            simGroupId: null,\r\n            customer: null,\r\n            dateFrom: null,\r\n            dateTo: null,\r\n            apnId: null,\r\n        };\r\n        this.searchInfo = {\r\n            msisdn: null,\r\n            imsi: null,\r\n            ratingPlanId: null,\r\n            contractCode: null,\r\n            contractor: null,\r\n            status: null,\r\n            simGroupId: null,\r\n            customer: null,\r\n            dateFrom: null,\r\n            dateTo: null,\r\n            apnId: null,\r\n            simType: null,\r\n            provinceCode : null,\r\n            userId : null,\r\n            loggable : null\r\n        };\r\n        this.dataCreateGroupSim = {\r\n            groupKey: null,\r\n            name: null,\r\n            description: null\r\n        }\r\n        this.quickSearchValue = \"\";\r\n        this.formSearchSim = this.formBuilder.group(this.searchInfo);\r\n        this.formCreateGroupSim = this.formBuilder.group(this.dataCreateGroupSim);\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.simmgmt\") }, { label: this.tranService.translate(\"global.menu.listsim\") },];\r\n        this.itemExports = this.itemExports = [\r\n\r\n            {\r\n                label: this.tranService.translate(\"global.button.exportExelSelect\"),\r\n                command: ()=>{\r\n                    if(me.selectItems.length == 0 || me.selectItems.length > CONSTANTS.MAX_ROW_EXCEL_EXPORT){\r\n                        me.helpExelExport(0);\r\n                        return;\r\n                    }\r\n                    me.messageCommonService.onload();\r\n                    me.simService.exportExelSelected({lstMsisdn: me.selectItems.map(el => el.msisdn)})\r\n                },\r\n            },\r\n            {\r\n                label: this.tranService.translate(\"global.button.exportExelFilter\"),\r\n                command: ()=>{\r\n                    if(me.checkDisabledExportExelFilter()){\r\n                        me.helpExelExport(1);\r\n                        return;\r\n                    }\r\n                    me.messageCommonService.onload();\r\n                    let dataParams:any = {};\r\n                    me.updateParams(dataParams)\r\n                    delete dataParams.page;\r\n                    delete dataParams.size;\r\n                    delete dataParams.sort;\r\n                    if((!this.isSearchAfterOpenChange && !this.searchPanelCollaps) || (this.isSearchAfterCollapChange && this.searchPanelCollaps && this.quickSearchValue != null && this.quickSearchValue != undefined))\r\n                        dataParams = {'keySearch' : this.quickSearchValue}\r\n                    me.simService.exportExels(dataParams);\r\n                },\r\n            },\r\n            {\r\n                label: this.tranService.translate(\"global.button.exportSelect\"),\r\n                command: ()=>{\r\n                    if(me.selectItems.length == 0 || me.selectItems.length > CONSTANTS.MAX_ROW_EXPORT){\r\n                        me.helpExport(0);\r\n                        return;\r\n                    }\r\n                    me.messageCommonService.onload();\r\n                    me.simService.exportSimSelected({lstMsisdn: me.selectItems.map(el => el.msisdn)})\r\n                },\r\n            },\r\n            {\r\n                label: this.tranService.translate(\"global.button.exportFilter\"),\r\n                command: ()=>{\r\n                    if(me.checkDisabledExportFilter()){\r\n                        me.helpExport(1);\r\n                        return;\r\n                    }\r\n                    me.messageCommonService.onload();\r\n                    let dataParams:any = {};\r\n                    me.updateParams(dataParams)\r\n                    delete dataParams.page;\r\n                    delete dataParams.size;\r\n                    delete dataParams.sort\r\n                    if((!this.isSearchAfterOpenChange && !this.searchPanelCollaps) || (this.isSearchAfterCollapChange && this.searchPanelCollaps && this.quickSearchValue != null && this.quickSearchValue != undefined))\r\n                        dataParams = {'keySearch' : this.quickSearchValue}\r\n                    me.simService.exportSim(dataParams);\r\n                },\r\n            }\r\n        ];\r\n        this.itemPushGroups = [\r\n            {\r\n                label: this.tranService.translate(\"groupSim.scope.admin\"),\r\n                command: ()=>{\r\n                    if(me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN)){\r\n                        me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_ADMIN;\r\n                        me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN);\r\n                        me.isShowDialogPushGroup = true\r\n\r\n                    }\r\n                },\r\n                visible: me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN)\r\n            },\r\n            {\r\n                label: this.tranService.translate(\"groupSim.scope.province\"),\r\n                command: ()=>{\r\n                    if(me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE)){\r\n                        me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE;\r\n                        me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE);\r\n                        me.isShowDialogPushGroup = true;\r\n                    }else{\r\n                        me.messageCommonService.info(me.tranService.translate(\"sim.text.sameProvince\"),me.tranService.translate(\"sim.text.pushSim\"));\r\n                    }\r\n                },\r\n                visible: me.userType == CONSTANTS.USER_TYPE.PROVINCE\r\n            },\r\n            {\r\n                label: this.tranService.translate(\"groupSim.scope.customer\"),\r\n                command: ()=>{\r\n                    if(me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER)){\r\n                        me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER;\r\n                        me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER);\r\n                        me.isShowDialogPushGroup = true;\r\n                    }else{\r\n                        me.messageCommonService.info(me.tranService.translate(\"sim.text.sameCustomer\"),me.tranService.translate(\"sim.text.pushSim\"));\r\n                    }\r\n                },\r\n            }\r\n        ]\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        this.statuSims = [\r\n            // {\r\n            //     value: CONSTANTS.SIM_STATUS.READY,\r\n            //     name: this.tranService.translate(\"sim.status.ready\")\r\n            // },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.ACTIVATED],\r\n                name: this.tranService.translate(\"sim.status.activated\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.INACTIVED],\r\n                name: this.tranService.translate(\"sim.status.inactivated\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.DEACTIVATED],\r\n                name: this.tranService.translate(\"sim.status.deactivated\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.PURGED],\r\n                name: this.tranService.translate(\"sim.status.purged\")\r\n            },\r\n            {\r\n                value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],\r\n                name: this.tranService.translate(\"sim.status.processingChangePlan\")\r\n            },\r\n            {\r\n                value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],\r\n                name: this.tranService.translate(\"sim.status.processingRegisterPlan\")\r\n            },\r\n            {\r\n                value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],\r\n                name: this.tranService.translate(\"sim.status.waitingCancelPlan\")\r\n            },\r\n        ]\r\n\r\n        this.typeSims = [\r\n            {\r\n                value: [CONSTANTS.SIM_TYPE.UNKNOWN],\r\n                name: this.tranService.translate(\"sim.type.unknown\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_TYPE.ESIM],\r\n                name: this.tranService.translate(\"sim.type.esim\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_TYPE.SIM],\r\n                name: this.tranService.translate(\"sim.type.sim\")\r\n            },\r\n        ]\r\n\r\n        this.columns = [{\r\n            name: this.tranService.translate(\"sim.label.sothuebao\"),\r\n            key: \"msisdn\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true,\r\n            style:{\r\n                cursor: \"pointer\",\r\n             color: \"var(--mainColorText)\"\r\n            },\r\n            funcClick(id, item) {\r\n                me.simId = id.toString();\r\n                me.getDetailSim();\r\n                me.isShowModalDetailSim = true;\r\n            },\r\n            funcGetClassname(){\r\n                return \"cursor-pointer\"\r\n            }\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.imsi\"),\r\n            key: \"imsi\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true\r\n        },\r\n        {\r\n            name: this.tranService.translate(\"sim.label.dungluong\"),\r\n            key: \"usagedData\",\r\n            size: \"150px\",\r\n            align: \"right\",\r\n            isShow: true,\r\n            isSort: true,\r\n            funcConvertText: function(value){\r\n                return me.utilService.bytesToMegabytes(value);\r\n            }\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.tengoicuoc\"),\r\n            key: \"ratingPlanName\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.nhomsim\"),\r\n            key: \"groupName\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: false,\r\n            isSort: true,\r\n        }, {\r\n            name: this.tranService.translate(\"sim.label.trangthaiketnoi\"),\r\n            key: \"connectionStatus\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true,\r\n            funcGetClassname: (value) => {\r\n                if(value == 'ON'){\r\n                    return ['p-2', 'text-green-800', \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                }else {\r\n                    return ['p-2', 'text-50', \"surface-500\", \"border-round\",\"inline-block\"];\r\n                }\r\n            },\r\n            funcConvertText: function(value){\r\n                return value == null? 'UNKNOWN' : value;\r\n            },\r\n            funcCustomizeToolTip: function(value, item){\r\n                if(item.originalConnectionStatus == 0){\r\n                    return me.tranService.translate(\"sim.label.statusDetach\")\r\n                }else if(item.originalConnectionStatus == 1){\r\n                    return me.tranService.translate(\"sim.label.statusNotAttach\")\r\n                }else if(item.originalConnectionStatus == 2){\r\n                    return me.tranService.translate(\"sim.label.statusAttach\")\r\n                }else if(item.originalConnectionStatus == 3){\r\n                    return me.tranService.translate(\"sim.label.statusNotConnect\")\r\n                }else if(item.originalConnectionStatus == 4){\r\n                    return me.tranService.translate(\"sim.label.statusConnect\")\r\n                }else if(item.originalConnectionStatus == 5){\r\n                    return me.tranService.translate(\"sim.label.statusNetwork\")\r\n                }else{\r\n                    return item.originalConnectionStatus\r\n                }\r\n            },\r\n            isShowTooltip:true\r\n         },\r\n            {\r\n            name: this.tranService.translate(\"sim.label.trangthaisim\"),\r\n            key: \"status\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: false,\r\n            isSort: true,\r\n            funcGetClassname: (value) => {\r\n                if(value == 0){\r\n                    return ['p-1' , \"border-round\", \"border-400\", \"text-color\",\"inline-block\"];\r\n                }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n                    // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n                    return ['p-2', \"text-green-800\", \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n                    return ['p-2', 'text-green-800', \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n                    return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\",\"inline-block\"];\r\n                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n                    return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\",\"inline-block\"];\r\n                }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n                    return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\",\"inline-block\"];\r\n                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n                    return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\",\"inline-block\"];\r\n                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n                    return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\",\"inline-block\"];\r\n                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n                    return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\",\"inline-block\"];\r\n                }\r\n                return [];\r\n            },\r\n            funcConvertText: (value)=>{\r\n                if(value == 0){\r\n                    return me.tranService.translate(\"sim.status.inventory\");\r\n                }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n                    // return me.tranService.translate(\"sim.status.ready\");\r\n                    return me.tranService.translate(\"sim.status.activated\");\r\n                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n                    return me.tranService.translate(\"sim.status.activated\");\r\n                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n                    return me.tranService.translate(\"sim.status.deactivated\");\r\n                }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n                    return me.tranService.translate(\"sim.status.purged\");\r\n                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n                    return me.tranService.translate(\"sim.status.inactivated\");\r\n                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n                    return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n                    return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n                    return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n                }\r\n                return \"\";\r\n            },\r\n            style:{\r\n                color: \"white\"\r\n            }\r\n        },\r\n            {\r\n            name: this.tranService.translate(\"sim.label.ngaykichhoat\"),\r\n            key: \"activatedDate\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true,\r\n            funcConvertText: function(value){\r\n                return me.utilService.convertLongDateToString(value);\r\n            }\r\n        }, {\r\n            name: this.tranService.translate(\"sim.label.startDate\"),\r\n            key: \"startDate\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: false,\r\n            isSort: true,\r\n            funcConvertText: function(value){\r\n            return me.utilService.convertLongDateToString(value);\r\n        }\r\n        },\r\n            {\r\n            name: this.tranService.translate(\"sim.label.maapn\"),\r\n            key: \"apnId\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: false,\r\n            isSort: true\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.khachhang\"),\r\n            key: \"customerName\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.makhachhang\"),\r\n            key: \"customerCode\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.mahopdong\"),\r\n            key: \"contractCode\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.ngaylamhopdong\"),\r\n            key: \"contractDate\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: false,\r\n            isSort: true,\r\n            funcConvertText:(value)=>{\r\n                return me.utilService.convertLongDateToString(value);\r\n            }\r\n        },{\r\n            name: this.tranService.translate(\"sim.label.nguoilamhopdong\"),\r\n            key: \"contractInfo\",\r\n            size: \"150px\",\r\n            align: \"left\",\r\n            isShow: false,\r\n            isSort: true\r\n        },\r\n            {\r\n                name: this.tranService.translate(\"sim.label.simType\"),\r\n                key: \"simType\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: false,\r\n                isSort: true,\r\n                funcGetClassname: (value) => {\r\n                    if(value == CONSTANTS.SIM_TYPE.UNKNOWN){\r\n                        return ['p-1' , \"text-orange-600\", \"bg-orange-100\", \"border-round\",\"inline-block\"];\r\n                    }else if(value == CONSTANTS.SIM_TYPE.ESIM){\r\n                        return ['p-2', \"text-blue-600\", \"bg-blue-100\",\"border-round\",\"inline-block\"];\r\n                    }else if(value == CONSTANTS.SIM_TYPE.SIM){\r\n                        return ['p-2', 'text-green-600', \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                    }\r\n                    return [];\r\n                },\r\n                funcConvertText: (value)=>{\r\n                    if(value == CONSTANTS.SIM_TYPE.UNKNOWN){\r\n                        return me.tranService.translate(\"sim.type.unknown\")\r\n                    }else if(value == CONSTANTS.SIM_TYPE.ESIM){\r\n                        return me.tranService.translate(\"sim.type.esim\")\r\n                    }else if(value == CONSTANTS.SIM_TYPE.SIM){\r\n                        return me.tranService.translate(\"sim.type.sim\")\r\n                    }\r\n                    return \"\";\r\n                },\r\n                style:{\r\n                    color: \"white\"\r\n                }\r\n            }\r\n    ];\r\n\r\n        this.optionTable = {\r\n            hasClearSelected:false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: true,\r\n        }\r\n        this.pageNumber = 0;\r\n        this.pageSize= 10;\r\n        this.sort = \"msisdn,asc\"\r\n        let contractCodeDefault = this.route.snapshot.paramMap.get(\"contractCode\");\r\n        let searchPanelCollapsRoute = this.route.snapshot.paramMap.get(\"searchPanelCollapsRoute\");\r\n        if((contractCodeDefault || \"\").length > 0){\r\n            this.searchInfo.contractCode = contractCodeDefault;\r\n        }\r\n        if((searchPanelCollapsRoute || \"\").length > 0 && searchPanelCollapsRoute == 'false'){\r\n            this.searchPanelCollaps = false;\r\n        }\r\n        this.dataSet ={\r\n            content: [],\r\n            total: 0\r\n        }\r\n\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n        this.getListProvince();\r\n    }\r\n\r\n    getListProvince(){\r\n        let me = this;\r\n        this.provinceService.getListProvince((response)=>{\r\n            me.listProvince = response;\r\n        })\r\n    }\r\n\r\n    onCollapsedChange(collapsed: boolean) {\r\n        // this.isSearchAfterOpenChange = false\r\n        // this.isSearchAfterCollapChange = false\r\n        console.log('Panel collapsed state:', collapsed);\r\n        this.searchPanelCollaps = collapsed\r\n    }\r\n\r\n    resetField(){\r\n        this.formSearchSim.reset();\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        let me = this;\r\n        if(this.isShowDialogPushGroup == false){\r\n            this.groupSimSelected = null;\r\n        }\r\n        if(this.isShowDialogCreateGroup == false){\r\n            this.formCreateGroupSim.reset();\r\n        }\r\n    }\r\n\r\n    quickSearch(){\r\n        event.preventDefault();\r\n        let params = {}\r\n        if (this.quickSearchValue != null && this.quickSearchValue != undefined)\r\n            params = {\r\n                keySearch: this.quickSearchValue.trim()\r\n            }\r\n        console.log(params)\r\n        this.search(0, this.pageSize, this.sort, params);\r\n    }\r\n\r\n    onSubmitSearch(){\r\n        this.pageNumber = 0;\r\n        this.searchInfo.loggable = true;\r\n        this.search(0, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    updateParams(dataParams){\r\n        let me = this;\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if(this.searchInfo[key] != null){\r\n                if(key == \"dateFrom\"){\r\n                    dataParams[\"contractDateFrom\"] = this.searchInfo.dateFrom.getTime();\r\n                }else if(key == \"dateTo\"){\r\n                    dataParams[\"contractDateTo\"] = this.searchInfo.dateTo.getTime();\r\n                }else if(key == \"contractCode\"){\r\n                    dataParams[\"contractCode\"] = me.utilService.stringToStrBase64(this.searchInfo.contractCode);\r\n                }\r\n                else{\r\n                    dataParams[key] = this.searchInfo[key];\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n    search(page, limit, sort, params){\r\n        if(this.searchPanelCollaps){\r\n            this.isSearchAfterCollapChange = true\r\n            this.isSearchAfterOpenChange = false\r\n        }else{\r\n            this.isSearchAfterCollapChange = false\r\n            this.isSearchAfterOpenChange = true\r\n        }\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let me = this;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        this.updateParams(dataParams);\r\n        me.messageCommonService.onload();\r\n        if(this.searchPanelCollaps){\r\n            params[\"page\"] = page;\r\n            params[\"size\"] = limit\r\n            params[\"sort\"] = sort\r\n            params[\"loggable\"] = true\r\n            params[\"keySearch\"] = this.quickSearchValue.trim();\r\n            console.log(params)\r\n            this.simService.quickSearch(params, (response)=>{\r\n                // console.log(response.content)\r\n                me.dataSet = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n                me.searchInfoStandard = {...me.searchInfo}\r\n                let list = []\r\n                for (const sim of me.dataSet.content) {\r\n                    list.push(sim.msisdn)\r\n                }\r\n                // lấy trạng thái kết nối\r\n                this.simService.getConnectionStatus(list, (resp) =>{\r\n                    let data = [...resp]\r\n                    for (const sim of me.dataSet.content) {\r\n                        for(let el of data) {\r\n                            if(sim.msisdn == el.msisdn) {\r\n                                sim.connectionStatus = this.getSimStatus(el.userstate)\r\n                                sim.originalConnectionStatus = el.userstate\r\n                            }\r\n                        }\r\n                    }\r\n                    // console.log(data)\r\n                }, null, ()=> {});\r\n                this.simService.getDataUsed(list, (resp)=>{\r\n                    for (const sim of me.dataSet.content) {\r\n                        for(let el of resp) {\r\n                            if(sim.msisdn == el.msisdn) {\r\n                                sim.usagedData = el.usedData\r\n                            }\r\n                        }\r\n                    }\r\n                })\r\n\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            return\r\n        }\r\n        dataParams[\"page\"] = page;\r\n        dataParams[\"size\"] = limit\r\n        dataParams[\"sort\"] = sort\r\n        this.simService.search(dataParams, (response)=>{\r\n            console.log(response.content)\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            me.searchInfoStandard = {...me.searchInfo}\r\n            let list = []\r\n            for (const sim of me.dataSet.content) {\r\n                list.push(sim.msisdn)\r\n            }\r\n            // lấy trạng thái kết nối\r\n            this.simService.getConnectionStatus(list, (resp) =>{\r\n                let data = [...resp]\r\n                for (const sim of me.dataSet.content) {\r\n                    for(let el of data) {\r\n                        if(sim.msisdn == el.msisdn) {\r\n                            sim.connectionStatus = this.getSimStatus(el.userstate)\r\n                            sim.originalConnectionStatus = el.userstate\r\n                        }\r\n                    }\r\n                }\r\n                // console.log(data)\r\n            }, null, ()=> {});\r\n            this.simService.getDataUsed(list, (resp)=>{\r\n                for (const sim of me.dataSet.content) {\r\n                    for(let el of resp) {\r\n                        if(sim.msisdn == el.msisdn) {\r\n                            sim.usagedData = el.usedData\r\n                        }\r\n                    }\r\n                }\r\n            })\r\n\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getSimStatus(status) {\r\n        if(status == null || status == undefined){\r\n            return 'UNKNOWN'\r\n        }\r\n        if(status !== null && status !== undefined) {\r\n            if(status == '0' || status == '5'){\r\n                return 'OFF'\r\n            } else if (status == '1' || status == '2' || status == '3' || status == '4') {\r\n                return 'ON'\r\n            } else {\r\n                return 'NOT FOUND'\r\n            }\r\n        }else return 'NOT FOUND'\r\n    }\r\n\r\n    getTooltipStatus(status){\r\n        if(status == 0){\r\n            return this.tranService.translate(\"sim.label.statusDetach\")\r\n        }else if(status == 1){\r\n            return this.tranService.translate(\"sim.label.statusNotAttach\")\r\n        }else if(status == 2){\r\n            return this.tranService.translate(\"sim.label.statusAttach\")\r\n        }else if(status == 3){\r\n            return this.tranService.translate(\"sim.label.statusNotConnect\")\r\n        }else if(status == 4){\r\n            return this.tranService.translate(\"sim.label.statusConnect\")\r\n        }else if(status == 5){\r\n            return this.tranService.translate(\"sim.label.statusNetwork\")\r\n        }else{\r\n            return status\r\n        }\r\n    }\r\n\r\n    checkInValidPushToGroupSim(){\r\n        if(this.selectItems.length == 0){\r\n            return true;\r\n        }\r\n        let flag = false;\r\n        for(let i = 0;i<this.selectItems.length;i++){\r\n            if((this.selectItems[i].groupName || \"\") != \"\"){\r\n                flag = true;\r\n                break;\r\n            }\r\n        }\r\n        return flag;\r\n    }\r\n\r\n\r\n    getListGroupSimForSelectPush(type){\r\n        let me = this;\r\n        let params = {scope: type};\r\n        if(type == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN){\r\n        }else if(type == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){\r\n            params['provinceCode'] = this.provinceCode;\r\n        }else if(type == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){\r\n            params['customerCode'] = this.customerCode;\r\n        }\r\n        this.paramSearchGroupSim = {...params};\r\n    }\r\n\r\n    onChangeDateFrom(value){\r\n        if(value){\r\n            this.minDateTo = value;\r\n        }else{\r\n            this.minDateTo = null\r\n        }\r\n    }\r\n\r\n    onChangeDateTo(value){\r\n        if(value){\r\n            this.maxDateFrom = value;\r\n        }else{\r\n            this.maxDateFrom = new Date();\r\n        }\r\n    }\r\n\r\n    pushGroupSim(type){\r\n        let me = this;\r\n        if(type == 0){//push group available\r\n            if(this.groupSimSelected == null || this.groupSimSelected == undefined){\r\n                return;\r\n            }\r\n            this.messageCommonService.onload();\r\n            this.simService.pushSimToGroup(this.selectItems.map(el => el.msisdn), {id: this.groupSimSelected}, (response)=>{\r\n                setTimeout(function(){\r\n                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                })\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.addGroupSuccess\"));\r\n                me.selectItems = [];\r\n            })\r\n            this.isShowDialogPushGroup = false;\r\n        }else{//create new group\r\n            let group = {\r\n                id: null,\r\n                groupKey: this.dataCreateGroupSim.groupKey,\r\n                name: this.dataCreateGroupSim.name,\r\n                customerCode: this.customerCode,\r\n                description: this.dataCreateGroupSim.description,\r\n                provinceCode: this.provinceCode,\r\n                scope: this.groupScope\r\n            }\r\n            this.messageCommonService.onload();\r\n            this.simService.pushSimToGroup(this.selectItems.map(el => el.msisdn), group, (response)=>{\r\n                setTimeout(function(){\r\n                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                })\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.addGroupSuccess\"));\r\n                me.selectItems = [];\r\n            })\r\n            this.isShowDialogCreateGroup = false;\r\n        }\r\n    }\r\n\r\n    checkExistGroupKey(){\r\n        if(this.dataCreateGroupSim.groupKey?.trim().length > 0){\r\n            let me = this;\r\n            this.debounceService.set(\"groupKey\", me.groupSimService.groupkeyCheckExisted.bind(me.groupSimService), {}, {query: me.dataCreateGroupSim.groupKey},(response)=>{\r\n                me.isExistsGroupKey = response == 1;\r\n            })\r\n        }\r\n    }\r\n\r\n    helpExport(type){\r\n        if(this.selectItems.length == 0 && type == 0){\r\n            this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportFilterEmpty\"),this.tranService.translate(\"global.button.export\"))\r\n        }else if(this.selectItems.length > CONSTANTS.MAX_ROW_EXPORT || this.dataSet.total > CONSTANTS.MAX_ROW_EXPORT){\r\n            this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportFilter\"),this.tranService.translate(\"global.button.export\"))\r\n        }\r\n\r\n    }\r\n\r\n    helpExelExport(type){\r\n        if(this.selectItems.length == 0 && type == 0){\r\n            this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportFilterEmpty\"),this.tranService.translate(\"global.button.export\"))\r\n        }else if(this.selectItems.length > CONSTANTS.MAX_ROW_EXCEL_EXPORT || this.dataSet.total > CONSTANTS.MAX_ROW_EXCEL_EXPORT){\r\n            this.messageCommonService.info(this.tranService.translate(\"global.message.conditionExportExelFilter\"),this.tranService.translate(\"global.button.export\"))\r\n        }\r\n\r\n    }\r\n\r\n    checkDisabledExportFilter(){\r\n        return JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoStandard) || this.dataSet.total == null\r\n            || (this.dataSet.total != null && (this.dataSet.total == 0 || this.dataSet.total > CONSTANTS.MAX_ROW_EXPORT));\r\n    }\r\n\r\n    checkDisabledExportExelFilter(){\r\n        return JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoStandard) || this.dataSet.total == null\r\n            || (this.dataSet.total != null && (this.dataSet.total == 0 || this.dataSet.total > CONSTANTS.MAX_ROW_EXCEL_EXPORT));\r\n    }\r\n\r\n    checkDisablePushSimToGroup(type):boolean{\r\n        if(type == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN){\r\n            return this.userType == CONSTANTS.USER_TYPE.ADMIN;\r\n        }\r\n        if((this.selectItems || []).length == 0) return false;\r\n        if(type == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){\r\n            if([CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT].includes(this.userType)){\r\n                this.provinceCode = this.selectItems[0]['provinceCode'];\r\n                this.provinceName = null;\r\n                for(let i = 0;i<this.listProvince.length;i++){\r\n                    if(this.listProvince[i].code == this.provinceCode){\r\n                        this.provinceName = this.listProvince[i].name;\r\n                        break;\r\n                    }\r\n                }\r\n                if(this.provinceName == null){\r\n                    this.provinceName = this.selectItems[0]['provinceName'];\r\n                }\r\n                for(let i = 1; i < this.selectItems.length;i++){\r\n                    if(this.selectItems[i]['provinceCode'] != this.provinceCode){\r\n                        return false;\r\n                    }\r\n                }\r\n                return true;\r\n            }\r\n        }else if(type == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){\r\n            if([CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER].includes(this.userType)){\r\n                this.customerCode = this.selectItems[0]['customerCode'];\r\n                this.customerName = this.selectItems[0]['customerName'];\r\n                this.provinceCode = this.selectItems[0]['provinceCode'];\r\n                for(let i = 1; i < this.selectItems.length;i++){\r\n                    if(this.selectItems[i]['customerCode'] != this.customerCode){\r\n                        return false;\r\n                    }\r\n                }\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    deleteSim(){\r\n        let me = this;\r\n        let listMsisdn = this.selectItems.map(e => e.msisdn)\r\n        this.isShowModalDeleteSim = false;\r\n        // TODO : call api xóa sim\r\n        me.messageCommonService.onload();\r\n        this.simService.deleteListSim(listMsisdn, (response)=>{\r\n            me.isShowModalDeleteSim = false;\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\r\n            me.selectItems = []\r\n        },null , ()=>{\r\n            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        })\r\n    }\r\n\r\n    showModalDeleteSim(){\r\n        if(this.selectItems.length === 0) return;\r\n        this.isShowModalDeleteSim = true;\r\n    }\r\n\r\n    getDetailSim(){\r\n        let me = this;\r\n        me.resetDetailSim()\r\n        this.messageCommonService.onload();\r\n        me.simService.getById(me.simId, (response)=>{\r\n            me.detailSim = {\r\n                ...response\r\n            }\r\n\r\n            me.getDetailCustomer();\r\n            me.getDetailApn();\r\n            me.getStatusSim()\r\n            me.getDetailRatingPlan()\r\n            me.getDetailContract()\r\n            // this.messageCommonService.onload();\r\n            // Promise.all([\r\n            //     this.getStatusSim(),\r\n            //     this.getDetailRatingPlan(),\r\n            //     this.getDetailContract()\r\n            // ])\r\n            //     .then(([statusSim,ratingPlan, contract]) => {\r\n            //     })\r\n            //     .catch(error => {\r\n            //         this.messageCommonService.error(this.tranService.translate(\"global.message.errorLoading\"));\r\n            //         console.error(\"Error loading:\", error);\r\n            //         this.isShowModalDetailSim = false;\r\n            //     })\r\n            //     .finally(() => {\r\n            //         this.messageCommonService.offload();\r\n            //     });\r\n            me.getDetailApn();\r\n            me.simService.getConnectionStatus([me.simId], (resp)=>{\r\n                me.detailSim.connectionStatus = resp[0].userstate\r\n            }, ()=>{})\r\n        }, null,()=>{\r\n            this.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    resetDetailSim(){\r\n        let me = this;\r\n        me.detailContract = {}\r\n        me.detailCustomer = {}\r\n        me.detailRatingPlan = {}\r\n        me.detailStatusSim = {}\r\n        me.detailSim = {}\r\n    }\r\n\r\n    getStatusSim(): Promise<any> {\r\n        return new Promise((resolve, reject) => {\r\n            this.simService.getDetailStatus(\r\n                this.detailSim.msisdn,\r\n                (response) => {\r\n                    this.detailStatusSim =  {\r\n                        statusData: response.gprsStatus == 1,\r\n                        statusReceiveCall: response.icStatus == 1,\r\n                        statusSendCall: response.ocStatus == 1,\r\n                        statusWorldCall: response.iddStatus == 1,\r\n                        statusReceiveSms: response.smtStatus == 1,\r\n                        statusSendSms: response.smoStatus == 1\r\n                    };\r\n                    resolve(response);\r\n                },\r\n                (error) => {\r\n                    reject(error);\r\n                },\r\n                () => {\r\n\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    getDetailCustomer(){\r\n        this.detailCustomer = {\r\n            name: this.detailSim.customerName,\r\n            code: this.detailSim.customerCode\r\n        }\r\n    }\r\n\r\n    getDetailRatingPlan(): Promise<any> {\r\n        return new Promise((resolve, reject) => {\r\n            this.simService.getDetailPlanSim(\r\n                this.detailSim.msisdn,\r\n                (response) => {\r\n                    this.detailRatingPlan = { ...response };\r\n                    resolve(response);\r\n                },\r\n                (error) => {\r\n                    reject(error);\r\n                },\r\n                () => {\r\n\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    getDetailContract(): Promise<any> {\r\n        return new Promise((resolve, reject) => {\r\n            this.simService.getDetailContract(\r\n                this.utilService.stringToStrBase64(this.detailSim.contractCode),\r\n                (response) => {\r\n                    this.detailContract = response;\r\n                    resolve(response);\r\n                },\r\n                (error) => {\r\n                    reject(error);\r\n                },\r\n                () => {\r\n\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n\r\n    getDetailApn(){\r\n        this.detailAPN = {\r\n            code: this.detailSim.apnCode,\r\n            type: \"Kết nối bằng 3G\",\r\n            ip: 0,\r\n            rangeIp: this.detailSim.ip\r\n        }\r\n    }\r\n\r\n    getNameStatus(value){\r\n        if(value == 0){\r\n            return this.tranService.translate(\"sim.status.inventory\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n            // return this.tranService.translate(\"sim.status.ready\");\r\n            return this.tranService.translate(\"sim.status.activated\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n            return this.tranService.translate(\"sim.status.activated\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n            return this.tranService.translate(\"sim.status.deactivated\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n            return this.tranService.translate(\"sim.status.purged\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n            return this.tranService.translate(\"sim.status.inactivated\");\r\n        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n            return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n            return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n            return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getClassStatus(value){\r\n        if(value == 0){\r\n            return ['p-1' , \"border-round\", \"border-400\", \"text-color\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n            // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n            return ['p-2', \"text-green-800\", \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n            return ['p-2', 'text-green-800', \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n            return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n            return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n            return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n            return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n            return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n            return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\",\"inline-block\"];\r\n        }\r\n        return [];\r\n    }\r\n\r\n    getServiceType(value) {\r\n        if(value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate(\"sim.serviceType.prepaid\")\r\n        else if(value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate(\"sim.serviceType.postpaid\")\r\n        else return \"\"\r\n    }\r\n\r\n    protected readonly Object = Object;\r\n}\r\n", "<style>\r\n    /* .col-3{\r\n        padding: 10px;\r\n    } */\r\n</style>\r\n\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.listsim\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"responsive-button-container col-5 flex flex-row justify-content-end align-items-center gap-2\">\r\n        <p-button (click)=\"showModalDeleteSim()\" [disabled]=\"selectItems.length === 0\" icon=\"pi pi-minus-circle\" label=\"{{tranService.translate('global.button.delete')}}\" styleClass=\"p-button p-button-danger equal-button\"></p-button>\r\n        <p-splitButton styleClass=\"mr-2 p-button-info\" [label]=\"tranService.translate('global.button.export')\" styleClass=\"equal-button\" icon=\"pi pi-download\" [model]=\"itemExports\" (onClick)=\"helpExport(1)\"></p-splitButton>\r\n<!--        <p-splitButton *ngIf=\"checkAuthen([allPermissions.GROUP_SIM.UPDATE,allPermissions.GROUP_SIM.CREATE])\" styleClass=\"p-button-success\" [label]=\"tranService.translate('global.button.pushGroupSim')\" icon=\"pi pi-file-import\" [model]=\"itemPushGroups\" [disabled]=\"checkInValidPushToGroupSim()\"></p-splitButton>-->\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchSim\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" (collapsedChange)=\"onCollapsedChange($event)\" [collapsed]=\"searchPanelCollaps\">\r\n        <ng-template pTemplate=\"icons\">\r\n\r\n        </ng-template>\r\n        <ng-template pTemplate=\"header\">\r\n            <div *ngIf=\"searchPanelCollaps\"  class=\"flex flex-row gap-3\">\r\n                <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"quickSearch()\" [(ngModel)]=\"quickSearchValue\" [ngModelOptions]=\"{standalone: true}\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"button\"\r\n                          (click)=\"quickSearch()\"\r\n                ></p-button>\r\n            </div>\r\n\r\n            <div *ngIf=\"!searchPanelCollaps\" class=\"font-bold text-lg\">{{tranService.translate(\"global.text.advanceSearch\")}}</div>\r\n        </ng-template>\r\n        <div class=\"grid search-grid-2\">\r\n            <!-- so thue bao -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                            class=\"w-full\"\r\n                            pInputText id=\"msisdn\"\r\n                            [(ngModel)]=\"searchInfo.msisdn\"\r\n                            formControlName=\"msisdn\"\r\n                    />\r\n                    <label htmlFor=\"msisdn\">{{tranService.translate(\"sim.label.sothuebao\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- ismi -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"imsi\"\r\n                            [(ngModel)]=\"searchInfo.imsi\"\r\n                            formControlName=\"imsi\"\r\n                    />\r\n                    <label htmlFor=\"imsi\">{{tranService.translate(\"sim.label.imsi\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- status -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                            id=\"status\" [autoDisplayFirst]=\"false\"\r\n                            [(ngModel)]=\"searchInfo.status\"\r\n                            formControlName=\"status\"\r\n                            [options]=\"statuSims\"\r\n                            optionLabel=\"name\"\r\n                            optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label for=\"status\">{{tranService.translate(\"sim.label.trangthaisim\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- nhom sim -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.simGroupId\"\r\n                        [placeholder]=\"tranService.translate('sim.label.nhomsim')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"id\"\r\n                        displayPattern=\"${name} - ${groupKey}\"\r\n                        typeValue=\"primitive\"\r\n                        [floatLabel]=\"true\"\r\n                        [paramDefault]=\"{type: 'groupSim'}\"\r\n                        [stylePositionBoxSelect] = \"{top: '40px', right: '0px', 'min-width':'100%'}\"\r\n                        [isMultiChoice]=\"false\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n             <!-- maapn -->\r\n             <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"apnId\"\r\n                            [(ngModel)]=\"searchInfo.apnId\"\r\n                            formControlName=\"apnId\"\r\n                    />\r\n                    <label htmlFor=\"apnId\">{{tranService.translate(\"sim.label.maapn\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- goi cuoc -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.ratingPlanId\"\r\n                        [placeholder]=\"tranService.translate('sim.label.goicuoc')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"id\"\r\n                        displayPattern=\"${name} - ${code}\"\r\n                        typeValue=\"primitive\"\r\n                        [paramDefault]=\"{type: 'ratingPlan'}\"\r\n                        [floatLabel]=\"true\"\r\n                        [isMultiChoice]=\"false\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <!-- ma hop dong -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.contractCode\"\r\n                        [placeholder]=\"tranService.translate('sim.label.mahopdong')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"contractCode\"\r\n                        displayPattern=\"${contractCode}\"\r\n                        typeValue=\"primitive\"\r\n                        [paramDefault]=\"{type: 'contract'}\"\r\n                        [floatLabel]=\"true\"\r\n                        [isMultiChoice]=\"false\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <!-- nguoi lam hop dong -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"contractor\"\r\n                            [(ngModel)]=\"searchInfo.contractor\"\r\n                            formControlName=\"contractor\"\r\n                    />\r\n                    <label htmlFor=\"contractor\">{{tranService.translate(\"sim.label.nguoilamhopdong\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- khach hang -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.customer\"\r\n                        [placeholder]=\"tranService.translate('sim.label.khachhang')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"customerCode\"\r\n                        displayPattern=\"${customerName} - ${customerCode}\"\r\n                        typeValue=\"primitive\"\r\n                        [paramDefault]=\"{type: 'customer'}\"\r\n                        [floatLabel]=\"true\"\r\n                        [isMultiChoice]=\"false\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                            id=\"dateFrom\"\r\n                            [(ngModel)]=\"searchInfo.dateFrom\"\r\n                            formControlName=\"dateFrom\"\r\n                            [showIcon]=\"true\"\r\n                            [showClear]=\"true\"\r\n                            dateFormat=\"dd/mm/yy\"\r\n                            [maxDate]=\"maxDateFrom\"\r\n                            (onSelect)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                            (onInput)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                    ></p-calendar>\r\n                    <label htmlFor=\"dateFrom\">{{tranService.translate(\"sim.label.ngaylamhopdongtu\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                            id=\"dateTo\"\r\n                            [(ngModel)]=\"searchInfo.dateTo\"\r\n                            formControlName=\"dateTo\"\r\n                            [showIcon]=\"true\"\r\n                            [showClear]=\"true\"\r\n                            dateFormat=\"dd/mm/yy\"\r\n                            [minDate]=\"minDateTo\"\r\n                            [maxDate]=\"maxDateTo\"\r\n                            (onSelect)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                            (onInput)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                    />\r\n                    <label htmlFor=\"dateTo\">{{tranService.translate(\"sim.label.ngaylamhopdongden\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"simType\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.simType\"\r\n                                formControlName=\"simType\"\r\n                                [options]=\"typeSims\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label for=\"status\">{{tranService.translate(\"sim.label.simType\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- Tinh thanh pho -->\r\n            <div class=\"col-3\" [class]=\"userType == optionuserType.ADMIN ? '': 'hidden'\">\r\n                 <span class=\"p-float-label\">\r\n                <p-dropdown styleClass=\"w-full\"\r\n                            [showClear]=\"true\"\r\n                            id=\"province\" [autoDisplayFirst]=\"false\"\r\n                            [(ngModel)]=\"searchInfo.provinceCode\"\r\n                            formControlName=\"provinceCode\"\r\n                            [options]=\"listProvince\"\r\n                            optionLabel=\"name\"\r\n                            [filter]=\"true\" filterBy=\"name\"\r\n                            optionValue=\"code\"\r\n                            [placeholder]=\"tranService.translate('account.text.selectProvince')\"\r\n                            [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                ></p-dropdown>\r\n                    <label for=\"provinceCode\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                </span>\r\n            </div>\r\n<!--            tai khoan-->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.userId\"\r\n                        [placeholder]=\"tranService.translate('account.text.account')\"\r\n                        objectKey=\"dropdownListUser\"\r\n                        paramKey=\"username\"\r\n                        keyReturn=\"id\"\r\n                        displayPattern=\"${username}\"\r\n                        [isMultiChoice]=\"false\"\r\n                        [floatLabel]=\"true\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                            styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                            type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt\r\n    [tableId]=\"'tableSimList'\"\r\n    [fieldId]=\"'msisdn'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"this.tranService.translate('global.menu.listsim')\"\r\n></table-vnpt>\r\n\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('global.button.pushGroupSim')\" [(visible)]=\"isShowDialogPushGroup\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"w-full field grid\">\r\n            <label htmlFor=\"groupSim\" class=\"col-fixed\" style=\"width:100px\">{{tranService.translate(\"sim.label.nhomsim\")}}</label>\r\n            <div class=\"col\" style=\"max-width: calc(100% - 140px);\">\r\n                <vnpt-select\r\n                    class=\"w-full\"\r\n                    [(value)]=\"groupSimSelected\"\r\n                    [placeholder]=\"tranService.translate('sim.text.selectGroupSim')\"\r\n                    objectKey=\"groupSim\"\r\n                    paramKey=\"name\"\r\n                    keyReturn=\"id\"\r\n                    displayPattern=\"${name} - ${groupKey}\"\r\n                    typeValue=\"primitive\"\r\n                    [isMultiChoice]=\"false\"\r\n                    [paramDefault]=\"paramSearchGroupSim\"\r\n                ></vnpt-select>\r\n            </div>\r\n            <div style=\"width:40px\">\r\n                <p-button *ngIf=\"checkAuthen([allPermissions.GROUP_SIM.CREATE])\"\r\n                    styleClass=\"p-button-info\" icon=\"pi pi-plus\"\r\n                    [pTooltip]=\"tranService.translate('global.button.add')\" (click)=\"isShowDialogCreateGroup = true;isShowDialogPushGroup = false;\"></p-button>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-items-center\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogPushGroup = false;groupSimSelected=null\"></p-button>\r\n            <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"pushGroupSim(0)\" [disabled]=\"groupSimSelected == null || groupSimSelected == undefined\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n<!-- chi tiết sim -->\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [header]=\"tranService.translate('sim.text.detailSim')\" [(visible)]=\"isShowModalDetailSim\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowModalDetailSim\">\r\n        <div class=\"grid grid-1 mt-1 h-auto\" style=\"width: calc(100% + 16px);\">\r\n            <div class=\"col sim-detail pr-0\">\r\n                <p-card [header]=\"tranService.translate('sim.text.simInfo')\" *ngIf=\"Object.keys(detailSim).length > 0\">\r\n                    <div class=\"flex flex-row justify-content-between custom-card\">\r\n                        <div class=\"w-6\">\r\n                            <div class=\"grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.sothuebao\")}}</span>\r\n                                <span class=\"col\">{{detailSim.msisdn}}</span>\r\n                            </div>\r\n                            <div class=\"sim-status-block mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.trangthaisim\")}}</span>\r\n                                <span class=\"w-auto ml-3\" [class]=\"getClassStatus(detailSim.status)\">{{getNameStatus(detailSim.status)}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.imsi\")}}</span>\r\n                                <span class=\"col\">{{detailSim.imsi}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.imeiDevice\")}}</span>\r\n                                <span class=\"col\">{{detailSim.imei}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.maapn\")}}</span>\r\n                                <span class=\"col\">{{detailSim.apnId}}</span>\r\n                            </div>\r\n                            <div class=\"sim-status-block mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.trangthaiketnoi\")}}</span>\r\n                                <span class=\" ml-3 p-2 border-round inline-block\" [ngClass]=\"getSimStatus(detailSim.connectionStatus) == 'ON'? 'text-green-800 bg-green-100' : 'text-50 surface-500'\" [pTooltip]=\"getTooltipStatus(detailSim.connectionStatus)\">{{getSimStatus(detailSim.connectionStatus)}}</span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-6\">\r\n                            <div class=\"grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.startDate\")}}</span>\r\n                                <span class=\"col\">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.serviceType\")}}</span>\r\n                                <span class=\"w-auto ml-3\">{{getServiceType(detailSim.serviceType)}}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.simInfo')\" *ngIf=\"Object.keys(detailSim).length === 0\">\r\n                    <div class=\"rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900\">\r\n                        <p-skeleton></p-skeleton>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.simStatusInfo')\" styleClass=\"mt-3 sim-status\" *ngIf=\"Object.keys(detailStatusSim).length > 0\">\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusData\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.data\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusReceiveCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callReceived\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusSendCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callSent\")}}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusWorldCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callWorld\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusReceiveSms\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.smsReceived\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusSendSms\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.smsSent\")}}</div>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.simStatusInfo')\" styleClass=\"mt-3\" *ngIf=\"Object.keys(detailStatusSim).length === 0\">\r\n                    <div class=\"rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900\">\r\n                        <p-skeleton></p-skeleton>\r\n                    </div>\r\n<!--                    <p-progressSpinner></p-progressSpinner>-->\r\n                </p-card>\r\n                <!-- goi cuoc -->\r\n                <p-card [header]=\"tranService.translate('sim.text.ratingPlanInfo')\" styleClass=\"mt-3\"  *ngIf=\"Object.keys(detailRatingPlan).length > 0\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.tengoicuoc\")}}</span>\r\n                        <span class=\"col\">{{detailSim.ratingPlanName}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.dataUseInMonth\")}}</span>\r\n                        <span class=\"col\">{{this.utilService.bytesToMegabytes(detailRatingPlan.dataUseInMonth) | number }} {{detailRatingPlan.unit?detailRatingPlan.unit:\"MB\"}}</span>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.ratingPlanInfo')\" styleClass=\"mt-3\" *ngIf=\"Object.keys(detailRatingPlan).length === 0\">\r\n                    <div class=\"rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900\">\r\n                        <p-skeleton></p-skeleton>\r\n                    </div>\r\n                </p-card>\r\n            </div>\r\n            <div class=\"col sim-detail pr-0\">\r\n                <!-- hop dong -->\r\n                <p-card [header]=\"tranService.translate('sim.text.contractInfo')\" *ngIf=\"Object.keys(detailContract).length > 0\">\r\n                    <div class=\"grid mt-0\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.mahopdong\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contractCode}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.ngaylamhopdong\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.nguoilamhopdong\")}}</span>\r\n                        <span class=\"col uppercase\">{{detailContract.contractorInfo}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.matrungtam\")}}</span>\r\n                        <span class=\"col\">{{detailContract.centerCode}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.dienthoailienhe\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contactPhone}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.diachilienhe\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contactAddress}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.paymentName\")}}</span>\r\n                        <span class=\"col uppercase\">{{detailContract.paymentName}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.paymentAddress\")}}</span>\r\n                        <span class=\"col\">{{detailContract.paymentAddress}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.routeCode\")}}</span>\r\n                        <span class=\"col\">{{detailContract.routeCode}}</span>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.contractInfo')\" *ngIf=\"Object.keys(detailContract).length === 0\">\r\n                    <div class=\"rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900\">\r\n                        <p-skeleton></p-skeleton>\r\n                    </div>\r\n                </p-card>\r\n                <!-- customer -->\r\n                <p-card [header]=\"tranService.translate('sim.text.customerInfo')\" styleClass=\"mt-3\" *ngIf=\"Object.keys(detailCustomer).length > 0\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.khachhang\")}}</span>\r\n                        <span class=\"col\">{{detailCustomer.name}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.customerCode\")}}</span>\r\n                        <span class=\"col\">{{detailCustomer.code}}</span>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.customerInfo')\" styleClass=\"mt-3\" *ngIf=\"Object.keys(detailCustomer).length === 0\">\r\n                    <div class=\"rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900\">\r\n                        <p-skeleton></p-skeleton>\r\n                    </div>\r\n                </p-card>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n<div class=\"flex justify-content-center dialog-create-group\">\r\n    <p-dialog [header]=\"tranService.translate('global.text.createGroupSimAndPushSimToGroup')\" [(visible)]=\"isShowDialogCreateGroup\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form [formGroup]=\"formCreateGroupSim\" (ngSubmit)=\"pushGroupSim(1)\">\r\n            <!-- group scope -->\r\n            <div class=\"w-full field grid\">\r\n                <label htmlFor=\"customer\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"groupSim.label.groupScope\")}}</label>\r\n                <div class=\"col\">\r\n                    <span *ngIf=\"groupScope == groupScopeObjects.GROUP_ADMIN\">{{tranService.translate(\"groupSim.scope.admin\")}}</span>\r\n                    <span *ngIf=\"groupScope == groupScopeObjects.GROUP_PROVINCE\">{{tranService.translate(\"groupSim.scope.province\")}}</span>\r\n                    <span *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\">{{tranService.translate(\"groupSim.scope.customer\")}}</span>\r\n                </div>\r\n            </div>\r\n            <!-- group key -->\r\n            <div class=\"w-full field grid\">\r\n                <label htmlFor=\"groupKey\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"sim.label.groupKey\")}}<span class=\"text-red-500\">*</span></label>\r\n                <div class=\"col\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"groupKey\"\r\n                            [(ngModel)]=\"dataCreateGroupSim.groupKey\"\r\n                            formControlName=\"groupKey\"\r\n                            [required]=\"true\"\r\n                            [maxLength]=\"16\"\r\n                            pattern=\"[a-zA-Z0-9\\-_]*\"\r\n                            [placeholder]=\"tranService.translate('sim.text.inputGroupKey')\"\r\n                            (keyup)=\"checkExistGroupKey()\"\r\n                    />\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full field grid text-error-field\">\r\n                <label htmlFor=\"groupName\" class=\"col-fixed\" style=\"width:140px\"></label>\r\n                <div class=\"col\">\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.groupKey.dirty && formCreateGroupSim.controls.groupKey.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.groupKey.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:16})}}</small>\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.groupKey.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                    <small class=\"text-red-500\" *ngIf=\"!formCreateGroupSim.controls.groupKey.errors?.required && isExistsGroupKey\">{{tranService.translate(\"global.message.exists\",{type:tranService.translate(\"sim.label.groupKey\").toLowerCase()})}}</small>\r\n                </div>\r\n            </div>\r\n            <!-- group name -->\r\n            <div class=\"w-full field grid\">\r\n                <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"sim.label.groupName\")}}<span class=\"text-red-500\">*</span></label>\r\n                <div class=\"col\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"name\"\r\n                            [(ngModel)]=\"dataCreateGroupSim.name\"\r\n                            formControlName=\"name\"\r\n                            [required]=\"true\"\r\n                            [maxLength]=\"255\"\r\n                            pattern=\"[^~`!@#\\$%^&*()=\\+\\[\\]\\{\\}\\|\\\\,<>/?]*\"\r\n                            [placeholder]=\"tranService.translate('sim.text.inputGroupName')\"\r\n                    />\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full field grid text-error-field\">\r\n                <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:140px\"></label>\r\n                <div class=\"col\">\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.name.dirty && formCreateGroupSim.controls.name.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.name.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.name.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                </div>\r\n            </div>\r\n            <!-- customer -->\r\n            <div class=\"w-full field grid\" *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\">\r\n                <label htmlFor=\"customer\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"sim.label.khachhang\")}}</label>\r\n                <div class=\"col\">\r\n                    {{customerName}} - {{customerCode}}\r\n                </div>\r\n            </div>\r\n            <!-- province -->\r\n            <div class=\"w-full field grid\" *ngIf=\"groupScope == groupScopeObjects.GROUP_PROVINCE\">\r\n                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                <div class=\"col\">\r\n                    {{provinceName}} ({{provinceCode}})\r\n                </div>\r\n            </div>\r\n            <!-- description -->\r\n            <div class=\"w-full field grid\">\r\n                <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"sim.label.description\")}}</label>\r\n                <div class=\"col\">\r\n                    <textarea  class=\"w-full\" style=\"resize: none;\"\r\n                            rows=\"3\"\r\n                            [autoResize]=\"false\"\r\n                            pInputTextarea id=\"description\"\r\n                            [(ngModel)]=\"dataCreateGroupSim.description\"\r\n                            formControlName=\"description\"\r\n                            [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                            [maxLength]=\"255\"\r\n                    ></textarea>\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full field grid text-error-field\">\r\n                <label htmlFor=\"groupName\" class=\"col-fixed\" style=\"width:140px\"></label>\r\n                <div class=\"col\">\r\n                    <small class=\"text-red-500\" *ngIf=\"formCreateGroupSim.controls.description.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogCreateGroup = false\"></p-button>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" type=\"submit\" [disabled]=\"formCreateGroupSim.invalid || isExistsGroupKey\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n<!--    dialog xóa sim-->\r\n    <div class=\"flex justify-content-center\">\r\n        <p-dialog [header]=\"tranService.translate('sim.label.deleteSim')\" [(visible)]=\"isShowModalDeleteSim\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n            <div class=\"flex flex-row justify-content-center align-items-center\">\r\n                <p>{{tranService.translate(\"sim.text.deleteSim\")}}</p>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowModalDeleteSim = false\"></p-button>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.agree')\" (click)=\"deleteSim()\" ></p-button>\r\n            </div>\r\n        </p-dialog>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAGA,SAASA,UAAU,QAAQ,gCAAgC;AAE3D,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICe1CC,EAAA,CAAAC,cAAA,cAA6D;IAC6DD,EAAA,CAAAE,UAAA,2BAAAC,iFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAiBP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC,2BAAAC,iFAAAC,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAAZ,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAI,OAAA,CAAAC,gBAAA,GAAAF,MAAA;IAAA;IAArJX,EAAA,CAAAc,YAAA,EAA2N;IAC3Nd,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAAE,UAAA,mBAAAa,4EAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAhB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAQ,OAAA,CAAAP,WAAA,EAAa;IAAA,EAAC;IAChCT,EAAA,CAAAc,YAAA,EAAW;;;;IAL2Cd,EAAA,CAAAiB,SAAA,GAA8D;IAA9DjB,EAAA,CAAAkB,UAAA,gBAAAC,OAAA,CAAAC,WAAA,CAAAC,SAAA,0BAA8D,YAAAF,OAAA,CAAAN,gBAAA,oBAAAb,EAAA,CAAAsB,eAAA,IAAAC,GAAA;;;;;IAQzHvB,EAAA,CAAAC,cAAA,cAA2D;IAAAD,EAAA,CAAAwB,MAAA,GAAsD;IAAAxB,EAAA,CAAAc,YAAA,EAAM;;;;IAA5Dd,EAAA,CAAAiB,SAAA,GAAsD;IAAtDjB,EAAA,CAAAyB,iBAAA,CAAAC,OAAA,CAAAN,WAAA,CAAAC,SAAA,8BAAsD;;;;;IATjHrB,EAAA,CAAA2B,UAAA,IAAAC,iDAAA,kBAOM;IAEN5B,EAAA,CAAA2B,UAAA,IAAAE,iDAAA,kBAAuH;;;;IATjH7B,EAAA,CAAAkB,UAAA,SAAAY,MAAA,CAAAC,kBAAA,CAAwB;IASxB/B,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAkB,UAAA,UAAAY,MAAA,CAAAC,kBAAA,CAAyB;;;;;;IAoQ3B/B,EAAA,CAAAC,cAAA,mBAEoI;IAAxED,EAAA,CAAAE,UAAA,mBAAA8B,mEAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAO,aAAA;MAAA2B,OAAA,CAAAC,uBAAA,GAAmC,IAAI;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAA0B,OAAA,CAAAE,qBAAA,GAAyB,KAAK;IAAA,EAAE;IAACpC,EAAA,CAAAc,YAAA,EAAW;;;;IAA3Id,EAAA,CAAAkB,UAAA,aAAAmB,MAAA,CAAAjB,WAAA,CAAAC,SAAA,sBAAuD;;;;;IAe3DrB,EAAA,CAAAC,cAAA,iBAAuG;IAIPD,EAAA,CAAAwB,MAAA,GAAgD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACvId,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,GAAoB;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAEjDd,EAAA,CAAAC,cAAA,cAAwC;IAC4CD,EAAA,CAAAwB,MAAA,IAAmD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC1Id,EAAA,CAAAC,cAAA,gBAAqE;IAAAD,EAAA,CAAAwB,MAAA,IAAmC;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAEnHd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAA2C;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAClId,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAkB;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE/Cd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAiD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACxId,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAkB;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE/Cd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAA4C;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACnId,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAmB;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAEhDd,EAAA,CAAAC,cAAA,eAAwC;IAC4CD,EAAA,CAAAwB,MAAA,IAAsD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC7Id,EAAA,CAAAC,cAAA,gBAAgO;IAAAD,EAAA,CAAAwB,MAAA,IAA4C;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAG3Rd,EAAA,CAAAC,cAAA,eAAiB;IAEuED,EAAA,CAAAwB,MAAA,IAAgD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACvId,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAA2C;;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAExEd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAkD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACzId,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAwB,MAAA,IAAyC;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAnClFd,EAAA,CAAAkB,UAAA,WAAAoB,OAAA,CAAAlB,WAAA,CAAAC,SAAA,qBAAoD;IAIoCrB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,wBAAgD;IAC9GrB,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAC,SAAA,CAAAC,MAAA,CAAoB;IAG0CxC,EAAA,CAAAiB,SAAA,GAAmD;IAAnDjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,2BAAmD;IACzGrB,EAAA,CAAAiB,SAAA,GAA0C;IAA1CjB,EAAA,CAAAyC,UAAA,CAAAH,OAAA,CAAAI,cAAA,CAAAJ,OAAA,CAAAC,SAAA,CAAAI,MAAA,EAA0C;IAAC3C,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAM,aAAA,CAAAN,OAAA,CAAAC,SAAA,CAAAI,MAAA,EAAmC;IAGxB3C,EAAA,CAAAiB,SAAA,GAA2C;IAA3CjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,mBAA2C;IACzGrB,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAC,SAAA,CAAAM,IAAA,CAAkB;IAG4C7C,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,yBAAiD;IAC/GrB,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAC,SAAA,CAAAO,IAAA,CAAkB;IAG4C9C,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,oBAA4C;IAC1GrB,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAC,SAAA,CAAAQ,KAAA,CAAmB;IAG2C/C,EAAA,CAAAiB,SAAA,GAAsD;IAAtDjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,8BAAsD;IACpFrB,EAAA,CAAAiB,SAAA,GAAmH;IAAnHjB,EAAA,CAAAkB,UAAA,YAAAoB,OAAA,CAAAU,YAAA,CAAAV,OAAA,CAAAC,SAAA,CAAAU,gBAAA,kEAAmH,aAAAX,OAAA,CAAAY,gBAAA,CAAAZ,OAAA,CAAAC,SAAA,CAAAU,gBAAA;IAA2DjD,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAU,YAAA,CAAAV,OAAA,CAAAC,SAAA,CAAAU,gBAAA,EAA4C;IAK5LjD,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,wBAAgD;IAC9GrB,EAAA,CAAAiB,SAAA,GAA2C;IAA3CjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAmD,WAAA,SAAAb,OAAA,CAAAC,SAAA,CAAAa,SAAA,gBAA2C;IAGmBpD,EAAA,CAAAiB,SAAA,GAAkD;IAAlDjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAlB,WAAA,CAAAC,SAAA,0BAAkD;IACxGrB,EAAA,CAAAiB,SAAA,GAAyC;IAAzCjB,EAAA,CAAAyB,iBAAA,CAAAa,OAAA,CAAAe,cAAA,CAAAf,OAAA,CAAAC,SAAA,CAAAe,WAAA,EAAyC;;;;;IAKnFtD,EAAA,CAAAC,cAAA,iBAAyG;IAEjGD,EAAA,CAAAuD,SAAA,iBAAyB;IAC7BvD,EAAA,CAAAc,YAAA,EAAM;;;;IAHFd,EAAA,CAAAkB,UAAA,WAAAsC,OAAA,CAAApC,WAAA,CAAAC,SAAA,qBAAoD;;;;;;IAK5DrB,EAAA,CAAAC,cAAA,iBAAgJ;IAGpHD,EAAA,CAAAE,UAAA,2BAAAuD,0FAAA9C,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAmD,OAAA,CAAAC,eAAA,CAAAC,UAAA,GAAAlD,MAAA,CAAkC;IAAA,EAAP;IAA+CX,EAAA,CAAAc,YAAA,EAAiB;IACxHd,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAwB,MAAA,GAAoD;IAAAxB,EAAA,CAAAc,YAAA,EAAM;IAEnEd,EAAA,CAAAC,cAAA,cAA+B;IACXD,EAAA,CAAAE,UAAA,2BAAA4D,0FAAAnD,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAK,OAAA,GAAA/D,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAuD,OAAA,CAAAH,eAAA,CAAAI,iBAAA,GAAArD,MAAA,CAAyC;IAAA,EAAP;IAA+CX,EAAA,CAAAc,YAAA,EAAiB;IAC/Hd,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAwB,MAAA,GAA4D;IAAAxB,EAAA,CAAAc,YAAA,EAAM;IAE3Ed,EAAA,CAAAC,cAAA,eAA+B;IACXD,EAAA,CAAAE,UAAA,2BAAA+D,2FAAAtD,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAQ,OAAA,GAAAlE,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAN,eAAA,CAAAO,cAAA,GAAAxD,MAAA,CAAsC;IAAA,EAAP;IAA+CX,EAAA,CAAAc,YAAA,EAAiB;IAC5Hd,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAwB,MAAA,IAAwD;IAAAxB,EAAA,CAAAc,YAAA,EAAM;IAG3Ed,EAAA,CAAAC,cAAA,eAAkB;IAEMD,EAAA,CAAAE,UAAA,2BAAAkE,2FAAAzD,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAW,OAAA,GAAArE,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA6D,OAAA,CAAAT,eAAA,CAAAU,eAAA,GAAA3D,MAAA,CAAuC;IAAA,EAAP;IAA+CX,EAAA,CAAAc,YAAA,EAAiB;IAC7Hd,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAwB,MAAA,IAAyD;IAAAxB,EAAA,CAAAc,YAAA,EAAM;IAExEd,EAAA,CAAAC,cAAA,eAA+B;IACXD,EAAA,CAAAE,UAAA,2BAAAqE,2FAAA5D,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAc,OAAA,GAAAxE,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAgE,OAAA,CAAAZ,eAAA,CAAAa,gBAAA,GAAA9D,MAAA,CAAwC;IAAA,EAAP;IAA+CX,EAAA,CAAAc,YAAA,EAAiB;IAC9Hd,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAwB,MAAA,IAA2D;IAAAxB,EAAA,CAAAc,YAAA,EAAM;IAE1Ed,EAAA,CAAAC,cAAA,eAA+B;IACXD,EAAA,CAAAE,UAAA,2BAAAwE,2FAAA/D,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAiB,OAAA,GAAA3E,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAmE,OAAA,CAAAf,eAAA,CAAAgB,aAAA,GAAAjE,MAAA,CAAqC;IAAA,EAAP;IAA+CX,EAAA,CAAAc,YAAA,EAAiB;IAC3Hd,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAwB,MAAA,IAAuD;IAAAxB,EAAA,CAAAc,YAAA,EAAM;;;;IA1BtEd,EAAA,CAAAkB,UAAA,WAAA2D,OAAA,CAAAzD,WAAA,CAAAC,SAAA,2BAA0D;IAGtCrB,EAAA,CAAAiB,SAAA,GAAwC;IAAxCjB,EAAA,CAAAkB,UAAA,YAAA2D,OAAA,CAAAjB,eAAA,CAAAC,UAAA,CAAwC;IACnD7D,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAzD,WAAA,CAAAC,SAAA,4BAAoD;IAGzCrB,EAAA,CAAAiB,SAAA,GAA+C;IAA/CjB,EAAA,CAAAkB,UAAA,YAAA2D,OAAA,CAAAjB,eAAA,CAAAI,iBAAA,CAA+C;IAC1DhE,EAAA,CAAAiB,SAAA,GAA4D;IAA5DjB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAzD,WAAA,CAAAC,SAAA,oCAA4D;IAGjDrB,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAkB,UAAA,YAAA2D,OAAA,CAAAjB,eAAA,CAAAO,cAAA,CAA4C;IACvDnE,EAAA,CAAAiB,SAAA,GAAwD;IAAxDjB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAzD,WAAA,CAAAC,SAAA,gCAAwD;IAK7CrB,EAAA,CAAAiB,SAAA,GAA6C;IAA7CjB,EAAA,CAAAkB,UAAA,YAAA2D,OAAA,CAAAjB,eAAA,CAAAU,eAAA,CAA6C;IACxDtE,EAAA,CAAAiB,SAAA,GAAyD;IAAzDjB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAzD,WAAA,CAAAC,SAAA,iCAAyD;IAG9CrB,EAAA,CAAAiB,SAAA,GAA8C;IAA9CjB,EAAA,CAAAkB,UAAA,YAAA2D,OAAA,CAAAjB,eAAA,CAAAa,gBAAA,CAA8C;IACzDzE,EAAA,CAAAiB,SAAA,GAA2D;IAA3DjB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAzD,WAAA,CAAAC,SAAA,mCAA2D;IAGhDrB,EAAA,CAAAiB,SAAA,GAA2C;IAA3CjB,EAAA,CAAAkB,UAAA,YAAA2D,OAAA,CAAAjB,eAAA,CAAAgB,aAAA,CAA2C;IACtD5E,EAAA,CAAAiB,SAAA,GAAuD;IAAvDjB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAzD,WAAA,CAAAC,SAAA,+BAAuD;;;;;IAIxErB,EAAA,CAAAC,cAAA,iBAAuI;IAE/HD,EAAA,CAAAuD,SAAA,iBAAyB;IAC7BvD,EAAA,CAAAc,YAAA,EAAM;;;;IAHFd,EAAA,CAAAkB,UAAA,WAAA4D,OAAA,CAAA1D,WAAA,CAAAC,SAAA,2BAA0D;;;;;IAOlErB,EAAA,CAAAC,cAAA,iBAAwI;IAEhDD,EAAA,CAAAwB,MAAA,GAAiD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACxId,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,GAA4B;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAEzDd,EAAA,CAAAC,cAAA,cAAuB;IAC6DD,EAAA,CAAAwB,MAAA,GAAqD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC5Id,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAqI;;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAP9Jd,EAAA,CAAAkB,UAAA,WAAA6D,OAAA,CAAA3D,WAAA,CAAAC,SAAA,4BAA2D;IAEqBrB,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAyB,iBAAA,CAAAsD,OAAA,CAAA3D,WAAA,CAAAC,SAAA,yBAAiD;IAC/GrB,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAyB,iBAAA,CAAAsD,OAAA,CAAAxC,SAAA,CAAAyC,cAAA,CAA4B;IAGkChF,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAyB,iBAAA,CAAAsD,OAAA,CAAA3D,WAAA,CAAAC,SAAA,6BAAqD;IACnHrB,EAAA,CAAAiB,SAAA,GAAqI;IAArIjB,EAAA,CAAAiF,kBAAA,KAAAjF,EAAA,CAAAkF,WAAA,QAAAH,OAAA,CAAAI,WAAA,CAAAC,gBAAA,CAAAL,OAAA,CAAAM,gBAAA,CAAAC,cAAA,SAAAP,OAAA,CAAAM,gBAAA,CAAAE,IAAA,GAAAR,OAAA,CAAAM,gBAAA,CAAAE,IAAA,YAAqI;;;;;IAG/JvF,EAAA,CAAAC,cAAA,iBAAyI;IAEjID,EAAA,CAAAuD,SAAA,iBAAyB;IAC7BvD,EAAA,CAAAc,YAAA,EAAM;;;;IAHFd,EAAA,CAAAkB,UAAA,WAAAsE,OAAA,CAAApE,WAAA,CAAAC,SAAA,4BAA2D;;;;;IAQnErB,EAAA,CAAAC,cAAA,iBAAiH;IAEzBD,EAAA,CAAAwB,MAAA,GAAgD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACvId,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,GAA+B;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE5Dd,EAAA,CAAAC,cAAA,cAAuB;IAC6DD,EAAA,CAAAwB,MAAA,GAAqD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC5Id,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAmD;;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAEhFd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAsD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC7Id,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAwB,MAAA,IAAiC;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAExEd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAiD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACxId,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAA6B;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE1Dd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAsD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC7Id,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAA+B;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE5Dd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAmD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC1Id,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAiC;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE9Dd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAkD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACzId,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAwB,MAAA,IAA8B;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAErEd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAqD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC5Id,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAiC;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAE9Dd,EAAA,CAAAC,cAAA,eAAuB;IAC6DD,EAAA,CAAAwB,MAAA,IAAgD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACvId,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAA4B;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAnCrDd,EAAA,CAAAkB,UAAA,WAAAuE,OAAA,CAAArE,WAAA,CAAAC,SAAA,0BAAyD;IAEuBrB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,wBAAgD;IAC9GrB,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAC,YAAA,CAA+B;IAG+B3F,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,6BAAqD;IACnHrB,EAAA,CAAAiB,SAAA,GAAmD;IAAnDjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAmD,WAAA,SAAAsC,OAAA,CAAAC,cAAA,CAAAE,YAAA,gBAAmD;IAGW5F,EAAA,CAAAiB,SAAA,GAAsD;IAAtDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,8BAAsD;IAC1GrB,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAG,cAAA,CAAiC;IAGmB7F,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,yBAAiD;IAC/GrB,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAI,UAAA,CAA6B;IAGiC9F,EAAA,CAAAiB,SAAA,GAAsD;IAAtDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,8BAAsD;IACpHrB,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAK,YAAA,CAA+B;IAG+B/F,EAAA,CAAAiB,SAAA,GAAmD;IAAnDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,2BAAmD;IACjHrB,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAM,cAAA,CAAiC;IAG6BhG,EAAA,CAAAiB,SAAA,GAAkD;IAAlDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,0BAAkD;IACtGrB,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAO,WAAA,CAA8B;IAGsBjG,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,6BAAqD;IACnHrB,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAQ,cAAA,CAAiC;IAG6BlG,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAArE,WAAA,CAAAC,SAAA,wBAAgD;IAC9GrB,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAyB,iBAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAS,SAAA,CAA4B;;;;;IAGtDnG,EAAA,CAAAC,cAAA,iBAAmH;IAE3GD,EAAA,CAAAuD,SAAA,iBAAyB;IAC7BvD,EAAA,CAAAc,YAAA,EAAM;;;;IAHFd,EAAA,CAAAkB,UAAA,WAAAkF,OAAA,CAAAhF,WAAA,CAAAC,SAAA,0BAAyD;;;;;IAMjErB,EAAA,CAAAC,cAAA,iBAAmI;IAE3CD,EAAA,CAAAwB,MAAA,GAAgD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IACvId,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,GAAuB;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAEpDd,EAAA,CAAAC,cAAA,cAAuB;IAC6DD,EAAA,CAAAwB,MAAA,GAAmD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;IAC1Id,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAwB,MAAA,IAAuB;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAPhDd,EAAA,CAAAkB,UAAA,WAAAmF,OAAA,CAAAjF,WAAA,CAAAC,SAAA,0BAAyD;IAEuBrB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAA4E,OAAA,CAAAjF,WAAA,CAAAC,SAAA,wBAAgD;IAC9GrB,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAyB,iBAAA,CAAA4E,OAAA,CAAAC,cAAA,CAAAC,IAAA,CAAuB;IAGuCvG,EAAA,CAAAiB,SAAA,GAAmD;IAAnDjB,EAAA,CAAAyB,iBAAA,CAAA4E,OAAA,CAAAjF,WAAA,CAAAC,SAAA,2BAAmD;IACjHrB,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAyB,iBAAA,CAAA4E,OAAA,CAAAC,cAAA,CAAAE,IAAA,CAAuB;;;;;IAGjDxG,EAAA,CAAAC,cAAA,iBAAqI;IAE7HD,EAAA,CAAAuD,SAAA,iBAAyB;IAC7BvD,EAAA,CAAAc,YAAA,EAAM;;;;IAHFd,EAAA,CAAAkB,UAAA,WAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,0BAAyD;;;;;;;;;;;IA7J7ErB,EAAA,CAAAC,cAAA,mBAAqN;IAApJD,EAAA,CAAAE,UAAA,2BAAAwG,2EAAA/F,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAuG,IAAA;MAAA,MAAAC,OAAA,GAAA5G,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAoG,OAAA,CAAAC,oBAAA,GAAAlG,MAAA;IAAA,EAAkC;IAC/FX,EAAA,CAAAC,cAAA,cAAuE;IAE/DD,EAAA,CAAA2B,UAAA,IAAAmF,iDAAA,uBAuCS;IACT9G,EAAA,CAAA2B,UAAA,IAAAoF,iDAAA,qBAIS;IACT/G,EAAA,CAAA2B,UAAA,IAAAqF,iDAAA,uBA6BS;IACThH,EAAA,CAAA2B,UAAA,IAAAsF,iDAAA,qBAKS;IAETjH,EAAA,CAAA2B,UAAA,IAAAuF,iDAAA,sBASS;IACTlH,EAAA,CAAA2B,UAAA,IAAAwF,iDAAA,qBAIS;IACbnH,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,cAAiC;IAE7BD,EAAA,CAAA2B,UAAA,KAAAyF,kDAAA,uBAqCS;IACTpH,EAAA,CAAA2B,UAAA,KAAA0F,kDAAA,qBAIS;IAETrH,EAAA,CAAA2B,UAAA,KAAA2F,kDAAA,sBASS;IACTtH,EAAA,CAAA2B,UAAA,KAAA4F,kDAAA,qBAIS;IACbvH,EAAA,CAAAc,YAAA,EAAM;;;;IAlKqGd,EAAA,CAAAwH,UAAA,CAAAxH,EAAA,CAAAsB,eAAA,KAAAmG,GAAA,EAA4B;IAArIzH,EAAA,CAAAkB,UAAA,WAAAwG,MAAA,CAAAtG,WAAA,CAAAC,SAAA,uBAAsD,YAAAqG,MAAA,CAAAb,oBAAA;IAGU7G,EAAA,CAAAiB,SAAA,GAAuC;IAAvCjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAAnF,SAAA,EAAAsF,MAAA,KAAuC;IAwCvC7H,EAAA,CAAAiB,SAAA,GAAyC;IAAzCjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAAnF,SAAA,EAAAsF,MAAA,OAAyC;IAKN7H,EAAA,CAAAiB,SAAA,GAA6C;IAA7CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAA9D,eAAA,EAAAiE,MAAA,KAA6C;IA8BxD7H,EAAA,CAAAiB,SAAA,GAA+C;IAA/CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAA9D,eAAA,EAAAiE,MAAA,OAA+C;IAO7C7H,EAAA,CAAAiB,SAAA,GAA8C;IAA9CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAArC,gBAAA,EAAAwC,MAAA,KAA8C;IAU/C7H,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAArC,gBAAA,EAAAwC,MAAA,OAAgD;IAQpE7H,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAAhC,cAAA,EAAAmC,MAAA,KAA4C;IAsC5C7H,EAAA,CAAAiB,SAAA,GAA8C;IAA9CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAAhC,cAAA,EAAAmC,MAAA,OAA8C;IAM5B7H,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAApB,cAAA,EAAAuB,MAAA,KAA4C;IAU5C7H,EAAA,CAAAiB,SAAA,GAA8C;IAA9CjB,EAAA,CAAAkB,UAAA,SAAAwG,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,CAAApB,cAAA,EAAAuB,MAAA,OAA8C;;;;;IAiB/H7H,EAAA,CAAAC,cAAA,WAA0D;IAAAD,EAAA,CAAAwB,MAAA,GAAiD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAAxDd,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAyB,iBAAA,CAAAqG,MAAA,CAAA1G,WAAA,CAAAC,SAAA,yBAAiD;;;;;IAC3GrB,EAAA,CAAAC,cAAA,WAA6D;IAAAD,EAAA,CAAAwB,MAAA,GAAoD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAA3Dd,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAyB,iBAAA,CAAAsG,MAAA,CAAA3G,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACjHrB,EAAA,CAAAC,cAAA,WAA6D;IAAAD,EAAA,CAAAwB,MAAA,GAAoD;IAAAxB,EAAA,CAAAc,YAAA,EAAO;;;;IAA3Dd,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAyB,iBAAA,CAAAuG,MAAA,CAAA5G,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAsBjHrB,EAAA,CAAAC,cAAA,gBAAwI;IAAAD,EAAA,CAAAwB,MAAA,GAAoD;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAA5Dd,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAyB,iBAAA,CAAAwG,MAAA,CAAA7G,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IAC5LrB,EAAA,CAAAC,cAAA,gBAA2F;IAAAD,EAAA,CAAAwB,MAAA,GAA8D;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAAtEd,EAAA,CAAAiB,SAAA,GAA8D;IAA9DjB,EAAA,CAAAyB,iBAAA,CAAAyG,MAAA,CAAA9G,WAAA,CAAAC,SAAA,6BAAArB,EAAA,CAAAsB,eAAA,IAAA6G,GAAA,GAA8D;;;;;IACzJnI,EAAA,CAAAC,cAAA,gBAAyF;IAAAD,EAAA,CAAAwB,MAAA,GAAsD;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAA9Dd,EAAA,CAAAiB,SAAA,GAAsD;IAAtDjB,EAAA,CAAAyB,iBAAA,CAAA2G,MAAA,CAAAhH,WAAA,CAAAC,SAAA,8BAAsD;;;;;;;;;;IAC/IrB,EAAA,CAAAC,cAAA,gBAA+G;IAAAD,EAAA,CAAAwB,MAAA,GAAmH;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAA3Hd,EAAA,CAAAiB,SAAA,GAAmH;IAAnHjB,EAAA,CAAAyB,iBAAA,CAAA4G,OAAA,CAAAjH,WAAA,CAAAC,SAAA,0BAAArB,EAAA,CAAAsI,eAAA,IAAAC,GAAA,EAAAF,OAAA,CAAAjH,WAAA,CAAAC,SAAA,uBAAAmH,WAAA,KAAmH;;;;;IAqBlOxI,EAAA,CAAAC,cAAA,gBAAgI;IAAAD,EAAA,CAAAwB,MAAA,GAAoD;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAA5Dd,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAyB,iBAAA,CAAAgH,OAAA,CAAArH,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACpLrB,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAwB,MAAA,GAA+D;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAAvEd,EAAA,CAAAiB,SAAA,GAA+D;IAA/DjB,EAAA,CAAAyB,iBAAA,CAAAiH,OAAA,CAAAtH,WAAA,CAAAC,SAAA,6BAAArB,EAAA,CAAAsB,eAAA,IAAAqH,GAAA,GAA+D;;;;;IACtJ3I,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAwB,MAAA,GAA2D;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAAnEd,EAAA,CAAAiB,SAAA,GAA2D;IAA3DjB,EAAA,CAAAyB,iBAAA,CAAAmH,OAAA,CAAAxH,WAAA,CAAAC,SAAA,mCAA2D;;;;;IAIxJrB,EAAA,CAAAC,cAAA,cAAsF;IAClBD,EAAA,CAAAwB,MAAA,GAAgD;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;IACxHd,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAc,YAAA,EAAM;;;;IAH0Dd,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAAoH,OAAA,CAAAzH,WAAA,CAAAC,SAAA,wBAAgD;IAE5GrB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAiF,kBAAA,MAAA4D,OAAA,CAAAC,YAAA,SAAAD,OAAA,CAAAE,YAAA,MACJ;;;;;IAGJ/I,EAAA,CAAAC,cAAA,cAAsF;IAClBD,EAAA,CAAAwB,MAAA,GAAmD;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;IAC3Hd,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAc,YAAA,EAAM;;;;IAH0Dd,EAAA,CAAAiB,SAAA,GAAmD;IAAnDjB,EAAA,CAAAyB,iBAAA,CAAAuH,OAAA,CAAA5H,WAAA,CAAAC,SAAA,2BAAmD;IAE/GrB,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAiF,kBAAA,MAAA+D,OAAA,CAAAC,YAAA,QAAAD,OAAA,CAAAE,YAAA,OACJ;;;;;IAoBIlJ,EAAA,CAAAC,cAAA,gBAA8F;IAAAD,EAAA,CAAAwB,MAAA,GAA+D;IAAAxB,EAAA,CAAAc,YAAA,EAAQ;;;;IAAvEd,EAAA,CAAAiB,SAAA,GAA+D;IAA/DjB,EAAA,CAAAyB,iBAAA,CAAA0H,OAAA,CAAA/H,WAAA,CAAAC,SAAA,6BAAArB,EAAA,CAAAsB,eAAA,IAAAqH,GAAA,GAA+D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADtiBjL,OAAM,MAAOS,mBAAoB,SAAQrJ,aAAa;EA8ElD;EACAsJ,YAAwCC,UAAsB,EAC1CC,WAAwB,EACCC,eAAgC,EAChCC,eAAgC,EAC9BC,iBAAoC,EAC/DC,eAAgC,EAChCC,QAAkB;IAClC,KAAK,CAACA,QAAQ,CAAC;IAPqB,KAAAN,UAAU,GAAVA,UAAU;IAC9B,KAAAC,WAAW,GAAXA,WAAW;IACc,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAC5C,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,QAAQ,GAARA,QAAQ;IArC5B,KAAAC,WAAW,GAA4B,IAAIC,IAAI,EAAE;IACjD,KAAAC,SAAS,GAA4B,IAAI;IACzC,KAAAC,SAAS,GAA4B,IAAIF,IAAI,EAAE;IAC/C,KAAA3H,uBAAuB,GAAW,KAAK;IACvC,KAAAC,qBAAqB,GAAW,KAAK;IAQrC,KAAA6H,iBAAiB,GAAQtK,SAAS,CAACuK,WAAW;IAC9C,KAAAC,cAAc,GAAGxK,SAAS,CAACyK,WAAW;IACtC,KAAAC,mBAAmB,GAAQ,EAAE;IAC7B,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,oBAAoB,GAAa,KAAK;IACtC,KAAA1D,oBAAoB,GAAa,KAAK;IAGtC,KAAAtE,SAAS,GAAO,EAAE;IAClB,KAAAqB,eAAe,GAAM,EAAE;IACvB,KAAA0C,cAAc,GAAK,EAAE;IACrB,KAAAjB,gBAAgB,GAAM,EAAE;IACxB,KAAAK,cAAc,GAAM,EAAE;IACtB,KAAA8E,SAAS,GAAM,EAAE;IACjB,KAAAzI,kBAAkB,GAAG,IAAI;IACzB,KAAAlB,gBAAgB,GAAG,IAAI;IACvB,KAAA4J,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,yBAAyB,GAAG,KAAK;IAwhCd,KAAA/C,MAAM,GAAGA,MAAM;EA9gClC;EACAgD,UAAUA,CAACC,KAAU;IACjBC,UAAU,CAAC,MAAK;MACZ,MAAMC,OAAO,GAAGF,KAAK,CAACG,aAAa,CAACC,MAAM,CAACC,OAAO,CAAC,2BAA2B,CAAC;MAC/E,IAAIH,OAAO,EAAE;QACTA,OAAO,CAACI,YAAY,CAAC,UAAU,EAAE,cAAc,CAAC;QAChDJ,OAAO,CAACI,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC;;IAEtD,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACC,IAAI;IACjD,IAAI,CAACC,cAAc,GAAG9L,SAAS,CAAC+L,SAAS;IACzC,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACpJ,SAAS,GAAG,EAAE;IACnB,IAAI,CAACqJ,kBAAkB,GAAG;MACtBpJ,MAAM,EAAE,IAAI;MACZK,IAAI,EAAE,IAAI;MACVgJ,YAAY,EAAE,IAAI;MAClBlG,YAAY,EAAE,IAAI;MAClBmG,UAAU,EAAE,IAAI;MAChBnJ,MAAM,EAAE,IAAI;MACZoJ,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZnJ,KAAK,EAAE;KACV;IACD,IAAI,CAACoJ,UAAU,GAAG;MACd3J,MAAM,EAAE,IAAI;MACZK,IAAI,EAAE,IAAI;MACVgJ,YAAY,EAAE,IAAI;MAClBlG,YAAY,EAAE,IAAI;MAClBmG,UAAU,EAAE,IAAI;MAChBnJ,MAAM,EAAE,IAAI;MACZoJ,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZnJ,KAAK,EAAE,IAAI;MACXqJ,OAAO,EAAE,IAAI;MACblD,YAAY,EAAG,IAAI;MACnBmD,MAAM,EAAG,IAAI;MACbC,QAAQ,EAAG;KACd;IACD,IAAI,CAACC,kBAAkB,GAAG;MACtBC,QAAQ,EAAE,IAAI;MACdjG,IAAI,EAAE,IAAI;MACVkG,WAAW,EAAE;KAChB;IACD,IAAI,CAAC5L,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC6L,aAAa,GAAG,IAAI,CAACnD,WAAW,CAACoD,KAAK,CAAC,IAAI,CAACR,UAAU,CAAC;IAC5D,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACrD,WAAW,CAACoD,KAAK,CAAC,IAAI,CAACJ,kBAAkB,CAAC;IACzE,IAAI,CAACM,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,qBAAqB;IAAC,CAAE,EAAE;MAAEyL,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,qBAAqB;IAAC,CAAE,CAAE;IAC1I,IAAI,CAAC0L,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAElC;MACID,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;MACnE2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACO,WAAW,CAAC9D,MAAM,IAAI,CAAC,IAAIuD,EAAE,CAACO,WAAW,CAAC9D,MAAM,GAAGlI,SAAS,CAACsN,oBAAoB,EAAC;UACpF7B,EAAE,CAAC8B,cAAc,CAAC,CAAC,CAAC;UACpB;;QAEJ9B,EAAE,CAAC+B,oBAAoB,CAACC,MAAM,EAAE;QAChChC,EAAE,CAAC9B,UAAU,CAAC+D,kBAAkB,CAAC;UAACC,SAAS,EAAElC,EAAE,CAACO,WAAW,CAAC4B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAChL,MAAM;QAAC,CAAC,CAAC;MACtF;KACH,EACD;MACIsK,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;MACnE2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACqC,6BAA6B,EAAE,EAAC;UAClCrC,EAAE,CAAC8B,cAAc,CAAC,CAAC,CAAC;UACpB;;QAEJ9B,EAAE,CAAC+B,oBAAoB,CAACC,MAAM,EAAE;QAChC,IAAIM,UAAU,GAAO,EAAE;QACvBtC,EAAE,CAACuC,YAAY,CAACD,UAAU,CAAC;QAC3B,OAAOA,UAAU,CAACE,IAAI;QACtB,OAAOF,UAAU,CAACG,IAAI;QACtB,OAAOH,UAAU,CAACI,IAAI;QACtB,IAAI,CAAC,IAAI,CAACrD,uBAAuB,IAAI,CAAC,IAAI,CAAC1I,kBAAkB,IAAM,IAAI,CAAC2I,yBAAyB,IAAI,IAAI,CAAC3I,kBAAkB,IAAI,IAAI,CAAClB,gBAAgB,IAAI,IAAI,IAAI,IAAI,CAACA,gBAAgB,IAAIkN,SAAU,EAChML,UAAU,GAAG;UAAC,WAAW,EAAG,IAAI,CAAC7M;QAAgB,CAAC;QACtDuK,EAAE,CAAC9B,UAAU,CAAC0E,WAAW,CAACN,UAAU,CAAC;MACzC;KACH,EACD;MACIZ,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAC/D2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACO,WAAW,CAAC9D,MAAM,IAAI,CAAC,IAAIuD,EAAE,CAACO,WAAW,CAAC9D,MAAM,GAAGlI,SAAS,CAACsO,cAAc,EAAC;UAC9E7C,EAAE,CAAC8C,UAAU,CAAC,CAAC,CAAC;UAChB;;QAEJ9C,EAAE,CAAC+B,oBAAoB,CAACC,MAAM,EAAE;QAChChC,EAAE,CAAC9B,UAAU,CAAC6E,iBAAiB,CAAC;UAACb,SAAS,EAAElC,EAAE,CAACO,WAAW,CAAC4B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAChL,MAAM;QAAC,CAAC,CAAC;MACrF;KACH,EACD;MACIsK,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAC/D2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACgD,yBAAyB,EAAE,EAAC;UAC9BhD,EAAE,CAAC8C,UAAU,CAAC,CAAC,CAAC;UAChB;;QAEJ9C,EAAE,CAAC+B,oBAAoB,CAACC,MAAM,EAAE;QAChC,IAAIM,UAAU,GAAO,EAAE;QACvBtC,EAAE,CAACuC,YAAY,CAACD,UAAU,CAAC;QAC3B,OAAOA,UAAU,CAACE,IAAI;QACtB,OAAOF,UAAU,CAACG,IAAI;QACtB,OAAOH,UAAU,CAACI,IAAI;QACtB,IAAI,CAAC,IAAI,CAACrD,uBAAuB,IAAI,CAAC,IAAI,CAAC1I,kBAAkB,IAAM,IAAI,CAAC2I,yBAAyB,IAAI,IAAI,CAAC3I,kBAAkB,IAAI,IAAI,CAAClB,gBAAgB,IAAI,IAAI,IAAI,IAAI,CAACA,gBAAgB,IAAIkN,SAAU,EAChML,UAAU,GAAG;UAAC,WAAW,EAAG,IAAI,CAAC7M;QAAgB,CAAC;QACtDuK,EAAE,CAAC9B,UAAU,CAAC+E,SAAS,CAACX,UAAU,CAAC;MACvC;KACH,CACJ;IACD,IAAI,CAACY,cAAc,GAAG,CAClB;MACIxB,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACzD2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACmD,0BAA0B,CAAC5O,SAAS,CAACuK,WAAW,CAACsE,WAAW,CAAC,EAAC;UAChEpD,EAAE,CAACqD,UAAU,GAAG9O,SAAS,CAACuK,WAAW,CAACsE,WAAW;UACjDpD,EAAE,CAACsD,4BAA4B,CAAC/O,SAAS,CAACuK,WAAW,CAACsE,WAAW,CAAC;UAClEpD,EAAE,CAAChJ,qBAAqB,GAAG,IAAI;;MAGvC,CAAC;MACDuM,OAAO,EAAEvD,EAAE,CAACmD,0BAA0B,CAAC5O,SAAS,CAACuK,WAAW,CAACsE,WAAW;KAC3E,EACD;MACI1B,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC5D2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACmD,0BAA0B,CAAC5O,SAAS,CAACuK,WAAW,CAAC0E,cAAc,CAAC,EAAC;UACnExD,EAAE,CAACqD,UAAU,GAAG9O,SAAS,CAACuK,WAAW,CAAC0E,cAAc;UACpDxD,EAAE,CAACsD,4BAA4B,CAAC/O,SAAS,CAACuK,WAAW,CAAC0E,cAAc,CAAC;UACrExD,EAAE,CAAChJ,qBAAqB,GAAG,IAAI;SAClC,MAAI;UACDgJ,EAAE,CAAC+B,oBAAoB,CAAC0B,IAAI,CAACzD,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,EAAC+J,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC,CAAC;;MAEpI,CAAC;MACDsN,OAAO,EAAEvD,EAAE,CAACC,QAAQ,IAAI1L,SAAS,CAAC+L,SAAS,CAACoD;KAC/C,EACD;MACIhC,KAAK,EAAE,IAAI,CAAC1L,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC5D2L,OAAO,EAAEA,CAAA,KAAI;QACT,IAAG5B,EAAE,CAACmD,0BAA0B,CAAC5O,SAAS,CAACuK,WAAW,CAAC6E,cAAc,CAAC,EAAC;UACnE3D,EAAE,CAACqD,UAAU,GAAG9O,SAAS,CAACuK,WAAW,CAAC6E,cAAc;UACpD3D,EAAE,CAACsD,4BAA4B,CAAC/O,SAAS,CAACuK,WAAW,CAAC6E,cAAc,CAAC;UACrE3D,EAAE,CAAChJ,qBAAqB,GAAG,IAAI;SAClC,MAAI;UACDgJ,EAAE,CAAC+B,oBAAoB,CAAC0B,IAAI,CAACzD,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,EAAC+J,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC,CAAC;;MAEpI;KACH,CACJ;IACD,IAAI,CAAC2N,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAI,CAACC,SAAS,GAAG;IACb;IACA;IACA;IACA;IACA;MACIC,KAAK,EAAE,CAACzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,CAAC;MACvC/I,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,sBAAsB;KAC1D,EACD;MACI+N,KAAK,EAAE,CAACzP,SAAS,CAAC0P,UAAU,CAACE,SAAS,CAAC;MACvChJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,wBAAwB;KAC5D,EACD;MACI+N,KAAK,EAAE,CAACzP,SAAS,CAAC0P,UAAU,CAACG,WAAW,CAAC;MACzCjJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,wBAAwB;KAC5D,EACD;MACI+N,KAAK,EAAE,CAACzP,SAAS,CAAC0P,UAAU,CAACI,MAAM,CAAC;MACpClJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,mBAAmB;KACvD,EACD;MACI+N,KAAK,EAAE,CAAC,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAE,EAAE,GAAG3P,SAAS,CAAC0P,UAAU,CAACK,KAAK,CAAC;MAC7EnJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,iCAAiC;KACrE,EACD;MACI+N,KAAK,EAAE,CAAC,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAE,EAAE,GAAG3P,SAAS,CAAC0P,UAAU,CAACK,KAAK,CAAC;MAC7EnJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,mCAAmC;KACvE,EACD;MACI+N,KAAK,EAAE,CAAC,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAE,EAAE,GAAG3P,SAAS,CAAC0P,UAAU,CAACK,KAAK,CAAC;MAC7EnJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,8BAA8B;KAClE,CACJ;IAED,IAAI,CAACsO,QAAQ,GAAG,CACZ;MACIP,KAAK,EAAE,CAACzP,SAAS,CAACiQ,QAAQ,CAACC,OAAO,CAAC;MACnCtJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,kBAAkB;KACtD,EACD;MACI+N,KAAK,EAAE,CAACzP,SAAS,CAACiQ,QAAQ,CAACE,IAAI,CAAC;MAChCvJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,eAAe;KACnD,EACD;MACI+N,KAAK,EAAE,CAACzP,SAAS,CAACiQ,QAAQ,CAACG,GAAG,CAAC;MAC/BxJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,cAAc;KAClD,CACJ;IAED,IAAI,CAAC2O,OAAO,GAAG,CAAC;MACZzJ,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD4O,GAAG,EAAE,QAAQ;MACbpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAC;QACFC,MAAM,EAAE,SAAS;QACpBC,KAAK,EAAE;OACP;MACDC,SAASA,CAACC,EAAE,EAAEC,IAAI;QACdtF,EAAE,CAACuF,KAAK,GAAGF,EAAE,CAACG,QAAQ,EAAE;QACxBxF,EAAE,CAACyF,YAAY,EAAE;QACjBzF,EAAE,CAACvE,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDiK,gBAAgBA,CAAA;QACZ,OAAO,gBAAgB;MAC3B;KACH,EAAC;MACEvK,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,gBAAgB,CAAC;MAClD4O,GAAG,EAAE,MAAM;MACXpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACI7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD4O,GAAG,EAAE,YAAY;MACjBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZW,eAAe,EAAE,SAAAA,CAAS3B,KAAK;QAC3B,OAAOhE,EAAE,CAACjG,WAAW,CAACC,gBAAgB,CAACgK,KAAK,CAAC;MACjD;KACH,EAAC;MACE7I,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxD4O,GAAG,EAAE,gBAAgB;MACrBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EAAC;MACE7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACrD4O,GAAG,EAAE,WAAW;MAChBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACX,EAAE;MACC7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D4O,GAAG,EAAE,kBAAkB;MACvBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZU,gBAAgB,EAAG1B,KAAK,IAAI;QACxB,IAAGA,KAAK,IAAI,IAAI,EAAC;UACb,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;SACjF,MAAK;UACF,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;;MAE/E,CAAC;MACD2B,eAAe,EAAE,SAAAA,CAAS3B,KAAK;QAC3B,OAAOA,KAAK,IAAI,IAAI,GAAE,SAAS,GAAGA,KAAK;MAC3C,CAAC;MACD4B,oBAAoB,EAAE,SAAAA,CAAS5B,KAAK,EAAEsB,IAAI;QACtC,IAAGA,IAAI,CAACO,wBAAwB,IAAI,CAAC,EAAC;UAClC,OAAO7F,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAGqP,IAAI,CAACO,wBAAwB,IAAI,CAAC,EAAC;UACxC,OAAO7F,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;SAC/D,MAAK,IAAGqP,IAAI,CAACO,wBAAwB,IAAI,CAAC,EAAC;UACxC,OAAO7F,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAGqP,IAAI,CAACO,wBAAwB,IAAI,CAAC,EAAC;UACxC,OAAO7F,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;SAChE,MAAK,IAAGqP,IAAI,CAACO,wBAAwB,IAAI,CAAC,EAAC;UACxC,OAAO7F,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;SAC7D,MAAK,IAAGqP,IAAI,CAACO,wBAAwB,IAAI,CAAC,EAAC;UACxC,OAAO7F,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;SAC7D,MAAI;UACD,OAAOqP,IAAI,CAACO,wBAAwB;;MAE5C,CAAC;MACDC,aAAa,EAAC;KAChB,EACE;MACA3K,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D4O,GAAG,EAAE,QAAQ;MACbpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,IAAI;MACZU,gBAAgB,EAAG1B,KAAK,IAAI;QACxB,IAAGA,KAAK,IAAI,CAAC,EAAC;UACV,OAAO,CAAC,KAAK,EAAG,cAAc,EAAE,YAAY,EAAE,YAAY,EAAC,cAAc,CAAC;SAC7E,MAAK,IAAGA,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UACzC;UACA,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;SACjF,MAAK,IAAGN,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAC;UAC7C,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;SACjF,MAAK,IAAGF,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACE,SAAS,EAAC;UAC7C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;SACpF,MAAK,IAAGH,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACG,WAAW,EAAC;UAC/C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;SACpF,MAAK,IAAGJ,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACI,MAAM,EAAC;UAC1C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAC,cAAc,CAAC;SAC9E,MAAK,IAAGL,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;SAChF,MAAK,IAAGN,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;SAChF,MAAK,IAAGN,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UAC9F,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;;QAErF,OAAO,EAAE;MACb,CAAC;MACDqB,eAAe,EAAG3B,KAAK,IAAG;QACtB,IAAGA,KAAK,IAAI,CAAC,EAAC;UACV,OAAOhE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UACzC;UACA,OAAOtE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAC;UAC7C,OAAOlE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACG,WAAW,EAAC;UAC/C,OAAOpE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACI,MAAM,EAAC;UAC1C,OAAOrE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACE,SAAS,EAAC;UAC7C,OAAOnE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAG+N,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UAC9F,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACvE,MAAK,IAAG+N,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UAC9F,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;SACzE,MAAK,IAAG+N,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;UAC9F,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;QAErE,OAAO,EAAE;MACb,CAAC;MACDgP,KAAK,EAAC;QACFE,KAAK,EAAE;;KAEd,EACG;MACAhK,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D4O,GAAG,EAAE,eAAe;MACpBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZW,eAAe,EAAE,SAAAA,CAAS3B,KAAK;QAC3B,OAAOhE,EAAE,CAACjG,WAAW,CAACgM,uBAAuB,CAAC/B,KAAK,CAAC;MACxD;KACH,EAAE;MACC7I,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD4O,GAAG,EAAE,WAAW;MAChBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,IAAI;MACZW,eAAe,EAAE,SAAAA,CAAS3B,KAAK;QAC/B,OAAOhE,EAAE,CAACjG,WAAW,CAACgM,uBAAuB,CAAC/B,KAAK,CAAC;MACxD;KACC,EACG;MACA7I,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,iBAAiB,CAAC;MACnD4O,GAAG,EAAE,OAAO;MACZpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACX,EAAC;MACE7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD4O,GAAG,EAAE,cAAc;MACnBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EAAC;MACE7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzD4O,GAAG,EAAE,cAAc;MACnBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EAAC;MACE7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD4O,GAAG,EAAE,cAAc;MACnBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EAAC;MACE7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5D4O,GAAG,EAAE,cAAc;MACnBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,IAAI;MACZW,eAAe,EAAE3B,KAAK,IAAG;QACrB,OAAOhE,EAAE,CAACjG,WAAW,CAACgM,uBAAuB,CAAC/B,KAAK,CAAC;MACxD;KACH,EAAC;MACE7I,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D4O,GAAG,EAAE,cAAc;MACnBpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACX,EACG;MACI7J,IAAI,EAAE,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACrD4O,GAAG,EAAE,SAAS;MACdpC,IAAI,EAAE,OAAO;MACbqC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,IAAI;MACZU,gBAAgB,EAAG1B,KAAK,IAAI;QACxB,IAAGA,KAAK,IAAIzP,SAAS,CAACiQ,QAAQ,CAACC,OAAO,EAAC;UACnC,OAAO,CAAC,KAAK,EAAG,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;SACrF,MAAK,IAAGT,KAAK,IAAIzP,SAAS,CAACiQ,QAAQ,CAACE,IAAI,EAAC;UACtC,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAC,cAAc,EAAC,cAAc,CAAC;SAC/E,MAAK,IAAGV,KAAK,IAAIzP,SAAS,CAACiQ,QAAQ,CAACG,GAAG,EAAC;UACrC,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;;QAElF,OAAO,EAAE;MACb,CAAC;MACDgB,eAAe,EAAG3B,KAAK,IAAG;QACtB,IAAGA,KAAK,IAAIzP,SAAS,CAACiQ,QAAQ,CAACC,OAAO,EAAC;UACnC,OAAOzE,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC;SACtD,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAACiQ,QAAQ,CAACE,IAAI,EAAC;UACtC,OAAO1E,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,eAAe,CAAC;SACnD,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAACiQ,QAAQ,CAACG,GAAG,EAAC;UACrC,OAAO3E,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,cAAc,CAAC;;QAEnD,OAAO,EAAE;MACb,CAAC;MACDgP,KAAK,EAAC;QACFE,KAAK,EAAE;;KAEd,CACR;IAEG,IAAI,CAACa,WAAW,GAAG;MACfC,gBAAgB,EAAC,KAAK;MACtBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAE,EAAE;IACjB,IAAI,CAAC5D,IAAI,GAAG,YAAY;IACxB,IAAI6D,mBAAmB,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC;IAC1E,IAAIC,uBAAuB,GAAG,IAAI,CAACJ,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACzF,IAAG,CAACJ,mBAAmB,IAAI,EAAE,EAAE9J,MAAM,GAAG,CAAC,EAAC;MACtC,IAAI,CAACsE,UAAU,CAACxG,YAAY,GAAGgM,mBAAmB;;IAEtD,IAAG,CAACK,uBAAuB,IAAI,EAAE,EAAEnK,MAAM,GAAG,CAAC,IAAImK,uBAAuB,IAAI,OAAO,EAAC;MAChF,IAAI,CAACjQ,kBAAkB,GAAG,KAAK;;IAEnC,IAAI,CAACkQ,OAAO,GAAE;MACVC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IAED,IAAI,CAACC,MAAM,CAAC,IAAI,CAACX,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC5D,IAAI,EAAE,IAAI,CAAC3B,UAAU,CAAC;IACvE,IAAI,CAACkG,eAAe,EAAE;EAC1B;EAEAA,eAAeA,CAAA;IACX,IAAIjH,EAAE,GAAG,IAAI;IACb,IAAI,CAACzB,eAAe,CAAC0I,eAAe,CAAEC,QAAQ,IAAG;MAC7ClH,EAAE,CAACd,YAAY,GAAGgI,QAAQ;IAC9B,CAAC,CAAC;EACN;EAEAC,iBAAiBA,CAACC,SAAkB;IAChC;IACA;IACAC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,SAAS,CAAC;IAChD,IAAI,CAACzQ,kBAAkB,GAAGyQ,SAAS;EACvC;EAEAG,UAAUA,CAAA;IACN,IAAI,CAACjG,aAAa,CAACkG,KAAK,EAAE;EAC9B;EAEAC,qBAAqBA,CAAA;IACjB,IAAIzH,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAAChJ,qBAAqB,IAAI,KAAK,EAAC;MACnC,IAAI,CAAC0Q,gBAAgB,GAAG,IAAI;;IAEhC,IAAG,IAAI,CAAC3Q,uBAAuB,IAAI,KAAK,EAAC;MACrC,IAAI,CAACyK,kBAAkB,CAACgG,KAAK,EAAE;;EAEvC;EAEAnS,WAAWA,CAAA;IACPmK,KAAK,CAACmI,cAAc,EAAE;IACtB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAI,IAAI,CAACnS,gBAAgB,IAAI,IAAI,IAAI,IAAI,CAACA,gBAAgB,IAAIkN,SAAS,EACnEiF,MAAM,GAAG;MACLC,SAAS,EAAE,IAAI,CAACpS,gBAAgB,CAACqS,IAAI;KACxC;IACLT,OAAO,CAACC,GAAG,CAACM,MAAM,CAAC;IACnB,IAAI,CAACZ,MAAM,CAAC,CAAC,EAAE,IAAI,CAACV,QAAQ,EAAE,IAAI,CAAC5D,IAAI,EAAEkF,MAAM,CAAC;EACpD;EAEAG,cAAcA,CAAA;IACV,IAAI,CAAC1B,UAAU,GAAG,CAAC;IACnB,IAAI,CAACtF,UAAU,CAACG,QAAQ,GAAG,IAAI;IAC/B,IAAI,CAAC8F,MAAM,CAAC,CAAC,EAAE,IAAI,CAACV,QAAQ,EAAE,IAAI,CAAC5D,IAAI,EAAE,IAAI,CAAC3B,UAAU,CAAC;EAC7D;EAEAwB,YAAYA,CAACD,UAAU;IACnB,IAAItC,EAAE,GAAG,IAAI;IACbzD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACuE,UAAU,CAAC,CAACiH,OAAO,CAACnD,GAAG,IAAG;MACvC,IAAG,IAAI,CAAC9D,UAAU,CAAC8D,GAAG,CAAC,IAAI,IAAI,EAAC;QAC5B,IAAGA,GAAG,IAAI,UAAU,EAAC;UACjBvC,UAAU,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACvB,UAAU,CAACF,QAAQ,CAACoH,OAAO,EAAE;SACtE,MAAK,IAAGpD,GAAG,IAAI,QAAQ,EAAC;UACrBvC,UAAU,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACvB,UAAU,CAACD,MAAM,CAACmH,OAAO,EAAE;SAClE,MAAK,IAAGpD,GAAG,IAAI,cAAc,EAAC;UAC3BvC,UAAU,CAAC,cAAc,CAAC,GAAGtC,EAAE,CAACjG,WAAW,CAACmO,iBAAiB,CAAC,IAAI,CAACnH,UAAU,CAACxG,YAAY,CAAC;SAC9F,MACG;UACA+H,UAAU,CAACuC,GAAG,CAAC,GAAG,IAAI,CAAC9D,UAAU,CAAC8D,GAAG,CAAC;;;IAGlD,CAAC,CAAC;EACN;EAEAmC,MAAMA,CAACxE,IAAI,EAAE2F,KAAK,EAAEzF,IAAI,EAAEkF,MAAM;IAC5B,IAAG,IAAI,CAACjR,kBAAkB,EAAC;MACvB,IAAI,CAAC2I,yBAAyB,GAAG,IAAI;MACrC,IAAI,CAACD,uBAAuB,GAAG,KAAK;KACvC,MAAI;MACD,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACD,uBAAuB,GAAG,IAAI;;IAEvC,IAAI,CAACgH,UAAU,GAAG7D,IAAI;IACtB,IAAI,CAAC8D,QAAQ,GAAG6B,KAAK;IACrB,IAAI,CAACzF,IAAI,GAAGA,IAAI;IAChB,IAAI1C,EAAE,GAAG,IAAI;IACb,IAAIsC,UAAU,GAAG;MACbE,IAAI;MACJC,IAAI,EAAE0F,KAAK;MACXzF;KACH;IACD,IAAI,CAACH,YAAY,CAACD,UAAU,CAAC;IAC7BtC,EAAE,CAAC+B,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAG,IAAI,CAACrL,kBAAkB,EAAC;MACvBiR,MAAM,CAAC,MAAM,CAAC,GAAGpF,IAAI;MACrBoF,MAAM,CAAC,MAAM,CAAC,GAAGO,KAAK;MACtBP,MAAM,CAAC,MAAM,CAAC,GAAGlF,IAAI;MACrBkF,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI;MACzBA,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI,CAACnS,gBAAgB,CAACqS,IAAI,EAAE;MAClDT,OAAO,CAACC,GAAG,CAACM,MAAM,CAAC;MACnB,IAAI,CAAC1J,UAAU,CAAC7I,WAAW,CAACuS,MAAM,EAAGV,QAAQ,IAAG;QAC5C;QACAlH,EAAE,CAAC6G,OAAO,GAAG;UACTC,OAAO,EAAEI,QAAQ,CAACJ,OAAO;UACzBC,KAAK,EAAEG,QAAQ,CAACkB;SACnB;QACDpI,EAAE,CAACQ,kBAAkB,GAAG;UAAC,GAAGR,EAAE,CAACe;QAAU,CAAC;QAC1C,IAAIsH,IAAI,GAAG,EAAE;QACb,KAAK,MAAMC,GAAG,IAAItI,EAAE,CAAC6G,OAAO,CAACC,OAAO,EAAE;UAClCuB,IAAI,CAACE,IAAI,CAACD,GAAG,CAAClR,MAAM,CAAC;;QAEzB;QACA,IAAI,CAAC8G,UAAU,CAACsK,mBAAmB,CAACH,IAAI,EAAGI,IAAI,IAAI;UAC/C,IAAIC,IAAI,GAAG,CAAC,GAAGD,IAAI,CAAC;UACpB,KAAK,MAAMH,GAAG,IAAItI,EAAE,CAAC6G,OAAO,CAACC,OAAO,EAAE;YAClC,KAAI,IAAI1E,EAAE,IAAIsG,IAAI,EAAE;cAChB,IAAGJ,GAAG,CAAClR,MAAM,IAAIgL,EAAE,CAAChL,MAAM,EAAE;gBACxBkR,GAAG,CAACzQ,gBAAgB,GAAG,IAAI,CAACD,YAAY,CAACwK,EAAE,CAACuG,SAAS,CAAC;gBACtDL,GAAG,CAACzC,wBAAwB,GAAGzD,EAAE,CAACuG,SAAS;;;;UAIvD;QACJ,CAAC,EAAE,IAAI,EAAE,MAAI,CAAE,CAAC,CAAC;QACjB,IAAI,CAACzK,UAAU,CAAC0K,WAAW,CAACP,IAAI,EAAGI,IAAI,IAAG;UACtC,KAAK,MAAMH,GAAG,IAAItI,EAAE,CAAC6G,OAAO,CAACC,OAAO,EAAE;YAClC,KAAI,IAAI1E,EAAE,IAAIqG,IAAI,EAAE;cAChB,IAAGH,GAAG,CAAClR,MAAM,IAAIgL,EAAE,CAAChL,MAAM,EAAE;gBACxBkR,GAAG,CAACO,UAAU,GAAGzG,EAAE,CAAC0G,QAAQ;;;;QAI5C,CAAC,CAAC;MAEN,CAAC,EAAE,IAAI,EAAE,MAAI;QACT9I,EAAE,CAAC+B,oBAAoB,CAACgH,OAAO,EAAE;MACrC,CAAC,CAAC;MACF;;IAEJzG,UAAU,CAAC,MAAM,CAAC,GAAGE,IAAI;IACzBF,UAAU,CAAC,MAAM,CAAC,GAAG6F,KAAK;IAC1B7F,UAAU,CAAC,MAAM,CAAC,GAAGI,IAAI;IACzB,IAAI,CAACxE,UAAU,CAAC8I,MAAM,CAAC1E,UAAU,EAAG4E,QAAQ,IAAG;MAC3CG,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAACJ,OAAO,CAAC;MAC7B9G,EAAE,CAAC6G,OAAO,GAAG;QACTC,OAAO,EAAEI,QAAQ,CAACJ,OAAO;QACzBC,KAAK,EAAEG,QAAQ,CAACkB;OACnB;MACDpI,EAAE,CAACQ,kBAAkB,GAAG;QAAC,GAAGR,EAAE,CAACe;MAAU,CAAC;MAC1C,IAAIsH,IAAI,GAAG,EAAE;MACb,KAAK,MAAMC,GAAG,IAAItI,EAAE,CAAC6G,OAAO,CAACC,OAAO,EAAE;QAClCuB,IAAI,CAACE,IAAI,CAACD,GAAG,CAAClR,MAAM,CAAC;;MAEzB;MACA,IAAI,CAAC8G,UAAU,CAACsK,mBAAmB,CAACH,IAAI,EAAGI,IAAI,IAAI;QAC/C,IAAIC,IAAI,GAAG,CAAC,GAAGD,IAAI,CAAC;QACpB,KAAK,MAAMH,GAAG,IAAItI,EAAE,CAAC6G,OAAO,CAACC,OAAO,EAAE;UAClC,KAAI,IAAI1E,EAAE,IAAIsG,IAAI,EAAE;YAChB,IAAGJ,GAAG,CAAClR,MAAM,IAAIgL,EAAE,CAAChL,MAAM,EAAE;cACxBkR,GAAG,CAACzQ,gBAAgB,GAAG,IAAI,CAACD,YAAY,CAACwK,EAAE,CAACuG,SAAS,CAAC;cACtDL,GAAG,CAACzC,wBAAwB,GAAGzD,EAAE,CAACuG,SAAS;;;;QAIvD;MACJ,CAAC,EAAE,IAAI,EAAE,MAAI,CAAE,CAAC,CAAC;MACjB,IAAI,CAACzK,UAAU,CAAC0K,WAAW,CAACP,IAAI,EAAGI,IAAI,IAAG;QACtC,KAAK,MAAMH,GAAG,IAAItI,EAAE,CAAC6G,OAAO,CAACC,OAAO,EAAE;UAClC,KAAI,IAAI1E,EAAE,IAAIqG,IAAI,EAAE;YAChB,IAAGH,GAAG,CAAClR,MAAM,IAAIgL,EAAE,CAAChL,MAAM,EAAE;cACxBkR,GAAG,CAACO,UAAU,GAAGzG,EAAE,CAAC0G,QAAQ;;;;MAI5C,CAAC,CAAC;IAEN,CAAC,EAAE,IAAI,EAAE,MAAI;MACT9I,EAAE,CAAC+B,oBAAoB,CAACgH,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAnR,YAAYA,CAACL,MAAM;IACf,IAAGA,MAAM,IAAI,IAAI,IAAIA,MAAM,IAAIoL,SAAS,EAAC;MACrC,OAAO,SAAS;;IAEpB,IAAGpL,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKoL,SAAS,EAAE;MACxC,IAAGpL,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,EAAC;QAC9B,OAAO,KAAK;OACf,MAAM,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,EAAE;QACzE,OAAO,IAAI;OACd,MAAM;QACH,OAAO,WAAW;;KAEzB,MAAK,OAAO,WAAW;EAC5B;EAEAO,gBAAgBA,CAACP,MAAM;IACnB,IAAGA,MAAM,IAAI,CAAC,EAAC;MACX,OAAO,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAGsB,MAAM,IAAI,CAAC,EAAC;MACjB,OAAO,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAGsB,MAAM,IAAI,CAAC,EAAC;MACjB,OAAO,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAGsB,MAAM,IAAI,CAAC,EAAC;MACjB,OAAO,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;KAClE,MAAK,IAAGsB,MAAM,IAAI,CAAC,EAAC;MACjB,OAAO,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAK,IAAGsB,MAAM,IAAI,CAAC,EAAC;MACjB,OAAO,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAI;MACD,OAAOsB,MAAM;;EAErB;EAEAyR,0BAA0BA,CAAA;IACtB,IAAG,IAAI,CAACzI,WAAW,CAAC9D,MAAM,IAAI,CAAC,EAAC;MAC5B,OAAO,IAAI;;IAEf,IAAIwM,IAAI,GAAG,KAAK;IAChB,KAAI,IAAIC,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAAC3I,WAAW,CAAC9D,MAAM,EAACyM,CAAC,EAAE,EAAC;MACxC,IAAG,CAAC,IAAI,CAAC3I,WAAW,CAAC2I,CAAC,CAAC,CAACC,SAAS,IAAI,EAAE,KAAK,EAAE,EAAC;QAC3CF,IAAI,GAAG,IAAI;QACX;;;IAGR,OAAOA,IAAI;EACf;EAGA3F,4BAA4BA,CAAClD,IAAI;IAC7B,IAAIJ,EAAE,GAAG,IAAI;IACb,IAAI4H,MAAM,GAAG;MAACwB,KAAK,EAAEhJ;IAAI,CAAC;IAC1B,IAAGA,IAAI,IAAI7L,SAAS,CAACuK,WAAW,CAACsE,WAAW,EAAC,C,CAC5C,MAAK,IAAGhD,IAAI,IAAI7L,SAAS,CAACuK,WAAW,CAAC0E,cAAc,EAAC;MAClDoE,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC9J,YAAY;KAC7C,MAAK,IAAGsC,IAAI,IAAI7L,SAAS,CAACuK,WAAW,CAAC6E,cAAc,EAAC;MAClDiE,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,CAACjK,YAAY;;IAE9C,IAAI,CAACsB,mBAAmB,GAAG;MAAC,GAAG2I;IAAM,CAAC;EAC1C;EAEAyB,gBAAgBA,CAACrF,KAAK;IAClB,IAAGA,KAAK,EAAC;MACL,IAAI,CAACrF,SAAS,GAAGqF,KAAK;KACzB,MAAI;MACD,IAAI,CAACrF,SAAS,GAAG,IAAI;;EAE7B;EAEA2K,cAAcA,CAACtF,KAAK;IAChB,IAAGA,KAAK,EAAC;MACL,IAAI,CAACvF,WAAW,GAAGuF,KAAK;KAC3B,MAAI;MACD,IAAI,CAACvF,WAAW,GAAG,IAAIC,IAAI,EAAE;;EAErC;EAEA6K,YAAYA,CAACnJ,IAAI;IACb,IAAIJ,EAAE,GAAG,IAAI;IACb,IAAGI,IAAI,IAAI,CAAC,EAAC;MAAC;MACV,IAAG,IAAI,CAACsH,gBAAgB,IAAI,IAAI,IAAI,IAAI,CAACA,gBAAgB,IAAI/E,SAAS,EAAC;QACnE;;MAEJ,IAAI,CAACZ,oBAAoB,CAACC,MAAM,EAAE;MAClC,IAAI,CAAC9D,UAAU,CAACsL,cAAc,CAAC,IAAI,CAACjJ,WAAW,CAAC4B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAChL,MAAM,CAAC,EAAE;QAACiO,EAAE,EAAE,IAAI,CAACqC;MAAgB,CAAC,EAAGR,QAAQ,IAAG;QAC3GzH,UAAU,CAAC;UACPO,EAAE,CAACgH,MAAM,CAAChH,EAAE,CAACqG,UAAU,EAAErG,EAAE,CAACsG,QAAQ,EAAEtG,EAAE,CAAC0C,IAAI,EAAE1C,EAAE,CAACe,UAAU,CAAC;QACjE,CAAC,CAAC;QACFf,EAAE,CAAC+B,oBAAoB,CAAC0H,OAAO,CAACzJ,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC3F+J,EAAE,CAACO,WAAW,GAAG,EAAE;MACvB,CAAC,CAAC;MACF,IAAI,CAACvJ,qBAAqB,GAAG,KAAK;KACrC,MAAI;MAAC;MACF,IAAIuK,KAAK,GAAG;QACR8D,EAAE,EAAE,IAAI;QACRjE,QAAQ,EAAE,IAAI,CAACD,kBAAkB,CAACC,QAAQ;QAC1CjG,IAAI,EAAE,IAAI,CAACgG,kBAAkB,CAAChG,IAAI;QAClCwC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/B0D,WAAW,EAAE,IAAI,CAACF,kBAAkB,CAACE,WAAW;QAChDvD,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BsL,KAAK,EAAE,IAAI,CAAC/F;OACf;MACD,IAAI,CAACtB,oBAAoB,CAACC,MAAM,EAAE;MAClC,IAAI,CAAC9D,UAAU,CAACsL,cAAc,CAAC,IAAI,CAACjJ,WAAW,CAAC4B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAChL,MAAM,CAAC,EAAEmK,KAAK,EAAG2F,QAAQ,IAAG;QACrFzH,UAAU,CAAC;UACPO,EAAE,CAACgH,MAAM,CAAChH,EAAE,CAACqG,UAAU,EAAErG,EAAE,CAACsG,QAAQ,EAAEtG,EAAE,CAAC0C,IAAI,EAAE1C,EAAE,CAACe,UAAU,CAAC;QACjE,CAAC,CAAC;QACFf,EAAE,CAAC+B,oBAAoB,CAAC0H,OAAO,CAACzJ,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC3F+J,EAAE,CAACO,WAAW,GAAG,EAAE;MACvB,CAAC,CAAC;MACF,IAAI,CAACxJ,uBAAuB,GAAG,KAAK;;EAE5C;EAEA2S,kBAAkBA,CAAA;IACd,IAAG,IAAI,CAACvI,kBAAkB,CAACC,QAAQ,EAAE0G,IAAI,EAAE,CAACrL,MAAM,GAAG,CAAC,EAAC;MACnD,IAAIuD,EAAE,GAAG,IAAI;MACb,IAAI,CAAC2J,eAAe,CAACC,GAAG,CAAC,UAAU,EAAE5J,EAAE,CAAC5B,eAAe,CAACyL,oBAAoB,CAACC,IAAI,CAAC9J,EAAE,CAAC5B,eAAe,CAAC,EAAE,EAAE,EAAE;QAAC2L,KAAK,EAAE/J,EAAE,CAACmB,kBAAkB,CAACC;MAAQ,CAAC,EAAE8F,QAAQ,IAAG;QAC3JlH,EAAE,CAACgK,gBAAgB,GAAG9C,QAAQ,IAAI,CAAC;MACvC,CAAC,CAAC;;EAEV;EAEApE,UAAUA,CAAC1C,IAAI;IACX,IAAG,IAAI,CAACG,WAAW,CAAC9D,MAAM,IAAI,CAAC,IAAI2D,IAAI,IAAI,CAAC,EAAC;MACzC,IAAI,CAAC2B,oBAAoB,CAAC0B,IAAI,CAAC,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2CAA2C,CAAC,EAAC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC;KAC7J,MAAK,IAAG,IAAI,CAACsK,WAAW,CAAC9D,MAAM,GAAGlI,SAAS,CAACsO,cAAc,IAAI,IAAI,CAACgE,OAAO,CAACE,KAAK,GAAGxS,SAAS,CAACsO,cAAc,EAAC;MACzG,IAAI,CAACd,oBAAoB,CAAC0B,IAAI,CAAC,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,sCAAsC,CAAC,EAAC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC;;EAG7J;EAEA6L,cAAcA,CAAC1B,IAAI;IACf,IAAG,IAAI,CAACG,WAAW,CAAC9D,MAAM,IAAI,CAAC,IAAI2D,IAAI,IAAI,CAAC,EAAC;MACzC,IAAI,CAAC2B,oBAAoB,CAAC0B,IAAI,CAAC,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2CAA2C,CAAC,EAAC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC;KAC7J,MAAK,IAAG,IAAI,CAACsK,WAAW,CAAC9D,MAAM,GAAGlI,SAAS,CAACsN,oBAAoB,IAAI,IAAI,CAACgF,OAAO,CAACE,KAAK,GAAGxS,SAAS,CAACsN,oBAAoB,EAAC;MACrH,IAAI,CAACE,oBAAoB,CAAC0B,IAAI,CAAC,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,0CAA0C,CAAC,EAAC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC;;EAGjK;EAEA+M,yBAAyBA,CAAA;IACrB,OAAOiH,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnJ,UAAU,CAAC,IAAIkJ,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1J,kBAAkB,CAAC,IAAI,IAAI,CAACqG,OAAO,CAACE,KAAK,IAAI,IAAI,IACvG,IAAI,CAACF,OAAO,CAACE,KAAK,IAAI,IAAI,KAAK,IAAI,CAACF,OAAO,CAACE,KAAK,IAAI,CAAC,IAAI,IAAI,CAACF,OAAO,CAACE,KAAK,GAAGxS,SAAS,CAACsO,cAAc,CAAE;EACrH;EAEAR,6BAA6BA,CAAA;IACzB,OAAO4H,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnJ,UAAU,CAAC,IAAIkJ,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1J,kBAAkB,CAAC,IAAI,IAAI,CAACqG,OAAO,CAACE,KAAK,IAAI,IAAI,IACvG,IAAI,CAACF,OAAO,CAACE,KAAK,IAAI,IAAI,KAAK,IAAI,CAACF,OAAO,CAACE,KAAK,IAAI,CAAC,IAAI,IAAI,CAACF,OAAO,CAACE,KAAK,GAAGxS,SAAS,CAACsN,oBAAoB,CAAE;EAC3H;EAEAsB,0BAA0BA,CAAC/C,IAAI;IAC3B,IAAGA,IAAI,IAAI7L,SAAS,CAACuK,WAAW,CAACsE,WAAW,EAAC;MACzC,OAAO,IAAI,CAACnD,QAAQ,IAAI1L,SAAS,CAAC+L,SAAS,CAAC6J,KAAK;;IAErD,IAAG,CAAC,IAAI,CAAC5J,WAAW,IAAI,EAAE,EAAE9D,MAAM,IAAI,CAAC,EAAE,OAAO,KAAK;IACrD,IAAG2D,IAAI,IAAI7L,SAAS,CAACuK,WAAW,CAAC0E,cAAc,EAAC;MAC5C,IAAG,CAACjP,SAAS,CAAC+L,SAAS,CAAC6J,KAAK,EAAE5V,SAAS,CAAC+L,SAAS,CAACoD,QAAQ,EAAEnP,SAAS,CAAC+L,SAAS,CAAC8J,QAAQ,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACpK,QAAQ,CAAC,EAAC;QAC/G,IAAI,CAACnC,YAAY,GAAG,IAAI,CAACyC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QACvD,IAAI,CAAC1C,YAAY,GAAG,IAAI;QACxB,KAAI,IAAIqL,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAAChK,YAAY,CAACzC,MAAM,EAACyM,CAAC,EAAE,EAAC;UACzC,IAAG,IAAI,CAAChK,YAAY,CAACgK,CAAC,CAAC,CAAC9N,IAAI,IAAI,IAAI,CAAC0C,YAAY,EAAC;YAC9C,IAAI,CAACD,YAAY,GAAG,IAAI,CAACqB,YAAY,CAACgK,CAAC,CAAC,CAAC/N,IAAI;YAC7C;;;QAGR,IAAG,IAAI,CAAC0C,YAAY,IAAI,IAAI,EAAC;UACzB,IAAI,CAACA,YAAY,GAAG,IAAI,CAAC0C,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;;QAE3D,KAAI,IAAI2I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3I,WAAW,CAAC9D,MAAM,EAACyM,CAAC,EAAE,EAAC;UAC3C,IAAG,IAAI,CAAC3I,WAAW,CAAC2I,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,IAAI,CAACpL,YAAY,EAAC;YACxD,OAAO,KAAK;;;QAGpB,OAAO,IAAI;;KAElB,MAAK,IAAGsC,IAAI,IAAI7L,SAAS,CAACuK,WAAW,CAAC6E,cAAc,EAAC;MAClD,IAAG,CAACpP,SAAS,CAAC+L,SAAS,CAAC6J,KAAK,EAAE5V,SAAS,CAAC+L,SAAS,CAACoD,QAAQ,EAAEnP,SAAS,CAAC+L,SAAS,CAAC8J,QAAQ,EAAE7V,SAAS,CAAC+L,SAAS,CAACgK,QAAQ,CAAC,CAACD,QAAQ,CAAC,IAAI,CAACpK,QAAQ,CAAC,EAAC;QAC7I,IAAI,CAACtC,YAAY,GAAG,IAAI,CAAC4C,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QACvD,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAAC6C,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QACvD,IAAI,CAACzC,YAAY,GAAG,IAAI,CAACyC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QACvD,KAAI,IAAI2I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3I,WAAW,CAAC9D,MAAM,EAACyM,CAAC,EAAE,EAAC;UAC3C,IAAG,IAAI,CAAC3I,WAAW,CAAC2I,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,IAAI,CAACvL,YAAY,EAAC;YACxD,OAAO,KAAK;;;QAGpB,OAAO,IAAI;;;IAGnB,OAAO,KAAK;EAChB;EAEA4M,SAASA,CAAA;IACL,IAAIvK,EAAE,GAAG,IAAI;IACb,IAAIwK,UAAU,GAAG,IAAI,CAACjK,WAAW,CAAC4B,GAAG,CAACsI,CAAC,IAAIA,CAAC,CAACrT,MAAM,CAAC;IACpD,IAAI,CAAC+H,oBAAoB,GAAG,KAAK;IACjC;IACAa,EAAE,CAAC+B,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC9D,UAAU,CAACwM,aAAa,CAACF,UAAU,EAAGtD,QAAQ,IAAG;MAClDlH,EAAE,CAACb,oBAAoB,GAAG,KAAK;MAC/Ba,EAAE,CAAC+B,oBAAoB,CAAC0H,OAAO,CAACzJ,EAAE,CAAChK,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;MACzF+J,EAAE,CAACO,WAAW,GAAG,EAAE;IACvB,CAAC,EAAC,IAAI,EAAG,MAAI;MACTP,EAAE,CAACgH,MAAM,CAAChH,EAAE,CAACqG,UAAU,EAAErG,EAAE,CAACsG,QAAQ,EAAEtG,EAAE,CAAC0C,IAAI,EAAE1C,EAAE,CAACe,UAAU,CAAC;IACjE,CAAC,CAAC;EACN;EAEA4J,kBAAkBA,CAAA;IACd,IAAG,IAAI,CAACpK,WAAW,CAAC9D,MAAM,KAAK,CAAC,EAAE;IAClC,IAAI,CAAC0C,oBAAoB,GAAG,IAAI;EACpC;EAEAsG,YAAYA,CAAA;IACR,IAAIzF,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4K,cAAc,EAAE;IACnB,IAAI,CAAC7I,oBAAoB,CAACC,MAAM,EAAE;IAClChC,EAAE,CAAC9B,UAAU,CAAC2M,OAAO,CAAC7K,EAAE,CAACuF,KAAK,EAAG2B,QAAQ,IAAG;MACxClH,EAAE,CAAC7I,SAAS,GAAG;QACX,GAAG+P;OACN;MAEDlH,EAAE,CAAC8K,iBAAiB,EAAE;MACtB9K,EAAE,CAAC+K,YAAY,EAAE;MACjB/K,EAAE,CAACgL,YAAY,EAAE;MACjBhL,EAAE,CAACiL,mBAAmB,EAAE;MACxBjL,EAAE,CAACkL,iBAAiB,EAAE;MACtB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAlL,EAAE,CAAC+K,YAAY,EAAE;MACjB/K,EAAE,CAAC9B,UAAU,CAACsK,mBAAmB,CAAC,CAACxI,EAAE,CAACuF,KAAK,CAAC,EAAGkD,IAAI,IAAG;QAClDzI,EAAE,CAAC7I,SAAS,CAACU,gBAAgB,GAAG4Q,IAAI,CAAC,CAAC,CAAC,CAACE,SAAS;MACrD,CAAC,EAAE,MAAI,CAAC,CAAC,CAAC;IACd,CAAC,EAAE,IAAI,EAAC,MAAI;MACR,IAAI,CAAC5G,oBAAoB,CAACgH,OAAO,EAAE;IACvC,CAAC,CAAC;EACN;EAEA6B,cAAcA,CAAA;IACV,IAAI5K,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC1F,cAAc,GAAG,EAAE;IACtB0F,EAAE,CAAC9E,cAAc,GAAG,EAAE;IACtB8E,EAAE,CAAC/F,gBAAgB,GAAG,EAAE;IACxB+F,EAAE,CAACxH,eAAe,GAAG,EAAE;IACvBwH,EAAE,CAAC7I,SAAS,GAAG,EAAE;EACrB;EAEA6T,YAAYA,CAAA;IACR,OAAO,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACnC,IAAI,CAACnN,UAAU,CAACoN,eAAe,CAC3B,IAAI,CAACnU,SAAS,CAACC,MAAM,EACpB8P,QAAQ,IAAI;QACT,IAAI,CAAC1O,eAAe,GAAI;UACpBC,UAAU,EAAEyO,QAAQ,CAACqE,UAAU,IAAI,CAAC;UACpC3S,iBAAiB,EAAEsO,QAAQ,CAACsE,QAAQ,IAAI,CAAC;UACzCzS,cAAc,EAAEmO,QAAQ,CAACuE,QAAQ,IAAI,CAAC;UACtCvS,eAAe,EAAEgO,QAAQ,CAACwE,SAAS,IAAI,CAAC;UACxCrS,gBAAgB,EAAE6N,QAAQ,CAACyE,SAAS,IAAI,CAAC;UACzCnS,aAAa,EAAE0N,QAAQ,CAAC0E,SAAS,IAAI;SACxC;QACDR,OAAO,CAAClE,QAAQ,CAAC;MACrB,CAAC,EACA2E,KAAK,IAAI;QACNR,MAAM,CAACQ,KAAK,CAAC;MACjB,CAAC,EACD,MAAK,CAEL,CAAC,CACJ;IACL,CAAC,CAAC;EACN;EAEAf,iBAAiBA,CAAA;IACb,IAAI,CAAC5P,cAAc,GAAG;MAClBC,IAAI,EAAE,IAAI,CAAChE,SAAS,CAACuG,YAAY;MACjCtC,IAAI,EAAE,IAAI,CAACjE,SAAS,CAACwG;KACxB;EACL;EAEAsN,mBAAmBA,CAAA;IACf,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACnC,IAAI,CAACnN,UAAU,CAAC4N,gBAAgB,CAC5B,IAAI,CAAC3U,SAAS,CAACC,MAAM,EACpB8P,QAAQ,IAAI;QACT,IAAI,CAACjN,gBAAgB,GAAG;UAAE,GAAGiN;QAAQ,CAAE;QACvCkE,OAAO,CAAClE,QAAQ,CAAC;MACrB,CAAC,EACA2E,KAAK,IAAI;QACNR,MAAM,CAACQ,KAAK,CAAC;MACjB,CAAC,EACD,MAAK,CAEL,CAAC,CACJ;IACL,CAAC,CAAC;EACN;EAEAX,iBAAiBA,CAAA;IACb,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACnC,IAAI,CAACnN,UAAU,CAACgN,iBAAiB,CAC7B,IAAI,CAACnR,WAAW,CAACmO,iBAAiB,CAAC,IAAI,CAAC/Q,SAAS,CAACoD,YAAY,CAAC,EAC9D2M,QAAQ,IAAI;QACT,IAAI,CAAC5M,cAAc,GAAG4M,QAAQ;QAC9BkE,OAAO,CAAClE,QAAQ,CAAC;MACrB,CAAC,EACA2E,KAAK,IAAI;QACNR,MAAM,CAACQ,KAAK,CAAC;MACjB,CAAC,EACD,MAAK,CAEL,CAAC,CACJ;IACL,CAAC,CAAC;EACN;EAGAd,YAAYA,CAAA;IACR,IAAI,CAAC3L,SAAS,GAAG;MACbhE,IAAI,EAAE,IAAI,CAACjE,SAAS,CAAC4U,OAAO;MAC5B3L,IAAI,EAAE,iBAAiB;MACvB4L,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,IAAI,CAAC9U,SAAS,CAAC6U;KAC3B;EACL;EAEAxU,aAAaA,CAACwM,KAAK;IACf,IAAGA,KAAK,IAAI,CAAC,EAAC;MACV,OAAO,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MACzC;MACA,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAC;MAC7C,OAAO,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACG,WAAW,EAAC;MAC/C,OAAO,IAAI,CAACpO,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACI,MAAM,EAAC;MAC1C,OAAO,IAAI,CAACrO,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;KACzD,MAAK,IAAG+N,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACE,SAAS,EAAC;MAC7C,OAAO,IAAI,CAACnO,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAG+N,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MAC9F,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;KACvE,MAAK,IAAG+N,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MAC9F,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;KACzE,MAAK,IAAG+N,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MAC9F,OAAO,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;IAErE,OAAO,EAAE;EACb;EAEAqB,cAAcA,CAAC0M,KAAK;IAChB,IAAGA,KAAK,IAAI,CAAC,EAAC;MACV,OAAO,CAAC,KAAK,EAAG,cAAc,EAAE,YAAY,EAAE,YAAY,EAAC,cAAc,CAAC;KAC7E,MAAK,IAAGA,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MACzC;MACA,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;KACjF,MAAK,IAAGN,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,EAAC;MAC7C,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;KACjF,MAAK,IAAGF,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACE,SAAS,EAAC;MAC7C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;KACpF,MAAK,IAAGH,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACG,WAAW,EAAC;MAC/C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;KACpF,MAAK,IAAGJ,KAAK,IAAIzP,SAAS,CAAC0P,UAAU,CAACI,MAAM,EAAC;MAC1C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAC,cAAc,CAAC;KAC9E,MAAK,IAAGL,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;KAChF,MAAK,IAAGN,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;KAChF,MAAK,IAAGN,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGzP,SAAS,CAAC0P,UAAU,CAACK,KAAK,EAAC;MAC9F,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;;IAErF,OAAO,EAAE;EACb;EAEArM,cAAcA,CAAC+L,KAAK;IAChB,IAAGA,KAAK,IAAIzP,SAAS,CAAC2X,YAAY,CAACC,OAAO,EAAE,OAAO,IAAI,CAACnW,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,MACnG,IAAG+N,KAAK,IAAIzP,SAAS,CAAC2X,YAAY,CAACE,QAAQ,EAAE,OAAO,IAAI,CAACpW,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,MAC1G,OAAO,EAAE;EAClB;;;uBAnmCS+H,mBAAmB,EAAApJ,EAAA,CAAAyX,iBAAA,CA+ER/X,UAAU,GAAAM,EAAA,CAAAyX,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3X,EAAA,CAAAyX,iBAAA,CAEV7X,eAAe,GAAAI,EAAA,CAAAyX,iBAAA,CACf5X,eAAe,GAAAG,EAAA,CAAAyX,iBAAA,CACf3X,iBAAiB,GAAAE,EAAA,CAAAyX,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7X,EAAA,CAAAyX,iBAAA,CAAAzX,EAAA,CAAA8X,QAAA;IAAA;EAAA;;;YAnF5B1O,mBAAmB;MAAA2O,SAAA;MAAAC,QAAA,GAAAhY,EAAA,CAAAiY,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhCvY,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAwB,MAAA,GAAgD;UAAAxB,EAAA,CAAAc,YAAA,EAAM;UAC1Fd,EAAA,CAAAuD,SAAA,sBAAoF;UACxFvD,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,aAA0G;UAC5FD,EAAA,CAAAE,UAAA,mBAAAuY,uDAAA;YAAA,OAASD,GAAA,CAAAzC,kBAAA,EAAoB;UAAA,EAAC;UAA8K/V,EAAA,CAAAc,YAAA,EAAW;UACjOd,EAAA,CAAAC,cAAA,uBAAuM;UAA1BD,EAAA,CAAAE,UAAA,qBAAAwY,8DAAA;YAAA,OAAWF,GAAA,CAAAtK,UAAA,CAAW,CAAC,CAAC;UAAA,EAAC;UAAClO,EAAA,CAAAc,YAAA,EAAgB;UAK/Nd,EAAA,CAAAC,cAAA,cAAiG;UAA/DD,EAAA,CAAAE,UAAA,sBAAAyY,sDAAA;YAAA,OAAYH,GAAA,CAAArF,cAAA,EAAgB;UAAA,EAAC;UAC3DnT,EAAA,CAAAC,cAAA,iBAA4G;UAA/ED,EAAA,CAAAE,UAAA,6BAAA0Y,gEAAAjY,MAAA;YAAA,OAAmB6X,GAAA,CAAAjG,iBAAA,CAAA5R,MAAA,CAAyB;UAAA,EAAC;UACtEX,EAAA,CAAA2B,UAAA,KAAAkX,2CAAA,yBAEc;UACd7Y,EAAA,CAAA2B,UAAA,KAAAmX,2CAAA,0BAWc;UACd9Y,EAAA,CAAAC,cAAA,eAAgC;UAOZD,EAAA,CAAAE,UAAA,2BAAA6Y,6DAAApY,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAA3J,MAAA,GAAA7B,MAAA;UAAA,EAA+B;UAHvCX,EAAA,CAAAc,YAAA,EAKE;UACFd,EAAA,CAAAC,cAAA,iBAAwB;UAAAD,EAAA,CAAAwB,MAAA,IAAgD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAIxFd,EAAA,CAAAC,cAAA,eAAmB;UAIHD,EAAA,CAAAE,UAAA,2BAAA8Y,6DAAArY,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAtJ,IAAA,GAAAlC,MAAA;UAAA,EAA6B;UAFrCX,EAAA,CAAAc,YAAA,EAIE;UACFd,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAwB,MAAA,IAA2C;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAIjFd,EAAA,CAAAC,cAAA,eAAmB;UAIHD,EAAA,CAAAE,UAAA,2BAAA+Y,kEAAAtY,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAxJ,MAAA,GAAAhC,MAAA;UAAA,EAA+B;UAKtCX,EAAA,CAAAc,YAAA,EAAa;UACdd,EAAA,CAAAC,cAAA,iBAAoB;UAAAD,EAAA,CAAAwB,MAAA,IAAmD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAIvFd,EAAA,CAAAC,cAAA,eAAmB;UAIPD,EAAA,CAAAE,UAAA,yBAAAgZ,iEAAAvY,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAJ,UAAA,GAAApL,MAAA;UAAA,EAAiC;UAWpCX,EAAA,CAAAc,YAAA,EAAc;UAItBd,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAAiZ,6DAAAxY,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAApJ,KAAA,GAAApC,MAAA;UAAA,EAA8B;UAFtCX,EAAA,CAAAc,YAAA,EAIE;UACFd,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAwB,MAAA,IAA4C;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAInFd,EAAA,CAAAC,cAAA,eAAmB;UAIPD,EAAA,CAAAE,UAAA,yBAAAkZ,iEAAAzY,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAN,YAAA,GAAAlL,MAAA;UAAA,EAAmC;UAUtCX,EAAA,CAAAc,YAAA,EAAc;UAIvBd,EAAA,CAAAC,cAAA,eAAmB;UAIPD,EAAA,CAAAE,UAAA,yBAAAmZ,iEAAA1Y,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAxG,YAAA,GAAAhF,MAAA;UAAA,EAAmC;UAUtCX,EAAA,CAAAc,YAAA,EAAc;UAIvBd,EAAA,CAAAC,cAAA,eAAmB;UAIHD,EAAA,CAAAE,UAAA,2BAAAoZ,6DAAA3Y,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAL,UAAA,GAAAnL,MAAA;UAAA,EAAmC;UAF3CX,EAAA,CAAAc,YAAA,EAIE;UACFd,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAwB,MAAA,IAAsD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAIlGd,EAAA,CAAAC,cAAA,eAAmB;UAIPD,EAAA,CAAAE,UAAA,yBAAAqZ,iEAAA5Y,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAH,QAAA,GAAArL,MAAA;UAAA,EAA+B;UAUlCX,EAAA,CAAAc,YAAA,EAAc;UAGvBd,EAAA,CAAAC,cAAA,eAAwB;UAIRD,EAAA,CAAAE,UAAA,2BAAAsZ,kEAAA7Y,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAF,QAAA,GAAAtL,MAAA;UAAA,EAAiC,sBAAA8Y,6DAAA;YAAA,OAMrBjB,GAAA,CAAA/D,gBAAA,CAAA+D,GAAA,CAAArM,UAAA,CAAAF,QAAA,CAAqC;UAAA,EANhB,qBAAAyN,4DAAA;YAAA,OAOtBlB,GAAA,CAAA/D,gBAAA,CAAA+D,GAAA,CAAArM,UAAA,CAAAF,QAAA,CAAqC;UAAA,EAPf;UAQxCjM,EAAA,CAAAc,YAAA,EAAa;UACdd,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAwB,MAAA,IAAuD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAGjGd,EAAA,CAAAC,cAAA,eAAwB;UAIRD,EAAA,CAAAE,UAAA,2BAAAyZ,kEAAAhZ,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAD,MAAA,GAAAvL,MAAA;UAAA,EAA+B,sBAAAiZ,6DAAA;YAAA,OAOnBpB,GAAA,CAAA9D,cAAA,CAAA8D,GAAA,CAAArM,UAAA,CAAAD,MAAA,CAAiC;UAAA,EAPd,qBAAA2N,4DAAA;YAAA,OAQpBrB,GAAA,CAAA9D,cAAA,CAAA8D,GAAA,CAAArM,UAAA,CAAAD,MAAA,CAAiC;UAAA,EARb;UAFvClM,EAAA,CAAAc,YAAA,EAWE;UACFd,EAAA,CAAAC,cAAA,iBAAwB;UAAAD,EAAA,CAAAwB,MAAA,IAAwD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAGhGd,EAAA,CAAAC,cAAA,eAAmB;UAICD,EAAA,CAAAE,UAAA,2BAAA4Z,kEAAAnZ,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAC,OAAA,GAAAzL,MAAA;UAAA,EAAgC;UAK3CX,EAAA,CAAAc,YAAA,EAAa;UACdd,EAAA,CAAAC,cAAA,iBAAoB;UAAAD,EAAA,CAAAwB,MAAA,IAA8C;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAIlFd,EAAA,CAAAC,cAAA,eAA6E;UAK7DD,EAAA,CAAAE,UAAA,2BAAA6Z,kEAAApZ,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAjD,YAAA,GAAAvI,MAAA;UAAA,EAAqC;UAQhDX,EAAA,CAAAc,YAAA,EAAa;UACVd,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAwB,MAAA,IAAmD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAI7Fd,EAAA,CAAAC,cAAA,eAAmB;UAIPD,EAAA,CAAAE,UAAA,yBAAA8Z,iEAAArZ,MAAA;YAAA,OAAA6X,GAAA,CAAArM,UAAA,CAAAE,MAAA,GAAA1L,MAAA;UAAA,EAA6B;UAQhCX,EAAA,CAAAc,YAAA,EAAc;UAIvBd,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAAuD,SAAA,oBAGY;UAChBvD,EAAA,CAAAc,YAAA,EAAM;UAKlBd,EAAA,CAAAC,cAAA,sBAaC;UAVGD,EAAA,CAAAE,UAAA,+BAAA+Z,sEAAAtZ,MAAA;YAAA,OAAA6X,GAAA,CAAA7M,WAAA,GAAAhL,MAAA;UAAA,EAA6B;UAUhCX,EAAA,CAAAc,YAAA,EAAa;UAEdd,EAAA,CAAAC,cAAA,eAA2D;UACkBD,EAAA,CAAAE,UAAA,2BAAAga,gEAAAvZ,MAAA;YAAA,OAAA6X,GAAA,CAAApW,qBAAA,GAAAzB,MAAA;UAAA,EAAmC;UACxGX,EAAA,CAAAC,cAAA,eAA+B;UACqCD,EAAA,CAAAwB,MAAA,IAA8C;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UACtHd,EAAA,CAAAC,cAAA,eAAwD;UAGhDD,EAAA,CAAAE,UAAA,yBAAAia,iEAAAxZ,MAAA;YAAA,OAAA6X,GAAA,CAAA1F,gBAAA,GAAAnS,MAAA;UAAA,EAA4B;UAS/BX,EAAA,CAAAc,YAAA,EAAc;UAEnBd,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA2B,UAAA,KAAAyY,wCAAA,uBAE+I;UACnJpa,EAAA,CAAAc,YAAA,EAAM;UAEVd,EAAA,CAAAC,cAAA,eAAqE;UACwDD,EAAA,CAAAE,UAAA,mBAAAma,wDAAA;YAAA7B,GAAA,CAAApW,qBAAA,GAAiC,KAAK;YAAA,OAAAoW,GAAA,CAAA1F,gBAAA,GAAkB,IAAI;UAAA,EAAC;UAAC9S,EAAA,CAAAc,YAAA,EAAW;UAClMd,EAAA,CAAAC,cAAA,oBAA4L;UAAjGD,EAAA,CAAAE,UAAA,mBAAAoa,wDAAA;YAAA,OAAS9B,GAAA,CAAA7D,YAAA,CAAa,CAAC,CAAC;UAAA,EAAC;UAAwE3U,EAAA,CAAAc,YAAA,EAAW;UAMnNd,EAAA,CAAAC,cAAA,eAAqD;UACjDD,EAAA,CAAA2B,UAAA,KAAA4Y,wCAAA,yBAoKW;UACfva,EAAA,CAAAc,YAAA,EAAM;UAENd,EAAA,CAAAC,cAAA,eAA6D;UACiCD,EAAA,CAAAE,UAAA,2BAAAsa,gEAAA7Z,MAAA;YAAA,OAAA6X,GAAA,CAAArW,uBAAA,GAAAxB,MAAA;UAAA,EAAqC;UAC3HX,EAAA,CAAAC,cAAA,gBAAoE;UAA7BD,EAAA,CAAAE,UAAA,sBAAAua,uDAAA;YAAA,OAAYjC,GAAA,CAAA7D,YAAA,CAAa,CAAC,CAAC;UAAA,EAAC;UAE/D3U,EAAA,CAAAC,cAAA,eAA+B;UACqCD,EAAA,CAAAwB,MAAA,IAAsD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAC9Hd,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAA2B,UAAA,KAAA+Y,oCAAA,mBAAkH;UAClH1a,EAAA,CAAA2B,UAAA,KAAAgZ,oCAAA,mBAAwH;UACxH3a,EAAA,CAAA2B,UAAA,KAAAiZ,oCAAA,mBAAwH;UAC5H5a,EAAA,CAAAc,YAAA,EAAM;UAGVd,EAAA,CAAAC,cAAA,gBAA+B;UACqCD,EAAA,CAAAwB,MAAA,KAA+C;UAAAxB,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAwB,MAAA,UAAC;UAAAxB,EAAA,CAAAc,YAAA,EAAO;UAClJd,EAAA,CAAAC,cAAA,gBAAiB;UAGLD,EAAA,CAAAE,UAAA,2BAAA2a,8DAAAla,MAAA;YAAA,OAAA6X,GAAA,CAAAjM,kBAAA,CAAAC,QAAA,GAAA7L,MAAA;UAAA,EAAyC,mBAAAma,sDAAA;YAAA,OAMhCtC,GAAA,CAAA1D,kBAAA,EAAoB;UAAA,EANY;UAFjD9U,EAAA,CAAAc,YAAA,EASE;UAGVd,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAAuD,SAAA,kBAAyE;UACzEvD,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAA2B,UAAA,MAAAoZ,sCAAA,oBAAoM;UACpM/a,EAAA,CAAA2B,UAAA,MAAAqZ,sCAAA,oBAAiK;UACjKhb,EAAA,CAAA2B,UAAA,MAAAsZ,sCAAA,oBAAuJ;UACvJjb,EAAA,CAAA2B,UAAA,MAAAuZ,sCAAA,oBAA0O;UAC9Olb,EAAA,CAAAc,YAAA,EAAM;UAGVd,EAAA,CAAAC,cAAA,gBAA+B;UACiCD,EAAA,CAAAwB,MAAA,KAAgD;UAAAxB,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAwB,MAAA,UAAC;UAAAxB,EAAA,CAAAc,YAAA,EAAO;UAC/Id,EAAA,CAAAC,cAAA,gBAAiB;UAGLD,EAAA,CAAAE,UAAA,2BAAAib,8DAAAxa,MAAA;YAAA,OAAA6X,GAAA,CAAAjM,kBAAA,CAAAhG,IAAA,GAAA5F,MAAA;UAAA,EAAqC;UAF7CX,EAAA,CAAAc,YAAA,EAQE;UAGVd,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAAuD,SAAA,kBAAoE;UACpEvD,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAA2B,UAAA,MAAAyZ,sCAAA,oBAA4L;UAC5Lpb,EAAA,CAAA2B,UAAA,MAAA0Z,sCAAA,oBAA8J;UAC9Jrb,EAAA,CAAA2B,UAAA,MAAA2Z,sCAAA,oBAAwJ;UAC5Jtb,EAAA,CAAAc,YAAA,EAAM;UAGVd,EAAA,CAAA2B,UAAA,MAAA4Z,oCAAA,kBAKM;UAENvb,EAAA,CAAA2B,UAAA,MAAA6Z,oCAAA,kBAKM;UAENxb,EAAA,CAAAC,cAAA,gBAA+B;UACwCD,EAAA,CAAAwB,MAAA,KAAkD;UAAAxB,EAAA,CAAAc,YAAA,EAAQ;UAC7Hd,EAAA,CAAAC,cAAA,gBAAiB;UAKLD,EAAA,CAAAE,UAAA,2BAAAub,iEAAA9a,MAAA;YAAA,OAAA6X,GAAA,CAAAjM,kBAAA,CAAAE,WAAA,GAAA9L,MAAA;UAAA,EAA4C;UAInDX,EAAA,CAAAc,YAAA,EAAW;UAGpBd,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAAuD,SAAA,kBAAyE;UACzEvD,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAA2B,UAAA,MAAA+Z,sCAAA,oBAAqK;UACzK1b,EAAA,CAAAc,YAAA,EAAM;UAEVd,EAAA,CAAAC,cAAA,gBAAqE;UACwDD,EAAA,CAAAE,UAAA,mBAAAyb,yDAAA;YAAA,OAAAnD,GAAA,CAAArW,uBAAA,GAAmC,KAAK;UAAA,EAAC;UAACnC,EAAA,CAAAc,YAAA,EAAW;UAC9Kd,EAAA,CAAAuD,SAAA,qBAAgL;UACpLvD,EAAA,CAAAc,YAAA,EAAM;UAIdd,EAAA,CAAAC,cAAA,gBAAyC;UAC6BD,EAAA,CAAAE,UAAA,2BAAA0b,iEAAAjb,MAAA;YAAA,OAAA6X,GAAA,CAAAjO,oBAAA,GAAA5J,MAAA;UAAA,EAAkC;UAChGX,EAAA,CAAAC,cAAA,gBAAqE;UAC9DD,EAAA,CAAAwB,MAAA,KAA+C;UAAAxB,EAAA,CAAAc,YAAA,EAAI;UAE1Dd,EAAA,CAAAC,cAAA,gBAA0E;UACiCD,EAAA,CAAAE,UAAA,mBAAA2b,yDAAA;YAAA,OAAArD,GAAA,CAAAjO,oBAAA,GAAgC,KAAK;UAAA,EAAC;UAACvK,EAAA,CAAAc,YAAA,EAAW;UACzJd,EAAA,CAAAC,cAAA,qBAAmH;UAAvBD,EAAA,CAAAE,UAAA,mBAAA4b,yDAAA;YAAA,OAAStD,GAAA,CAAA7C,SAAA,EAAW;UAAA,EAAC;UAAE3V,EAAA,CAAAc,YAAA,EAAW;;;UA/jBlGd,EAAA,CAAAiB,SAAA,GAAgD;UAAhDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,wBAAgD;UAC7CrB,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAA3L,KAAA,CAAe,SAAA2L,GAAA,CAAAxJ,IAAA;UAGmDhP,EAAA,CAAAiB,SAAA,GAAyD;UAAzDjB,EAAA,CAAA+b,qBAAA,UAAAvD,GAAA,CAAApX,WAAA,CAAAC,SAAA,yBAAyD;UAAzHrB,EAAA,CAAAkB,UAAA,aAAAsX,GAAA,CAAA7M,WAAA,CAAA9D,MAAA,OAAqC;UAC/B7H,EAAA,CAAAiB,SAAA,GAAuD;UAAvDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,yBAAuD,UAAAmX,GAAA,CAAAzL,WAAA;UAKxG/M,EAAA,CAAAiB,SAAA,GAA2B;UAA3BjB,EAAA,CAAAkB,UAAA,cAAAsX,GAAA,CAAA9L,aAAA,CAA2B;UACpB1M,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,oBAAmB,cAAAsX,GAAA,CAAAzW,kBAAA;UAuBJ/B,EAAA,CAAAiB,SAAA,GAA+B;UAA/BjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAArM,UAAA,CAAA3J,MAAA,CAA+B;UAGfxC,EAAA,CAAAiB,SAAA,GAAgD;UAAhDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,wBAAgD;UAQhErB,EAAA,CAAAiB,SAAA,GAA6B;UAA7BjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAArM,UAAA,CAAAtJ,IAAA,CAA6B;UAGf7C,EAAA,CAAAiB,SAAA,GAA2C;UAA3CjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,mBAA2C;UAMjCrB,EAAA,CAAAiB,SAAA,GAAkB;UAAlBjB,EAAA,CAAAkB,UAAA,mBAAkB,uCAAAsX,GAAA,CAAArM,UAAA,CAAAxJ,MAAA,aAAA6V,GAAA,CAAArJ,SAAA;UAQ9BnP,EAAA,CAAAiB,SAAA,GAAmD;UAAnDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,2BAAmD;UAQnErB,EAAA,CAAAiB,SAAA,GAAiC;UAAjCjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAArM,UAAA,CAAAJ,UAAA,CAAiC,gBAAAyM,GAAA,CAAApX,WAAA,CAAAC,SAAA,2DAAArB,EAAA,CAAAsB,eAAA,MAAA0a,GAAA,6BAAAhc,EAAA,CAAAsB,eAAA,MAAA2a,GAAA;UAmB7Bjc,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAArM,UAAA,CAAApJ,KAAA,CAA8B;UAGf/C,EAAA,CAAAiB,SAAA,GAA4C;UAA5CjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,oBAA4C;UAQ/DrB,EAAA,CAAAiB,SAAA,GAAmC;UAAnCjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAArM,UAAA,CAAAN,YAAA,CAAmC,gBAAA2M,GAAA,CAAApX,WAAA,CAAAC,SAAA,uCAAArB,EAAA,CAAAsB,eAAA,MAAA4a,GAAA;UAkBnClc,EAAA,CAAAiB,SAAA,GAAmC;UAAnCjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAArM,UAAA,CAAAxG,YAAA,CAAmC,gBAAA6S,GAAA,CAAApX,WAAA,CAAAC,SAAA,yCAAArB,EAAA,CAAAsB,eAAA,MAAA6a,GAAA;UAkB/Bnc,EAAA,CAAAiB,SAAA,GAAmC;UAAnCjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAArM,UAAA,CAAAL,UAAA,CAAmC;UAGf9L,EAAA,CAAAiB,SAAA,GAAsD;UAAtDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,8BAAsD;UAQ9ErB,EAAA,CAAAiB,SAAA,GAA+B;UAA/BjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAArM,UAAA,CAAAH,QAAA,CAA+B,gBAAAwM,GAAA,CAAApX,WAAA,CAAAC,SAAA,yCAAArB,EAAA,CAAAsB,eAAA,MAAA8a,GAAA;UAiB3Bpc,EAAA,CAAAiB,SAAA,GAAiC;UAAjCjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAArM,UAAA,CAAAF,QAAA,CAAiC,iDAAAuM,GAAA,CAAA3O,WAAA;UASf7J,EAAA,CAAAiB,SAAA,GAAuD;UAAvDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,+BAAuD;UAOzErB,EAAA,CAAAiB,SAAA,GAA+B;UAA/BjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAArM,UAAA,CAAAD,MAAA,CAA+B,iDAAAsM,GAAA,CAAAzO,SAAA,aAAAyO,GAAA,CAAAxO,SAAA;UAUfhK,EAAA,CAAAiB,SAAA,GAAwD;UAAxDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,gCAAwD;UAKhDrB,EAAA,CAAAiB,SAAA,GAAkB;UAAlBjB,EAAA,CAAAkB,UAAA,mBAAkB,uCAAAsX,GAAA,CAAArM,UAAA,CAAAC,OAAA,aAAAoM,GAAA,CAAA7I,QAAA;UAQ9B3P,EAAA,CAAAiB,SAAA,GAA8C;UAA9CjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,sBAA8C;UAIvDrB,EAAA,CAAAiB,SAAA,GAAyD;UAAzDjB,EAAA,CAAAyC,UAAA,CAAA+V,GAAA,CAAAnN,QAAA,IAAAmN,GAAA,CAAA/M,cAAA,CAAA8J,KAAA,iBAAyD;UAG5DvV,EAAA,CAAAiB,SAAA,GAAkB;UAAlBjB,EAAA,CAAAkB,UAAA,mBAAkB,uCAAAsX,GAAA,CAAArM,UAAA,CAAAjD,YAAA,aAAAsP,GAAA,CAAAlO,YAAA,iCAAAkO,GAAA,CAAApX,WAAA,CAAAC,SAAA,uDAAAmX,GAAA,CAAApX,WAAA,CAAAC,SAAA;UAWArB,EAAA,CAAAiB,SAAA,GAAmD;UAAnDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,2BAAmD;UAQzErB,EAAA,CAAAiB,SAAA,GAA6B;UAA7BjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAArM,UAAA,CAAAE,MAAA,CAA6B,gBAAAmM,GAAA,CAAApX,WAAA,CAAAC,SAAA;UAuBjDrB,EAAA,CAAAiB,SAAA,GAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,2BAA0B,qCAAAsX,GAAA,CAAA7M,WAAA,aAAA6M,GAAA,CAAAxI,OAAA,aAAAwI,GAAA,CAAAvG,OAAA,aAAAuG,GAAA,CAAApH,WAAA,cAAAoH,GAAA,CAAApG,MAAA,CAAA8C,IAAA,CAAAsD,GAAA,iBAAAA,GAAA,CAAA/G,UAAA,cAAA+G,GAAA,CAAA9G,QAAA,UAAA8G,GAAA,CAAA1K,IAAA,YAAA0K,GAAA,CAAArM,UAAA,gBAAAqM,GAAA,CAAApX,WAAA,CAAAC,SAAA;UAekGrB,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAwH,UAAA,CAAAxH,EAAA,CAAAsB,eAAA,MAAA+a,IAAA,EAA4B;UAA9Irc,EAAA,CAAAkB,UAAA,WAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,+BAA8D,YAAAmX,GAAA,CAAApW,qBAAA;UAEApC,EAAA,CAAAiB,SAAA,GAA8C;UAA9CjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,sBAA8C;UAItGrB,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAA1F,gBAAA,CAA4B,gBAAA0F,GAAA,CAAApX,WAAA,CAAAC,SAAA,qEAAAmX,GAAA,CAAAnO,mBAAA;UAYrBrK,EAAA,CAAAiB,SAAA,GAAoD;UAApDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA8D,WAAA,CAAAtc,EAAA,CAAAsI,eAAA,MAAAiU,IAAA,EAAA/D,GAAA,CAAArO,cAAA,CAAAqS,SAAA,CAAAC,MAAA,GAAoD;UAMFzc,EAAA,CAAAiB,SAAA,GAAuD;UAAvDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,yBAAuD;UACnFrB,EAAA,CAAAiB,SAAA,GAAqD;UAArDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,uBAAqD,aAAAmX,GAAA,CAAA1F,gBAAA,YAAA0F,GAAA,CAAA1F,gBAAA,IAAA/E,SAAA;UAOuF/N,EAAA,CAAAiB,SAAA,GAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA3R,oBAAA,CAA0B;UAwKpE7G,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAwH,UAAA,CAAAxH,EAAA,CAAAsB,eAAA,MAAA+a,IAAA,EAA4B;UAAjKrc,EAAA,CAAAkB,UAAA,WAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,gDAA+E,YAAAmX,GAAA,CAAArW,uBAAA;UAC/EnC,EAAA,CAAAiB,SAAA,GAAgC;UAAhCjB,EAAA,CAAAkB,UAAA,cAAAsX,GAAA,CAAA5L,kBAAA,CAAgC;UAGkC5M,EAAA,CAAAiB,SAAA,GAAsD;UAAtDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,8BAAsD;UAE3GrB,EAAA,CAAAiB,SAAA,GAAiD;UAAjDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA/J,UAAA,IAAA+J,GAAA,CAAAvO,iBAAA,CAAAuE,WAAA,CAAiD;UACjDxO,EAAA,CAAAiB,SAAA,GAAoD;UAApDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA/J,UAAA,IAAA+J,GAAA,CAAAvO,iBAAA,CAAA2E,cAAA,CAAoD;UACpD5O,EAAA,CAAAiB,SAAA,GAAoD;UAApDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA/J,UAAA,IAAA+J,GAAA,CAAAvO,iBAAA,CAAA8E,cAAA,CAAoD;UAKC/O,EAAA,CAAAiB,SAAA,GAA+C;UAA/CjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,uBAA+C;UAInGrB,EAAA,CAAAiB,SAAA,GAAyC;UAAzCjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAAjM,kBAAA,CAAAC,QAAA,CAAyC,mDAAAgM,GAAA,CAAApX,WAAA,CAAAC,SAAA;UAapBrB,EAAA,CAAAiB,SAAA,GAAyG;UAAzGjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAmQ,KAAA,KAAAnE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,CAAAC,QAAA,EAAyG;UACzG7c,EAAA,CAAAiB,SAAA,GAA4D;UAA5DjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,CAAAE,SAAA,CAA4D;UAC5D9c,EAAA,CAAAiB,SAAA,GAA0D;UAA1DjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,CAAAG,OAAA,CAA0D;UAC1D/c,EAAA,CAAAiB,SAAA,GAAgF;UAAhFjB,EAAA,CAAAkB,UAAA,WAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAlQ,QAAA,CAAAoQ,MAAA,CAAAC,QAAA,KAAArE,GAAA,CAAApD,gBAAA,CAAgF;UAKrDpV,EAAA,CAAAiB,SAAA,GAAgD;UAAhDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,wBAAgD;UAIhGrB,EAAA,CAAAiB,SAAA,GAAqC;UAArCjB,EAAA,CAAAkB,UAAA,YAAAsX,GAAA,CAAAjM,kBAAA,CAAAhG,IAAA,CAAqC,oDAAAiS,GAAA,CAAApX,WAAA,CAAAC,SAAA;UAYhBrB,EAAA,CAAAiB,SAAA,GAAiG;UAAjGjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAoW,KAAA,KAAAnE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAqW,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAqW,MAAA,CAAAC,QAAA,EAAiG;UACjG7c,EAAA,CAAAiB,SAAA,GAAwD;UAAxDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAqW,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAqW,MAAA,CAAAE,SAAA,CAAwD;UACxD9c,EAAA,CAAAiB,SAAA,GAAsD;UAAtDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAqW,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAnW,IAAA,CAAAqW,MAAA,CAAAG,OAAA,CAAsD;UAI3D/c,EAAA,CAAAiB,SAAA,GAAoD;UAApDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA/J,UAAA,IAAA+J,GAAA,CAAAvO,iBAAA,CAAA8E,cAAA,CAAoD;UAOpD/O,EAAA,CAAAiB,SAAA,GAAoD;UAApDjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA/J,UAAA,IAAA+J,GAAA,CAAAvO,iBAAA,CAAA2E,cAAA,CAAoD;UAQb5O,EAAA,CAAAiB,SAAA,GAAkD;UAAlDjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,0BAAkD;UAIzGrB,EAAA,CAAAiB,SAAA,GAAoB;UAApBjB,EAAA,CAAAkB,UAAA,qBAAoB,YAAAsX,GAAA,CAAAjM,kBAAA,CAAAE,WAAA,iBAAA+L,GAAA,CAAApX,WAAA,CAAAC,SAAA;UAYCrB,EAAA,CAAAiB,SAAA,GAA+D;UAA/DjB,EAAA,CAAAkB,UAAA,SAAAsX,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAjQ,WAAA,CAAAmQ,MAAA,kBAAApE,GAAA,CAAA5L,kBAAA,CAAA8P,QAAA,CAAAjQ,WAAA,CAAAmQ,MAAA,CAAAE,SAAA,CAA+D;UAI/B9c,EAAA,CAAAiB,SAAA,GAAuD;UAAvDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,yBAAuD;UACnFrB,EAAA,CAAAiB,SAAA,GAAqD;UAArDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,uBAAqD,aAAAmX,GAAA,CAAA5L,kBAAA,CAAAoQ,OAAA,IAAAxE,GAAA,CAAApD,gBAAA;UAMkBpV,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAwH,UAAA,CAAAxH,EAAA,CAAAsB,eAAA,MAAA+a,IAAA,EAA4B;UAAtIrc,EAAA,CAAAkB,UAAA,WAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,wBAAuD,YAAAmX,GAAA,CAAAjO,oBAAA;UAEtDvK,EAAA,CAAAiB,SAAA,GAA+C;UAA/CjB,EAAA,CAAAyB,iBAAA,CAAA+W,GAAA,CAAApX,WAAA,CAAAC,SAAA,uBAA+C;UAGHrB,EAAA,CAAAiB,SAAA,GAAuD;UAAvDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,yBAAuD;UACjErB,EAAA,CAAAiB,SAAA,GAAsD;UAAtDjB,EAAA,CAAAkB,UAAA,UAAAsX,GAAA,CAAApX,WAAA,CAAAC,SAAA,wBAAsD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}