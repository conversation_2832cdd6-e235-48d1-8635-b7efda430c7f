{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Mongolian [mn]\n//! author : Javkhlantugs Nyamdorj : https://github.com/javkhaanj7\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function translate(number, withoutSuffix, key, isFuture) {\n    switch (key) {\n      case 's':\n        return withoutSuffix ? 'хэдхэн секунд' : 'хэдхэн секундын';\n      case 'ss':\n        return number + (withoutSuffix ? ' секунд' : ' секундын');\n      case 'm':\n      case 'mm':\n        return number + (withoutSuffix ? ' минут' : ' минутын');\n      case 'h':\n      case 'hh':\n        return number + (withoutSuffix ? ' цаг' : ' цагийн');\n      case 'd':\n      case 'dd':\n        return number + (withoutSuffix ? ' өдөр' : ' өдрийн');\n      case 'M':\n      case 'MM':\n        return number + (withoutSuffix ? ' сар' : ' сарын');\n      case 'y':\n      case 'yy':\n        return number + (withoutSuffix ? ' жил' : ' жилийн');\n      default:\n        return number;\n    }\n  }\n  var mn = moment.defineLocale('mn', {\n    months: 'Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар'.split('_'),\n    monthsShort: '1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба'.split('_'),\n    weekdaysShort: 'Ням_Дав_Мяг_Лха_Пүр_Баа_Бям'.split('_'),\n    weekdaysMin: 'Ня_Да_Мя_Лх_Пү_Ба_Бя'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY оны MMMMын D',\n      LLL: 'YYYY оны MMMMын D HH:mm',\n      LLLL: 'dddd, YYYY оны MMMMын D HH:mm'\n    },\n    meridiemParse: /ҮӨ|ҮХ/i,\n    isPM: function (input) {\n      return input === 'ҮХ';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ҮӨ';\n      } else {\n        return 'ҮХ';\n      }\n    },\n    calendar: {\n      sameDay: '[Өнөөдөр] LT',\n      nextDay: '[Маргааш] LT',\n      nextWeek: '[Ирэх] dddd LT',\n      lastDay: '[Өчигдөр] LT',\n      lastWeek: '[Өнгөрсөн] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s дараа',\n      past: '%s өмнө',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2} өдөр/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + ' өдөр';\n        default:\n          return number;\n      }\n    }\n  });\n  return mn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "translate", "number", "withoutSuffix", "key", "isFuture", "mn", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "period"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/moment/locale/mn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Mongolian [mn]\n//! author : Javkhlantugs Nyamdorj : https://github.com/javkhaanj7\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function translate(number, withoutSuffix, key, isFuture) {\n        switch (key) {\n            case 's':\n                return withoutSuffix ? 'хэдхэн секунд' : 'хэдхэн секундын';\n            case 'ss':\n                return number + (withoutSuffix ? ' секунд' : ' секундын');\n            case 'm':\n            case 'mm':\n                return number + (withoutSuffix ? ' минут' : ' минутын');\n            case 'h':\n            case 'hh':\n                return number + (withoutSuffix ? ' цаг' : ' цагийн');\n            case 'd':\n            case 'dd':\n                return number + (withoutSuffix ? ' өдөр' : ' өдрийн');\n            case 'M':\n            case 'MM':\n                return number + (withoutSuffix ? ' сар' : ' сарын');\n            case 'y':\n            case 'yy':\n                return number + (withoutSuffix ? ' жил' : ' жилийн');\n            default:\n                return number;\n        }\n    }\n\n    var mn = moment.defineLocale('mn', {\n        months: 'Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар'.split(\n            '_'\n        ),\n        monthsShort:\n            '1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба'.split('_'),\n        weekdaysShort: 'Ням_Дав_Мяг_Лха_Пүр_Баа_Бям'.split('_'),\n        weekdaysMin: 'Ня_Да_Мя_Лх_Пү_Ба_Бя'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY-MM-DD',\n            LL: 'YYYY оны MMMMын D',\n            LLL: 'YYYY оны MMMMын D HH:mm',\n            LLLL: 'dddd, YYYY оны MMMMын D HH:mm',\n        },\n        meridiemParse: /ҮӨ|ҮХ/i,\n        isPM: function (input) {\n            return input === 'ҮХ';\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ҮӨ';\n            } else {\n                return 'ҮХ';\n            }\n        },\n        calendar: {\n            sameDay: '[Өнөөдөр] LT',\n            nextDay: '[Маргааш] LT',\n            nextWeek: '[Ирэх] dddd LT',\n            lastDay: '[Өчигдөр] LT',\n            lastWeek: '[Өнгөрсөн] dddd LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s дараа',\n            past: '%s өмнө',\n            s: translate,\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: translate,\n            hh: translate,\n            d: translate,\n            dd: translate,\n            M: translate,\n            MM: translate,\n            y: translate,\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2} өдөр/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'd':\n                case 'D':\n                case 'DDD':\n                    return number + ' өдөр';\n                default:\n                    return number;\n            }\n        },\n    });\n\n    return mn;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACrD,QAAQD,GAAG;MACP,KAAK,GAAG;QACJ,OAAOD,aAAa,GAAG,eAAe,GAAG,iBAAiB;MAC9D,KAAK,IAAI;QACL,OAAOD,MAAM,IAAIC,aAAa,GAAG,SAAS,GAAG,WAAW,CAAC;MAC7D,KAAK,GAAG;MACR,KAAK,IAAI;QACL,OAAOD,MAAM,IAAIC,aAAa,GAAG,QAAQ,GAAG,UAAU,CAAC;MAC3D,KAAK,GAAG;MACR,KAAK,IAAI;QACL,OAAOD,MAAM,IAAIC,aAAa,GAAG,MAAM,GAAG,SAAS,CAAC;MACxD,KAAK,GAAG;MACR,KAAK,IAAI;QACL,OAAOD,MAAM,IAAIC,aAAa,GAAG,OAAO,GAAG,SAAS,CAAC;MACzD,KAAK,GAAG;MACR,KAAK,IAAI;QACL,OAAOD,MAAM,IAAIC,aAAa,GAAG,MAAM,GAAG,QAAQ,CAAC;MACvD,KAAK,GAAG;MACR,KAAK,IAAI;QACL,OAAOD,MAAM,IAAIC,aAAa,GAAG,MAAM,GAAG,SAAS,CAAC;MACxD;QACI,OAAOD,MAAM;IACrB;EACJ;EAEA,IAAII,EAAE,GAAGN,MAAM,CAACO,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,8LAA8L,CAACC,KAAK,CACxM,GACJ,CAAC;IACDC,WAAW,EACP,4EAA4E,CAACD,KAAK,CAC9E,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,4CAA4C,CAACH,KAAK,CAAC,GAAG,CAAC;IACjEI,aAAa,EAAE,6BAA6B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,mBAAmB;MACvBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,IAAI;IACzB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAEvC,SAAS;MACZwC,EAAE,EAAExC,SAAS;MACbyC,CAAC,EAAEzC,SAAS;MACZ0C,EAAE,EAAE1C,SAAS;MACb2C,CAAC,EAAE3C,SAAS;MACZ4C,EAAE,EAAE5C,SAAS;MACb6C,CAAC,EAAE7C,SAAS;MACZ8C,EAAE,EAAE9C,SAAS;MACb+C,CAAC,EAAE/C,SAAS;MACZgD,EAAE,EAAEhD,SAAS;MACbiD,CAAC,EAAEjD,SAAS;MACZkD,EAAE,EAAElD;IACR,CAAC;IACDmD,sBAAsB,EAAE,cAAc;IACtCC,OAAO,EAAE,SAAAA,CAAUnD,MAAM,EAAEoD,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;UACN,OAAOpD,MAAM,GAAG,OAAO;QAC3B;UACI,OAAOA,MAAM;MACrB;IACJ;EACJ,CAAC,CAAC;EAEF,OAAOI,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}