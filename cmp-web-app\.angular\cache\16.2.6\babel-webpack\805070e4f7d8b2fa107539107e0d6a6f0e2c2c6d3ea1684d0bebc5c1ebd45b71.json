{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LandingComponent } from './landing.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class LandingRoutingModule {\n  static {\n    this.ɵfac = function LandingRoutingModule_Factory(t) {\n      return new (t || LandingRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LandingRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: '',\n        component: LandingComponent\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LandingRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LandingComponent", "LandingRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\landing\\landing-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { LandingComponent } from './landing.component';\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild([\r\n        { path: '', component: LandingComponent }\r\n    ])],\r\n    exports: [RouterModule]\r\n})\r\nexport class LandingRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,qBAAqB;;;AAQtD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBALnBF,YAAY,CAACG,QAAQ,CAAC,CAC5B;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAgB,CAAE,CAC5C,CAAC,EACQD,YAAY;IAAA;EAAA;;;2EAEbE,oBAAoB;IAAAI,OAAA,GAAAC,EAAA,CAAAP,YAAA;IAAAQ,OAAA,GAFnBR,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}