{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\nconst _c0 = [\"content\"];\nconst _c1 = [\"navbar\"];\nconst _c2 = [\"inkbar\"];\nconst _c3 = [\"prevBtn\"];\nconst _c4 = [\"nextBtn\"];\nfunction TabMenu_button_2_ChevronLeftIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\");\n  }\n}\nfunction TabMenu_button_2_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11, 12);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.navBackward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_2_ChevronLeftIcon_2_Template, 1, 0, \"ChevronLeftIcon\", 13);\n    i0.ɵɵtemplate(3, TabMenu_button_2_3_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.previousIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.previousIconTemplate);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.icon)(\"ngStyle\", item_r12.iconStyle);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.label);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 26);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r12.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_1_ng_container_1_span_1_Template, 1, 2, \"span\", 20);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_1_ng_container_1_span_2_Template, 2, 1, \"span\", 21);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_1_ng_container_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, TabMenu_li_7_a_1_ng_container_1_span_5_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(4);\n    const item_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.escape !== false)(\"ngIfElse\", _r20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    index: a1\n  };\n};\nfunction TabMenu_li_7_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function TabMenu_li_7_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.itemClick($event, item_r12));\n    })(\"keydown.enter\", function TabMenu_li_7_a_1_Template_a_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.itemClick($event, item_r12));\n    });\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_1_ng_container_1_Template, 6, 4, \"ng-container\", 13);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_1_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    const item_r12 = ctx_r33.$implicit;\n    const i_r13 = ctx_r33.index;\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", item_r12.target);\n    i0.ɵɵattribute(\"href\", item_r12.url, i0.ɵɵsanitizeUrl)(\"tabindex\", item_r12.disabled ? null : \"0\")(\"title\", item_r12.title)(\"id\", item_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r14.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r14.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(8, _c5, item_r12, i_r13));\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.icon)(\"ngStyle\", item_r12.iconStyle);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.label);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 26);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r12.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_2_ng_container_1_span_1_Template, 1, 2, \"span\", 20);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_ng_container_1_span_2_Template, 2, 1, \"span\", 21);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_2_ng_container_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, TabMenu_li_7_a_2_ng_container_1_span_5_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r38 = i0.ɵɵreference(4);\n    const item_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.escape !== false)(\"ngIfElse\", _r38);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c6 = function () {\n  return {\n    exact: false\n  };\n};\nfunction TabMenu_li_7_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function TabMenu_li_7_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.itemClick($event, item_r12));\n    })(\"keydown.enter\", function TabMenu_li_7_a_2_Template_a_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.itemClick($event, item_r12));\n    });\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_2_ng_container_1_Template, 6, 4, \"ng-container\", 13);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext();\n    const item_r12 = ctx_r51.$implicit;\n    const i_r13 = ctx_r51.index;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r12.routerLink)(\"queryParams\", item_r12.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r12.routerLinkActiveOptions || i0.ɵɵpureFunction0(17, _c6))(\"target\", item_r12.target)(\"fragment\", item_r12.fragment)(\"queryParamsHandling\", item_r12.queryParamsHandling)(\"preserveFragment\", item_r12.preserveFragment)(\"skipLocationChange\", item_r12.skipLocationChange)(\"replaceUrl\", item_r12.replaceUrl)(\"state\", item_r12.state);\n    i0.ɵɵattribute(\"tabindex\", item_r12.disabled ? null : \"0\")(\"title\", item_r12.title)(\"id\", item_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r15.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(18, _c5, item_r12, i_r13));\n  }\n}\nconst _c7 = function (a1, a2, a3) {\n  return {\n    \"p-tabmenuitem\": true,\n    \"p-disabled\": a1,\n    \"p-highlight\": a2,\n    \"p-hidden\": a3\n  };\n};\nfunction TabMenu_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 15);\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_1_Template, 3, 11, \"a\", 16);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_Template, 3, 21, \"a\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(item_r12.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r12.style)(\"ngClass\", i0.ɵɵpureFunction3(9, _c7, item_r12.disabled, ctx_r3.isActive(item_r12), item_r12.visible === false))(\"tooltipOptions\", item_r12.tooltipOptions);\n    i0.ɵɵattribute(\"aria-selected\", ctx_r3.isActive(item_r12))(\"aria-expanded\", ctx_r3.isActive(item_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r12.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.routerLink);\n  }\n}\nfunction TabMenu_button_10_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n}\nfunction TabMenu_button_10_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_10_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_10_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30, 31);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.navForward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_10_ChevronRightIcon_2_Template, 1, 0, \"ChevronRightIcon\", 13);\n    i0.ɵɵtemplate(3, TabMenu_button_10_3_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.previousIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r5.nextIconTemplate);\n  }\n}\nconst _c8 = function (a1) {\n  return {\n    \"p-tabmenu p-component\": true,\n    \"p-tabmenu-scrollable\": a1\n  };\n};\nclass TabMenu {\n  platformId;\n  router;\n  route;\n  cd;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  model;\n  /**\n   * Defines the default active menuitem\n   * @group Props\n   */\n  activeItem;\n  /**\n   * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n   * @group Props\n   */\n  scrollable;\n  /**\n   * Defines if popup mode enabled.\n   */\n  popup;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Event fired when a tab is selected.\n   * @param {MenuItem} item - Menu item.\n   * @group Emits\n   */\n  activeItemChange = new EventEmitter();\n  content;\n  navbar;\n  inkbar;\n  prevBtn;\n  nextBtn;\n  templates;\n  itemTemplate;\n  previousIconTemplate;\n  nextIconTemplate;\n  tabChanged;\n  backwardIsDisabled = true;\n  forwardIsDisabled = false;\n  timerIdForInitialAutoScroll = null;\n  constructor(platformId, router, route, cd) {\n    this.platformId = platformId;\n    this.router = router;\n    this.route = route;\n    this.cd = cd;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'nexticon':\n          this.nextIconTemplate = item.template;\n          break;\n        case 'previousicon':\n          this.previousIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.updateInkBar();\n      this.initAutoScrollForActiveItem();\n      this.initButtonState();\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.tabChanged) {\n      this.updateInkBar();\n      this.tabChanged = false;\n    }\n  }\n  ngOnDestroy() {\n    this.clearAutoScrollHandler();\n  }\n  isActive(item) {\n    if (item.routerLink) {\n      const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n      return this.router.isActive(this.router.createUrlTree(routerLink, {\n        relativeTo: this.route\n      }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n    }\n    return item === this.activeItem;\n  }\n  itemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n    this.activeItem = item;\n    this.activeItemChange.emit(item);\n    this.tabChanged = true;\n    this.cd.markForCheck();\n  }\n  updateInkBar() {\n    const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n    if (tabHeader) {\n      this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n      this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n    }\n  }\n  getVisibleButtonWidths() {\n    return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => el ? acc + DomHandler.getWidth(el) : acc, 0);\n  }\n  updateButtonState() {\n    const content = this.content?.nativeElement;\n    const {\n      scrollLeft,\n      scrollWidth\n    } = content;\n    const width = DomHandler.getWidth(content);\n    this.backwardIsDisabled = scrollLeft === 0;\n    this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n  }\n  updateScrollBar(index) {\n    const tabHeader = this.navbar?.nativeElement.children[index];\n    if (!tabHeader) {\n      return;\n    }\n    tabHeader.scrollIntoView({\n      block: 'nearest',\n      inline: 'center'\n    });\n  }\n  onScroll(event) {\n    this.scrollable && this.updateButtonState();\n    event.preventDefault();\n  }\n  navBackward() {\n    const content = this.content?.nativeElement;\n    const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n    const pos = content.scrollLeft - width;\n    content.scrollLeft = pos <= 0 ? 0 : pos;\n  }\n  navForward() {\n    const content = this.content?.nativeElement;\n    const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n    const pos = content.scrollLeft + width;\n    const lastPos = content.scrollWidth - width;\n    content.scrollLeft = pos >= lastPos ? lastPos : pos;\n  }\n  initAutoScrollForActiveItem() {\n    if (!this.scrollable) {\n      return;\n    }\n    this.clearAutoScrollHandler();\n    // We have to wait for the rendering and then can scroll to element.\n    this.timerIdForInitialAutoScroll = setTimeout(() => {\n      const activeItem = this.model.findIndex(menuItem => this.isActive(menuItem));\n      if (activeItem !== -1) {\n        this.updateScrollBar(activeItem);\n      }\n    });\n  }\n  clearAutoScrollHandler() {\n    if (this.timerIdForInitialAutoScroll) {\n      clearTimeout(this.timerIdForInitialAutoScroll);\n      this.timerIdForInitialAutoScroll = null;\n    }\n  }\n  initButtonState() {\n    if (this.scrollable) {\n      // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n      // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n      Promise.resolve().then(() => {\n        this.updateButtonState();\n        this.cd.markForCheck();\n      });\n    }\n  }\n  static ɵfac = function TabMenu_Factory(t) {\n    return new (t || TabMenu)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TabMenu,\n    selectors: [[\"p-tabMenu\"]],\n    contentQueries: function TabMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function TabMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inkbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.prevBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextBtn = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      activeItem: \"activeItem\",\n      scrollable: \"scrollable\",\n      popup: \"popup\",\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    outputs: {\n      activeItemChange: \"activeItemChange\"\n    },\n    decls: 11,\n    vars: 9,\n    consts: [[3, \"ngClass\", \"ngStyle\"], [1, \"p-tabmenu-nav-container\"], [\"class\", \"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-tabmenu-nav-content\", 3, \"scroll\"], [\"content\", \"\"], [\"role\", \"tablist\", 1, \"p-tabmenu-nav\", \"p-reset\"], [\"navbar\", \"\"], [\"role\", \"tab\", \"pTooltip\", \"\", 3, \"ngStyle\", \"class\", \"ngClass\", \"tooltipOptions\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-tabmenu-ink-bar\"], [\"inkbar\", \"\"], [\"class\", \"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-prev\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"], [\"prevBtn\", \"\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"tab\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", \"role\", \"presentation\", \"pRipple\", \"\", 3, \"target\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"role\", \"presentation\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"role\", \"presentation\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"target\", \"click\", \"keydown.enter\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [\"role\", \"presentation\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown.enter\"], [\"htmlRouteLabel\", \"\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-next\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"], [\"nextBtn\", \"\"]],\n    template: function TabMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, TabMenu_button_2_Template, 4, 2, \"button\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3, 4);\n        i0.ɵɵlistener(\"scroll\", function TabMenu_Template_div_scroll_3_listener($event) {\n          return ctx.onScroll($event);\n        });\n        i0.ɵɵelementStart(5, \"ul\", 5, 6);\n        i0.ɵɵtemplate(7, TabMenu_li_7_Template, 3, 13, \"li\", 7);\n        i0.ɵɵelement(8, \"li\", 8, 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(10, TabMenu_button_10_Template, 4, 2, \"button\", 10);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c8, ctx.scrollable))(\"ngStyle\", ctx.style);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.backwardIsDisabled);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.forwardIsDisabled);\n      }\n    },\n    dependencies: function () {\n      return [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.RouterLink, i1.RouterLinkActive, i3.Ripple, i4.Tooltip, ChevronLeftIcon, ChevronRightIcon];\n    },\n    styles: [\".p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-tabMenu',\n      template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"tablist\">\n                        <li\n                            *ngFor=\"let item of model; let i = index\"\n                            role=\"tab\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.aria-selected]=\"isActive(item)\"\n                            [attr.aria-expanded]=\"isActive(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': item.disabled, 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                *ngIf=\"!item.routerLink\"\n                                [attr.href]=\"item.url\"\n                                class=\"p-menuitem-link\"\n                                role=\"presentation\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"item.routerLink\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"presentation\"\n                                class=\"p-menuitem-link\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i1.Router\n    }, {\n      type: i1.ActivatedRoute\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    activeItem: [{\n      type: Input\n    }],\n    scrollable: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    activeItemChange: [{\n      type: Output\n    }],\n    content: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    navbar: [{\n      type: ViewChild,\n      args: ['navbar']\n    }],\n    inkbar: [{\n      type: ViewChild,\n      args: ['inkbar']\n    }],\n    prevBtn: [{\n      type: ViewChild,\n      args: ['prevBtn']\n    }],\n    nextBtn: [{\n      type: ViewChild,\n      args: ['nextBtn']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass TabMenuModule {\n  static ɵfac = function TabMenuModule_Factory(t) {\n    return new (t || TabMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TabMenuModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon, RouterModule, SharedModule, TooltipModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon],\n      exports: [TabMenu, RouterModule, SharedModule, TooltipModule],\n      declarations: [TabMenu]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabMenu, TabMenuModule };", "map": {"version": 3, "names": ["i2", "isPlatformBrowser", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "i1", "RouterModule", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ChevronLeftIcon", "ChevronRightIcon", "i3", "RippleModule", "i4", "TooltipModule", "_c0", "_c1", "_c2", "_c3", "_c4", "TabMenu_button_2_ChevronLeftIcon_2_Template", "rf", "ctx", "ɵɵelement", "TabMenu_button_2_3_ng_template_0_Template", "TabMenu_button_2_3_Template", "ɵɵtemplate", "TabMenu_button_2_Template", "_r11", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TabMenu_button_2_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "navBackward", "ɵɵelementEnd", "ctx_r0", "ɵɵadvance", "ɵɵproperty", "previousIconTemplate", "TabMenu_li_7_a_1_ng_container_1_span_1_Template", "item_r12", "$implicit", "icon", "iconStyle", "TabMenu_li_7_a_1_ng_container_1_span_2_Template", "ɵɵtext", "ɵɵtextInterpolate", "label", "TabMenu_li_7_a_1_ng_container_1_ng_template_3_Template", "ɵɵsanitizeHtml", "TabMenu_li_7_a_1_ng_container_1_span_5_Template", "badgeStyleClass", "badge", "TabMenu_li_7_a_1_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplateRefExtractor", "ɵɵelementContainerEnd", "_r20", "ɵɵreference", "escape", "TabMenu_li_7_a_1_ng_container_2_Template", "ɵɵelementContainer", "_c5", "a0", "a1", "index", "TabMenu_li_7_a_1_Template", "_r30", "TabMenu_li_7_a_1_Template_a_click_0_listener", "$event", "ctx_r28", "itemClick", "TabMenu_li_7_a_1_Template_a_keydown_enter_0_listener", "ctx_r31", "ctx_r33", "i_r13", "ctx_r14", "target", "ɵɵattribute", "url", "ɵɵsanitizeUrl", "disabled", "title", "id", "itemTemplate", "ɵɵpureFunction2", "TabMenu_li_7_a_2_ng_container_1_span_1_Template", "TabMenu_li_7_a_2_ng_container_1_span_2_Template", "TabMenu_li_7_a_2_ng_container_1_ng_template_3_Template", "TabMenu_li_7_a_2_ng_container_1_span_5_Template", "TabMenu_li_7_a_2_ng_container_1_Template", "_r38", "TabMenu_li_7_a_2_ng_container_2_Template", "_c6", "exact", "TabMenu_li_7_a_2_Template", "_r48", "TabMenu_li_7_a_2_Template_a_click_0_listener", "ctx_r46", "TabMenu_li_7_a_2_Template_a_keydown_enter_0_listener", "ctx_r49", "ctx_r51", "ctx_r15", "routerLink", "queryParams", "routerLinkActiveOptions", "ɵɵpureFunction0", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "state", "_c7", "a2", "a3", "TabMenu_li_7_Template", "ctx_r3", "ɵɵclassMap", "styleClass", "style", "ɵɵpureFunction3", "isActive", "visible", "tooltipOptions", "TabMenu_button_10_ChevronRightIcon_2_Template", "TabMenu_button_10_3_ng_template_0_Template", "TabMenu_button_10_3_Template", "TabMenu_button_10_Template", "_r57", "TabMenu_button_10_Template_button_click_0_listener", "ctx_r56", "navForward", "ctx_r5", "nextIconTemplate", "_c8", "TabMenu", "platformId", "router", "route", "cd", "model", "activeItem", "scrollable", "popup", "activeItemChange", "content", "navbar", "inkbar", "prevBtn", "nextBtn", "templates", "tabChanged", "backwardIsDisabled", "forwardIsDisabled", "timerIdForInitialAutoScroll", "constructor", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "ngAfterViewInit", "updateInkBar", "initAutoScrollForActiveItem", "initButtonState", "ngAfterViewChecked", "ngOnDestroy", "clearAutoScrollHandler", "Array", "isArray", "createUrlTree", "relativeTo", "toString", "event", "preventDefault", "command", "originalEvent", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabHeader", "findSingle", "nativeElement", "width", "getWidth", "left", "getOffset", "getVisibleButtonWidths", "reduce", "acc", "el", "updateButtonState", "scrollLeft", "scrollWidth", "parseInt", "updateScrollBar", "children", "scrollIntoView", "block", "inline", "onScroll", "pos", "lastPos", "setTimeout", "findIndex", "menuItem", "clearTimeout", "Promise", "resolve", "then", "ɵfac", "TabMenu_Factory", "t", "ɵɵdirectiveInject", "Router", "ActivatedRoute", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "TabMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "TabMenu_Query", "ɵɵviewQuery", "first", "hostAttrs", "inputs", "outputs", "decls", "vars", "consts", "TabMenu_Template", "TabMenu_Template_div_scroll_3_listener", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLink", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "None", "host", "class", "undefined", "decorators", "TabMenuModule", "TabMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/primeng/fesm2022/primeng-tabmenu.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\nclass TabMenu {\n    platformId;\n    router;\n    route;\n    cd;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    model;\n    /**\n     * Defines the default active menuitem\n     * @group Props\n     */\n    activeItem;\n    /**\n     * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n     * @group Props\n     */\n    scrollable;\n    /**\n     * Defines if popup mode enabled.\n     */\n    popup;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Event fired when a tab is selected.\n     * @param {MenuItem} item - Menu item.\n     * @group Emits\n     */\n    activeItemChange = new EventEmitter();\n    content;\n    navbar;\n    inkbar;\n    prevBtn;\n    nextBtn;\n    templates;\n    itemTemplate;\n    previousIconTemplate;\n    nextIconTemplate;\n    tabChanged;\n    backwardIsDisabled = true;\n    forwardIsDisabled = false;\n    timerIdForInitialAutoScroll = null;\n    constructor(platformId, router, route, cd) {\n        this.platformId = platformId;\n        this.router = router;\n        this.route = route;\n        this.cd = cd;\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'nexticon':\n                    this.nextIconTemplate = item.template;\n                    break;\n                case 'previousicon':\n                    this.previousIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.updateInkBar();\n            this.initAutoScrollForActiveItem();\n            this.initButtonState();\n        }\n    }\n    ngAfterViewChecked() {\n        if (this.tabChanged) {\n            this.updateInkBar();\n            this.tabChanged = false;\n        }\n    }\n    ngOnDestroy() {\n        this.clearAutoScrollHandler();\n    }\n    isActive(item) {\n        if (item.routerLink) {\n            const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n            return this.router.isActive(this.router.createUrlTree(routerLink, { relativeTo: this.route }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n        }\n        return item === this.activeItem;\n    }\n    itemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        this.activeItem = item;\n        this.activeItemChange.emit(item);\n        this.tabChanged = true;\n        this.cd.markForCheck();\n    }\n    updateInkBar() {\n        const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n        if (tabHeader) {\n            this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n            this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n        }\n    }\n    getVisibleButtonWidths() {\n        return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => (el ? acc + DomHandler.getWidth(el) : acc), 0);\n    }\n    updateButtonState() {\n        const content = this.content?.nativeElement;\n        const { scrollLeft, scrollWidth } = content;\n        const width = DomHandler.getWidth(content);\n        this.backwardIsDisabled = scrollLeft === 0;\n        this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n    }\n    updateScrollBar(index) {\n        const tabHeader = this.navbar?.nativeElement.children[index];\n        if (!tabHeader) {\n            return;\n        }\n        tabHeader.scrollIntoView({ block: 'nearest', inline: 'center' });\n    }\n    onScroll(event) {\n        this.scrollable && this.updateButtonState();\n        event.preventDefault();\n    }\n    navBackward() {\n        const content = this.content?.nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft - width;\n        content.scrollLeft = pos <= 0 ? 0 : pos;\n    }\n    navForward() {\n        const content = this.content?.nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft + width;\n        const lastPos = content.scrollWidth - width;\n        content.scrollLeft = pos >= lastPos ? lastPos : pos;\n    }\n    initAutoScrollForActiveItem() {\n        if (!this.scrollable) {\n            return;\n        }\n        this.clearAutoScrollHandler();\n        // We have to wait for the rendering and then can scroll to element.\n        this.timerIdForInitialAutoScroll = setTimeout(() => {\n            const activeItem = this.model.findIndex((menuItem) => this.isActive(menuItem));\n            if (activeItem !== -1) {\n                this.updateScrollBar(activeItem);\n            }\n        });\n    }\n    clearAutoScrollHandler() {\n        if (this.timerIdForInitialAutoScroll) {\n            clearTimeout(this.timerIdForInitialAutoScroll);\n            this.timerIdForInitialAutoScroll = null;\n        }\n    }\n    initButtonState() {\n        if (this.scrollable) {\n            // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n            // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n            Promise.resolve().then(() => {\n                this.updateButtonState();\n                this.cd.markForCheck();\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: TabMenu, deps: [{ token: PLATFORM_ID }, { token: i1.Router }, { token: i1.ActivatedRoute }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.2\", type: TabMenu, selector: \"p-tabMenu\", inputs: { model: \"model\", activeItem: \"activeItem\", scrollable: \"scrollable\", popup: \"popup\", style: \"style\", styleClass: \"styleClass\" }, outputs: { activeItemChange: \"activeItemChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"content\", first: true, predicate: [\"content\"], descendants: true }, { propertyName: \"navbar\", first: true, predicate: [\"navbar\"], descendants: true }, { propertyName: \"inkbar\", first: true, predicate: [\"inkbar\"], descendants: true }, { propertyName: \"prevBtn\", first: true, predicate: [\"prevBtn\"], descendants: true }, { propertyName: \"nextBtn\", first: true, predicate: [\"nextBtn\"], descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"tablist\">\n                        <li\n                            *ngFor=\"let item of model; let i = index\"\n                            role=\"tab\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.aria-selected]=\"isActive(item)\"\n                            [attr.aria-expanded]=\"isActive(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': item.disabled, 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                *ngIf=\"!item.routerLink\"\n                                [attr.href]=\"item.url\"\n                                class=\"p-menuitem-link\"\n                                role=\"presentation\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"item.routerLink\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"presentation\"\n                                class=\"p-menuitem-link\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i2.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgForOf; }), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgTemplateOutlet; }), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.RouterLink; }), selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.RouterLinkActive; }), selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i3.Ripple; }), selector: \"[pRipple]\" }, { kind: \"directive\", type: i0.forwardRef(function () { return i4.Tooltip; }), selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"autoHide\", \"fitContent\", \"hideOnEscape\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i0.forwardRef(function () { return ChevronLeftIcon; }), selector: \"ChevronLeftIcon\" }, { kind: \"component\", type: i0.forwardRef(function () { return ChevronRightIcon; }), selector: \"ChevronRightIcon\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: TabMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tabMenu', template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"tablist\">\n                        <li\n                            *ngFor=\"let item of model; let i = index\"\n                            role=\"tab\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.aria-selected]=\"isActive(item)\"\n                            [attr.aria-expanded]=\"isActive(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': item.disabled, 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                *ngIf=\"!item.routerLink\"\n                                [attr.href]=\"item.url\"\n                                class=\"p-menuitem-link\"\n                                role=\"presentation\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"item.routerLink\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"presentation\"\n                                class=\"p-menuitem-link\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\".p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i1.Router }, { type: i1.ActivatedRoute }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { model: [{\n                type: Input\n            }], activeItem: [{\n                type: Input\n            }], scrollable: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], activeItemChange: [{\n                type: Output\n            }], content: [{\n                type: ViewChild,\n                args: ['content']\n            }], navbar: [{\n                type: ViewChild,\n                args: ['navbar']\n            }], inkbar: [{\n                type: ViewChild,\n                args: ['inkbar']\n            }], prevBtn: [{\n                type: ViewChild,\n                args: ['prevBtn']\n            }], nextBtn: [{\n                type: ViewChild,\n                args: ['nextBtn']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass TabMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: TabMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.2\", ngImport: i0, type: TabMenuModule, declarations: [TabMenu], imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon], exports: [TabMenu, RouterModule, SharedModule, TooltipModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: TabMenuModule, imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon, RouterModule, SharedModule, TooltipModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: TabMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon],\n                    exports: [TabMenu, RouterModule, SharedModule, TooltipModule],\n                    declarations: [TabMenu]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabMenu, TabMenuModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;;AAE/C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAiM6F7B,EAAE,CAAA+B,SAAA,qBAK3B,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAH,EAAA,EAAAC,GAAA;AAAA,SAAAG,4BAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IALwB7B,EAAE,CAAAkC,UAAA,IAAAF,yCAAA,qBAMR,CAAC;EAAA;AAAA;AAAA,SAAAG,0BAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAO,IAAA,GANKpC,EAAE,CAAAqC,gBAAA;IAAFrC,EAAE,CAAAsC,cAAA,oBAI6E,CAAC;IAJhFtC,EAAE,CAAAuC,UAAA,mBAAAC,kDAAA;MAAFxC,EAAE,CAAAyC,aAAA,CAAAL,IAAA;MAAA,MAAAM,OAAA,GAAF1C,EAAE,CAAA2C,aAAA;MAAA,OAAF3C,EAAE,CAAA4C,WAAA,CAIyCF,OAAA,CAAAG,WAAA,CAAY,EAAC;IAAA,EAAC;IAJzD7C,EAAE,CAAAkC,UAAA,IAAAN,2CAAA,6BAK3B,CAAC;IALwB5B,EAAE,CAAAkC,UAAA,IAAAD,2BAAA,gBAMR,CAAC;IANKjC,EAAE,CAAA8C,YAAA,CAOvE,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAkB,MAAA,GAPoE/C,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAgD,SAAA,EAK/B,CAAC;IAL4BhD,EAAE,CAAAiD,UAAA,UAAAF,MAAA,CAAAG,oBAK/B,CAAC;IAL4BlD,EAAE,CAAAgD,SAAA,EAMxB,CAAC;IANqBhD,EAAE,CAAAiD,UAAA,qBAAAF,MAAA,CAAAG,oBAMxB,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IANqB7B,EAAE,CAAA+B,SAAA,cAmC4C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,QAAA,GAnC/CpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAiD,UAAA,YAAAG,QAAA,CAAAE,IAmCT,CAAC,YAAAF,QAAA,CAAAG,SAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCM7B,EAAE,CAAAsC,cAAA,cAoCgB,CAAC;IApCnBtC,EAAE,CAAAyD,MAAA,EAoCgC,CAAC;IApCnCzD,EAAE,CAAA8C,YAAA,CAoCuC,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAuB,QAAA,GApC1CpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAgD,SAAA,EAoCgC,CAAC;IApCnChD,EAAE,CAAA0D,iBAAA,CAAAN,QAAA,CAAAO,KAoCgC,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApCnC7B,EAAE,CAAA+B,SAAA,cAqC0B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,QAAA,GArC7BpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAiD,UAAA,cAAAG,QAAA,CAAAO,KAAA,EAAF3D,EAAE,CAAA6D,cAqCkB,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArCrB7B,EAAE,CAAAsC,cAAA,cAsCuB,CAAC;IAtC1BtC,EAAE,CAAAyD,MAAA,EAsCuC,CAAC;IAtC1CzD,EAAE,CAAA8C,YAAA,CAsC8C,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAuB,QAAA,GAtCjDpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAiD,UAAA,YAAAG,QAAA,CAAAW,eAsCsB,CAAC;IAtCzB/D,EAAE,CAAAgD,SAAA,EAsCuC,CAAC;IAtC1ChD,EAAE,CAAA0D,iBAAA,CAAAN,QAAA,CAAAY,KAsCuC,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtC1C7B,EAAE,CAAAkE,uBAAA,EAkC5B,CAAC;IAlCyBlE,EAAE,CAAAkC,UAAA,IAAAiB,+CAAA,kBAmC4C,CAAC;IAnC/CnD,EAAE,CAAAkC,UAAA,IAAAsB,+CAAA,kBAoCuC,CAAC;IApC1CxD,EAAE,CAAAkC,UAAA,IAAA0B,sDAAA,iCAAF5D,EAAE,CAAAmE,sBAqCwC,CAAC;IArC3CnE,EAAE,CAAAkC,UAAA,IAAA4B,+CAAA,kBAsC8C,CAAC;IAtCjD9D,EAAE,CAAAoE,qBAAA,CAuCjD,CAAC;EAAA;EAAA,IAAAvC,EAAA;IAAA,MAAAwC,IAAA,GAvC8CrE,EAAE,CAAAsE,WAAA;IAAA,MAAAlB,QAAA,GAAFpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAgD,SAAA,EAmCQ,CAAC;IAnCXhD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAAE,IAmCQ,CAAC;IAnCXtD,EAAE,CAAAgD,SAAA,EAoCA,CAAC;IApCHhD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAAmB,MAAA,UAoCA,CAAC,aAAAF,IAAD,CAAC;IApCHrE,EAAE,CAAAgD,SAAA,EAsCZ,CAAC;IAtCShD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAAY,KAsCZ,CAAC;EAAA;AAAA;AAAA,SAAAQ,yCAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtCS7B,EAAE,CAAAyE,kBAAA,EAwCsC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA;IAAAvB,SAAA,EAAAsB,EAAA;IAAAE,KAAA,EAAAD;EAAA;AAAA;AAAA,SAAAE,0BAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkD,IAAA,GAxCzC/E,EAAE,CAAAqC,gBAAA;IAAFrC,EAAE,CAAAsC,cAAA,WAiCnE,CAAC;IAjCgEtC,EAAE,CAAAuC,UAAA,mBAAAyC,6CAAAC,MAAA;MAAFjF,EAAE,CAAAyC,aAAA,CAAAsC,IAAA;MAAA,MAAA3B,QAAA,GAAFpD,EAAE,CAAA2C,aAAA,GAAAU,SAAA;MAAA,MAAA6B,OAAA,GAAFlF,EAAE,CAAA2C,aAAA;MAAA,OAAF3C,EAAE,CAAA4C,WAAA,CA0BtDsC,OAAA,CAAAC,SAAA,CAAAF,MAAA,EAAA7B,QAAsB,EAAC;IAAA,EAAC,2BAAAgC,qDAAAH,MAAA;MA1B4BjF,EAAE,CAAAyC,aAAA,CAAAsC,IAAA;MAAA,MAAA3B,QAAA,GAAFpD,EAAE,CAAA2C,aAAA,GAAAU,SAAA;MAAA,MAAAgC,OAAA,GAAFrF,EAAE,CAAA2C,aAAA;MAAA,OAAF3C,EAAE,CAAA4C,WAAA,CA2B9CyC,OAAA,CAAAF,SAAA,CAAAF,MAAA,EAAA7B,QAAsB,EAAC;IAAA,CADR,CAAC;IA1B4BpD,EAAE,CAAAkC,UAAA,IAAA+B,wCAAA,0BAuCjD,CAAC;IAvC8CjE,EAAE,CAAAkC,UAAA,IAAAsC,wCAAA,0BAwCsC,CAAC;IAxCzCxE,EAAE,CAAA8C,YAAA,CAyChE,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAyD,OAAA,GAzC6DtF,EAAE,CAAA2C,aAAA;IAAA,MAAAS,QAAA,GAAAkC,OAAA,CAAAjC,SAAA;IAAA,MAAAkC,KAAA,GAAAD,OAAA,CAAAT,KAAA;IAAA,MAAAW,OAAA,GAAFxF,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAiD,UAAA,WAAAG,QAAA,CAAAqC,MA6B1C,CAAC;IA7BuCzF,EAAE,CAAA0F,WAAA,SAAAtC,QAAA,CAAAuC,GAAA,EAAF3F,EAAE,CAAA4F,aAuB1C,CAAC,aAAAxC,QAAA,CAAAyC,QAAA,aAAD,CAAC,UAAAzC,QAAA,CAAA0C,KAAD,CAAC,OAAA1C,QAAA,CAAA2C,EAAD,CAAC;IAvBuC/F,EAAE,CAAAgD,SAAA,EAkC9B,CAAC;IAlC2BhD,EAAE,CAAAiD,UAAA,UAAAuC,OAAA,CAAAQ,YAkC9B,CAAC;IAlC2BhG,EAAE,CAAAgD,SAAA,EAwCjB,CAAC;IAxCchD,EAAE,CAAAiD,UAAA,qBAAAuC,OAAA,CAAAQ,YAwCjB,CAAC,4BAxCchG,EAAE,CAAAiG,eAAA,IAAAvB,GAAA,EAAAtB,QAAA,EAAAmC,KAAA,CAwCjB,CAAC;EAAA;AAAA;AAAA,SAAAW,gDAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxCc7B,EAAE,CAAA+B,SAAA,cAiE4C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,QAAA,GAjE/CpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAiD,UAAA,YAAAG,QAAA,CAAAE,IAiET,CAAC,YAAAF,QAAA,CAAAG,SAAD,CAAC;EAAA;AAAA;AAAA,SAAA4C,gDAAAtE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjEM7B,EAAE,CAAAsC,cAAA,cAkEqB,CAAC;IAlExBtC,EAAE,CAAAyD,MAAA,EAkEqC,CAAC;IAlExCzD,EAAE,CAAA8C,YAAA,CAkE4C,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAuB,QAAA,GAlE/CpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAgD,SAAA,EAkEqC,CAAC;IAlExChD,EAAE,CAAA0D,iBAAA,CAAAN,QAAA,CAAAO,KAkEqC,CAAC;EAAA;AAAA;AAAA,SAAAyC,uDAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlExC7B,EAAE,CAAA+B,SAAA,cAmE+B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAuB,QAAA,GAnElCpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAiD,UAAA,cAAAG,QAAA,CAAAO,KAAA,EAAF3D,EAAE,CAAA6D,cAmEuB,CAAC;EAAA;AAAA;AAAA,SAAAwC,gDAAAxE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnE1B7B,EAAE,CAAAsC,cAAA,cAoEuB,CAAC;IApE1BtC,EAAE,CAAAyD,MAAA,EAoEuC,CAAC;IApE1CzD,EAAE,CAAA8C,YAAA,CAoE8C,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAuB,QAAA,GApEjDpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAiD,UAAA,YAAAG,QAAA,CAAAW,eAoEsB,CAAC;IApEzB/D,EAAE,CAAAgD,SAAA,EAoEuC,CAAC;IApE1ChD,EAAE,CAAA0D,iBAAA,CAAAN,QAAA,CAAAY,KAoEuC,CAAC;EAAA;AAAA;AAAA,SAAAsC,yCAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApE1C7B,EAAE,CAAAkE,uBAAA,EAgE5B,CAAC;IAhEyBlE,EAAE,CAAAkC,UAAA,IAAAgE,+CAAA,kBAiE4C,CAAC;IAjE/ClG,EAAE,CAAAkC,UAAA,IAAAiE,+CAAA,kBAkE4C,CAAC;IAlE/CnG,EAAE,CAAAkC,UAAA,IAAAkE,sDAAA,iCAAFpG,EAAE,CAAAmE,sBAmE6C,CAAC;IAnEhDnE,EAAE,CAAAkC,UAAA,IAAAmE,+CAAA,kBAoE8C,CAAC;IApEjDrG,EAAE,CAAAoE,qBAAA,CAqEjD,CAAC;EAAA;EAAA,IAAAvC,EAAA;IAAA,MAAA0E,IAAA,GArE8CvG,EAAE,CAAAsE,WAAA;IAAA,MAAAlB,QAAA,GAAFpD,EAAE,CAAA2C,aAAA,IAAAU,SAAA;IAAFrD,EAAE,CAAAgD,SAAA,EAiEQ,CAAC;IAjEXhD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAAE,IAiEQ,CAAC;IAjEXtD,EAAE,CAAAgD,SAAA,EAkEA,CAAC;IAlEHhD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAAmB,MAAA,UAkEA,CAAC,aAAAgC,IAAD,CAAC;IAlEHvG,EAAE,CAAAgD,SAAA,EAoEZ,CAAC;IApEShD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAAY,KAoEZ,CAAC;EAAA;AAAA;AAAA,SAAAwC,yCAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApES7B,EAAE,CAAAyE,kBAAA,EAsEsC,CAAC;EAAA;AAAA;AAAA,MAAAgC,GAAA,YAAAA,CAAA;EAAA;IAAAC,KAAA;EAAA;AAAA;AAAA,SAAAC,0BAAA9E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+E,IAAA,GAtEzC5G,EAAE,CAAAqC,gBAAA;IAAFrC,EAAE,CAAAsC,cAAA,WA+DnE,CAAC;IA/DgEtC,EAAE,CAAAuC,UAAA,mBAAAsE,6CAAA5B,MAAA;MAAFjF,EAAE,CAAAyC,aAAA,CAAAmE,IAAA;MAAA,MAAAxD,QAAA,GAAFpD,EAAE,CAAA2C,aAAA,GAAAU,SAAA;MAAA,MAAAyD,OAAA,GAAF9G,EAAE,CAAA2C,aAAA;MAAA,OAAF3C,EAAE,CAAA4C,WAAA,CAkDtDkE,OAAA,CAAA3B,SAAA,CAAAF,MAAA,EAAA7B,QAAsB,EAAC;IAAA,EAAC,2BAAA2D,qDAAA9B,MAAA;MAlD4BjF,EAAE,CAAAyC,aAAA,CAAAmE,IAAA;MAAA,MAAAxD,QAAA,GAAFpD,EAAE,CAAA2C,aAAA,GAAAU,SAAA;MAAA,MAAA2D,OAAA,GAAFhH,EAAE,CAAA2C,aAAA;MAAA,OAAF3C,EAAE,CAAA4C,WAAA,CAmD9CoE,OAAA,CAAA7B,SAAA,CAAAF,MAAA,EAAA7B,QAAsB,EAAC;IAAA,CADR,CAAC;IAlD4BpD,EAAE,CAAAkC,UAAA,IAAAoE,wCAAA,0BAqEjD,CAAC;IArE8CtG,EAAE,CAAAkC,UAAA,IAAAsE,wCAAA,0BAsEsC,CAAC;IAtEzCxG,EAAE,CAAA8C,YAAA,CAuEhE,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAoF,OAAA,GAvE6DjH,EAAE,CAAA2C,aAAA;IAAA,MAAAS,QAAA,GAAA6D,OAAA,CAAA5D,SAAA;IAAA,MAAAkC,KAAA,GAAA0B,OAAA,CAAApC,KAAA;IAAA,MAAAqC,OAAA,GAAFlH,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAiD,UAAA,eAAAG,QAAA,CAAA+D,UA4ClC,CAAC,gBAAA/D,QAAA,CAAAgE,WAAD,CAAC,6CAAD,CAAC,4BAAAhE,QAAA,CAAAiE,uBAAA,IA5C+BrH,EAAE,CAAAsH,eAAA,KAAAb,GAAA,CA4ClC,CAAC,WAAArD,QAAA,CAAAqC,MAAD,CAAC,aAAArC,QAAA,CAAAmE,QAAD,CAAC,wBAAAnE,QAAA,CAAAoE,mBAAD,CAAC,qBAAApE,QAAA,CAAAqE,gBAAD,CAAC,uBAAArE,QAAA,CAAAsE,kBAAD,CAAC,eAAAtE,QAAA,CAAAuE,UAAD,CAAC,UAAAvE,QAAA,CAAAwE,KAAD,CAAC;IA5C+B5H,EAAE,CAAA0F,WAAA,aAAAtC,QAAA,CAAAyC,QAAA,aAoDpB,CAAC,UAAAzC,QAAA,CAAA0C,KAAD,CAAC,OAAA1C,QAAA,CAAA2C,EAAD,CAAC;IApDiB/F,EAAE,CAAAgD,SAAA,EAgE9B,CAAC;IAhE2BhD,EAAE,CAAAiD,UAAA,UAAAiE,OAAA,CAAAlB,YAgE9B,CAAC;IAhE2BhG,EAAE,CAAAgD,SAAA,EAsEjB,CAAC;IAtEchD,EAAE,CAAAiD,UAAA,qBAAAiE,OAAA,CAAAlB,YAsEjB,CAAC,4BAtEchG,EAAE,CAAAiG,eAAA,KAAAvB,GAAA,EAAAtB,QAAA,EAAAmC,KAAA,CAsEjB,CAAC;EAAA;AAAA;AAAA,MAAAsC,GAAA,YAAAA,CAAAjD,EAAA,EAAAkD,EAAA,EAAAC,EAAA;EAAA;IAAA;IAAA,cAAAnD,EAAA;IAAA,eAAAkD,EAAA;IAAA,YAAAC;EAAA;AAAA;AAAA,SAAAC,sBAAAnG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtEc7B,EAAE,CAAAsC,cAAA,YAoBvE,CAAC;IApBoEtC,EAAE,CAAAkC,UAAA,IAAA4C,yBAAA,gBAyChE,CAAC;IAzC6D9E,EAAE,CAAAkC,UAAA,IAAAyE,yBAAA,gBAuEhE,CAAC;IAvE6D3G,EAAE,CAAA8C,YAAA,CAwEnE,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAuB,QAAA,GAAAtB,GAAA,CAAAuB,SAAA;IAAA,MAAA4E,MAAA,GAxEgEjI,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAkI,UAAA,CAAA9E,QAAA,CAAA+E,UAc3C,CAAC;IAdwCnI,EAAE,CAAAiD,UAAA,YAAAG,QAAA,CAAAgF,KAa9C,CAAC,YAb2CpI,EAAE,CAAAqI,eAAA,IAAAR,GAAA,EAAAzE,QAAA,CAAAyC,QAAA,EAAAoC,MAAA,CAAAK,QAAA,CAAAlF,QAAA,GAAAA,QAAA,CAAAmF,OAAA,WAa9C,CAAC,mBAAAnF,QAAA,CAAAoF,cAAD,CAAC;IAb2CxI,EAAE,CAAA0F,WAAA,kBAAAuC,MAAA,CAAAK,QAAA,CAAAlF,QAAA,CAe/B,CAAC,kBAAA6E,MAAA,CAAAK,QAAA,CAAAlF,QAAA,CAAD,CAAC;IAf4BpD,EAAE,CAAAgD,SAAA,EAsBzC,CAAC;IAtBsChD,EAAE,CAAAiD,UAAA,UAAAG,QAAA,CAAA+D,UAsBzC,CAAC;IAtBsCnH,EAAE,CAAAgD,SAAA,EA2C1C,CAAC;IA3CuChD,EAAE,CAAAiD,UAAA,SAAAG,QAAA,CAAA+D,UA2C1C,CAAC;EAAA;AAAA;AAAA,SAAAsB,8CAAA5G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3CuC7B,EAAE,CAAA+B,SAAA,sBA6E1B,CAAC;EAAA;AAAA;AAAA,SAAA2G,2CAAA7G,EAAA,EAAAC,GAAA;AAAA,SAAA6G,6BAAA9G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7EuB7B,EAAE,CAAAkC,UAAA,IAAAwG,0CAAA,qBA8EZ,CAAC;EAAA;AAAA;AAAA,SAAAE,2BAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgH,IAAA,GA9ES7I,EAAE,CAAAqC,gBAAA;IAAFrC,EAAE,CAAAsC,cAAA,oBA4E2E,CAAC;IA5E9EtC,EAAE,CAAAuC,UAAA,mBAAAuG,mDAAA;MAAF9I,EAAE,CAAAyC,aAAA,CAAAoG,IAAA;MAAA,MAAAE,OAAA,GAAF/I,EAAE,CAAA2C,aAAA;MAAA,OAAF3C,EAAE,CAAA4C,WAAA,CA4EwCmG,OAAA,CAAAC,UAAA,CAAW,EAAC;IAAA,EAAC;IA5EvDhJ,EAAE,CAAAkC,UAAA,IAAAuG,6CAAA,8BA6E1B,CAAC;IA7EuBzI,EAAE,CAAAkC,UAAA,IAAAyG,4BAAA,gBA8EZ,CAAC;IA9ES3I,EAAE,CAAA8C,YAAA,CA+EvE,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAoH,MAAA,GA/EoEjJ,EAAE,CAAA2C,aAAA;IAAF3C,EAAE,CAAAgD,SAAA,EA6E9B,CAAC;IA7E2BhD,EAAE,CAAAiD,UAAA,UAAAgG,MAAA,CAAA/F,oBA6E9B,CAAC;IA7E2BlD,EAAE,CAAAgD,SAAA,EA8E5B,CAAC;IA9EyBhD,EAAE,CAAAiD,UAAA,qBAAAgG,MAAA,CAAAC,gBA8E5B,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAvE,EAAA;EAAA;IAAA;IAAA,wBAAAA;EAAA;AAAA;AA3QpE,MAAMwE,OAAO,CAAC;EACVC,UAAU;EACVC,MAAM;EACNC,KAAK;EACLC,EAAE;EACF;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIxB,KAAK;EACL;AACJ;AACA;AACA;EACID,UAAU;EACV;AACJ;AACA;AACA;AACA;EACI0B,gBAAgB,GAAG,IAAI5J,YAAY,CAAC,CAAC;EACrC6J,OAAO;EACPC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,SAAS;EACTnE,YAAY;EACZ9C,oBAAoB;EACpBgG,gBAAgB;EAChBkB,UAAU;EACVC,kBAAkB,GAAG,IAAI;EACzBC,iBAAiB,GAAG,KAAK;EACzBC,2BAA2B,GAAG,IAAI;EAClCC,WAAWA,CAACnB,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,EAAE,EAAE;IACvC,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,EAAE,GAAGA,EAAE;EAChB;EACAiB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,SAAS,EAAEO,OAAO,CAAEC,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,MAAM;UACP,IAAI,CAAC5E,YAAY,GAAG2E,IAAI,CAACE,QAAQ;UACjC;QACJ,KAAK,UAAU;UACX,IAAI,CAAC3B,gBAAgB,GAAGyB,IAAI,CAACE,QAAQ;UACrC;QACJ,KAAK,cAAc;UACf,IAAI,CAAC3H,oBAAoB,GAAGyH,IAAI,CAACE,QAAQ;UACzC;QACJ;UACI,IAAI,CAAC7E,YAAY,GAAG2E,IAAI,CAACE,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd,IAAIhL,iBAAiB,CAAC,IAAI,CAACuJ,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC0B,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACd,UAAU,EAAE;MACjB,IAAI,CAACW,YAAY,CAAC,CAAC;MACnB,IAAI,CAACX,UAAU,GAAG,KAAK;IAC3B;EACJ;EACAe,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACA9C,QAAQA,CAACqC,IAAI,EAAE;IACX,IAAIA,IAAI,CAACxD,UAAU,EAAE;MACjB,MAAMA,UAAU,GAAGkE,KAAK,CAACC,OAAO,CAACX,IAAI,CAACxD,UAAU,CAAC,GAAGwD,IAAI,CAACxD,UAAU,GAAG,CAACwD,IAAI,CAACxD,UAAU,CAAC;MACvF,OAAO,IAAI,CAACmC,MAAM,CAAChB,QAAQ,CAAC,IAAI,CAACgB,MAAM,CAACiC,aAAa,CAACpE,UAAU,EAAE;QAAEqE,UAAU,EAAE,IAAI,CAACjC;MAAM,CAAC,CAAC,CAACkC,QAAQ,CAAC,CAAC,EAAEd,IAAI,CAACtD,uBAAuB,EAAEX,KAAK,IAAIiE,IAAI,CAACtD,uBAAuB,IAAI,KAAK,CAAC;IAC3L;IACA,OAAOsD,IAAI,KAAK,IAAI,CAACjB,UAAU;EACnC;EACAvE,SAASA,CAACuG,KAAK,EAAEf,IAAI,EAAE;IACnB,IAAIA,IAAI,CAAC9E,QAAQ,EAAE;MACf6F,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB;IACJ;IACA,IAAI,CAAChB,IAAI,CAAChF,GAAG,IAAI,CAACgF,IAAI,CAACxD,UAAU,EAAE;MAC/BuE,KAAK,CAACC,cAAc,CAAC,CAAC;IAC1B;IACA,IAAIhB,IAAI,CAACiB,OAAO,EAAE;MACdjB,IAAI,CAACiB,OAAO,CAAC;QACTC,aAAa,EAAEH,KAAK;QACpBf,IAAI,EAAEA;MACV,CAAC,CAAC;IACN;IACA,IAAI,CAACjB,UAAU,GAAGiB,IAAI;IACtB,IAAI,CAACd,gBAAgB,CAACiC,IAAI,CAACnB,IAAI,CAAC;IAChC,IAAI,CAACP,UAAU,GAAG,IAAI;IACtB,IAAI,CAACZ,EAAE,CAACuC,YAAY,CAAC,CAAC;EAC1B;EACAhB,YAAYA,CAAA,EAAG;IACX,MAAMiB,SAAS,GAAGhL,UAAU,CAACiL,UAAU,CAAC,IAAI,CAAClC,MAAM,EAAEmC,aAAa,EAAE,gBAAgB,CAAC;IACrF,IAAIF,SAAS,EAAE;MACX,IAAI,CAAChC,MAAM,CAACkC,aAAa,CAAC9D,KAAK,CAAC+D,KAAK,GAAGnL,UAAU,CAACoL,QAAQ,CAACJ,SAAS,CAAC,GAAG,IAAI;MAC7E,IAAI,CAAChC,MAAM,CAACkC,aAAa,CAAC9D,KAAK,CAACiE,IAAI,GAAGrL,UAAU,CAACsL,SAAS,CAACN,SAAS,CAAC,CAACK,IAAI,GAAGrL,UAAU,CAACsL,SAAS,CAAC,IAAI,CAACvC,MAAM,EAAEmC,aAAa,CAAC,CAACG,IAAI,GAAG,IAAI;IAC9I;EACJ;EACAE,sBAAsBA,CAAA,EAAG;IACrB,OAAO,CAAC,IAAI,CAACtC,OAAO,EAAEiC,aAAa,EAAE,IAAI,CAAChC,OAAO,EAAEgC,aAAa,CAAC,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,KAAMA,EAAE,GAAGD,GAAG,GAAGzL,UAAU,CAACoL,QAAQ,CAACM,EAAE,CAAC,GAAGD,GAAI,EAAE,CAAC,CAAC;EACxI;EACAE,iBAAiBA,CAAA,EAAG;IAChB,MAAM7C,OAAO,GAAG,IAAI,CAACA,OAAO,EAAEoC,aAAa;IAC3C,MAAM;MAAEU,UAAU;MAAEC;IAAY,CAAC,GAAG/C,OAAO;IAC3C,MAAMqC,KAAK,GAAGnL,UAAU,CAACoL,QAAQ,CAACtC,OAAO,CAAC;IAC1C,IAAI,CAACO,kBAAkB,GAAGuC,UAAU,KAAK,CAAC;IAC1C,IAAI,CAACtC,iBAAiB,GAAGwC,QAAQ,CAACF,UAAU,CAAC,KAAKC,WAAW,GAAGV,KAAK;EACzE;EACAY,eAAeA,CAAClI,KAAK,EAAE;IACnB,MAAMmH,SAAS,GAAG,IAAI,CAACjC,MAAM,EAAEmC,aAAa,CAACc,QAAQ,CAACnI,KAAK,CAAC;IAC5D,IAAI,CAACmH,SAAS,EAAE;MACZ;IACJ;IACAA,SAAS,CAACiB,cAAc,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAC,CAAC;EACpE;EACAC,QAAQA,CAAC1B,KAAK,EAAE;IACZ,IAAI,CAAC/B,UAAU,IAAI,IAAI,CAACgD,iBAAiB,CAAC,CAAC;IAC3CjB,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B;EACA9I,WAAWA,CAAA,EAAG;IACV,MAAMiH,OAAO,GAAG,IAAI,CAACA,OAAO,EAAEoC,aAAa;IAC3C,MAAMC,KAAK,GAAGnL,UAAU,CAACoL,QAAQ,CAACtC,OAAO,CAAC,GAAG,IAAI,CAACyC,sBAAsB,CAAC,CAAC;IAC1E,MAAMc,GAAG,GAAGvD,OAAO,CAAC8C,UAAU,GAAGT,KAAK;IACtCrC,OAAO,CAAC8C,UAAU,GAAGS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC3C;EACArE,UAAUA,CAAA,EAAG;IACT,MAAMc,OAAO,GAAG,IAAI,CAACA,OAAO,EAAEoC,aAAa;IAC3C,MAAMC,KAAK,GAAGnL,UAAU,CAACoL,QAAQ,CAACtC,OAAO,CAAC,GAAG,IAAI,CAACyC,sBAAsB,CAAC,CAAC;IAC1E,MAAMc,GAAG,GAAGvD,OAAO,CAAC8C,UAAU,GAAGT,KAAK;IACtC,MAAMmB,OAAO,GAAGxD,OAAO,CAAC+C,WAAW,GAAGV,KAAK;IAC3CrC,OAAO,CAAC8C,UAAU,GAAGS,GAAG,IAAIC,OAAO,GAAGA,OAAO,GAAGD,GAAG;EACvD;EACArC,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAC,IAAI,CAACrB,UAAU,EAAE;MAClB;IACJ;IACA,IAAI,CAACyB,sBAAsB,CAAC,CAAC;IAC7B;IACA,IAAI,CAACb,2BAA2B,GAAGgD,UAAU,CAAC,MAAM;MAChD,MAAM7D,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC+D,SAAS,CAAEC,QAAQ,IAAK,IAAI,CAACnF,QAAQ,CAACmF,QAAQ,CAAC,CAAC;MAC9E,IAAI/D,UAAU,KAAK,CAAC,CAAC,EAAE;QACnB,IAAI,CAACqD,eAAe,CAACrD,UAAU,CAAC;MACpC;IACJ,CAAC,CAAC;EACN;EACA0B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACb,2BAA2B,EAAE;MAClCmD,YAAY,CAAC,IAAI,CAACnD,2BAA2B,CAAC;MAC9C,IAAI,CAACA,2BAA2B,GAAG,IAAI;IAC3C;EACJ;EACAU,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACtB,UAAU,EAAE;MACjB;MACA;MACAgE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAClB,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACnD,EAAE,CAACuC,YAAY,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;EACJ;EACA,OAAO+B,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF5E,OAAO,EAAjBpJ,EAAE,CAAAiO,iBAAA,CAAiC/N,WAAW,GAA9CF,EAAE,CAAAiO,iBAAA,CAAyDrN,EAAE,CAACsN,MAAM,GAApElO,EAAE,CAAAiO,iBAAA,CAA+ErN,EAAE,CAACuN,cAAc,GAAlGnO,EAAE,CAAAiO,iBAAA,CAA6GjO,EAAE,CAACoO,iBAAiB;EAAA;EAC5N,OAAOC,IAAI,kBAD8ErO,EAAE,CAAAsO,iBAAA;IAAAC,IAAA,EACJnF,OAAO;IAAAoF,SAAA;IAAAC,cAAA,WAAAC,uBAAA7M,EAAA,EAAAC,GAAA,EAAA6M,QAAA;MAAA,IAAA9M,EAAA;QADL7B,EAAE,CAAA4O,cAAA,CAAAD,QAAA,EACkT7N,aAAa;MAAA;MAAA,IAAAe,EAAA;QAAA,IAAAgN,EAAA;QADjU7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAAjN,GAAA,CAAAqI,SAAA,GAAA0E,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,cAAApN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF7B,EAAE,CAAAkP,WAAA,CAAA3N,GAAA;QAAFvB,EAAE,CAAAkP,WAAA,CAAA1N,GAAA;QAAFxB,EAAE,CAAAkP,WAAA,CAAAzN,GAAA;QAAFzB,EAAE,CAAAkP,WAAA,CAAAxN,GAAA;QAAF1B,EAAE,CAAAkP,WAAA,CAAAvN,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAAgN,EAAA;QAAF7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAAjN,GAAA,CAAAgI,OAAA,GAAA+E,EAAA,CAAAM,KAAA;QAAFnP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAAjN,GAAA,CAAAiI,MAAA,GAAA8E,EAAA,CAAAM,KAAA;QAAFnP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAAjN,GAAA,CAAAkI,MAAA,GAAA6E,EAAA,CAAAM,KAAA;QAAFnP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAAjN,GAAA,CAAAmI,OAAA,GAAA4E,EAAA,CAAAM,KAAA;QAAFnP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAAjN,GAAA,CAAAoI,OAAA,GAAA2E,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAA5F,KAAA;MAAAC,UAAA;MAAAC,UAAA;MAAAC,KAAA;MAAAxB,KAAA;MAAAD,UAAA;IAAA;IAAAmH,OAAA;MAAAzF,gBAAA;IAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA5E,QAAA,WAAA6E,iBAAA7N,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF7B,EAAE,CAAAsC,cAAA,YAEsC,CAAC,YAAD,CAAC;QAFzCtC,EAAE,CAAAkC,UAAA,IAAAC,yBAAA,mBAOvE,CAAC;QAPoEnC,EAAE,CAAAsC,cAAA,eAQR,CAAC;QARKtC,EAAE,CAAAuC,UAAA,oBAAAoN,uCAAA1K,MAAA;UAAA,OAQzBnD,GAAA,CAAAsL,QAAA,CAAAnI,MAAe,CAAC;QAAA,EAAC;QARMjF,EAAE,CAAAsC,cAAA,cASnB,CAAC;QATgBtC,EAAE,CAAAkC,UAAA,IAAA8F,qBAAA,gBAwEnE,CAAC;QAxEgEhI,EAAE,CAAA+B,SAAA,cAyE7B,CAAC;QAzE0B/B,EAAE,CAAA8C,YAAA,CA0EvE,CAAC,CAAD,CAAC;QA1EoE9C,EAAE,CAAAkC,UAAA,KAAA0G,0BAAA,oBA+EvE,CAAC;QA/EoE5I,EAAE,CAAA8C,YAAA,CAgF9E,CAAC,CAAD,CAAC;MAAA;MAAA,IAAAjB,EAAA;QAhF2E7B,EAAE,CAAAkI,UAAA,CAAApG,GAAA,CAAAqG,UAEqC,CAAC;QAFxCnI,EAAE,CAAAiD,UAAA,YAAFjD,EAAE,CAAA4P,eAAA,IAAAzG,GAAA,EAAArH,GAAA,CAAA6H,UAAA,CAEF,CAAC,YAAA7H,GAAA,CAAAsG,KAAD,CAAC;QAFDpI,EAAE,CAAAgD,SAAA,EAIhC,CAAC;QAJ6BhD,EAAE,CAAAiD,UAAA,SAAAnB,GAAA,CAAA6H,UAAA,KAAA7H,GAAA,CAAAuI,kBAIhC,CAAC;QAJ6BrK,EAAE,CAAAgD,SAAA,EAWzC,CAAC;QAXsChD,EAAE,CAAAiD,UAAA,YAAAnB,GAAA,CAAA2H,KAWzC,CAAC;QAXsCzJ,EAAE,CAAAgD,SAAA,EA4EjC,CAAC;QA5E8BhD,EAAE,CAAAiD,UAAA,SAAAnB,GAAA,CAAA6H,UAAA,KAAA7H,GAAA,CAAAwI,iBA4EjC,CAAC;MAAA;IAAA;IAAAuF,YAAA,WAAAA,CAAA;MAAA,QAM++BhQ,EAAE,CAACiQ,OAAO,EAA2HjQ,EAAE,CAACkQ,OAAO,EAA0JlQ,EAAE,CAACmQ,IAAI,EAAoInQ,EAAE,CAACoQ,gBAAgB,EAA2LpQ,EAAE,CAACqQ,OAAO,EAAkHtP,EAAE,CAACuP,UAAU,EAAmQvP,EAAE,CAACwP,gBAAgB,EAAqPjP,EAAE,CAACkP,MAAM,EAA6FhP,EAAE,CAACiP,OAAO,EAAoXrP,eAAe,EAAmGC,gBAAgB;IAAA;IAAAqP,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC16F;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApF6F1Q,EAAE,CAAA2Q,iBAAA,CAoFJvH,OAAO,EAAc,CAAC;IACrGmF,IAAI,EAAEpO,SAAS;IACfyQ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAEhG,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE4F,eAAe,EAAErQ,uBAAuB,CAAC0Q,MAAM;MAAEN,aAAa,EAAEnQ,iBAAiB,CAAC0Q,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,87BAA87B;IAAE,CAAC;EACz9B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhC,IAAI,EAAE2C,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9D5C,IAAI,EAAEjO,MAAM;QACZsQ,IAAI,EAAE,CAAC1Q,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEqO,IAAI,EAAE3N,EAAE,CAACsN;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAE3N,EAAE,CAACuN;IAAe,CAAC,EAAE;MAAEI,IAAI,EAAEvO,EAAE,CAACoO;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE3E,KAAK,EAAE,CAAC;MACtH8E,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAEmJ,UAAU,EAAE,CAAC;MACb6E,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAEoJ,UAAU,EAAE,CAAC;MACb4E,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAEqJ,KAAK,EAAE,CAAC;MACR2E,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE6H,KAAK,EAAE,CAAC;MACRmG,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAE4H,UAAU,EAAE,CAAC;MACboG,IAAI,EAAEhO;IACV,CAAC,CAAC;IAAEsJ,gBAAgB,EAAE,CAAC;MACnB0E,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEsJ,OAAO,EAAE,CAAC;MACVyE,IAAI,EAAE9N,SAAS;MACfmQ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE7G,MAAM,EAAE,CAAC;MACTwE,IAAI,EAAE9N,SAAS;MACfmQ,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE5G,MAAM,EAAE,CAAC;MACTuE,IAAI,EAAE9N,SAAS;MACfmQ,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE3G,OAAO,EAAE,CAAC;MACVsE,IAAI,EAAE9N,SAAS;MACfmQ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE1G,OAAO,EAAE,CAAC;MACVqE,IAAI,EAAE9N,SAAS;MACfmQ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEzG,SAAS,EAAE,CAAC;MACZoE,IAAI,EAAE7N,eAAe;MACrBkQ,IAAI,EAAE,CAAC9P,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMsQ,aAAa,CAAC;EAChB,OAAOtD,IAAI,YAAAuD,sBAAArD,CAAA;IAAA,YAAAA,CAAA,IAAwFoD,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAhN8EtR,EAAE,CAAAuR,gBAAA;IAAAhD,IAAA,EAgNS6C;EAAa;EACjH,OAAOI,IAAI,kBAjN8ExR,EAAE,CAAAyR,gBAAA;IAAAC,OAAA,GAiNkC3R,YAAY,EAAEc,YAAY,EAAEE,YAAY,EAAEK,YAAY,EAAEE,aAAa,EAAEL,eAAe,EAAEC,gBAAgB,EAAEL,YAAY,EAAEE,YAAY,EAAEO,aAAa;EAAA;AACpR;AACA;EAAA,QAAAoP,SAAA,oBAAAA,SAAA,KAnN6F1Q,EAAE,CAAA2Q,iBAAA,CAmNJS,aAAa,EAAc,CAAC;IAC3G7C,IAAI,EAAE5N,QAAQ;IACdiQ,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAAC3R,YAAY,EAAEc,YAAY,EAAEE,YAAY,EAAEK,YAAY,EAAEE,aAAa,EAAEL,eAAe,EAAEC,gBAAgB,CAAC;MACnHyQ,OAAO,EAAE,CAACvI,OAAO,EAAEvI,YAAY,EAAEE,YAAY,EAAEO,aAAa,CAAC;MAC7DsQ,YAAY,EAAE,CAACxI,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,OAAO,EAAEgI,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}