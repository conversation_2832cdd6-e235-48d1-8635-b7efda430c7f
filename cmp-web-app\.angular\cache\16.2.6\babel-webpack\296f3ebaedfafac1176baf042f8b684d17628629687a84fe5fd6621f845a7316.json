{"ast": null, "code": "export default {\n  label: {\n    isdn: \"<PERSON><PERSON> thuê bao\",\n    customerName: \"<PERSON><PERSON><PERSON><PERSON> hàng\",\n    activeDate: \"<PERSON><PERSON><PERSON> hiệu lực\",\n    expiredDate: \"<PERSON><PERSON><PERSON> hết hạn\",\n    time: \"Thời gian\",\n    ratingPlan: \"<PERSON><PERSON><PERSON> cước\",\n    actionType: \"Loại hành động\",\n    status: \"Trạng thái\",\n    modifyBy: \"Tài khoản thực hiện\",\n    fromDate: \"Từ ngày\",\n    toDate: \"Đến ngày\"\n  },\n  status: {\n    success: \"Thành công\",\n    unsuccessful: \"Thất bại\"\n  },\n  actionType: {\n    assignPlan: \"<PERSON>án gói cước\",\n    switchPlan: \"Đổi gói cước\"\n  }\n};", "map": {"version": 3, "names": ["label", "isdn", "customerName", "activeDate", "expiredDate", "time", "ratingPlan", "actionType", "status", "modifyBy", "fromDate", "toDate", "success", "unsuccessful", "assignPlan", "switchPlan"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\history-register-plan.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        isdn: \"<PERSON><PERSON> thuê bao\",\r\n        customerName: \"<PERSON><PERSON><PERSON><PERSON> hàng\",\r\n        activeDate: \"<PERSON><PERSON><PERSON> hiệu lực\",\r\n        expiredDate: \"<PERSON><PERSON><PERSON> hết hạn\",\r\n        time: \"Thờ<PERSON> gian\",\r\n        ratingPlan: \"<PERSON><PERSON>i cước\",\r\n        actionType: \"Loại hành động\",\r\n        status: \"Trạng thái\",\r\n        modifyBy: \"Tài khoản thực hiện\",\r\n        fromDate: \"Từ ngày\",\r\n        toDate: \"Đến ngày\",\r\n\r\n    },\r\n    status: {\r\n        success: \"Thành công\",\r\n        unsuccessful: \"Thất bại\"\r\n    },\r\n    actionType: {\r\n        assignPlan: \"Gán gói cước\",\r\n        switchPlan: \"Đổi gói cước\"\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,IAAI,EAAE,aAAa;IACnBC,YAAY,EAAE,YAAY;IAC1BC,UAAU,EAAE,eAAe;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,gBAAgB;IAC5BC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,qBAAqB;IAC/BC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE;GAEX;EACDH,MAAM,EAAE;IACJI,OAAO,EAAE,YAAY;IACrBC,YAAY,EAAE;GACjB;EACDN,UAAU,EAAE;IACRO,UAAU,EAAE,cAAc;IAC1BC,UAAU,EAAE;;CAGnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}