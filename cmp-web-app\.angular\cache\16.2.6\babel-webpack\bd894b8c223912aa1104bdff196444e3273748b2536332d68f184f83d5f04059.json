{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/app.layout.service\";\nimport * as i2 from \"./app.menu.component\";\nexport class AppSidebarComponent {\n  constructor(layoutService, el) {\n    this.layoutService = layoutService;\n    this.el = el;\n  }\n  static {\n    this.ɵfac = function AppSidebarComponent_Factory(t) {\n      return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-menu\");\n        }\n      },\n      dependencies: [i2.AppMenuComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "constructor", "layoutService", "el", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "selectors", "decls", "vars", "template", "AppSidebarComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\layout\\app.sidebar.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\layout\\app.sidebar.component.html"], "sourcesContent": ["import { Component, ElementRef } from '@angular/core';\r\nimport { LayoutService } from \"src/app/service/app.layout.service\";\r\n\r\n@Component({\r\n    selector: 'app-sidebar',\r\n    templateUrl: './app.sidebar.component.html'\r\n})\r\nexport class AppSidebarComponent {\r\n    constructor(public layoutService: LayoutService, public el: ElementRef) { }\r\n}\r\n\r\n", " <app-menu></app-menu>\r\n"], "mappings": ";;;AAOA,OAAM,MAAOA,mBAAmB;EAC5BC,YAAmBC,aAA4B,EAASC,EAAc;IAAnD,KAAAD,aAAa,GAAbA,aAAa;IAAwB,KAAAC,EAAE,GAAFA,EAAE;EAAgB;;;uBADjEH,mBAAmB,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA;IAAA;EAAA;;;YAAnBR,mBAAmB;MAAAS,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP/BV,EAAA,CAAAY,SAAA,eAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}