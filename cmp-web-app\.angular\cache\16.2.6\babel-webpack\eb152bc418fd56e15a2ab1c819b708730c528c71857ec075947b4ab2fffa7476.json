{"ast": null, "code": "import { ComponentBase } from 'src/app/component.base';\nimport { GroupSimService } from 'src/app/service/group-sim/GroupSimService';\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { CustomerService } from \"../../../service/customer/CustomerService\";\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport { SimService } from \"../../../service/sim/SimService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../service/account/AccountService\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../common-module/table/table.component\";\nimport * as i8 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i9 from \"primeng/splitbutton\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"primeng/dialog\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/panel\";\nimport * as i14 from \"src/app/service/group-sim/GroupSimService\";\nimport * as i15 from \"../../../service/customer/CustomerService\";\nimport * as i16 from \"../../../service/sim/SimService\";\nfunction GroupSimComponent_p_splitButton_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-splitButton\", 48);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.add\"))(\"model\", ctx_r0.itemAddGroups);\n  }\n}\nfunction GroupSimComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"span\", 9)(2, \"p-dropdown\", 49);\n    i0.ɵɵlistener(\"ngModelChange\", function GroupSimComponent_div_19_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.searchInfo.scope = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfo.scope)(\"options\", ctx_r1.groupScopes);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"groupSim.label.groupScope\"));\n  }\n}\nfunction GroupSimComponent_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function GroupSimComponent_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.showOverLayEdit());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"groupSim.label.buttonEdit\"));\n  }\n}\nfunction GroupSimComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function GroupSimComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.deleteGroupSim());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"global.button.delete\"));\n  }\n}\nfunction GroupSimComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function GroupSimComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.removeMany());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectItemsDetail.length == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"groupSim.label.buttonDelete\"));\n  }\n}\nfunction GroupSimComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function GroupSimComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.showOverLayAddSim());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"groupSim.label.buttonAddSim\"));\n  }\n}\nfunction GroupSimComponent_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"groupSim.scope.admin\"));\n  }\n}\nfunction GroupSimComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"groupSim.scope.province\"));\n  }\n}\nfunction GroupSimComponent_span_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"groupSim.scope.customer\"));\n  }\n}\nfunction GroupSimComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 31)(2, \"label\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.tranService.translate(\"groupSim.label.customer\"), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.customer, \" \");\n  }\n}\nfunction GroupSimComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 31)(2, \"label\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r10.tranService.translate(\"groupSim.label.contractCode\"), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.contractCode, \" \");\n  }\n}\nfunction GroupSimComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 31)(2, \"label\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.tranService.translate(\"account.label.province\"), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.province, \" \");\n  }\n}\nfunction GroupSimComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"label\", 29)(3, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return [a0];\n};\nconst _c1 = function () {\n  return {\n    width: \"980px\",\n    height: \"720px\"\n  };\n};\nconst _c2 = function () {\n  return {\n    width: \"45vw\"\n  };\n};\nconst _c3 = function () {\n  return {\n    \"960px\": \"75vw\"\n  };\n};\nexport class GroupSimComponent extends ComponentBase {\n  constructor(groupSimService, customerService, simService, accountService, injector) {\n    super(injector);\n    this.groupSimService = groupSimService;\n    this.customerService = customerService;\n    this.simService = simService;\n    this.accountService = accountService;\n    this.buttonAdd = this.tranService.translate(\"groupSim.label.buttonAdd\");\n    this.tableLabel = this.tranService.translate(\"groupSim.breadCrumb.group\");\n    this.isShowModalDetailGroupSim = false;\n    this.groupScopeObjects = CONSTANTS.GROUP_SCOPE;\n    this.paramSearchSim = {};\n    this.boxSimAddController = new ComboLazyControl();\n    this.displayAddSim = false;\n    this.selectedSimItems = [];\n    this.headerSim = this.tranService.translate(\"groupSim.label.buttonAddSim\");\n    this.placeholderSIM = this.tranService.translate(\"groupSim.placeHolder.addSim\");\n    this.buttonSaveSimToGroup = this.tranService.translate(\"global.button.save\");\n    this.allPermissions = CONSTANTS.PERMISSIONS;\n    this.CONSTANTS = CONSTANTS;\n  }\n  convertToDDMMYYYY(input) {\n    const date = new Date(input);\n    const day = date.getUTCDate().toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị\n    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị và +1 vì getMonth() trả về từ 0-11\n    const year = date.getUTCFullYear().toString();\n    return `${day}/${month}/${year}`;\n  }\n  ngOnInit() {\n    let me = this;\n    this.userType = this.sessionService.userInfo.type;\n    this.isShowGroupScope = [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE].includes(this.userType);\n    this.items = [{\n      label: this.tranService.translate(`global.menu.simmgmt`)\n    }, {\n      label: this.tranService.translate(\"groupSim.breadCrumb.group\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.searchInfo = {};\n    this.optionTableDetail = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: 'pi pi-fw pi-trash',\n        tooltip: this.tranService.translate('global.button.delete'),\n        func: id => {\n          let ids = [id];\n          this.removeSim(ids);\n        }\n      }]\n    };\n    this.pageNumberDetail = 0;\n    this.pageSizeDetail = 10;\n    this.sortDetail = 'msisdn,asc';\n    this.selectItemsDetail = [];\n    this.dataSetDetail = {\n      content: [],\n      total: 0\n    };\n    this.columnsDetail = [{\n      name: this.tranService.translate('groupSim.detail.subNumber'),\n      key: 'msisdn',\n      size: '20%',\n      align: 'left',\n      isShow: true,\n      isSort: true,\n      style: {\n        color: 'var(--mainColorText)'\n      },\n      funcGetRouting(item) {\n        return [\"/sims/detail/\" + item.msisdn];\n      }\n    }, {\n      name: \"IMSI\",\n      key: 'imsi',\n      size: '20%',\n      align: 'left',\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.trangthaisim\"),\n      key: \"status\",\n      size: \"20%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == 0) {\n          return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n          return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n        }\n        return [];\n      },\n      funcConvertText: value => {\n        if (value == 0) {\n          return me.tranService.translate(\"sim.status.inventory\");\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return me.tranService.translate(\"sim.status.ready\");\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return me.tranService.translate(\"sim.status.deactivated\");\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return me.tranService.translate(\"sim.status.purged\");\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return me.tranService.translate(\"sim.status.inactivated\");\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingChangePlan\");\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n        }\n        return \"\";\n      },\n      style: {\n        color: \"white\"\n      }\n    }, {\n      name: this.tranService.translate('groupSim.detail.planName'),\n      key: 'ratingPlanName',\n      size: '20%',\n      align: 'left',\n      isShow: true,\n      isSort: false\n    }];\n    this.groupScopes = [{\n      value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\n      name: this.tranService.translate(\"groupSim.scope.customer\")\n    }];\n    this.itemAddGroups = [{\n      label: this.tranService.translate(\"groupSim.scope.customer\"),\n      command: () => {\n        me.create.apply(me, [2]);\n      }\n    }];\n    let groupScopeForAdmin = [{\n      value: CONSTANTS.GROUP_SCOPE.GROUP_ADMIN,\n      name: this.tranService.translate(\"groupSim.scope.admin\")\n    }, {\n      value: CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE,\n      name: this.tranService.translate(\"groupSim.scope.province\")\n    }, {\n      value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\n      name: this.tranService.translate(\"groupSim.scope.customer\")\n    }];\n    let groupScopeForProvince = [{\n      value: CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE,\n      name: this.tranService.translate(\"groupSim.scope.province\")\n    }, {\n      value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\n      name: this.tranService.translate(\"groupSim.scope.customer\")\n    }];\n    let groupScopeForManager = [{\n      value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\n      name: this.tranService.translate(\"groupSim.scope.customer\")\n    }];\n    if (this.userType == CONSTANTS.USER_TYPE.ADMIN) {\n      this.groupScopes = groupScopeForAdmin;\n      this.itemAddGroups = [{\n        label: this.tranService.translate(\"groupSim.scope.admin\"),\n        command: () => {\n          me.create.apply(me, [0]);\n        }\n      }, {\n        label: this.tranService.translate(\"groupSim.scope.province\"),\n        command: () => {\n          me.create.apply(me, [1]);\n        }\n      }, {\n        label: this.tranService.translate(\"groupSim.scope.customer\"),\n        command: () => {\n          me.create.apply(me, [2]);\n        }\n      }];\n    } else if (this.userType == CONSTANTS.USER_TYPE.PROVINCE) {\n      this.groupScopes = groupScopeForProvince;\n      this.itemAddGroups = [{\n        label: this.tranService.translate(\"groupSim.scope.province\"),\n        command: () => {\n          me.create.apply(me, [1]);\n        }\n      }, {\n        label: this.tranService.translate(\"groupSim.scope.customer\"),\n        command: () => {\n          me.create.apply(me, [2]);\n        }\n      }];\n    } else if (this.userType == CONSTANTS.USER_TYPE.DISTRICT) {\n      this.groupScopes = groupScopeForManager;\n      this.itemAddGroups = [{\n        label: this.tranService.translate(\"groupSim.scope.customer\"),\n        command: () => {\n          me.create.apply(me, [2]);\n        }\n      }];\n    }\n    this.columns = [{\n      name: this.tranService.translate(\"groupSim.label.groupKey\"),\n      key: \"groupKey\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\",\n        'min-width': '300px'\n      },\n      funcClick(id, item) {\n        me.isShowModalDetailGroupSim = true;\n        me.selectItemsDetail = [];\n        me.idForEdit = id;\n        me.searchDetail(0, me.pageSizeDetail, me.sortDetail, null);\n        me.groupSimService.getSimGroupById(me.idForEdit, {}, {}, response => {\n          me.groupKey = response.groupKey;\n          me.customer = response.customer;\n          me.groupName = response.name;\n          me.description = response.description;\n          me.groupScope = response.scope;\n          me.customerCode = response.customerCode;\n          me.contractCode = response.contractCode;\n          me.provinceCode = response.provinceCode;\n          if (me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER) {\n            me.paramSearchSim = {\n              customer: me.customerCode,\n              contractCode: me.contractCode ? me.contractCode : \" \"\n            };\n            me.customerService.getByKey(\"customerCode\", me.customerCode, res => {\n              me.customer = `${res[0].customerName} - ${res[0].customerCode}`;\n            });\n          }\n          if (me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE) {\n            me.paramSearchSim = {\n              provinceCode: me.provinceCode\n            };\n            me.accountService.getListProvince(res => {\n              (res || []).forEach(el => {\n                if (el.code == response.provinceCode) {\n                  me.province = `${el.name} (${el.code})`;\n                }\n              });\n            });\n          }\n        }, null, () => {\n          me.messageCommonService.offload();\n        });\n      }\n    }, {\n      name: this.tranService.translate(\"groupSim.label.groupName\"),\n      key: \"name\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        'min-width': '300px'\n      }\n    }, {\n      name: this.tranService.translate(\"groupSim.label.groupScope\"),\n      key: \"scope\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN) {\n          return me.tranService.translate(\"groupSim.scope.admin\");\n        } else if (value == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE) {\n          return me.tranService.translate(\"groupSim.scope.province\");\n        } else if (value == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER) {\n          return me.tranService.translate(\"groupSim.scope.customer\");\n        }\n        return \"\";\n      },\n      style: {\n        'min-width': '300px'\n      }\n    }, {\n      name: this.tranService.translate(\"groupSim.label.time\"),\n      key: \"createdDate\",\n      size: \"200px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-fw pi-pencil\",\n        tooltip: this.tranService.translate('global.button.edit'),\n        func: id => {\n          // console.log(id)\n          this.router.navigate(['/sims/group/update', id]);\n        },\n        funcAppear: (id, item) => {\n          return me.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE]);\n        }\n      }, {\n        icon: \"pi pi-fw pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: id => {\n          me.messageCommonService.confirm(this.tranService.translate(\"groupSim.label.confirmDelete\"), this.tranService.translate(\"groupSim.label.deleteTextGroup\"), {\n            ok: () => {\n              this.onDelete(id);\n            },\n            cancel: () => {}\n          });\n        },\n        funcAppear: (id, item) => {\n          return me.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.DELETE]);\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"createdDate,desc\";\n    this.selectItems = [];\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.groupSimService.searchSimGroup({}, {\n      ...this.searchInfo,\n      sort: this.sort\n    }, response => {\n      this.dataSet.content = response.content.map(item => {\n        item.createdDate = this.convertToDDMMYYYY(item.createdDate);\n        return item;\n      });\n      this.dataSet.total = response.totalElements;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  onSearch() {\n    // console.log(this.searchInfo);\n    this.search(0, this.pageSize, this.sort, this.searchInfo);\n  }\n  onDelete(id) {\n    let me = this;\n    // console.log(id)\n    this.groupSimService.deleteSimGroup(id, {}, {}, response => {\n      me.messageCommonService.success(this.tranService.translate(\"global.message.deleteSuccess\"));\n      // me.selectItems = me.selectItems.filter(el => el.id != id);\n      this.search(0, this.pageSize, this.sort, this.searchInfo);\n    }, error => {\n      console.error(error);\n    }, () => {});\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    if (this.searchInfo.scope == null) this.searchInfo.scope = \"\";\n    let dataParam = {\n      ...params,\n      page,\n      size: limit,\n      sort\n    };\n    me.messageCommonService.onload();\n    this.groupSimService.searchSimGroup({}, dataParam, response => {\n      this.dataSet.content = response.content.map(item => {\n        item.createdDate = this.convertToDDMMYYYY(item.createdDate);\n        return item;\n      });\n      this.dataSet.total = response.totalElements;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  searchDetail(page, limit, sort, params) {\n    let me = this;\n    this.pageNumberDetail = page;\n    this.pageSizeDetail = limit;\n    this.sortDetail = sort;\n    let dataParam = {\n      page,\n      size: limit,\n      sort\n    };\n    this.groupSimService.getListDetailGroupSim(this.idForEdit, {}, dataParam, response => {\n      me.dataSetDetail.content = response.content;\n      me.dataSetDetail.total = response.totalElements;\n    });\n  }\n  create(type) {\n    this.router.navigate(['/sims/group/create'], {\n      queryParams: {\n        type\n      }\n    });\n  }\n  showOverLayAddSim() {\n    this.boxSimAddController.reload();\n    this.displayAddSim = true;\n    this.selectedSimItems = [];\n  }\n  showOverLayEdit() {\n    this.router.navigate([\"/sims/group/update\", this.idForEdit]);\n  }\n  removeMany() {\n    // console.log(this.selectItems)\n    if (this.selectItemsDetail.length == 0) return null;\n    this.idForDelete = [];\n    this.selectItemsDetail.map(item => {\n      this.idForDelete.push(item.msisdn);\n    });\n    this.removeSim(this.idForDelete);\n  }\n  removeSim(ids) {\n    this.messageCommonService.confirm(this.tranService.translate(\"groupSim.label.confirmDelete\"), this.tranService.translate(\"groupSim.label.deleteTextSim\"), {\n      ok: () => {\n        this.simService.removeSIMFromGroup(ids, parseInt(this.idForEdit), response => {\n          // console.log(response);\n          this.selectItemsDetail = [];\n          this.messageCommonService.success(this.tranService.translate(\"global.message.deleteSuccess\"));\n          this.searchDetail(0, this.pageSizeDetail, this.sortDetail, null);\n        });\n      },\n      cancel: () => {}\n    });\n  }\n  deleteGroupSim() {\n    let me = this;\n    this.messageCommonService.confirm(this.tranService.translate(\"groupSim.label.deleteTextGroup\"), this.tranService.translate(\"groupSim.label.confirmDelete\"), {\n      ok: () => {\n        me.groupSimService.deleteSimGroup(me.idForEdit, {}, {}, response => {\n          me.messageCommonService.success(me.tranService.translate(\"global.message.success\"));\n          me.router.navigate(['/sims/group']);\n        });\n      }\n    });\n  }\n  loadSimNotInGroup(data, callback) {\n    this.simService.searchNotInGroup(data, callback);\n  }\n  handleModelClose() {\n    this.displayAddSim = false;\n  }\n  handleSavetoGroup() {\n    if (this.selectedSimItems.length > 0) {\n      this.simService.pushSimToGroup(this.selectedSimItems, {\n        id: parseInt(this.idForEdit)\n      }, response => {\n        this.messageCommonService.success(this.tranService.translate(\"global.message.addGroupSuccess\"));\n        this.searchDetail(0, this.pageSize, this.sort, null);\n      });\n      this.displayAddSim = false;\n    }\n  }\n  static {\n    this.ɵfac = function GroupSimComponent_Factory(t) {\n      return new (t || GroupSimComponent)(i0.ɵɵdirectiveInject(GroupSimService), i0.ɵɵdirectiveInject(CustomerService), i0.ɵɵdirectiveInject(SimService), i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupSimComponent,\n      selectors: [[\"app-group-sim\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 79,\n      vars: 88,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-success\", \"icon\", \"pi pi-plus\", 3, \"label\", \"model\", 4, \"ngIf\"], [1, \"vnpt-field-set\", 3, \"toggleable\", \"header\", \"styleClass\"], [1, \"grid\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"id\", \"groupKey\", 1, \"w-full\", 3, \"ngModel\", \"keyup.enter\", \"ngModelChange\"], [\"htmlFor\", \"groupKey\"], [\"pInputText\", \"\", \"id\", \"name\", 1, \"w-full\", 3, \"ngModel\", \"keyup.enter\", \"ngModelChange\"], [\"htmlFor\", \"groupName\"], [\"class\", \"col-3\", 4, \"ngIf\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", 3, \"click\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [\"styleClass\", \"custom-dialog\", 3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\"], [\"pButton\", \"\", \"class\", \"p-button-info mr-2\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"class\", \"p-button-secondary mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"col-5\", \"flex\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\", \"responsive-button-container\"], [\"pButton\", \"\", \"class\", \"p-button-secondary mr-2 center-button equal-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"class\", \"p-button-success mr-2 equal-button\", 3, \"click\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"grid\", \"dialog-sim-list-grid-1\"], [1, \"col-6\", \"pt-0\", \"pb-0\"], [1, \"flex-1\", \"flex\", \"justify-content-between\", \"col-12\", \"sm:col-8\", \"md:col-12\", \"py-0\"], [\"htmlFor\", \"groupCode\", 1, \"m-0\", \"p-0\", \"text-lg\", \"font-medium\", 2, \"min-width\", \"130px\", \"align-self\", \"flex-end\"], [1, \"col-9\", \"sm:col-6\", \"md:col-10\", \"py-0\", \"text-lg\", \"font-medium\"], [1, \"flex-1\", \"flex\", \"justify-content-between\", \"col-12\", \"md:col-12\", \"py-0\"], [\"htmlFor\", \"groupScope\", 1, \"m-0\", \"p-0\", \"text-lg\", \"font-medium\", 2, \"min-width\", \"130px\", \"align-self\", \"flex-end\"], [1, \"col-9\", \"md:col-10\", \"py-0\", \"text-lg\", \"font-medium\", \"span-responsive\"], [4, \"ngIf\"], [1, \"col-9\", \"md:col-10\", \"pb-0\", \"text-lg\", \"font-medium\"], [\"class\", \"col-6 pt-0 pb-0\", 4, \"ngIf\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"selectItemsChange\"], [\"showEffect\", \"fade\", 3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"breakpoints\", \"visibleChange\"], [\"overlayGanSim\", \"\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"col-12\", \"md:col-12\", \"py-0\"], [\"htmlFor\", \"groupCode\", 1, \"my-auto\", 2, \"min-width\", \"150px\"], [1, \"text-red-500\"], [1, \"flex-grow-1\"], [\"objectKey\", \"sim\", \"paramKey\", \"msisdn\", \"keyReturn\", \"msisdn\", \"displayPattern\", \"${msisdn}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"paramDefault\", \"loadData\", \"valueChange\"], [1, \"pt-4\", \"flex\", \"flex-row\", \"gap-3\", \"justify-content-center\"], [\"pButton\", \"\", 1, \"p-button-secondary\", \"p-button-outlined\", 3, \"click\"], [\"pButton\", \"\", 3, \"label\", \"disabled\", \"click\"], [\"styleClass\", \"p-button-success\", \"icon\", \"pi pi-plus\", 3, \"label\", \"model\"], [\"styleClass\", \"w-full\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"scope\"], [\"pButton\", \"\", 1, \"p-button-info\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", 1, \"p-button-secondary\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", 1, \"p-button-secondary\", \"mr-2\", \"center-button\", \"equal-button\", 3, \"disabled\", \"click\"], [\"pButton\", \"\", 1, \"p-button-success\", \"mr-2\", \"equal-button\", 3, \"click\"]],\n      template: function GroupSimComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, GroupSimComponent_p_splitButton_6_Template, 1, 2, \"p-splitButton\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"p-panel\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"span\", 9)(11, \"input\", 10);\n          i0.ɵɵlistener(\"keyup.enter\", function GroupSimComponent_Template_input_keyup_enter_11_listener() {\n            return ctx.onSearch();\n          })(\"ngModelChange\", function GroupSimComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.searchInfo.groupKey = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"label\", 11);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"span\", 9)(16, \"input\", 12);\n          i0.ɵɵlistener(\"keyup.enter\", function GroupSimComponent_Template_input_keyup_enter_16_listener() {\n            return ctx.onSearch();\n          })(\"ngModelChange\", function GroupSimComponent_Template_input_ngModelChange_16_listener($event) {\n            return ctx.searchInfo.name = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"label\", 13);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(19, GroupSimComponent_div_19_Template, 5, 5, \"div\", 14);\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"p-button\", 16);\n          i0.ɵɵlistener(\"click\", function GroupSimComponent_Template_p_button_click_21_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"table-vnpt\", 17);\n          i0.ɵɵlistener(\"selectItemsChange\", function GroupSimComponent_Template_table_vnpt_selectItemsChange_22_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p-dialog\", 18);\n          i0.ɵɵlistener(\"visibleChange\", function GroupSimComponent_Template_p_dialog_visibleChange_23_listener($event) {\n            return ctx.isShowModalDetailGroupSim = $event;\n          });\n          i0.ɵɵelementStart(24, \"div\", 0)(25, \"div\", 19);\n          i0.ɵɵtemplate(26, GroupSimComponent_button_26_Template, 2, 1, \"button\", 20);\n          i0.ɵɵtemplate(27, GroupSimComponent_button_27_Template, 2, 1, \"button\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 22);\n          i0.ɵɵtemplate(29, GroupSimComponent_button_29_Template, 2, 2, \"button\", 23);\n          i0.ɵɵtemplate(30, GroupSimComponent_button_30_Template, 2, 1, \"button\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"p-card\", 25)(32, \"div\", 26)(33, \"div\", 27)(34, \"div\", 28)(35, \"label\", 29);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 30);\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 27)(40, \"div\", 31)(41, \"label\", 32);\n          i0.ɵɵtext(42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 33);\n          i0.ɵɵtemplate(44, GroupSimComponent_span_44_Template, 2, 1, \"span\", 34);\n          i0.ɵɵtemplate(45, GroupSimComponent_span_45_Template, 2, 1, \"span\", 34);\n          i0.ɵɵtemplate(46, GroupSimComponent_span_46_Template, 2, 1, \"span\", 34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 27)(48, \"div\", 31)(49, \"label\", 29);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 35);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(53, GroupSimComponent_div_53_Template, 6, 2, \"div\", 36);\n          i0.ɵɵtemplate(54, GroupSimComponent_div_54_Template, 6, 2, \"div\", 36);\n          i0.ɵɵtemplate(55, GroupSimComponent_div_55_Template, 6, 2, \"div\", 36);\n          i0.ɵɵtemplate(56, GroupSimComponent_div_56_Template, 4, 0, \"div\", 36);\n          i0.ɵɵelementStart(57, \"div\", 27)(58, \"div\", 31)(59, \"label\", 29);\n          i0.ɵɵtext(60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 35);\n          i0.ɵɵtext(62);\n          i0.ɵɵelementStart(63, \"span\");\n          i0.ɵɵtext(64, \"\\u00A0\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(65, \"table-vnpt\", 37);\n          i0.ɵɵlistener(\"selectItemsChange\", function GroupSimComponent_Template_table_vnpt_selectItemsChange_65_listener($event) {\n            return ctx.selectItemsDetail = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"p-dialog\", 38, 39);\n          i0.ɵɵlistener(\"visibleChange\", function GroupSimComponent_Template_p_dialog_visibleChange_66_listener($event) {\n            return ctx.displayAddSim = $event;\n          });\n          i0.ɵɵelementStart(68, \"div\", 40)(69, \"label\", 41);\n          i0.ɵɵtext(70);\n          i0.ɵɵelementStart(71, \"span\", 42);\n          i0.ɵɵtext(72, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 43)(74, \"vnpt-select\", 44);\n          i0.ɵɵlistener(\"valueChange\", function GroupSimComponent_Template_vnpt_select_valueChange_74_listener($event) {\n            return ctx.selectedSimItems = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"div\", 45)(76, \"button\", 46);\n          i0.ɵɵlistener(\"click\", function GroupSimComponent_Template_button_click_76_listener() {\n            return ctx.handleModelClose();\n          });\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 47);\n          i0.ɵɵlistener(\"click\", function GroupSimComponent_Template_button_click_78_listener() {\n            return ctx.handleSavetoGroup();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.breadCrumb.group\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(75, _c0, ctx.allPermissions.GROUP_SIM.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"))(\"styleClass\", \"pb-2 pt-3\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.groupKey);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.label.groupKey\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.label.groupName\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowGroupScope);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tableLabel);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(77, _c1));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"groupSim.breadCrumb.detail\"))(\"visible\", ctx.isShowModalDetailGroupSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(78, _c0, ctx.CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(80, _c0, ctx.CONSTANTS.PERMISSIONS.GROUP_SIM.DELETE)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(82, _c0, ctx.CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(84, _c0, ctx.CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"styleClass\", \"my-4 py-0\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"groupSim.label.groupKey\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.groupKey, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"groupSim.label.groupScope\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_ADMIN);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_PROVINCE);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_CUSTOMER);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"groupSim.label.groupName\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.groupName, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_CUSTOMER);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_CUSTOMER && ctx.contractCode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope == ctx.groupScopeObjects.GROUP_PROVINCE);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupScope != ctx.groupScopeObjects.GROUP_ADMIN);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"groupSim.label.description\"), \":\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.description, \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"msisdn\")(\"selectItems\", ctx.selectItemsDetail)(\"columns\", ctx.columnsDetail)(\"dataSet\", ctx.dataSetDetail)(\"options\", ctx.optionTableDetail)(\"loadData\", ctx.searchDetail.bind(ctx))(\"pageNumber\", ctx.pageNumberDetail)(\"pageSize\", ctx.pageSizeDetail)(\"sort\", ctx.sortDetail);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(86, _c2));\n          i0.ɵɵproperty(\"header\", ctx.headerSim)(\"visible\", ctx.displayAddSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"breakpoints\", i0.ɵɵpureFunction0(87, _c3));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.detail.subNumber\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"control\", ctx.boxSimAddController)(\"value\", ctx.selectedSimItems)(\"placeholder\", ctx.placeholderSIM)(\"paramDefault\", ctx.paramSearchSim)(\"loadData\", ctx.loadSimNotInGroup.bind(ctx));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.label.buttonCancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.buttonSaveSimToGroup)(\"disabled\", ctx.selectedSimItems.length == 0);\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.InputText, i6.ButtonDirective, i6.Button, i7.TableVnptComponent, i8.VnptCombobox, i9.SplitButton, i10.Dropdown, i11.Dialog, i12.Card, i13.Panel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "GroupSimService", "CONSTANTS", "CustomerService", "ComboLazyControl", "SimService", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "tranService", "translate", "itemAddGroups", "ɵɵelementStart", "ɵɵlistener", "GroupSimComponent_div_19_Template_p_dropdown_ngModelChange_2_listener", "$event", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "searchInfo", "scope", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ctx_r1", "groupScopes", "ɵɵtextInterpolate", "GroupSimComponent_button_26_Template_button_click_0_listener", "_r17", "ctx_r16", "showOverLayEdit", "ctx_r2", "GroupSimComponent_button_27_Template_button_click_0_listener", "_r19", "ctx_r18", "deleteGroupSim", "ctx_r3", "GroupSimComponent_button_29_Template_button_click_0_listener", "_r21", "ctx_r20", "remove<PERSON>any", "ctx_r4", "selectItemsDetail", "length", "GroupSimComponent_button_30_Template_button_click_0_listener", "_r23", "ctx_r22", "showOverLayAddSim", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r8", "ɵɵtextInterpolate1", "ctx_r9", "customer", "ctx_r10", "contractCode", "ctx_r11", "province", "GroupSimComponent", "constructor", "groupSimService", "customerService", "simService", "accountService", "injector", "buttonAdd", "tableLabel", "isShowModalDetailGroupSim", "groupScopeObjects", "GROUP_SCOPE", "paramSearchSim", "boxSimAddController", "displayAddSim", "selectedSimItems", "headerSim", "placeholderSIM", "buttonSaveSimToGroup", "allPermissions", "PERMISSIONS", "convertToDDMMYYYY", "input", "date", "Date", "day", "getUTCDate", "toString", "padStart", "month", "getUTCMonth", "year", "getUTCFullYear", "ngOnInit", "me", "userType", "sessionService", "userInfo", "type", "isShowGroupScope", "USER_TYPE", "ADMIN", "PROVINCE", "includes", "items", "label", "home", "icon", "routerLink", "optionTableDetail", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "tooltip", "func", "id", "ids", "removeSim", "pageNumberDetail", "pageSizeDetail", "sortDetail", "dataSetDetail", "content", "total", "columnsDetail", "name", "key", "size", "align", "isShow", "isSort", "style", "color", "funcGetRouting", "item", "msisdn", "funcGetClassname", "value", "SIM_STATUS", "READY", "ACTIVATED", "INACTIVED", "DEACTIVATED", "PURGED", "funcConvertText", "GROUP_CUSTOMER", "command", "create", "apply", "groupScopeForAdmin", "GROUP_ADMIN", "GROUP_PROVINCE", "groupScopeForProvince", "groupScopeForManager", "DISTRICT", "columns", "cursor", "funcClick", "idForEdit", "searchDetail", "getSimGroupById", "response", "groupKey", "groupName", "description", "groupScope", "customerCode", "provinceCode", "get<PERSON><PERSON><PERSON><PERSON>", "res", "customerName", "getListProvince", "for<PERSON>ach", "el", "code", "messageCommonService", "offload", "optionTable", "router", "navigate", "funcAppear", "<PERSON><PERSON><PERSON><PERSON>", "GROUP_SIM", "UPDATE", "confirm", "ok", "onDelete", "cancel", "DELETE", "pageNumber", "pageSize", "sort", "selectItems", "dataSet", "onload", "searchSimGroup", "map", "createdDate", "totalElements", "onSearch", "search", "deleteSimGroup", "success", "error", "console", "page", "limit", "params", "dataParam", "getListDetailGroupSim", "queryParams", "reload", "idForDelete", "push", "removeSIMFromGroup", "parseInt", "loadSimNotInGroup", "data", "callback", "searchNotInGroup", "handleModelClose", "handleSavetoGroup", "pushSimToGroup", "ɵɵdirectiveInject", "i1", "AccountService", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "GroupSimComponent_Template", "rf", "ctx", "ɵɵtemplate", "GroupSimComponent_p_splitButton_6_Template", "GroupSimComponent_Template_input_keyup_enter_11_listener", "GroupSimComponent_Template_input_ngModelChange_11_listener", "GroupSimComponent_Template_input_keyup_enter_16_listener", "GroupSimComponent_Template_input_ngModelChange_16_listener", "GroupSimComponent_div_19_Template", "GroupSimComponent_Template_p_button_click_21_listener", "GroupSimComponent_Template_table_vnpt_selectItemsChange_22_listener", "GroupSimComponent_Template_p_dialog_visibleChange_23_listener", "GroupSimComponent_button_26_Template", "GroupSimComponent_button_27_Template", "GroupSimComponent_button_29_Template", "GroupSimComponent_button_30_Template", "GroupSimComponent_span_44_Template", "GroupSimComponent_span_45_Template", "GroupSimComponent_span_46_Template", "GroupSimComponent_div_53_Template", "GroupSimComponent_div_54_Template", "GroupSimComponent_div_55_Template", "GroupSimComponent_div_56_Template", "GroupSimComponent_Template_table_vnpt_selectItemsChange_65_listener", "GroupSimComponent_Template_p_dialog_visibleChange_66_listener", "GroupSimComponent_Template_vnpt_select_valueChange_74_listener", "GroupSimComponent_Template_button_click_76_listener", "GroupSimComponent_Template_button_click_78_listener", "ɵɵpureFunction1", "_c0", "CREATE", "bind", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "_c2", "_c3"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\group-sim\\group-sim.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\group-sim\\group-sim.list.component.html"], "sourcesContent": ["import { ComponentBase } from 'src/app/component.base';\r\nimport { Inject, Injector } from '@angular/core';\r\nimport { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ColumnInfo, OptionTable } from '../../common-module/table/table.component';\r\nimport { TranslateService } from 'src/app/service/comon/translate.service';\r\nimport { MessageCommonService } from 'src/app/service/comon/message-common.service';\r\nimport { Router } from '@angular/router';\r\nimport { GroupSimService } from 'src/app/service/group-sim/GroupSimService';\r\nimport { CONSTANTS } from 'src/app/service/comon/constants';\r\nimport {AccountService} from \"../../../service/account/AccountService\";\r\nimport {CustomerService} from \"../../../service/customer/CustomerService\";\r\nimport {ComboLazyControl} from \"../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {SimService} from \"../../../service/sim/SimService\";\r\n\r\n\r\n\r\n@Component({\r\n  selector: 'app-group-sim',\r\n  templateUrl: './group-sim.list.component.html',\r\n//   styleUrls: ['./group-sim.component.scss']\r\n})\r\nexport class GroupSimComponent extends ComponentBase {\r\n    buttonAdd: string =this.tranService.translate(\"groupSim.label.buttonAdd\");\r\n    tableLabel=this.tranService.translate(\"groupSim.breadCrumb.group\")\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        groupKey?:string,\r\n        name?: string,\r\n        customer?: string,\r\n        time?: string,\r\n        scope?: string\r\n    };\r\n    columns: Array<ColumnInfo>;\r\n    columnsDetail: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    dataSetDetail: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    // dataStore: Array<any>;\r\n    selectItems: Array<any>;\r\n    selectItemsDetail: Array<any>;\r\n    optionTable: OptionTable;\r\n    optionTableDetail: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    pageNumberDetail: number;\r\n    pageSizeDetail: number;\r\n    sortDetail: string;\r\n    sort: string;\r\n    groupScopes: Array<any>;\r\n    userType: number;\r\n    isShowModalDetailGroupSim: boolean = false;\r\n    groupKey: string;\r\n    groupScope: number;\r\n    groupScopeObjects = CONSTANTS.GROUP_SCOPE;\r\n    groupName: string;\r\n    description: string;\r\n    idForEdit:string;\r\n    customer: string;\r\n    customerCode: string;\r\n    contractCode: string;\r\n    provinceCode: string;\r\n    province: string;\r\n    paramSearchSim = {};\r\n    boxSimAddController: ComboLazyControl = new ComboLazyControl();\r\n    idForDelete:string[];\r\n    displayAddSim: boolean = false;\r\n    selectedSimItems:Array<any> = [];\r\n    headerSim: string = this.tranService.translate(\"groupSim.label.buttonAddSim\");\r\n    placeholderSIM= this.tranService.translate(\"groupSim.placeHolder.addSim\");\r\n    buttonSaveSimToGroup = this.tranService.translate(\"global.button.save\");\r\n    allPermissions = CONSTANTS.PERMISSIONS;\r\n    constructor( @Inject(GroupSimService) private groupSimService: GroupSimService,\r\n                 @Inject(CustomerService) private customerService: CustomerService,\r\n                 @Inject(SimService) private simService: SimService,\r\n                 private accountService: AccountService,\r\n                injector: Injector) {super(injector);}\r\n\r\n    convertToDDMMYYYY(input: string): string {\r\n        const date = new Date(input);\r\n        const day = date.getUTCDate().toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị\r\n        const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị và +1 vì getMonth() trả về từ 0-11\r\n        const year = date.getUTCFullYear().toString();\r\n\r\n        return `${day}/${month}/${year}`;\r\n    }\r\n\r\n    isShowGroupScope: boolean;\r\n    itemAddGroups: MenuItem[];\r\n  ngOnInit(){\r\n\r\n    let me = this;\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.isShowGroupScope = [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE].includes(this.userType);\r\n        this.items = [{ label: this.tranService.translate(`global.menu.simmgmt`) }, { label: this.tranService.translate(\"groupSim.breadCrumb.group\") },];\r\n\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        this.searchInfo = {};\r\n        this.optionTableDetail = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: 'pi pi-fw pi-trash',\r\n                    tooltip: this.tranService.translate('global.button.delete'),\r\n                    func: (id: string) => {  let ids:string[] = [id];this.removeSim(ids)\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n        this.pageNumberDetail = 0;\r\n        this.pageSizeDetail = 10;\r\n        this.sortDetail = 'msisdn,asc';\r\n\r\n        this.selectItemsDetail = [];\r\n        this.dataSetDetail = {\r\n          content: [],\r\n          total: 0,\r\n        };\r\n\r\n      this.columnsDetail = [\r\n          {\r\n              name: this.tranService.translate('groupSim.detail.subNumber'),\r\n              key: 'msisdn',\r\n              size: '20%',\r\n              align: 'left',\r\n              isShow: true,\r\n              isSort: true,\r\n              style: {\r\n                  color: 'var(--mainColorText)',\r\n              },\r\n              funcGetRouting(item) {\r\n                  return [\"/sims/detail/\"+item.msisdn];\r\n              },\r\n          },\r\n          {\r\n              name: \"IMSI\",\r\n              key: 'imsi',\r\n              size: '20%',\r\n              align: 'left',\r\n              isShow: true,\r\n              isSort: true,\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"sim.label.trangthaisim\"),\r\n              key: \"status\",\r\n              size: \"20%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: true,\r\n              funcGetClassname: (value) => {\r\n                  if(value == 0){\r\n                      return ['p-1' , \"border-round\", \"border-400\", \"text-color\",\"inline-block\"];\r\n                  }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n                      // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n                      return ['p-2', \"text-green-800\", \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                  }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n                      return ['p-2', 'text-green-800', \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                  }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n                      return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\",\"inline-block\"];\r\n                  }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n                      return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\",\"inline-block\"];\r\n                  }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n                      return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\",\"inline-block\"];\r\n                  }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n                      return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\",\"inline-block\"];\r\n                  }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n                      return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\",\"inline-block\"];\r\n                  }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n                      return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\",\"inline-block\"];\r\n                  }\r\n                  return [];\r\n              },\r\n              funcConvertText: (value)=>{\r\n                  if(value == 0){\r\n                      return me.tranService.translate(\"sim.status.inventory\");\r\n                  }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n                      // return me.tranService.translate(\"sim.status.ready\");\r\n                      return me.tranService.translate(\"sim.status.activated\");\r\n                  }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n                      return me.tranService.translate(\"sim.status.activated\");\r\n                  }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n                      return me.tranService.translate(\"sim.status.deactivated\");\r\n                  }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n                      return me.tranService.translate(\"sim.status.purged\");\r\n                  }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n                      return me.tranService.translate(\"sim.status.inactivated\");\r\n                  }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n                      return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n                  }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n                      return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n                  }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n                      return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n                  }\r\n                  return \"\";\r\n              },\r\n              style:{\r\n                  color: \"white\"\r\n              }\r\n          },\r\n          {\r\n              name: this.tranService.translate('groupSim.detail.planName'),\r\n              key: 'ratingPlanName',\r\n              size: '20%',\r\n              align: 'left',\r\n              isShow: true,\r\n              isSort: false,\r\n          },\r\n      ];\r\n\r\n        this.groupScopes = [\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\r\n                name: this.tranService.translate(\"groupSim.scope.customer\")\r\n            }\r\n        ];\r\n        this.itemAddGroups = [\r\n            {\r\n                label: this.tranService.translate(\"groupSim.scope.customer\"),\r\n                command: ()=>{\r\n                    me.create.apply(me, [2]);\r\n                }\r\n            }\r\n        ];\r\n        let groupScopeForAdmin = [\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_ADMIN,\r\n                name: this.tranService.translate(\"groupSim.scope.admin\")\r\n            },\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE,\r\n                name: this.tranService.translate(\"groupSim.scope.province\")\r\n            },\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\r\n                name: this.tranService.translate(\"groupSim.scope.customer\")\r\n            }\r\n        ];\r\n\r\n        let groupScopeForProvince = [\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE,\r\n                name: this.tranService.translate(\"groupSim.scope.province\")\r\n            },\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\r\n                name: this.tranService.translate(\"groupSim.scope.customer\")\r\n            }\r\n        ]\r\n\r\n        let groupScopeForManager = [\r\n            {\r\n                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,\r\n                name: this.tranService.translate(\"groupSim.scope.customer\")\r\n            }\r\n        ]\r\n        if(this.userType == CONSTANTS.USER_TYPE.ADMIN){\r\n            this.groupScopes = groupScopeForAdmin;\r\n            this.itemAddGroups = [\r\n                {\r\n                    label: this.tranService.translate(\"groupSim.scope.admin\"),\r\n                    command: ()=>{\r\n                        me.create.apply(me, [0]);\r\n                    },\r\n                },\r\n                {\r\n                    label: this.tranService.translate(\"groupSim.scope.province\"),\r\n                    command: ()=>{\r\n                        me.create.apply(me, [1]);\r\n                    }\r\n                },\r\n                {\r\n                    label: this.tranService.translate(\"groupSim.scope.customer\"),\r\n                    command: ()=>{\r\n                        me.create.apply(me, [2]);\r\n                    }\r\n                }\r\n            ];\r\n        }else if(this.userType == CONSTANTS.USER_TYPE.PROVINCE){\r\n            this.groupScopes = groupScopeForProvince;\r\n            this.itemAddGroups = [\r\n                {\r\n                    label: this.tranService.translate(\"groupSim.scope.province\"),\r\n                    command: ()=>{\r\n                        me.create.apply(me, [1]);\r\n                    }\r\n                },\r\n                {\r\n                    label: this.tranService.translate(\"groupSim.scope.customer\"),\r\n                    command: ()=>{\r\n                        me.create.apply(me, [2]);\r\n                    }\r\n                }\r\n            ];\r\n        } else if (this.userType == CONSTANTS.USER_TYPE.DISTRICT) {\r\n            this.groupScopes = groupScopeForManager;\r\n            this.itemAddGroups = [\r\n                {\r\n                    label: this.tranService.translate(\"groupSim.scope.customer\"),\r\n                    command: ()=>{\r\n                        me.create.apply(me, [2]);\r\n                    }\r\n                }\r\n            ];\r\n        }\r\n\r\n        this.columns = [{\r\n            name: this.tranService.translate(\"groupSim.label.groupKey\"),\r\n            key: \"groupKey\",\r\n            size: \"25%\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true,\r\n            style:{\r\n                cursor: \"pointer\",\r\n                color: \"var(--mainColorText)\",\r\n                'min-width': '300px'\r\n            },\r\n            funcClick(id, item) {\r\n                me.isShowModalDetailGroupSim = true;\r\n                me.selectItemsDetail = [];\r\n                me.idForEdit = id;\r\n                me.searchDetail(0, me.pageSizeDetail,me.sortDetail, null)\r\n                me.groupSimService.getSimGroupById(me.idForEdit,{},{},(response)=>{\r\n                    me.groupKey = response.groupKey\r\n                    me.customer = response.customer;\r\n                    me.groupName = response.name;\r\n                    me.description = response.description;\r\n                    me.groupScope = response.scope;\r\n                    me.customerCode = response.customerCode;\r\n                    me.contractCode = response.contractCode;\r\n                    me.provinceCode = response.provinceCode;\r\n                    if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){\r\n                        me.paramSearchSim = {\r\n                            customer: me.customerCode,\r\n                            contractCode: me.contractCode ? me.contractCode : \" \",\r\n                        }\r\n                        me.customerService.getByKey(\"customerCode\", me.customerCode,(res)=>{\r\n                            me.customer = `${res[0].customerName} - ${res[0].customerCode}`;\r\n                        })\r\n                    }\r\n                    if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){\r\n                        me.paramSearchSim = {\r\n                            provinceCode: me.provinceCode\r\n                        }\r\n                        me.accountService.getListProvince((res)=>{\r\n                            (res || []).forEach(el => {\r\n                                if(el.code == response.provinceCode){\r\n                                    me.province = `${el.name} (${el.code})`\r\n                                }\r\n                            })\r\n                        })\r\n                    }\r\n                }, null, ()=>{\r\n                    me.messageCommonService.offload();\r\n                })\r\n            },\r\n        },\r\n        {\r\n            name: this.tranService.translate(\"groupSim.label.groupName\"),\r\n            key: \"name\",\r\n            size: \"25%\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true,\r\n            style:{\r\n                'min-width': '300px'\r\n            },\r\n        },\r\n        {\r\n            name: this.tranService.translate(\"groupSim.label.groupScope\"),\r\n            key: \"scope\",\r\n            size: \"25%\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true,\r\n            funcConvertText(value){\r\n                if(value == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN){\r\n                    return me.tranService.translate(\"groupSim.scope.admin\");\r\n                }else if(value == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){\r\n                    return me.tranService.translate(\"groupSim.scope.province\");\r\n                }else if(value == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){\r\n                    return me.tranService.translate(\"groupSim.scope.customer\");\r\n                }\r\n                return \"\";\r\n            },\r\n            style:{\r\n                'min-width': '300px'\r\n            },\r\n        },\r\n        {\r\n            name: this.tranService.translate(\"groupSim.label.time\"),\r\n            key: \"createdDate\",\r\n            size: \"200px\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: true\r\n        }];\r\n\r\n        this.optionTable = {\r\n            hasClearSelected:false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-fw pi-pencil\",\r\n                    tooltip: this.tranService.translate('global.button.edit'),\r\n                    func: (id: string)=>{\r\n                        // console.log(id)\r\n                        this.router.navigate(['/sims/group/update', id]);\r\n                    },\r\n                    funcAppear: (id:string, item)=>{\r\n                        return me.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-fw pi-trash\",\r\n                    tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                    func: (id: string)=>{\r\n                        me.messageCommonService.confirm(this.tranService.translate(\"groupSim.label.confirmDelete\"),this.tranService.translate(\"groupSim.label.deleteTextGroup\"),{ok: ()=> {\r\n                            this.onDelete(id);\r\n                        }, cancel: ()=>{}});\r\n                    },\r\n                    funcAppear: (id:string, item)=>{\r\n                        return me.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.DELETE]);\r\n                    }\r\n                }\r\n            ]\r\n        }\r\n        this.pageNumber = 0;\r\n        this.pageSize= 10;\r\n        this.sort = \"createdDate,desc\"\r\n\r\n        this.selectItems = [];\r\n        this.dataSet ={\r\n            content: [],\r\n            total: 0\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.groupSimService.searchSimGroup({},{...this.searchInfo,sort:this.sort},(response)=>{\r\n            this.dataSet.content=response.content.map((item:any)=>{\r\n                item.createdDate=this.convertToDDMMYYYY(item.createdDate)\r\n                return item;\r\n            });\r\n            this.dataSet.total = response.totalElements;\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    onSearch(){\r\n        // console.log(this.searchInfo);\r\n        this.search(0,this.pageSize, this.sort, this.searchInfo)\r\n    }\r\n\r\n    onDelete(id:string){\r\n        let me = this;\r\n        // console.log(id)\r\n        this.groupSimService.deleteSimGroup(id,{},{},(response)=>{\r\n            me.messageCommonService.success(this.tranService.translate(\"global.message.deleteSuccess\"));\r\n            // me.selectItems = me.selectItems.filter(el => el.id != id);\r\n            this.search(0, this.pageSize,this.sort, this.searchInfo)\r\n        },(error)=>{console.error(error)},()=>{})\r\n    }\r\n\r\n    search(page, limit, sort,params){\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        if(this.searchInfo.scope==null)\r\n            this.searchInfo.scope=\"\"\r\n        let dataParam = {\r\n            ...params,\r\n            page,\r\n            size:limit,\r\n            sort\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.groupSimService.searchSimGroup({},dataParam,(response)=>{\r\n            this.dataSet.content=response.content.map((item:any)=>{\r\n                item.createdDate=this.convertToDDMMYYYY(item.createdDate)\r\n                return item;\r\n            });\r\n            this.dataSet.total = response.totalElements;\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    searchDetail(page, limit, sort, params){\r\n        let me = this;\r\n        this.pageNumberDetail = page;\r\n        this.pageSizeDetail = limit;\r\n        this.sortDetail = sort;\r\n        let dataParam = {\r\n            page,\r\n            size:limit,\r\n            sort\r\n        }\r\n        this.groupSimService.getListDetailGroupSim(this.idForEdit,{},dataParam,(response)=>{\r\n            me.dataSetDetail.content=response.content;\r\n            me.dataSetDetail.total = response.totalElements;\r\n        })\r\n    }\r\n\r\n    create(type){\r\n        this.router.navigate(['/sims/group/create'],{queryParams: {type}})\r\n    }\r\n\r\n    showOverLayAddSim(){\r\n        this.boxSimAddController.reload();\r\n        this.displayAddSim=true;\r\n        this.selectedSimItems = []\r\n    }\r\n\r\n    showOverLayEdit(){\r\n        this.router.navigate([\"/sims/group/update\", this.idForEdit]);\r\n    }\r\n\r\n    removeMany(){\r\n        // console.log(this.selectItems)\r\n        if(this.selectItemsDetail.length==0)\r\n            return null\r\n        this.idForDelete=[];\r\n        this.selectItemsDetail.map((item)=>{\r\n            this.idForDelete.push(item.msisdn);\r\n        })\r\n        this.removeSim(this.idForDelete)\r\n    }\r\n\r\n    removeSim(ids:string[]){\r\n        this.messageCommonService.confirm(this.tranService.translate(\"groupSim.label.confirmDelete\"),this.tranService.translate(\"groupSim.label.deleteTextSim\"), {\r\n            ok: () => {\r\n                this.simService.removeSIMFromGroup(ids,parseInt(this.idForEdit),(response)=>{\r\n                    // console.log(response);\r\n                    this.selectItemsDetail=[];\r\n                    this.messageCommonService.success(this.tranService.translate(\"global.message.deleteSuccess\"))\r\n                    this.searchDetail(0, this.pageSizeDetail,this.sortDetail, null)\r\n                })\r\n            },\r\n            cancel: () => {\r\n            },\r\n        });\r\n    }\r\n\r\n    deleteGroupSim(){\r\n        let me = this;\r\n        this.messageCommonService.confirm(this.tranService.translate(\"groupSim.label.deleteTextGroup\"), this.tranService.translate(\"groupSim.label.confirmDelete\"),{\r\n            ok: ()=>{\r\n                me.groupSimService.deleteSimGroup(me.idForEdit, {}, {}, (response)=>{\r\n                    me.messageCommonService.success(me.tranService.translate(\"global.message.success\"));\r\n                    me.router.navigate(['/sims/group'])\r\n                })\r\n            }\r\n        })\r\n    }\r\n\r\n    loadSimNotInGroup(data, callback){\r\n        this.simService.searchNotInGroup(data, callback);\r\n    }\r\n\r\n    handleModelClose(){\r\n        this.displayAddSim=false;\r\n    }\r\n\r\n    handleSavetoGroup(){\r\n        if (this.selectedSimItems.length > 0) {\r\n            this.simService.pushSimToGroup(this.selectedSimItems,{id:parseInt(this.idForEdit)},\r\n                (response)=>{\r\n                    this.messageCommonService.success(this.tranService.translate(\"global.message.addGroupSuccess\"))\r\n                    this.searchDetail(0, this.pageSize,this.sort, null)\r\n\r\n                })\r\n            this.displayAddSim=false;\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS\r\n\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"groupSim.breadCrumb.group\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-splitButton *ngIf=\"checkAuthen([allPermissions.GROUP_SIM.CREATE])\" styleClass=\"p-button-success\" [label]=\"tranService.translate('global.button.add')\" icon=\"pi pi-plus\" [model]=\"itemAddGroups\"></p-splitButton>\r\n    </div>\r\n</div>\r\n<!-- <p-fieldset class=\"vnpt-field-set\" [styleClass]=\"'mt-4'\" [legend]=\"tranService.translate('global.text.filter')\" [toggleable]=\"true\">\r\n\r\n</p-fieldset> -->\r\n<p-panel class=\"vnpt-field-set\" [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\" [styleClass]=\"'pb-2 pt-3'\">\r\n    <div class=\"grid\">\r\n        <div class=\"col-3\">\r\n            <span class=\"p-float-label\">\r\n                <input (keyup.enter)=\"onSearch()\" class=\"w-full\" pInputText id=\"groupKey\" [(ngModel)]=\"searchInfo.groupKey\" />\r\n                <label htmlFor=\"groupKey\">{{tranService.translate(\"groupSim.label.groupKey\")}}</label>\r\n            </span>\r\n        </div>\r\n        <div class=\"col-3\">\r\n            <span class=\"p-float-label\">\r\n                <input (keyup.enter)=\"onSearch()\" class=\"w-full\" pInputText id=\"name\" [(ngModel)]=\"searchInfo.name\" />\r\n                <label htmlFor=\"groupName\">{{tranService.translate(\"groupSim.label.groupName\")}}</label>\r\n            </span>\r\n        </div>\r\n        <!-- <div class=\"col-3\">\r\n            <span class=\"p-float-label\">\r\n                <input class=\"w-full\" pInputText id=\"customer\" [(ngModel)]=\"searchInfo.customer\" />\r\n                <label htmlFor=\"customer\">{{tranService.translate(\"groupSim.label.customer\")}}</label>\r\n            </span>\r\n        </div> -->\r\n\r\n        <div class=\"col-3\" *ngIf=\"isShowGroupScope\">\r\n            <span class=\"p-float-label\">\r\n                <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                        [autoDisplayFirst]=\"false\"\r\n                        [(ngModel)]=\"searchInfo.scope\"\r\n                        [options]=\"groupScopes\"\r\n                        optionLabel=\"name\"\r\n                        optionValue=\"value\"\r\n                ></p-dropdown>\r\n                <label for=\"scope\">{{tranService.translate(\"groupSim.label.groupScope\")}}</label>\r\n            </span>\r\n        </div>\r\n\r\n        <div class=\"col-3 pb-0\">\r\n            <p-button icon=\"pi pi-search\"\r\n                        styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                        (click)=\"onSearch()\"\r\n            ></p-button>\r\n        </div>\r\n    </div>\r\n</p-panel>\r\n<!-- <div>{{selectItems.length}}</div> -->\r\n<table-vnpt\r\n    [fieldId]=\"'id'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tableLabel\"\r\n></table-vnpt>\r\n<p-dialog [header]=\"tranService.translate('groupSim.breadCrumb.detail')\" [(visible)]=\"isShowModalDetailGroupSim\" [modal]=\"true\" [style]=\"{ width: '980px', height: '720px' }\" [draggable]=\"false\" [resizable]=\"true\" styleClass=\"custom-dialog\">\r\n    <div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n        <div class=\"col-5 flex flex-row justify-content-start align-items-center\">\r\n            <button pButton *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])\" class=\"p-button-info mr-2\" (click)=\"showOverLayEdit()\">{{this.tranService.translate(\"groupSim.label.buttonEdit\")}}</button>\r\n            <button pButton *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.DELETE])\" class=\"p-button-secondary mr-2\" (click)=\"deleteGroupSim()\">{{this.tranService.translate(\"global.button.delete\")}}</button>\r\n        </div>\r\n        <div class=\"col-5 flex flex flex-row justify-content-end align-items-center responsive-button-container\">\r\n            <button pButton *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])\" class=\"p-button-secondary mr-2 center-button equal-button\" (click)=\"removeMany()\" [disabled]=\"selectItemsDetail.length == 0\">{{this.tranService.translate(\"groupSim.label.buttonDelete\")}}</button>\r\n            <button pButton *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])\" class=\"p-button-success mr-2 equal-button\" (click)=\"showOverLayAddSim()\">{{this.tranService.translate(\"groupSim.label.buttonAddSim\")}}</button>\r\n        </div>\r\n    </div>\r\n\r\n    <p-card [styleClass]=\"'my-4 py-0'\">\r\n        <div class=\"grid dialog-sim-list-grid-1 \">\r\n            <div class=\"col-6 pt-0 pb-0\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 sm:col-8 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium\" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"groupSim.label.groupKey\")}}:</label>\r\n                    <div class=\"col-9 sm:col-6 md:col-10 py-0 text-lg font-medium \">\r\n                        {{groupKey}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupScope\" class=\"m-0 p-0 text-lg font-medium \" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"groupSim.label.groupScope\")}}:</label>\r\n                    <div class=\"col-9 md:col-10 py-0 text-lg font-medium span-responsive\">\r\n                        <span *ngIf=\"groupScope == groupScopeObjects.GROUP_ADMIN\">{{tranService.translate(\"groupSim.scope.admin\")}}</span>\r\n                        <span *ngIf=\"groupScope == groupScopeObjects.GROUP_PROVINCE\">{{tranService.translate(\"groupSim.scope.province\")}}</span>\r\n                        <span *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\">{{tranService.translate(\"groupSim.scope.customer\")}}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium\" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"groupSim.label.groupName\")}}:</label>\r\n                    <div class=\"col-9 md:col-10 pb-0 text-lg font-medium\">\r\n                        {{groupName}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\" *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium\" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"groupSim.label.customer\")}}:</label>\r\n                    <div class=\"col-9 md:col-10 pb-0 text-lg font-medium\">\r\n                        {{customer}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\" *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER && contractCode\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium\" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"groupSim.label.contractCode\")}}:</label>\r\n                    <div class=\"col-9 md:col-10 pb-0 text-lg font-medium\">\r\n                        {{contractCode}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\" *ngIf=\"groupScope == groupScopeObjects.GROUP_PROVINCE\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium\" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"account.label.province\")}}:</label>\r\n                    <div class=\"col-9 md:col-10 pb-0 text-lg font-medium\">\r\n                        {{province}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\" *ngIf=\"groupScope != groupScopeObjects.GROUP_ADMIN\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium\" style=\"min-width: 130px; align-self: flex-end;\"></label>\r\n                    <div class=\"col-9 md:col-10 pb-0 text-lg font-medium\">\r\n\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 pt-0 pb-0\">\r\n                <div class=\"flex-1 flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"m-0 p-0 text-lg font-medium \" style=\"min-width: 130px; align-self: flex-end;\">{{this.tranService.translate(\"groupSim.label.description\")}}:</label>\r\n                    <div class=\"col-9 md:col-10 pb-0 text-lg font-medium\">\r\n                        {{description}}<span>&nbsp;</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </p-card>\r\n\r\n\r\n    <table-vnpt\r\n        [fieldId]=\"'msisdn'\"\r\n        [(selectItems)]=\"selectItemsDetail\"\r\n        [columns]=\"columnsDetail\"\r\n        [dataSet]=\"dataSetDetail\"\r\n        [options]=\"optionTableDetail\"\r\n        [loadData]=\"searchDetail.bind(this)\"\r\n        [pageNumber]=\"pageNumberDetail\"\r\n        [pageSize]=\"pageSizeDetail\"\r\n        [sort]=\"sortDetail\"\r\n    ></table-vnpt>\r\n\r\n    <p-dialog #overlayGanSim [header]=\"headerSim\" [(visible)]=\"displayAddSim\" [modal]=\"true\" [draggable]=\"false\" [resizable]=\"false\" showEffect=\"fade\" [style]=\"{width: '45vw'}\" [breakpoints]=\"{'960px': '75vw'}\">\r\n\r\n        <div class=\"flex justify-content-center align-items-center col-12 md:col-12 py-0\">\r\n            <label htmlFor=\"groupCode\" class=\"my-auto\" style=\"min-width: 150px;\">{{tranService.translate(\"groupSim.detail.subNumber\")}}<span class=\"text-red-500\">*</span></label>\r\n            <div class=\"flex-grow-1\">\r\n                <vnpt-select\r\n                    [control]=\"boxSimAddController\"\r\n                    class=\"w-full\"\r\n                    [(value)]=\"selectedSimItems\"\r\n                    [placeholder]=\"placeholderSIM\"\r\n                    objectKey=\"sim\"\r\n                    paramKey=\"msisdn\"\r\n                    keyReturn=\"msisdn\"\r\n                    displayPattern=\"${msisdn}\"\r\n                    typeValue=\"primitive\"\r\n                    [paramDefault]=\"paramSearchSim\"\r\n                    [loadData]=\"loadSimNotInGroup.bind(this)\"\r\n                ></vnpt-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"pt-4 flex flex-row gap-3 justify-content-center\">\r\n            <button pButton (click)=\"handleModelClose()\" class=\"p-button-secondary p-button-outlined\">{{this.tranService.translate(\"groupSim.label.buttonCancel\")}}</button>\r\n            <button pButton (click)=\"handleSavetoGroup()\" [label]=\"buttonSaveSimToGroup\" [disabled]=\"selectedSimItems.length == 0\"></button>\r\n        </div>\r\n    </p-dialog>\r\n\r\n</p-dialog>\r\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,wBAAwB;AAQtD,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,SAAQC,eAAe,QAAO,2CAA2C;AACzE,SAAQC,gBAAgB,QAAO,yDAAyD;AACxF,SAAQC,UAAU,QAAO,iCAAiC;;;;;;;;;;;;;;;;;;;;ICPlDC,EAAA,CAAAC,SAAA,wBAAmN;;;;IAA/GD,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,sBAAoD,UAAAF,MAAA,CAAAG,aAAA;;;;;;IA2BxJN,EAAA,CAAAO,cAAA,aAA4C;IAI5BP,EAAA,CAAAQ,UAAA,2BAAAC,sEAAAC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAad,EAAA,CAAAe,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAAC,KAAA,GAAAP,MAAA,CAChC;IAAA,EADiD;IAIrCV,EAAA,CAAAkB,YAAA,EAAa;IACdlB,EAAA,CAAAO,cAAA,gBAAmB;IAAAP,EAAA,CAAAmB,MAAA,GAAsD;IAAAnB,EAAA,CAAAkB,YAAA,EAAQ;;;;IAPjDlB,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAE,UAAA,mBAAkB,uCAAAmB,MAAA,CAAAL,UAAA,CAAAC,KAAA,aAAAI,MAAA,CAAAC,WAAA;IAO/BtB,EAAA,CAAAoB,SAAA,GAAsD;IAAtDpB,EAAA,CAAAuB,iBAAA,CAAAF,MAAA,CAAAjB,WAAA,CAAAC,SAAA,8BAAsD;;;;;;IA6B7EL,EAAA,CAAAO,cAAA,iBAAqI;IAA5BP,EAAA,CAAAQ,UAAA,mBAAAgB,6DAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAW,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAAC3B,EAAA,CAAAmB,MAAA,GAA2D;IAAAnB,EAAA,CAAAkB,YAAA,EAAS;;;;IAApElB,EAAA,CAAAoB,SAAA,GAA2D;IAA3DpB,EAAA,CAAAuB,iBAAA,CAAAK,MAAA,CAAAxB,WAAA,CAAAC,SAAA,8BAA2D;;;;;;IAChML,EAAA,CAAAO,cAAA,iBAAyI;IAA3BP,EAAA,CAAAQ,UAAA,mBAAAqB,6DAAA;MAAA7B,EAAA,CAAAW,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAgB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAAChC,EAAA,CAAAmB,MAAA,GAAsD;IAAAnB,EAAA,CAAAkB,YAAA,EAAS;;;;IAA/DlB,EAAA,CAAAoB,SAAA,GAAsD;IAAtDpB,EAAA,CAAAuB,iBAAA,CAAAU,MAAA,CAAA7B,WAAA,CAAAC,SAAA,yBAAsD;;;;;;IAG/LL,EAAA,CAAAO,cAAA,iBAA2M;IAAlEP,EAAA,CAAAQ,UAAA,mBAAA0B,6DAAA;MAAAlC,EAAA,CAAAW,aAAA,CAAAwB,IAAA;MAAA,MAAAC,OAAA,GAAApC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAqB,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAA4CrC,EAAA,CAAAmB,MAAA,GAA6D;IAAAnB,EAAA,CAAAkB,YAAA,EAAS;;;;IAAjHlB,EAAA,CAAAE,UAAA,aAAAoC,MAAA,CAAAC,iBAAA,CAAAC,MAAA,MAA0C;IAACxC,EAAA,CAAAoB,SAAA,GAA6D;IAA7DpB,EAAA,CAAAuB,iBAAA,CAAAe,MAAA,CAAAlC,WAAA,CAAAC,SAAA,gCAA6D;;;;;;IACxQL,EAAA,CAAAO,cAAA,iBAAuJ;IAA9BP,EAAA,CAAAQ,UAAA,mBAAAiC,6DAAA;MAAAzC,EAAA,CAAAW,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAA4B,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAAC5C,EAAA,CAAAmB,MAAA,GAA6D;IAAAnB,EAAA,CAAAkB,YAAA,EAAS;;;;IAAtElB,EAAA,CAAAoB,SAAA,GAA6D;IAA7DpB,EAAA,CAAAuB,iBAAA,CAAAsB,MAAA,CAAAzC,WAAA,CAAAC,SAAA,gCAA6D;;;;;IAkBxML,EAAA,CAAAO,cAAA,WAA0D;IAAAP,EAAA,CAAAmB,MAAA,GAAiD;IAAAnB,EAAA,CAAAkB,YAAA,EAAO;;;;IAAxDlB,EAAA,CAAAoB,SAAA,GAAiD;IAAjDpB,EAAA,CAAAuB,iBAAA,CAAAuB,MAAA,CAAA1C,WAAA,CAAAC,SAAA,yBAAiD;;;;;IAC3GL,EAAA,CAAAO,cAAA,WAA6D;IAAAP,EAAA,CAAAmB,MAAA,GAAoD;IAAAnB,EAAA,CAAAkB,YAAA,EAAO;;;;IAA3DlB,EAAA,CAAAoB,SAAA,GAAoD;IAApDpB,EAAA,CAAAuB,iBAAA,CAAAwB,MAAA,CAAA3C,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACjHL,EAAA,CAAAO,cAAA,WAA6D;IAAAP,EAAA,CAAAmB,MAAA,GAAoD;IAAAnB,EAAA,CAAAkB,YAAA,EAAO;;;;IAA3DlB,EAAA,CAAAoB,SAAA,GAAoD;IAApDpB,EAAA,CAAAuB,iBAAA,CAAAyB,MAAA,CAAA5C,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAY7HL,EAAA,CAAAO,cAAA,cAAoF;IAEmCP,EAAA,CAAAmB,MAAA,GAA0D;IAAAnB,EAAA,CAAAkB,YAAA,EAAQ;IACjLlB,EAAA,CAAAO,cAAA,cAAsD;IAClDP,EAAA,CAAAmB,MAAA,GACJ;IAAAnB,EAAA,CAAAkB,YAAA,EAAM;;;;IAHyGlB,EAAA,CAAAoB,SAAA,GAA0D;IAA1DpB,EAAA,CAAAiD,kBAAA,KAAAC,MAAA,CAAA9C,WAAA,CAAAC,SAAA,iCAA0D;IAErKL,EAAA,CAAAoB,SAAA,GACJ;IADIpB,EAAA,CAAAiD,kBAAA,MAAAC,MAAA,CAAAC,QAAA,MACJ;;;;;IAGRnD,EAAA,CAAAO,cAAA,cAAoG;IAEmBP,EAAA,CAAAmB,MAAA,GAA8D;IAAAnB,EAAA,CAAAkB,YAAA,EAAQ;IACrLlB,EAAA,CAAAO,cAAA,cAAsD;IAClDP,EAAA,CAAAmB,MAAA,GACJ;IAAAnB,EAAA,CAAAkB,YAAA,EAAM;;;;IAHyGlB,EAAA,CAAAoB,SAAA,GAA8D;IAA9DpB,EAAA,CAAAiD,kBAAA,KAAAG,OAAA,CAAAhD,WAAA,CAAAC,SAAA,qCAA8D;IAEzKL,EAAA,CAAAoB,SAAA,GACJ;IADIpB,EAAA,CAAAiD,kBAAA,MAAAG,OAAA,CAAAC,YAAA,MACJ;;;;;IAGRrD,EAAA,CAAAO,cAAA,cAAoF;IAEmCP,EAAA,CAAAmB,MAAA,GAAyD;IAAAnB,EAAA,CAAAkB,YAAA,EAAQ;IAChLlB,EAAA,CAAAO,cAAA,cAAsD;IAClDP,EAAA,CAAAmB,MAAA,GACJ;IAAAnB,EAAA,CAAAkB,YAAA,EAAM;;;;IAHyGlB,EAAA,CAAAoB,SAAA,GAAyD;IAAzDpB,EAAA,CAAAiD,kBAAA,KAAAK,OAAA,CAAAlD,WAAA,CAAAC,SAAA,gCAAyD;IAEpKL,EAAA,CAAAoB,SAAA,GACJ;IADIpB,EAAA,CAAAiD,kBAAA,MAAAK,OAAA,CAAAC,QAAA,MACJ;;;;;IAGRvD,EAAA,CAAAO,cAAA,cAAiF;IAEzEP,EAAA,CAAAC,SAAA,gBAAuH;IAI3HD,EAAA,CAAAkB,YAAA,EAAM;;;;;;;;;;;;;;;;;;;;;;ADpHtB,OAAM,MAAOsC,iBAAkB,SAAQ9D,aAAa;EAwDhD+D,YAA8CC,eAAgC,EAChCC,eAAgC,EACrCC,UAAsB,EAC1CC,cAA8B,EACvCC,QAAkB;IAAG,KAAK,CAACA,QAAQ,CAAC;IAJF,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACpB,KAAAC,UAAU,GAAVA,UAAU;IAC9B,KAAAC,cAAc,GAAdA,cAAc;IA1DnC,KAAAE,SAAS,GAAU,IAAI,CAAC3D,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;IACzE,KAAA2D,UAAU,GAAC,IAAI,CAAC5D,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;IAiClE,KAAA4D,yBAAyB,GAAY,KAAK;IAG1C,KAAAC,iBAAiB,GAAGtE,SAAS,CAACuE,WAAW;IASzC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,mBAAmB,GAAqB,IAAIvE,gBAAgB,EAAE;IAE9D,KAAAwE,aAAa,GAAY,KAAK;IAC9B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,SAAS,GAAW,IAAI,CAACpE,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;IAC7E,KAAAoE,cAAc,GAAE,IAAI,CAACrE,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;IACzE,KAAAqE,oBAAoB,GAAG,IAAI,CAACtE,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;IACvE,KAAAsE,cAAc,GAAG/E,SAAS,CAACgF,WAAW;IA+fnB,KAAAhF,SAAS,GAAGA,SAAS;EA1fS;EAEjDiF,iBAAiBA,CAACC,KAAa;IAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;IAC5B,MAAMG,GAAG,GAAGF,IAAI,CAACG,UAAU,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,MAAMC,KAAK,GAAG,CAACN,IAAI,CAACO,WAAW,EAAE,GAAG,CAAC,EAAEH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACpE,MAAMG,IAAI,GAAGR,IAAI,CAACS,cAAc,EAAE,CAACL,QAAQ,EAAE;IAE7C,OAAO,GAAGF,GAAG,IAAII,KAAK,IAAIE,IAAI,EAAE;EACpC;EAIFE,QAAQA,CAAA;IAEN,IAAIC,EAAE,GAAG,IAAI;IACT,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACC,IAAI;IACjD,IAAI,CAACC,gBAAgB,GAAG,CAACnG,SAAS,CAACoG,SAAS,CAACC,KAAK,EAAErG,SAAS,CAACoG,SAAS,CAACE,QAAQ,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACR,QAAQ,CAAC;IACzG,IAAI,CAACS,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,qBAAqB;IAAC,CAAE,EAAE;MAAEgG,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,2BAA2B;IAAC,CAAE,CAAE;IAEhJ,IAAI,CAACiG,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAI,CAACxF,UAAU,GAAG,EAAE;IACpB,IAAI,CAACyF,iBAAiB,GAAG;MACrBC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIP,IAAI,EAAE,mBAAmB;QACzBQ,OAAO,EAAE,IAAI,CAAC3G,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3D2G,IAAI,EAAGC,EAAU,IAAI;UAAI,IAAIC,GAAG,GAAY,CAACD,EAAE,CAAC;UAAC,IAAI,CAACE,SAAS,CAACD,GAAG,CAAC;QACpE;OACH;KAER;IACD,IAAI,CAACE,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,UAAU,GAAG,YAAY;IAE9B,IAAI,CAAC/E,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACgF,aAAa,GAAG;MACnBC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACR;IAEH,IAAI,CAACC,aAAa,GAAG,CACjB;MACIC,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DuH,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACHC,KAAK,EAAE;OACV;MACDC,cAAcA,CAACC,IAAI;QACf,OAAO,CAAC,eAAe,GAACA,IAAI,CAACC,MAAM,CAAC;MACxC;KACH,EACD;MACIV,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIL,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DuH,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZM,gBAAgB,EAAGC,KAAK,IAAI;QACxB,IAAGA,KAAK,IAAI,CAAC,EAAC;UACV,OAAO,CAAC,KAAK,EAAG,cAAc,EAAE,YAAY,EAAE,YAAY,EAAC,cAAc,CAAC;SAC7E,MAAK,IAAGA,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UACzC;UACA,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;SACjF,MAAK,IAAGF,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,EAAC;UAC7C,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;SACjF,MAAK,IAAGH,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACG,SAAS,EAAC;UAC7C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;SACpF,MAAK,IAAGJ,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACI,WAAW,EAAC;UAC/C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;SACpF,MAAK,IAAGL,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACK,MAAM,EAAC;UAC1C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAC,cAAc,CAAC;SAC9E,MAAK,IAAGN,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,IAAIH,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;SAChF,MAAK,IAAGF,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,IAAIH,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;SAChF,MAAK,IAAGF,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,IAAIH,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UAC9F,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;;QAErF,OAAO,EAAE;MACb,CAAC;MACDK,eAAe,EAAGP,KAAK,IAAG;QACtB,IAAGA,KAAK,IAAI,CAAC,EAAC;UACV,OAAO7C,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UACzC;UACA,OAAO/C,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,EAAC;UAC7C,OAAOhD,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACI,WAAW,EAAC;UAC/C,OAAOlD,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACK,MAAM,EAAC;UAC1C,OAAOnD,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAAC4I,UAAU,CAACG,SAAS,EAAC;UAC7C,OAAOjD,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAGkI,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,IAAIH,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UAC9F,OAAO,IAAI,CAACrI,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACvE,MAAK,IAAGkI,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,IAAIH,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UAC9F,OAAO,IAAI,CAACrI,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;SACzE,MAAK,IAAGkI,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACE,SAAS,IAAIH,KAAK,IAAI,EAAE,GAAG3I,SAAS,CAAC4I,UAAU,CAACC,KAAK,EAAC;UAC9F,OAAO,IAAI,CAACrI,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;QAErE,OAAO,EAAE;MACb,CAAC;MACD4H,KAAK,EAAC;QACFC,KAAK,EAAE;;KAEd,EACD;MACIP,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DuH,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAEC,IAAI,CAAC1G,WAAW,GAAG,CACf;MACIiH,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAAC4E,cAAc;MAC3CpB,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB;KAC7D,CACJ;IACD,IAAI,CAACC,aAAa,GAAG,CACjB;MACI+F,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC5D2I,OAAO,EAAEA,CAAA,KAAI;QACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5B;KACH,CACJ;IACD,IAAIyD,kBAAkB,GAAG,CACrB;MACIZ,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAACiF,WAAW;MACxCzB,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,sBAAsB;KAC1D,EACD;MACIkI,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAACkF,cAAc;MAC3C1B,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB;KAC7D,EACD;MACIkI,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAAC4E,cAAc;MAC3CpB,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB;KAC7D,CACJ;IAED,IAAIiJ,qBAAqB,GAAG,CACxB;MACIf,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAACkF,cAAc;MAC3C1B,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB;KAC7D,EACD;MACIkI,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAAC4E,cAAc;MAC3CpB,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB;KAC7D,CACJ;IAED,IAAIkJ,oBAAoB,GAAG,CACvB;MACIhB,KAAK,EAAE3I,SAAS,CAACuE,WAAW,CAAC4E,cAAc;MAC3CpB,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB;KAC7D,CACJ;IACD,IAAG,IAAI,CAACsF,QAAQ,IAAI/F,SAAS,CAACoG,SAAS,CAACC,KAAK,EAAC;MAC1C,IAAI,CAAC3E,WAAW,GAAG6H,kBAAkB;MACrC,IAAI,CAAC7I,aAAa,GAAG,CACjB;QACI+F,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACzD2I,OAAO,EAAEA,CAAA,KAAI;UACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B;OACH,EACD;QACIW,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D2I,OAAO,EAAEA,CAAA,KAAI;UACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B;OACH,EACD;QACIW,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D2I,OAAO,EAAEA,CAAA,KAAI;UACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B;OACH,CACJ;KACJ,MAAK,IAAG,IAAI,CAACC,QAAQ,IAAI/F,SAAS,CAACoG,SAAS,CAACE,QAAQ,EAAC;MACnD,IAAI,CAAC5E,WAAW,GAAGgI,qBAAqB;MACxC,IAAI,CAAChJ,aAAa,GAAG,CACjB;QACI+F,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D2I,OAAO,EAAEA,CAAA,KAAI;UACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B;OACH,EACD;QACIW,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D2I,OAAO,EAAEA,CAAA,KAAI;UACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B;OACH,CACJ;KACJ,MAAM,IAAI,IAAI,CAACC,QAAQ,IAAI/F,SAAS,CAACoG,SAAS,CAACwD,QAAQ,EAAE;MACtD,IAAI,CAAClI,WAAW,GAAGiI,oBAAoB;MACvC,IAAI,CAACjJ,aAAa,GAAG,CACjB;QACI+F,KAAK,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D2I,OAAO,EAAEA,CAAA,KAAI;UACTtD,EAAE,CAACuD,MAAM,CAACC,KAAK,CAACxD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B;OACH,CACJ;;IAGL,IAAI,CAAC+D,OAAO,GAAG,CAAC;MACZ9B,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3DuH,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAC;QACFyB,MAAM,EAAE,SAAS;QACjBxB,KAAK,EAAE,sBAAsB;QAC7B,WAAW,EAAE;OAChB;MACDyB,SAASA,CAAC1C,EAAE,EAAEmB,IAAI;QACd1C,EAAE,CAACzB,yBAAyB,GAAG,IAAI;QACnCyB,EAAE,CAACnD,iBAAiB,GAAG,EAAE;QACzBmD,EAAE,CAACkE,SAAS,GAAG3C,EAAE;QACjBvB,EAAE,CAACmE,YAAY,CAAC,CAAC,EAAEnE,EAAE,CAAC2B,cAAc,EAAC3B,EAAE,CAAC4B,UAAU,EAAE,IAAI,CAAC;QACzD5B,EAAE,CAAChC,eAAe,CAACoG,eAAe,CAACpE,EAAE,CAACkE,SAAS,EAAC,EAAE,EAAC,EAAE,EAAEG,QAAQ,IAAG;UAC9DrE,EAAE,CAACsE,QAAQ,GAAGD,QAAQ,CAACC,QAAQ;UAC/BtE,EAAE,CAACvC,QAAQ,GAAG4G,QAAQ,CAAC5G,QAAQ;UAC/BuC,EAAE,CAACuE,SAAS,GAAGF,QAAQ,CAACpC,IAAI;UAC5BjC,EAAE,CAACwE,WAAW,GAAGH,QAAQ,CAACG,WAAW;UACrCxE,EAAE,CAACyE,UAAU,GAAGJ,QAAQ,CAAC9I,KAAK;UAC9ByE,EAAE,CAAC0E,YAAY,GAAGL,QAAQ,CAACK,YAAY;UACvC1E,EAAE,CAACrC,YAAY,GAAG0G,QAAQ,CAAC1G,YAAY;UACvCqC,EAAE,CAAC2E,YAAY,GAAGN,QAAQ,CAACM,YAAY;UACvC,IAAG3E,EAAE,CAACyE,UAAU,IAAIvK,SAAS,CAACuE,WAAW,CAAC4E,cAAc,EAAC;YACrDrD,EAAE,CAACtB,cAAc,GAAG;cAChBjB,QAAQ,EAAEuC,EAAE,CAAC0E,YAAY;cACzB/G,YAAY,EAAEqC,EAAE,CAACrC,YAAY,GAAGqC,EAAE,CAACrC,YAAY,GAAG;aACrD;YACDqC,EAAE,CAAC/B,eAAe,CAAC2G,QAAQ,CAAC,cAAc,EAAE5E,EAAE,CAAC0E,YAAY,EAAEG,GAAG,IAAG;cAC/D7E,EAAE,CAACvC,QAAQ,GAAG,GAAGoH,GAAG,CAAC,CAAC,CAAC,CAACC,YAAY,MAAMD,GAAG,CAAC,CAAC,CAAC,CAACH,YAAY,EAAE;YACnE,CAAC,CAAC;;UAEN,IAAG1E,EAAE,CAACyE,UAAU,IAAIvK,SAAS,CAACuE,WAAW,CAACkF,cAAc,EAAC;YACrD3D,EAAE,CAACtB,cAAc,GAAG;cAChBiG,YAAY,EAAE3E,EAAE,CAAC2E;aACpB;YACD3E,EAAE,CAAC7B,cAAc,CAAC4G,eAAe,CAAEF,GAAG,IAAG;cACrC,CAACA,GAAG,IAAI,EAAE,EAAEG,OAAO,CAACC,EAAE,IAAG;gBACrB,IAAGA,EAAE,CAACC,IAAI,IAAIb,QAAQ,CAACM,YAAY,EAAC;kBAChC3E,EAAE,CAACnC,QAAQ,GAAG,GAAGoH,EAAE,CAAChD,IAAI,KAAKgD,EAAE,CAACC,IAAI,GAAG;;cAE/C,CAAC,CAAC;YACN,CAAC,CAAC;;QAEV,CAAC,EAAE,IAAI,EAAE,MAAI;UACTlF,EAAE,CAACmF,oBAAoB,CAACC,OAAO,EAAE;QACrC,CAAC,CAAC;MACN;KACH,EACD;MACInD,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DuH,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAC;QACF,WAAW,EAAE;;KAEpB,EACD;MACIN,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DuH,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZc,eAAeA,CAACP,KAAK;QACjB,IAAGA,KAAK,IAAI3I,SAAS,CAACuE,WAAW,CAACiF,WAAW,EAAC;UAC1C,OAAO1D,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAACuE,WAAW,CAACkF,cAAc,EAAC;UACnD,OAAO3D,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;SAC7D,MAAK,IAAGkI,KAAK,IAAI3I,SAAS,CAACuE,WAAW,CAAC4E,cAAc,EAAC;UACnD,OAAOrD,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;;QAE9D,OAAO,EAAE;MACb,CAAC;MACD4H,KAAK,EAAC;QACF,WAAW,EAAE;;KAEpB,EACD;MACIN,IAAI,EAAE,IAAI,CAACvH,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvDuH,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CAAC;IAEF,IAAI,CAAC+C,WAAW,GAAG;MACfrE,gBAAgB,EAAC,KAAK;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIP,IAAI,EAAE,oBAAoB;QAC1BQ,OAAO,EAAE,IAAI,CAAC3G,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzD2G,IAAI,EAAGC,EAAU,IAAG;UAChB;UACA,IAAI,CAAC+D,MAAM,CAACC,QAAQ,CAAC,CAAC,oBAAoB,EAAEhE,EAAE,CAAC,CAAC;QACpD,CAAC;QACDiE,UAAU,EAAEA,CAACjE,EAAS,EAAEmB,IAAI,KAAG;UAC3B,OAAO1C,EAAE,CAACyF,WAAW,CAAC,CAACvL,SAAS,CAACgF,WAAW,CAACwG,SAAS,CAACC,MAAM,CAAC,CAAC;QACnE;OACH,EACD;QACI9E,IAAI,EAAE,mBAAmB;QACzBQ,OAAO,EAAE,IAAI,CAAC3G,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3D2G,IAAI,EAAGC,EAAU,IAAG;UAChBvB,EAAE,CAACmF,oBAAoB,CAACS,OAAO,CAAC,IAAI,CAAClL,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,EAAC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,EAAC;YAACkL,EAAE,EAAEA,CAAA,KAAI;cAC7J,IAAI,CAACC,QAAQ,CAACvE,EAAE,CAAC;YACrB,CAAC;YAAEwE,MAAM,EAAEA,CAAA,KAAI,CAAC;UAAC,CAAC,CAAC;QACvB,CAAC;QACDP,UAAU,EAAEA,CAACjE,EAAS,EAAEmB,IAAI,KAAG;UAC3B,OAAO1C,EAAE,CAACyF,WAAW,CAAC,CAACvL,SAAS,CAACgF,WAAW,CAACwG,SAAS,CAACM,MAAM,CAAC,CAAC;QACnE;OACH;KAER;IACD,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAE,EAAE;IACjB,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAE9B,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,OAAO,GAAE;MACVvE,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD/B,EAAE,CAACmF,oBAAoB,CAACmB,MAAM,EAAE;IAChC,IAAI,CAACtI,eAAe,CAACuI,cAAc,CAAC,EAAE,EAAC;MAAC,GAAG,IAAI,CAACjL,UAAU;MAAC6K,IAAI,EAAC,IAAI,CAACA;IAAI,CAAC,EAAE9B,QAAQ,IAAG;MACnF,IAAI,CAACgC,OAAO,CAACvE,OAAO,GAACuC,QAAQ,CAACvC,OAAO,CAAC0E,GAAG,CAAE9D,IAAQ,IAAG;QAClDA,IAAI,CAAC+D,WAAW,GAAC,IAAI,CAACtH,iBAAiB,CAACuD,IAAI,CAAC+D,WAAW,CAAC;QACzD,OAAO/D,IAAI;MACf,CAAC,CAAC;MACF,IAAI,CAAC2D,OAAO,CAACtE,KAAK,GAAGsC,QAAQ,CAACqC,aAAa;IAC/C,CAAC,EAAE,IAAI,EAAE,MAAI;MACT1G,EAAE,CAACmF,oBAAoB,CAACC,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAuB,QAAQA,CAAA;IACJ;IACA,IAAI,CAACC,MAAM,CAAC,CAAC,EAAC,IAAI,CAACV,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC7K,UAAU,CAAC;EAC5D;EAEAwK,QAAQA,CAACvE,EAAS;IACd,IAAIvB,EAAE,GAAG,IAAI;IACb;IACA,IAAI,CAAChC,eAAe,CAAC6I,cAAc,CAACtF,EAAE,EAAC,EAAE,EAAC,EAAE,EAAE8C,QAAQ,IAAG;MACrDrE,EAAE,CAACmF,oBAAoB,CAAC2B,OAAO,CAAC,IAAI,CAACpM,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;MAC3F;MACA,IAAI,CAACiM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACV,QAAQ,EAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC7K,UAAU,CAAC;IAC5D,CAAC,EAAEyL,KAAK,IAAG;MAACC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IAAA,CAAC,EAAC,MAAI,CAAC,CAAC,CAAC;EAC7C;EAEAH,MAAMA,CAACK,IAAI,EAAEC,KAAK,EAAEf,IAAI,EAACgB,MAAM;IAC3B,IAAInH,EAAE,GAAG,IAAI;IACb,IAAI,CAACiG,UAAU,GAAGgB,IAAI;IACtB,IAAI,CAACf,QAAQ,GAAGgB,KAAK;IACrB,IAAI,CAACf,IAAI,GAAGA,IAAI;IAChB,IAAG,IAAI,CAAC7K,UAAU,CAACC,KAAK,IAAE,IAAI,EAC1B,IAAI,CAACD,UAAU,CAACC,KAAK,GAAC,EAAE;IAC5B,IAAI6L,SAAS,GAAG;MACZ,GAAGD,MAAM;MACTF,IAAI;MACJ9E,IAAI,EAAC+E,KAAK;MACVf;KACH;IACDnG,EAAE,CAACmF,oBAAoB,CAACmB,MAAM,EAAE;IAChC,IAAI,CAACtI,eAAe,CAACuI,cAAc,CAAC,EAAE,EAACa,SAAS,EAAE/C,QAAQ,IAAG;MACzD,IAAI,CAACgC,OAAO,CAACvE,OAAO,GAACuC,QAAQ,CAACvC,OAAO,CAAC0E,GAAG,CAAE9D,IAAQ,IAAG;QAClDA,IAAI,CAAC+D,WAAW,GAAC,IAAI,CAACtH,iBAAiB,CAACuD,IAAI,CAAC+D,WAAW,CAAC;QACzD,OAAO/D,IAAI;MACf,CAAC,CAAC;MACF,IAAI,CAAC2D,OAAO,CAACtE,KAAK,GAAGsC,QAAQ,CAACqC,aAAa;IAC/C,CAAC,EAAE,IAAI,EAAE,MAAI;MACT1G,EAAE,CAACmF,oBAAoB,CAACC,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAjB,YAAYA,CAAC8C,IAAI,EAAEC,KAAK,EAAEf,IAAI,EAAEgB,MAAM;IAClC,IAAInH,EAAE,GAAG,IAAI;IACb,IAAI,CAAC0B,gBAAgB,GAAGuF,IAAI;IAC5B,IAAI,CAACtF,cAAc,GAAGuF,KAAK;IAC3B,IAAI,CAACtF,UAAU,GAAGuE,IAAI;IACtB,IAAIiB,SAAS,GAAG;MACZH,IAAI;MACJ9E,IAAI,EAAC+E,KAAK;MACVf;KACH;IACD,IAAI,CAACnI,eAAe,CAACqJ,qBAAqB,CAAC,IAAI,CAACnD,SAAS,EAAC,EAAE,EAACkD,SAAS,EAAE/C,QAAQ,IAAG;MAC/ErE,EAAE,CAAC6B,aAAa,CAACC,OAAO,GAACuC,QAAQ,CAACvC,OAAO;MACzC9B,EAAE,CAAC6B,aAAa,CAACE,KAAK,GAAGsC,QAAQ,CAACqC,aAAa;IACnD,CAAC,CAAC;EACN;EAEAnD,MAAMA,CAACnD,IAAI;IACP,IAAI,CAACkF,MAAM,CAACC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAC;MAAC+B,WAAW,EAAE;QAAClH;MAAI;IAAC,CAAC,CAAC;EACtE;EAEAlD,iBAAiBA,CAAA;IACb,IAAI,CAACyB,mBAAmB,CAAC4I,MAAM,EAAE;IACjC,IAAI,CAAC3I,aAAa,GAAC,IAAI;IACvB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC9B;EAEA5C,eAAeA,CAAA;IACX,IAAI,CAACqJ,MAAM,CAACC,QAAQ,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAACrB,SAAS,CAAC,CAAC;EAChE;EAEAvH,UAAUA,CAAA;IACN;IACA,IAAG,IAAI,CAACE,iBAAiB,CAACC,MAAM,IAAE,CAAC,EAC/B,OAAO,IAAI;IACf,IAAI,CAAC0K,WAAW,GAAC,EAAE;IACnB,IAAI,CAAC3K,iBAAiB,CAAC2J,GAAG,CAAE9D,IAAI,IAAG;MAC/B,IAAI,CAAC8E,WAAW,CAACC,IAAI,CAAC/E,IAAI,CAACC,MAAM,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAAClB,SAAS,CAAC,IAAI,CAAC+F,WAAW,CAAC;EACpC;EAEA/F,SAASA,CAACD,GAAY;IAClB,IAAI,CAAC2D,oBAAoB,CAACS,OAAO,CAAC,IAAI,CAAClL,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,EAAC,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,EAAE;MACrJkL,EAAE,EAAEA,CAAA,KAAK;QACL,IAAI,CAAC3H,UAAU,CAACwJ,kBAAkB,CAAClG,GAAG,EAACmG,QAAQ,CAAC,IAAI,CAACzD,SAAS,CAAC,EAAEG,QAAQ,IAAG;UACxE;UACA,IAAI,CAACxH,iBAAiB,GAAC,EAAE;UACzB,IAAI,CAACsI,oBAAoB,CAAC2B,OAAO,CAAC,IAAI,CAACpM,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;UAC7F,IAAI,CAACwJ,YAAY,CAAC,CAAC,EAAE,IAAI,CAACxC,cAAc,EAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAAC;QACnE,CAAC,CAAC;MACN,CAAC;MACDmE,MAAM,EAAEA,CAAA,KAAK,CACb;KACH,CAAC;EACN;EAEAzJ,cAAcA,CAAA;IACV,IAAI0D,EAAE,GAAG,IAAI;IACb,IAAI,CAACmF,oBAAoB,CAACS,OAAO,CAAC,IAAI,CAAClL,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,EAAE,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,EAAC;MACvJkL,EAAE,EAAEA,CAAA,KAAI;QACJ7F,EAAE,CAAChC,eAAe,CAAC6I,cAAc,CAAC7G,EAAE,CAACkE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAGG,QAAQ,IAAG;UAChErE,EAAE,CAACmF,oBAAoB,CAAC2B,OAAO,CAAC9G,EAAE,CAACtF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC;UACnFqF,EAAE,CAACsF,MAAM,CAACC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC,CAAC;MACN;KACH,CAAC;EACN;EAEAqC,iBAAiBA,CAACC,IAAI,EAAEC,QAAQ;IAC5B,IAAI,CAAC5J,UAAU,CAAC6J,gBAAgB,CAACF,IAAI,EAAEC,QAAQ,CAAC;EACpD;EAEAE,gBAAgBA,CAAA;IACZ,IAAI,CAACpJ,aAAa,GAAC,KAAK;EAC5B;EAEAqJ,iBAAiBA,CAAA;IACb,IAAI,IAAI,CAACpJ,gBAAgB,CAAC/B,MAAM,GAAG,CAAC,EAAE;MAClC,IAAI,CAACoB,UAAU,CAACgK,cAAc,CAAC,IAAI,CAACrJ,gBAAgB,EAAC;QAAC0C,EAAE,EAACoG,QAAQ,CAAC,IAAI,CAACzD,SAAS;MAAC,CAAC,EAC7EG,QAAQ,IAAG;QACR,IAAI,CAACc,oBAAoB,CAAC2B,OAAO,CAAC,IAAI,CAACpM,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC/F,IAAI,CAACwJ,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC+B,QAAQ,EAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC;MAEvD,CAAC,CAAC;MACN,IAAI,CAACvH,aAAa,GAAC,KAAK;;EAEhC;;;uBApjBSd,iBAAiB,EAAAxD,EAAA,CAAA6N,iBAAA,CAwDLlO,eAAe,GAAAK,EAAA,CAAA6N,iBAAA,CACfhO,eAAe,GAAAG,EAAA,CAAA6N,iBAAA,CACf9N,UAAU,GAAAC,EAAA,CAAA6N,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/N,EAAA,CAAA6N,iBAAA,CAAA7N,EAAA,CAAAgO,QAAA;IAAA;EAAA;;;YA1DtBxK,iBAAiB;MAAAyK,SAAA;MAAAC,QAAA,GAAAlO,EAAA,CAAAmO,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB9BzO,EAAA,CAAAO,cAAA,aAAqG;UAEzDP,EAAA,CAAAmB,MAAA,GAAsD;UAAAnB,EAAA,CAAAkB,YAAA,EAAM;UAChGlB,EAAA,CAAAC,SAAA,sBAAoF;UACxFD,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAO,cAAA,aAAwE;UACpEP,EAAA,CAAA2O,UAAA,IAAAC,0CAAA,2BAAmN;UACvN5O,EAAA,CAAAkB,YAAA,EAAM;UAKVlB,EAAA,CAAAO,cAAA,iBAAsI;UAI/GP,EAAA,CAAAQ,UAAA,yBAAAqO,yDAAA;YAAA,OAAeH,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC,2BAAAyC,2DAAApO,MAAA;YAAA,OAAAgO,GAAA,CAAA1N,UAAA,CAAAgJ,QAAA,GAAAtJ,MAAA;UAAA;UAAjCV,EAAA,CAAAkB,YAAA,EAA8G;UAC9GlB,EAAA,CAAAO,cAAA,iBAA0B;UAAAP,EAAA,CAAAmB,MAAA,IAAoD;UAAAnB,EAAA,CAAAkB,YAAA,EAAQ;UAG9FlB,EAAA,CAAAO,cAAA,cAAmB;UAEJP,EAAA,CAAAQ,UAAA,yBAAAuO,yDAAA;YAAA,OAAeL,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC,2BAAA2C,2DAAAtO,MAAA;YAAA,OAAAgO,GAAA,CAAA1N,UAAA,CAAA2G,IAAA,GAAAjH,MAAA;UAAA;UAAjCV,EAAA,CAAAkB,YAAA,EAAsG;UACtGlB,EAAA,CAAAO,cAAA,iBAA2B;UAAAP,EAAA,CAAAmB,MAAA,IAAqD;UAAAnB,EAAA,CAAAkB,YAAA,EAAQ;UAUhGlB,EAAA,CAAA2O,UAAA,KAAAM,iCAAA,kBAWM;UAENjP,EAAA,CAAAO,cAAA,eAAwB;UAGRP,EAAA,CAAAQ,UAAA,mBAAA0O,sDAAA;YAAA,OAASR,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAC/BrM,EAAA,CAAAkB,YAAA,EAAW;UAKxBlB,EAAA,CAAAO,cAAA,sBAYC;UAVGP,EAAA,CAAAQ,UAAA,+BAAA2O,oEAAAzO,MAAA;YAAA,OAAAgO,GAAA,CAAA5C,WAAA,GAAApL,MAAA;UAAA,EAA6B;UAUhCV,EAAA,CAAAkB,YAAA,EAAa;UACdlB,EAAA,CAAAO,cAAA,oBAAgP;UAAvKP,EAAA,CAAAQ,UAAA,2BAAA4O,8DAAA1O,MAAA;YAAA,OAAAgO,GAAA,CAAAzK,yBAAA,GAAAvD,MAAA;UAAA,EAAuC;UAC5GV,EAAA,CAAAO,cAAA,cAAqG;UAE7FP,EAAA,CAAA2O,UAAA,KAAAU,oCAAA,qBAAyM;UACzMrP,EAAA,CAAA2O,UAAA,KAAAW,oCAAA,qBAAwM;UAC5MtP,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAO,cAAA,eAAyG;UACrGP,EAAA,CAAA2O,UAAA,KAAAY,oCAAA,qBAAiR;UACjRvP,EAAA,CAAA2O,UAAA,KAAAa,oCAAA,qBAA6N;UACjOxP,EAAA,CAAAkB,YAAA,EAAM;UAGVlB,EAAA,CAAAO,cAAA,kBAAmC;UAI4FP,EAAA,CAAAmB,MAAA,IAA0D;UAAAnB,EAAA,CAAAkB,YAAA,EAAQ;UACjLlB,EAAA,CAAAO,cAAA,eAAgE;UAC5DP,EAAA,CAAAmB,MAAA,IACJ;UAAAnB,EAAA,CAAAkB,YAAA,EAAM;UAGdlB,EAAA,CAAAO,cAAA,eAA6B;UAE4FP,EAAA,CAAAmB,MAAA,IAA4D;UAAAnB,EAAA,CAAAkB,YAAA,EAAQ;UACrLlB,EAAA,CAAAO,cAAA,eAAsE;UAClEP,EAAA,CAAA2O,UAAA,KAAAc,kCAAA,mBAAkH;UAClHzP,EAAA,CAAA2O,UAAA,KAAAe,kCAAA,mBAAwH;UACxH1P,EAAA,CAAA2O,UAAA,KAAAgB,kCAAA,mBAAwH;UAC5H3P,EAAA,CAAAkB,YAAA,EAAM;UAGdlB,EAAA,CAAAO,cAAA,eAA6B;UAE0FP,EAAA,CAAAmB,MAAA,IAA2D;UAAAnB,EAAA,CAAAkB,YAAA,EAAQ;UAClLlB,EAAA,CAAAO,cAAA,eAAsD;UAClDP,EAAA,CAAAmB,MAAA,IACJ;UAAAnB,EAAA,CAAAkB,YAAA,EAAM;UAGdlB,EAAA,CAAA2O,UAAA,KAAAiB,iCAAA,kBAOM;UACN5P,EAAA,CAAA2O,UAAA,KAAAkB,iCAAA,kBAOM;UACN7P,EAAA,CAAA2O,UAAA,KAAAmB,iCAAA,kBAOM;UACN9P,EAAA,CAAA2O,UAAA,KAAAoB,iCAAA,kBAOM;UACN/P,EAAA,CAAAO,cAAA,eAA6B;UAE2FP,EAAA,CAAAmB,MAAA,IAA6D;UAAAnB,EAAA,CAAAkB,YAAA,EAAQ;UACrLlB,EAAA,CAAAO,cAAA,eAAsD;UAClDP,EAAA,CAAAmB,MAAA,IAAe;UAAAnB,EAAA,CAAAO,cAAA,YAAM;UAAAP,EAAA,CAAAmB,MAAA,cAAM;UAAAnB,EAAA,CAAAkB,YAAA,EAAO;UAQtDlB,EAAA,CAAAO,cAAA,sBAUC;UARGP,EAAA,CAAAQ,UAAA,+BAAAwP,oEAAAtP,MAAA;YAAA,OAAAgO,GAAA,CAAAnM,iBAAA,GAAA7B,MAAA;UAAA,EAAmC;UAQtCV,EAAA,CAAAkB,YAAA,EAAa;UAEdlB,EAAA,CAAAO,cAAA,wBAA+M;UAAjKP,EAAA,CAAAQ,UAAA,2BAAAyP,8DAAAvP,MAAA;YAAA,OAAAgO,GAAA,CAAApK,aAAA,GAAA5D,MAAA;UAAA,EAA2B;UAErEV,EAAA,CAAAO,cAAA,eAAkF;UACTP,EAAA,CAAAmB,MAAA,IAAsD;UAAAnB,EAAA,CAAAO,cAAA,gBAA2B;UAAAP,EAAA,CAAAmB,MAAA,SAAC;UAAAnB,EAAA,CAAAkB,YAAA,EAAO;UAC9JlB,EAAA,CAAAO,cAAA,eAAyB;UAIjBP,EAAA,CAAAQ,UAAA,yBAAA0P,+DAAAxP,MAAA;YAAA,OAAAgO,GAAA,CAAAnK,gBAAA,GAAA7D,MAAA;UAAA,EAA4B;UAS/BV,EAAA,CAAAkB,YAAA,EAAc;UAGvBlB,EAAA,CAAAO,cAAA,eAA6D;UACzCP,EAAA,CAAAQ,UAAA,mBAAA2P,oDAAA;YAAA,OAASzB,GAAA,CAAAhB,gBAAA,EAAkB;UAAA,EAAC;UAA8C1N,EAAA,CAAAmB,MAAA,IAA6D;UAAAnB,EAAA,CAAAkB,YAAA,EAAS;UAChKlB,EAAA,CAAAO,cAAA,kBAAuH;UAAvGP,EAAA,CAAAQ,UAAA,mBAAA4P,oDAAA;YAAA,OAAS1B,GAAA,CAAAf,iBAAA,EAAmB;UAAA,EAAC;UAA0E3N,EAAA,CAAAkB,YAAA,EAAS;;;UAxLhGlB,EAAA,CAAAoB,SAAA,GAAsD;UAAtDpB,EAAA,CAAAuB,iBAAA,CAAAmN,GAAA,CAAAtO,WAAA,CAAAC,SAAA,8BAAsD;UACnDL,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAE,UAAA,UAAAwO,GAAA,CAAAtI,KAAA,CAAe,SAAAsI,GAAA,CAAApI,IAAA;UAGtCtG,EAAA,CAAAoB,SAAA,GAAoD;UAApDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvD,WAAA,CAAAnL,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAA5B,GAAA,CAAA/J,cAAA,CAAAyG,SAAA,CAAAmF,MAAA,GAAoD;UAM5CvQ,EAAA,CAAAoB,SAAA,GAAmB;UAAnBpB,EAAA,CAAAE,UAAA,oBAAmB,WAAAwO,GAAA,CAAAtO,WAAA,CAAAC,SAAA;UAIuCL,EAAA,CAAAoB,SAAA,GAAiC;UAAjCpB,EAAA,CAAAE,UAAA,YAAAwO,GAAA,CAAA1N,UAAA,CAAAgJ,QAAA,CAAiC;UACjFhK,EAAA,CAAAoB,SAAA,GAAoD;UAApDpB,EAAA,CAAAuB,iBAAA,CAAAmN,GAAA,CAAAtO,WAAA,CAAAC,SAAA,4BAAoD;UAKRL,EAAA,CAAAoB,SAAA,GAA6B;UAA7BpB,EAAA,CAAAE,UAAA,YAAAwO,GAAA,CAAA1N,UAAA,CAAA2G,IAAA,CAA6B;UACxE3H,EAAA,CAAAoB,SAAA,GAAqD;UAArDpB,EAAA,CAAAuB,iBAAA,CAAAmN,GAAA,CAAAtO,WAAA,CAAAC,SAAA,6BAAqD;UAUpEL,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAA3I,gBAAA,CAAsB;UAuB9C/F,EAAA,CAAAoB,SAAA,GAAgB;UAAhBpB,EAAA,CAAAE,UAAA,iBAAgB,gBAAAwO,GAAA,CAAA5C,WAAA,aAAA4C,GAAA,CAAAjF,OAAA,aAAAiF,GAAA,CAAA3C,OAAA,aAAA2C,GAAA,CAAA3D,WAAA,cAAA2D,GAAA,CAAApC,MAAA,CAAAkE,IAAA,CAAA9B,GAAA,iBAAAA,GAAA,CAAA/C,UAAA,cAAA+C,GAAA,CAAA9C,QAAA,UAAA8C,GAAA,CAAA7C,IAAA,YAAA6C,GAAA,CAAA1N,UAAA,gBAAA0N,GAAA,CAAA1K,UAAA;UAY4GhE,EAAA,CAAAoB,SAAA,GAA6C;UAA7CpB,EAAA,CAAAyQ,UAAA,CAAAzQ,EAAA,CAAA0Q,eAAA,KAAAC,GAAA,EAA6C;UAAnK3Q,EAAA,CAAAE,UAAA,WAAAwO,GAAA,CAAAtO,WAAA,CAAAC,SAAA,+BAA8D,YAAAqO,GAAA,CAAAzK,yBAAA;UAG3CjE,EAAA,CAAAoB,SAAA,GAA2D;UAA3DpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvD,WAAA,CAAAnL,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAA5B,GAAA,CAAA9O,SAAA,CAAAgF,WAAA,CAAAwG,SAAA,CAAAC,MAAA,GAA2D;UAC3DrL,EAAA,CAAAoB,SAAA,GAA2D;UAA3DpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvD,WAAA,CAAAnL,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAA5B,GAAA,CAAA9O,SAAA,CAAAgF,WAAA,CAAAwG,SAAA,CAAAM,MAAA,GAA2D;UAG3D1L,EAAA,CAAAoB,SAAA,GAA2D;UAA3DpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvD,WAAA,CAAAnL,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAA5B,GAAA,CAAA9O,SAAA,CAAAgF,WAAA,CAAAwG,SAAA,CAAAC,MAAA,GAA2D;UAC3DrL,EAAA,CAAAoB,SAAA,GAA2D;UAA3DpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvD,WAAA,CAAAnL,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAA5B,GAAA,CAAA9O,SAAA,CAAAgF,WAAA,CAAAwG,SAAA,CAAAC,MAAA,GAA2D;UAI5ErL,EAAA,CAAAoB,SAAA,GAA0B;UAA1BpB,EAAA,CAAAE,UAAA,2BAA0B;UAI6FF,EAAA,CAAAoB,SAAA,GAA0D;UAA1DpB,EAAA,CAAAiD,kBAAA,KAAAyL,GAAA,CAAAtO,WAAA,CAAAC,SAAA,iCAA0D;UAErKL,EAAA,CAAAoB,SAAA,GACJ;UADIpB,EAAA,CAAAiD,kBAAA,MAAAyL,GAAA,CAAA1E,QAAA,MACJ;UAKiHhK,EAAA,CAAAoB,SAAA,GAA4D;UAA5DpB,EAAA,CAAAiD,kBAAA,KAAAyL,GAAA,CAAAtO,WAAA,CAAAC,SAAA,mCAA4D;UAElKL,EAAA,CAAAoB,SAAA,GAAiD;UAAjDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAAkF,WAAA,CAAiD;UACjDpJ,EAAA,CAAAoB,SAAA,GAAoD;UAApDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAAmF,cAAA,CAAoD;UACpDrJ,EAAA,CAAAoB,SAAA,GAAoD;UAApDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAA6E,cAAA,CAAoD;UAMgD/I,EAAA,CAAAoB,SAAA,GAA2D;UAA3DpB,EAAA,CAAAiD,kBAAA,KAAAyL,GAAA,CAAAtO,WAAA,CAAAC,SAAA,kCAA2D;UAEtKL,EAAA,CAAAoB,SAAA,GACJ;UADIpB,EAAA,CAAAiD,kBAAA,MAAAyL,GAAA,CAAAzE,SAAA,MACJ;UAGsBjK,EAAA,CAAAoB,SAAA,GAAoD;UAApDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAA6E,cAAA,CAAoD;UAQpD/I,EAAA,CAAAoB,SAAA,GAAoE;UAApEpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAA6E,cAAA,IAAA2F,GAAA,CAAArL,YAAA,CAAoE;UAQpErD,EAAA,CAAAoB,SAAA,GAAoD;UAApDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAAmF,cAAA,CAAoD;UAQpDrJ,EAAA,CAAAoB,SAAA,GAAiD;UAAjDpB,EAAA,CAAAE,UAAA,SAAAwO,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxK,iBAAA,CAAAkF,WAAA,CAAiD;UAUyCpJ,EAAA,CAAAoB,SAAA,GAA6D;UAA7DpB,EAAA,CAAAiD,kBAAA,KAAAyL,GAAA,CAAAtO,WAAA,CAAAC,SAAA,oCAA6D;UAEzKL,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAiD,kBAAA,MAAAyL,GAAA,CAAAxE,WAAA,KAAe;UAS/BlK,EAAA,CAAAoB,SAAA,GAAoB;UAApBpB,EAAA,CAAAE,UAAA,qBAAoB,gBAAAwO,GAAA,CAAAnM,iBAAA,aAAAmM,GAAA,CAAAhH,aAAA,aAAAgH,GAAA,CAAAnH,aAAA,aAAAmH,GAAA,CAAAjI,iBAAA,cAAAiI,GAAA,CAAA7E,YAAA,CAAA2G,IAAA,CAAA9B,GAAA,iBAAAA,GAAA,CAAAtH,gBAAA,cAAAsH,GAAA,CAAArH,cAAA,UAAAqH,GAAA,CAAApH,UAAA;UAW2HtH,EAAA,CAAAoB,SAAA,GAAyB;UAAzBpB,EAAA,CAAAyQ,UAAA,CAAAzQ,EAAA,CAAA0Q,eAAA,KAAAE,GAAA,EAAyB;UAAnJ5Q,EAAA,CAAAE,UAAA,WAAAwO,GAAA,CAAAlK,SAAA,CAAoB,YAAAkK,GAAA,CAAApK,aAAA,wEAAAtE,EAAA,CAAA0Q,eAAA,KAAAG,GAAA;UAGgC7Q,EAAA,CAAAoB,SAAA,GAAsD;UAAtDpB,EAAA,CAAAuB,iBAAA,CAAAmN,GAAA,CAAAtO,WAAA,CAAAC,SAAA,8BAAsD;UAGnHL,EAAA,CAAAoB,SAAA,GAA+B;UAA/BpB,EAAA,CAAAE,UAAA,YAAAwO,GAAA,CAAArK,mBAAA,CAA+B,UAAAqK,GAAA,CAAAnK,gBAAA,iBAAAmK,GAAA,CAAAjK,cAAA,kBAAAiK,GAAA,CAAAtK,cAAA,cAAAsK,GAAA,CAAApB,iBAAA,CAAAkD,IAAA,CAAA9B,GAAA;UAemD1O,EAAA,CAAAoB,SAAA,GAA6D;UAA7DpB,EAAA,CAAAuB,iBAAA,CAAAmN,GAAA,CAAAtO,WAAA,CAAAC,SAAA,gCAA6D;UACzGL,EAAA,CAAAoB,SAAA,GAA8B;UAA9BpB,EAAA,CAAAE,UAAA,UAAAwO,GAAA,CAAAhK,oBAAA,CAA8B,aAAAgK,GAAA,CAAAnK,gBAAA,CAAA/B,MAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}