{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LoginRoutingModule } from './login-routing.module';\nimport { LoginComponent } from './login.component';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CommonVnptModule } from '../../common-module/common.module';\nimport { DialogModule } from \"primeng/dialog\";\nimport { RecaptchaModule } from \"ng-recaptcha\";\nimport * as i0 from \"@angular/core\";\nexport class LoginModule {\n  static {\n    this.ɵfac = function LoginModule_Factory(t) {\n      return new (t || LoginModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LoginModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule, CommonVnptModule, DialogModule, CommonModule, FormsModule, ReactiveFormsModule, CommonVnptModule, CommonVnptModule, RecaptchaModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LoginModule, {\n    declarations: [LoginComponent],\n    imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule, CommonVnptModule, DialogModule, CommonModule, FormsModule, ReactiveFormsModule, CommonVnptModule, CommonVnptModule, RecaptchaModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "LoginRoutingModule", "LoginComponent", "ButtonModule", "CheckboxModule", "FormsModule", "ReactiveFormsModule", "PasswordModule", "InputTextModule", "CommonVnptModule", "DialogModule", "RecaptchaModule", "LoginModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\login\\login.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { LoginRoutingModule } from './login-routing.module';\r\nimport { LoginComponent } from './login.component';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\r\nimport { PasswordModule } from 'primeng/password';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { CommonVnptModule } from '../../common-module/common.module';\r\nimport { DialogModule } from \"primeng/dialog\";\r\nimport {RecaptchaModule} from \"ng-recaptcha\";\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        LoginRoutingModule,\r\n        ButtonModule,\r\n        CheckboxModule,\r\n        InputTextModule,\r\n        FormsModule,\r\n        PasswordModule,\r\n        CommonVnptModule,\r\n        DialogModule,\r\n        CommonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        CommonVnptModule,\r\n        CommonVnptModule,\r\n        RecaptchaModule,\r\n    ],\r\n    declarations: [LoginComponent]\r\n})\r\nexport class LoginModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,cAAc;;AAqB5C,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAlBhBZ,YAAY,EACZC,kBAAkB,EAClBE,YAAY,EACZC,cAAc,EACdI,eAAe,EACfH,WAAW,EACXE,cAAc,EACdE,gBAAgB,EAChBC,YAAY,EACZV,YAAY,EACZK,WAAW,EACXC,mBAAmB,EACnBG,gBAAgB,EAChBA,gBAAgB,EAChBE,eAAe;IAAA;EAAA;;;2EAIVC,WAAW;IAAAC,YAAA,GAFLX,cAAc;IAAAY,OAAA,GAhBzBd,YAAY,EACZC,kBAAkB,EAClBE,YAAY,EACZC,cAAc,EACdI,eAAe,EACfH,WAAW,EACXE,cAAc,EACdE,gBAAgB,EAChBC,YAAY,EACZV,YAAY,EACZK,WAAW,EACXC,mBAAmB,EACnBG,gBAAgB,EAChBA,gBAAgB,EAChBE,eAAe;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}