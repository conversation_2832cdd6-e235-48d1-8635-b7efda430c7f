{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LayoutService {\n  constructor() {\n    this.typeMenu = \"big\";\n    this.isShowMenu = true;\n    this.config = {\n      ripple: false,\n      inputStyle: 'outlined',\n      menuMode: 'static',\n      colorScheme: 'light',\n      theme: 'lara-light-indigo',\n      scale: 14\n    };\n    this.state = {\n      staticMenuDesktopInactive: false,\n      overlayMenuActive: false,\n      profileSidebarVisible: false,\n      configSidebarVisible: false,\n      staticMenuMobileActive: false,\n      menuHoverActive: false\n    };\n    this.configUpdate = new Subject();\n    this.overlayOpen = new Subject();\n    this.configUpdate$ = this.configUpdate.asObservable();\n    this.overlayOpen$ = this.overlayOpen.asObservable();\n  }\n  onMenuToggle() {\n    this.isShowMenu = !this.isShowMenu;\n    if (this.isOverlay()) {\n      this.state.overlayMenuActive = !this.state.overlayMenuActive;\n      if (this.state.overlayMenuActive) {\n        this.overlayOpen.next(null);\n      }\n    }\n    if (this.isDesktop()) {\n      this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;\n    } else {\n      this.state.staticMenuMobileActive = !this.state.staticMenuMobileActive;\n      if (this.state.staticMenuMobileActive) {\n        this.overlayOpen.next(null);\n      }\n    }\n  }\n  changeSize(type) {\n    this.typeMenu = type;\n  }\n  showProfileSidebar() {\n    this.state.profileSidebarVisible = !this.state.profileSidebarVisible;\n    if (this.state.profileSidebarVisible) {\n      this.overlayOpen.next(null);\n    }\n  }\n  showConfigSidebar() {\n    this.state.configSidebarVisible = true;\n  }\n  isOverlay() {\n    return this.config.menuMode === 'overlay';\n  }\n  isDesktop() {\n    return window.innerWidth > 991;\n  }\n  isMobile() {\n    return !this.isDesktop();\n  }\n  onConfigUpdate() {\n    this.configUpdate.next(this.config);\n  }\n  static {\n    this.ɵfac = function LayoutService_Factory(t) {\n      return new (t || LayoutService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LayoutService,\n      factory: LayoutService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "LayoutService", "constructor", "typeMenu", "isShowMenu", "config", "ripple", "inputStyle", "menuMode", "colorScheme", "theme", "scale", "state", "staticMenuDesktopInactive", "overlayMenuActive", "profileSidebarVisible", "configSidebarVisible", "staticMenuMobileActive", "menuHoverActive", "configUpdate", "overlayOpen", "configUpdate$", "asObservable", "overlayOpen$", "onMenuToggle", "isOverlay", "next", "isDesktop", "changeSize", "type", "showProfileSidebar", "showConfigSidebar", "window", "innerWidth", "isMobile", "onConfigUpdate", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\app.layout.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\n\r\nexport interface AppConfig {\r\n    inputStyle: string;\r\n    colorScheme: string;\r\n    theme: string;\r\n    ripple: boolean;\r\n    menuMode: string;\r\n    scale: number;\r\n}\r\n\r\ninterface LayoutState {\r\n    staticMenuDesktopInactive: boolean;\r\n    overlayMenuActive: boolean;\r\n    profileSidebarVisible: boolean;\r\n    configSidebarVisible: boolean;\r\n    staticMenuMobileActive: boolean;\r\n    menuHoverActive: boolean;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class LayoutService {\r\n    typeMenu: string = \"big\";\r\n    isShowMenu:boolean = true;\r\n\r\n    config: AppConfig = {\r\n        ripple: false,\r\n        inputStyle: 'outlined',\r\n        menuMode: 'static',\r\n        colorScheme: 'light',\r\n        theme: 'lara-light-indigo',\r\n        scale: 14,\r\n    };\r\n\r\n    state: LayoutState = {\r\n        staticMenuDesktopInactive: false,\r\n        overlayMenuActive: false,\r\n        profileSidebarVisible: false,\r\n        configSidebarVisible: false,\r\n        staticMenuMobileActive: false,\r\n        menuHoverActive: false\r\n    };\r\n\r\n    private configUpdate = new Subject<AppConfig>();\r\n\r\n    private overlayOpen = new Subject<any>();\r\n\r\n    configUpdate$ = this.configUpdate.asObservable();\r\n\r\n    overlayOpen$ = this.overlayOpen.asObservable();\r\n\r\n    onMenuToggle() {\r\n        this.isShowMenu = !this.isShowMenu;\r\n        if (this.isOverlay()) {\r\n            this.state.overlayMenuActive = !this.state.overlayMenuActive;\r\n            if (this.state.overlayMenuActive) {\r\n                this.overlayOpen.next(null);\r\n            }\r\n        }\r\n\r\n        if (this.isDesktop()) {\r\n            this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;\r\n        }\r\n        else {\r\n            this.state.staticMenuMobileActive = !this.state.staticMenuMobileActive;\r\n\r\n            if (this.state.staticMenuMobileActive) {\r\n                this.overlayOpen.next(null);\r\n            }\r\n        }\r\n    }\r\n\r\n    changeSize(type:string){\r\n        this.typeMenu = type;\r\n    }\r\n\r\n    showProfileSidebar() {\r\n        this.state.profileSidebarVisible = !this.state.profileSidebarVisible;\r\n        if (this.state.profileSidebarVisible) {\r\n            this.overlayOpen.next(null);\r\n        }\r\n    }\r\n\r\n    showConfigSidebar() {\r\n        this.state.configSidebarVisible = true;\r\n    }\r\n\r\n    isOverlay() {\r\n        return this.config.menuMode === 'overlay';\r\n    }\r\n\r\n    isDesktop() {\r\n        return window.innerWidth > 991;\r\n    }\r\n\r\n    isMobile() {\r\n        return !this.isDesktop();\r\n    }\r\n\r\n    onConfigUpdate() {\r\n        this.configUpdate.next(this.config);\r\n    }\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,MAAM;;AAuB9B,OAAM,MAAOC,aAAa;EAH1BC,YAAA;IAII,KAAAC,QAAQ,GAAW,KAAK;IACxB,KAAAC,UAAU,GAAW,IAAI;IAEzB,KAAAC,MAAM,GAAc;MAChBC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,OAAO;MACpBC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE;KACV;IAED,KAAAC,KAAK,GAAgB;MACjBC,yBAAyB,EAAE,KAAK;MAChCC,iBAAiB,EAAE,KAAK;MACxBC,qBAAqB,EAAE,KAAK;MAC5BC,oBAAoB,EAAE,KAAK;MAC3BC,sBAAsB,EAAE,KAAK;MAC7BC,eAAe,EAAE;KACpB;IAEO,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAa;IAEvC,KAAAoB,WAAW,GAAG,IAAIpB,OAAO,EAAO;IAExC,KAAAqB,aAAa,GAAG,IAAI,CAACF,YAAY,CAACG,YAAY,EAAE;IAEhD,KAAAC,YAAY,GAAG,IAAI,CAACH,WAAW,CAACE,YAAY,EAAE;;EAE9CE,YAAYA,CAAA;IACR,IAAI,CAACpB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACqB,SAAS,EAAE,EAAE;MAClB,IAAI,CAACb,KAAK,CAACE,iBAAiB,GAAG,CAAC,IAAI,CAACF,KAAK,CAACE,iBAAiB;MAC5D,IAAI,IAAI,CAACF,KAAK,CAACE,iBAAiB,EAAE;QAC9B,IAAI,CAACM,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC;;;IAInC,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;MAClB,IAAI,CAACf,KAAK,CAACC,yBAAyB,GAAG,CAAC,IAAI,CAACD,KAAK,CAACC,yBAAyB;KAC/E,MACI;MACD,IAAI,CAACD,KAAK,CAACK,sBAAsB,GAAG,CAAC,IAAI,CAACL,KAAK,CAACK,sBAAsB;MAEtE,IAAI,IAAI,CAACL,KAAK,CAACK,sBAAsB,EAAE;QACnC,IAAI,CAACG,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC;;;EAGvC;EAEAE,UAAUA,CAACC,IAAW;IAClB,IAAI,CAAC1B,QAAQ,GAAG0B,IAAI;EACxB;EAEAC,kBAAkBA,CAAA;IACd,IAAI,CAAClB,KAAK,CAACG,qBAAqB,GAAG,CAAC,IAAI,CAACH,KAAK,CAACG,qBAAqB;IACpE,IAAI,IAAI,CAACH,KAAK,CAACG,qBAAqB,EAAE;MAClC,IAAI,CAACK,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC;;EAEnC;EAEAK,iBAAiBA,CAAA;IACb,IAAI,CAACnB,KAAK,CAACI,oBAAoB,GAAG,IAAI;EAC1C;EAEAS,SAASA,CAAA;IACL,OAAO,IAAI,CAACpB,MAAM,CAACG,QAAQ,KAAK,SAAS;EAC7C;EAEAmB,SAASA,CAAA;IACL,OAAOK,MAAM,CAACC,UAAU,GAAG,GAAG;EAClC;EAEAC,QAAQA,CAAA;IACJ,OAAO,CAAC,IAAI,CAACP,SAAS,EAAE;EAC5B;EAEAQ,cAAcA,CAAA;IACV,IAAI,CAAChB,YAAY,CAACO,IAAI,CAAC,IAAI,CAACrB,MAAM,CAAC;EACvC;;;uBAhFSJ,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAmC,OAAA,EAAbnC,aAAa,CAAAoC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}