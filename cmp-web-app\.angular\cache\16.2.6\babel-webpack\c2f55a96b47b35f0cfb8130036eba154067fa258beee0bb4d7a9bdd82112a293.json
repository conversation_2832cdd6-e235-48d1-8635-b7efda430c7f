{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class APILogService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/user-mgmt\";\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/searchAPI`, {\n      timeout: 120000\n    }, params, callback, errorCallBack, finallyCallback);\n  }\n  detail(apnId, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${apnId}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function APILogService_Factory(t) {\n      return new (t || APILogService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: APILogService,\n      factory: APILogService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "APILogService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "timeout", "detail", "apnId", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\api-log\\APILogService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n@Injectable()\r\nexport class APILogService {\r\n    private prefixApi: string;\r\n\r\n    constructor(@Inject(HttpService) private httpService: HttpService) {\r\n        this.prefixApi = \"/user-mgmt\";\r\n    }\r\n\r\n    public search(params: {\r\n        [key: string]: any\r\n    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {\r\n        this.httpService.get(`${this.prefixApi}/searchAPI`, {timeout: 120000}, params, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public detail(apnId: number|string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/${apnId}`,{},{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAEnD,OAAM,MAAOC,aAAa;EAGtBC,YAAyCC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,YAAY;EACjC;EAEOC,MAAMA,CAACC,MAEb,EAAEC,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACxE,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,YAAY,EAAE;MAACO,OAAO,EAAE;IAAM,CAAC,EAAEL,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC5H;EACOG,MAAMA,CAACC,KAAoB,EAAEN,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACtG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIS,KAAK,EAAE,EAAC,EAAE,EAAC,EAAE,EAAEN,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACtG;;;uBAdSR,aAAa,EAAAa,EAAA,CAAAC,QAAA,CAGFf,WAAW;IAAA;EAAA;;;aAHtBC,aAAa;MAAAe,OAAA,EAAbf,aAAa,CAAAgB;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}