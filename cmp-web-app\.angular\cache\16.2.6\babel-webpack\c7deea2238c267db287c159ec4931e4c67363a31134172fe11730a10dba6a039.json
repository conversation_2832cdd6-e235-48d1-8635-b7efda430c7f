{"ast": null, "code": "export default {\n  label: {\n    name: \"<PERSON><PERSON><PERSON>\",\n    customer: \"<PERSON><PERSON><PERSON><PERSON> hàng\",\n    contractCode: \"<PERSON><PERSON> hợp đồng\",\n    statusSIM: \"Tình trạng thuê bao\",\n    subscriptionNumber: \"<PERSON><PERSON> thuê bao\",\n    group: \"<PERSON>h<PERSON><PERSON> thuê bao\",\n    status: \"Trạng thái\",\n    afterTime: \"Sau thời gian\",\n    afterCount: \"Sau số lần\",\n    unit: \"Đơn vị\",\n    unitValue: \"Giá trị\",\n    description: \"<PERSON><PERSON> tả\",\n    level: \"Mức độ\",\n    groupReceiving: \"Nhóm nhận cảnh báo\",\n    url: \"URL:\",\n    emails: \"Email nhận cảnh báo\",\n    topic: \"Ch<PERSON> đề email\",\n    contentEmail: \"Nội dung email\",\n    contentSms: \"Nội dung SMS\",\n    sms: \"Số điện thoại nhận SMS\",\n    alertreceivinggroup: \"Tên nhóm nhận cảnh báo\",\n    simstatus: \"Tình trạng Sim\",\n    time: \"Thờ<PERSON> gian\",\n    fromdate: \"Thời gian từ\",\n    todate: \"Thời gian đến\",\n    minutes: \"Phút\",\n    news: \"Bản tin\",\n    rule: \"Loại\",\n    event: \"Điều kiện kích hoạt\",\n    action: \"Loại hành động\",\n    appliedPlan: \"Gói cước áp dụng\",\n    frequency: \"Tần suất lặp\",\n    repeat: \"Lặp lại\",\n    exceededPakage: \"Ngưỡng gói cước (%)\",\n    exceededValue: \"Ngưỡng giá trị (MB)\",\n    smsExceededPakage: \"Ngưỡng SMS (%)\",\n    smsExceededValue: \"Ngưỡng giá trị (Số lượng)\",\n    inactivePopup: \"Bấm để ngừng hoạt động\",\n    activePopup: \"Bấm để kích hoạt\",\n    wallet: \"Ví lưu lượng\",\n    thresholdValue: \"Giá trị ngưỡng\",\n    walletEmail: \"Email chủ ví nhận cảnh báo\",\n    walletPhone: \"SĐT chủ ví nhận cảnh báo\"\n  },\n  text: {\n    inputName: \"Nhập tên quy tắc\",\n    inputStatusSIM: \"Chọn tình trạng thuê bao\",\n    inputCustomer: \"Chọn khách hàng\",\n    inputContractCode: \"Chọn mã hợp đồng\",\n    inputSubscriptionNumber: \"Chọn số thuê bao\",\n    inputGroup: \"Chọn nhóm thuê bao\",\n    inputafterTime: \"Nhập thời gian\",\n    inputafterCount: \"Nhập số lần\",\n    inputunit: \"Chọn đơn vị\",\n    inputunitValue: \"Nhập giá trị\",\n    inputDescription: \"Nhập mô tả\",\n    inputlevel: \"Chọn mức độ\",\n    inputgroupReceiving: \"Chọn nhóm nhận cảnh báo\",\n    inputurl: \"Nhập URL:\",\n    inputemails: \"Nhập email nhận cảnh báo\",\n    inputtopic: \"Nhập chủ đề email\",\n    inputcontentEmail: \"Nhập nội dung email\",\n    inputcontentSms: \"Nhập nội dung sms\",\n    inputsms: \"Nhập số điện thoại nhận sms\",\n    headerAPI: \"Nhận quy tắc qua API\",\n    headerEmail: \"Nhận quy tắc qua Email\",\n    headerSMS: \"Nhận quy tắc qua SMS\",\n    labelAlert: \"Thông tin nhận quy tắc\",\n    inputNameReceiving: \"Nhập tên nhóm nhận cảnh báo\",\n    removeAlert: \"Xóa Email\",\n    removeSms: \"Xóa SMS\",\n    rule: \"Chọn loại\",\n    eventType: \"Chọn điều kiện kích hoạt\",\n    appliedPlan: \"Nhập gói cước áp dụng\",\n    actionType: \"Chọn hành động\",\n    filterApplieInfo: \"Lọc thông tin áp dụng\",\n    sendNotifyExpiredData: \"Gửi thông báo khi gói sắp hết hiệu lực trước\",\n    hour: \"giờ\",\n    day: \"Ngày\",\n    sendType: \"Hình thức gửi thông báo\"\n  },\n  status: {\n    active: \"Hoạt động\",\n    inactive: \"Ngừng hoạt động\"\n  },\n  type: {\n    admin: \"Admin\",\n    customer: \"Customer\",\n    province: \"Province\",\n    district: \"District\",\n    agency: \"Agency\"\n  },\n  statusSim: {\n    outPlan: \"Data chạm ngưỡng gói\",\n    outLine: \"Data chạm ngưỡng giá trị\",\n    disconnected: \"Mất kết nối\",\n    newConnection: \"Phát sinh kết nối mới\"\n  },\n  receiving: {\n    name: \"Tên nhóm nhận quy tắc\",\n    description: \"Mô tả\",\n    emails: \"Email\",\n    sms: \"SMS\"\n  },\n  event: {\n    thresholddatapacket: \"Data chạm ngưỡng gói\",\n    thresholddatavalue: \"Data chạm ngưỡng giá trị\",\n    lostconnecting: \"Mất kết nối\",\n    connecting: \"Phát sinh kết nối\",\n    purgesim: \"Huỷ SIM\"\n  },\n  severity: {\n    critical: \"Nghiêm trọng\",\n    major: \"Cao\",\n    minor: \"Trung bình\",\n    info: \"Thấp\"\n  },\n  eventType: {\n    exceededPakage: \"Data chạm ngưỡng gói\",\n    exceededValue: \"Data chạm ngưỡng giá trị\",\n    sessionEnd: \"Mất kết nối\",\n    sessionStart: \"Phát sinh kết nối mới\",\n    smsExceededPakage: \"SMS chạm ngưỡng gói\",\n    smsExceededValue: \"SMS chạm ngưỡng giá trị \",\n    owLock: \"Khoá 1 chiều\",\n    twLock: \"Khoá 2 chiều\",\n    noConection: \"Không kết nối\",\n    simExp: \"Hết hạn gói cước\",\n    datapoolExp: \"Datapool qúa hạn\",\n    subExp: \"Hết hạn thuê bao\",\n    dataWalletExp: \"Hết hạn ví lưu lượng\",\n    owtwlock: \"Khoá 1 chiều - 2 chiều\",\n    walletThreshold: \"Ví lưu lượng vượt ngưỡng giá trị\"\n  },\n  actionType: {\n    alert: \"Cảnh báo\",\n    api: \"API\"\n  },\n  ruleCategory: {\n    monitoring: \"Giám sát sử dụng\",\n    management: \"Quản lý sử dụng gói cước\"\n  },\n  message: {\n    existedPlan: \"Gói cước đã được sử dụng\",\n    checkboxRequired: \"Vui lòng chọn ít nhất 1 check box\",\n    exceededPakage: \"Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng [value] % của gói cước [plan_name]\",\n    smsExceededPakage: \"Cảnh báo thuê bao [msisdn] sử dụng SMS chạm ngưỡng [value] % của gói cước [plan_name]\",\n    exceededValue: \"Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng giá trị [value] MB\",\n    smsExceededValue: \"Cảnh báo thuê bao [msisdn] sử dụng số lượng SMS chạm ngưỡng giá trị [value]\",\n    status: \"Cảnh báo thuê bao [msisdn] đã [status]\"\n  }\n};", "map": {"version": 3, "names": ["label", "name", "customer", "contractCode", "statusSIM", "subscriptionNumber", "group", "status", "afterTime", "afterCount", "unit", "unitValue", "description", "level", "groupReceiving", "url", "emails", "topic", "contentEmail", "contentSms", "sms", "alertreceivinggroup", "simstatus", "time", "fromdate", "todate", "minutes", "news", "rule", "event", "action", "appliedPlan", "frequency", "repeat", "exceeded<PERSON>akage", "exceededValue", "smsExceededPakage", "smsExceededValue", "inactivePopup", "activePopup", "wallet", "thresholdValue", "walletEmail", "walletPhone", "text", "inputName", "inputStatusSIM", "inputCustomer", "inputContractCode", "inputSubscriptionNumber", "inputGroup", "inputafterTime", "inputafterCount", "inputunit", "inputunitValue", "inputDescription", "inputlevel", "inputgroupReceiving", "<PERSON><PERSON><PERSON>", "inputemails", "inputtopic", "inputcontentEmail", "inputcontentSms", "inputsms", "headerAPI", "headerEmail", "headerSMS", "labelAlert", "inputNameReceiving", "<PERSON><PERSON><PERSON><PERSON>", "removeSms", "eventType", "actionType", "filterApplieInfo", "sendNotifyExpiredData", "hour", "day", "sendType", "active", "inactive", "type", "admin", "province", "district", "agency", "statusSim", "outPlan", "outLine", "disconnected", "newConnection", "receiving", "thresholddatapacket", "thresholddatavalue", "lostconnecting", "connecting", "purgesim", "severity", "critical", "major", "minor", "info", "sessionEnd", "sessionStart", "owLock", "twLock", "noConection", "simExp", "datapoolExp", "subExp", "dataWalletExp", "owtwlock", "walletThreshold", "alert", "api", "ruleCategory", "monitoring", "management", "message", "existedPlan", "checkboxRequired"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\alert.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        name: \"<PERSON><PERSON><PERSON>\",\r\n        customer: \"<PERSON><PERSON><PERSON><PERSON> hàng\",\r\n        contractCode: \"<PERSON><PERSON> hợp đồng\",\r\n        statusSIM: \"Tình trạng thuê bao\",\r\n        subscriptionNumber: \"<PERSON><PERSON> thuê bao\",\r\n        group: \"<PERSON>h<PERSON><PERSON> thuê bao\",\r\n        status: \"Trạng thái\",\r\n        afterTime: \"Sau thời gian\",\r\n        afterCount: \"Sau số lần\",\r\n        unit: \"Đơn vị\",\r\n        unitValue: \"Giá trị\",\r\n        description: \"<PERSON><PERSON> tả\",\r\n        level: \"Mức độ\",\r\n        groupReceiving: \"Nhóm nhận cảnh báo\",\r\n        url: \"URL:\",\r\n        emails: \"Email nhận cảnh báo\",\r\n        topic: \"Ch<PERSON> đề email\",\r\n        contentEmail: \"Nội dung email\",\r\n        contentSms: \"Nội dung SMS\",\r\n        sms: \"Số điện thoại nhận SMS\",\r\n        alertreceivinggroup: \"Tên nhóm nhận cảnh báo\",\r\n        simstatus: \"Tình trạng Sim\",\r\n        time: \"Thờ<PERSON> gian\",\r\n        fromdate: \"Thời gian từ\",\r\n        todate: \"Thời gian đến\",\r\n        minutes: \"Phút\",\r\n        news: \"Bản tin\",\r\n        rule: \"Loại\",\r\n        event: \"Điều kiện kích hoạt\",\r\n        action: \"Loại hành động\",\r\n        appliedPlan: \"Gói cước áp dụng\",\r\n        frequency:\"Tần suất lặp\",\r\n        repeat:\"Lặp lại\",\r\n        exceededPakage:\"Ngưỡng gói cước (%)\",\r\n        exceededValue: \"Ngưỡng giá trị (MB)\",\r\n        smsExceededPakage:\"Ngưỡng SMS (%)\",\r\n        smsExceededValue:\"Ngưỡng giá trị (Số lượng)\",\r\n        inactivePopup: \"Bấm để ngừng hoạt động\" ,\r\n        activePopup :\"Bấm để kích hoạt\",\r\n        wallet: \"Ví lưu lượng\",\r\n        thresholdValue: \"Giá trị ngưỡng\",\r\n        walletEmail: \"Email chủ ví nhận cảnh báo\",\r\n        walletPhone: \"SĐT chủ ví nhận cảnh báo\",\r\n    },\r\n    text: {\r\n        inputName: \"Nhập tên quy tắc\",\r\n        inputStatusSIM: \"Chọn tình trạng thuê bao\",\r\n        inputCustomer: \"Chọn khách hàng\",\r\n        inputContractCode: \"Chọn mã hợp đồng\",\r\n        inputSubscriptionNumber: \"Chọn số thuê bao\",\r\n        inputGroup: \"Chọn nhóm thuê bao\",\r\n        inputafterTime: \"Nhập thời gian\",\r\n        inputafterCount: \"Nhập số lần\",\r\n        inputunit: \"Chọn đơn vị\",\r\n        inputunitValue: \"Nhập giá trị\",\r\n        inputDescription: \"Nhập mô tả\",\r\n        inputlevel: \"Chọn mức độ\",\r\n        inputgroupReceiving: \"Chọn nhóm nhận cảnh báo\",\r\n        inputurl: \"Nhập URL:\",\r\n        inputemails: \"Nhập email nhận cảnh báo\",\r\n        inputtopic: \"Nhập chủ đề email\",\r\n        inputcontentEmail: \"Nhập nội dung email\",\r\n        inputcontentSms: \"Nhập nội dung sms\",\r\n        inputsms: \"Nhập số điện thoại nhận sms\",\r\n        headerAPI: \"Nhận quy tắc qua API\",\r\n        headerEmail: \"Nhận quy tắc qua Email\",\r\n        headerSMS: \"Nhận quy tắc qua SMS\",\r\n        labelAlert: \"Thông tin nhận quy tắc\",\r\n        inputNameReceiving: \"Nhập tên nhóm nhận cảnh báo\",\r\n        removeAlert: \"Xóa Email\",\r\n        removeSms: \"Xóa SMS\",\r\n        rule: \"Chọn loại\",\r\n        eventType: \"Chọn điều kiện kích hoạt\",\r\n        appliedPlan: \"Nhập gói cước áp dụng\",\r\n        actionType: \"Chọn hành động\",\r\n        filterApplieInfo: \"Lọc thông tin áp dụng\",\r\n        sendNotifyExpiredData : \"Gửi thông báo khi gói sắp hết hiệu lực trước\",\r\n        hour: \"giờ\",\r\n        day : \"Ngày\",\r\n        sendType : \"Hình thức gửi thông báo\"\r\n    },\r\n    status: {\r\n        active: \"Hoạt động\",\r\n        inactive: \"Ngừng hoạt động\",\r\n    },\r\n    type: {\r\n        admin: \"Admin\",\r\n        customer: \"Customer\",\r\n        province: \"Province\",\r\n        district: \"District\",\r\n        agency: \"Agency\",\r\n    },\r\n    statusSim: {\r\n        outPlan: \"Data chạm ngưỡng gói\",\r\n        outLine: \"Data chạm ngưỡng giá trị\",\r\n        disconnected: \"Mất kết nối\",\r\n        newConnection: \"Phát sinh kết nối mới\"\r\n    },\r\n\r\n    receiving: {\r\n        name: \"Tên nhóm nhận quy tắc\",\r\n        description: \"Mô tả\",\r\n        emails: \"Email\",\r\n        sms: \"SMS\",\r\n    },\r\n    event: {\r\n        thresholddatapacket: \"Data chạm ngưỡng gói\",\r\n        thresholddatavalue: \"Data chạm ngưỡng giá trị\",\r\n        lostconnecting: \"Mất kết nối\",\r\n        connecting: \"Phát sinh kết nối\",\r\n        purgesim: \"Huỷ SIM\",\r\n    },\r\n    severity: {\r\n        critical: \"Nghiêm trọng\",\r\n        major: \"Cao\",\r\n        minor: \"Trung bình\",\r\n        info: \"Thấp\",\r\n    },\r\n    eventType:{\r\n        exceededPakage:\"Data chạm ngưỡng gói\",\r\n        exceededValue: \"Data chạm ngưỡng giá trị\",\r\n        sessionEnd : \"Mất kết nối\",\r\n        sessionStart : \"Phát sinh kết nối mới\",\r\n        smsExceededPakage:\"SMS chạm ngưỡng gói\",\r\n        smsExceededValue:\"SMS chạm ngưỡng giá trị \",\r\n        owLock:\"Khoá 1 chiều\",\r\n        twLock:\"Khoá 2 chiều\",\r\n        noConection:\"Không kết nối\",\r\n        simExp:\"Hết hạn gói cước\",\r\n        datapoolExp:\"Datapool qúa hạn\",\r\n        subExp: \"Hết hạn thuê bao\",\r\n        dataWalletExp: \"Hết hạn ví lưu lượng\",\r\n        owtwlock: \"Khoá 1 chiều - 2 chiều\",\r\n        walletThreshold: \"Ví lưu lượng vượt ngưỡng giá trị\",\r\n    },\r\n    actionType:{\r\n        alert:\"Cảnh báo\",\r\n        api:\"API\"\r\n    },\r\n    ruleCategory:{\r\n        monitoring:\"Giám sát sử dụng\",\r\n        management:\"Quản lý sử dụng gói cước\",\r\n    },\r\n    message:{\r\n        existedPlan:\"Gói cước đã được sử dụng\",\r\n        checkboxRequired : \"Vui lòng chọn ít nhất 1 check box\",\r\n        exceededPakage: \"Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng [value] % của gói cước [plan_name]\",\r\n        smsExceededPakage: \"Cảnh báo thuê bao [msisdn] sử dụng SMS chạm ngưỡng [value] % của gói cước [plan_name]\",\r\n        exceededValue: \"Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng giá trị [value] MB\",\r\n        smsExceededValue: \"Cảnh báo thuê bao [msisdn] sử dụng số lượng SMS chạm ngưỡng giá trị [value]\",\r\n        status: \"Cảnh báo thuê bao [msisdn] đã [status]\",\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,aAAa;IAC3BC,SAAS,EAAE,qBAAqB;IAChCC,kBAAkB,EAAE,aAAa;IACjCC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,YAAY;IACpBC,SAAS,EAAE,eAAe;IAC1BC,UAAU,EAAE,YAAY;IACxBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,OAAO;IACpBC,KAAK,EAAE,QAAQ;IACfC,cAAc,EAAE,oBAAoB;IACpCC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAE,qBAAqB;IAC7BC,KAAK,EAAE,cAAc;IACrBC,YAAY,EAAE,gBAAgB;IAC9BC,UAAU,EAAE,cAAc;IAC1BC,GAAG,EAAE,wBAAwB;IAC7BC,mBAAmB,EAAE,wBAAwB;IAC7CC,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,eAAe;IACvBC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE,gBAAgB;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAC,cAAc;IACxBC,MAAM,EAAC,SAAS;IAChBC,cAAc,EAAC,qBAAqB;IACpCC,aAAa,EAAE,qBAAqB;IACpCC,iBAAiB,EAAC,gBAAgB;IAClCC,gBAAgB,EAAC,2BAA2B;IAC5CC,aAAa,EAAE,wBAAwB;IACvCC,WAAW,EAAE,kBAAkB;IAC/BC,MAAM,EAAE,cAAc;IACtBC,cAAc,EAAE,gBAAgB;IAChCC,WAAW,EAAE,4BAA4B;IACzCC,WAAW,EAAE;GAChB;EACDC,IAAI,EAAE;IACFC,SAAS,EAAE,kBAAkB;IAC7BC,cAAc,EAAE,0BAA0B;IAC1CC,aAAa,EAAE,iBAAiB;IAChCC,iBAAiB,EAAE,kBAAkB;IACrCC,uBAAuB,EAAE,kBAAkB;IAC3CC,UAAU,EAAE,oBAAoB;IAChCC,cAAc,EAAE,gBAAgB;IAChCC,eAAe,EAAE,aAAa;IAC9BC,SAAS,EAAE,aAAa;IACxBC,cAAc,EAAE,cAAc;IAC9BC,gBAAgB,EAAE,YAAY;IAC9BC,UAAU,EAAE,aAAa;IACzBC,mBAAmB,EAAE,yBAAyB;IAC9CC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,0BAA0B;IACvCC,UAAU,EAAE,mBAAmB;IAC/BC,iBAAiB,EAAE,qBAAqB;IACxCC,eAAe,EAAE,mBAAmB;IACpCC,QAAQ,EAAE,6BAA6B;IACvCC,SAAS,EAAE,sBAAsB;IACjCC,WAAW,EAAE,wBAAwB;IACrCC,SAAS,EAAE,sBAAsB;IACjCC,UAAU,EAAE,wBAAwB;IACpCC,kBAAkB,EAAE,6BAA6B;IACjDC,WAAW,EAAE,WAAW;IACxBC,SAAS,EAAE,SAAS;IACpB1C,IAAI,EAAE,WAAW;IACjB2C,SAAS,EAAE,0BAA0B;IACrCxC,WAAW,EAAE,uBAAuB;IACpCyC,UAAU,EAAE,gBAAgB;IAC5BC,gBAAgB,EAAE,uBAAuB;IACzCC,qBAAqB,EAAG,8CAA8C;IACtEC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAG,MAAM;IACZC,QAAQ,EAAG;GACd;EACDtE,MAAM,EAAE;IACJuE,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE;GACb;EACDC,IAAI,EAAE;IACFC,KAAK,EAAE,OAAO;IACd/E,QAAQ,EAAE,UAAU;IACpBgF,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE;GACX;EACDC,SAAS,EAAE;IACPC,OAAO,EAAE,sBAAsB;IAC/BC,OAAO,EAAE,0BAA0B;IACnCC,YAAY,EAAE,aAAa;IAC3BC,aAAa,EAAE;GAClB;EAEDC,SAAS,EAAE;IACPzF,IAAI,EAAE,uBAAuB;IAC7BW,WAAW,EAAE,OAAO;IACpBI,MAAM,EAAE,OAAO;IACfI,GAAG,EAAE;GACR;EACDS,KAAK,EAAE;IACH8D,mBAAmB,EAAE,sBAAsB;IAC3CC,kBAAkB,EAAE,0BAA0B;IAC9CC,cAAc,EAAE,aAAa;IAC7BC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE;GACb;EACDC,QAAQ,EAAE;IACNC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;GACT;EACD7B,SAAS,EAAC;IACNrC,cAAc,EAAC,sBAAsB;IACrCC,aAAa,EAAE,0BAA0B;IACzCkE,UAAU,EAAG,aAAa;IAC1BC,YAAY,EAAG,uBAAuB;IACtClE,iBAAiB,EAAC,qBAAqB;IACvCC,gBAAgB,EAAC,0BAA0B;IAC3CkE,MAAM,EAAC,cAAc;IACrBC,MAAM,EAAC,cAAc;IACrBC,WAAW,EAAC,eAAe;IAC3BC,MAAM,EAAC,kBAAkB;IACzBC,WAAW,EAAC,kBAAkB;IAC9BC,MAAM,EAAE,kBAAkB;IAC1BC,aAAa,EAAE,sBAAsB;IACrCC,QAAQ,EAAE,wBAAwB;IAClCC,eAAe,EAAE;GACpB;EACDvC,UAAU,EAAC;IACPwC,KAAK,EAAC,UAAU;IAChBC,GAAG,EAAC;GACP;EACDC,YAAY,EAAC;IACTC,UAAU,EAAC,kBAAkB;IAC7BC,UAAU,EAAC;GACd;EACDC,OAAO,EAAC;IACJC,WAAW,EAAC,0BAA0B;IACtCC,gBAAgB,EAAG,mCAAmC;IACtDrF,cAAc,EAAE,wFAAwF;IACxGE,iBAAiB,EAAE,uFAAuF;IAC1GD,aAAa,EAAE,wEAAwE;IACvFE,gBAAgB,EAAE,6EAA6E;IAC/F9B,MAAM,EAAE;;CAGf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}