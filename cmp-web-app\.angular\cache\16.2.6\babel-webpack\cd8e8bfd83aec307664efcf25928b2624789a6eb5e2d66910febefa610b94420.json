{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class TicketService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/ticket\";\n  }\n  searchTicketConfig(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/searchConfig`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  getDetailTicketConfig(provinceCode, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/detailConfig/${provinceCode}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  updateTicketConfig(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/updateConfig`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  searchTicket(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  getDetailTicket(ticketId, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${ticketId}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  updateTicket(ticketId, body, callback, errorCallback, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/update/${ticketId}`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  createTicket(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/create`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  getListAssignee(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/getListAssginee`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  sendMailNotify(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/sendMailNotify`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  countNotify(params, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/countNotify`, {}, params, callback, errorCallback, finallyCallback);\n  }\n  downloadTemplate() {\n    this.httpService.downloadLocal(`/assets/data/Template_active_IMSI.xlsx`, \"Template_active_IMSI.xlsx\");\n  }\n  static {\n    this.ɵfac = function TicketService_Factory(t) {\n      return new (t || TicketService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TicketService,\n      factory: TicketService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "TicketService", "constructor", "httpService", "prefixApi", "searchTicketConfig", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "getDetailTicketConfig", "provinceCode", "<PERSON><PERSON><PERSON><PERSON>", "updateTicketConfig", "body", "post", "searchTicket", "getDetailTicket", "ticketId", "updateTicket", "put", "createTicket", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendMailNotify", "countNotify", "downloadTemplate", "downloadLocal", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\ticket\\TicketService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class TicketService{\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/ticket\";\r\n    }\r\n\r\n    public searchTicketConfig(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/searchConfig`,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n    public getDetailTicketConfig(provinceCode: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/detailConfig/${provinceCode}`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public updateTicketConfig(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/updateConfig`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public searchTicket(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/search`,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getDetailTicket(ticketId: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/${ticketId}`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public updateTicket(ticketId, body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/update/${ticketId}`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public createTicket(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/create`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public getListAssignee(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/getListAssginee`,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public sendMailNotify(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/sendMailNotify`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public countNotify(params:{[key:string]:any},callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/countNotify`, {},params, callback, errorCallback, finallyCallback);\r\n    }\r\n    public downloadTemplate() {\r\n        this.httpService.downloadLocal(`/assets/data/Template_active_IMSI.xlsx`, \"Template_active_IMSI.xlsx\");\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,aAAa;EAEtBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,SAAS;EAC9B;EAEOC,kBAAkBA,CAACC,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACvH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,eAAe,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC9G;EACOE,qBAAqBA,CAACC,YAAoB,EAAEL,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IACvH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,iBAAiBQ,YAAY,EAAE,EAAC,EAAE,EAAE,EAAE,EAAEL,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EAC3H;EAEOK,kBAAkBA,CAACC,IAAI,EAACR,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IACnG,IAAI,CAACN,WAAW,CAACa,IAAI,CAAC,GAAG,IAAI,CAACZ,SAAS,eAAe,EAAE,EAAE,EAACW,IAAI,EAAC,EAAE,EAAER,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EACjH;EAEOQ,YAAYA,CAACX,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACjH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;EAEOS,eAAeA,CAACC,QAAgB,EAAEZ,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IAC7G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIe,QAAQ,EAAE,EAAC,EAAE,EAAE,EAAE,EAAEZ,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EAC1G;EAEOW,YAAYA,CAACD,QAAQ,EAAEJ,IAAI,EAACR,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IACvG,IAAI,CAACN,WAAW,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACjB,SAAS,WAAWe,QAAQ,EAAE,EAAE,EAAE,EAACJ,IAAI,EAAC,EAAE,EAAER,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EACtH;EAEOa,YAAYA,CAACP,IAAI,EAACR,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IAC7F,IAAI,CAACN,WAAW,CAACa,IAAI,CAAC,GAAG,IAAI,CAACZ,SAAS,SAAS,EAAE,EAAE,EAACW,IAAI,EAAC,EAAE,EAAER,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EAC3G;EAEOc,eAAeA,CAACjB,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACpH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,kBAAkB,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACjH;EAEOe,cAAcA,CAACT,IAAI,EAACR,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IAC/F,IAAI,CAACN,WAAW,CAACa,IAAI,CAAC,GAAG,IAAI,CAACZ,SAAS,iBAAiB,EAAE,EAAE,EAACW,IAAI,EAAC,EAAE,EAAER,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EACnH;EAEOgB,WAAWA,CAACnB,MAAyB,EAACC,QAAmB,EAAEM,aAAuB,EAAEJ,eAA0B;IACjH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,cAAc,EAAE,EAAE,EAACE,MAAM,EAAEC,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EAC9G;EACOiB,gBAAgBA,CAAA;IACnB,IAAI,CAACvB,WAAW,CAACwB,aAAa,CAAC,wCAAwC,EAAE,2BAA2B,CAAC;EACzG;;;uBA9CS1B,aAAa,EAAA2B,EAAA,CAAAC,QAAA,CAEF7B,WAAW;IAAA;EAAA;;;aAFtBC,aAAa;MAAA6B,OAAA,EAAb7B,aAAa,CAAA8B;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}