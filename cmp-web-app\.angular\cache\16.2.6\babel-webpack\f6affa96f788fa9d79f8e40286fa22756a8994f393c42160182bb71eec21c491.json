{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ComponentBase } from \"../../../../component.base\";\nimport { CONSTANTS } from \"../../../../service/comon/constants\";\nimport { FormControl, FormGroup, Validators } from \"@angular/forms\";\nimport { ComboLazyControl } from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport { TrafficWalletService } from \"../../../../service/datapool/TrafficWalletService\";\nimport { ShareManagementService } from \"../../../../service/datapool/ShareManagementService\";\nimport { noneWhitespaceValidator } from \"../../../common-module/validatorCustoms\";\nimport * as Excel from \"exceljs\";\nimport * as moment from \"moment/moment\";\nimport { saveAs } from \"file-saver\";\nimport * as FileSaver from \"file-saver\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../service/group-sub-wallet/GroupSubWalletService\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i7 from \"primeng/dialog\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/inputtextarea\";\nimport * as i14 from \"primeng/table\";\nimport * as i15 from \"../../../../service/datapool/TrafficWalletService\";\nimport * as i16 from \"../../../../service/datapool/ShareManagementService\";\nfunction GroupSubWalletCreateComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.tranService.translate(\"groupSim.error.requiredError\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.tranService.translate(\"groupSim.error.lengthError_16\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.tranService.translate(\"groupSim.error.characterError_code\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.tranService.translate(\"datapool.error.existedGroupCode\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.tranService.translate(\"groupSim.error.requiredError\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.tranService.translate(\"groupSim.error.lengthError_255\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.tranService.translate(\"groupSim.error.characterError_name\"), \" \");\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction GroupSubWalletCreateComponent_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction GroupSubWalletCreateComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, GroupSubWalletCreateComponent_div_39_div_1_Template, 2, 2, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.createGroupForm.get(\"description\").errors.maxlength);\n  }\n}\nfunction GroupSubWalletCreateComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.tranService.translate(\"datapool.message.digitError\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"th\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"datapool.label.phone\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"datapool.label.fullName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"datapool.label.email\"));\n  }\n}\nconst _c1 = function () {\n  return {\n    len: 50\n  };\n};\nfunction GroupSubWalletCreateComponent_ng_template_54_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c1)), \" \");\n  }\n}\nconst _c2 = function () {\n  return {\n    len: 150\n  };\n};\nfunction GroupSubWalletCreateComponent_ng_template_54_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.tranService.translate(\"global.message.wrongFormatName\", i0.ɵɵpureFunction0(1, _c2)), \" \");\n  }\n}\nconst _c3 = function () {\n  return {\n    len: 100\n  };\n};\nfunction GroupSubWalletCreateComponent_ng_template_54_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c3)), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_ng_template_54_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.tranService.translate(\"global.message.formatEmail\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 52);\n    i0.ɵɵlistener(\"input\", function GroupSubWalletCreateComponent_ng_template_54_Template_input_input_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const index_r18 = restoredCtx.rowIndex;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.changeDataName($event, index_r18));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GroupSubWalletCreateComponent_ng_template_54_div_5_Template, 2, 2, \"div\", 15);\n    i0.ɵɵtemplate(6, GroupSubWalletCreateComponent_ng_template_54_div_6_Template, 2, 2, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 52);\n    i0.ɵɵlistener(\"input\", function GroupSubWalletCreateComponent_ng_template_54_Template_input_input_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const index_r18 = restoredCtx.rowIndex;\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.changeDataMail($event, index_r18));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, GroupSubWalletCreateComponent_ng_template_54_div_9_Template, 2, 2, \"div\", 15);\n    i0.ɵɵtemplate(10, GroupSubWalletCreateComponent_ng_template_54_div_10_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_ng_template_54_Template_button_click_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const index_r18 = restoredCtx.rowIndex;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.deleteItem(index_r18));\n    });\n    i0.ɵɵelement(13, \"i\", 54);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const list_r17 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(list_r17.phoneReceipt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", list_r17.name)(\"readonly\", ctx_r10.userInfo.type == ctx_r10.CONSTANTS.USER_TYPE.CUSTOMER && list_r17.createdBy != null && list_r17.createdBy != ctx_r10.userInfo.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (list_r17.name == null ? null : list_r17.name.length) >= 50);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.utilService.checkValidCharacterVietnamese(list_r17.name));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", list_r17.email)(\"readonly\", ctx_r10.userInfo.type == ctx_r10.CONSTANTS.USER_TYPE.CUSTOMER && list_r17.createdBy != null && list_r17.createdBy != ctx_r10.userInfo.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (list_r17.email == null ? null : list_r17.email.length) >= 100);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isMailInvalid(list_r17.email));\n  }\n}\nfunction GroupSubWalletCreateComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 55)(2, \"div\", 56)(3, \"span\", 57);\n    i0.ɵɵtext(4, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.tranService.translate(\"global.text.nodata\"), \" \");\n  }\n}\nfunction GroupSubWalletCreateComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_div_69_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.resetFile());\n    });\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSubWalletCreateComponent_small_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction GroupSubWalletCreateComponent_small_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"global.message.maxsizeupload\", i0.ɵɵpureFunction0(1, _c1)));\n  }\n}\nfunction GroupSubWalletCreateComponent_small_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.options.messageErrorType ? ctx_r15.options.messageErrorType : ctx_r15.tranService.translate(\"global.message.invalidtypeupload\"));\n  }\n}\nconst _c4 = function () {\n  return {\n    width: \"800px\",\n    overflowY: \"scroll\",\n    maxHeight: \"80%\",\n    height: \"400px\"\n  };\n};\nconst _c5 = function () {\n  return {\n    \"overflow\": \"visible\"\n  };\n};\nconst _c6 = function () {\n  return {\n    \"min-width\": \"50rem\"\n  };\n};\nconst _c7 = function () {\n  return {\n    width: \"800px\",\n    overflowY: \"scroll\",\n    maxHeight: \"80%\"\n  };\n};\nconst _c8 = function (a0) {\n  return {\n    \"width\": a0\n  };\n};\nexport class GroupSubWalletCreateComponent extends ComponentBase {\n  constructor(injector, formBuilder, groupSubWalletService, walletService, shareService) {\n    super(injector);\n    this.formBuilder = formBuilder;\n    this.groupSubWalletService = groupSubWalletService;\n    this.walletService = walletService;\n    this.shareService = shareService;\n    this.createGroupForm = new FormGroup({\n      groupCode: new FormControl(\"\", [Validators.required, Validators.maxLength(16), Validators.pattern('^[A-Za-z0-9_-]+$')]),\n      groupName: new FormControl(\"\", [Validators.required, Validators.maxLength(255), Validators.pattern('^[a-zA-Z0-9\\\\- _\\\\u00C0-\\\\u024F\\\\u1E00-\\\\u1EFF]+$'), noneWhitespaceValidator()]),\n      description: new FormControl(\"\", [Validators.maxLength(255)])\n    });\n    this.boxSimAddController = new ComboLazyControl();\n    this.displayAddSim = false;\n    this.phoneReceiptSelect = \"\";\n    this.isClickCreate = true;\n    this.phoneList = [];\n    this.isValidPhone = true;\n    this.isShowDialogAddSub = false;\n    this.isShowDialogAddFile = false;\n    this.isShowErrorUpload = false;\n    this.listGroup = [];\n    this.exportFile = (bytes, fileName, fileType) => {\n      const file = new Blob([bytes], {\n        type: fileType || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n      });\n      FileSaver.saveAs(file, fileName);\n    };\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.isExistGroupCode = false;\n    this.formObject = {\n      file: null\n    };\n    this.formInstance = this.formBuilder.group(this.formObject);\n    this.textDescription = this.tranService.translate(\"global.button.uploadFile\");\n    me.userInfo = this.sessionService.userInfo;\n    this.options = {\n      type: ['xls', 'xlsx'],\n      messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\n      maxSize: 10,\n      unit: \"MB\",\n      required: true,\n      isShowButtonUpload: true,\n      actionUpload: this.uploadFile.bind(this),\n      disabled: false\n    };\n    if (me.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])) {\n      this.items = [{\n        label: this.tranService.translate(\"global.menu.trafficManagement\")\n      }, {\n        label: this.tranService.translate(\"global.menu.listGroupSub\"),\n        routerLink: \"/data-pool/group/listGroupSub\"\n      }, {\n        label: this.tranService.translate(\"datapool.label.createGroupShare\")\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n      this.shareList = [];\n      me.getAllGroup();\n    } else {\n      window.location.hash = \"/access\";\n    }\n  }\n  getAllGroup() {\n    let me = this;\n    me.messageCommonService.onload();\n    me.groupSubWalletService.getAllGroup(response => {\n      me.listGroup = response;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getListShareInfoCbb(params, callback) {\n    return this.shareService.getListShareInfoCbb(params, response => {\n      this.phoneList = response.content;\n      callback(response);\n    });\n  }\n  submitForm() {\n    let me = this;\n    let dataParams = {\n      groupCode: me.createGroupForm.value.groupCode.trim(),\n      groupName: me.createGroupForm.value.groupName.trim(),\n      description: me.createGroupForm.value.description.trim(),\n      listSub: me.shareList\n    };\n    this.messageCommonService.onload();\n    this.groupSubWalletService.create({}, dataParams, {}, response => {\n      me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      this.router.navigate([`/data-pool/group/edit/${response.id}`]);\n    }, error => {\n      if (error.error.error.errorCode === \"error.duplicate.value\") {\n        me.messageCommonService.error(this.tranService.translate(\"datapool.error.existedGroupCode\"));\n      }\n    }, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  checkValidAdd() {\n    this.isClickCreate = true;\n    if (!this.phoneList.find(dta => dta.phoneReceipt.toString() === this.phoneReceiptSelect)) {\n      this.isClickCreate = false;\n    } else {\n      this.isClickCreate = true;\n    }\n    if (this.phoneReceiptSelect == \"\" || this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined) {\n      this.isClickCreate = true;\n    }\n    const regex = /^0[0-9]{9,10}$/;\n    const inputValue = this.phoneReceiptSelect;\n    this.isValidPhone = regex.test(inputValue);\n  }\n  cleanArray(arr) {\n    return arr.filter(item => item !== null && item !== undefined && item !== \"\");\n  }\n  addSubToGroup() {\n    let me = this;\n    me.isShowDialogAddSub = true;\n  }\n  addSubFile() {\n    let me = this;\n    me.isShowDialogAddFile = true;\n  }\n  cancelAddSub() {\n    let me = this;\n    me.isShowDialogAddSub = false;\n    me.shareList = [];\n    me.phoneReceiptSelect = \"\";\n  }\n  addPhone(data) {\n    let me = this;\n    if (data === null || data === undefined) {\n      return;\n    }\n    me.isClickCreate = false;\n    const value = me.phoneList.find(dta => dta.phoneReceipt === data);\n    const phone = String(data)?.replace(/^0/, \"84\");\n    //check trước khi vào\n    let exists = this.shareList.some(item => item.phoneReceipt.toString() === phone);\n    if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\n      me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\n      return;\n    } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {\n      me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\n      return;\n    } else if (exists) {\n      console.log(\"vào check trc\");\n      me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\n      return;\n    }\n    if (value?.idGroup) {\n      me.messageCommonService.error(me.tranService.translate('datapool.message.duplicateSub', {\n        data: data,\n        groupName: value?.groupName\n      }));\n    } else {\n      me.addPhoneTable(value, data);\n      /**\n       * UAT 2.4 issue 31\n       * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:\n       * - Chia sẻ thường\n       * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động\n       * - Thêm thuê bao vào nhóm\n       * - icon chia sẻ ở Danh sách ví\n       */\n      // me.messageCommonService.onload()\n      // me.walletService.checkParticipant({phoneNumber : phone},\n      //     (response)=>{\n      //         if(response.error_code === \"0\" && (response.result === \"02\" || response.result === \"11\")){\n      //             me.addPhoneTable(value, data)\n      //         }else if(response.error_code === \"0\" && response.result === \"0\"){\n      //             if(isVinaphoneNumber(data)){\n      //                 me.addPhoneTable(value, data)\n      //             }else{\n      //                 me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\n      //             }\n      //         }else{\n      //             me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\n      //         }\n      //     },\n      //     null,()=>{\n      //         me.messageCommonService.offload();\n      //     })\n    }\n  }\n\n  addPhoneNotInSelect(phone) {\n    let me = this;\n    if (!phone) {\n      return;\n    }\n    const value = me.phoneList.find(dta => dta.phoneReceipt === phone);\n    const phoneValid = String(phone)?.replace(/^0/, \"84\");\n    //check trước khi vào\n    let exists = this.shareList.some(item => item.phoneReceipt.toString() === phoneValid);\n    if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\n      me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\n      return;\n    } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {\n      me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\n      return;\n    } else if (exists) {\n      console.log(\"vào check trc\");\n      me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\n      return;\n    }\n    me.groupSubWalletService.checkPhoneBelongGroup({\n      phoneNumber: phone\n    }, response => {\n      if (response) {\n        me.messageCommonService.error(me.tranService.translate('Thuê bao này đã thuộc nhóm khác'));\n      } else {\n        if (value?.idGroup) {\n          me.messageCommonService.error(`Thuê bao đang thuộc nhóm \"${value.groupName}\"`);\n        } else {\n          me.addPhoneTable(value, phone);\n        }\n        /**\n         * bỏ check số vina\n         */\n        // me.messageCommonService.onload()\n        // me.walletService.checkParticipant({phoneNumber : phoneValid},\n        //     (response)=>{\n        //         if (value?.idGroup) {\n        //             me.messageCommonService.error(`Thuê bao đang thuộc nhóm \"${value.groupName}\"`);\n        //         } else if(response.error_code === \"0\" && (response.result === \"02\" || response.result === \"11\") && !value?.idGroup){\n        //             me.addPhoneTable(value, phone);\n        //             me.phoneReceiptSelect = \"\";\n        //         } else if(response.error_code === \"0\" && response.result === \"0\" && !value?.idGroup){\n        //             if(isVinaphoneNumber(phone)){\n        //                 me.addPhoneTable(value, phone);\n        //                 me.phoneReceiptSelect = \"\";\n        //             }else{\n        //                 me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\n        //             }\n        //         }else{\n        //             me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\n        //         }\n        //     },\n        //     null,()=>{\n        //         me.messageCommonService.offload();\n        //     })\n        me.phoneReceiptSelect = \"\";\n        this.getListShareInfoCbb.call(this);\n      }\n    }, null, null);\n  }\n  addPhoneTable(value, data) {\n    console.log(value);\n    let me = this;\n    let exists = this.shareList.some(item => item.phoneReceipt === data);\n    if (value) {\n      if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\n      } else if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT) {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\n      } else if (exists) {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\n      } else {\n        me.shareList.unshift(value);\n        setTimeout(function () {\n          me.phoneReceiptSelect = \"\";\n        }, 100);\n      }\n    } else {\n      let pushData = {\n        id: value?.id || \"\",\n        phoneReceipt: data,\n        name: value?.name || \"\",\n        email: value?.email || \"\"\n      };\n      if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\n      } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\n      } else if (exists) {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\n      } else {\n        me.shareList.unshift(pushData);\n      }\n    }\n    me.isClickCreate = true;\n  }\n  changeDataName(event, i) {\n    const shareValue = event.target.value;\n    this.shareList[i].name = shareValue;\n  }\n  changeDataMail(event, i) {\n    const shareValue = event.target.value;\n    this.shareList[i].email = shareValue;\n    this.isAllEmailsValid();\n  }\n  isMailInvalid(email) {\n    if (!email) {\n      return false;\n    }\n    // const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$/\n    const pattern = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$/;\n    return !pattern.test(email);\n  }\n  // Hàm kiểm tra xem tất cả email trong listSub có hợp lệ không\n  isAllEmailsValid() {\n    return this.shareList.every(item => !this.isMailInvalid(item.email));\n  }\n  deleteItem(i) {\n    this.messageCommonService.confirm(this.tranService.translate(\"datapool.button.deleteSub\"), this.tranService.translate(\"datapool.message.confirmDelete\"), {\n      ok: () => {\n        const a = this.shareList[i].data;\n        if (a) {\n          this.shareList[i].data = null;\n          this.shareList[i].percent = null;\n        }\n        this.shareList = this.shareList.filter((item, index) => index != i);\n      }\n    });\n  }\n  onNameBlur() {\n    let me = this;\n    let formattedValue = this.createGroupForm.get('groupName').value;\n    formattedValue = formattedValue.trim().replace(/\\s+/g, ' ');\n    this.createGroupForm.get('groupName').setValue(formattedValue);\n  }\n  onCodeBlur() {\n    let me = this;\n    let value = this.createGroupForm.get('groupCode').value;\n    this.groupSubWalletService.checkExistGroupCode({\n      groupCode: value\n    }, res => {\n      if (res == true) {\n        this.isExistGroupCode = true;\n      } else {\n        this.isExistGroupCode = false;\n      }\n    });\n  }\n  clearFileCallback() {\n    this.isShowErrorUpload = false;\n  }\n  uploadFile(objectFile) {\n    var _this = this;\n    let me = this;\n    if (objectFile.size >= 1048576) {\n      this.messageCommonService.error(\"Dung lượng file vượt quá dung lượng tối đa\");\n      return;\n    }\n    let dataParams = {\n      groupCode: me.createGroupForm.value.groupCode.trim(),\n      groupName: me.createGroupForm.value.groupName.trim(),\n      description: me.createGroupForm.value.description.trim()\n    };\n    me.messageCommonService.onload();\n    this.groupSubWalletService.uploadFile(objectFile, dataParams, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (response) {\n        const createdId = response.headers.get('CREATED-ID');\n        const dataError = [];\n        const errorMessageCode = {\n          '10': 'Tham số đầu vào không hợp lệ',\n          '400': response => dataError.push(response?.headers?.get('cause')),\n          '401': 'Kích thước file vượt quá giới hạn',\n          '402': 'File tải lên thừa cột',\n          '403': 'File tải lên thiếu cột',\n          '404': 'File tải lên trùng cột',\n          '405': 'Không thể lấy thông tin hàng từ file excel',\n          '501': response => dataError.push(response?.headers?.get('Content-Disposition')),\n          '430': 'Sai định dạng file mẫu',\n          '440': 'File vượt quá 3000 SĐT',\n          '450': 'Tổng số điện thoại trong nhóm và file đã vượt quá giới hạn 3000 SĐT. Vui lòng kiểm tra lại dữ liệu!'\n        };\n        if (createdId) {\n          me.messageCommonService.success('Import người được chia sẻ thành công');\n          me.isShowDialogAddFile = false;\n          _this.router.navigate(['/data-pool/group/edit', createdId]);\n        }\n        if (response?.headers?.get('cause') === '0') {} else {\n          me.isShowErrorUpload = true;\n          const errorMessage = errorMessageCode[response?.headers?.get('cause')] || 'Lỗi không xác định';\n          console.log(errorMessageCode);\n          if (typeof errorMessage === 'function') {\n            errorMessage(response);\n            if (!response?.body) {\n              const fileName = response?.headers?.get('Content-Disposition');\n              const workbook = new Excel.Workbook();\n              const buf = yield workbook.xlsx.writeBuffer();\n              const spliceFileName = fileName.substring(0, fileName.length - 5);\n              const exportFileName = ''.concat(spliceFileName, '_Danh sách lỗi_', moment().format('DDMMYYYYHHMMss'));\n              // download the processed file\n              yield saveAs(new Blob([buf]), `${exportFileName}.xlsx`);\n            } else {\n              const dateMoment = moment().format('DDMMYYYYHHmmss');\n              const name = (objectFile.name || objectFile.fileName).split('.');\n              me.exportFile(response?.body, `${name[0]}_Danh sách lỗi_${dateMoment}`, '');\n              me.messageCommonService.error(me.tranService.translate('datapool.text.downloadErrorMessage'), null, 10000);\n            }\n          } else {\n            me.messageCommonService.error(errorMessage);\n          }\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }(), null, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  downloadTemplate() {\n    this.groupSubWalletService.downloadTemplate();\n  }\n  changeFile(event) {\n    let file = event.target.files[0];\n    this.fileObject = file;\n    if (this.fileObject == null) {\n      this.textDescription = this.tranService.translate(\"global.button.uploadFile\");\n      this.checkValid();\n      return;\n    }\n    let filename = file.name;\n    let filesize = Math.round(file.size / 1024);\n    let suffix = \"KB\";\n    if (filesize / 1024 > 2) {\n      filesize = Math.round(filesize / 1024);\n      suffix = \"MB\";\n    }\n    this.textDescription = `${filename} ${this.utilService.convertNumberToString(filesize)}(${suffix})`;\n    this.checkValid();\n  }\n  resetFile() {\n    this.formObject.file = null;\n    this.textDescription = this.tranService.translate(\"global.button.uploadFile\");\n    this.fileObject = null;\n    this.checkValid();\n  }\n  checkValid() {\n    this.invalid = null;\n    if (this.fileObject) {\n      if (this.options.type) {\n        let extension = this.fileObject.name.substring(this.fileObject.name.lastIndexOf(\".\") + 1, this.fileObject.name.length);\n        if (!this.options.type.includes(extension)) {\n          this.invalid = \"invalidtype\";\n        }\n      }\n      if (this.options.maxSize && this.invalid == null) {\n        let comparesize = this.options.maxSize;\n        if (this.options.unit == \"KB\") {\n          comparesize = comparesize * 1024;\n        } else if (this.options.unit == \"MB\") {\n          comparesize = comparesize * 1024 * 1024;\n        } else if (this.options.unit == \"GB\") {\n          comparesize = comparesize * 1024 * 1024 * 1024;\n        }\n        if (this.fileObject.size > comparesize) {\n          this.invalid = \"maxsize\";\n        }\n      }\n    } else {\n      if (this.options.required) {\n        this.invalid = \"required\";\n      }\n    }\n  }\n  reset() {\n    this.formObject.file = null;\n    this.fileObject = null;\n    this.invalid = null;\n    this.options.disabled = false;\n  }\n  upload() {\n    this.options.actionUpload(this.fileObject);\n  }\n  static {\n    this.ɵfac = function GroupSubWalletCreateComponent_Factory(t) {\n      return new (t || GroupSubWalletCreateComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GroupSubWalletService), i0.ɵɵdirectiveInject(TrafficWalletService), i0.ɵɵdirectiveInject(ShareManagementService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupSubWalletCreateComponent,\n      selectors: [[\"app-create-group-sub\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 81,\n      vars: 82,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"flex\", \"gap-3\", \"responsive-container\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"disabled\", \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-success\", 3, \"disabled\", \"label\", \"click\"], [\"action\", \"\", 1, \"responsive-form\", 3, \"formGroup\", \"submit\", \"keydown.enter\"], [1, \"mt-3\"], [1, \"gap-4\", \"mt-3\", \"px-2\", \"mb-0\"], [1, \"flex\", \"flex-column\", \"gap-2\", \"flex-1\"], [\"htmlFor\", \"groupCode\", 1, \"my-auto\", 2, \"min-width\", \"110px\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"groupKey\", \"formControlName\", \"groupCode\", \"type\", \"text\", 3, \"placeholder\", \"blur\"], [1, \"flex\", \"flex-row\", \"gap-4\", \"px-2\", \"py-0\", \"m-0\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"gap-2\", \"flex-1\", \"mt-3\"], [\"htmlFor\", \"groupName\", 1, \"my-auto\", 2, \"min-width\", \"110px\"], [\"pInputText\", \"\", \"id\", \"groupName\", \"formControlName\", \"groupName\", \"type\", \"text\", 1, \"w-full\", 3, \"placeholder\", \"blur\"], [1, \"w-full\", \"mt-3\", \"px-2\"], [\"htmlFor\", \"description\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"placeholder\"], [1, \"w-full\", \"field\", \"grid\", \"px-2\", \"m-0\", \"py-0\", \"mb-3\"], [4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"col-12\", \"md:col-12\", \"py-0\", \"gap-3\", \"mt-4\"], [\"type\", \"button\", \"styleClass\", \"p-button-outlined p-button-secondary\", \"routerLink\", \"/data-pool/group/listGroupSub\", 3, \"label\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [1, \"w-full\", 3, \"contentStyle\", \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"mt-5\", \"flex\", \"flex-row\", \"gap-3\", \"justify-content-between\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"col-12\"], [1, \"col-5\", 2, \"max-width\", \"calc(100% - 1px) !important\"], [\"paramKey\", \"phoneReceipt\", \"keyReturn\", \"phoneReceipt\", \"displayPattern\", \"${phoneReceipt}\", 3, \"value\", \"isAutoComplete\", \"isMultiChoice\", \"lazyLoad\", \"placeholder\", \"loadData\", \"valueChange\", \"onchange\", \"onSelectItem\"], [1, \"mb-5\", \"flex\", \"flex-row\", \"gap-3\", \"justify-content-between\", \"text-red-500\", \"px-1\"], [3, \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-button-secondary\", 3, \"label\", \"click\"], [1, \"w-full\", \"responsive-dialog-2\", 3, \"contentStyle\", \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\", \"onHide\"], [1, \"w-full\", \"field\", \"grid\"], [1, \"col-10\", \"flex\", \"flex-column\", \"justify-content-start\"], [1, \"w-full\", \"h-auto\", \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\"], [1, \"relative\", \"mr-2\"], [1, \"h-full\", \"w-full\", \"absolute\", \"top-0\", \"left-0\", \"z-1\", \"opacity-0\"], [\"type\", \"file\", 1, \"h-full\", \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"change\"], [1, \"w-full\", \"border-1\", \"border-black-alpha-40\", \"border-round\", \"border-dotted\", \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", 2, \"box-sizing\", \"border-box\", \"min-height\", \"42px\"], [1, \"max-w-full\", \"overflow-hidden\", \"text-overflow-ellipsis\", \"p-2\", \"pl-4\", \"pr-4\", \"white-space\", 2, \"box-sizing\", \"border-box\"], [\"class\", \"cursor-pointer button-reset-file\", 3, \"click\", 4, \"ngIf\"], [1, \"col-2\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"icon\", \"pi pi-download\", \"styleClass\", \"p-button-outlined p-button-secondary\", 3, \"pTooltip\", \"click\"], [1, \"flex\", \"justify-content-center\", \"gap-3\"], [\"pButton\", \"\", \"type\", \"button\", 3, \"disabled\", \"pTooltip\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-button-secondary\", 3, \"click\"], [\"type\", \"text\", \"pInputText\", \"\", 3, \"value\", \"readonly\", \"input\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"p-button-outlined\", 3, \"click\"], [1, \"pi\", \"pi-trash\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"100px\", \"min-height\", \"120px\", \"text-align\", \"center\"], [1, \"box-item-empty\"], [1, \"pi\", \"pi-inbox\", 2, \"font-size\", \"x-large\"], [1, \"cursor-pointer\", \"button-reset-file\", 3, \"click\"], [1, \"pi\", \"pi-times\"]],\n      template: function GroupSubWalletCreateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_button_click_6_listener() {\n            return ctx.addSubToGroup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_button_click_7_listener() {\n            return ctx.addSubFile();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"form\", 7);\n          i0.ɵɵlistener(\"submit\", function GroupSubWalletCreateComponent_Template_form_submit_8_listener() {\n            return ctx.submitForm();\n          })(\"keydown.enter\", function GroupSubWalletCreateComponent_Template_form_keydown_enter_8_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"p-card\")(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementStart(15, \"span\", 12);\n          i0.ɵɵtext(16, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"input\", 13);\n          i0.ɵɵlistener(\"blur\", function GroupSubWalletCreateComponent_Template_input_blur_17_listener() {\n            return ctx.onCodeBlur();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 14);\n          i0.ɵɵtemplate(19, GroupSubWalletCreateComponent_div_19_Template, 2, 1, \"div\", 15);\n          i0.ɵɵtemplate(20, GroupSubWalletCreateComponent_div_20_Template, 2, 1, \"div\", 15);\n          i0.ɵɵtemplate(21, GroupSubWalletCreateComponent_div_21_Template, 2, 1, \"div\", 15);\n          i0.ɵɵtemplate(22, GroupSubWalletCreateComponent_div_22_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 16)(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementStart(26, \"span\", 12);\n          i0.ɵɵtext(27, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"input\", 18);\n          i0.ɵɵlistener(\"blur\", function GroupSubWalletCreateComponent_Template_input_blur_28_listener() {\n            return ctx.onNameBlur();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 14);\n          i0.ɵɵtemplate(30, GroupSubWalletCreateComponent_div_30_Template, 2, 1, \"div\", 15);\n          i0.ɵɵtemplate(31, GroupSubWalletCreateComponent_div_31_Template, 2, 1, \"div\", 15);\n          i0.ɵɵtemplate(32, GroupSubWalletCreateComponent_div_32_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 19)(34, \"div\", 10)(35, \"label\", 20);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"textarea\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 22);\n          i0.ɵɵtemplate(39, GroupSubWalletCreateComponent_div_39_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 24);\n          i0.ɵɵelement(41, \"p-button\", 25)(42, \"p-button\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 8)(44, \"p-dialog\", 27);\n          i0.ɵɵlistener(\"visibleChange\", function GroupSubWalletCreateComponent_Template_p_dialog_visibleChange_44_listener($event) {\n            return ctx.isShowDialogAddSub = $event;\n          });\n          i0.ɵɵelementStart(45, \"div\", 28)(46, \"div\", 29)(47, \"div\", 30)(48, \"vnpt-select\", 31);\n          i0.ɵɵlistener(\"valueChange\", function GroupSubWalletCreateComponent_Template_vnpt_select_valueChange_48_listener($event) {\n            return ctx.phoneReceiptSelect = $event;\n          })(\"onchange\", function GroupSubWalletCreateComponent_Template_vnpt_select_onchange_48_listener() {\n            return ctx.checkValidAdd();\n          })(\"onSelectItem\", function GroupSubWalletCreateComponent_Template_vnpt_select_onSelectItem_48_listener() {\n            return ctx.addPhone(ctx.phoneReceiptSelect);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_button_click_49_listener() {\n            return ctx.addPhoneNotInSelect(ctx.phoneReceiptSelect);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 32);\n          i0.ɵɵtemplate(51, GroupSubWalletCreateComponent_div_51_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p-table\", 33);\n          i0.ɵɵtemplate(53, GroupSubWalletCreateComponent_ng_template_53_Template, 8, 3, \"ng-template\", 34);\n          i0.ɵɵtemplate(54, GroupSubWalletCreateComponent_ng_template_54_Template, 14, 9, \"ng-template\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(55, GroupSubWalletCreateComponent_div_55_Template, 6, 1, \"div\", 23);\n          i0.ɵɵelementStart(56, \"div\", 24)(57, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_button_click_57_listener() {\n            return ctx.cancelAddSub();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"p-button\", 26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"p-dialog\", 37);\n          i0.ɵɵlistener(\"visibleChange\", function GroupSubWalletCreateComponent_Template_p_dialog_visibleChange_59_listener($event) {\n            return ctx.isShowDialogAddFile = $event;\n          })(\"onHide\", function GroupSubWalletCreateComponent_Template_p_dialog_onHide_59_listener() {\n            return ctx.reset();\n          });\n          i0.ɵɵelementStart(60, \"div\", 38)(61, \"div\", 39)(62, \"div\", 40)(63, \"div\", 41)(64, \"div\", 42)(65, \"input\", 43);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupSubWalletCreateComponent_Template_input_ngModelChange_65_listener($event) {\n            return ctx.formObject.file = $event;\n          })(\"change\", function GroupSubWalletCreateComponent_Template_input_change_65_listener($event) {\n            return ctx.changeFile($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 44)(67, \"div\", 45);\n          i0.ɵɵtext(68);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(69, GroupSubWalletCreateComponent_div_69_Template, 2, 0, \"div\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\");\n          i0.ɵɵtemplate(71, GroupSubWalletCreateComponent_small_71_Template, 2, 1, \"small\", 15);\n          i0.ɵɵtemplate(72, GroupSubWalletCreateComponent_small_72_Template, 2, 2, \"small\", 15);\n          i0.ɵɵtemplate(73, GroupSubWalletCreateComponent_small_73_Template, 2, 1, \"small\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 47)(75, \"p-button\", 48);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_p_button_click_75_listener() {\n            return ctx.downloadTemplate();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"div\", 49)(77, \"button\", 50);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_button_click_77_listener() {\n            return ctx.upload();\n          });\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function GroupSubWalletCreateComponent_Template_button_click_79_listener() {\n            return ctx.isShowDialogAddFile = false;\n          });\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.breadCrumb.group\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.createGroupForm.invalid || ctx.isExistGroupCode)(\"label\", ctx.tranService.translate(\"global.button.addSubToGroup\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.createGroupForm.invalid || ctx.isExistGroupCode)(\"label\", ctx.tranService.translate(\"groupSim.label.addByFile\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.createGroupForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.label.groupKey\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"placeholder\", ctx.tranService.translate(\"groupSim.placeHolder.groupKey\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (ctx.createGroupForm.controls[\"groupCode\"] == null ? null : ctx.createGroupForm.controls[\"groupCode\"].dirty) && ctx.createGroupForm.controls[\"groupCode\"].hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createGroupForm.controls.groupCode.errors == null ? null : ctx.createGroupForm.controls.groupCode.errors[\"maxlength\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createGroupForm.controls.groupCode.errors == null ? null : ctx.createGroupForm.controls.groupCode.errors[\"pattern\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExistGroupCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.label.groupName\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"placeholder\", ctx.tranService.translate(\"groupSim.placeHolder.groupName\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (ctx.createGroupForm.controls[\"groupName\"] == null ? null : ctx.createGroupForm.controls[\"groupName\"].dirty) && (ctx.createGroupForm.controls[\"groupName\"].hasError(\"required\") || (ctx.createGroupForm.controls.groupName.errors == null ? null : ctx.createGroupForm.controls.groupName.errors[\"whitespace\"])));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createGroupForm.controls.groupName.errors == null ? null : ctx.createGroupForm.controls.groupName.errors[\"maxlength\"]);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createGroupForm.controls.groupName.errors == null ? null : ctx.createGroupForm.controls.groupName.errors[\"pattern\"]);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.description\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"autoResize\", false)(\"placeholder\", ctx.tranService.translate(\"sim.text.inputDescription\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.createGroupForm.get(\"description\").invalid && ctx.createGroupForm.get(\"description\").dirty);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"groupSim.label.buttonCancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"groupSim.label.buttonSave\"))(\"disabled\", ctx.createGroupForm.invalid || ctx.isExistGroupCode);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(75, _c4));\n          i0.ɵɵproperty(\"contentStyle\", i0.ɵɵpureFunction0(76, _c5))(\"header\", ctx.tranService.translate(\"global.button.addSubToGroup\"))(\"visible\", ctx.isShowDialogAddSub)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.phoneReceiptSelect)(\"isAutoComplete\", true)(\"isMultiChoice\", false)(\"lazyLoad\", true)(\"placeholder\", ctx.tranService.translate(\"datapool.label.receiverPhone\"))(\"loadData\", ctx.getListShareInfoCbb.bind(ctx));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isClickCreate || !ctx.isValidPhone)(\"label\", ctx.tranService.translate(\"groupSim.label.buttonAddSim\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isValidPhone);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.shareList)(\"tableStyle\", i0.ɵɵpureFunction0(77, _c6));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.shareList.length == 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"groupSim.label.buttonCancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"groupSim.label.buttonSave\"))(\"disabled\", ctx.createGroupForm.invalid || ctx.shareList.length == 0 || !ctx.isAllEmailsValid());\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(78, _c7));\n          i0.ɵɵproperty(\"contentStyle\", i0.ɵɵpureFunction0(79, _c5))(\"header\", ctx.tranService.translate(\"groupSim.label.addPhoneByFile\"))(\"visible\", ctx.isShowDialogAddFile)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction1(80, _c8, ctx.options.isShowButtonUpload ? \"80%\" : \"100%\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.options.disabled ? \"\" : \"cursor-pointer\");\n          i0.ɵɵproperty(\"ngModel\", ctx.formObject.file)(\"disabled\", ctx.options.disabled);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.options.disabled ? \"bg-black-alpha-10\" : \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.fileObject ? ctx.textDescription : ctx.tranService.translate(\"global.button.uploadFile\"), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.fileObject != null && !ctx.options.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.invalid == \"required\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.invalid == \"maxsize\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.invalid == \"invalidtype\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"pTooltip\", ctx.tranService.translate(\"global.button.downloadTemp\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.invalid || ctx.fileObject == null || ctx.options.disabled)(\"pTooltip\", ctx.tranService.translate(\"global.button.upFile\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.save\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.cancel\"));\n        }\n      },\n      dependencies: [i3.RouterLink, i4.ButtonDirective, i4.Button, i5.PrimeTemplate, i6.VnptCombobox, i7.Dialog, i8.Breadcrumb, i9.Tooltip, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i10.InputText, i11.NgIf, i1.FormGroupDirective, i1.FormControlName, i12.Card, i13.InputTextarea, i14.Table],\n      styles: [\".button-reset-file[_ngcontent-%COMP%]{\\n        width: fit-content;\\n        height: fit-content;\\n        top: 50%;\\n        position: absolute;\\n        right: 12px;\\n        z-index: 2;\\n        transform: translateY(-50%);\\n        line-height: 14px;\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "FormControl", "FormGroup", "Validators", "ComboLazyControl", "TrafficWalletService", "ShareManagementService", "noneWhitespaceValidator", "Excel", "moment", "saveAs", "FileSaver", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "tranService", "translate", "ctx_r1", "ctx_r2", "ctx_r3", "ctx_r4", "ctx_r5", "ctx_r6", "ɵɵtextInterpolate", "ctx_r16", "ɵɵpureFunction0", "_c0", "ɵɵtemplate", "GroupSubWalletCreateComponent_div_39_div_1_Template", "ɵɵproperty", "ctx_r7", "createGroupForm", "get", "errors", "maxlength", "ctx_r8", "ɵɵelement", "ctx_r9", "ctx_r19", "_c1", "ctx_r20", "_c2", "ctx_r21", "_c3", "ctx_r22", "ɵɵlistener", "GroupSubWalletCreateComponent_ng_template_54_Template_input_input_4_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r24", "index_r18", "rowIndex", "ctx_r23", "ɵɵnextContext", "ɵɵresetView", "changeDataName", "GroupSubWalletCreateComponent_ng_template_54_div_5_Template", "GroupSubWalletCreateComponent_ng_template_54_div_6_Template", "GroupSubWalletCreateComponent_ng_template_54_Template_input_input_8_listener", "ctx_r25", "changeDataMail", "GroupSubWalletCreateComponent_ng_template_54_div_9_Template", "GroupSubWalletCreateComponent_ng_template_54_div_10_Template", "GroupSubWalletCreateComponent_ng_template_54_Template_button_click_12_listener", "ctx_r26", "deleteItem", "list_r17", "phoneReceipt", "name", "ctx_r10", "userInfo", "type", "USER_TYPE", "CUSTOMER", "created<PERSON>y", "id", "length", "utilService", "checkValidCharacterVietnamese", "email", "isMailInvalid", "ctx_r11", "GroupSubWalletCreateComponent_div_69_Template_div_click_0_listener", "_r28", "ctx_r27", "resetFile", "ctx_r13", "ctx_r14", "ctx_r15", "options", "messageErrorType", "GroupSubWalletCreateComponent", "constructor", "injector", "formBuilder", "groupSubWalletService", "walletService", "shareService", "groupCode", "required", "max<PERSON><PERSON><PERSON>", "pattern", "groupName", "description", "boxSimAddController", "displayAddSim", "phoneReceiptSelect", "isClickCreate", "phoneList", "isValidPhone", "isShowDialogAddSub", "isShowDialogAddFile", "isShowErrorUpload", "listGroup", "exportFile", "bytes", "fileName", "fileType", "file", "Blob", "ngOnInit", "me", "isExistGroupCode", "formObject", "formInstance", "group", "textDescription", "sessionService", "maxSize", "unit", "isShowButtonUpload", "actionUpload", "uploadFile", "bind", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "SHARE_GROUP", "CREATE", "items", "label", "routerLink", "home", "icon", "shareList", "getAllGroup", "window", "location", "hash", "messageCommonService", "onload", "response", "offload", "getListShareInfoCbb", "params", "callback", "content", "submitForm", "dataParams", "value", "trim", "listSub", "create", "success", "router", "navigate", "error", "errorCode", "checkValidAdd", "find", "dta", "toString", "undefined", "regex", "inputValue", "test", "cleanArray", "arr", "filter", "item", "addSubToGroup", "addSubFile", "cancelAddSub", "addPhone", "data", "phone", "String", "replace", "exists", "some", "LIMIT_ADD", "LIMIT", "console", "log", "idGroup", "addPhoneTable", "addPhoneNotInSelect", "phoneValid", "checkPhoneBelongGroup", "phoneNumber", "call", "unshift", "setTimeout", "pushData", "event", "i", "shareValue", "target", "isAllEmailsValid", "every", "confirm", "ok", "a", "percent", "index", "onNameBlur", "formattedValue", "setValue", "onCodeBlur", "checkExistGroupCode", "res", "clearFileCallback", "objectFile", "_this", "size", "_ref", "_asyncToGenerator", "createdId", "headers", "dataError", "errorMessageCode", "push", "errorMessage", "body", "workbook", "Workbook", "buf", "xlsx", "writeBuffer", "spliceFileName", "substring", "exportFileName", "concat", "format", "dateMoment", "split", "_x", "apply", "arguments", "downloadTemplate", "changeFile", "files", "fileObject", "checkValid", "filename", "filesize", "Math", "round", "suffix", "convertNumberToString", "invalid", "extension", "lastIndexOf", "includes", "comparesize", "reset", "upload", "ɵɵdirectiveInject", "Injector", "i1", "FormBuilder", "i2", "GroupSubWalletService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "GroupSubWalletCreateComponent_Template", "rf", "ctx", "GroupSubWalletCreateComponent_Template_button_click_6_listener", "GroupSubWalletCreateComponent_Template_button_click_7_listener", "GroupSubWalletCreateComponent_Template_form_submit_8_listener", "GroupSubWalletCreateComponent_Template_form_keydown_enter_8_listener", "preventDefault", "GroupSubWalletCreateComponent_Template_input_blur_17_listener", "GroupSubWalletCreateComponent_div_19_Template", "GroupSubWalletCreateComponent_div_20_Template", "GroupSubWalletCreateComponent_div_21_Template", "GroupSubWalletCreateComponent_div_22_Template", "GroupSubWalletCreateComponent_Template_input_blur_28_listener", "GroupSubWalletCreateComponent_div_30_Template", "GroupSubWalletCreateComponent_div_31_Template", "GroupSubWalletCreateComponent_div_32_Template", "GroupSubWalletCreateComponent_div_39_Template", "GroupSubWalletCreateComponent_Template_p_dialog_visibleChange_44_listener", "GroupSubWalletCreateComponent_Template_vnpt_select_valueChange_48_listener", "GroupSubWalletCreateComponent_Template_vnpt_select_onchange_48_listener", "GroupSubWalletCreateComponent_Template_vnpt_select_onSelectItem_48_listener", "GroupSubWalletCreateComponent_Template_button_click_49_listener", "GroupSubWalletCreateComponent_div_51_Template", "GroupSubWalletCreateComponent_ng_template_53_Template", "GroupSubWalletCreateComponent_ng_template_54_Template", "GroupSubWalletCreateComponent_div_55_Template", "GroupSubWalletCreateComponent_Template_button_click_57_listener", "GroupSubWalletCreateComponent_Template_p_dialog_visibleChange_59_listener", "GroupSubWalletCreateComponent_Template_p_dialog_onHide_59_listener", "GroupSubWalletCreateComponent_Template_input_ngModelChange_65_listener", "GroupSubWalletCreateComponent_Template_input_change_65_listener", "GroupSubWalletCreateComponent_div_69_Template", "GroupSubWalletCreateComponent_small_71_Template", "GroupSubWalletCreateComponent_small_72_Template", "GroupSubWalletCreateComponent_small_73_Template", "GroupSubWalletCreateComponent_Template_p_button_click_75_listener", "GroupSubWalletCreateComponent_Template_button_click_77_listener", "GroupSubWalletCreateComponent_Template_button_click_79_listener", "controls", "dirty", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵstyleMap", "_c4", "_c5", "_c6", "_c7", "ɵɵpureFunction1", "_c8", "ɵɵclassMap"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\group-sub\\create\\group-sub-wallet.create.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\group-sub\\create\\group-sub-wallet.create.component.html"], "sourcesContent": ["import {Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport {ComponentBase} from \"../../../../component.base\";\r\nimport {CONSTANTS, isVinaphoneNumber} from \"../../../../service/comon/constants\";\r\nimport {GroupSubWalletService} from \"../../../../service/group-sub-wallet/GroupSubWalletService\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {FormControl, FormGroup, Validators, FormBuilder} from \"@angular/forms\";\r\nimport {ComboLazyControl} from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {PhoneInfo, PhoneInGroup, ShareDetail} from \"../../data-pool.type-data\";\r\nimport {TrafficWalletService} from \"../../../../service/datapool/TrafficWalletService\";\r\nimport {ShareManagementService} from \"../../../../service/datapool/ShareManagementService\";\r\nimport {noneWhitespaceValidator} from \"../../../common-module/validatorCustoms\";\r\nimport {OptionInputFile} from \"../../../common-module/input-file/input.file.component\";\r\nimport * as Excel from \"exceljs\";\r\nimport * as moment from \"moment/moment\";\r\nimport {saveAs} from \"file-saver\";\r\nimport * as FileSaver from \"file-saver\";\r\n\r\n@Component({\r\n    selector: 'app-create-group-sub',\r\n    templateUrl: './group-sub-wallet.create.component.html',\r\n})\r\n\r\nexport class GroupSubWalletCreateComponent extends ComponentBase implements OnInit {\r\n    createGroupForm = new FormGroup({\r\n        groupCode: new FormControl(\"\", [Validators.required,Validators.maxLength(16), Validators.pattern('^[A-Za-z0-9_-]+$')]),\r\n        groupName: new FormControl(\"\",[Validators.required, Validators.maxLength(255), Validators.pattern('^[a-zA-Z0-9\\\\- _\\\\u00C0-\\\\u024F\\\\u1E00-\\\\u1EFF]+$'), noneWhitespaceValidator()]),\r\n        description: new FormControl(\"\", [Validators.maxLength(255)]),\r\n    });\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    boxSimAddController: ComboLazyControl = new ComboLazyControl();\r\n    displayAddSim: boolean = false;\r\n    phoneReceiptSelect: string = \"\";\r\n    isClickCreate: boolean = true;\r\n    phoneList : PhoneInGroup[] = [];\r\n    isValidPhone: boolean = true;\r\n    isShowDialogAddSub: boolean = false;\r\n    isShowDialogAddFile: boolean = false;\r\n    isShowErrorUpload: boolean = false;\r\n    fileObject: any;\r\n    messageErrorUpload: string| null;\r\n    shareList: ShareDetail[];\r\n    listGroup: any = [];\r\n    isExistGroupCode: boolean;\r\n    userInfo: any;\r\n    formInstance: any;\r\n    formObject: {\r\n        file: any\r\n    }\r\n    textDescription: string | null;\r\n    options!: OptionInputFile;\r\n    invalid: \"required\" | \"maxsize\" | \"invalidtype\" | null;\r\n    constructor(\r\n        injector: Injector,\r\n        private formBuilder: FormBuilder,\r\n        private groupSubWalletService: GroupSubWalletService,\r\n        @Inject(TrafficWalletService) private walletService: TrafficWalletService,\r\n        @Inject(ShareManagementService) private shareService: ShareManagementService,\r\n    ) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit() {\r\n        let me = this;\r\n        this.isExistGroupCode = false;\r\n        this.formObject = {\r\n            file: null\r\n        }\r\n        this.formInstance = this.formBuilder.group(this.formObject);\r\n        this.textDescription = this.tranService.translate(\"global.button.uploadFile\");\r\n        me.userInfo = this.sessionService.userInfo;\r\n        this.options = {\r\n            type: ['xls','xlsx'],\r\n            messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\r\n            maxSize: 10,\r\n            unit: \"MB\",\r\n            required: true,\r\n            isShowButtonUpload: true,\r\n            actionUpload: this.uploadFile.bind(this),\r\n            disabled: false\r\n        }\r\n        if (me.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])) {\r\n            this.items = [{ label: this.tranService.translate(\"global.menu.trafficManagement\") }, { label: this.tranService.translate(\"global.menu.listGroupSub\"), routerLink: \"/data-pool/group/listGroupSub\" }, { label: this.tranService.translate(\"datapool.label.createGroupShare\") }];\r\n            this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n            this.shareList = [];\r\n            me.getAllGroup();\r\n        } else {\r\n            window.location.hash = \"/access\";\r\n        }\r\n    }\r\n\r\n    getAllGroup () {\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        me.groupSubWalletService.getAllGroup((response) => {\r\n            me.listGroup = response;\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    getListShareInfoCbb(params, callback) {\r\n        return this.shareService.getListShareInfoCbb(params, (response)=>{\r\n            this.phoneList = response.content;\r\n            callback(response)\r\n        });\r\n    }\r\n\r\n    submitForm () {\r\n        let me = this;\r\n        let dataParams = {\r\n            groupCode: me.createGroupForm.value.groupCode.trim(),\r\n            groupName: me.createGroupForm.value.groupName.trim(),\r\n            description: me.createGroupForm.value.description.trim(),\r\n            listSub: me.shareList\r\n        };\r\n        this.messageCommonService.onload()\r\n        this.groupSubWalletService.create({},dataParams,{},(response)=>{\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"))\r\n            this.router.navigate([`/data-pool/group/edit/${response.id}`]);\r\n        }, (error) => {\r\n            if (error.error.error.errorCode === \"error.duplicate.value\") {\r\n                me.messageCommonService.error(this.tranService.translate(\"datapool.error.existedGroupCode\"));\r\n            }\r\n        },\r\n            () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    checkValidAdd(){\r\n        this.isClickCreate = true\r\n        if(!this.phoneList.find(dta => dta.phoneReceipt.toString() === this.phoneReceiptSelect)){\r\n            this.isClickCreate = false\r\n        }else{\r\n            this.isClickCreate = true\r\n        }\r\n        if(this.phoneReceiptSelect == \"\"|| this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined){\r\n            this.isClickCreate = true\r\n        }\r\n\r\n        const regex = /^0[0-9]{9,10}$/;\r\n        const inputValue = this.phoneReceiptSelect;\r\n        this.isValidPhone = regex.test(inputValue);\r\n    }\r\n\r\n    cleanArray(arr: any[]): any[] {\r\n        return arr.filter(item => item !== null && item !== undefined && item !== \"\");\r\n    }\r\n\r\n    addSubToGroup() {\r\n        let me = this;\r\n        me.isShowDialogAddSub = true;\r\n    }\r\n\r\n    addSubFile() {\r\n        let me = this;\r\n        me.isShowDialogAddFile = true;\r\n    }\r\n\r\n    cancelAddSub() {\r\n        let me = this;\r\n        me.isShowDialogAddSub = false;\r\n        me.shareList = [];\r\n        me.phoneReceiptSelect = \"\";\r\n    }\r\n\r\n    addPhone(data){\r\n        let me = this;\r\n        if(data === null || data === undefined){\r\n            return;\r\n        }\r\n        me.isClickCreate = false\r\n        const value = me.phoneList.find(dta => dta.phoneReceipt === data);\r\n        const phone = String(data)?.replace(/^0/,\"84\");\r\n        //check trước khi vào\r\n        let exists = this.shareList.some(item => item.phoneReceipt.toString() === phone);\r\n        if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\r\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\r\n            return;\r\n        } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {\r\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\r\n            return;\r\n        } else if (exists) {\r\n            console.log(\"vào check trc\")\r\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\r\n            return;\r\n        }\r\n\r\n        if (value?.idGroup) {\r\n            me.messageCommonService.error(me.tranService.translate('datapool.message.duplicateSub', {data: data, groupName: value?.groupName}));\r\n        } else {\r\n            me.addPhoneTable(value, data)\r\n            /**\r\n             * UAT 2.4 issue 31\r\n             * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:\r\n             * - Chia sẻ thường\r\n             * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động\r\n             * - Thêm thuê bao vào nhóm\r\n             * - icon chia sẻ ở Danh sách ví\r\n             */\r\n            // me.messageCommonService.onload()\r\n            // me.walletService.checkParticipant({phoneNumber : phone},\r\n            //     (response)=>{\r\n            //         if(response.error_code === \"0\" && (response.result === \"02\" || response.result === \"11\")){\r\n            //             me.addPhoneTable(value, data)\r\n            //         }else if(response.error_code === \"0\" && response.result === \"0\"){\r\n            //             if(isVinaphoneNumber(data)){\r\n            //                 me.addPhoneTable(value, data)\r\n            //             }else{\r\n            //                 me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\r\n            //             }\r\n            //         }else{\r\n            //             me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\r\n            //         }\r\n            //     },\r\n            //     null,()=>{\r\n            //         me.messageCommonService.offload();\r\n            //     })\r\n        }\r\n    }\r\n\r\n    addPhoneNotInSelect(phone) {\r\n        let me = this;\r\n\r\n        if(!phone){\r\n            return;\r\n        }\r\n\r\n        const value = me.phoneList.find(dta => dta.phoneReceipt === phone);\r\n        const phoneValid = String(phone)?.replace(/^0/,\"84\");\r\n        //check trước khi vào\r\n        let exists = this.shareList.some(item => item.phoneReceipt.toString() === phoneValid);\r\n        if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\r\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\r\n            return;\r\n        } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {\r\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\r\n            return;\r\n        } else if (exists) {\r\n            console.log(\"vào check trc\")\r\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\r\n            return;\r\n        }\r\n        me.groupSubWalletService.checkPhoneBelongGroup({phoneNumber: phone}, (response)=>{\r\n\r\n\r\n            if (response) {\r\n                me.messageCommonService.error(me.tranService.translate('Thuê bao này đã thuộc nhóm khác'));\r\n            } else {\r\n                if (value?.idGroup) {\r\n                    me.messageCommonService.error(`Thuê bao đang thuộc nhóm \"${value.groupName}\"`);\r\n                } else {\r\n                    me.addPhoneTable(value, phone);\r\n                }\r\n                /**\r\n                 * bỏ check số vina\r\n                 */\r\n                // me.messageCommonService.onload()\r\n                // me.walletService.checkParticipant({phoneNumber : phoneValid},\r\n                //     (response)=>{\r\n                //         if (value?.idGroup) {\r\n                //             me.messageCommonService.error(`Thuê bao đang thuộc nhóm \"${value.groupName}\"`);\r\n                //         } else if(response.error_code === \"0\" && (response.result === \"02\" || response.result === \"11\") && !value?.idGroup){\r\n                //             me.addPhoneTable(value, phone);\r\n                //             me.phoneReceiptSelect = \"\";\r\n                //         } else if(response.error_code === \"0\" && response.result === \"0\" && !value?.idGroup){\r\n                //             if(isVinaphoneNumber(phone)){\r\n                //                 me.addPhoneTable(value, phone);\r\n                //                 me.phoneReceiptSelect = \"\";\r\n                //             }else{\r\n                //                 me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\r\n                //             }\r\n                //         }else{\r\n                //             me.messageCommonService.error(me.tranService.translate(\"datapool.message.notValidPhone\"))\r\n                //         }\r\n                //     },\r\n                //     null,()=>{\r\n                //         me.messageCommonService.offload();\r\n                //     })\r\n                me.phoneReceiptSelect = \"\";\r\n                this.getListShareInfoCbb.call(this)\r\n            }\r\n        },null ,null );\r\n    }\r\n\r\n    addPhoneTable(value, data){\r\n        console.log(value)\r\n        let me = this;\r\n        let exists = this.shareList.some(item => item.phoneReceipt === data);\r\n        if(value){\r\n            if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\r\n            } else if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT) {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\r\n            } else if (exists) {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\r\n            } else {\r\n                me.shareList.unshift(value)\r\n                setTimeout(function(){\r\n                    me.phoneReceiptSelect = \"\";\r\n                },100);\r\n            }\r\n        }else{\r\n            let pushData: ShareDetail = {\r\n                id: value?.id || \"\",\r\n                phoneReceipt: data,\r\n                name:value?.name || \"\",\r\n                email:value?.email || \"\",\r\n            }\r\n            if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubAdd\"));\r\n            } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.message.maximumSubDisplay\"));\r\n            } else if (exists) {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.message.dublicateShareInfo\"));\r\n            } else {\r\n                me.shareList.unshift(pushData)\r\n            }\r\n        }\r\n        me.isClickCreate = true\r\n    }\r\n\r\n    changeDataName(event, i){\r\n        const shareValue = event.target.value\r\n        this.shareList[i].name = shareValue\r\n    }\r\n\r\n    changeDataMail(event, i){\r\n        const shareValue = event.target.value\r\n        this.shareList[i].email = shareValue\r\n        this.isAllEmailsValid();\r\n    }\r\n\r\n    isMailInvalid(email:string){\r\n        if (!email){\r\n            return false\r\n        }\r\n        // const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$/\r\n        const pattern: RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$/;\r\n        return !pattern.test(email);\r\n    }\r\n    // Hàm kiểm tra xem tất cả email trong listSub có hợp lệ không\r\n    isAllEmailsValid(): boolean {\r\n        return this.shareList.every(item => !this.isMailInvalid(item.email));\r\n    }\r\n\r\n    deleteItem(i){\r\n        this.messageCommonService.confirm(this.tranService.translate(\"datapool.button.deleteSub\"),\r\n            this.tranService.translate(\"datapool.message.confirmDelete\"),{\r\n                ok: ()=>{\r\n                    const a = this.shareList[i].data\r\n                    if(a){\r\n                        this.shareList[i].data = null\r\n                        this.shareList[i].percent = null\r\n                    }\r\n                    this.shareList = this.shareList.filter((item,index) => index != i);\r\n                }\r\n            })\r\n    }\r\n    onNameBlur() {\r\n        let me = this;\r\n        let formattedValue = this.createGroupForm.get('groupName').value;\r\n        formattedValue = formattedValue.trim().replace(/\\s+/g, ' ');\r\n        this.createGroupForm.get('groupName').setValue(formattedValue);\r\n    }\r\n    onCodeBlur() {\r\n        let me = this;\r\n        let value = this.createGroupForm.get('groupCode').value;\r\n        this.groupSubWalletService.checkExistGroupCode({groupCode: value}, (res) => {\r\n            if (res == true) {\r\n                this.isExistGroupCode = true;\r\n            } else {\r\n                this.isExistGroupCode = false\r\n            }\r\n        })\r\n    }\r\n\r\n    clearFileCallback(){\r\n        this.isShowErrorUpload = false;\r\n    }\r\n\r\n    uploadFile(objectFile: any) {\r\n        let me = this;\r\n        if(objectFile.size >= 1048576){\r\n            this.messageCommonService.error(\"Dung lượng file vượt quá dung lượng tối đa\")\r\n            return\r\n        }\r\n        let dataParams = {\r\n            groupCode: me.createGroupForm.value.groupCode.trim(),\r\n            groupName: me.createGroupForm.value.groupName.trim(),\r\n            description: me.createGroupForm.value.description.trim(),\r\n        };\r\n        me.messageCommonService.onload();\r\n        this.groupSubWalletService.uploadFile(objectFile, dataParams, async (response) => {\r\n            const createdId = response.headers.get('CREATED-ID');\r\n            const dataError = [];\r\n            const errorMessageCode = {\r\n                '10': 'Tham số đầu vào không hợp lệ',\r\n                '400': response => dataError.push(response?.headers?.get('cause')),\r\n                '401': 'Kích thước file vượt quá giới hạn',\r\n                '402': 'File tải lên thừa cột',\r\n                '403': 'File tải lên thiếu cột',\r\n                '404': 'File tải lên trùng cột',\r\n                '405': 'Không thể lấy thông tin hàng từ file excel',\r\n                '501': response => dataError.push(response?.headers?.get('Content-Disposition')),\r\n                '430': 'Sai định dạng file mẫu',\r\n                '440': 'File vượt quá 3000 SĐT',\r\n                '450': 'Tổng số điện thoại trong nhóm và file đã vượt quá giới hạn 3000 SĐT. Vui lòng kiểm tra lại dữ liệu!'\r\n            };\r\n\r\n            if(createdId){\r\n                me.messageCommonService.success('Import người được chia sẻ thành công');\r\n                me.isShowDialogAddFile = false;\r\n                this.router.navigate(['/data-pool/group/edit', createdId]);\r\n            }\r\n\r\n            if (response?.headers?.get('cause') === '0') {\r\n\r\n\r\n            } else {\r\n                me.isShowErrorUpload = true;\r\n                const errorMessage = errorMessageCode[response?.headers?.get('cause')] || 'Lỗi không xác định';\r\n                console.log(errorMessageCode)\r\n                if (typeof errorMessage === 'function') {\r\n                    errorMessage(response);\r\n                    if (!response?.body) {\r\n                        const fileName = response?.headers?.get('Content-Disposition');\r\n                        const workbook = new Excel.Workbook();\r\n                        const buf = await workbook.xlsx.writeBuffer();\r\n                        const spliceFileName = fileName.substring(0, fileName.length - 5);\r\n                        const exportFileName = ''.concat(spliceFileName, '_Danh sách lỗi_', moment().format('DDMMYYYYHHMMss'));\r\n                        // download the processed file\r\n                        await saveAs(new Blob([buf]), `${exportFileName}.xlsx`);\r\n                    } else {\r\n                        const dateMoment = moment().format('DDMMYYYYHHmmss');\r\n                        const name = (objectFile.name || objectFile.fileName).split('.');\r\n                        me.exportFile(response?.body, `${name[0]}_Danh sách lỗi_${dateMoment}`, '');\r\n                        me.messageCommonService.error(me.tranService.translate('datapool.text.downloadErrorMessage'), null, 10000)\r\n                    }\r\n                } else {\r\n                    me.messageCommonService.error(errorMessage);\r\n                }\r\n            }\r\n\r\n        },null,()=>{\r\n            this.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    exportFile = (bytes, fileName, fileType) => {\r\n\r\n        const file = new Blob([bytes], {\r\n            type: fileType || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        });\r\n        FileSaver.saveAs(file, fileName);\r\n    };\r\n\r\n\r\n    downloadTemplate(){\r\n        this.groupSubWalletService.downloadTemplate();\r\n    }\r\n\r\n    changeFile(event){\r\n        let file = event.target.files[0];\r\n        this.fileObject = file;\r\n        if(this.fileObject == null){\r\n            this.textDescription = this.tranService.translate(\"global.button.uploadFile\");\r\n            this.checkValid();\r\n            return;\r\n        }\r\n        let filename = file.name;\r\n        let filesize = Math.round(file.size/1024);\r\n        let suffix = \"KB\";\r\n        if(filesize/1024 > 2){\r\n            filesize = Math.round(filesize/1024);\r\n            suffix = \"MB\";\r\n        }\r\n        this.textDescription = `${filename} ${this.utilService.convertNumberToString(filesize)}(${suffix})`\r\n        this.checkValid();\r\n    }\r\n\r\n\r\n    resetFile(){\r\n        this.formObject.file = null;\r\n        this.textDescription = this.tranService.translate(\"global.button.uploadFile\");\r\n        this.fileObject = null;\r\n        this.checkValid();\r\n    }\r\n\r\n    checkValid(){\r\n        this.invalid = null;\r\n        if(this.fileObject){\r\n            if(this.options.type){\r\n                let extension = this.fileObject.name.substring(this.fileObject.name.lastIndexOf(\".\")+1, this.fileObject.name.length);\r\n                if(!this.options.type.includes(extension)){\r\n                    this.invalid = \"invalidtype\";\r\n                }\r\n            }\r\n            if(this.options.maxSize && this.invalid == null){\r\n                let comparesize = this.options.maxSize;\r\n                if(this.options.unit == \"KB\"){\r\n                    comparesize = comparesize * 1024;\r\n                }else if(this.options.unit == \"MB\"){\r\n                    comparesize = comparesize * 1024 * 1024;\r\n                }else if(this.options.unit == \"GB\"){\r\n                    comparesize = comparesize * 1024 * 1024 * 1024;\r\n                }\r\n                if(this.fileObject.size > comparesize){\r\n                    this.invalid = \"maxsize\";\r\n                }\r\n            }\r\n        }else{\r\n            if(this.options.required){\r\n                this.invalid = \"required\";\r\n            }\r\n        }\r\n    }\r\n\r\n    reset(){\r\n        this.formObject.file = null;\r\n        this.fileObject = null;\r\n        this.invalid = null;\r\n        this.options.disabled = false;\r\n    }\r\n\r\n    upload(){\r\n        this.options.actionUpload(this.fileObject);\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"groupSim.breadCrumb.group\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"flex gap-3 responsive-container\">\r\n        <button\r\n            [disabled]=\"createGroupForm.invalid || isExistGroupCode\"\r\n            type=\"button\"\r\n            pButton\r\n            [label]=\"tranService.translate('global.button.addSubToGroup')\"\r\n            (click)=\"addSubToGroup()\"\r\n        >\r\n        </button>\r\n        <button pButton [disabled]=\"createGroupForm.invalid || isExistGroupCode\" type=\"button\" [label]=\"tranService.translate('groupSim.label.addByFile')\" (click)=\"addSubFile()\" class=\"p-button-success\"></button>\r\n    </div>\r\n</div>\r\n<form\r\n    action=\"\"\r\n    [formGroup]=\"createGroupForm\"\r\n    (submit)=\"submitForm()\"\r\n    (keydown.enter)=\"$event.preventDefault()\"\r\n    class =\"responsive-form\">\r\n\r\n    <div class=\"mt-3\">\r\n        <p-card>\r\n            <div class=\"gap-4 mt-3 px-2 mb-0\">\r\n                <div class=\"flex flex-column gap-2 flex-1\">\r\n                    <label htmlFor=\"groupCode\" class=\"my-auto\" style=\"min-width: 110px;\">{{tranService.translate(\"groupSim.label.groupKey\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <input (blur)=\"onCodeBlur()\" pInputText id=\"groupKey\" formControlName=\"groupCode\" type=\"text\" [placeholder]=\"tranService.translate('groupSim.placeHolder.groupKey')\" />\r\n                </div>\r\n                <div class=\"flex flex-row gap-4 px-2 py-0 m-0\">\r\n                    <div *ngIf=\"createGroupForm.controls['groupCode']?.dirty && createGroupForm.controls['groupCode'].hasError('required')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"groupSim.error.requiredError\")}}\r\n                    </div>\r\n                    <div *ngIf=\"createGroupForm.controls.groupCode.errors?.['maxlength']\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"groupSim.error.lengthError_16\")}}\r\n                    </div>\r\n                    <div *ngIf=\"createGroupForm.controls.groupCode.errors?.['pattern']\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"groupSim.error.characterError_code\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isExistGroupCode\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"datapool.error.existedGroupCode\")}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex flex-column gap-2 flex-1 mt-3\">\r\n                    <label htmlFor=\"groupName\" class=\"my-auto\" style=\"min-width: 110px;\">{{tranService.translate(\"groupSim.label.groupName\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <input (blur)=\"onNameBlur()\" pInputText id=\"groupName\" formControlName=\"groupName\" type=\"text\" class=\"w-full\" [placeholder]=\"tranService.translate('groupSim.placeHolder.groupName')\"/>\r\n                </div>\r\n                <div class=\"flex flex-row gap-4 px-2 py-0 m-0\">\r\n                    <div *ngIf=\"createGroupForm.controls['groupName']?.dirty && (createGroupForm.controls['groupName'].hasError('required') || createGroupForm.controls.groupName.errors?.['whitespace'])\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"groupSim.error.requiredError\")}}\r\n                    </div>\r\n                    <div *ngIf=\"createGroupForm.controls.groupName.errors?.['maxlength']\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"groupSim.error.lengthError_255\")}}\r\n                    </div>\r\n                    <div *ngIf=\"createGroupForm.controls.groupName.errors?.['pattern']\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"groupSim.error.characterError_name\")}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full mt-3 px-2\">\r\n                <div class=\"flex flex-column gap-2 flex-1\">\r\n                    <label htmlFor=\"description\">{{tranService.translate(\"datapool.label.description\")}}</label>\r\n                    <textarea\r\n                        class=\"w-full\" style=\"resize: none;\"\r\n                        rows=\"5\"\r\n                        [autoResize]=\"false\"\r\n                        pInputTextarea id=\"description\"\r\n                        formControlName=\"description\"\r\n                        [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                    ></textarea>\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full field grid px-2 m-0 py-0 mb-3\">\r\n                <div *ngIf=\"createGroupForm.get('description').invalid && createGroupForm.get('description').dirty\">\r\n                    <div *ngIf=\"createGroupForm.get('description').errors.maxlength\" class=\"text-red-500\" >{{tranService.translate(\"global.message.maxLength\",{len:255})}}</div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"flex justify-content-center col-12 md:col-12 py-0 gap-3 mt-4\">\r\n                <p-button type=\"button\" [label]=\"tranService.translate('groupSim.label.buttonCancel')\"\r\n                    styleClass=\"p-button-outlined p-button-secondary\"\r\n                    routerLink=\"/data-pool/group/listGroupSub\"\r\n                ></p-button>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('groupSim.label.buttonSave')\" type=\"submit\" [disabled]=\"createGroupForm.invalid || isExistGroupCode\"></p-button>\r\n            </div>\r\n        </p-card>\r\n    </div>\r\n\r\n    <div class=\"mt-3\">\r\n        <p-dialog [contentStyle]=\"{'overflow':'visible'}\" class=\"w-full\" [header]=\"tranService.translate('global.button.addSubToGroup')\" [(visible)]=\"isShowDialogAddSub\" [modal]=\"true\" [style]=\"{ width: '800px', overflowY :'scroll', maxHeight : '80%', height: '400px' }\"  [draggable]=\"false\" [resizable]=\"false\">\r\n            <div class=\"mt-5 flex flex-row gap-3 justify-content-between\">\r\n                <div class=\"flex flex-row gap-3 col-12\">\r\n                    <div class=\"col-5\" style=\"max-width: calc(100% - 1px) !important;\">\r\n                        <vnpt-select\r\n                            [(value)]=\"phoneReceiptSelect\"\r\n                            (onchange)=\"checkValidAdd()\"\r\n                            (onSelectItem)=\"addPhone(phoneReceiptSelect)\"\r\n                            [isAutoComplete]=\"true\"\r\n                            [isMultiChoice]=\"false\"\r\n                            paramKey=\"phoneReceipt\"\r\n                            keyReturn=\"phoneReceipt\"\r\n                            [lazyLoad]=\"true\"\r\n                            [placeholder]=\"tranService.translate('datapool.label.receiverPhone')\"\r\n                            displayPattern=\"${phoneReceipt}\"\r\n                            [loadData]=\"getListShareInfoCbb.bind(this)\"\r\n                        ></vnpt-select>\r\n                    </div>\r\n                    <button [disabled]=\"isClickCreate || !isValidPhone\" type=\"button\" pButton [label]=\"tranService.translate('groupSim.label.buttonAddSim')\" (click)=\"addPhoneNotInSelect(phoneReceiptSelect)\"></button>\r\n                </div>\r\n            </div>\r\n            <div class=\"mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1\">\r\n                <div *ngIf=\"!isValidPhone\">\r\n                    {{tranService.translate(\"datapool.message.digitError\")}}\r\n                </div>\r\n            </div>\r\n            <p-table [value]=\"shareList\" [tableStyle]=\"{ 'min-width': '50rem' }\">\r\n                <ng-template pTemplate=\"header\">\r\n                    <tr>\r\n                        <th>{{tranService.translate(\"datapool.label.phone\")}}</th>\r\n                        <th>{{tranService.translate('datapool.label.fullName')}}</th>\r\n                        <th>{{tranService.translate('datapool.label.email')}}</th>\r\n                        <th></th>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"body\" let-list let-index=\"rowIndex\">\r\n                    <tr>\r\n                        <td>{{ list.phoneReceipt }}</td>\r\n                        <td>\r\n                            <input type=\"text\" (input)=\"changeDataName($event, index)\" pInputText [value]=\"list.name\" [readonly]=\"userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && list.createdBy!= null && list.createdBy != userInfo.id\">\r\n                            <div class=\"text-red-500\" *ngIf=\"list.name?.length >=50\">\r\n                                {{tranService.translate(\"global.message.maxLength\",{len:50})}}\r\n                            </div>\r\n                            <div *ngIf=\"!utilService.checkValidCharacterVietnamese(list.name)\" class=\"text-red-500\">\r\n                                {{tranService.translate(\"global.message.wrongFormatName\",{len:150})}}\r\n                            </div>\r\n                        </td>\r\n                        <td>\r\n                            <input type=\"text\" (input)=\"changeDataMail($event, index)\" pInputText [value]=\"list.email\" [readonly]=\"userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && list.createdBy!= null && list.createdBy != userInfo.id\">\r\n                            <div class=\"text-red-500\" *ngIf=\"list.email?.length >=100\">\r\n                                {{tranService.translate(\"global.message.maxLength\",{len:100})}}\r\n                            </div>\r\n                            <div class=\"text-red-500\" *ngIf=\"isMailInvalid(list.email)\">\r\n                                {{tranService.translate(\"global.message.formatEmail\")}}\r\n                            </div>\r\n                        </td>\r\n                        <td><button type=\"button\" pButton class=\"p-button-outlined\" (click)=\"deleteItem(index)\"><i class=\"pi pi-trash\"></i></button></td>\r\n                    </tr>\r\n                </ng-template>\r\n            </p-table>\r\n            <div *ngIf=\"shareList.length == 0\">\r\n                <div class=\"flex justify-content-center align-items-center\" style=\"height: 100px; min-height: 120px; text-align: center;\">\r\n                    <div class=\"box-item-empty\">\r\n                        <span class=\"pi pi-inbox\" style=\"font-size: x-large;\">&nbsp;</span>{{tranService.translate(\"global.text.nodata\")}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex justify-content-center col-12 md:col-12 py-0 gap-3 mt-4\">\r\n                <button pButton pRipple type=\"button\" [label]=\"tranService.translate('groupSim.label.buttonCancel')\" class=\"p-button-outlined p-button-secondary\" (click)=\"cancelAddSub()\"></button>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('groupSim.label.buttonSave')\" type=\"submit\" [disabled]=\"createGroupForm.invalid || shareList.length == 0 || !isAllEmailsValid()\"></p-button>\r\n            </div>\r\n        </p-dialog>\r\n    </div>\r\n</form>\r\n\r\n<style>\r\n    .button-reset-file{\r\n        width: fit-content;\r\n        height: fit-content;\r\n        top: 50%;\r\n        position: absolute;\r\n        right: 12px;\r\n        z-index: 2;\r\n        transform: translateY(-50%);\r\n        line-height: 14px;\r\n    }\r\n</style>\r\n\r\n<p-dialog [contentStyle]=\"{'overflow':'visible'}\" class=\"w-full responsive-dialog-2\" [header]=\"tranService.translate('groupSim.label.addPhoneByFile')\" [(visible)]=\"isShowDialogAddFile\" [modal]=\"true\" [style]=\"{ width: '800px', overflowY :'scroll', maxHeight : '80%' }\"  [draggable]=\"false\" [resizable]=\"false\" (onHide)=\"reset()\">\r\n    <div class=\"w-full field grid\">\r\n        <div class=\"col-10 flex flex-column justify-content-start\">\r\n            <div class=\"w-full h-auto flex flex-row justify-content-start align-items-center\">\r\n                <div class=\"relative mr-2\" [style]=\"{'width': (options.isShowButtonUpload?'80%':'100%')}\">\r\n                    <div class=\"h-full w-full absolute top-0 left-0 z-1 opacity-0\">\r\n                        <input\r\n                            type=\"file\"\r\n                            [(ngModel)]=\"formObject.file\"\r\n                            class=\"h-full w-full\"\r\n                            [class]=\"options.disabled?'':'cursor-pointer'\"\r\n                            (change)=\"changeFile($event)\"\r\n                            [disabled]=\"options.disabled\"\r\n                        />\r\n                    </div>\r\n                    <div [class]=\"options.disabled?'bg-black-alpha-10':''\"  class=\"w-full border-1 border-black-alpha-40 border-round border-dotted flex flex-row justify-content-center align-items-center\" style=\"box-sizing: border-box;min-height: 42px;\">\r\n                        <div class=\"max-w-full overflow-hidden text-overflow-ellipsis p-2 pl-4 pr-4 white-space\" style=\"box-sizing: border-box;\">\r\n                            {{fileObject?textDescription:tranService.translate(\"global.button.uploadFile\")}}\r\n                        </div>\r\n                    </div>\r\n                    <div *ngIf=\"fileObject != null && !options.disabled\" class=\"cursor-pointer button-reset-file\" (click)=\"resetFile()\">\r\n                        <i class=\"pi pi-times\"></i>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div>\r\n                <small class=\"text-red-500\" *ngIf=\"invalid =='required'\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                <small class=\"text-red-500\" *ngIf=\"invalid =='maxsize'\">{{tranService.translate(\"global.message.maxsizeupload\",{len:50})}}</small>\r\n                <small class=\"text-red-500\" *ngIf=\"invalid =='invalidtype'\">{{options.messageErrorType ? options.messageErrorType : tranService.translate(\"global.message.invalidtypeupload\")}}</small>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-2 flex flex-row justify-content-end align-items-center\">\r\n            <p-button icon=\"pi pi-download\" [pTooltip]=\"tranService.translate('global.button.downloadTemp')\" styleClass=\"p-button-outlined p-button-secondary\" (click)=\"downloadTemplate()\"></p-button>\r\n        </div>\r\n    </div>\r\n<!--    <div class=\"grid\"><div class=\"col pt-0\"><small class=\"text-red-500\" *ngIf=\"isShowErrorUpload\">{{messageErrorUpload}}</small></div></div>-->\r\n    <div class=\"flex justify-content-center gap-3\">\r\n        <button [disabled]=\"invalid || fileObject == null || options.disabled\" pButton type=\"button\" [pTooltip]=\"tranService.translate('global.button.upFile')\" (click)=\"upload()\">{{tranService.translate(\"global.button.save\")}}</button>\r\n        <button pButton (click)=\"isShowDialogAddFile = false\"  class=\"p-button-outlined p-button-secondary\" type=\"button\">{{tranService.translate(\"global.button.cancel\")}}</button>\r\n    </div>\r\n</p-dialog>\r\n\r\n"], "mappings": ";AACA,SAAQA,aAAa,QAAO,4BAA4B;AACxD,SAAQC,SAAS,QAA0B,qCAAqC;AAGhF,SAAQC,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAoB,gBAAgB;AAC9E,SAAQC,gBAAgB,QAAO,4DAA4D;AAE3F,SAAQC,oBAAoB,QAAO,mDAAmD;AACtF,SAAQC,sBAAsB,QAAO,qDAAqD;AAC1F,SAAQC,uBAAuB,QAAO,yCAAyC;AAE/E,OAAO,KAAKC,KAAK,MAAM,SAAS;AAChC,OAAO,KAAKC,MAAM,MAAM,eAAe;AACvC,SAAQC,MAAM,QAAO,YAAY;AACjC,OAAO,KAAKC,SAAS,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;ICiBnBC,EAAA,CAAAC,cAAA,cAA6I;IACzID,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,sCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA2F;IACvFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,uCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyF;IACrFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAK,MAAA,CAAAH,WAAA,CAAAC,SAAA,4CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAmD;IAC/CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAM,MAAA,CAAAJ,WAAA,CAAAC,SAAA,yCACJ;;;;;IAOAR,EAAA,CAAAC,cAAA,cAA4M;IACxMD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,sCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA2F;IACvFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyF;IACrFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAS,MAAA,CAAAP,WAAA,CAAAC,SAAA,4CACJ;;;;;;;;;;IAkBAR,EAAA,CAAAC,cAAA,cAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAe,iBAAA,CAAAC,OAAA,CAAAT,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAiB,eAAA,IAAAC,GAAA,GAA+D;;;;;IAD1JlB,EAAA,CAAAC,cAAA,UAAoG;IAChGD,EAAA,CAAAmB,UAAA,IAAAC,mDAAA,kBAA4J;IAChKpB,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAqB,UAAA,SAAAC,MAAA,CAAAC,eAAA,CAAAC,GAAA,gBAAAC,MAAA,CAAAC,SAAA,CAAyD;;;;;IAqCnE1B,EAAA,CAAAC,cAAA,UAA2B;IACvBD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsB,MAAA,CAAApB,WAAA,CAAAC,SAAA,qCACJ;;;;;IAIIR,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAA4B,SAAA,SAAS;IACb5B,EAAA,CAAAG,YAAA,EAAK;;;;IAJGH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAe,iBAAA,CAAAc,MAAA,CAAAtB,WAAA,CAAAC,SAAA,yBAAiD;IACjDR,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAe,iBAAA,CAAAc,MAAA,CAAAtB,WAAA,CAAAC,SAAA,4BAAoD;IACpDR,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAe,iBAAA,CAAAc,MAAA,CAAAtB,WAAA,CAAAC,SAAA,yBAAiD;;;;;;;;;;IASjDR,EAAA,CAAAC,cAAA,cAAyD;IACrDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyB,OAAA,CAAAvB,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAiB,eAAA,IAAAc,GAAA,QACJ;;;;;;;;;;IACA/B,EAAA,CAAAC,cAAA,cAAwF;IACpFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2B,OAAA,CAAAzB,WAAA,CAAAC,SAAA,mCAAAR,EAAA,CAAAiB,eAAA,IAAAgB,GAAA,QACJ;;;;;;;;;;IAIAjC,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA6B,OAAA,CAAA3B,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAiB,eAAA,IAAAkB,GAAA,QACJ;;;;;IACAnC,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA+B,OAAA,CAAA7B,WAAA,CAAAC,SAAA,oCACJ;;;;;;IAlBRR,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IACmBD,EAAA,CAAAqC,UAAA,mBAAAC,6EAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,QAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAF,OAAA,CAAAG,cAAA,CAAAT,MAAA,EAAAI,SAAA,CAA6B;IAAA,EAAC;IAA1D3C,EAAA,CAAAG,YAAA,EAA+M;IAC/MH,EAAA,CAAAmB,UAAA,IAAA8B,2DAAA,kBAEM;IACNjD,EAAA,CAAAmB,UAAA,IAAA+B,2DAAA,kBAEM;IACVlD,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACmBD,EAAA,CAAAqC,UAAA,mBAAAc,6EAAAZ,MAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,QAAA;MAAA,MAAAQ,OAAA,GAAApD,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAK,OAAA,CAAAC,cAAA,CAAAd,MAAA,EAAAI,SAAA,CAA6B;IAAA,EAAC;IAA1D3C,EAAA,CAAAG,YAAA,EAAgN;IAChNH,EAAA,CAAAmB,UAAA,IAAAmC,2DAAA,kBAEM;IACNtD,EAAA,CAAAmB,UAAA,KAAAoC,4DAAA,kBAEM;IACVvD,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAwDD,EAAA,CAAAqC,UAAA,mBAAAmB,+EAAA;MAAA,MAAAhB,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,QAAA;MAAA,MAAAa,OAAA,GAAAzD,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAf,SAAA,CAAiB;IAAA,EAAC;IAAC3C,EAAA,CAAA4B,SAAA,aAA2B;IAAA5B,EAAA,CAAAG,YAAA,EAAS;;;;;IAnBxHH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,iBAAA,CAAA4C,QAAA,CAAAC,YAAA,CAAuB;IAE+C5D,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAqB,UAAA,UAAAsC,QAAA,CAAAE,IAAA,CAAmB,aAAAC,OAAA,CAAAC,QAAA,CAAAC,IAAA,IAAAF,OAAA,CAAA1E,SAAA,CAAA6E,SAAA,CAAAC,QAAA,IAAAP,QAAA,CAAAQ,SAAA,YAAAR,QAAA,CAAAQ,SAAA,IAAAL,OAAA,CAAAC,QAAA,CAAAK,EAAA;IAC9DpE,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAqB,UAAA,UAAAsC,QAAA,CAAAE,IAAA,kBAAAF,QAAA,CAAAE,IAAA,CAAAQ,MAAA,QAA4B;IAGjDrE,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAqB,UAAA,UAAAyC,OAAA,CAAAQ,WAAA,CAAAC,6BAAA,CAAAZ,QAAA,CAAAE,IAAA,EAA2D;IAKK7D,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,UAAAsC,QAAA,CAAAa,KAAA,CAAoB,aAAAV,OAAA,CAAAC,QAAA,CAAAC,IAAA,IAAAF,OAAA,CAAA1E,SAAA,CAAA6E,SAAA,CAAAC,QAAA,IAAAP,QAAA,CAAAQ,SAAA,YAAAR,QAAA,CAAAQ,SAAA,IAAAL,OAAA,CAAAC,QAAA,CAAAK,EAAA;IAC/DpE,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAqB,UAAA,UAAAsC,QAAA,CAAAa,KAAA,kBAAAb,QAAA,CAAAa,KAAA,CAAAH,MAAA,SAA8B;IAG9BrE,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAqB,UAAA,SAAAyC,OAAA,CAAAW,aAAA,CAAAd,QAAA,CAAAa,KAAA,EAA+B;;;;;IAQ1ExE,EAAA,CAAAC,cAAA,UAAmC;IAG+BD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADiEH,EAAA,CAAAI,SAAA,GACvE;IADuEJ,EAAA,CAAAK,kBAAA,KAAAqE,OAAA,CAAAnE,WAAA,CAAAC,SAAA,4BACvE;;;;;;IA4CAR,EAAA,CAAAC,cAAA,cAAoH;IAAtBD,EAAA,CAAAqC,UAAA,mBAAAsC,mEAAA;MAAA3E,EAAA,CAAAyC,aAAA,CAAAmC,IAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAA8B,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAC/G9E,EAAA,CAAA4B,SAAA,YAA2B;IAC/B5B,EAAA,CAAAG,YAAA,EAAM;;;;;IAIVH,EAAA,CAAAC,cAAA,gBAAyD;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAe,iBAAA,CAAAgE,OAAA,CAAAxE,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC7GR,EAAA,CAAAC,cAAA,gBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA1EH,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAe,iBAAA,CAAAiE,OAAA,CAAAzE,WAAA,CAAAC,SAAA,iCAAAR,EAAA,CAAAiB,eAAA,IAAAc,GAAA,GAAkE;;;;;IAC1H/B,EAAA,CAAAC,cAAA,gBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAmH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA3HH,EAAA,CAAAI,SAAA,GAAmH;IAAnHJ,EAAA,CAAAe,iBAAA,CAAAkE,OAAA,CAAAC,OAAA,CAAAC,gBAAA,GAAAF,OAAA,CAAAC,OAAA,CAAAC,gBAAA,GAAAF,OAAA,CAAA1E,WAAA,CAAAC,SAAA,qCAAmH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADzL/L,OAAM,MAAO4E,6BAA8B,SAAQjG,aAAa;EA8B5DkG,YACIC,QAAkB,EACVC,WAAwB,EACxBC,qBAA4C,EACdC,aAAmC,EACjCC,YAAoC;IAE5E,KAAK,CAACJ,QAAQ,CAAC;IALP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,qBAAqB,GAArBA,qBAAqB;IACS,KAAAC,aAAa,GAAbA,aAAa;IACX,KAAAC,YAAY,GAAZA,YAAY;IAlCxD,KAAAnE,eAAe,GAAG,IAAIjC,SAAS,CAAC;MAC5BqG,SAAS,EAAE,IAAItG,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACqG,QAAQ,EAACrG,UAAU,CAACsG,SAAS,CAAC,EAAE,CAAC,EAAEtG,UAAU,CAACuG,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;MACtHC,SAAS,EAAE,IAAI1G,WAAW,CAAC,EAAE,EAAC,CAACE,UAAU,CAACqG,QAAQ,EAAErG,UAAU,CAACsG,SAAS,CAAC,GAAG,CAAC,EAAEtG,UAAU,CAACuG,OAAO,CAAC,mDAAmD,CAAC,EAAEnG,uBAAuB,EAAE,CAAC,CAAC;MACnLqG,WAAW,EAAE,IAAI3G,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACsG,SAAS,CAAC,GAAG,CAAC,CAAC;KAC/D,CAAC;IAGF,KAAAI,mBAAmB,GAAqB,IAAIzG,gBAAgB,EAAE;IAC9D,KAAA0G,aAAa,GAAY,KAAK;IAC9B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,aAAa,GAAY,IAAI;IAC7B,KAAAC,SAAS,GAAoB,EAAE;IAC/B,KAAAC,YAAY,GAAY,IAAI;IAC5B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,iBAAiB,GAAY,KAAK;IAIlC,KAAAC,SAAS,GAAQ,EAAE;IAwZnB,KAAAC,UAAU,GAAG,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,KAAI;MAEvC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,KAAK,CAAC,EAAE;QAC3B5C,IAAI,EAAE8C,QAAQ,IAAI;OACrB,CAAC;MACF/G,SAAS,CAACD,MAAM,CAACiH,IAAI,EAAEF,QAAQ,CAAC;IACpC,CAAC;IA0EkB,KAAAzH,SAAS,GAAGA,SAAS;EAtdxC;EAEA6H,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,UAAU,GAAG;MACdL,IAAI,EAAE;KACT;IACD,IAAI,CAACM,YAAY,GAAG,IAAI,CAAC9B,WAAW,CAAC+B,KAAK,CAAC,IAAI,CAACF,UAAU,CAAC;IAC3D,IAAI,CAACG,eAAe,GAAG,IAAI,CAAChH,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;IAC7E0G,EAAE,CAACnD,QAAQ,GAAG,IAAI,CAACyD,cAAc,CAACzD,QAAQ;IAC1C,IAAI,CAACmB,OAAO,GAAG;MACXlB,IAAI,EAAE,CAAC,KAAK,EAAC,MAAM,CAAC;MACpBmB,gBAAgB,EAAE,IAAI,CAAC5E,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MAC7EiH,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,IAAI;MACV9B,QAAQ,EAAE,IAAI;MACd+B,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;MACxCC,QAAQ,EAAE;KACb;IACD,IAAIb,EAAE,CAACc,WAAW,CAAC,CAAC5I,SAAS,CAAC6I,WAAW,CAACC,WAAW,CAACC,MAAM,CAAC,CAAC,EAAE;MAC5D,IAAI,CAACC,KAAK,GAAG,CAAC;QAAEC,KAAK,EAAE,IAAI,CAAC9H,WAAW,CAACC,SAAS,CAAC,+BAA+B;MAAC,CAAE,EAAE;QAAE6H,KAAK,EAAE,IAAI,CAAC9H,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAAE8H,UAAU,EAAE;MAA+B,CAAE,EAAE;QAAED,KAAK,EAAE,IAAI,CAAC9H,WAAW,CAACC,SAAS,CAAC,iCAAiC;MAAC,CAAE,CAAC;MAC/Q,IAAI,CAAC+H,IAAI,GAAG;QAAEC,IAAI,EAAE,YAAY;QAAEF,UAAU,EAAE;MAAG,CAAE;MACnD,IAAI,CAACG,SAAS,GAAG,EAAE;MACnBvB,EAAE,CAACwB,WAAW,EAAE;KACnB,MAAM;MACHC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;;EAExC;EAEAH,WAAWA,CAAA;IACP,IAAIxB,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4B,oBAAoB,CAACC,MAAM,EAAE;IAChC7B,EAAE,CAAC1B,qBAAqB,CAACkD,WAAW,CAAEM,QAAQ,IAAI;MAC9C9B,EAAE,CAACR,SAAS,GAAGsC,QAAQ;IAC3B,CAAC,EAAE,IAAI,EAAE,MAAK;MACV9B,EAAE,CAAC4B,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,mBAAmBA,CAACC,MAAM,EAAEC,QAAQ;IAChC,OAAO,IAAI,CAAC1D,YAAY,CAACwD,mBAAmB,CAACC,MAAM,EAAGH,QAAQ,IAAG;MAC7D,IAAI,CAAC3C,SAAS,GAAG2C,QAAQ,CAACK,OAAO;MACjCD,QAAQ,CAACJ,QAAQ,CAAC;IACtB,CAAC,CAAC;EACN;EAEAM,UAAUA,CAAA;IACN,IAAIpC,EAAE,GAAG,IAAI;IACb,IAAIqC,UAAU,GAAG;MACb5D,SAAS,EAAEuB,EAAE,CAAC3F,eAAe,CAACiI,KAAK,CAAC7D,SAAS,CAAC8D,IAAI,EAAE;MACpD1D,SAAS,EAAEmB,EAAE,CAAC3F,eAAe,CAACiI,KAAK,CAACzD,SAAS,CAAC0D,IAAI,EAAE;MACpDzD,WAAW,EAAEkB,EAAE,CAAC3F,eAAe,CAACiI,KAAK,CAACxD,WAAW,CAACyD,IAAI,EAAE;MACxDC,OAAO,EAAExC,EAAE,CAACuB;KACf;IACD,IAAI,CAACK,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACvD,qBAAqB,CAACmE,MAAM,CAAC,EAAE,EAACJ,UAAU,EAAC,EAAE,EAAEP,QAAQ,IAAG;MAC3D9B,EAAE,CAAC4B,oBAAoB,CAACc,OAAO,CAAC1C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;MACvF,IAAI,CAACqJ,MAAM,CAACC,QAAQ,CAAC,CAAC,yBAAyBd,QAAQ,CAAC5E,EAAE,EAAE,CAAC,CAAC;IAClE,CAAC,EAAG2F,KAAK,IAAI;MACT,IAAIA,KAAK,CAACA,KAAK,CAACA,KAAK,CAACC,SAAS,KAAK,uBAAuB,EAAE;QACzD9C,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC,IAAI,CAACxJ,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC,CAAC;;IAEpG,CAAC,EACG,MAAK;MACL0G,EAAE,CAAC4B,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAgB,aAAaA,CAAA;IACT,IAAI,CAAC7D,aAAa,GAAG,IAAI;IACzB,IAAG,CAAC,IAAI,CAACC,SAAS,CAAC6D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvG,YAAY,CAACwG,QAAQ,EAAE,KAAK,IAAI,CAACjE,kBAAkB,CAAC,EAAC;MACpF,IAAI,CAACC,aAAa,GAAG,KAAK;KAC7B,MAAI;MACD,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE7B,IAAG,IAAI,CAACD,kBAAkB,IAAI,EAAE,IAAG,IAAI,CAACA,kBAAkB,IAAI,IAAI,IAAI,IAAI,CAACA,kBAAkB,KAAKkE,SAAS,EAAC;MACxG,IAAI,CAACjE,aAAa,GAAG,IAAI;;IAG7B,MAAMkE,KAAK,GAAG,gBAAgB;IAC9B,MAAMC,UAAU,GAAG,IAAI,CAACpE,kBAAkB;IAC1C,IAAI,CAACG,YAAY,GAAGgE,KAAK,CAACE,IAAI,CAACD,UAAU,CAAC;EAC9C;EAEAE,UAAUA,CAACC,GAAU;IACjB,OAAOA,GAAG,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKP,SAAS,IAAIO,IAAI,KAAK,EAAE,CAAC;EACjF;EAEAC,aAAaA,CAAA;IACT,IAAI3D,EAAE,GAAG,IAAI;IACbA,EAAE,CAACX,kBAAkB,GAAG,IAAI;EAChC;EAEAuE,UAAUA,CAAA;IACN,IAAI5D,EAAE,GAAG,IAAI;IACbA,EAAE,CAACV,mBAAmB,GAAG,IAAI;EACjC;EAEAuE,YAAYA,CAAA;IACR,IAAI7D,EAAE,GAAG,IAAI;IACbA,EAAE,CAACX,kBAAkB,GAAG,KAAK;IAC7BW,EAAE,CAACuB,SAAS,GAAG,EAAE;IACjBvB,EAAE,CAACf,kBAAkB,GAAG,EAAE;EAC9B;EAEA6E,QAAQA,CAACC,IAAI;IACT,IAAI/D,EAAE,GAAG,IAAI;IACb,IAAG+D,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKZ,SAAS,EAAC;MACnC;;IAEJnD,EAAE,CAACd,aAAa,GAAG,KAAK;IACxB,MAAMoD,KAAK,GAAGtC,EAAE,CAACb,SAAS,CAAC6D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvG,YAAY,KAAKqH,IAAI,CAAC;IACjE,MAAMC,KAAK,GAAGC,MAAM,CAACF,IAAI,CAAC,EAAEG,OAAO,CAAC,IAAI,EAAC,IAAI,CAAC;IAC9C;IACA,IAAIC,MAAM,GAAG,IAAI,CAAC5C,SAAS,CAAC6C,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAChH,YAAY,CAACwG,QAAQ,EAAE,KAAKc,KAAK,CAAC;IAChF,IAAIhE,EAAE,CAACuB,SAAS,CAACpE,MAAM,IAAIjF,SAAS,CAAC8I,WAAW,CAACqD,SAAS,EAAE;MACxDrE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;MACzF;KACH,MAAM,IAAI0G,EAAE,CAACuB,SAAS,CAACpE,MAAM,GAAGjF,SAAS,CAAC8I,WAAW,CAACsD,KAAK,EAAE;MAC1DtE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;MAC7F;KACH,MAAM,IAAI6K,MAAM,EAAE;MACfI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5BxE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC,CAAC;MAC9F;;IAGJ,IAAIgJ,KAAK,EAAEmC,OAAO,EAAE;MAChBzE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,+BAA+B,EAAE;QAACyK,IAAI,EAAEA,IAAI;QAAElF,SAAS,EAAEyD,KAAK,EAAEzD;MAAS,CAAC,CAAC,CAAC;KACtI,MAAM;MACHmB,EAAE,CAAC0E,aAAa,CAACpC,KAAK,EAAEyB,IAAI,CAAC;MAC7B;;;;;;;;MAQA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;EAER;;EAEAY,mBAAmBA,CAACX,KAAK;IACrB,IAAIhE,EAAE,GAAG,IAAI;IAEb,IAAG,CAACgE,KAAK,EAAC;MACN;;IAGJ,MAAM1B,KAAK,GAAGtC,EAAE,CAACb,SAAS,CAAC6D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvG,YAAY,KAAKsH,KAAK,CAAC;IAClE,MAAMY,UAAU,GAAGX,MAAM,CAACD,KAAK,CAAC,EAAEE,OAAO,CAAC,IAAI,EAAC,IAAI,CAAC;IACpD;IACA,IAAIC,MAAM,GAAG,IAAI,CAAC5C,SAAS,CAAC6C,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAChH,YAAY,CAACwG,QAAQ,EAAE,KAAK0B,UAAU,CAAC;IACrF,IAAI5E,EAAE,CAACuB,SAAS,CAACpE,MAAM,IAAIjF,SAAS,CAAC8I,WAAW,CAACqD,SAAS,EAAE;MACxDrE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;MACzF;KACH,MAAM,IAAI0G,EAAE,CAACuB,SAAS,CAACpE,MAAM,GAAGjF,SAAS,CAAC8I,WAAW,CAACsD,KAAK,EAAE;MAC1DtE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;MAC7F;KACH,MAAM,IAAI6K,MAAM,EAAE;MACfI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5BxE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC,CAAC;MAC9F;;IAEJ0G,EAAE,CAAC1B,qBAAqB,CAACuG,qBAAqB,CAAC;MAACC,WAAW,EAAEd;IAAK,CAAC,EAAGlC,QAAQ,IAAG;MAG7E,IAAIA,QAAQ,EAAE;QACV9B,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC,CAAC;OAC7F,MAAM;QACH,IAAIgJ,KAAK,EAAEmC,OAAO,EAAE;UAChBzE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC,6BAA6BP,KAAK,CAACzD,SAAS,GAAG,CAAC;SACjF,MAAM;UACHmB,EAAE,CAAC0E,aAAa,CAACpC,KAAK,EAAE0B,KAAK,CAAC;;QAElC;;;QAGA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAhE,EAAE,CAACf,kBAAkB,GAAG,EAAE;QAC1B,IAAI,CAAC+C,mBAAmB,CAAC+C,IAAI,CAAC,IAAI,CAAC;;IAE3C,CAAC,EAAC,IAAI,EAAE,IAAI,CAAE;EAClB;EAEAL,aAAaA,CAACpC,KAAK,EAAEyB,IAAI;IACrBQ,OAAO,CAACC,GAAG,CAAClC,KAAK,CAAC;IAClB,IAAItC,EAAE,GAAG,IAAI;IACb,IAAImE,MAAM,GAAG,IAAI,CAAC5C,SAAS,CAAC6C,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAChH,YAAY,KAAKqH,IAAI,CAAC;IACpE,IAAGzB,KAAK,EAAC;MACL,IAAItC,EAAE,CAACuB,SAAS,CAACpE,MAAM,IAAIjF,SAAS,CAAC8I,WAAW,CAACqD,SAAS,EAAE;QACxDrE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;OAC5F,MAAM,IAAI0G,EAAE,CAACuB,SAAS,CAACpE,MAAM,IAAIjF,SAAS,CAAC8I,WAAW,CAACsD,KAAK,EAAE;QAC3DtE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;OAChG,MAAM,IAAI6K,MAAM,EAAE;QACfnE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC,CAAC;OACjG,MAAM;QACH0G,EAAE,CAACuB,SAAS,CAACyD,OAAO,CAAC1C,KAAK,CAAC;QAC3B2C,UAAU,CAAC;UACPjF,EAAE,CAACf,kBAAkB,GAAG,EAAE;QAC9B,CAAC,EAAC,GAAG,CAAC;;KAEb,MAAI;MACD,IAAIiG,QAAQ,GAAgB;QACxBhI,EAAE,EAAEoF,KAAK,EAAEpF,EAAE,IAAI,EAAE;QACnBR,YAAY,EAAEqH,IAAI;QAClBpH,IAAI,EAAC2F,KAAK,EAAE3F,IAAI,IAAI,EAAE;QACtBW,KAAK,EAACgF,KAAK,EAAEhF,KAAK,IAAI;OACzB;MACD,IAAI0C,EAAE,CAACuB,SAAS,CAACpE,MAAM,IAAIjF,SAAS,CAAC8I,WAAW,CAACqD,SAAS,EAAE;QACxDrE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;OAC5F,MAAM,IAAI0G,EAAE,CAACuB,SAAS,CAACpE,MAAM,GAAGjF,SAAS,CAAC8I,WAAW,CAACsD,KAAK,EAAE;QAC1DtE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;OAChG,MAAM,IAAI6K,MAAM,EAAE;QACfnE,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC,CAAC;OACjG,MAAM;QACH0G,EAAE,CAACuB,SAAS,CAACyD,OAAO,CAACE,QAAQ,CAAC;;;IAGtClF,EAAE,CAACd,aAAa,GAAG,IAAI;EAC3B;EAEApD,cAAcA,CAACqJ,KAAK,EAAEC,CAAC;IACnB,MAAMC,UAAU,GAAGF,KAAK,CAACG,MAAM,CAAChD,KAAK;IACrC,IAAI,CAACf,SAAS,CAAC6D,CAAC,CAAC,CAACzI,IAAI,GAAG0I,UAAU;EACvC;EAEAlJ,cAAcA,CAACgJ,KAAK,EAAEC,CAAC;IACnB,MAAMC,UAAU,GAAGF,KAAK,CAACG,MAAM,CAAChD,KAAK;IACrC,IAAI,CAACf,SAAS,CAAC6D,CAAC,CAAC,CAAC9H,KAAK,GAAG+H,UAAU;IACpC,IAAI,CAACE,gBAAgB,EAAE;EAC3B;EAEAhI,aAAaA,CAACD,KAAY;IACtB,IAAI,CAACA,KAAK,EAAC;MACP,OAAO,KAAK;;IAEhB;IACA,MAAMsB,OAAO,GAAW,qEAAqE;IAC7F,OAAO,CAACA,OAAO,CAAC0E,IAAI,CAAChG,KAAK,CAAC;EAC/B;EACA;EACAiI,gBAAgBA,CAAA;IACZ,OAAO,IAAI,CAAChE,SAAS,CAACiE,KAAK,CAAC9B,IAAI,IAAI,CAAC,IAAI,CAACnG,aAAa,CAACmG,IAAI,CAACpG,KAAK,CAAC,CAAC;EACxE;EAEAd,UAAUA,CAAC4I,CAAC;IACR,IAAI,CAACxD,oBAAoB,CAAC6D,OAAO,CAAC,IAAI,CAACpM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC,EACrF,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,EAAC;MACzDoM,EAAE,EAAEA,CAAA,KAAI;QACJ,MAAMC,CAAC,GAAG,IAAI,CAACpE,SAAS,CAAC6D,CAAC,CAAC,CAACrB,IAAI;QAChC,IAAG4B,CAAC,EAAC;UACD,IAAI,CAACpE,SAAS,CAAC6D,CAAC,CAAC,CAACrB,IAAI,GAAG,IAAI;UAC7B,IAAI,CAACxC,SAAS,CAAC6D,CAAC,CAAC,CAACQ,OAAO,GAAG,IAAI;;QAEpC,IAAI,CAACrE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACkC,MAAM,CAAC,CAACC,IAAI,EAACmC,KAAK,KAAKA,KAAK,IAAIT,CAAC,CAAC;MACtE;KACH,CAAC;EACV;EACAU,UAAUA,CAAA;IACN,IAAI9F,EAAE,GAAG,IAAI;IACb,IAAI+F,cAAc,GAAG,IAAI,CAAC1L,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,CAACgI,KAAK;IAChEyD,cAAc,GAAGA,cAAc,CAACxD,IAAI,EAAE,CAAC2B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAC3D,IAAI,CAAC7J,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,CAAC0L,QAAQ,CAACD,cAAc,CAAC;EAClE;EACAE,UAAUA,CAAA;IACN,IAAIjG,EAAE,GAAG,IAAI;IACb,IAAIsC,KAAK,GAAG,IAAI,CAACjI,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,CAACgI,KAAK;IACvD,IAAI,CAAChE,qBAAqB,CAAC4H,mBAAmB,CAAC;MAACzH,SAAS,EAAE6D;IAAK,CAAC,EAAG6D,GAAG,IAAI;MACvE,IAAIA,GAAG,IAAI,IAAI,EAAE;QACb,IAAI,CAAClG,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;EACN;EAEAmG,iBAAiBA,CAAA;IACb,IAAI,CAAC7G,iBAAiB,GAAG,KAAK;EAClC;EAEAoB,UAAUA,CAAC0F,UAAe;IAAA,IAAAC,KAAA;IACtB,IAAItG,EAAE,GAAG,IAAI;IACb,IAAGqG,UAAU,CAACE,IAAI,IAAI,OAAO,EAAC;MAC1B,IAAI,CAAC3E,oBAAoB,CAACiB,KAAK,CAAC,4CAA4C,CAAC;MAC7E;;IAEJ,IAAIR,UAAU,GAAG;MACb5D,SAAS,EAAEuB,EAAE,CAAC3F,eAAe,CAACiI,KAAK,CAAC7D,SAAS,CAAC8D,IAAI,EAAE;MACpD1D,SAAS,EAAEmB,EAAE,CAAC3F,eAAe,CAACiI,KAAK,CAACzD,SAAS,CAAC0D,IAAI,EAAE;MACpDzD,WAAW,EAAEkB,EAAE,CAAC3F,eAAe,CAACiI,KAAK,CAACxD,WAAW,CAACyD,IAAI;KACzD;IACDvC,EAAE,CAAC4B,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACvD,qBAAqB,CAACqC,UAAU,CAAC0F,UAAU,EAAEhE,UAAU;MAAA,IAAAmE,IAAA,GAAAC,iBAAA,CAAE,WAAO3E,QAAQ,EAAI;QAC7E,MAAM4E,SAAS,GAAG5E,QAAQ,CAAC6E,OAAO,CAACrM,GAAG,CAAC,YAAY,CAAC;QACpD,MAAMsM,SAAS,GAAG,EAAE;QACpB,MAAMC,gBAAgB,GAAG;UACrB,IAAI,EAAE,8BAA8B;UACpC,KAAK,EAAE/E,QAAQ,IAAI8E,SAAS,CAACE,IAAI,CAAChF,QAAQ,EAAE6E,OAAO,EAAErM,GAAG,CAAC,OAAO,CAAC,CAAC;UAClE,KAAK,EAAE,mCAAmC;UAC1C,KAAK,EAAE,uBAAuB;UAC9B,KAAK,EAAE,wBAAwB;UAC/B,KAAK,EAAE,wBAAwB;UAC/B,KAAK,EAAE,4CAA4C;UACnD,KAAK,EAAEwH,QAAQ,IAAI8E,SAAS,CAACE,IAAI,CAAChF,QAAQ,EAAE6E,OAAO,EAAErM,GAAG,CAAC,qBAAqB,CAAC,CAAC;UAChF,KAAK,EAAE,wBAAwB;UAC/B,KAAK,EAAE,wBAAwB;UAC/B,KAAK,EAAE;SACV;QAED,IAAGoM,SAAS,EAAC;UACT1G,EAAE,CAAC4B,oBAAoB,CAACc,OAAO,CAAC,sCAAsC,CAAC;UACvE1C,EAAE,CAACV,mBAAmB,GAAG,KAAK;UAC9BgH,KAAI,CAAC3D,MAAM,CAACC,QAAQ,CAAC,CAAC,uBAAuB,EAAE8D,SAAS,CAAC,CAAC;;QAG9D,IAAI5E,QAAQ,EAAE6E,OAAO,EAAErM,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,C,CAG5C,MAAM;UACH0F,EAAE,CAACT,iBAAiB,GAAG,IAAI;UAC3B,MAAMwH,YAAY,GAAGF,gBAAgB,CAAC/E,QAAQ,EAAE6E,OAAO,EAAErM,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,oBAAoB;UAC9FiK,OAAO,CAACC,GAAG,CAACqC,gBAAgB,CAAC;UAC7B,IAAI,OAAOE,YAAY,KAAK,UAAU,EAAE;YACpCA,YAAY,CAACjF,QAAQ,CAAC;YACtB,IAAI,CAACA,QAAQ,EAAEkF,IAAI,EAAE;cACjB,MAAMrH,QAAQ,GAAGmC,QAAQ,EAAE6E,OAAO,EAAErM,GAAG,CAAC,qBAAqB,CAAC;cAC9D,MAAM2M,QAAQ,GAAG,IAAIvO,KAAK,CAACwO,QAAQ,EAAE;cACrC,MAAMC,GAAG,SAASF,QAAQ,CAACG,IAAI,CAACC,WAAW,EAAE;cAC7C,MAAMC,cAAc,GAAG3H,QAAQ,CAAC4H,SAAS,CAAC,CAAC,EAAE5H,QAAQ,CAACxC,MAAM,GAAG,CAAC,CAAC;cACjE,MAAMqK,cAAc,GAAG,EAAE,CAACC,MAAM,CAACH,cAAc,EAAE,iBAAiB,EAAE3O,MAAM,EAAE,CAAC+O,MAAM,CAAC,gBAAgB,CAAC,CAAC;cACtG;cACA,MAAM9O,MAAM,CAAC,IAAIkH,IAAI,CAAC,CAACqH,GAAG,CAAC,CAAC,EAAE,GAAGK,cAAc,OAAO,CAAC;aAC1D,MAAM;cACH,MAAMG,UAAU,GAAGhP,MAAM,EAAE,CAAC+O,MAAM,CAAC,gBAAgB,CAAC;cACpD,MAAM/K,IAAI,GAAG,CAAC0J,UAAU,CAAC1J,IAAI,IAAI0J,UAAU,CAAC1G,QAAQ,EAAEiI,KAAK,CAAC,GAAG,CAAC;cAChE5H,EAAE,CAACP,UAAU,CAACqC,QAAQ,EAAEkF,IAAI,EAAE,GAAGrK,IAAI,CAAC,CAAC,CAAC,kBAAkBgL,UAAU,EAAE,EAAE,EAAE,CAAC;cAC3E3H,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAAC7C,EAAE,CAAC3G,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;;WAEjH,MAAM;YACH0G,EAAE,CAAC4B,oBAAoB,CAACiB,KAAK,CAACkE,YAAY,CAAC;;;MAIvD,CAAC;MAAA,iBAAAc,EAAA;QAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;MAAA;IAAA,KAAC,IAAI,EAAC,MAAI;MACP,IAAI,CAACnG,oBAAoB,CAACG,OAAO,EAAE;IACvC,CAAC,CAAC;EACN;EAWAiG,gBAAgBA,CAAA;IACZ,IAAI,CAAC1J,qBAAqB,CAAC0J,gBAAgB,EAAE;EACjD;EAEAC,UAAUA,CAAC9C,KAAK;IACZ,IAAItF,IAAI,GAAGsF,KAAK,CAACG,MAAM,CAAC4C,KAAK,CAAC,CAAC,CAAC;IAChC,IAAI,CAACC,UAAU,GAAGtI,IAAI;IACtB,IAAG,IAAI,CAACsI,UAAU,IAAI,IAAI,EAAC;MACvB,IAAI,CAAC9H,eAAe,GAAG,IAAI,CAAChH,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC7E,IAAI,CAAC8O,UAAU,EAAE;MACjB;;IAEJ,IAAIC,QAAQ,GAAGxI,IAAI,CAAClD,IAAI;IACxB,IAAI2L,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC3I,IAAI,CAAC0G,IAAI,GAAC,IAAI,CAAC;IACzC,IAAIkC,MAAM,GAAG,IAAI;IACjB,IAAGH,QAAQ,GAAC,IAAI,GAAG,CAAC,EAAC;MACjBA,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAC,IAAI,CAAC;MACpCG,MAAM,GAAG,IAAI;;IAEjB,IAAI,CAACpI,eAAe,GAAG,GAAGgI,QAAQ,IAAI,IAAI,CAACjL,WAAW,CAACsL,qBAAqB,CAACJ,QAAQ,CAAC,IAAIG,MAAM,GAAG;IACnG,IAAI,CAACL,UAAU,EAAE;EACrB;EAGAxK,SAASA,CAAA;IACL,IAAI,CAACsC,UAAU,CAACL,IAAI,GAAG,IAAI;IAC3B,IAAI,CAACQ,eAAe,GAAG,IAAI,CAAChH,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;IAC7E,IAAI,CAAC6O,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,EAAE;EACrB;EAEAA,UAAUA,CAAA;IACN,IAAI,CAACO,OAAO,GAAG,IAAI;IACnB,IAAG,IAAI,CAACR,UAAU,EAAC;MACf,IAAG,IAAI,CAACnK,OAAO,CAAClB,IAAI,EAAC;QACjB,IAAI8L,SAAS,GAAG,IAAI,CAACT,UAAU,CAACxL,IAAI,CAAC4K,SAAS,CAAC,IAAI,CAACY,UAAU,CAACxL,IAAI,CAACkM,WAAW,CAAC,GAAG,CAAC,GAAC,CAAC,EAAE,IAAI,CAACV,UAAU,CAACxL,IAAI,CAACQ,MAAM,CAAC;QACpH,IAAG,CAAC,IAAI,CAACa,OAAO,CAAClB,IAAI,CAACgM,QAAQ,CAACF,SAAS,CAAC,EAAC;UACtC,IAAI,CAACD,OAAO,GAAG,aAAa;;;MAGpC,IAAG,IAAI,CAAC3K,OAAO,CAACuC,OAAO,IAAI,IAAI,CAACoI,OAAO,IAAI,IAAI,EAAC;QAC5C,IAAII,WAAW,GAAG,IAAI,CAAC/K,OAAO,CAACuC,OAAO;QACtC,IAAG,IAAI,CAACvC,OAAO,CAACwC,IAAI,IAAI,IAAI,EAAC;UACzBuI,WAAW,GAAGA,WAAW,GAAG,IAAI;SACnC,MAAK,IAAG,IAAI,CAAC/K,OAAO,CAACwC,IAAI,IAAI,IAAI,EAAC;UAC/BuI,WAAW,GAAGA,WAAW,GAAG,IAAI,GAAG,IAAI;SAC1C,MAAK,IAAG,IAAI,CAAC/K,OAAO,CAACwC,IAAI,IAAI,IAAI,EAAC;UAC/BuI,WAAW,GAAGA,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;;QAElD,IAAG,IAAI,CAACZ,UAAU,CAAC5B,IAAI,GAAGwC,WAAW,EAAC;UAClC,IAAI,CAACJ,OAAO,GAAG,SAAS;;;KAGnC,MAAI;MACD,IAAG,IAAI,CAAC3K,OAAO,CAACU,QAAQ,EAAC;QACrB,IAAI,CAACiK,OAAO,GAAG,UAAU;;;EAGrC;EAEAK,KAAKA,CAAA;IACD,IAAI,CAAC9I,UAAU,CAACL,IAAI,GAAG,IAAI;IAC3B,IAAI,CAACsI,UAAU,GAAG,IAAI;IACtB,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC3K,OAAO,CAAC6C,QAAQ,GAAG,KAAK;EACjC;EAEAoI,MAAMA,CAAA;IACF,IAAI,CAACjL,OAAO,CAAC0C,YAAY,CAAC,IAAI,CAACyH,UAAU,CAAC;EAC9C;;;uBA1fSjK,6BAA6B,EAAApF,EAAA,CAAAoQ,iBAAA,CAAApQ,EAAA,CAAAqQ,QAAA,GAAArQ,EAAA,CAAAoQ,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAAvQ,EAAA,CAAAoQ,iBAAA,CAAAI,EAAA,CAAAC,qBAAA,GAAAzQ,EAAA,CAAAoQ,iBAAA,CAkC1B3Q,oBAAoB,GAAAO,EAAA,CAAAoQ,iBAAA,CACpB1Q,sBAAsB;IAAA;EAAA;;;YAnCzB0F,6BAA6B;MAAAsL,SAAA;MAAAC,QAAA,GAAA3Q,EAAA,CAAA4Q,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB1ClR,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChGH,EAAA,CAAA4B,SAAA,sBAAoF;UACxF5B,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA6C;UAMrCD,EAAA,CAAAqC,UAAA,mBAAA+O,+DAAA;YAAA,OAASD,GAAA,CAAAtG,aAAA,EAAe;UAAA,EAAC;UAE7B7K,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,gBAAmM;UAAhDD,EAAA,CAAAqC,UAAA,mBAAAgP,+DAAA;YAAA,OAASF,GAAA,CAAArG,UAAA,EAAY;UAAA,EAAC;UAA0B9K,EAAA,CAAAG,YAAA,EAAS;UAGpNH,EAAA,CAAAC,cAAA,cAK6B;UAFzBD,EAAA,CAAAqC,UAAA,oBAAAiP,8DAAA;YAAA,OAAUH,GAAA,CAAA7H,UAAA,EAAY;UAAA,EAAC,2BAAAiI,qEAAAhP,MAAA;YAAA,OACNA,MAAA,CAAAiP,cAAA,EAAuB;UAAA,EADjB;UAIvBxR,EAAA,CAAAC,cAAA,aAAkB;UAImED,EAAA,CAAAE,MAAA,IAAoD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5JH,EAAA,CAAAC,cAAA,iBAAuK;UAAhKD,EAAA,CAAAqC,UAAA,kBAAAoP,8DAAA;YAAA,OAAQN,GAAA,CAAAhE,UAAA,EAAY;UAAA,EAAC;UAA5BnN,EAAA,CAAAG,YAAA,EAAuK;UAE3KH,EAAA,CAAAC,cAAA,eAA+C;UAC3CD,EAAA,CAAAmB,UAAA,KAAAuQ,6CAAA,kBAEM;UACN1R,EAAA,CAAAmB,UAAA,KAAAwQ,6CAAA,kBAEM;UACN3R,EAAA,CAAAmB,UAAA,KAAAyQ,6CAAA,kBAEM;UACN5R,EAAA,CAAAmB,UAAA,KAAA0Q,6CAAA,kBAEM;UACV7R,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAgD;UACyBD,EAAA,CAAAE,MAAA,IAAqD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7JH,EAAA,CAAAC,cAAA,iBAAuL;UAAhLD,EAAA,CAAAqC,UAAA,kBAAAyP,8DAAA;YAAA,OAAQX,GAAA,CAAAnE,UAAA,EAAY;UAAA,EAAC;UAA5BhN,EAAA,CAAAG,YAAA,EAAuL;UAE3LH,EAAA,CAAAC,cAAA,eAA+C;UAC3CD,EAAA,CAAAmB,UAAA,KAAA4Q,6CAAA,kBAEM;UACN/R,EAAA,CAAAmB,UAAA,KAAA6Q,6CAAA,kBAEM;UACNhS,EAAA,CAAAmB,UAAA,KAAA8Q,6CAAA,kBAEM;UACVjS,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,eAA8B;UAEOD,EAAA,CAAAE,MAAA,IAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAA4B,SAAA,oBAOY;UAChB5B,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,eAAkD;UAC9CD,EAAA,CAAAmB,UAAA,KAAA+Q,6CAAA,kBAEM;UACVlS,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA0E;UACtED,EAAA,CAAA4B,SAAA,oBAGY;UAEhB5B,EAAA,CAAAG,YAAA,EAAM;UAIdH,EAAA,CAAAC,cAAA,cAAkB;UACmHD,EAAA,CAAAqC,UAAA,2BAAA8P,0EAAA5P,MAAA;YAAA,OAAA4O,GAAA,CAAA5K,kBAAA,GAAAhE,MAAA;UAAA,EAAgC;UAC7JvC,EAAA,CAAAC,cAAA,eAA8D;UAI9CD,EAAA,CAAAqC,UAAA,yBAAA+P,2EAAA7P,MAAA;YAAA,OAAA4O,GAAA,CAAAhL,kBAAA,GAAA5D,MAAA;UAAA,EAA8B,sBAAA8P,wEAAA;YAAA,OAClBlB,GAAA,CAAAlH,aAAA,EAAe;UAAA,EADG,0BAAAqI,4EAAA;YAAA,OAEdnB,GAAA,CAAAnG,QAAA,CAAAmG,GAAA,CAAAhL,kBAAA,CAA4B;UAAA,EAFd;UAWjCnG,EAAA,CAAAG,YAAA,EAAc;UAEnBH,EAAA,CAAAC,cAAA,iBAA2L;UAAlDD,EAAA,CAAAqC,UAAA,mBAAAkQ,gEAAA;YAAA,OAASpB,GAAA,CAAAtF,mBAAA,CAAAsF,GAAA,CAAAhL,kBAAA,CAAuC;UAAA,EAAC;UAACnG,EAAA,CAAAG,YAAA,EAAS;UAG5MH,EAAA,CAAAC,cAAA,eAAgF;UAC5ED,EAAA,CAAAmB,UAAA,KAAAqR,6CAAA,kBAEM;UACVxS,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,mBAAqE;UACjED,EAAA,CAAAmB,UAAA,KAAAsR,qDAAA,0BAOc;UACdzS,EAAA,CAAAmB,UAAA,KAAAuR,qDAAA,2BAuBc;UAClB1S,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAmB,UAAA,KAAAwR,6CAAA,kBAMM;UACN3S,EAAA,CAAAC,cAAA,eAA0E;UAC4ED,EAAA,CAAAqC,UAAA,mBAAAuQ,gEAAA;YAAA,OAASzB,GAAA,CAAApG,YAAA,EAAc;UAAA,EAAC;UAAC/K,EAAA,CAAAG,YAAA,EAAS;UACpLH,EAAA,CAAA4B,SAAA,oBAAgN;UACpN5B,EAAA,CAAAG,YAAA,EAAM;UAkBlBH,EAAA,CAAAC,cAAA,oBAAyU;UAAlLD,EAAA,CAAAqC,UAAA,2BAAAwQ,0EAAAtQ,MAAA;YAAA,OAAA4O,GAAA,CAAA3K,mBAAA,GAAAjE,MAAA;UAAA,EAAiC,oBAAAuQ,mEAAA;YAAA,OAAwI3B,GAAA,CAAAjB,KAAA,EAAO;UAAA,EAA/I;UACpLlQ,EAAA,CAAAC,cAAA,eAA+B;UAOPD,EAAA,CAAAqC,UAAA,2BAAA0Q,uEAAAxQ,MAAA;YAAA,OAAA4O,GAAA,CAAA/J,UAAA,CAAAL,IAAA,GAAAxE,MAAA;UAAA,EAA6B,oBAAAyQ,gEAAAzQ,MAAA;YAAA,OAGnB4O,GAAA,CAAAhC,UAAA,CAAA5M,MAAA,CAAkB;UAAA,EAHC;UAFjCvC,EAAA,CAAAG,YAAA,EAOE;UAENH,EAAA,CAAAC,cAAA,eAA0O;UAElOD,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAmB,UAAA,KAAA8R,6CAAA,kBAEM;UACVjT,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,WAAK;UACDD,EAAA,CAAAmB,UAAA,KAAA+R,+CAAA,oBAAqH;UACrHlT,EAAA,CAAAmB,UAAA,KAAAgS,+CAAA,oBAAkI;UAClInT,EAAA,CAAAmB,UAAA,KAAAiS,+CAAA,oBAAuL;UAC3LpT,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,eAAwE;UAC+ED,EAAA,CAAAqC,UAAA,mBAAAgR,kEAAA;YAAA,OAASlC,GAAA,CAAAjC,gBAAA,EAAkB;UAAA,EAAC;UAAClP,EAAA,CAAAG,YAAA,EAAW;UAInMH,EAAA,CAAAC,cAAA,eAA+C;UAC6GD,EAAA,CAAAqC,UAAA,mBAAAiR,gEAAA;YAAA,OAASnC,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UAACnQ,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnOH,EAAA,CAAAC,cAAA,kBAAkH;UAAlGD,EAAA,CAAAqC,UAAA,mBAAAkR,gEAAA;YAAA,OAAApC,GAAA,CAAA3K,mBAAA,GAA+B,KAAK;UAAA,EAAC;UAA6DxG,EAAA,CAAAE,MAAA,IAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAvNxIH,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAe,iBAAA,CAAAoQ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,8BAAsD;UACnDR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA/I,KAAA,CAAe,SAAA+I,GAAA,CAAA5I,IAAA;UAIlDvI,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAqB,UAAA,aAAA8P,GAAA,CAAA5P,eAAA,CAAAsO,OAAA,IAAAsB,GAAA,CAAAhK,gBAAA,CAAwD,UAAAgK,GAAA,CAAA5Q,WAAA,CAAAC,SAAA;UAO5CR,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAqB,UAAA,aAAA8P,GAAA,CAAA5P,eAAA,CAAAsO,OAAA,IAAAsB,GAAA,CAAAhK,gBAAA,CAAwD,UAAAgK,GAAA,CAAA5Q,WAAA,CAAAC,SAAA;UAK5ER,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAqB,UAAA,cAAA8P,GAAA,CAAA5P,eAAA,CAA6B;UASwDvB,EAAA,CAAAI,SAAA,GAAoD;UAApDJ,EAAA,CAAAe,iBAAA,CAAAoQ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,4BAAoD;UAC3BR,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAqB,UAAA,gBAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,kCAAsE;UAG9JR,EAAA,CAAAI,SAAA,GAAgH;UAAhHJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,+BAAArC,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,cAAAC,KAAA,KAAAtC,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,cAAAE,QAAA,aAAgH;UAGhH1T,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAA7N,SAAA,CAAAlE,MAAA,kBAAA0P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAA7N,SAAA,CAAAlE,MAAA,cAA8D;UAG9DzB,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAA7N,SAAA,CAAAlE,MAAA,kBAAA0P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAA7N,SAAA,CAAAlE,MAAA,YAA4D;UAG5DzB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAAhK,gBAAA,CAAsB;UAKyCnH,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAe,iBAAA,CAAAoQ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,6BAAqD;UACZR,EAAA,CAAAI,SAAA,GAAuE;UAAvEJ,EAAA,CAAAqB,UAAA,gBAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,mCAAuE;UAG/KR,EAAA,CAAAI,SAAA,GAA+K;UAA/KJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,+BAAArC,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,cAAAC,KAAA,MAAAtC,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,cAAAE,QAAA,iBAAAvC,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAAzN,SAAA,CAAAtE,MAAA,kBAAA0P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAAzN,SAAA,CAAAtE,MAAA,iBAA+K;UAG/KzB,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAAzN,SAAA,CAAAtE,MAAA,kBAAA0P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAAzN,SAAA,CAAAtE,MAAA,cAA8D;UAG9DzB,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAAzN,SAAA,CAAAtE,MAAA,kBAAA0P,GAAA,CAAA5P,eAAA,CAAAiS,QAAA,CAAAzN,SAAA,CAAAtE,MAAA,YAA4D;UAOrCzB,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAe,iBAAA,CAAAoQ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,+BAAuD;UAIhFR,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAqB,UAAA,qBAAoB,gBAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA;UAQtBR,EAAA,CAAAI,SAAA,GAA4F;UAA5FJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA5P,eAAA,CAAAC,GAAA,gBAAAqO,OAAA,IAAAsB,GAAA,CAAA5P,eAAA,CAAAC,GAAA,gBAAAiS,KAAA,CAA4F;UAM1EzT,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,gCAA8D;UAIjDR,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,8BAA4D,aAAA2Q,GAAA,CAAA5P,eAAA,CAAAsO,OAAA,IAAAsB,GAAA,CAAAhK,gBAAA;UAMwEnH,EAAA,CAAAI,SAAA,GAAqF;UAArFJ,EAAA,CAAA2T,UAAA,CAAA3T,EAAA,CAAAiB,eAAA,KAAA2S,GAAA,EAAqF;UAA5P5T,EAAA,CAAAqB,UAAA,iBAAArB,EAAA,CAAAiB,eAAA,KAAA4S,GAAA,EAAuC,WAAA1C,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,4CAAA2Q,GAAA,CAAA5K,kBAAA;UAK7BvG,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAAhL,kBAAA,CAA8B,kFAAAgL,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,8CAAA2Q,GAAA,CAAAjI,mBAAA,CAAApB,IAAA,CAAAqJ,GAAA;UAa9BnR,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAqB,UAAA,aAAA8P,GAAA,CAAA/K,aAAA,KAAA+K,GAAA,CAAA7K,YAAA,CAA2C,UAAA6K,GAAA,CAAA5Q,WAAA,CAAAC,SAAA;UAIjDR,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA7K,YAAA,CAAmB;UAIpBtG,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA1I,SAAA,CAAmB,eAAAzI,EAAA,CAAAiB,eAAA,KAAA6S,GAAA;UAkCtB9T,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA1I,SAAA,CAAApE,MAAA,MAA2B;UAQSrE,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,gCAA8D;UAC/DR,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAqB,UAAA,UAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,8BAA4D,aAAA2Q,GAAA,CAAA5P,eAAA,CAAAsO,OAAA,IAAAsB,GAAA,CAAA1I,SAAA,CAAApE,MAAA,UAAA8M,GAAA,CAAA1E,gBAAA;UAmBuFzM,EAAA,CAAAI,SAAA,GAAoE;UAApEJ,EAAA,CAAA2T,UAAA,CAAA3T,EAAA,CAAAiB,eAAA,KAAA8S,GAAA,EAAoE;UAAlQ/T,EAAA,CAAAqB,UAAA,iBAAArB,EAAA,CAAAiB,eAAA,KAAA4S,GAAA,EAAuC,WAAA1C,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,8CAAA2Q,GAAA,CAAA3K,mBAAA;UAINxG,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAA2T,UAAA,CAAA3T,EAAA,CAAAgU,eAAA,KAAAC,GAAA,EAAA9C,GAAA,CAAAjM,OAAA,CAAAyC,kBAAA,mBAA8D;UAM7E3H,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAkU,UAAA,CAAA/C,GAAA,CAAAjM,OAAA,CAAA6C,QAAA,yBAA8C;UAF9C/H,EAAA,CAAAqB,UAAA,YAAA8P,GAAA,CAAA/J,UAAA,CAAAL,IAAA,CAA6B,aAAAoK,GAAA,CAAAjM,OAAA,CAAA6C,QAAA;UAOhC/H,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAkU,UAAA,CAAA/C,GAAA,CAAAjM,OAAA,CAAA6C,QAAA,4BAAiD;UAE9C/H,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAK,kBAAA,MAAA8Q,GAAA,CAAA9B,UAAA,GAAA8B,GAAA,CAAA5J,eAAA,GAAA4J,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,kCACJ;UAEER,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAA9B,UAAA,aAAA8B,GAAA,CAAAjM,OAAA,CAAA6C,QAAA,CAA6C;UAM1B/H,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAAtB,OAAA,eAA0B;UAC1B7P,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAAtB,OAAA,cAAyB;UACzB7P,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAqB,UAAA,SAAA8P,GAAA,CAAAtB,OAAA,kBAA6B;UAI9B7P,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAqB,UAAA,aAAA8P,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,+BAAgE;UAK5FR,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAqB,UAAA,aAAA8P,GAAA,CAAAtB,OAAA,IAAAsB,GAAA,CAAA9B,UAAA,YAAA8B,GAAA,CAAAjM,OAAA,CAAA6C,QAAA,CAA8D,aAAAoJ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA;UAAqGR,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAe,iBAAA,CAAAoQ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,uBAA+C;UACxGR,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAe,iBAAA,CAAAoQ,GAAA,CAAA5Q,WAAA,CAAAC,SAAA,yBAAiD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}