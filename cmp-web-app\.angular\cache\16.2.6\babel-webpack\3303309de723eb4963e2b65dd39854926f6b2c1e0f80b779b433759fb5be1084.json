{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class SimService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/sim-mgmt\";\n  }\n  demo(url, params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(url, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {\n      timeout: 180000\n    }, params, callback, errorCallBack, finallyCallback);\n  }\n  quickSearch(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/quickSearch`, {\n      timeout: 900000\n    }, params, callback, errorCallBack, finallyCallback);\n  }\n  searchNotInGroup(params, callback) {\n    this.httpService.get(`${this.prefixApi}/get-sim-not-in-group`, {}, params, callback);\n  }\n  getById(msisdn, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  getDetailPlanSim(msisdn, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/detail-plan/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  getByKey(key, value, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/getByKey`, {}, {\n      key,\n      value\n    }, callback, errorCallback, finallyCallback);\n  }\n  getDetailContract(contractCode, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/detail-contract/${contractCode}`, {\n      timeout: 120000\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  getDetailStatus(msisdn, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/detail-status/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  pushSimToGroup(sims, group, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/add-sim-to-group`, {}, {\n      sims,\n      group\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  removeSIMFromGroup(msisdnList, groupId, callback) {\n    this.httpService.post(`/group-sim/remove-sim-group`, {}, {\n      msisdnList,\n      groupId\n    }, {}, callback);\n  }\n  exportSim(params) {\n    this.httpService.download(`${this.prefixApi}/export-sim`, {\n      timeout: 900000\n    }, params);\n  }\n  exportSimSelected(data) {\n    this.httpService.downloadPost(`${this.prefixApi}/export-sim`, {}, data, {});\n  }\n  exportExels(params) {\n    this.httpService.download(`${this.prefixApi}/export-sim-excel`, {\n      timeout: 600000\n    }, params);\n  }\n  exportExelSelected(data) {\n    this.httpService.downloadPost(`${this.prefixApi}/export-sim-excel`, {\n      timeout: 600000\n    }, data, {});\n  }\n  getSimByContractCode(params, callback) {\n    this.httpService.get(this.prefixApi + \"/get-by-contract\", {\n      timeout: 180000\n    }, params, callback);\n  }\n  getListByCustomerCode(query, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/get-by-customer-code`, {}, query, callback, errorCallBack, finallyCallback);\n  }\n  deleteListSim(ids, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/delete-sim`, {}, {\n      ids\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  // get connection status\n  getConnectionStatus(body, callback, errorCallBack, finallyCallback) {\n    this.httpService.post('/media/getStatus', {}, body, {}, callback, errorCallBack, finallyCallback);\n  }\n  // get data used\n  getDataUsed(body, callback, errorCallBack, finallyCallback) {\n    this.httpService.postNoError(`${this.prefixApi}/get-data-used`, {}, body, {}, callback, errorCallBack, finallyCallback);\n  }\n  getSimInfoForLog(list, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/get-sim-info-for-log`, {}, list, {}, callback);\n  }\n  loadDropdown(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/dropdown`, {\n      timeout: 180000\n    }, params, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function SimService_Factory(t) {\n      return new (t || SimService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SimService,\n      factory: SimService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "SimService", "constructor", "httpService", "prefixApi", "demo", "url", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "search", "timeout", "quickSearch", "searchNotInGroup", "getById", "msisdn", "getDetailPlanSim", "get<PERSON><PERSON><PERSON><PERSON>", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "getDetailContract", "contractCode", "getDetailStatus", "pushSimToGroup", "sims", "group", "post", "removeSIMFromGroup", "msisdnList", "groupId", "exportSim", "download", "exportSimSelected", "data", "downloadPost", "exportExels", "exportExelSelected", "getSimByContractCode", "getListByCustomerCode", "query", "deleteListSim", "ids", "getConnectionStatus", "body", "getDataUsed", "postNoError", "getSimInfoForLog", "list", "loadDropdown", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\sim\\SimService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class SimService{\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/sim-mgmt\";\r\n    }\r\n\r\n    public demo(url:string, params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(url,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public search(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/search`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public quickSearch(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/quickSearch`,{timeout: 900000}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public searchNotInGroup(params:{[key:string]:any}, callback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/get-sim-not-in-group`,{}, params,callback);\r\n    }\r\n\r\n    public getById(msisdn: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/${msisdn}`,{},{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getDetailPlanSim(msisdn: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/detail-plan/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public getDetailContract(contractCode: string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/detail-contract/${contractCode}`, {timeout: 120000}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getDetailStatus(msisdn: string, callback,errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/detail-status/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public pushSimToGroup(sims: Array<number>, group:{id: number|null, groupKey?:string,name?:string,customer?:string,description?:string|null}, callback:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}/add-sim-to-group`,{},{sims, group}, {},callback,errorCallBack,finallyCallback);\r\n    }\r\n\r\n    public removeSIMFromGroup(msisdnList: Array<string>, groupId:number,callback:Function){\r\n        this.httpService.post(`/group-sim/remove-sim-group`,{},{msisdnList, groupId},{},callback)\r\n    }\r\n\r\n    public exportSim(params: any){\r\n        this.httpService.download(`${this.prefixApi}/export-sim`, {timeout: 900000},params);\r\n    }\r\n\r\n    public exportSimSelected(data: any){\r\n        this.httpService.downloadPost(`${this.prefixApi}/export-sim`, {},data,{});\r\n    }\r\n\r\n    public exportExels(params: any){\r\n        this.httpService.download(`${this.prefixApi}/export-sim-excel`, {timeout: 600000},params);\r\n    }\r\n\r\n    public exportExelSelected(data: any){\r\n        this.httpService.downloadPost(`${this.prefixApi}/export-sim-excel`, {timeout: 600000},data,{});\r\n    }\r\n\r\n    public getSimByContractCode(params:any, callback){\r\n        this.httpService.get(this.prefixApi+\"/get-by-contract\",{timeout: 180000},params, callback)\r\n    }\r\n\r\n    public getListByCustomerCode(query:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/get-by-customer-code`,{}, query,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public deleteListSim(ids:Array<String>, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}/delete-sim`,{},{ids},{},callback, errorCallBack, finallyCallback);\r\n    }\r\n    // get connection status\r\n    public getConnectionStatus(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.post('/media/getStatus',{}, body,{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    // get data used\r\n    public getDataUsed(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.postNoError(`${this.prefixApi}/get-data-used`,{}, body,{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getSimInfoForLog(list: Array<any>, callback:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}/get-sim-info-for-log`,{},list,{},callback)\r\n    }\r\n\r\n    public loadDropdown(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/dropdown`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,UAAU;EAEnBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,WAAW;EAChC;EAEOC,IAAIA,CAACC,GAAU,EAAEC,MAA8B,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC1H,IAAI,CAACP,WAAW,CAACQ,GAAG,CAACL,GAAG,EAAC,EAAE,EAAEC,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACjF;EAEOE,MAAMA,CAACL,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC3G,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,SAAS,EAAC;MAACS,OAAO,EAAE;IAAM,CAAC,EAAEN,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACvH;EAEOI,WAAWA,CAACP,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAChH,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,cAAc,EAAC;MAACS,OAAO,EAAE;IAAM,CAAC,EAAEN,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC5H;EAEOK,gBAAgBA,CAACR,MAAyB,EAAEC,QAAkB;IACjE,IAAI,CAACL,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,uBAAuB,EAAC,EAAE,EAAEG,MAAM,EAACC,QAAQ,CAAC;EACtF;EAEOQ,OAAOA,CAACC,MAAc,EAAET,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACjG,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,IAAIa,MAAM,EAAE,EAAC,EAAE,EAAC,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACvG;EAEOQ,gBAAgBA,CAACD,MAAc,EAAET,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC1G,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,gBAAgBa,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrH;EAEOS,QAAQA,CAACC,GAAW,EAAEC,KAAa,EAAEb,QAAmB,EAAEc,aAAuB,EAAEZ,eAA0B;IAChH,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,WAAW,EAAC,EAAE,EAAE;MAACgB,GAAG;MAAEC;IAAK,CAAC,EAAEb,QAAQ,EAAEc,aAAa,EAAEZ,eAAe,CAAC;EACjH;EAEOa,iBAAiBA,CAACC,YAAoB,EAAEhB,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACjH,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,oBAAoBoB,YAAY,EAAE,EAAE;MAACX,OAAO,EAAE;IAAM,CAAC,EAAE,EAAE,EAAEL,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC9I;EAEOe,eAAeA,CAACR,MAAc,EAAET,QAAQ,EAACC,aAAuB,EAAEC,eAAyB;IAC9F,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,kBAAkBa,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACvH;EAEOgB,cAAcA,CAACC,IAAmB,EAAEC,KAAgG,EAAEpB,QAAiB,EAAEC,aAAuB,EAAEC,eAAyB;IAC9M,IAAI,CAACP,WAAW,CAAC0B,IAAI,CAAC,GAAG,IAAI,CAACzB,SAAS,mBAAmB,EAAC,EAAE,EAAC;MAACuB,IAAI;MAAEC;IAAK,CAAC,EAAE,EAAE,EAACpB,QAAQ,EAACC,aAAa,EAACC,eAAe,CAAC;EAC3H;EAEOoB,kBAAkBA,CAACC,UAAyB,EAAEC,OAAc,EAACxB,QAAiB;IACjF,IAAI,CAACL,WAAW,CAAC0B,IAAI,CAAC,6BAA6B,EAAC,EAAE,EAAC;MAACE,UAAU;MAAEC;IAAO,CAAC,EAAC,EAAE,EAACxB,QAAQ,CAAC;EAC7F;EAEOyB,SAASA,CAAC1B,MAAW;IACxB,IAAI,CAACJ,WAAW,CAAC+B,QAAQ,CAAC,GAAG,IAAI,CAAC9B,SAAS,aAAa,EAAE;MAACS,OAAO,EAAE;IAAM,CAAC,EAACN,MAAM,CAAC;EACvF;EAEO4B,iBAAiBA,CAACC,IAAS;IAC9B,IAAI,CAACjC,WAAW,CAACkC,YAAY,CAAC,GAAG,IAAI,CAACjC,SAAS,aAAa,EAAE,EAAE,EAACgC,IAAI,EAAC,EAAE,CAAC;EAC7E;EAEOE,WAAWA,CAAC/B,MAAW;IAC1B,IAAI,CAACJ,WAAW,CAAC+B,QAAQ,CAAC,GAAG,IAAI,CAAC9B,SAAS,mBAAmB,EAAE;MAACS,OAAO,EAAE;IAAM,CAAC,EAACN,MAAM,CAAC;EAC7F;EAEOgC,kBAAkBA,CAACH,IAAS;IAC/B,IAAI,CAACjC,WAAW,CAACkC,YAAY,CAAC,GAAG,IAAI,CAACjC,SAAS,mBAAmB,EAAE;MAACS,OAAO,EAAE;IAAM,CAAC,EAACuB,IAAI,EAAC,EAAE,CAAC;EAClG;EAEOI,oBAAoBA,CAACjC,MAAU,EAAEC,QAAQ;IAC5C,IAAI,CAACL,WAAW,CAACQ,GAAG,CAAC,IAAI,CAACP,SAAS,GAAC,kBAAkB,EAAC;MAACS,OAAO,EAAE;IAAM,CAAC,EAACN,MAAM,EAAEC,QAAQ,CAAC;EAC9F;EAEOiC,qBAAqBA,CAACC,KAAwB,EAAElC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACzH,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,uBAAuB,EAAC,EAAE,EAAEsC,KAAK,EAAClC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrH;EAEOiC,aAAaA,CAACC,GAAiB,EAAEpC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC1G,IAAI,CAACP,WAAW,CAAC0B,IAAI,CAAC,GAAG,IAAI,CAACzB,SAAS,aAAa,EAAC,EAAE,EAAC;MAACwC;IAAG,CAAC,EAAC,EAAE,EAACpC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC9G;EACA;EACOmC,mBAAmBA,CAACC,IAAI,EAAEtC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACnG,IAAI,CAACP,WAAW,CAAC0B,IAAI,CAAC,kBAAkB,EAAC,EAAE,EAAEiB,IAAI,EAAC,EAAE,EAAEtC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACnG;EAEA;EACOqC,WAAWA,CAACD,IAAI,EAAEtC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC3F,IAAI,CAACP,WAAW,CAAC6C,WAAW,CAAC,GAAG,IAAI,CAAC5C,SAAS,gBAAgB,EAAC,EAAE,EAAE0C,IAAI,EAAC,EAAE,EAAEtC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzH;EAEOuC,gBAAgBA,CAACC,IAAgB,EAAE1C,QAAiB,EAAEC,aAAuB,EAAEC,eAAyB;IAC3G,IAAI,CAACP,WAAW,CAAC0B,IAAI,CAAC,GAAG,IAAI,CAACzB,SAAS,uBAAuB,EAAC,EAAE,EAAC8C,IAAI,EAAC,EAAE,EAAC1C,QAAQ,CAAC;EACvF;EAEO2C,YAAYA,CAAC5C,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACjH,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,WAAW,EAAC;MAACS,OAAO,EAAE;IAAM,CAAC,EAAEN,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzH;;;uBA7FST,UAAU,EAAAmD,EAAA,CAAAC,QAAA,CAECrD,WAAW;IAAA;EAAA;;;aAFtBC,UAAU;MAAAqD,OAAA,EAAVrD,UAAU,CAAAsD;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}