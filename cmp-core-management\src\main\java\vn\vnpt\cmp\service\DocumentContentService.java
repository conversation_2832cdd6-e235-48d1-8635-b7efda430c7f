package vn.vnpt.cmp.service;

import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vnpt.cmp.base.constants.Constants;
import vn.vnpt.cmp.base.constants.ResponseCode;
import vn.vnpt.cmp.base.event.Event;
import vn.vnpt.cmp.base.jpa.dto.docs.DocumentContent;
import vn.vnpt.cmp.base.jpa.dto.docs.Project;
import vn.vnpt.cmp.base.jpa.entity.DocumentEntity;
import vn.vnpt.cmp.base.jpa.entity.ProjectEntity;
import vn.vnpt.cmp.repository.DocumentContentRepository;
import vn.vnpt.cmp.repository.ProjectRepository;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DocumentContentService {
    private static final Logger logger = LoggerFactory.getLogger(DocumentContentService.class);
    @Value("${folder.storage}")
    private String folderStorage;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private DocumentContentRepository documentContentRepository;

    private String getKeyProject(Event event){
        Long userId = event.userId;
        Integer userType = event.userType;
        String keyProject = null;
        try {
            Map<String, Object> param = (Map<String, Object>) event.payload;
            keyProject = (String) param.get("keyProject");
        } catch (Exception exception) {
            logger.error(exception.getMessage(), exception);
        }
        if (!Strings.isNullOrEmpty(keyProject)){
            return keyProject;
        }
        if(userId.equals(1l)){
//            super admin cung lay admin
            keyProject = "cmp_admin";
        }else if(userType.equals(Constants.UserType.ADMIN)){
            keyProject = "cmp_admin";
        }else if(userType.equals(Constants.UserType.SUPERVISOR)){
            keyProject = "cmp_province";
        }else if(userType.equals(Constants.UserType.MANAGER)){
            keyProject = "cmp_teller";
        }else if(userType.equals(Constants.UserType.CUSTOMER)){
            keyProject = "cmp_customer";
        }else if(userType.equals(Constants.UserType.AGENCY)){
            keyProject = "cmp_agency";
        }
        return keyProject;
    }

    public Event getProjectInfo(Event event) {
        String keyProject = getKeyProject(event);
        try {
            Project project = new Project(projectRepository.findFirstByKey(keyProject));
            return event.createResponse(project, ResponseCode.OK, null);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
    }

    public Event getListPage(Event event) {
        String keyProject = getKeyProject(event);
        try {
            ProjectEntity project = projectRepository.findFirstByKey(keyProject);
            List<DocumentEntity> resultSearch = documentContentRepository.findByProjectId(project.getId());
            List<DocumentContent> documentContents = resultSearch.stream().map(n -> new DocumentContent(n)).collect(Collectors.toList());
            return event.createResponse(documentContents, ResponseCode.OK, null);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
    }

    private void getContentForPage(DocumentContent documentContent, String lang) {
        try {
            if(lang == null){
                lang = documentContent.getTitle().keySet().iterator().next();
            }
            if(documentContent.getContent() != null && documentContent.getContent().containsKey(lang)){
                String fileName = documentContent.getContent().get(lang);
                if(!Strings.isNullOrEmpty(fileName)){
                    documentContent.setContentHtml(new HashMap<>());
                    documentContent.getContentHtml().put(lang, readFile(fileName));
                }
            }
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
    }

    public String readFile(String fileName){
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(new File(String.format("%s/%s", folderStorage, fileName))));
            String value = "";
            String text = reader.readLine();
            do {
                value+=text;
                text = reader.readLine();
            }while (text != null);
            return value;
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }finally {
            if(reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public Event getPageInfo(Event event) {
        String keyProject = getKeyProject(event);
        try {
            ProjectEntity project = projectRepository.findFirstByKey(keyProject);
            Map<String,Object> params = (Map<String, Object>) event.payload;
            String path = params.get("path").toString();
            String lang = params.get("lang").toString();
            DocumentEntity documentEntity = documentContentRepository.findFirstByProjectIdAndPath(project.getId(), path);
            if(documentEntity != null){
                DocumentContent documentContent = new DocumentContent(documentEntity);
                getContentForPage(documentContent, lang);
                return event.createResponse(documentContent, ResponseCode.OK, null);
            }
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
    }
}
