{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class ConfigChartService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/dashboard\";\n    // this.prefixApi = \"/config-chart\";\n  }\n\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {\n      timeout: 120000\n    }, params, callback, errorCallBack, finallyCallback);\n  }\n  detail(chartId, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${chartId}`, {\n      timeout: 120000\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  delete(chartId, callback, errorCallBack, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/${chartId}`, {\n      timeout: 120000\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  create(data, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}`, {}, data, {\n      timeout: 120000\n    }, callback, errorCallBack, finallyCallback);\n  }\n  update(data, callback, errorCallBack, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/${data.id}`, {\n      timeout: 120000\n    }, data, {}, callback, errorCallBack, finallyCallback);\n  }\n  checkExists(name, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/checkName`, {\n      timeout: 120000\n    }, {\n      name\n    }, callback, errorCallBack, finallyCallback);\n  }\n  getAll(callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/getAll`, {\n      timeout: 120000\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  getContent(data, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/getContent`, {\n      timeout: 300000\n    }, data, {}, callback, errorCallBack, finallyCallback);\n  }\n  getListConfig(userId, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(\"/threshold-config/get-list-config/\" + userId, {\n      timeout: 120000\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  saveConfigDashboard(data, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(\"/threshold-config/create-config\", {\n      timeout: 120000\n    }, data, {}, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function ConfigChartService_Factory(t) {\n      return new (t || ConfigChartService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ConfigChartService,\n      factory: ConfigChartService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "ConfigChartService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "timeout", "detail", "chartId", "delete", "create", "data", "post", "update", "put", "id", "checkExists", "name", "getAll", "get<PERSON>ontent", "getListConfig", "userId", "saveConfigDashboard", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\charts\\ConfigChartService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\nimport { callback } from \"chart.js/types/helpers\";\r\n@Injectable()\r\nexport class ConfigChartService {\r\n    private prefixApi: string;\r\n\r\n    constructor(@Inject(HttpService) private httpService: HttpService) {\r\n        this.prefixApi = \"/dashboard\";\r\n        // this.prefixApi = \"/config-chart\";\r\n    }\r\n\r\n    public search(params: {\r\n        [key: string]: any\r\n    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {\r\n        this.httpService.get(`${this.prefixApi}/search`, {timeout: 120000}, params, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public detail(chartId: number|string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/${chartId}`,{timeout: 120000},{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public delete(chartId: number|string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.delete(`${this.prefixApi}/${chartId}`,{timeout: 120000},{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public create(data: any, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}`, {}, data, {timeout: 120000}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public update(data: any, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/${data.id}`, {timeout: 120000}, data, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public checkExists(name: string, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/checkName`, {timeout: 120000}, {name}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getAll(callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/getAll`, {timeout: 120000}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getContent(data, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/getContent`, {timeout: 300000}, data, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getListConfig(userId, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.get(\"/threshold-config/get-list-config/\"+userId, {timeout: 120000}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public saveConfigDashboard(data, callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.post(\"/threshold-config/create-config\", {timeout: 120000}, data, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,kBAAkB;EAG3BC,YAAyCC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,YAAY;IAC7B;EACJ;;EAEOC,MAAMA,CAACC,MAEb,EAAEC,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACxE,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAE;MAACO,OAAO,EAAE;IAAM,CAAC,EAAEL,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzH;EACOG,MAAMA,CAACC,OAAsB,EAAEN,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACxG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIS,OAAO,EAAE,EAAC;MAACF,OAAO,EAAE;IAAM,CAAC,EAAC,EAAE,EAAEJ,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACvH;EAEOK,MAAMA,CAACD,OAAsB,EAAEN,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACxG,IAAI,CAACN,WAAW,CAACW,MAAM,CAAC,GAAG,IAAI,CAACV,SAAS,IAAIS,OAAO,EAAE,EAAC;MAACF,OAAO,EAAE;IAAM,CAAC,EAAC,EAAE,EAAEJ,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1H;EAEOM,MAAMA,CAACC,IAAS,EAAET,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IAC9F,IAAI,CAACN,WAAW,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,SAAS,EAAE,EAAE,EAAE,EAAEY,IAAI,EAAE;MAACL,OAAO,EAAE;IAAM,CAAC,EAAEJ,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrH;EAEOS,MAAMA,CAACF,IAAS,EAAET,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IAC9F,IAAI,CAACN,WAAW,CAACgB,GAAG,CAAC,GAAG,IAAI,CAACf,SAAS,IAAIY,IAAI,CAACI,EAAE,EAAE,EAAE;MAACT,OAAO,EAAE;IAAM,CAAC,EAAEK,IAAI,EAAE,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC/H;EAEOY,WAAWA,CAACC,IAAY,EAAEf,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACtG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,YAAY,EAAE;MAACO,OAAO,EAAE;IAAM,CAAC,EAAE;MAACW;IAAI,CAAC,EAAEf,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC5H;EAEOc,MAAMA,CAAChB,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACnF,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAE;MAACO,OAAO,EAAE;IAAM,CAAC,EAAE,EAAE,EAAEJ,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrH;EAEOe,UAAUA,CAACR,IAAI,EAAET,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IAC7F,IAAI,CAACN,WAAW,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,SAAS,aAAa,EAAE;MAACO,OAAO,EAAE;IAAM,CAAC,EAAEK,IAAI,EAAE,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAChI;EAEOgB,aAAaA,CAACC,MAAM,EAAEnB,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IAClG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,oCAAoC,GAACgB,MAAM,EAAE;MAACf,OAAO,EAAE;IAAM,CAAC,EAAE,EAAE,EAAEJ,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACtI;EAEOkB,mBAAmBA,CAACX,IAAI,EAAET,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACtG,IAAI,CAACN,WAAW,CAACc,IAAI,CAAC,iCAAiC,EAAE;MAACN,OAAO,EAAE;IAAM,CAAC,EAAEK,IAAI,EAAE,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACnI;;;uBA/CSR,kBAAkB,EAAA2B,EAAA,CAAAC,QAAA,CAGP7B,WAAW;IAAA;EAAA;;;aAHtBC,kBAAkB;MAAA6B,OAAA,EAAlB7B,kBAAkB,CAAA8B;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}