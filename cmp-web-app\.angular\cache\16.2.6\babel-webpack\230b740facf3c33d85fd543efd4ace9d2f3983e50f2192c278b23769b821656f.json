{"ast": null, "code": "export default {\n  label: {\n    isdn: \"ISDN\",\n    customerName: \"Customer Name\",\n    activeDate: \"Active Date\",\n    expiredDate: \"Expired Date\",\n    time: \"Time\",\n    ratingPlan: \"Rating plan\",\n    actionType: \"Action Type\",\n    status: \"Status\",\n    modifyBy: \"Modify by\",\n    fromDate: \"From Date\",\n    toDate: \"To Date\"\n  },\n  status: {\n    success: \"Successful\",\n    unsuccessful: \"Unsuccessful\"\n  },\n  actionType: {\n    assignPlan: \"Assign Plan\",\n    switchPlan: \"Change Plan\"\n  }\n};", "map": {"version": 3, "names": ["label", "isdn", "customerName", "activeDate", "expiredDate", "time", "ratingPlan", "actionType", "status", "modifyBy", "fromDate", "toDate", "success", "unsuccessful", "assignPlan", "switchPlan"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\history-register-plan.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        isdn: \"ISDN\",\r\n        customerName: \"Customer Name\",\r\n        activeDate: \"Active Date\",\r\n        expiredDate: \"Expired Date\",\r\n        time: \"Time\",\r\n        ratingPlan: \"Rating plan\",\r\n        actionType: \"Action Type\",\r\n        status: \"Status\",\r\n        modifyBy: \"Modify by\",\r\n        fromDate: \"From Date\",\r\n        toDate: \"To Date\",\r\n    },\r\n    status: {\r\n        success: \"Successful\",\r\n        unsuccessful: \"Unsuccessful\",\r\n    },\r\n    actionType: {\r\n        assignPlan: \"Assign Plan\",\r\n        switchPlan: \"Change Plan\"\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,eAAe;IAC7BC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,aAAa;IACzBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE;GACX;EACDH,MAAM,EAAE;IACJI,OAAO,EAAE,YAAY;IACrBC,YAAY,EAAE;GACjB;EACDN,UAAU,EAAE;IACRO,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE;;CAGnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}