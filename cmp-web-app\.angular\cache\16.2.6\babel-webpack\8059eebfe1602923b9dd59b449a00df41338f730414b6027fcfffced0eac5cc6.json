{"ast": null, "code": "import { ObservableService } from \"src/app/service/comon/observable.service\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"src/app/service/comon/translate.service\";\nimport * as i3 from \"primeng/confirmdialog\";\nimport * as i4 from \"primeng/toast\";\nimport * as i5 from \"primeng/blockui\";\nimport * as i6 from \"primeng/progressspinner\";\nimport * as i7 from \"src/app/service/comon/observable.service\";\nfunction MessageCommonComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-progressSpinner\");\n  }\n}\nconst _c0 = function () {\n  return {\n    width: \"50vw\"\n  };\n};\nexport class MessageCommonComponent {\n  constructor(observableService, confirmationService, messageService, cd, tranService) {\n    this.observableService = observableService;\n    this.confirmationService = confirmationService;\n    this.messageService = messageService;\n    this.cd = cd;\n    this.tranService = tranService;\n    this.blockedDocument = false;\n    observableService.init(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON);\n    this.subscriptionMessage = observableService.subscribe(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON, {\n      next: this.handleNext.bind(this),\n      error: this.handleError.bind(this),\n      complete: this.handleComplete.bind(this)\n    });\n  }\n  ngOnInit() {}\n  showConfirm(data) {\n    this.confirmationService.confirm({\n      message: data.message,\n      header: data.title,\n      icon: 'pi pi-exclamation-triangle',\n      acceptLabel: this.tranService.translate(\"global.button.yes\"),\n      rejectLabel: this.tranService.translate(\"global.button.no\"),\n      acceptButtonStyleClass: \"p-button-info\",\n      rejectButtonStyleClass: \"p-button-secondary\",\n      accept: () => {\n        if (data.action.ok) {\n          data.action.ok();\n        }\n      },\n      reject: type => {\n        if (data.action.cancel) {\n          data.action.cancel();\n        }\n      }\n    });\n  }\n  showInfo(data) {\n    this.messageService.add({\n      severity: 'info',\n      summary: data.summary || 'Info',\n      detail: data.message,\n      life: data.timeout || 4000\n    });\n  }\n  showError(data) {\n    this.messageService.add({\n      severity: 'error',\n      summary: data.summary || 'Error',\n      detail: data.message,\n      life: data.timeout || 4000\n    });\n  }\n  showWarning(data) {\n    this.messageService.add({\n      severity: 'warn',\n      summary: data.summary || 'Warning',\n      detail: data.message,\n      life: data.timeout || 4000\n    });\n  }\n  showSuccess(data) {\n    this.messageService.add({\n      severity: 'success',\n      summary: data.summary || 'Success',\n      detail: data.message,\n      life: data.timeout || 4000\n    });\n  }\n  showLoad(data) {\n    this.blockedDocument = true;\n  }\n  hiddenLoad(data) {\n    this.blockedDocument = false;\n    this.cd.markForCheck();\n  }\n  handleNext(data) {\n    if (data.type == \"confirm\") {\n      this.showConfirm(data);\n    } else if (data.type == \"info\") {\n      this.showInfo(data);\n    } else if (data.type == \"error\") {\n      this.showError(data);\n    } else if (data.type == \"warning\") {\n      this.showWarning(data);\n    } else if (data.type == \"load\") {\n      if (data.isShow) {\n        this.showLoad(data);\n      } else {\n        this.hiddenLoad(data);\n      }\n    } else if (data.type == \"success\") {\n      this.showSuccess(data);\n    }\n  }\n  handleError(error) {\n    console.log(\"handle error: \", error);\n  }\n  handleComplete() {\n    console.log(\"complete\");\n  }\n  ngOnDestroy() {\n    this.subscriptionMessage.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MessageCommonComponent_Factory(t) {\n      return new (t || MessageCommonComponent)(i0.ɵɵdirectiveInject(ObservableService), i0.ɵɵdirectiveInject(i1.ConfirmationService), i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.TranslateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageCommonComponent,\n      selectors: [[\"message-common\"]],\n      features: [i0.ɵɵProvidersFeature([ConfirmationService, MessageService])],\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"flex\", \"justify-content-center\", \"gap-2\", \"surface-ground\", \"border-none\", \"absolute\", \"z-10000\"], [3, \"blocked\"], [\"pTemplate\", \"content\"]],\n      template: function MessageCommonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"p-toast\")(2, \"p-confirmDialog\");\n          i0.ɵɵelementStart(3, \"p-blockUI\", 1);\n          i0.ɵɵtemplate(4, MessageCommonComponent_ng_template_4_Template, 1, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(3, _c0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"blocked\", ctx.blockedDocument);\n        }\n      },\n      dependencies: [i1.PrimeTemplate, i3.ConfirmDialog, i4.Toast, i5.BlockUI, i6.ProgressSpinner],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ObservableService", "CONSTANTS", "ConfirmationService", "MessageService", "i0", "ɵɵelement", "MessageCommonComponent", "constructor", "observableService", "confirmationService", "messageService", "cd", "tranService", "blockedDocument", "init", "OBSERVABLE", "KEY_MESSAGE_COMMON", "subscriptionMessage", "subscribe", "next", "handleNext", "bind", "error", "handleError", "complete", "handleComplete", "ngOnInit", "showConfirm", "data", "confirm", "message", "header", "title", "icon", "acceptLabel", "translate", "<PERSON><PERSON><PERSON><PERSON>", "acceptButtonStyleClass", "rejectButtonStyleClass", "accept", "action", "ok", "reject", "type", "cancel", "showInfo", "add", "severity", "summary", "detail", "life", "timeout", "showError", "showWarning", "showSuccess", "showLoad", "hiddenLoad", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isShow", "console", "log", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "ChangeDetectorRef", "i2", "TranslateService", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "MessageCommonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "MessageCommonComponent_ng_template_4_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\message-common\\message-common.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\message-common\\message-common.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ChangeDetectorRef  } from \"@angular/core\"\r\nimport { ObservableService } from \"src/app/service/comon/observable.service\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport {  Subscription } from \"rxjs\";\r\nimport { ConfirmationService, MessageService, ConfirmEventType } from 'primeng/api';\r\nimport { TranslateService } from \"src/app/service/comon/translate.service\";\r\n@Component({\r\n    selector:\"message-common\",\r\n    templateUrl: \"./message-common.component.html\",\r\n    providers: [ConfirmationService, MessageService]\r\n})\r\nexport class MessageCommonComponent implements OnInit, OnDestroy {\r\n    subscriptionMessage: Subscription;\r\n    blockedDocument: boolean = false;\r\n    constructor(@Inject(ObservableService) private observableService: ObservableService,\r\n                    private confirmationService: ConfirmationService, private messageService: MessageService,\r\n                    private cd: ChangeDetectorRef,\r\n                    private tranService: TranslateService) {\r\n        observableService.init(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON);\r\n        this.subscriptionMessage = observableService.subscribe(CONSTANTS.OBSERVABLE.KEY_MESSAGE_COMMON, {\r\n            next: this.handleNext.bind(this),\r\n            error: this.handleError.bind(this),\r\n            complete: this.handleComplete.bind(this)\r\n        })\r\n    }\r\n    ngOnInit(): void {\r\n\r\n    }\r\n\r\n    showConfirm(data){\r\n        this.confirmationService.confirm({\r\n            message: data.message,\r\n            header: data.title,\r\n            icon: 'pi pi-exclamation-triangle',\r\n            acceptLabel: this.tranService.translate(\"global.button.yes\"),\r\n            rejectLabel: this.tranService.translate(\"global.button.no\"),\r\n            acceptButtonStyleClass: \"p-button-info\",\r\n            rejectButtonStyleClass: \"p-button-secondary\",\r\n            accept: () => {\r\n                if(data.action.ok){\r\n                    data.action.ok();\r\n                }\r\n            },\r\n            reject: (type) => {\r\n                if(data.action.cancel){\r\n                    data.action.cancel();\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    showInfo(data){\r\n        this.messageService.add({ severity: 'info', summary: data.summary || 'Info', detail: data.message, life: data.timeout || 4000 });\r\n    }\r\n\r\n    showError(data){\r\n        this.messageService.add({ severity: 'error', summary: data.summary || 'Error', detail: data.message, life: data.timeout || 4000 });\r\n    }\r\n\r\n    showWarning(data){\r\n        this.messageService.add({ severity: 'warn', summary: data.summary || 'Warning', detail: data.message, life: data.timeout || 4000 });\r\n    }\r\n\r\n    showSuccess(data){\r\n        this.messageService.add({ severity: 'success', summary: data.summary || 'Success', detail: data.message, life: data.timeout || 4000 });\r\n    }\r\n\r\n    showLoad(data){\r\n        this.blockedDocument = true;\r\n    }\r\n\r\n    hiddenLoad(data){\r\n        this.blockedDocument = false;\r\n        this.cd.markForCheck();\r\n    }\r\n\r\n    handleNext(data:{message?:string, action?:{ok?:Function, cancel?:Function}, type: \"confirm\"|\"info\"|\"error\"|\"warning\"|\"load\"|\"success\", isShow?: boolean}){\r\n        if(data.type == \"confirm\"){\r\n            this.showConfirm(data);\r\n        }else if(data.type == \"info\"){\r\n            this.showInfo(data);\r\n        }else if(data.type == \"error\"){\r\n            this.showError(data);\r\n        }else if(data.type == \"warning\"){\r\n            this.showWarning(data);\r\n        }else if(data.type == \"load\"){\r\n            if(data.isShow){\r\n                this.showLoad(data);\r\n            }else{\r\n                this.hiddenLoad(data);\r\n            }\r\n        }else if(data.type == \"success\"){\r\n            this.showSuccess(data);\r\n        }\r\n    }\r\n\r\n    handleError(error){\r\n        console.log(\"handle error: \",error);\r\n    }\r\n\r\n    handleComplete(){\r\n        console.log(\"complete\");\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        this.subscriptionMessage.unsubscribe();\r\n    }\r\n}\r\n", "<div class=\"flex justify-content-center gap-2 surface-ground border-none absolute z-10000\">\r\n    <p-toast></p-toast>\r\n    <p-confirmDialog [style]=\"{width: '50vw'}\"></p-confirmDialog>\r\n    <p-blockUI [blocked]=\"blockedDocument\">\r\n        <ng-template pTemplate=\"content\">\r\n            <p-progressSpinner></p-progressSpinner>\r\n        </ng-template>\r\n    </p-blockUI>\r\n</div>\r\n"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,SAASC,mBAAmB,EAAEC,cAAc,QAA0B,aAAa;;;;;;;;;;;ICCvEC,EAAA,CAAAC,SAAA,wBAAuC;;;;;;;;ADMnD,OAAM,MAAOC,sBAAsB;EAG/BC,YAA+CC,iBAAoC,EAC3DC,mBAAwC,EAAUC,cAA8B,EAChFC,EAAqB,EACrBC,WAA6B;IAHN,KAAAJ,iBAAiB,GAAjBA,iBAAiB;IACxC,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAA+B,KAAAC,cAAc,GAAdA,cAAc;IAChE,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IAJnC,KAAAC,eAAe,GAAY,KAAK;IAK5BL,iBAAiB,CAACM,IAAI,CAACb,SAAS,CAACc,UAAU,CAACC,kBAAkB,CAAC;IAC/D,IAAI,CAACC,mBAAmB,GAAGT,iBAAiB,CAACU,SAAS,CAACjB,SAAS,CAACc,UAAU,CAACC,kBAAkB,EAAE;MAC5FG,IAAI,EAAE,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;MAChCC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACF,IAAI,CAAC,IAAI,CAAC;MAClCG,QAAQ,EAAE,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC,IAAI;KAC1C,CAAC;EACN;EACAK,QAAQA,CAAA,GAER;EAEAC,WAAWA,CAACC,IAAI;IACZ,IAAI,CAACnB,mBAAmB,CAACoB,OAAO,CAAC;MAC7BC,OAAO,EAAEF,IAAI,CAACE,OAAO;MACrBC,MAAM,EAAEH,IAAI,CAACI,KAAK;MAClBC,IAAI,EAAE,4BAA4B;MAClCC,WAAW,EAAE,IAAI,CAACtB,WAAW,CAACuB,SAAS,CAAC,mBAAmB,CAAC;MAC5DC,WAAW,EAAE,IAAI,CAACxB,WAAW,CAACuB,SAAS,CAAC,kBAAkB,CAAC;MAC3DE,sBAAsB,EAAE,eAAe;MACvCC,sBAAsB,EAAE,oBAAoB;MAC5CC,MAAM,EAAEA,CAAA,KAAK;QACT,IAAGX,IAAI,CAACY,MAAM,CAACC,EAAE,EAAC;UACdb,IAAI,CAACY,MAAM,CAACC,EAAE,EAAE;;MAExB,CAAC;MACDC,MAAM,EAAGC,IAAI,IAAI;QACb,IAAGf,IAAI,CAACY,MAAM,CAACI,MAAM,EAAC;UAClBhB,IAAI,CAACY,MAAM,CAACI,MAAM,EAAE;;MAE5B;KACH,CAAC;EACN;EAEAC,QAAQA,CAACjB,IAAI;IACT,IAAI,CAAClB,cAAc,CAACoC,GAAG,CAAC;MAAEC,QAAQ,EAAE,MAAM;MAAEC,OAAO,EAAEpB,IAAI,CAACoB,OAAO,IAAI,MAAM;MAAEC,MAAM,EAAErB,IAAI,CAACE,OAAO;MAAEoB,IAAI,EAAEtB,IAAI,CAACuB,OAAO,IAAI;IAAI,CAAE,CAAC;EACpI;EAEAC,SAASA,CAACxB,IAAI;IACV,IAAI,CAAClB,cAAc,CAACoC,GAAG,CAAC;MAAEC,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAEpB,IAAI,CAACoB,OAAO,IAAI,OAAO;MAAEC,MAAM,EAAErB,IAAI,CAACE,OAAO;MAAEoB,IAAI,EAAEtB,IAAI,CAACuB,OAAO,IAAI;IAAI,CAAE,CAAC;EACtI;EAEAE,WAAWA,CAACzB,IAAI;IACZ,IAAI,CAAClB,cAAc,CAACoC,GAAG,CAAC;MAAEC,QAAQ,EAAE,MAAM;MAAEC,OAAO,EAAEpB,IAAI,CAACoB,OAAO,IAAI,SAAS;MAAEC,MAAM,EAAErB,IAAI,CAACE,OAAO;MAAEoB,IAAI,EAAEtB,IAAI,CAACuB,OAAO,IAAI;IAAI,CAAE,CAAC;EACvI;EAEAG,WAAWA,CAAC1B,IAAI;IACZ,IAAI,CAAClB,cAAc,CAACoC,GAAG,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEC,OAAO,EAAEpB,IAAI,CAACoB,OAAO,IAAI,SAAS;MAAEC,MAAM,EAAErB,IAAI,CAACE,OAAO;MAAEoB,IAAI,EAAEtB,IAAI,CAACuB,OAAO,IAAI;IAAI,CAAE,CAAC;EAC1I;EAEAI,QAAQA,CAAC3B,IAAI;IACT,IAAI,CAACf,eAAe,GAAG,IAAI;EAC/B;EAEA2C,UAAUA,CAAC5B,IAAI;IACX,IAAI,CAACf,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACF,EAAE,CAAC8C,YAAY,EAAE;EAC1B;EAEArC,UAAUA,CAACQ,IAA6I;IACpJ,IAAGA,IAAI,CAACe,IAAI,IAAI,SAAS,EAAC;MACtB,IAAI,CAAChB,WAAW,CAACC,IAAI,CAAC;KACzB,MAAK,IAAGA,IAAI,CAACe,IAAI,IAAI,MAAM,EAAC;MACzB,IAAI,CAACE,QAAQ,CAACjB,IAAI,CAAC;KACtB,MAAK,IAAGA,IAAI,CAACe,IAAI,IAAI,OAAO,EAAC;MAC1B,IAAI,CAACS,SAAS,CAACxB,IAAI,CAAC;KACvB,MAAK,IAAGA,IAAI,CAACe,IAAI,IAAI,SAAS,EAAC;MAC5B,IAAI,CAACU,WAAW,CAACzB,IAAI,CAAC;KACzB,MAAK,IAAGA,IAAI,CAACe,IAAI,IAAI,MAAM,EAAC;MACzB,IAAGf,IAAI,CAAC8B,MAAM,EAAC;QACX,IAAI,CAACH,QAAQ,CAAC3B,IAAI,CAAC;OACtB,MAAI;QACD,IAAI,CAAC4B,UAAU,CAAC5B,IAAI,CAAC;;KAE5B,MAAK,IAAGA,IAAI,CAACe,IAAI,IAAI,SAAS,EAAC;MAC5B,IAAI,CAACW,WAAW,CAAC1B,IAAI,CAAC;;EAE9B;EAEAL,WAAWA,CAACD,KAAK;IACbqC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAACtC,KAAK,CAAC;EACvC;EAEAG,cAAcA,CAAA;IACVkC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;EAC3B;EAEAC,WAAWA,CAAA;IACP,IAAI,CAAC5C,mBAAmB,CAAC6C,WAAW,EAAE;EAC1C;;;uBA/FSxD,sBAAsB,EAAAF,EAAA,CAAA2D,iBAAA,CAGX/D,iBAAiB,GAAAI,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAA9D,mBAAA,GAAAE,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAA7D,cAAA,GAAAC,EAAA,CAAA2D,iBAAA,CAAA3D,EAAA,CAAA6D,iBAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAH5B7D,sBAAsB;MAAA8D,SAAA;MAAAC,QAAA,GAAAjE,EAAA,CAAAkE,kBAAA,CAFpB,CAACpE,mBAAmB,EAAEC,cAAc,CAAC;MAAAoE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTpDxE,EAAA,CAAA0E,cAAA,aAA2F;UACvF1E,EAAA,CAAAC,SAAA,cAAmB;UAEnBD,EAAA,CAAA0E,cAAA,mBAAuC;UACnC1E,EAAA,CAAA2E,UAAA,IAAAC,6CAAA,yBAEc;UAClB5E,EAAA,CAAA6E,YAAA,EAAY;;;UALK7E,EAAA,CAAA8E,SAAA,GAAyB;UAAzB9E,EAAA,CAAA+E,UAAA,CAAA/E,EAAA,CAAAgF,eAAA,IAAAC,GAAA,EAAyB;UAC/BjF,EAAA,CAAA8E,SAAA,GAA2B;UAA3B9E,EAAA,CAAAkF,UAAA,YAAAT,GAAA,CAAAhE,eAAA,CAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}