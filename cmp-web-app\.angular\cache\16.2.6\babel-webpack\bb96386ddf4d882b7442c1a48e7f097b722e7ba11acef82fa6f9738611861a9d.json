{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter, booleanAttribute, forwardRef } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/inputtext\";\nimport * as i3 from \"primeng/autofocus\";\nfunction InputOtp_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"input\", 2);\n    i0.ɵɵlistener(\"input\", function InputOtp_ng_container_0_ng_container_1_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const i_r1 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onInput($event, i_r1 - 1));\n    })(\"focus\", function InputOtp_ng_container_0_ng_container_1_Template_input_focus_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onInputFocus($event));\n    })(\"blur\", function InputOtp_ng_container_0_ng_container_1_Template_input_blur_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onInputBlur($event));\n    })(\"paste\", function InputOtp_ng_container_0_ng_container_1_Template_input_paste_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onPaste($event));\n    })(\"keydown\", function InputOtp_ng_container_0_ng_container_1_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onKeyDown($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r2.getModelValue(i_r1))(\"maxLength\", 1)(\"type\", ctx_r2.inputType)(\"inputmode\", ctx_r2.inputMode)(\"variant\", ctx_r2.variant)(\"readonly\", ctx_r2.readonly)(\"disabled\", ctx_r2.disabled)(\"invalid\", ctx_r2.invalid)(\"tabindex\", ctx_r2.tabindex)(\"unstyled\", ctx_r2.unstyled)(\"autofocus\", ctx_r2.getAutofocus(i_r1));\n  }\n}\nfunction InputOtp_ng_container_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    $implicit: a0,\n    events: a1,\n    index: a2\n  };\n};\nfunction InputOtp_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputOtp_ng_container_0_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.inputTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c0, ctx_r3.getToken(i_r1 - 1), ctx_r3.getTemplateEvents(i_r1 - 1), i_r1));\n  }\n}\nfunction InputOtp_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputOtp_ng_container_0_ng_container_1_Template, 2, 11, \"ng-container\", 1);\n    i0.ɵɵtemplate(2, InputOtp_ng_container_0_ng_container_2_Template, 2, 6, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.inputTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.inputTemplate);\n  }\n}\nexport const INPUT_OTP_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputOtp),\n  multi: true\n};\n/**\n * Input Otp is used to enter one time passwords.\n * @group Components\n */\nexport class InputOtp {\n  get inputMode() {\n    return this.integerOnly ? 'numeric' : 'text';\n  }\n  get inputType() {\n    return this.mask ? 'password' : 'text';\n  }\n  constructor(cd) {\n    this.cd = cd;\n    /**\n     * When present, it specifies that the component should have invalid state style.\n     * @group Props\n     */\n    this.invalid = false;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    this.disabled = false;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    this.readonly = false;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    this.variant = null;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    this.tabindex = null;\n    /**\n     * Number of characters to initiate.\n     * @group Props\n     */\n    this.length = 4;\n    /**\n     * Mask pattern.\n     * @group Props\n     */\n    this.mask = false;\n    /**\n     * When present, it specifies that an input field is integer-only.\n     * @group Props\n     */\n    this.integerOnly = false;\n    /**\n     * Callback to invoke on value change.\n     * @group Emits\n     */\n    this.onChange = new EventEmitter();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    this.onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    this.onBlur = new EventEmitter();\n    this.tokens = [];\n    this.onModelChange = () => {};\n    this.onModelTouched = () => {};\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'input':\n          this.inputTemplate = item.template;\n          break;\n        default:\n          this.inputTemplate = item.template;\n          break;\n      }\n    });\n  }\n  getToken(index) {\n    return this.tokens[index];\n  }\n  getTemplateEvents(index) {\n    return {\n      input: event => this.onInput(event, index),\n      keydown: event => this.onKeyDown(event),\n      focus: event => this.onFocus.emit(event),\n      blur: event => this.onBlur.emit(event),\n      paste: event => this.onPaste(event)\n    };\n  }\n  onInput(event, index) {\n    this.tokens[index] = event.target.value;\n    this.updateModel(event);\n    if (event.inputType === 'deleteContentBackward') {\n      this.moveToPrev(event);\n    } else if (event.inputType === 'insertText' || event.inputType === 'deleteContentForward') {\n      this.moveToNext(event);\n    }\n  }\n  updateModel(event) {\n    const newValue = this.tokens.join('');\n    this.onModelChange(newValue);\n    this.onChange.emit({\n      originalEvent: event,\n      value: newValue\n    });\n  }\n  writeValue(value) {\n    if (value) {\n      if (Array.isArray(value) && value.length > 0) {\n        this.value = value.slice(0, this.length);\n      } else {\n        this.value = value.toString().split('').slice(0, this.length);\n      }\n    } else {\n      this.value = value;\n    }\n    this.updateTokens();\n    this.cd.markForCheck();\n  }\n  updateTokens() {\n    if (this.value !== null && this.value !== undefined) {\n      if (Array.isArray(this.value)) {\n        this.tokens = [...this.value];\n      } else {\n        this.tokens = this.value.toString().split('');\n      }\n    } else {\n      this.tokens = [];\n    }\n  }\n  getModelValue(i) {\n    return this.tokens[i - 1] || '';\n  }\n  getAutofocus(i) {\n    if (i === 1) {\n      return this.autofocus;\n    }\n    return false;\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  moveToPrev(event) {\n    let prevInput = this.findPrevInput(event.target);\n    if (prevInput) {\n      prevInput.focus();\n      prevInput.select();\n    }\n  }\n  moveToNext(event) {\n    let nextInput = this.findNextInput(event.target);\n    if (nextInput) {\n      nextInput.focus();\n      nextInput.select();\n    }\n  }\n  findNextInput(element) {\n    let nextElement = element.nextElementSibling;\n    if (!nextElement) return;\n    return nextElement.nodeName === 'INPUT' ? nextElement : this.findNextInput(nextElement);\n  }\n  findPrevInput(element) {\n    let prevElement = element.previousElementSibling;\n    if (!prevElement) return;\n    return prevElement.nodeName === 'INPUT' ? prevElement : this.findPrevInput(prevElement);\n  }\n  onInputFocus(event) {\n    event.target.select();\n    this.onFocus.emit(event);\n  }\n  onInputBlur(event) {\n    this.onBlur.emit(event);\n  }\n  onKeyDown(event) {\n    switch (event.code) {\n      case 'ArrowLeft':\n        this.moveToPrev(event);\n        event.preventDefault();\n        break;\n      case 'ArrowUp':\n      case 'ArrowDown':\n        event.preventDefault();\n        break;\n      case 'Backspace':\n        if (event.target.value.length === 0) {\n          this.moveToPrev(event);\n          event.preventDefault();\n        }\n        break;\n      case 'ArrowRight':\n        this.moveToNext(event);\n        event.preventDefault();\n        break;\n      default:\n        if (this.integerOnly && !((event.code.startsWith('Digit') || event.code.startsWith('Numpad')) && Number(event.key) >= 0 && Number(event.key) <= 9) || this.tokens.join('').length >= this.length && event.code !== 'Delete') {\n          event.preventDefault();\n        }\n        break;\n    }\n  }\n  onPaste(event) {\n    let paste = event.clipboardData.getData('text');\n    if (paste.length) {\n      let pastedCode = paste.substring(0, this.length + 1);\n      if (!this.integerOnly || !isNaN(pastedCode)) {\n        this.tokens = pastedCode.split('');\n        this.updateModel(event);\n      }\n    }\n    event.preventDefault();\n  }\n  getRange(n) {\n    return Array.from({\n      length: n\n    }, (_, index) => index + 1);\n  }\n  trackByFn(index) {\n    return index;\n  }\n  static {\n    this.ɵfac = function InputOtp_Factory(t) {\n      return new (t || InputOtp)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InputOtp,\n      selectors: [[\"p-inputOtp\"]],\n      contentQueries: function InputOtp_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-inputotp\", \"p-component\"],\n      inputs: {\n        invalid: \"invalid\",\n        disabled: \"disabled\",\n        readonly: \"readonly\",\n        variant: \"variant\",\n        tabindex: \"tabindex\",\n        length: \"length\",\n        mask: \"mask\",\n        integerOnly: \"integerOnly\",\n        autofocus: [\"autofocus\", \"autofocus\", booleanAttribute]\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      features: [i0.ɵɵProvidersFeature([INPUT_OTP_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature],\n      decls: 1,\n      vars: 2,\n      consts: [[4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"type\", \"text\", \"pInputText\", \"\", \"pAutoFocus\", \"\", 1, \"p-inputotp-input\", 3, \"value\", \"maxLength\", \"type\", \"inputmode\", \"variant\", \"readonly\", \"disabled\", \"invalid\", \"tabindex\", \"unstyled\", \"autofocus\", \"input\", \"focus\", \"blur\", \"paste\", \"keydown\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function InputOtp_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, InputOtp_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.getRange(ctx.length))(\"ngForTrackBy\", ctx.trackByFn);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i2.InputText, i3.AutoFocus],\n      styles: [\".p-inputotp {\\n    display: flex;\\n    align-items: center;\\n    gap: 0.5rem;\\n  }\\n\\n.p-inputotp-input {\\n    text-align: center;\\n    width: 2.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImlucHV0b3RwLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLGFBQWE7SUFDYixtQkFBbUI7SUFDbkIsV0FBVztFQUNiOztBQUVGO0lBQ0ksa0JBQWtCO0lBQ2xCLGFBQWE7QUFDakIiLCJmaWxlIjoiaW5wdXRvdHAuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLnAtaW5wdXRvdHAge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBnYXA6IDAuNXJlbTtcclxuICB9XHJcblxyXG4ucC1pbnB1dG90cC1pbnB1dCB7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICB3aWR0aDogMi41cmVtO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdGVtcGxhdGUvY29tbW9uLW1vZHVsZS9uZy1wcmltZS9pbnB1dG90cC9pbnB1dG90cC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxhQUFhO0lBQ2IsbUJBQW1CO0lBQ25CLFdBQVc7RUFDYjs7QUFFRjtJQUNJLGtCQUFrQjtJQUNsQixhQUFhO0FBQ2pCO0FBQ0EsZ2hCQUFnaEIiLCJzb3VyY2VzQ29udGVudCI6WyIucC1pbnB1dG90cCB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGdhcDogMC41cmVtO1xyXG4gIH1cclxuXHJcbi5wLWlucHV0b3RwLWlucHV0IHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIHdpZHRoOiAyLjVyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\nexport class InputOtpModule {\n  static {\n    this.ɵfac = function InputOtpModule_Factory(t) {\n      return new (t || InputOtpModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: InputOtpModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, InputTextModule, AutoFocusModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(InputOtpModule, {\n    declarations: [InputOtp],\n    imports: [CommonModule, SharedModule, InputTextModule, AutoFocusModule],\n    exports: [InputOtp, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "booleanAttribute", "forwardRef", "PrimeTemplate", "SharedModule", "InputTextModule", "NG_VALUE_ACCESSOR", "AutoFocusModule", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "InputOtp_ng_container_0_ng_container_1_Template_input_input_1_listener", "$event", "ɵɵrestoreView", "_r6", "i_r1", "ɵɵnextContext", "$implicit", "ctx_r4", "ɵɵresetView", "onInput", "InputOtp_ng_container_0_ng_container_1_Template_input_focus_1_listener", "ctx_r7", "onInputFocus", "InputOtp_ng_container_0_ng_container_1_Template_input_blur_1_listener", "ctx_r8", "onInputBlur", "InputOtp_ng_container_0_ng_container_1_Template_input_paste_1_listener", "ctx_r9", "onPaste", "InputOtp_ng_container_0_ng_container_1_Template_input_keydown_1_listener", "ctx_r10", "onKeyDown", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r2", "getModelValue", "inputType", "inputMode", "variant", "readonly", "disabled", "invalid", "tabindex", "unstyled", "getAutofocus", "ɵɵelementContainer", "ɵɵtemplate", "InputOtp_ng_container_0_ng_container_2_ng_container_1_Template", "ctx_r3", "inputTemplate", "ɵɵpureFunction3", "_c0", "getToken", "getTemplateEvents", "InputOtp_ng_container_0_ng_container_1_Template", "InputOtp_ng_container_0_ng_container_2_Template", "ctx_r0", "INPUT_OTP_VALUE_ACCESSOR", "provide", "useExisting", "InputOtp", "multi", "integerOnly", "mask", "constructor", "cd", "length", "onChange", "onFocus", "onBlur", "tokens", "onModelChange", "onModelTouched", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "template", "index", "input", "event", "keydown", "focus", "emit", "blur", "paste", "target", "value", "updateModel", "moveToPrev", "moveToNext", "newValue", "join", "originalEvent", "writeValue", "Array", "isArray", "slice", "toString", "split", "updateTokens", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "i", "autofocus", "registerOnChange", "fn", "registerOnTouched", "prevInput", "findPrevInput", "select", "nextInput", "findNextInput", "element", "nextElement", "nextElement<PERSON><PERSON>ling", "nodeName", "prevElement", "previousElementSibling", "code", "preventDefault", "startsWith", "Number", "key", "clipboardData", "getData", "pastedCode", "substring", "isNaN", "getRange", "n", "from", "_", "trackByFn", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "contentQueries", "InputOtp_ContentQueries", "rf", "ctx", "dirIndex", "outputs", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "decls", "vars", "consts", "InputOtp_Template", "InputOtp_ng_container_0_Template", "InputOtpModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\ng-prime\\inputotp\\inputotp.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation, booleanAttribute, forwardRef } from '@angular/core';\r\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { Nullable } from 'primeng/ts-helpers';\r\nimport { AutoFocusModule } from 'primeng/autofocus';\r\nimport { InputOtpChangeEvent } from './inputotp.interface';\r\n\r\nexport const INPUT_OTP_VALUE_ACCESSOR: any = {\r\n    provide: NG_VALUE_ACCESSOR,\r\n    useExisting: forwardRef(() => InputOtp),\r\n    multi: true\r\n};\r\n/**\r\n * Input Otp is used to enter one time passwords.\r\n * @group Components\r\n */\r\n@Component({\r\n    selector: 'p-inputOtp',\r\n    template: `\r\n        <ng-container *ngFor=\"let i of getRange(length); trackBy: trackByFn\">\r\n            <ng-container *ngIf=\"!inputTemplate\">\r\n                <input\r\n                    type=\"text\"\r\n                    pInputText\r\n                    [value]=\"getModelValue(i)\"\r\n                    [maxLength]=\"1\"\r\n                    [type]=\"inputType\"\r\n                    class=\"p-inputotp-input\"\r\n                    [inputmode]=\"inputMode\"\r\n                    [variant]=\"variant\"\r\n                    [readonly]=\"readonly\"\r\n                    [disabled]=\"disabled\"\r\n                    [invalid]=\"invalid\"\r\n                    [tabindex]=\"tabindex\"\r\n                    [unstyled]=\"unstyled\"\r\n                    (input)=\"onInput($event, i - 1)\"\r\n                    (focus)=\"onInputFocus($event)\"\r\n                    (blur)=\"onInputBlur($event)\"\r\n                    (paste)=\"onPaste($event)\"\r\n                    (keydown)=\"onKeyDown($event)\"\r\n                    pAutoFocus\r\n                    [autofocus]=\"getAutofocus(i)\"\r\n                />\r\n            </ng-container>\r\n            <ng-container *ngIf=\"inputTemplate\">\r\n                <ng-container *ngTemplateOutlet=\"inputTemplate; context: { $implicit: getToken(i - 1), events: getTemplateEvents(i - 1), index: i }\"> </ng-container>\r\n            </ng-container>\r\n        </ng-container>\r\n    `,\r\n    changeDetection: ChangeDetectionStrategy.OnPush,\r\n    encapsulation: ViewEncapsulation.None,\r\n    host: {\r\n        class: 'p-inputotp p-component'\r\n    },\r\n    providers: [INPUT_OTP_VALUE_ACCESSOR],\r\n    styleUrls: ['inputotp.css']\r\n})\r\nexport class InputOtp implements AfterContentInit {\r\n    /**\r\n     * When present, it specifies that the component should have invalid state style.\r\n     * @group Props\r\n     */\r\n    @Input() invalid: boolean = false;\r\n    /**\r\n     * When present, it specifies that the component should be disabled.\r\n     * @group Props\r\n     */\r\n\r\n    @Input() disabled: boolean = false;\r\n    /**\r\n     * When present, it specifies that an input field is read-only.\r\n     * @group Props\r\n     */\r\n    @Input() readonly: boolean = false;\r\n    /**\r\n     * Specifies the input variant of the component.\r\n     * @group Props\r\n     */\r\n    @Input() variant: string | null = null;\r\n    /**\r\n     * Index of the element in tabbing order.\r\n     * @group Props\r\n     */\r\n    @Input() tabindex: number | null = null;\r\n    /**\r\n     * Number of characters to initiate.\r\n     * @group Props\r\n     */\r\n    @Input() length: number = 4;\r\n    /**\r\n     * Mask pattern.\r\n     * @group Props\r\n     */\r\n    @Input() mask: boolean = false;\r\n    /**\r\n     * When present, it specifies that an input field is integer-only.\r\n     * @group Props\r\n     */\r\n    @Input() integerOnly: boolean = false;\r\n    /**\r\n     * When present, it specifies that the component should automatically get focus on load.\r\n     * @group Props\r\n     */\r\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\r\n    /**\r\n     * Callback to invoke on value change.\r\n     * @group Emits\r\n     */\r\n    @Output() onChange: EventEmitter<InputOtpChangeEvent> = new EventEmitter<InputOtpChangeEvent>();\r\n    /**\r\n     * Callback to invoke when the component receives focus.\r\n     * @param {Event} event - Browser event.\r\n     * @group Emits\r\n     */\r\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter();\r\n    /**\r\n     * Callback to invoke when the component loses focus.\r\n     * @param {Event} event - Browser event.\r\n     * @group Emits\r\n     */\r\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter();\r\n\r\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\r\n\r\n    inputTemplate: Nullable<TemplateRef<any>>;\r\n\r\n    tokens: any = [];\r\n\r\n    onModelChange: Function = () => {};\r\n\r\n    onModelTouched: Function = () => {};\r\n\r\n    value: any;\r\n\r\n    get inputMode(): string {\r\n        return this.integerOnly ? 'numeric' : 'text';\r\n    }\r\n\r\n    get inputType(): string {\r\n        return this.mask ? 'password' : 'text';\r\n    }\r\n\r\n    constructor(public cd: ChangeDetectorRef) {}\r\n\r\n    ngAfterContentInit() {\r\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\r\n            switch (item.getType()) {\r\n                case 'input':\r\n                    this.inputTemplate = item.template;\r\n                    break;\r\n                default:\r\n                    this.inputTemplate = item.template;\r\n                    break;\r\n            }\r\n        });\r\n    }\r\n\r\n    getToken(index) {\r\n        return this.tokens[index];\r\n    }\r\n\r\n    getTemplateEvents(index) {\r\n        return {\r\n            input: (event) => this.onInput(event, index),\r\n            keydown: (event) => this.onKeyDown(event),\r\n            focus: (event) => this.onFocus.emit(event),\r\n            blur: (event) => this.onBlur.emit(event),\r\n            paste: (event) => this.onPaste(event)\r\n        };\r\n    }\r\n\r\n    onInput(event, index) {\r\n        this.tokens[index] = event.target.value;\r\n        this.updateModel(event);\r\n\r\n        if (event.inputType === 'deleteContentBackward') {\r\n            this.moveToPrev(event);\r\n        } else if (event.inputType === 'insertText' || event.inputType === 'deleteContentForward') {\r\n            this.moveToNext(event);\r\n        }\r\n    }\r\n\r\n    updateModel(event: any) {\r\n        const newValue = this.tokens.join('');\r\n        this.onModelChange(newValue);\r\n\r\n        this.onChange.emit({\r\n            originalEvent: event,\r\n            value: newValue\r\n        });\r\n    }\r\n\r\n    writeValue(value: any): void {\r\n        if (value) {\r\n            if (Array.isArray(value) && value.length > 0) {\r\n                this.value = value.slice(0, this.length);\r\n            } else {\r\n                this.value = value.toString().split('').slice(0, this.length);\r\n            }\r\n        } else {\r\n            this.value = value;\r\n        }\r\n        this.updateTokens();\r\n        this.cd.markForCheck();\r\n    }\r\n\r\n    updateTokens() {\r\n        if (this.value !== null && this.value !== undefined) {\r\n            if (Array.isArray(this.value)) {\r\n                this.tokens = [...this.value];\r\n            } else {\r\n                this.tokens = this.value.toString().split('');\r\n            }\r\n        } else {\r\n            this.tokens = [];\r\n        }\r\n    }\r\n\r\n    getModelValue(i: number) {\r\n        return this.tokens[i - 1] || '';\r\n    }\r\n\r\n    getAutofocus(i: number): boolean {\r\n        if (i === 1) {\r\n            return this.autofocus;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    registerOnChange(fn: Function): void {\r\n        this.onModelChange = fn;\r\n    }\r\n\r\n    registerOnTouched(fn: Function): void {\r\n        this.onModelTouched = fn;\r\n    }\r\n\r\n    moveToPrev(event) {\r\n        let prevInput = this.findPrevInput(event.target);\r\n\r\n        if (prevInput) {\r\n            prevInput.focus();\r\n            prevInput.select();\r\n        }\r\n    }\r\n\r\n    moveToNext(event) {\r\n        let nextInput = this.findNextInput(event.target);\r\n\r\n        if (nextInput) {\r\n            nextInput.focus();\r\n            nextInput.select();\r\n        }\r\n    }\r\n\r\n    findNextInput(element) {\r\n        let nextElement = element.nextElementSibling;\r\n\r\n        if (!nextElement) return;\r\n\r\n        return nextElement.nodeName === 'INPUT' ? nextElement : this.findNextInput(nextElement);\r\n    }\r\n\r\n    findPrevInput(element) {\r\n        let prevElement = element.previousElementSibling;\r\n\r\n        if (!prevElement) return;\r\n\r\n        return prevElement.nodeName === 'INPUT' ? prevElement : this.findPrevInput(prevElement);\r\n    }\r\n\r\n    onInputFocus(event) {\r\n        event.target.select();\r\n        this.onFocus.emit(event);\r\n    }\r\n\r\n    onInputBlur(event) {\r\n        this.onBlur.emit(event);\r\n    }\r\n\r\n    onKeyDown(event) {\r\n        switch (event.code) {\r\n            case 'ArrowLeft':\r\n                this.moveToPrev(event);\r\n                event.preventDefault();\r\n\r\n                break;\r\n\r\n            case 'ArrowUp':\r\n            case 'ArrowDown':\r\n                event.preventDefault();\r\n\r\n                break;\r\n\r\n            case 'Backspace':\r\n                if (event.target.value.length === 0) {\r\n                    this.moveToPrev(event);\r\n                    event.preventDefault();\r\n                }\r\n\r\n                break;\r\n\r\n            case 'ArrowRight':\r\n                this.moveToNext(event);\r\n                event.preventDefault();\r\n\r\n                break;\r\n\r\n            default:\r\n                if ((this.integerOnly && !((event.code.startsWith('Digit') || event.code.startsWith('Numpad')) && Number(event.key) >= 0 && Number(event.key) <= 9)) || (this.tokens.join('').length >= this.length && event.code !== 'Delete')) {\r\n                    event.preventDefault();\r\n                }\r\n\r\n                break;\r\n        }\r\n    }\r\n\r\n    onPaste(event) {\r\n        let paste = event.clipboardData.getData('text');\r\n\r\n        if (paste.length) {\r\n            let pastedCode = paste.substring(0, this.length + 1);\r\n\r\n            if (!this.integerOnly || !isNaN(pastedCode)) {\r\n                this.tokens = pastedCode.split('');\r\n                this.updateModel(event);\r\n            }\r\n        }\r\n\r\n        event.preventDefault();\r\n    }\r\n\r\n    getRange(n: number): number[] {\r\n        return Array.from({ length: n }, (_, index) => index + 1);\r\n    }\r\n\r\n    trackByFn(index: number) {\r\n        return index;\r\n    }\r\n}\r\n\r\n@NgModule({\r\n    imports: [CommonModule, SharedModule, InputTextModule, AutoFocusModule],\r\n    exports: [InputOtp, SharedModule],\r\n    declarations: [InputOtp]\r\n})\r\nexport class InputOtpModule {}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAmGC,YAAY,EAAsEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACxO,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,iBAAiB,QAAQ,gBAAgB;AAElD,SAASC,eAAe,QAAQ,mBAAmB;;;;;;;;IAgBvCC,EAAA,CAAAC,uBAAA,GAAqC;IACjCD,EAAA,CAAAE,cAAA,eAqBE;IAPEF,EAAA,CAAAG,UAAA,mBAAAC,uEAAAC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAR,EAAA,CAAAS,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAY,WAAA,CAAAD,MAAA,CAAAE,OAAA,CAAAR,MAAA,EAAAG,IAAA,GAAoB,CAAC,CAAC;IAAA,EAAC,mBAAAM,uEAAAT,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAQ,MAAA,GAAAf,EAAA,CAAAS,aAAA;MAAA,OACvBT,EAAA,CAAAY,WAAA,CAAAG,MAAA,CAAAC,YAAA,CAAAX,MAAA,CAAoB;IAAA,EADG,kBAAAY,sEAAAZ,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAW,MAAA,GAAAlB,EAAA,CAAAS,aAAA;MAAA,OAExBT,EAAA,CAAAY,WAAA,CAAAM,MAAA,CAAAC,WAAA,CAAAd,MAAA,CAAmB;IAAA,EAFK,mBAAAe,uEAAAf,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAc,MAAA,GAAArB,EAAA,CAAAS,aAAA;MAAA,OAGvBT,EAAA,CAAAY,WAAA,CAAAS,MAAA,CAAAC,OAAA,CAAAjB,MAAA,CAAe;IAAA,EAHQ,qBAAAkB,yEAAAlB,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAiB,OAAA,GAAAxB,EAAA,CAAAS,aAAA;MAAA,OAIrBT,EAAA,CAAAY,WAAA,CAAAY,OAAA,CAAAC,SAAA,CAAApB,MAAA,CAAiB;IAAA,EAJI;IAdpCL,EAAA,CAAA0B,YAAA,EAqBE;IACN1B,EAAA,CAAA2B,qBAAA,EAAe;;;;;IAnBP3B,EAAA,CAAA4B,SAAA,GAA0B;IAA1B5B,EAAA,CAAA6B,UAAA,UAAAC,MAAA,CAAAC,aAAA,CAAAvB,IAAA,EAA0B,yBAAAsB,MAAA,CAAAE,SAAA,eAAAF,MAAA,CAAAG,SAAA,aAAAH,MAAA,CAAAI,OAAA,cAAAJ,MAAA,CAAAK,QAAA,cAAAL,MAAA,CAAAM,QAAA,aAAAN,MAAA,CAAAO,OAAA,cAAAP,MAAA,CAAAQ,QAAA,cAAAR,MAAA,CAAAS,QAAA,eAAAT,MAAA,CAAAU,YAAA,CAAAhC,IAAA;;;;;IAqB9BR,EAAA,CAAAyC,kBAAA,GAAqJ;;;;;;;;;;;;IADzJzC,EAAA,CAAAC,uBAAA,GAAoC;IAChCD,EAAA,CAAA0C,UAAA,IAAAC,8DAAA,0BAAqJ;IACzJ3C,EAAA,CAAA2B,qBAAA,EAAe;;;;;IADI3B,EAAA,CAAA4B,SAAA,GAAiC;IAAjC5B,EAAA,CAAA6B,UAAA,qBAAAe,MAAA,CAAAC,aAAA,CAAiC,4BAAA7C,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAH,MAAA,CAAAI,QAAA,CAAAxC,IAAA,OAAAoC,MAAA,CAAAK,iBAAA,CAAAzC,IAAA,OAAAA,IAAA;;;;;IA1BxDR,EAAA,CAAAC,uBAAA,GAAqE;IACjED,EAAA,CAAA0C,UAAA,IAAAQ,+CAAA,2BAuBe;IACflD,EAAA,CAAA0C,UAAA,IAAAS,+CAAA,0BAEe;IACnBnD,EAAA,CAAA2B,qBAAA,EAAe;;;;IA3BI3B,EAAA,CAAA4B,SAAA,GAAoB;IAApB5B,EAAA,CAAA6B,UAAA,UAAAuB,MAAA,CAAAP,aAAA,CAAoB;IAwBpB7C,EAAA,CAAA4B,SAAA,GAAmB;IAAnB5B,EAAA,CAAA6B,UAAA,SAAAuB,MAAA,CAAAP,aAAA,CAAmB;;;AArC9C,OAAO,MAAMQ,wBAAwB,GAAQ;EACzCC,OAAO,EAAExD,iBAAiB;EAC1ByD,WAAW,EAAE7D,UAAU,CAAC,MAAM8D,QAAQ,CAAC;EACvCC,KAAK,EAAE;CACV;AACD;;;;AA6CA,OAAM,MAAOD,QAAQ;EA6EjB,IAAIvB,SAASA,CAAA;IACT,OAAO,IAAI,CAACyB,WAAW,GAAG,SAAS,GAAG,MAAM;EAChD;EAEA,IAAI1B,SAASA,CAAA;IACT,OAAO,IAAI,CAAC2B,IAAI,GAAG,UAAU,GAAG,MAAM;EAC1C;EAEAC,YAAmBC,EAAqB;IAArB,KAAAA,EAAE,GAAFA,EAAE;IApFrB;;;;IAIS,KAAAxB,OAAO,GAAY,KAAK;IACjC;;;;IAKS,KAAAD,QAAQ,GAAY,KAAK;IAClC;;;;IAIS,KAAAD,QAAQ,GAAY,KAAK;IAClC;;;;IAIS,KAAAD,OAAO,GAAkB,IAAI;IACtC;;;;IAIS,KAAAI,QAAQ,GAAkB,IAAI;IACvC;;;;IAIS,KAAAwB,MAAM,GAAW,CAAC;IAC3B;;;;IAIS,KAAAH,IAAI,GAAY,KAAK;IAC9B;;;;IAIS,KAAAD,WAAW,GAAY,KAAK;IAMrC;;;;IAIU,KAAAK,QAAQ,GAAsC,IAAIvE,YAAY,EAAuB;IAC/F;;;;;IAKU,KAAAwE,OAAO,GAAwB,IAAIxE,YAAY,EAAE;IAC3D;;;;;IAKU,KAAAyE,MAAM,GAAwB,IAAIzE,YAAY,EAAE;IAM1D,KAAA0E,MAAM,GAAQ,EAAE;IAEhB,KAAAC,aAAa,GAAa,MAAK,CAAE,CAAC;IAElC,KAAAC,cAAc,GAAa,MAAK,CAAE,CAAC;EAYQ;EAE3CC,kBAAkBA,CAAA;IACb,IAAI,CAACC,SAAsC,CAACC,OAAO,CAAEC,IAAI,IAAI;MAC1D,QAAQA,IAAI,CAACC,OAAO,EAAE;QAClB,KAAK,OAAO;UACR,IAAI,CAAC5B,aAAa,GAAG2B,IAAI,CAACE,QAAQ;UAClC;QACJ;UACI,IAAI,CAAC7B,aAAa,GAAG2B,IAAI,CAACE,QAAQ;UAClC;;IAEZ,CAAC,CAAC;EACN;EAEA1B,QAAQA,CAAC2B,KAAK;IACV,OAAO,IAAI,CAACT,MAAM,CAACS,KAAK,CAAC;EAC7B;EAEA1B,iBAAiBA,CAAC0B,KAAK;IACnB,OAAO;MACHC,KAAK,EAAGC,KAAK,IAAK,IAAI,CAAChE,OAAO,CAACgE,KAAK,EAAEF,KAAK,CAAC;MAC5CG,OAAO,EAAGD,KAAK,IAAK,IAAI,CAACpD,SAAS,CAACoD,KAAK,CAAC;MACzCE,KAAK,EAAGF,KAAK,IAAK,IAAI,CAACb,OAAO,CAACgB,IAAI,CAACH,KAAK,CAAC;MAC1CI,IAAI,EAAGJ,KAAK,IAAK,IAAI,CAACZ,MAAM,CAACe,IAAI,CAACH,KAAK,CAAC;MACxCK,KAAK,EAAGL,KAAK,IAAK,IAAI,CAACvD,OAAO,CAACuD,KAAK;KACvC;EACL;EAEAhE,OAAOA,CAACgE,KAAK,EAAEF,KAAK;IAChB,IAAI,CAACT,MAAM,CAACS,KAAK,CAAC,GAAGE,KAAK,CAACM,MAAM,CAACC,KAAK;IACvC,IAAI,CAACC,WAAW,CAACR,KAAK,CAAC;IAEvB,IAAIA,KAAK,CAAC7C,SAAS,KAAK,uBAAuB,EAAE;MAC7C,IAAI,CAACsD,UAAU,CAACT,KAAK,CAAC;KACzB,MAAM,IAAIA,KAAK,CAAC7C,SAAS,KAAK,YAAY,IAAI6C,KAAK,CAAC7C,SAAS,KAAK,sBAAsB,EAAE;MACvF,IAAI,CAACuD,UAAU,CAACV,KAAK,CAAC;;EAE9B;EAEAQ,WAAWA,CAACR,KAAU;IAClB,MAAMW,QAAQ,GAAG,IAAI,CAACtB,MAAM,CAACuB,IAAI,CAAC,EAAE,CAAC;IACrC,IAAI,CAACtB,aAAa,CAACqB,QAAQ,CAAC;IAE5B,IAAI,CAACzB,QAAQ,CAACiB,IAAI,CAAC;MACfU,aAAa,EAAEb,KAAK;MACpBO,KAAK,EAAEI;KACV,CAAC;EACN;EAEAG,UAAUA,CAACP,KAAU;IACjB,IAAIA,KAAK,EAAE;MACP,IAAIQ,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,IAAIA,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;QAC1C,IAAI,CAACsB,KAAK,GAAGA,KAAK,CAACU,KAAK,CAAC,CAAC,EAAE,IAAI,CAAChC,MAAM,CAAC;OAC3C,MAAM;QACH,IAAI,CAACsB,KAAK,GAAGA,KAAK,CAACW,QAAQ,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC,CAACF,KAAK,CAAC,CAAC,EAAE,IAAI,CAAChC,MAAM,CAAC;;KAEpE,MAAM;MACH,IAAI,CAACsB,KAAK,GAAGA,KAAK;;IAEtB,IAAI,CAACa,YAAY,EAAE;IACnB,IAAI,CAACpC,EAAE,CAACqC,YAAY,EAAE;EAC1B;EAEAD,YAAYA,CAAA;IACR,IAAI,IAAI,CAACb,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAKe,SAAS,EAAE;MACjD,IAAIP,KAAK,CAACC,OAAO,CAAC,IAAI,CAACT,KAAK,CAAC,EAAE;QAC3B,IAAI,CAAClB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACkB,KAAK,CAAC;OAChC,MAAM;QACH,IAAI,CAAClB,MAAM,GAAG,IAAI,CAACkB,KAAK,CAACW,QAAQ,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;;KAEpD,MAAM;MACH,IAAI,CAAC9B,MAAM,GAAG,EAAE;;EAExB;EAEAnC,aAAaA,CAACqE,CAAS;IACnB,OAAO,IAAI,CAAClC,MAAM,CAACkC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;EACnC;EAEA5D,YAAYA,CAAC4D,CAAS;IAClB,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI,CAACC,SAAS;;IAEzB,OAAO,KAAK;EAChB;EAEAC,gBAAgBA,CAACC,EAAY;IACzB,IAAI,CAACpC,aAAa,GAAGoC,EAAE;EAC3B;EAEAC,iBAAiBA,CAACD,EAAY;IAC1B,IAAI,CAACnC,cAAc,GAAGmC,EAAE;EAC5B;EAEAjB,UAAUA,CAACT,KAAK;IACZ,IAAI4B,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC7B,KAAK,CAACM,MAAM,CAAC;IAEhD,IAAIsB,SAAS,EAAE;MACXA,SAAS,CAAC1B,KAAK,EAAE;MACjB0B,SAAS,CAACE,MAAM,EAAE;;EAE1B;EAEApB,UAAUA,CAACV,KAAK;IACZ,IAAI+B,SAAS,GAAG,IAAI,CAACC,aAAa,CAAChC,KAAK,CAACM,MAAM,CAAC;IAEhD,IAAIyB,SAAS,EAAE;MACXA,SAAS,CAAC7B,KAAK,EAAE;MACjB6B,SAAS,CAACD,MAAM,EAAE;;EAE1B;EAEAE,aAAaA,CAACC,OAAO;IACjB,IAAIC,WAAW,GAAGD,OAAO,CAACE,kBAAkB;IAE5C,IAAI,CAACD,WAAW,EAAE;IAElB,OAAOA,WAAW,CAACE,QAAQ,KAAK,OAAO,GAAGF,WAAW,GAAG,IAAI,CAACF,aAAa,CAACE,WAAW,CAAC;EAC3F;EAEAL,aAAaA,CAACI,OAAO;IACjB,IAAII,WAAW,GAAGJ,OAAO,CAACK,sBAAsB;IAEhD,IAAI,CAACD,WAAW,EAAE;IAElB,OAAOA,WAAW,CAACD,QAAQ,KAAK,OAAO,GAAGC,WAAW,GAAG,IAAI,CAACR,aAAa,CAACQ,WAAW,CAAC;EAC3F;EAEAlG,YAAYA,CAAC6D,KAAK;IACdA,KAAK,CAACM,MAAM,CAACwB,MAAM,EAAE;IACrB,IAAI,CAAC3C,OAAO,CAACgB,IAAI,CAACH,KAAK,CAAC;EAC5B;EAEA1D,WAAWA,CAAC0D,KAAK;IACb,IAAI,CAACZ,MAAM,CAACe,IAAI,CAACH,KAAK,CAAC;EAC3B;EAEApD,SAASA,CAACoD,KAAK;IACX,QAAQA,KAAK,CAACuC,IAAI;MACd,KAAK,WAAW;QACZ,IAAI,CAAC9B,UAAU,CAACT,KAAK,CAAC;QACtBA,KAAK,CAACwC,cAAc,EAAE;QAEtB;MAEJ,KAAK,SAAS;MACd,KAAK,WAAW;QACZxC,KAAK,CAACwC,cAAc,EAAE;QAEtB;MAEJ,KAAK,WAAW;QACZ,IAAIxC,KAAK,CAACM,MAAM,CAACC,KAAK,CAACtB,MAAM,KAAK,CAAC,EAAE;UACjC,IAAI,CAACwB,UAAU,CAACT,KAAK,CAAC;UACtBA,KAAK,CAACwC,cAAc,EAAE;;QAG1B;MAEJ,KAAK,YAAY;QACb,IAAI,CAAC9B,UAAU,CAACV,KAAK,CAAC;QACtBA,KAAK,CAACwC,cAAc,EAAE;QAEtB;MAEJ;QACI,IAAK,IAAI,CAAC3D,WAAW,IAAI,EAAE,CAACmB,KAAK,CAACuC,IAAI,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIzC,KAAK,CAACuC,IAAI,CAACE,UAAU,CAAC,QAAQ,CAAC,KAAKC,MAAM,CAAC1C,KAAK,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAAID,MAAM,CAAC1C,KAAK,CAAC2C,GAAG,CAAC,IAAI,CAAC,CAAC,IAAM,IAAI,CAACtD,MAAM,CAACuB,IAAI,CAAC,EAAE,CAAC,CAAC3B,MAAM,IAAI,IAAI,CAACA,MAAM,IAAIe,KAAK,CAACuC,IAAI,KAAK,QAAS,EAAE;UAC7NvC,KAAK,CAACwC,cAAc,EAAE;;QAG1B;;EAEZ;EAEA/F,OAAOA,CAACuD,KAAK;IACT,IAAIK,KAAK,GAAGL,KAAK,CAAC4C,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IAE/C,IAAIxC,KAAK,CAACpB,MAAM,EAAE;MACd,IAAI6D,UAAU,GAAGzC,KAAK,CAAC0C,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC9D,MAAM,GAAG,CAAC,CAAC;MAEpD,IAAI,CAAC,IAAI,CAACJ,WAAW,IAAI,CAACmE,KAAK,CAACF,UAAU,CAAC,EAAE;QACzC,IAAI,CAACzD,MAAM,GAAGyD,UAAU,CAAC3B,KAAK,CAAC,EAAE,CAAC;QAClC,IAAI,CAACX,WAAW,CAACR,KAAK,CAAC;;;IAI/BA,KAAK,CAACwC,cAAc,EAAE;EAC1B;EAEAS,QAAQA,CAACC,CAAS;IACd,OAAOnC,KAAK,CAACoC,IAAI,CAAC;MAAElE,MAAM,EAAEiE;IAAC,CAAE,EAAE,CAACE,CAAC,EAAEtD,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC;EAC7D;EAEAuD,SAASA,CAACvD,KAAa;IACnB,OAAOA,KAAK;EAChB;;;uBAzRSnB,QAAQ,EAAAxD,EAAA,CAAAmI,iBAAA,CAAAnI,EAAA,CAAAoI,iBAAA;IAAA;EAAA;;;YAAR5E,QAAQ;MAAA6E,SAAA;MAAAC,cAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;sCAiEA7I,aAAa;;;;;;;;;;;;;;;;;8CAnBVF,gBAAgB;MAAA;MAAAkJ,OAAA;QAAA5E,QAAA;QAAAC,OAAA;QAAAC,MAAA;MAAA;MAAA2E,QAAA,GAAA5I,EAAA,CAAA6I,kBAAA,CAjDzB,CAACxF,wBAAwB,CAAC,GAAArD,EAAA,CAAA8I,wBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAvE,QAAA,WAAAwE,kBAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnCjCxI,EAAA,CAAA0C,UAAA,IAAAyG,gCAAA,0BA4Be;;;UA5BanJ,EAAA,CAAA6B,UAAA,YAAA4G,GAAA,CAAAX,QAAA,CAAAW,GAAA,CAAA3E,MAAA,EAAqB,iBAAA2E,GAAA,CAAAP,SAAA;;;;;;;;;;AAuUzD,OAAM,MAAOkB,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAJb7J,YAAY,EAAEK,YAAY,EAAEC,eAAe,EAAEE,eAAe,EAClDH,YAAY;IAAA;EAAA;;;2EAGvBwJ,cAAc;IAAAC,YAAA,GAjSd7F,QAAQ;IAAA8F,OAAA,GA6RP/J,YAAY,EAAEK,YAAY,EAAEC,eAAe,EAAEE,eAAe;IAAAwJ,OAAA,GA7R7D/F,QAAQ,EA8RG5D,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}