{"ast": null, "code": "import { CONSTANTS } from \"src/app/service/comon/constants\";\nclass CommonChart {\n  constructor() {\n    this.CustomFunctionTooltip = [{\n      name: \"TOTAL\",\n      value: \"{%s}\",\n      action: tooltipItems => {\n        let total = 0;\n        let chartType = tooltipItems[0].dataset.type;\n        if (chartType != 'pie' && chartType != 'doughnut') {\n          tooltipItems.forEach(el => total += parseInt(el.raw + \"\"));\n        } else {\n          tooltipItems[0].dataset.data.forEach(el => total += parseInt(el + \"\"));\n        }\n        return Intl.NumberFormat('vi-VI').format(total);\n      }\n    }, {\n      name: \"TITLE\",\n      value: \"{%t}\",\n      action: tooltipItems => {\n        if ('label' in tooltipItems) {\n          return tooltipItems.label;\n        }\n        return tooltipItems[0].label;\n      }\n    }, {\n      name: \"LEGEND\",\n      value: \"{%l}\",\n      action: tooltipItem => {\n        return tooltipItem.dataset.label;\n      }\n    }, {\n      name: \"VALUE\",\n      value: \"{%v}\",\n      action: tooltipItem => {\n        return tooltipItem.formattedValue;\n      }\n    }, {\n      name: \"PERCENT\",\n      value: \"{%npv}\",\n      action: tooltipItem => {\n        let datasets = tooltipItem.chart.data.datasets;\n        let total = 0;\n        let chartType = datasets[0].type;\n        if (chartType != 'pie' && chartType != 'doughnut') {\n          datasets.forEach(el => total += parseInt(el.data[tooltipItem.dataIndex] + \"\"));\n        } else {\n          tooltipItem.dataset.data.forEach(el => total += parseInt(el + \"\"));\n        }\n        return Math.round(tooltipItem.raw / total * 100);\n      }\n    }];\n    this.CustomFunctionLegend = [{\n      name: \"LEGEND\",\n      value: \"{%l}\"\n    }, {\n      name: \"VALUE\",\n      value: \"{%v}\"\n    }];\n  }\n  genDatasetBarChart(label, data, typeAnimation, xAxisID, yAxisID, stack, backgroundColor = null, barPercentage = 0.5, barThickness = 30, maxBarThickness = 50, base = 50, borderColor = null, borderRadius = 0, borderWidth = 0, categoryPercentage = 1, hoverBackgroundColor = null, hoverBorderColor = null, hoverBorderWidth = 0) {\n    let result = {\n      type: \"bar\",\n      label,\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      barPercentage,\n      barThickness,\n      maxBarThickness,\n      base,\n      borderColor,\n      borderRadius,\n      borderWidth,\n      categoryPercentage,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth\n    };\n    if (xAxisID) {\n      result.xAxisID = xAxisID;\n    }\n    if (yAxisID) {\n      result.yAxisID = yAxisID;\n    }\n    if (stack) {\n      result.stack = stack;\n    }\n    return result;\n  }\n  // genDatasetBarThresholdChart(label: string, data: number[], typeAnimation: EasingFunction,\n  //                    xAxisID?:string,yAxisID?: string, stack?: string,\n  //                    backgroundColor:string[]|null=null, barPercentage: number = 0.5, barThickness: number = 30,\n  //                    maxBarThickness: number = 50, base: number = 50, borderColor: string|null=null,\n  //                    borderRadius: number = 0, borderWidth: number = 0, categoryPercentage: number = 1,\n  //                    hoverBackgroundColor: string[]|null=null, hoverBorderColor: string|null=null, hoverBorderWidth: number = 0): ChartDataset {\n  //     let result: ChartDataset = {\n  //         type: \"bar\",\n  //         label,\n  //         data,\n  //         animation: {\n  //             easing: typeAnimation\n  //         },\n  //         backgroundColor,\n  //         barPercentage,\n  //         barThickness,\n  //         maxBarThickness,\n  //         base,\n  //         borderColor,\n  //         borderRadius,\n  //         borderWidth,\n  //         categoryPercentage,\n  //         hoverBackgroundColor,\n  //         hoverBorderColor,\n  //         hoverBorderWidth\n  //     };\n  //     if(xAxisID){\n  //         result.xAxisID = xAxisID;\n  //     }\n  //     if(yAxisID){\n  //         result.yAxisID = yAxisID;\n  //     }\n  //     if(stack){\n  //         result.stack = stack;\n  //     }\n  //     return result;\n  // }\n  genDatasetLineChart(label, data, typeAnimation, backgroundColor, borderColor, borderCapStyle, borderJoinStyle, fill, tension, isLineDash, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, pointBackgroundColor, pointBorderColor, pointBorderWidth, xAxisID, yAxisID) {\n    let result = {\n      type: \"line\",\n      label,\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      borderColor,\n      borderDash: isLineDash ? [10, 5] : [0, 0],\n      borderCapStyle,\n      borderJoinStyle,\n      borderWidth: 2,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      pointBackgroundColor,\n      pointBorderColor,\n      pointBorderWidth,\n      fill,\n      tension\n    };\n    if (xAxisID) {\n      result.xAxisID = xAxisID;\n    }\n    if (yAxisID) {\n      result.yAxisID = yAxisID;\n    }\n    return result;\n  }\n  genDatasetBubbleChart(label, data, backgroundColor, borderColor, borderWidth, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, pointStyle) {\n    let result = {\n      type: \"bubble\",\n      label,\n      data,\n      backgroundColor,\n      borderColor,\n      borderWidth,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      pointStyle\n    };\n    return result;\n  }\n  genDatasetDoughnutChart(data, typeAnimation, backgroundColor, borderColor, borderWidth, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, weight) {\n    let result = {\n      type: \"doughnut\",\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      borderColor,\n      borderWidth,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      weight\n    };\n    return result;\n  }\n  genDatasetPieChart(data, typeAnimation, backgroundColor, borderColor, borderWidth, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, weight) {\n    let result = {\n      type: \"pie\",\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      borderColor,\n      borderWidth,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      weight\n    };\n    return result;\n  }\n  genDatasetPolarAreaChart(data, typeAnimation, backgroundColor, borderColor, borderWidth, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, weight) {\n    let result = {\n      type: \"polarArea\",\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      angle: Math.max(...data),\n      borderColor,\n      borderWidth,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      weight\n    };\n    return result;\n  }\n  genDatasetRadarChart(label, data, typeAnimation, backgroundColor, borderColor, borderCapStyle, borderJoinStyle, fill, tension, isDash, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, pointBackgroundColor, pointBorderColor, pointBorderWidth, borderWidth) {\n    let result = {\n      type: \"radar\",\n      label,\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      borderCapStyle,\n      borderColor,\n      borderDash: isDash ? [10, 5] : [0, 0],\n      borderWidth,\n      borderJoinStyle,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      pointBackgroundColor,\n      pointBorderColor,\n      pointBorderWidth,\n      fill,\n      tension\n    };\n    return result;\n  }\n  genDatasetScatterChart(label, data, typeAnimation, backgroundColor, borderColor, borderWidth, hoverBackgroundColor, hoverBorderColor, hoverBorderWidth, isDash, fill, tension, borderCapStyle, borderJoinStyle, pointBackgroundColor, pointBorderColor, pointBorderWidth) {\n    let result = {\n      type: \"scatter\",\n      label,\n      data,\n      animation: {\n        easing: typeAnimation\n      },\n      backgroundColor,\n      borderCapStyle,\n      borderColor,\n      borderDash: isDash ? [10, 5] : [0, 0],\n      borderWidth,\n      borderJoinStyle,\n      hoverBackgroundColor,\n      hoverBorderColor,\n      hoverBorderWidth,\n      pointBackgroundColor,\n      pointBorderColor,\n      pointBorderWidth,\n      fill,\n      tension\n    };\n    return result;\n  }\n  // tooltipItems: {chart: object, dataIndex: number, datasetIndex: number, formattedValue: string, label: string, raw: number, dataset.data: number[]}\n  // {\n  //     family: \"roboto\",\n  //     lineHeight: 1,\n  //     size: 12,\n  //     style: \"inherit\",\n  //     weight: \"bold\"\n  // }\n  genOptionTooltip(mode, intersect, backgroundColor, bodyColor, bodyAlign, bodyFont, borderColor, borderWidth, bodySpacing, boxHeight, boxWidth, radius, caretSize, titleAlign, titleColor, titleFont, footerColor, patternTitle, patternLabel, patternFooter) {\n    let tooltip = {\n      mode,\n      intersect,\n      backgroundColor,\n      bodyColor,\n      bodyAlign,\n      bodyFont,\n      borderColor,\n      borderWidth,\n      bodySpacing,\n      boxHeight,\n      boxWidth,\n      cornerRadius: {\n        bottomLeft: radius,\n        bottomRight: radius,\n        topLeft: radius,\n        topRight: radius\n      },\n      caretSize,\n      titleAlign,\n      titleColor,\n      titleFont,\n      footerColor\n    };\n    if (patternTitle || patternLabel || patternFooter) {\n      let callbacks = {};\n      if (patternTitle) {\n        callbacks['title'] = tooltipItems => {\n          let result = patternTitle;\n          this.CustomFunctionTooltip.forEach(item => {\n            if (result.indexOf(item.value) >= 0) {\n              result = result.replace(item.value, item.action(tooltipItems));\n            }\n          });\n          return result.split(\"|\");\n        };\n      }\n      if (patternLabel) {\n        callbacks['label'] = tooltipItems => {\n          let result = patternLabel;\n          this.CustomFunctionTooltip.forEach(item => {\n            if (result.indexOf(item.value) >= 0) {\n              result = result.replace(item.value, item.action(tooltipItems));\n            }\n          });\n          return result.split(\"|\");\n        };\n      }\n      if (patternFooter) {\n        callbacks['footer'] = tooltipItems => {\n          let result = patternFooter;\n          this.CustomFunctionTooltip.forEach(item => {\n            if (result.indexOf(item.value) >= 0) {\n              result = result.replace(item.value, item.action(tooltipItems));\n            }\n          });\n          return result.split(\"|\");\n        };\n      }\n      tooltip['callbacks'] = callbacks;\n    }\n    return tooltip;\n  }\n  genOptionLegend(titleColor, titleDisplay, titleFont, titleText, align, display, labelColor, labelFont, boxWidth, boxHeight, position, patternBody) {\n    let legend = {\n      title: {\n        color: titleColor,\n        display: titleDisplay,\n        font: titleFont,\n        text: titleText\n      },\n      align,\n      display,\n      labels: {\n        color: labelColor,\n        font: labelFont,\n        boxWidth,\n        boxHeight,\n        generateLabels(chart) {\n          let result = [];\n          let datasets = chart.data.datasets;\n          let labels = chart.data.labels;\n          if (!datasets || datasets.length == 0) return null;\n          if (datasets[0].type == CONSTANTS.CHART_TYPE.PIE || datasets[0].type == CONSTANTS.CHART_TYPE.DOUGHNUT) {\n            labels.forEach((label, i) => {\n              let text = `${label} (${Intl.NumberFormat('vi-VI').format(parseInt(datasets[0].data[i].toString()))})`;\n              if (patternBody != undefined && patternBody != null) {\n                text = patternBody.replaceAll(\"{%l}\", label);\n                text = text.replaceAll(\"{%v}\", Intl.NumberFormat('vi-VI').format(parseInt(datasets[0].data[i].toString())));\n              }\n              result.push({\n                text: text,\n                index: i,\n                fillStyle: datasets[0].backgroundColor[i]\n              });\n            });\n          } else {\n            datasets.forEach((dataset, index) => {\n              let total = 0;\n              let data = dataset.data;\n              data.forEach(el => total += parseInt(el.toString()));\n              let strTotal = Intl.NumberFormat('vi-VI').format(total);\n              let text = `${dataset.label} (${strTotal})`;\n              if (patternBody != undefined && patternBody != null) {\n                text = patternBody.replaceAll(\"{%l}\", dataset.label);\n                text = text.replaceAll(\"{%v}\", strTotal);\n              }\n              result.push({\n                text: text,\n                datasetIndex: index,\n                fillStyle: dataset.backgroundColor.toString(),\n                hidden: false\n              });\n            });\n          }\n          //sort legend biểu đồ ngưỡng\n          const order = [\"cảnh báo\", \"xem xét\", \"an toàn\"];\n          result.sort((a, b) => {\n            const indexA = order.findIndex(item => a.text.toLowerCase().startsWith(item));\n            const indexB = order.findIndex(item => b.text.toLowerCase().startsWith(item));\n            return indexA - indexB;\n          });\n          return result;\n        }\n      },\n      position\n    };\n    return legend;\n  }\n  genOptionsTitleAndSubTitle(align, color, display, font, position, text) {\n    let title = {\n      align,\n      color,\n      display,\n      font,\n      position,\n      text\n    };\n    return title;\n  }\n  genOptionScaleX(stacked, beginAtZero, position, tickColor, rotation, drawOnChartArea, colorGrid, borderGridColor, isDash, titleText, titleAlign, titleDisplay, titleColor, titleFont) {\n    let x = {\n      beginAtZero,\n      position,\n      ticks: {\n        color: tickColor,\n        minRotation: rotation\n        // callback: function (value) {\n        //     return Number.isInteger(value) ? value : null; // Chỉ hiển thị số nguyên\n        // },\n      },\n\n      grid: {\n        drawOnChartArea,\n        color: colorGrid,\n        borderColor: borderGridColor,\n        borderDash: isDash ? [5, 5] : [0, 0]\n      },\n      stacked,\n      title: {\n        text: titleText,\n        align: titleAlign,\n        display: titleDisplay,\n        color: titleColor,\n        font: titleFont\n      }\n    };\n    return x;\n  }\n  genOptionScaleY(stacked, beginAtZero, position, tickColor, rotation, drawOnChartArea, colorGrid, borderGridColor, isDash, titleText, titleAlign, titleDisplay, titleColor, titleFont) {\n    let y = {\n      stacked,\n      position,\n      beginAtZero,\n      ticks: {\n        color: tickColor,\n        align: \"center\",\n        minRotation: rotation\n      },\n      grid: {\n        color: colorGrid,\n        borderColor: borderGridColor,\n        drawOnChartArea,\n        borderDash: isDash ? [5, 5] : [0, 0]\n      },\n      title: {\n        text: titleText,\n        align: titleAlign,\n        display: titleDisplay,\n        color: titleColor,\n        font: titleFont\n      }\n    };\n    return y;\n  }\n  getOptionBoxValue(isShowBoxValue) {\n    return {\n      align: \"center\",\n      anchor(context) {\n        if (context.dataset.type == CONSTANTS.CHART_TYPE.BAR) {\n          return \"end\";\n        } else {\n          return \"center\";\n        }\n      },\n      backgroundColor(context) {\n        // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\n        //     return \"transparent\";\n        // }else{\n        return \"#FFFAFA\";\n        // }\n      },\n\n      borderColor(context) {\n        // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\n        //     return \"none\";\n        // }else{\n        return \"#8B8989\";\n        // }\n      },\n\n      borderRadius: 8,\n      borderWidth(context) {\n        if (context.dataset.type == CONSTANTS.CHART_TYPE.BAR) {\n          return 1;\n        } else {\n          return 1;\n        }\n      },\n      color(context) {\n        if (context.dataset.type == CONSTANTS.CHART_TYPE.BAR) {\n          return \"#000\";\n        } else {\n          return \"#528B8B\";\n        }\n      },\n      display(context) {\n        return isShowBoxValue;\n        // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\n        //     return true;\n        // }else{\n        //     return false;\n        // }\n      },\n\n      font: {\n        size: 14\n      },\n      formatter(value, context) {\n        let strValue = Intl.NumberFormat('vi-VI').format(value);\n        if (context.dataset.type == CONSTANTS.CHART_TYPE.BAR) {\n          return strValue;\n        } else {\n          return strValue;\n        }\n      },\n      opacity: 1,\n      padding: {\n        bottom: 1,\n        left: 8,\n        right: 8,\n        top: 1\n      },\n      rotation: 0,\n      textAlign: \"center\"\n    };\n  }\n  genOptions(isBarHorizontal, tooltip, legend, title, subtitle, scales, datalabels) {\n    let result = {\n      indexAxis: isBarHorizontal ? \"y\" : \"x\",\n      maintainAspectRatio: false,\n      aspectRatio: 0.6,\n      plugins: {\n        tooltip,\n        legend,\n        title,\n        subtitle,\n        datalabels\n      }\n    };\n    if (scales != null) {\n      result.scales = scales;\n    }\n    return result;\n  }\n}\nexport const commonChart = new CommonChart();", "map": {"version": 3, "names": ["CONSTANTS", "<PERSON><PERSON><PERSON>", "constructor", "CustomFunctionTooltip", "name", "value", "action", "tooltipItems", "total", "chartType", "dataset", "type", "for<PERSON>ach", "el", "parseInt", "raw", "data", "Intl", "NumberFormat", "format", "label", "tooltipItem", "formattedValue", "datasets", "chart", "dataIndex", "Math", "round", "CustomFunctionLegend", "genDatasetBarChart", "typeAnimation", "xAxisID", "yAxisID", "stack", "backgroundColor", "barPercentage", "barThickness", "maxBar<PERSON><PERSON><PERSON><PERSON>", "base", "borderColor", "borderRadius", "borderWidth", "categoryPercentage", "hoverBackgroundColor", "hoverBorderColor", "hoverBorderWidth", "result", "animation", "easing", "genDatasetLineChart", "borderCapStyle", "borderJoinStyle", "fill", "tension", "isLineDash", "pointBackgroundColor", "pointBorderColor", "pointBorderWidth", "borderDash", "genDatasetBubbleChart", "pointStyle", "genDatasetDoughnutChart", "weight", "genDataset<PERSON>ie<PERSON>hart", "genDatasetPolarAreaChart", "angle", "max", "genDatasetRadarChart", "isDash", "genDatasetScatterChart", "genOptionTooltip", "mode", "intersect", "bodyColor", "bodyAlign", "bodyFont", "bodySpacing", "boxHeight", "boxWidth", "radius", "caretSize", "titleAlign", "titleColor", "titleFont", "footerColor", "patternTitle", "patternLabel", "patternFooter", "tooltip", "cornerRadius", "bottomLeft", "bottomRight", "topLeft", "topRight", "callbacks", "item", "indexOf", "replace", "split", "genOptionLegend", "titleDisplay", "titleText", "align", "display", "labelColor", "labelFont", "position", "patternBody", "legend", "title", "color", "font", "text", "labels", "generateLabels", "length", "CHART_TYPE", "PIE", "DOUGHNUT", "i", "toString", "undefined", "replaceAll", "push", "index", "fillStyle", "strTotal", "datasetIndex", "hidden", "order", "sort", "a", "b", "indexA", "findIndex", "toLowerCase", "startsWith", "indexB", "genOptionsTitleAndSubTitle", "genOptionScaleX", "stacked", "beginAtZero", "tickColor", "rotation", "drawOnChartArea", "colorGrid", "borderGridColor", "x", "ticks", "minRotation", "grid", "genOptionScaleY", "y", "getOptionBoxValue", "isShowBoxValue", "anchor", "context", "BAR", "size", "formatter", "strValue", "opacity", "padding", "bottom", "left", "right", "top", "textAlign", "genOptions", "isBarHorizontal", "subtitle", "scales", "datalabels", "indexAxis", "maintainAspectRatio", "aspectRatio", "plugins", "common<PERSON>hart"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\charts\\common-chart.ts"], "sourcesContent": ["import { Align, ChartDataset, ChartOptions, EasingFunction, InteractionMode, LayoutPosition, LegendItem, PointStyle, TitleOptions, TooltipOptions } from \"chart.js\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport chart from \"src/i18n/vi/chart\";\r\n\r\nclass CommonChart {\r\n    CustomFunctionTooltip: Array<any>;\r\n    CustomFunctionLegend: Array<any>;\r\n    constructor() {\r\n        this.CustomFunctionTooltip = [\r\n            {\r\n                name: \"TOTAL\",\r\n                value: \"{%s}\",\r\n                action: (tooltipItems) => {\r\n                    let total = 0;\r\n                    let chartType = tooltipItems[0].dataset.type;\r\n                    if(chartType != 'pie' && chartType != 'doughnut'){\r\n                        tooltipItems.forEach(el => total += parseInt(el.raw+\"\"));\r\n                    }else{\r\n                        tooltipItems[0].dataset.data.forEach(el => total += parseInt(el+\"\"));\r\n                    }\r\n                    return Intl.NumberFormat('vi-VI').format(total);\r\n                }\r\n            },\r\n            {\r\n                name: \"TITLE\",\r\n                value: \"{%t}\",\r\n                action: (tooltipItems) => {\r\n                    if('label' in tooltipItems){\r\n                        return tooltipItems.label;\r\n                    }\r\n                    return tooltipItems[0].label;\r\n                }\r\n            },\r\n            {\r\n                name: \"LEGEND\",\r\n                value: \"{%l}\",\r\n                action: (tooltipItem) => {\r\n                    return tooltipItem.dataset.label;\r\n                }\r\n            },\r\n            {\r\n                name: \"VALUE\",\r\n                value: \"{%v}\",\r\n                action: (tooltipItem) => {\r\n                    return tooltipItem.formattedValue;\r\n                }\r\n            },\r\n            {\r\n                name: \"PERCENT\",\r\n                value: \"{%npv}\",\r\n                action: (tooltipItem) => {\r\n                    let datasets = tooltipItem.chart.data.datasets;\r\n                    let total = 0;\r\n                    let chartType = datasets[0].type;\r\n                    if(chartType != 'pie' && chartType != 'doughnut'){\r\n                        datasets.forEach(el => total += parseInt(el.data[tooltipItem.dataIndex]+\"\"));\r\n                    }else{\r\n                        tooltipItem.dataset.data.forEach(el => total += parseInt(el+\"\"));\r\n                    }\r\n                    return Math.round(tooltipItem.raw/total*100);\r\n                }\r\n            }\r\n        ];\r\n        this.CustomFunctionLegend = [\r\n            {\r\n                name: \"LEGEND\",\r\n                value: \"{%l}\",\r\n            },\r\n            {\r\n                name: \"VALUE\",\r\n                value: \"{%v}\",\r\n            },\r\n        ];\r\n    }\r\n\r\n    genDatasetBarChart(label: string, data: number[], typeAnimation: EasingFunction,\r\n                        xAxisID?:string,yAxisID?: string, stack?: string,\r\n                        backgroundColor:string|null=null, barPercentage: number = 0.5, barThickness: number = 30,\r\n                        maxBarThickness: number = 50, base: number = 50, borderColor: string|null=null,\r\n                        borderRadius: number = 0, borderWidth: number = 0, categoryPercentage: number = 1,\r\n                        hoverBackgroundColor: string|null=null, hoverBorderColor: string|null=null, hoverBorderWidth: number = 0): ChartDataset {\r\n        let result: ChartDataset = {\r\n            type: \"bar\",\r\n            label,\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            barPercentage,\r\n            barThickness,\r\n            maxBarThickness,\r\n            base,\r\n            borderColor,\r\n            borderRadius,\r\n            borderWidth,\r\n            categoryPercentage,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth\r\n        };\r\n        if(xAxisID){\r\n            result.xAxisID = xAxisID;\r\n        }\r\n        if(yAxisID){\r\n            result.yAxisID = yAxisID;\r\n        }\r\n        if(stack){\r\n            result.stack = stack;\r\n        }\r\n        return result;\r\n    }\r\n    // genDatasetBarThresholdChart(label: string, data: number[], typeAnimation: EasingFunction,\r\n    //                    xAxisID?:string,yAxisID?: string, stack?: string,\r\n    //                    backgroundColor:string[]|null=null, barPercentage: number = 0.5, barThickness: number = 30,\r\n    //                    maxBarThickness: number = 50, base: number = 50, borderColor: string|null=null,\r\n    //                    borderRadius: number = 0, borderWidth: number = 0, categoryPercentage: number = 1,\r\n    //                    hoverBackgroundColor: string[]|null=null, hoverBorderColor: string|null=null, hoverBorderWidth: number = 0): ChartDataset {\r\n    //     let result: ChartDataset = {\r\n    //         type: \"bar\",\r\n    //         label,\r\n    //         data,\r\n    //         animation: {\r\n    //             easing: typeAnimation\r\n    //         },\r\n    //         backgroundColor,\r\n    //         barPercentage,\r\n    //         barThickness,\r\n    //         maxBarThickness,\r\n    //         base,\r\n    //         borderColor,\r\n    //         borderRadius,\r\n    //         borderWidth,\r\n    //         categoryPercentage,\r\n    //         hoverBackgroundColor,\r\n    //         hoverBorderColor,\r\n    //         hoverBorderWidth\r\n    //     };\r\n    //     if(xAxisID){\r\n    //         result.xAxisID = xAxisID;\r\n    //     }\r\n    //     if(yAxisID){\r\n    //         result.yAxisID = yAxisID;\r\n    //     }\r\n    //     if(stack){\r\n    //         result.stack = stack;\r\n    //     }\r\n    //     return result;\r\n    // }\r\n    genDatasetLineChart(label: string, data: number[], typeAnimation: EasingFunction,\r\n                        backgroundColor: string, borderColor: string, borderCapStyle: CanvasLineCap, borderJoinStyle: CanvasLineJoin,\r\n                        fill: boolean, tension: number, isLineDash: boolean, hoverBackgroundColor: string, hoverBorderColor: string,\r\n                        hoverBorderWidth: number,pointBackgroundColor: string,pointBorderColor: string,pointBorderWidth: number, xAxisID: string, yAxisID: string): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"line\",\r\n            label,\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            borderColor,\r\n            borderDash: isLineDash ? [10,5] : [0,0],\r\n            borderCapStyle,\r\n            borderJoinStyle,\r\n            borderWidth: 2,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            pointBackgroundColor,\r\n            pointBorderColor,\r\n            pointBorderWidth,\r\n            fill,\r\n            tension\r\n        };\r\n        if(xAxisID){\r\n            result.xAxisID = xAxisID;\r\n        }\r\n        if(yAxisID){\r\n            result.yAxisID = yAxisID;\r\n        }\r\n        return result;\r\n    }\r\n\r\n    genDatasetBubbleChart(label: string, data: Array<{x: number, y: number, r: number}>,\r\n        backgroundColor: string, borderColor: string, borderWidth: number, hoverBackgroundColor: string, hoverBorderColor: string,\r\n        hoverBorderWidth: number, pointStyle: PointStyle): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"bubble\",\r\n            label,\r\n            data,\r\n            backgroundColor,\r\n            borderColor,\r\n            borderWidth,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            pointStyle\r\n        };\r\n        return result;\r\n    }\r\n\r\n    genDatasetDoughnutChart(data: number[], typeAnimation: EasingFunction, backgroundColor: string[], borderColor: string[],\r\n        borderWidth: number, hoverBackgroundColor: string[], hoverBorderColor: string[], hoverBorderWidth: number, weight: number): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"doughnut\",\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            borderColor,\r\n            borderWidth,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            weight\r\n        };\r\n        return result;\r\n    }\r\n\r\n    genDatasetPieChart(data: number[], typeAnimation: EasingFunction, backgroundColor: string[], borderColor: string[],\r\n        borderWidth: number, hoverBackgroundColor: string[], hoverBorderColor: string[], hoverBorderWidth: number, weight: number): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"pie\",\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            borderColor,\r\n            borderWidth,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            weight\r\n        };\r\n        return result;\r\n    }\r\n\r\n    genDatasetPolarAreaChart( data: number[], typeAnimation: EasingFunction, backgroundColor: string[], borderColor: string,\r\n        borderWidth: number, hoverBackgroundColor: string, hoverBorderColor: string, hoverBorderWidth: number, weight: number): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"polarArea\",\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            angle: Math.max(...data),\r\n            borderColor,\r\n            borderWidth,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            weight\r\n        };\r\n        return result;\r\n    }\r\n\r\n    genDatasetRadarChart(label: string, data: number[], typeAnimation: EasingFunction,\r\n                        backgroundColor: string, borderColor: string, borderCapStyle: CanvasLineCap, borderJoinStyle: CanvasLineJoin,\r\n                        fill: boolean, tension: number, isDash:boolean, hoverBackgroundColor: string, hoverBorderColor: string,\r\n                        hoverBorderWidth: number, pointBackgroundColor: string, pointBorderColor: string, pointBorderWidth: number,borderWidth: number): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"radar\",\r\n            label,\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            borderCapStyle,\r\n            borderColor,\r\n            borderDash: isDash ? [10,5] : [0, 0],\r\n            borderWidth,\r\n            borderJoinStyle,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            pointBackgroundColor,\r\n            pointBorderColor,\r\n            pointBorderWidth,\r\n            fill,\r\n            tension\r\n        };\r\n        return result;\r\n    }\r\n\r\n    genDatasetScatterChart(label: string, data: Array<{x: number, y: number, r: number}>, typeAnimation: EasingFunction,\r\n        backgroundColor: string, borderColor: string, borderWidth: number, hoverBackgroundColor: string, hoverBorderColor: string, hoverBorderWidth: number,\r\n        isDash:boolean, fill: boolean, tension: number,borderCapStyle: CanvasLineCap, borderJoinStyle: CanvasLineJoin, pointBackgroundColor: string, pointBorderColor: string, pointBorderWidth: number): ChartDataset{\r\n        let result: ChartDataset = {\r\n            type: \"scatter\",\r\n            label,\r\n            data,\r\n            animation: {\r\n                easing: typeAnimation\r\n            },\r\n            backgroundColor,\r\n            borderCapStyle,\r\n            borderColor,\r\n            borderDash: isDash ? [10,5] : [0, 0],\r\n            borderWidth,\r\n            borderJoinStyle,\r\n            hoverBackgroundColor,\r\n            hoverBorderColor,\r\n            hoverBorderWidth,\r\n            pointBackgroundColor,\r\n            pointBorderColor,\r\n            pointBorderWidth,\r\n            fill,\r\n            tension\r\n        };\r\n        return result;\r\n    }\r\n\r\n    // tooltipItems: {chart: object, dataIndex: number, datasetIndex: number, formattedValue: string, label: string, raw: number, dataset.data: number[]}\r\n\r\n    // {\r\n    //     family: \"roboto\",\r\n    //     lineHeight: 1,\r\n    //     size: 12,\r\n    //     style: \"inherit\",\r\n    //     weight: \"bold\"\r\n    // }\r\n\r\n\r\n\r\n    genOptionTooltip(mode: InteractionMode, intersect: boolean, backgroundColor: string,bodyColor: string, bodyAlign: string, bodyFont: any,\r\n        borderColor: string, borderWidth: number, bodySpacing: number, boxHeight: number, boxWidth: number, radius: number, caretSize: number,\r\n        titleAlign: string, titleColor: string, titleFont: any, footerColor: string, patternTitle: string, patternLabel: string, patternFooter: string){\r\n        let tooltip = {\r\n            mode,\r\n            intersect,\r\n            backgroundColor,\r\n            bodyColor,\r\n            bodyAlign,//center left right\r\n            bodyFont,\r\n            borderColor,\r\n            borderWidth,\r\n            bodySpacing,\r\n            boxHeight,\r\n            boxWidth,\r\n            cornerRadius: {\r\n                bottomLeft: radius,\r\n                bottomRight: radius,\r\n                topLeft: radius,\r\n                topRight: radius\r\n            },\r\n            caretSize,\r\n            titleAlign,\r\n            titleColor,\r\n            titleFont,\r\n            footerColor,\r\n        }\r\n        if(patternTitle || patternLabel || patternFooter){\r\n            let callbacks = {};\r\n            if(patternTitle){\r\n                callbacks['title'] = (tooltipItems) => {\r\n                    let result = patternTitle;\r\n                    this.CustomFunctionTooltip.forEach(item => {\r\n                        if(result.indexOf(item.value) >= 0){\r\n                            result = result.replace(item.value, item.action(tooltipItems));\r\n                        }\r\n                    })\r\n                    return result.split(\"|\");\r\n                }\r\n            }\r\n            if(patternLabel){\r\n                callbacks['label'] = (tooltipItems) => {\r\n                    let result = patternLabel;\r\n                    this.CustomFunctionTooltip.forEach(item => {\r\n                        if(result.indexOf(item.value) >= 0){\r\n                            result = result.replace(item.value, item.action(tooltipItems));\r\n                        }\r\n                    })\r\n                    return result.split(\"|\");\r\n                }\r\n            }\r\n            if(patternFooter){\r\n                callbacks['footer'] = (tooltipItems) => {\r\n                    let result = patternFooter;\r\n                    this.CustomFunctionTooltip.forEach(item => {\r\n                        if(result.indexOf(item.value) >= 0){\r\n                            result = result.replace(item.value, item.action(tooltipItems));\r\n                        }\r\n                    })\r\n                    return result.split(\"|\");\r\n                }\r\n            }\r\n            tooltip['callbacks'] = callbacks;\r\n        }\r\n        return tooltip;\r\n    }\r\n\r\n    genOptionLegend(titleColor: string, titleDisplay: boolean, titleFont, titleText: string, align: Align, display: boolean,\r\n        labelColor: string, labelFont, boxWidth: number, boxHeight: number, position: LayoutPosition, patternBody: string){\r\n        let legend = {\r\n            title: {\r\n                color: titleColor,\r\n                display: titleDisplay,\r\n                font: titleFont,\r\n                text: titleText\r\n            },\r\n            align,\r\n            display,\r\n            labels: {\r\n                color: labelColor,\r\n                font: labelFont,\r\n                boxWidth,\r\n                boxHeight,\r\n                generateLabels(chart) {\r\n                    let result: LegendItem[] = [];\r\n                    let datasets = chart.data.datasets;\r\n                    let labels = chart.data.labels;\r\n                    if(!datasets || datasets.length == 0) return null;\r\n                    if(datasets[0].type == CONSTANTS.CHART_TYPE.PIE || datasets[0].type == CONSTANTS.CHART_TYPE.DOUGHNUT){\r\n                        labels.forEach((label, i)=>{\r\n                            let text = `${label} (${Intl.NumberFormat('vi-VI').format(parseInt(datasets[0].data[i].toString()))})`;\r\n                            if(patternBody != undefined && patternBody != null){\r\n                                text = patternBody.replaceAll(\"{%l}\", label);\r\n                                text = text.replaceAll(\"{%v}\", Intl.NumberFormat('vi-VI').format(parseInt(datasets[0].data[i].toString())));\r\n                            }\r\n                            result.push({\r\n                                text: text,\r\n                                index: i,\r\n                                fillStyle: datasets[0].backgroundColor[i]\r\n                            })\r\n                        })\r\n                    }else{\r\n                        datasets.forEach((dataset, index)=>{\r\n                            let total = 0;\r\n                            let data = dataset.data;\r\n                            data.forEach(el => total += parseInt(el.toString()));\r\n                            let strTotal = Intl.NumberFormat('vi-VI').format(total);\r\n\r\n                            let text = `${dataset.label} (${strTotal})`;\r\n                            if(patternBody != undefined && patternBody != null){\r\n                                text = patternBody.replaceAll(\"{%l}\", dataset.label);\r\n                                text = text.replaceAll(\"{%v}\", strTotal);\r\n                            }\r\n                            result.push({\r\n                                text: text,\r\n                                datasetIndex: index,\r\n                                fillStyle: dataset.backgroundColor.toString(),\r\n                                hidden: false,\r\n                            })\r\n                        })\r\n                    }\r\n                    //sort legend biểu đồ ngưỡng\r\n                    const order = [\"cảnh báo\", \"xem xét\", \"an toàn\"];\r\n                    result.sort((a, b) => {\r\n                        const indexA = order.findIndex(item => a.text.toLowerCase().startsWith(item));\r\n                        const indexB = order.findIndex(item => b.text.toLowerCase().startsWith(item));\r\n                        return indexA - indexB;\r\n                    });\r\n\r\n                    return result;\r\n                },\r\n            },\r\n            position,\r\n        }\r\n        return legend;\r\n    }\r\n\r\n    genOptionsTitleAndSubTitle(align: Align, color: string, display: boolean, font, position: 'top' | 'left' | 'bottom' | 'right', text: string){\r\n        let title = {\r\n            align,\r\n            color,\r\n            display,\r\n            font,\r\n            position,\r\n            text\r\n        }\r\n        return title;\r\n    }\r\n\r\n    genOptionScaleX(stacked: boolean, beginAtZero: boolean, position: 'top' | 'left' | 'bottom' | 'right', tickColor: string,rotation: number,\r\n    drawOnChartArea: boolean, colorGrid: string, borderGridColor: string, isDash: boolean, titleText: string, titleAlign: Align,\r\n    titleDisplay: boolean, titleColor: string, titleFont){\r\n        let x = {\r\n            beginAtZero,\r\n            position,\r\n            ticks: {\r\n                color: tickColor,\r\n                minRotation: rotation,\r\n                // callback: function (value) {\r\n                //     return Number.isInteger(value) ? value : null; // Chỉ hiển thị số nguyên\r\n                // },\r\n            },\r\n            grid: {\r\n                drawOnChartArea,\r\n                color: colorGrid,\r\n                borderColor: borderGridColor,\r\n                borderDash: isDash ? [5,5] : [0,0]\r\n            },\r\n            stacked,\r\n            title: {\r\n                text: titleText,\r\n                align: titleAlign,\r\n                display: titleDisplay,\r\n                color: titleColor,\r\n                font: titleFont\r\n            }\r\n        }\r\n        return x;\r\n    }\r\n\r\n    genOptionScaleY(stacked: boolean, beginAtZero: boolean, position: 'top' | 'left' | 'bottom' | 'right', tickColor: string,rotation: number,\r\n    drawOnChartArea: boolean, colorGrid: string, borderGridColor: string, isDash: boolean, titleText: string, titleAlign: Align,\r\n    titleDisplay: boolean, titleColor: string, titleFont){\r\n        let y = {\r\n            stacked,\r\n            position,\r\n            beginAtZero,\r\n            ticks: {\r\n                color: tickColor,\r\n                align: \"center\",\r\n                minRotation: rotation\r\n            },\r\n            grid: {\r\n                color: colorGrid,\r\n                borderColor: borderGridColor,\r\n                drawOnChartArea,\r\n                borderDash: isDash ? [5,5] : [0,0]\r\n            },\r\n            title: {\r\n                text: titleText,\r\n                align: titleAlign,\r\n                display: titleDisplay,\r\n                color: titleColor,\r\n                font: titleFont\r\n            }\r\n        }\r\n        return y;\r\n    }\r\n\r\n    getOptionBoxValue(isShowBoxValue: boolean){\r\n        return {\r\n            align: \"center\",\r\n            anchor(context){\r\n                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                    return \"end\"\r\n                }else{\r\n                    return \"center\";\r\n                }\r\n            },\r\n            backgroundColor(context){\r\n                // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                //     return \"transparent\";\r\n                // }else{\r\n                    return \"#FFFAFA\";\r\n                // }\r\n            },\r\n            borderColor(context){\r\n                // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                //     return \"none\";\r\n                // }else{\r\n                    return \"#8B8989\"\r\n                // }\r\n            },\r\n            borderRadius: 8,\r\n            borderWidth(context) {\r\n                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                    return 1;\r\n                }else{\r\n                    return 1;\r\n                }\r\n            },\r\n            color(context){\r\n                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                    return \"#000\";\r\n                }else{\r\n                    return \"#528B8B\"\r\n                }\r\n            },\r\n            display(context) {\r\n                return isShowBoxValue;\r\n                // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                //     return true;\r\n                // }else{\r\n                //     return false;\r\n                // }\r\n            },\r\n            font: {\r\n                size: 14,\r\n            },\r\n            formatter(value, context) {\r\n                let strValue = Intl.NumberFormat('vi-VI').format(value);\r\n                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){\r\n                    return strValue;\r\n                }else{\r\n                    return strValue;\r\n                }\r\n            },\r\n            opacity: 1,\r\n            padding:{\r\n                bottom: 1,\r\n                left: 8,\r\n                right: 8,\r\n                top: 1\r\n            },\r\n            rotation: 0,\r\n            textAlign: \"center\",\r\n        }\r\n    }\r\n\r\n    genOptions(isBarHorizontal, tooltip, legend, title, subtitle, scales, datalabels): ChartOptions {\r\n        let result : ChartOptions = {\r\n            indexAxis: isBarHorizontal ? \"y\" : \"x\",\r\n            maintainAspectRatio: false,\r\n            aspectRatio: 0.6,\r\n            plugins: {\r\n                tooltip,\r\n                legend,\r\n                title,\r\n                subtitle,\r\n                datalabels\r\n\r\n            },\r\n        }\r\n        if(scales != null){\r\n            result.scales = scales;\r\n        }\r\n        return result;\r\n    }\r\n\r\n\r\n}\r\n\r\nexport const commonChart = new CommonChart();\r\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,iCAAiC;AAG3D,MAAMC,WAAW;EAGbC,YAAA;IACI,IAAI,CAACC,qBAAqB,GAAG,CACzB;MACIC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAGC,YAAY,IAAI;QACrB,IAAIC,KAAK,GAAG,CAAC;QACb,IAAIC,SAAS,GAAGF,YAAY,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,IAAI;QAC5C,IAAGF,SAAS,IAAI,KAAK,IAAIA,SAAS,IAAI,UAAU,EAAC;UAC7CF,YAAY,CAACK,OAAO,CAACC,EAAE,IAAIL,KAAK,IAAIM,QAAQ,CAACD,EAAE,CAACE,GAAG,GAAC,EAAE,CAAC,CAAC;SAC3D,MAAI;UACDR,YAAY,CAAC,CAAC,CAAC,CAACG,OAAO,CAACM,IAAI,CAACJ,OAAO,CAACC,EAAE,IAAIL,KAAK,IAAIM,QAAQ,CAACD,EAAE,GAAC,EAAE,CAAC,CAAC;;QAExE,OAAOI,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC;MACnD;KACH,EACD;MACIJ,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAGC,YAAY,IAAI;QACrB,IAAG,OAAO,IAAIA,YAAY,EAAC;UACvB,OAAOA,YAAY,CAACa,KAAK;;QAE7B,OAAOb,YAAY,CAAC,CAAC,CAAC,CAACa,KAAK;MAChC;KACH,EACD;MACIhB,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAGe,WAAW,IAAI;QACpB,OAAOA,WAAW,CAACX,OAAO,CAACU,KAAK;MACpC;KACH,EACD;MACIhB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAGe,WAAW,IAAI;QACpB,OAAOA,WAAW,CAACC,cAAc;MACrC;KACH,EACD;MACIlB,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAGe,WAAW,IAAI;QACpB,IAAIE,QAAQ,GAAGF,WAAW,CAACG,KAAK,CAACR,IAAI,CAACO,QAAQ;QAC9C,IAAIf,KAAK,GAAG,CAAC;QACb,IAAIC,SAAS,GAAGc,QAAQ,CAAC,CAAC,CAAC,CAACZ,IAAI;QAChC,IAAGF,SAAS,IAAI,KAAK,IAAIA,SAAS,IAAI,UAAU,EAAC;UAC7Cc,QAAQ,CAACX,OAAO,CAACC,EAAE,IAAIL,KAAK,IAAIM,QAAQ,CAACD,EAAE,CAACG,IAAI,CAACK,WAAW,CAACI,SAAS,CAAC,GAAC,EAAE,CAAC,CAAC;SAC/E,MAAI;UACDJ,WAAW,CAACX,OAAO,CAACM,IAAI,CAACJ,OAAO,CAACC,EAAE,IAAIL,KAAK,IAAIM,QAAQ,CAACD,EAAE,GAAC,EAAE,CAAC,CAAC;;QAEpE,OAAOa,IAAI,CAACC,KAAK,CAACN,WAAW,CAACN,GAAG,GAACP,KAAK,GAAC,GAAG,CAAC;MAChD;KACH,CACJ;IACD,IAAI,CAACoB,oBAAoB,GAAG,CACxB;MACIxB,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACV,EACD;MACID,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACV,CACJ;EACL;EAEAwB,kBAAkBA,CAACT,KAAa,EAAEJ,IAAc,EAAEc,aAA6B,EAC3DC,OAAe,EAACC,OAAgB,EAAEC,KAAc,EAChDC,eAAA,GAA4B,IAAI,EAAEC,aAAA,GAAwB,GAAG,EAAEC,YAAA,GAAuB,EAAE,EACxFC,eAAA,GAA0B,EAAE,EAAEC,IAAA,GAAe,EAAE,EAAEC,WAAA,GAAyB,IAAI,EAC9EC,YAAA,GAAuB,CAAC,EAAEC,WAAA,GAAsB,CAAC,EAAEC,kBAAA,GAA6B,CAAC,EACjFC,oBAAA,GAAkC,IAAI,EAAEC,gBAAA,GAA8B,IAAI,EAAEC,gBAAA,GAA2B,CAAC;IACxH,IAAIC,MAAM,GAAiB;MACvBnC,IAAI,EAAE,KAAK;MACXS,KAAK;MACLJ,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACfC,aAAa;MACbC,YAAY;MACZC,eAAe;MACfC,IAAI;MACJC,WAAW;MACXC,YAAY;MACZC,WAAW;MACXC,kBAAkB;MAClBC,oBAAoB;MACpBC,gBAAgB;MAChBC;KACH;IACD,IAAGd,OAAO,EAAC;MACPe,MAAM,CAACf,OAAO,GAAGA,OAAO;;IAE5B,IAAGC,OAAO,EAAC;MACPc,MAAM,CAACd,OAAO,GAAGA,OAAO;;IAE5B,IAAGC,KAAK,EAAC;MACLa,MAAM,CAACb,KAAK,GAAGA,KAAK;;IAExB,OAAOa,MAAM;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,mBAAmBA,CAAC7B,KAAa,EAAEJ,IAAc,EAAEc,aAA6B,EAC5DI,eAAuB,EAAEK,WAAmB,EAAEW,cAA6B,EAAEC,eAA+B,EAC5GC,IAAa,EAAEC,OAAe,EAAEC,UAAmB,EAAEX,oBAA4B,EAAEC,gBAAwB,EAC3GC,gBAAwB,EAACU,oBAA4B,EAACC,gBAAwB,EAACC,gBAAwB,EAAE1B,OAAe,EAAEC,OAAe;IACzJ,IAAIc,MAAM,GAAiB;MACvBnC,IAAI,EAAE,MAAM;MACZS,KAAK;MACLJ,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACfK,WAAW;MACXmB,UAAU,EAAEJ,UAAU,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC;MACvCJ,cAAc;MACdC,eAAe;MACfV,WAAW,EAAE,CAAC;MACdE,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBU,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBL,IAAI;MACJC;KACH;IACD,IAAGtB,OAAO,EAAC;MACPe,MAAM,CAACf,OAAO,GAAGA,OAAO;;IAE5B,IAAGC,OAAO,EAAC;MACPc,MAAM,CAACd,OAAO,GAAGA,OAAO;;IAE5B,OAAOc,MAAM;EACjB;EAEAa,qBAAqBA,CAACvC,KAAa,EAAEJ,IAA8C,EAC/EkB,eAAuB,EAAEK,WAAmB,EAAEE,WAAmB,EAAEE,oBAA4B,EAAEC,gBAAwB,EACzHC,gBAAwB,EAAEe,UAAsB;IAChD,IAAId,MAAM,GAAiB;MACvBnC,IAAI,EAAE,QAAQ;MACdS,KAAK;MACLJ,IAAI;MACJkB,eAAe;MACfK,WAAW;MACXE,WAAW;MACXE,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBe;KACH;IACD,OAAOd,MAAM;EACjB;EAEAe,uBAAuBA,CAAC7C,IAAc,EAAEc,aAA6B,EAAEI,eAAyB,EAAEK,WAAqB,EACnHE,WAAmB,EAAEE,oBAA8B,EAAEC,gBAA0B,EAAEC,gBAAwB,EAAEiB,MAAc;IACzH,IAAIhB,MAAM,GAAiB;MACvBnC,IAAI,EAAE,UAAU;MAChBK,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACfK,WAAW;MACXE,WAAW;MACXE,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBiB;KACH;IACD,OAAOhB,MAAM;EACjB;EAEAiB,kBAAkBA,CAAC/C,IAAc,EAAEc,aAA6B,EAAEI,eAAyB,EAAEK,WAAqB,EAC9GE,WAAmB,EAAEE,oBAA8B,EAAEC,gBAA0B,EAAEC,gBAAwB,EAAEiB,MAAc;IACzH,IAAIhB,MAAM,GAAiB;MACvBnC,IAAI,EAAE,KAAK;MACXK,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACfK,WAAW;MACXE,WAAW;MACXE,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBiB;KACH;IACD,OAAOhB,MAAM;EACjB;EAEAkB,wBAAwBA,CAAEhD,IAAc,EAAEc,aAA6B,EAAEI,eAAyB,EAAEK,WAAmB,EACnHE,WAAmB,EAAEE,oBAA4B,EAAEC,gBAAwB,EAAEC,gBAAwB,EAAEiB,MAAc;IACrH,IAAIhB,MAAM,GAAiB;MACvBnC,IAAI,EAAE,WAAW;MACjBK,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACf+B,KAAK,EAAEvC,IAAI,CAACwC,GAAG,CAAC,GAAGlD,IAAI,CAAC;MACxBuB,WAAW;MACXE,WAAW;MACXE,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBiB;KACH;IACD,OAAOhB,MAAM;EACjB;EAEAqB,oBAAoBA,CAAC/C,KAAa,EAAEJ,IAAc,EAAEc,aAA6B,EAC7DI,eAAuB,EAAEK,WAAmB,EAAEW,cAA6B,EAAEC,eAA+B,EAC5GC,IAAa,EAAEC,OAAe,EAAEe,MAAc,EAAEzB,oBAA4B,EAAEC,gBAAwB,EACtGC,gBAAwB,EAAEU,oBAA4B,EAAEC,gBAAwB,EAAEC,gBAAwB,EAAChB,WAAmB;IAC9I,IAAIK,MAAM,GAAiB;MACvBnC,IAAI,EAAE,OAAO;MACbS,KAAK;MACLJ,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACfgB,cAAc;MACdX,WAAW;MACXmB,UAAU,EAAEU,MAAM,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACpC3B,WAAW;MACXU,eAAe;MACfR,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBU,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBL,IAAI;MACJC;KACH;IACD,OAAOP,MAAM;EACjB;EAEAuB,sBAAsBA,CAACjD,KAAa,EAAEJ,IAA8C,EAAEc,aAA6B,EAC/GI,eAAuB,EAAEK,WAAmB,EAAEE,WAAmB,EAAEE,oBAA4B,EAAEC,gBAAwB,EAAEC,gBAAwB,EACnJuB,MAAc,EAAEhB,IAAa,EAAEC,OAAe,EAACH,cAA6B,EAAEC,eAA+B,EAAEI,oBAA4B,EAAEC,gBAAwB,EAAEC,gBAAwB;IAC/L,IAAIX,MAAM,GAAiB;MACvBnC,IAAI,EAAE,SAAS;MACfS,KAAK;MACLJ,IAAI;MACJ+B,SAAS,EAAE;QACPC,MAAM,EAAElB;OACX;MACDI,eAAe;MACfgB,cAAc;MACdX,WAAW;MACXmB,UAAU,EAAEU,MAAM,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACpC3B,WAAW;MACXU,eAAe;MACfR,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBU,oBAAoB;MACpBC,gBAAgB;MAChBC,gBAAgB;MAChBL,IAAI;MACJC;KACH;IACD,OAAOP,MAAM;EACjB;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAIAwB,gBAAgBA,CAACC,IAAqB,EAAEC,SAAkB,EAAEtC,eAAuB,EAACuC,SAAiB,EAAEC,SAAiB,EAAEC,QAAa,EACnIpC,WAAmB,EAAEE,WAAmB,EAAEmC,WAAmB,EAAEC,SAAiB,EAAEC,QAAgB,EAAEC,MAAc,EAAEC,SAAiB,EACrIC,UAAkB,EAAEC,UAAkB,EAAEC,SAAc,EAAEC,WAAmB,EAAEC,YAAoB,EAAEC,YAAoB,EAAEC,aAAqB;IAC9I,IAAIC,OAAO,GAAG;MACVjB,IAAI;MACJC,SAAS;MACTtC,eAAe;MACfuC,SAAS;MACTC,SAAS;MACTC,QAAQ;MACRpC,WAAW;MACXE,WAAW;MACXmC,WAAW;MACXC,SAAS;MACTC,QAAQ;MACRW,YAAY,EAAE;QACVC,UAAU,EAAEX,MAAM;QAClBY,WAAW,EAAEZ,MAAM;QACnBa,OAAO,EAAEb,MAAM;QACfc,QAAQ,EAAEd;OACb;MACDC,SAAS;MACTC,UAAU;MACVC,UAAU;MACVC,SAAS;MACTC;KACH;IACD,IAAGC,YAAY,IAAIC,YAAY,IAAIC,aAAa,EAAC;MAC7C,IAAIO,SAAS,GAAG,EAAE;MAClB,IAAGT,YAAY,EAAC;QACZS,SAAS,CAAC,OAAO,CAAC,GAAIvF,YAAY,IAAI;UAClC,IAAIuC,MAAM,GAAGuC,YAAY;UACzB,IAAI,CAAClF,qBAAqB,CAACS,OAAO,CAACmF,IAAI,IAAG;YACtC,IAAGjD,MAAM,CAACkD,OAAO,CAACD,IAAI,CAAC1F,KAAK,CAAC,IAAI,CAAC,EAAC;cAC/ByC,MAAM,GAAGA,MAAM,CAACmD,OAAO,CAACF,IAAI,CAAC1F,KAAK,EAAE0F,IAAI,CAACzF,MAAM,CAACC,YAAY,CAAC,CAAC;;UAEtE,CAAC,CAAC;UACF,OAAOuC,MAAM,CAACoD,KAAK,CAAC,GAAG,CAAC;QAC5B,CAAC;;MAEL,IAAGZ,YAAY,EAAC;QACZQ,SAAS,CAAC,OAAO,CAAC,GAAIvF,YAAY,IAAI;UAClC,IAAIuC,MAAM,GAAGwC,YAAY;UACzB,IAAI,CAACnF,qBAAqB,CAACS,OAAO,CAACmF,IAAI,IAAG;YACtC,IAAGjD,MAAM,CAACkD,OAAO,CAACD,IAAI,CAAC1F,KAAK,CAAC,IAAI,CAAC,EAAC;cAC/ByC,MAAM,GAAGA,MAAM,CAACmD,OAAO,CAACF,IAAI,CAAC1F,KAAK,EAAE0F,IAAI,CAACzF,MAAM,CAACC,YAAY,CAAC,CAAC;;UAEtE,CAAC,CAAC;UACF,OAAOuC,MAAM,CAACoD,KAAK,CAAC,GAAG,CAAC;QAC5B,CAAC;;MAEL,IAAGX,aAAa,EAAC;QACbO,SAAS,CAAC,QAAQ,CAAC,GAAIvF,YAAY,IAAI;UACnC,IAAIuC,MAAM,GAAGyC,aAAa;UAC1B,IAAI,CAACpF,qBAAqB,CAACS,OAAO,CAACmF,IAAI,IAAG;YACtC,IAAGjD,MAAM,CAACkD,OAAO,CAACD,IAAI,CAAC1F,KAAK,CAAC,IAAI,CAAC,EAAC;cAC/ByC,MAAM,GAAGA,MAAM,CAACmD,OAAO,CAACF,IAAI,CAAC1F,KAAK,EAAE0F,IAAI,CAACzF,MAAM,CAACC,YAAY,CAAC,CAAC;;UAEtE,CAAC,CAAC;UACF,OAAOuC,MAAM,CAACoD,KAAK,CAAC,GAAG,CAAC;QAC5B,CAAC;;MAELV,OAAO,CAAC,WAAW,CAAC,GAAGM,SAAS;;IAEpC,OAAON,OAAO;EAClB;EAEAW,eAAeA,CAACjB,UAAkB,EAAEkB,YAAqB,EAAEjB,SAAS,EAAEkB,SAAiB,EAAEC,KAAY,EAAEC,OAAgB,EACnHC,UAAkB,EAAEC,SAAS,EAAE3B,QAAgB,EAAED,SAAiB,EAAE6B,QAAwB,EAAEC,WAAmB;IACjH,IAAIC,MAAM,GAAG;MACTC,KAAK,EAAE;QACHC,KAAK,EAAE5B,UAAU;QACjBqB,OAAO,EAAEH,YAAY;QACrBW,IAAI,EAAE5B,SAAS;QACf6B,IAAI,EAAEX;OACT;MACDC,KAAK;MACLC,OAAO;MACPU,MAAM,EAAE;QACJH,KAAK,EAAEN,UAAU;QACjBO,IAAI,EAAEN,SAAS;QACf3B,QAAQ;QACRD,SAAS;QACTqC,cAAcA,CAAC1F,KAAK;UAChB,IAAIsB,MAAM,GAAiB,EAAE;UAC7B,IAAIvB,QAAQ,GAAGC,KAAK,CAACR,IAAI,CAACO,QAAQ;UAClC,IAAI0F,MAAM,GAAGzF,KAAK,CAACR,IAAI,CAACiG,MAAM;UAC9B,IAAG,CAAC1F,QAAQ,IAAIA,QAAQ,CAAC4F,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;UACjD,IAAG5F,QAAQ,CAAC,CAAC,CAAC,CAACZ,IAAI,IAAIX,SAAS,CAACoH,UAAU,CAACC,GAAG,IAAI9F,QAAQ,CAAC,CAAC,CAAC,CAACZ,IAAI,IAAIX,SAAS,CAACoH,UAAU,CAACE,QAAQ,EAAC;YACjGL,MAAM,CAACrG,OAAO,CAAC,CAACQ,KAAK,EAAEmG,CAAC,KAAG;cACvB,IAAIP,IAAI,GAAG,GAAG5F,KAAK,KAAKH,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACL,QAAQ,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACP,IAAI,CAACuG,CAAC,CAAC,CAACC,QAAQ,EAAE,CAAC,CAAC,GAAG;cACtG,IAAGb,WAAW,IAAIc,SAAS,IAAId,WAAW,IAAI,IAAI,EAAC;gBAC/CK,IAAI,GAAGL,WAAW,CAACe,UAAU,CAAC,MAAM,EAAEtG,KAAK,CAAC;gBAC5C4F,IAAI,GAAGA,IAAI,CAACU,UAAU,CAAC,MAAM,EAAEzG,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACL,QAAQ,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACP,IAAI,CAACuG,CAAC,CAAC,CAACC,QAAQ,EAAE,CAAC,CAAC,CAAC;;cAE/G1E,MAAM,CAAC6E,IAAI,CAAC;gBACRX,IAAI,EAAEA,IAAI;gBACVY,KAAK,EAAEL,CAAC;gBACRM,SAAS,EAAEtG,QAAQ,CAAC,CAAC,CAAC,CAACW,eAAe,CAACqF,CAAC;eAC3C,CAAC;YACN,CAAC,CAAC;WACL,MAAI;YACDhG,QAAQ,CAACX,OAAO,CAAC,CAACF,OAAO,EAAEkH,KAAK,KAAG;cAC/B,IAAIpH,KAAK,GAAG,CAAC;cACb,IAAIQ,IAAI,GAAGN,OAAO,CAACM,IAAI;cACvBA,IAAI,CAACJ,OAAO,CAACC,EAAE,IAAIL,KAAK,IAAIM,QAAQ,CAACD,EAAE,CAAC2G,QAAQ,EAAE,CAAC,CAAC;cACpD,IAAIM,QAAQ,GAAG7G,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC;cAEvD,IAAIwG,IAAI,GAAG,GAAGtG,OAAO,CAACU,KAAK,KAAK0G,QAAQ,GAAG;cAC3C,IAAGnB,WAAW,IAAIc,SAAS,IAAId,WAAW,IAAI,IAAI,EAAC;gBAC/CK,IAAI,GAAGL,WAAW,CAACe,UAAU,CAAC,MAAM,EAAEhH,OAAO,CAACU,KAAK,CAAC;gBACpD4F,IAAI,GAAGA,IAAI,CAACU,UAAU,CAAC,MAAM,EAAEI,QAAQ,CAAC;;cAE5ChF,MAAM,CAAC6E,IAAI,CAAC;gBACRX,IAAI,EAAEA,IAAI;gBACVe,YAAY,EAAEH,KAAK;gBACnBC,SAAS,EAAEnH,OAAO,CAACwB,eAAe,CAACsF,QAAQ,EAAE;gBAC7CQ,MAAM,EAAE;eACX,CAAC;YACN,CAAC,CAAC;;UAEN;UACA,MAAMC,KAAK,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC;UAChDnF,MAAM,CAACoF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACjB,MAAMC,MAAM,GAAGJ,KAAK,CAACK,SAAS,CAACvC,IAAI,IAAIoC,CAAC,CAACnB,IAAI,CAACuB,WAAW,EAAE,CAACC,UAAU,CAACzC,IAAI,CAAC,CAAC;YAC7E,MAAM0C,MAAM,GAAGR,KAAK,CAACK,SAAS,CAACvC,IAAI,IAAIqC,CAAC,CAACpB,IAAI,CAACuB,WAAW,EAAE,CAACC,UAAU,CAACzC,IAAI,CAAC,CAAC;YAC7E,OAAOsC,MAAM,GAAGI,MAAM;UAC1B,CAAC,CAAC;UAEF,OAAO3F,MAAM;QACjB;OACH;MACD4D;KACH;IACD,OAAOE,MAAM;EACjB;EAEA8B,0BAA0BA,CAACpC,KAAY,EAAEQ,KAAa,EAAEP,OAAgB,EAAEQ,IAAI,EAAEL,QAA6C,EAAEM,IAAY;IACvI,IAAIH,KAAK,GAAG;MACRP,KAAK;MACLQ,KAAK;MACLP,OAAO;MACPQ,IAAI;MACJL,QAAQ;MACRM;KACH;IACD,OAAOH,KAAK;EAChB;EAEA8B,eAAeA,CAACC,OAAgB,EAAEC,WAAoB,EAAEnC,QAA6C,EAAEoC,SAAiB,EAACC,QAAgB,EACzIC,eAAwB,EAAEC,SAAiB,EAAEC,eAAuB,EAAE9E,MAAe,EAAEiC,SAAiB,EAAEpB,UAAiB,EAC3HmB,YAAqB,EAAElB,UAAkB,EAAEC,SAAS;IAChD,IAAIgE,CAAC,GAAG;MACJN,WAAW;MACXnC,QAAQ;MACR0C,KAAK,EAAE;QACHtC,KAAK,EAAEgC,SAAS;QAChBO,WAAW,EAAEN;QACb;QACA;QACA;OACH;;MACDO,IAAI,EAAE;QACFN,eAAe;QACflC,KAAK,EAAEmC,SAAS;QAChB1G,WAAW,EAAE2G,eAAe;QAC5BxF,UAAU,EAAEU,MAAM,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC;OACpC;MACDwE,OAAO;MACP/B,KAAK,EAAE;QACHG,IAAI,EAAEX,SAAS;QACfC,KAAK,EAAErB,UAAU;QACjBsB,OAAO,EAAEH,YAAY;QACrBU,KAAK,EAAE5B,UAAU;QACjB6B,IAAI,EAAE5B;;KAEb;IACD,OAAOgE,CAAC;EACZ;EAEAI,eAAeA,CAACX,OAAgB,EAAEC,WAAoB,EAAEnC,QAA6C,EAAEoC,SAAiB,EAACC,QAAgB,EACzIC,eAAwB,EAAEC,SAAiB,EAAEC,eAAuB,EAAE9E,MAAe,EAAEiC,SAAiB,EAAEpB,UAAiB,EAC3HmB,YAAqB,EAAElB,UAAkB,EAAEC,SAAS;IAChD,IAAIqE,CAAC,GAAG;MACJZ,OAAO;MACPlC,QAAQ;MACRmC,WAAW;MACXO,KAAK,EAAE;QACHtC,KAAK,EAAEgC,SAAS;QAChBxC,KAAK,EAAE,QAAQ;QACf+C,WAAW,EAAEN;OAChB;MACDO,IAAI,EAAE;QACFxC,KAAK,EAAEmC,SAAS;QAChB1G,WAAW,EAAE2G,eAAe;QAC5BF,eAAe;QACftF,UAAU,EAAEU,MAAM,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC;OACpC;MACDyC,KAAK,EAAE;QACHG,IAAI,EAAEX,SAAS;QACfC,KAAK,EAAErB,UAAU;QACjBsB,OAAO,EAAEH,YAAY;QACrBU,KAAK,EAAE5B,UAAU;QACjB6B,IAAI,EAAE5B;;KAEb;IACD,OAAOqE,CAAC;EACZ;EAEAC,iBAAiBA,CAACC,cAAuB;IACrC,OAAO;MACHpD,KAAK,EAAE,QAAQ;MACfqD,MAAMA,CAACC,OAAO;QACV,IAAGA,OAAO,CAAClJ,OAAO,CAACC,IAAI,IAAIX,SAAS,CAACoH,UAAU,CAACyC,GAAG,EAAC;UAChD,OAAO,KAAK;SACf,MAAI;UACD,OAAO,QAAQ;;MAEvB,CAAC;MACD3H,eAAeA,CAAC0H,OAAO;QACnB;QACA;QACA;QACI,OAAO,SAAS;QACpB;MACJ,CAAC;;MACDrH,WAAWA,CAACqH,OAAO;QACf;QACA;QACA;QACI,OAAO,SAAS;QACpB;MACJ,CAAC;;MACDpH,YAAY,EAAE,CAAC;MACfC,WAAWA,CAACmH,OAAO;QACf,IAAGA,OAAO,CAAClJ,OAAO,CAACC,IAAI,IAAIX,SAAS,CAACoH,UAAU,CAACyC,GAAG,EAAC;UAChD,OAAO,CAAC;SACX,MAAI;UACD,OAAO,CAAC;;MAEhB,CAAC;MACD/C,KAAKA,CAAC8C,OAAO;QACT,IAAGA,OAAO,CAAClJ,OAAO,CAACC,IAAI,IAAIX,SAAS,CAACoH,UAAU,CAACyC,GAAG,EAAC;UAChD,OAAO,MAAM;SAChB,MAAI;UACD,OAAO,SAAS;;MAExB,CAAC;MACDtD,OAAOA,CAACqD,OAAO;QACX,OAAOF,cAAc;QACrB;QACA;QACA;QACA;QACA;MACJ,CAAC;;MACD3C,IAAI,EAAE;QACF+C,IAAI,EAAE;OACT;MACDC,SAASA,CAAC1J,KAAK,EAAEuJ,OAAO;QACpB,IAAII,QAAQ,GAAG/I,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACd,KAAK,CAAC;QACvD,IAAGuJ,OAAO,CAAClJ,OAAO,CAACC,IAAI,IAAIX,SAAS,CAACoH,UAAU,CAACyC,GAAG,EAAC;UAChD,OAAOG,QAAQ;SAClB,MAAI;UACD,OAAOA,QAAQ;;MAEvB,CAAC;MACDC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAC;QACJC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE;OACR;MACDvB,QAAQ,EAAE,CAAC;MACXwB,SAAS,EAAE;KACd;EACL;EAEAC,UAAUA,CAACC,eAAe,EAAEjF,OAAO,EAAEoB,MAAM,EAAEC,KAAK,EAAE6D,QAAQ,EAAEC,MAAM,EAAEC,UAAU;IAC5E,IAAI9H,MAAM,GAAkB;MACxB+H,SAAS,EAAEJ,eAAe,GAAG,GAAG,GAAG,GAAG;MACtCK,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE,GAAG;MAChBC,OAAO,EAAE;QACLxF,OAAO;QACPoB,MAAM;QACNC,KAAK;QACL6D,QAAQ;QACRE;;KAGP;IACD,IAAGD,MAAM,IAAI,IAAI,EAAC;MACd7H,MAAM,CAAC6H,MAAM,GAAGA,MAAM;;IAE1B,OAAO7H,MAAM;EACjB;;AAKJ,OAAO,MAAMmI,WAAW,GAAG,IAAIhL,WAAW,EAAE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}