{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AppSimCreateComponent {\n  static {\n    this.ɵfac = function AppSimCreateComponent_Factory(t) {\n      return new (t || AppSimCreateComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSimCreateComponent,\n      selectors: [[\"app-sim-create\"]],\n      decls: 2,\n      vars: 0,\n      template: function AppSimCreateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵtext(1, \"Trang tao sim\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSimCreateComponent", "selectors", "decls", "vars", "template", "AppSimCreateComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\create\\app.sim.create.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\create\\app.sim.create.component.html"], "sourcesContent": ["import { Component } from \"@angular/core\";\r\n\r\n@Component({\r\n    selector: \"app-sim-create\",\r\n    templateUrl: './app.sim.create.component.html'\r\n})\r\nexport class AppSimCreateComponent {\r\n    \r\n}", "<div>Trang tao sim</div>"], "mappings": ";AAMA,OAAM,MAAOA,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNlCE,EAAA,CAAAC,cAAA,UAAK;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}