package vn.vnpt.cmp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.cmp.base.jpa.dto.docs.DocumentContent;
import vn.vnpt.cmp.base.jpa.dto.docs.Project;
import vn.vnpt.cmp.service.DocumentContentService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/docs")
public class DocumentContentController {
    @Autowired
    private DocumentContentService documentContentService;
    @GetMapping("/projectInfo")
    @CrossOrigin
    public ResponseEntity<Project> getProjectInfo(
             @RequestParam Map<String,Object> params
    ){
        Project project = documentContentService.getProjectInfo(params);
        if(project != null){
            return ResponseEntity.ok(project);
        }else{
            return ResponseEntity.notFound().build();
        }

    }

    @GetMapping("/search")
    @CrossOrigin
    public ResponseEntity<List<DocumentContent>> getPageForProject(
            @RequestParam Map<String,Object> params
    ){
        List<DocumentContent> result = documentContentService.getPageForProject(params);
        if(result != null){
            return ResponseEntity.ok(result);
        }else{
            return ResponseEntity.notFound().build();
        }
    }
    @GetMapping("/pageInfo")
    @CrossOrigin
    public ResponseEntity<DocumentContent> getPageInfo(@RequestParam Map<String,Object> params){
        DocumentContent documentContent = documentContentService.getPageInfo(params);
        if(documentContent != null){
            return ResponseEntity.ok(documentContent);
        }else{
            return ResponseEntity.notFound().build();
        }
    }

}
