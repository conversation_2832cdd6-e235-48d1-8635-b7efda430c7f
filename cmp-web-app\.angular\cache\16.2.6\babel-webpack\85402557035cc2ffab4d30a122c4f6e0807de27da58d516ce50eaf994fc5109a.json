{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ErrorRoutingModule } from './error-routing.module';\nimport { ErrorComponent } from './error.component';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nexport class ErrorModule {\n  static {\n    this.ɵfac = function ErrorModule_Factory(t) {\n      return new (t || ErrorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ErrorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ErrorRoutingModule, ButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ErrorModule, {\n    declarations: [ErrorComponent],\n    imports: [CommonModule, ErrorRoutingModule, ButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ErrorRoutingModule", "ErrorComponent", "ButtonModule", "ErrorModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\error\\error.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ErrorRoutingModule } from './error-routing.module';\r\nimport { ErrorComponent } from './error.component';\r\nimport { ButtonModule } from 'primeng/button';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        ErrorRoutingModule,\r\n        ButtonModule\r\n    ],\r\n    declarations: [ErrorComponent]\r\n})\r\nexport class ErrorModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,YAAY,QAAQ,gBAAgB;;AAU7C,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBANhBJ,YAAY,EACZC,kBAAkB,EAClBE,YAAY;IAAA;EAAA;;;2EAIPC,WAAW;IAAAC,YAAA,GAFLH,cAAc;IAAAI,OAAA,GAJzBN,YAAY,EACZC,kBAAkB,EAClBE,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}