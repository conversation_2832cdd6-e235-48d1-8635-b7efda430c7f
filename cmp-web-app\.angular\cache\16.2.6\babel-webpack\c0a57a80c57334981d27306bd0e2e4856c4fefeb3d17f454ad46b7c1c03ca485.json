{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/button\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nexport class ErrorComponent {\n  static {\n    this.ɵfac = function ErrorComponent_Factory(t) {\n      return new (t || ErrorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ErrorComponent,\n      selectors: [[\"app-error\"]],\n      decls: 14,\n      vars: 2,\n      consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [\"src\", \"assets/images/m2m.png\", \"alt\", \"ONEIOT Platform logo\", 1, \"mb-5\", \"w-20rem\", \"flex-shrink-0\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\", \"background\", \"linear-gradient(180deg, rgba(233, 30, 99, 0.4) 10%, rgba(33, 150, 243, 0) 30%)\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", \"flex\", \"flex-column\", \"align-items-center\", 2, \"border-radius\", \"53px\"], [1, \"grid\", \"flex\", \"flex-column\", \"align-items-center\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-pink-500\", \"border-circle\", 2, \"height\", \"3.2rem\", \"width\", \"3.2rem\"], [1, \"pi\", \"pi-fw\", \"pi-exclamation-circle\", \"text-2xl\", \"text-white\"], [1, \"text-900\", \"font-bold\", \"text-5xl\", \"mb-2\"], [1, \"text-600\", \"mb-5\"], [\"src\", \"assets/images/error/asset-error.svg\", \"alt\", \"Error\", \"width\", \"80%\", 1, \"mb-5\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-arrow-left\", \"label\", \"Go to Dashboard\", 1, \"p-button-text\", 3, \"routerLink\"]],\n      template: function ErrorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"h1\", 8);\n          i0.ɵɵtext(9, \"Error Occured\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 9);\n          i0.ɵɵtext(11, \"Requested resource is not available.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"img\", 10)(13, \"button\", 11);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ButtonDirective],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ErrorComponent", "selectors", "decls", "vars", "consts", "template", "ErrorComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\error\\error.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\error\\error.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n    selector: 'app-error',\r\n    templateUrl: './error.component.html',\r\n})\r\nexport class ErrorComponent { }", "<div class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\r\n    <div class=\"flex flex-column align-items-center justify-content-center\">\r\n        <img src=\"assets/images/m2m.png\" alt=\"ONEIOT Platform logo\" class=\"mb-5 w-20rem flex-shrink-0\">\r\n        <div style=\"border-radius:56px; padding:0.3rem; background: linear-gradient(180deg, rgba(233, 30, 99, 0.4) 10%, rgba(33, 150, 243, 0) 30%);\">\r\n            <div class=\"w-full surface-card py-8 px-5 sm:px-8 flex flex-column align-items-center\" style=\"border-radius:53px\">\r\n                <div class=\"grid flex flex-column align-items-center\">\r\n                    <div class=\"flex justify-content-center align-items-center bg-pink-500 border-circle\" style=\"height:3.2rem; width:3.2rem;\">\r\n                        <i class=\"pi pi-fw pi-exclamation-circle text-2xl text-white\"></i>\r\n                    </div>\r\n                    <h1 class=\"text-900 font-bold text-5xl mb-2\">Error Occured</h1>\r\n                    <span class=\"text-600 mb-5\">Requested resource is not available.</span>\r\n                    <img src=\"assets/images/error/asset-error.svg\" alt=\"Error\" class=\"mb-5\" width=\"80%\">\r\n                    <button pButton pRipple icon=\"pi pi-arrow-left\" label=\"Go to Dashboard\" class=\"p-button-text\" [routerLink]=\"['/']\"></button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;AAMA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCN3BE,EAAA,CAAAC,cAAA,aAAqH;UAE7GD,EAAA,CAAAE,SAAA,aAA+F;UAC/FF,EAAA,CAAAC,cAAA,aAA6I;UAI7HD,EAAA,CAAAE,SAAA,WAAkE;UACtEF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,YAA6C;UAAAD,EAAA,CAAAI,MAAA,oBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAI,MAAA,4CAAoC;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAE,SAAA,eAAoF;UAExFF,EAAA,CAAAG,YAAA,EAAM;;;UAD4FH,EAAA,CAAAK,SAAA,IAAoB;UAApBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}