{"ast": null, "code": "export default {\n  label: {\n    tabGeneral: \"Thông tin chung\",\n    tabCrontab: \"Lập lịch tổng hợp\",\n    tabSend: \"<PERSON><PERSON>i báo cáo\",\n    reportName: \"Tên báo cáo\",\n    reportStatus: \"Trạng thái báo cáo\",\n    reportEnablePreview: \"Xem trước báo cáo\",\n    description: \"Mô tả\",\n    tableList: \"<PERSON>h sách các bảng\",\n    paramList: \"<PERSON><PERSON> sách các mục tìm kiếm\",\n    tableName: \"Tên bảng\",\n    paramKey: \"Key\",\n    paramType: \"Kiểu dữ liệu\",\n    paramDisplay: \"Tên hiển thị\",\n    query: \"<PERSON><PERSON>u truy vấn\",\n    schema: \"Schema\",\n    updateDate: \"<PERSON><PERSON><PERSON> cập nhật\",\n    fromDate: \"Từ ngày\",\n    toDate: \"Đến ngày\",\n    reportreceivinggroup: \"Tên nhóm nhận báo cáo động\",\n    fieldDisplay: \"Tên cột hiển thị\",\n    fieldData: \"Tên trường dữ liệu\",\n    valueDisplay: \"Giá trị hiển thị\",\n    valueDB: \"Giá trị trong database\",\n    timeOnce: \"Giờ tổng hợp\",\n    startTime: \"Giờ bắt đầu\",\n    endTime: \"Giờ kết thúc\",\n    typeSchedule: \"Kiểu báo cáo\",\n    runOne: \"Chạy 1 lần\",\n    runRepeat: \"Chạy lặp\",\n    numberRepeatHour: \"Số giờ lặp\",\n    dayInMonth: \"Ngày trong tháng\",\n    dayInWeek: \"Ngày trong tuần\",\n    monthInYear: \"Tháng trong năm\",\n    monday: \"Thứ hai\",\n    tuesday: \"Thứ ba\",\n    wednesday: \"Thứ tư\",\n    thursday: \"Thứ năm\",\n    friday: \"Thứ sáu\",\n    saturday: \"Thứ 7\",\n    sunday: \"Chủ nhật\",\n    january: \"Tháng 1\",\n    february: \"Tháng 2\",\n    march: \"Tháng 3\",\n    april: \"Tháng 4\",\n    may: \"Tháng 5\",\n    june: \"Tháng 6\",\n    july: \"Tháng 7\",\n    august: \"Tháng 8\",\n    september: \"Tháng 9\",\n    october: \"Tháng 10\",\n    november: \"Tháng 11\",\n    december: \"Tháng 12\",\n    emailSubject: \"Tiêu đề email\",\n    emailGroups: \"Nhóm nhận báo cáo\",\n    emailReceive: \"Email nhận báo cáo\",\n    hourSend: \"Giờ gửi báo cáo\",\n    dateType: \"Kiểu hiển thị\",\n    isAutoComplete: \"Gọi truy vấn\",\n    isMultiChoice: \"Lựa chọn nhiều\",\n    objectKey: \"Đối tượng truy vấn\",\n    input: \"Trường truy vấn\",\n    output: \"Trường lấy giá trị\",\n    displayPattern: \"Mẫu hiển thị\",\n    queryParams: \"Tìm kiếm tùy chọn\",\n    required: \"Bắt buộc\",\n    sampleQueryParam: \"Mẫu dữ liệu: customerCode=$customerCode&userType=1 (customerCode,userType: Trường query; $customerCode: trường customerCode có thể cho người dùng lựa chọn; userType=1: trường tìm kiếm cố định)\",\n    recentlyDateFrom: \"Recently Date From\",\n    recentlyDateTo: \"Recently Date To\",\n    showDateDefault: \"Hiển thị thời gian hiện tại\"\n  },\n  status: {\n    active: \"Hoạt động\",\n    inactive: \"Không hoạt động\"\n  },\n  text: {\n    inputReportName: \"Nhập tên báo cáo\",\n    createTable: \"Tạo bảng\",\n    updateTable: \"Cập nhật bảng\",\n    detailTable: \"Chi tiết bảng\",\n    createParameter: \"Tạo mục tìm kiếm\",\n    updateParameter: \"Cập nhật mục tìm kiếm\",\n    detailParameter: \"Chi tiết mục tìm kiếm\",\n    inputQuery: \"Nhập truy vấn\",\n    inputTableName: \"Nhập tên bảng\",\n    selectSchema: \"Chọn schema\",\n    inputParamKey: \"Nhập key tìm kiếm\",\n    inputDisplayName: \"Nhập tên hiển thị\",\n    selectParamType: \"Chọn kiểu dữ liệu\",\n    inputDescription: \"Nhập mô tả\",\n    inputemails: \"Nhập Emails\",\n    inputsms: \"Nhập SMS\",\n    inputNameReceiving: \"Nhập tên nhóm nhận báo cáo động\",\n    selectCycle: \"Chọn số giờ lặp\",\n    selectHourSummary: \"Chọn giờ tổng hợp\",\n    selectStartTime: \"Chọn giờ bắt đầu\",\n    selectEndTime: \"Chọn giờ kết thúc\",\n    selectEmailGroup: \"Chọn nhóm nhận báo cáo\",\n    selectHourSend: \"Chọn giờ gửi báo cáo\",\n    inputEmailSubject: \"Nhập tiêu đề email\",\n    selectDateType: \"Chọn kiểu hiển thị\",\n    inputObjectKey: \"Nhập đối tượng truy vấn\",\n    inputInput: \"Nhập trường truy vấn\",\n    inputOutput: \"Nhập trường lấy giá trị\",\n    inputDisplayPattern: \"Nhập mẫu hiển thị\",\n    errorExportLimit: \"Dữ liệu xuất file vượt quá 1 triệu dòng\",\n    inputQueryParam: \"Nhập Query Param\",\n    recentlyDateFrom: \"Recently Date From\",\n    recentlyDateTo: \"Recently Date To\"\n  },\n  button: {\n    addTable: \"Thêm bảng\",\n    addParam: \"Thêm mục tìm kiếm\"\n  },\n  paramType: {\n    number: \"Number\",\n    string: \"String\",\n    date: \"Date\",\n    listNumber: \"Enum Number\",\n    listString: \"Enum String\",\n    timestamp: \"Timestamp\",\n    recentlyDateFrom: \"Recently Date From\",\n    recentlyDateTo: \"Recently Date To\"\n  },\n  schema: {\n    core: \"COREMGMT\",\n    sim: \"SIMMGMT\",\n    rule: \"RULEMGMT\",\n    bill: \"BILLING\",\n    log: \"LOGGING\",\n    monitor: \"MONITORING\",\n    report: \"REPORTING\",\n    elasticsearch: \"ELASTICSEARCH\"\n  },\n  receiving: {\n    name: \"Tên nhóm nhận báo cáo động\",\n    description: \"Mô tả\",\n    emails: \"Email\",\n    sms: \"SMS\"\n  },\n  datetype: {\n    month: \"Tháng\",\n    date: \"Ngày\",\n    datetime: \"Ngày giờ\"\n  },\n  message: {\n    wrongQueryParamFormat: \"Sai định dạng query param. Vui lòng kiểm tra hướng dẫn bên cạnh label\"\n  }\n};", "map": {"version": 3, "names": ["label", "tabGeneral", "tabCrontab", "tabSend", "reportName", "reportStatus", "reportEnablePreview", "description", "tableList", "paramList", "tableName", "<PERSON><PERSON><PERSON><PERSON>", "paramType", "paramDisplay", "query", "schema", "updateDate", "fromDate", "toDate", "reportreceivinggroup", "fieldDisplay", "fieldData", "valueDisplay", "valueDB", "timeOnce", "startTime", "endTime", "typeSchedule", "runOne", "runRepeat", "numberRepeatHour", "dayInMonth", "dayInWeek", "monthInYear", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "january", "february", "march", "april", "may", "june", "july", "august", "september", "october", "november", "december", "emailSubject", "emailGroups", "emailReceive", "hourSend", "dateType", "isAutoComplete", "isMultiChoice", "object<PERSON>ey", "input", "output", "displayPattern", "queryParams", "required", "sampleQueryParam", "recentlyDateFrom", "recentlyDateTo", "showDateDefault", "status", "active", "inactive", "text", "inputReportName", "createTable", "updateTable", "detailTable", "createParameter", "updateParameter", "detailParameter", "inputQuery", "inputTableName", "selectSchema", "inputParamKey", "inputDisplayName", "selectParamType", "inputDescription", "inputemails", "inputsms", "inputNameReceiving", "selectCycle", "selectHourSummary", "selectStartTime", "selectEndTime", "selectEmailGroup", "selectHourSend", "inputEmailSubject", "selectDateType", "inputObjectKey", "inputInput", "inputOutput", "inputDisplayPattern", "errorExportLimit", "inputQueryParam", "button", "addTable", "addParam", "number", "string", "date", "listNumber", "listString", "timestamp", "core", "sim", "rule", "bill", "log", "monitor", "report", "elasticsearch", "receiving", "name", "emails", "sms", "datetype", "month", "datetime", "message", "wrongQueryParamFormat"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\report.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        tabGeneral: \"Thông tin chung\",\r\n        tabCrontab: \"Lập lịch tổng hợp\",\r\n        tabSend: \"<PERSON><PERSON>i báo cáo\",\r\n        reportName: \"Tên báo cáo\",\r\n        reportStatus: \"Trạng thái báo cáo\",\r\n        reportEnablePreview: \"Xem trước báo cáo\",\r\n        description: \"Mô tả\",\r\n        tableList: \"<PERSON>h sách các bảng\",\r\n        paramList: \"<PERSON><PERSON> sách các mục tìm kiếm\",\r\n        tableName: \"Tên bảng\",\r\n        paramKey: \"Key\",\r\n        paramType: \"Kiểu dữ liệu\",\r\n        paramDisplay: \"Tên hiển thị\",\r\n        query: \"<PERSON><PERSON>u truy vấn\",\r\n        schema: \"Schema\",\r\n        updateDate: \"<PERSON><PERSON><PERSON> cập nhật\",\r\n        fromDate: \"Từ ngày\",\r\n        toDate: \"Đến ngày\",\r\n        reportreceivinggroup: \"Tên nhóm nhận báo cáo động\",\r\n        fieldDisplay: \"Tên cột hiển thị\",\r\n        fieldData: \"Tên trường dữ liệu\",\r\n        valueDisplay: \"Giá trị hiển thị\",\r\n        valueDB: \"Giá trị trong database\",\r\n        timeOnce: \"Giờ tổng hợp\",\r\n        startTime: \"Giờ bắt đầu\",\r\n        endTime: \"Giờ kết thúc\",\r\n        typeSchedule: \"Kiểu báo cáo\",\r\n        runOne: \"Chạy 1 lần\",\r\n        runRepeat: \"Chạy lặp\",\r\n        numberRepeatHour: \"Số giờ lặp\",\r\n        dayInMonth: \"Ngày trong tháng\",\r\n        dayInWeek: \"Ngày trong tuần\",\r\n        monthInYear: \"Tháng trong năm\",\r\n        monday: \"Thứ hai\",\r\n        tuesday: \"Thứ ba\",\r\n        wednesday: \"Thứ tư\",\r\n        thursday: \"Thứ năm\",\r\n        friday: \"Thứ sáu\",\r\n        saturday: \"Thứ 7\",\r\n        sunday: \"Chủ nhật\",\r\n        january: \"Tháng 1\",\r\n        february: \"Tháng 2\",\r\n        march: \"Tháng 3\",\r\n        april: \"Tháng 4\",\r\n        may: \"Tháng 5\",\r\n        june: \"Tháng 6\",\r\n        july: \"Tháng 7\",\r\n        august: \"Tháng 8\",\r\n        september: \"Tháng 9\",\r\n        october: \"Tháng 10\",\r\n        november: \"Tháng 11\",\r\n        december: \"Tháng 12\",\r\n        emailSubject: \"Tiêu đề email\",\r\n        emailGroups: \"Nhóm nhận báo cáo\",\r\n        emailReceive: \"Email nhận báo cáo\",\r\n        hourSend: \"Giờ gửi báo cáo\",\r\n        dateType: \"Kiểu hiển thị\",\r\n        isAutoComplete: \"Gọi truy vấn\",\r\n        isMultiChoice: \"Lựa chọn nhiều\",\r\n        objectKey: \"Đối tượng truy vấn\",\r\n        input: \"Trường truy vấn\",\r\n        output: \"Trường lấy giá trị\",\r\n        displayPattern: \"Mẫu hiển thị\",\r\n        queryParams:\"Tìm kiếm tùy chọn\",\r\n        required: \"Bắt buộc\",\r\n        sampleQueryParam:\"Mẫu dữ liệu: customerCode=$customerCode&userType=1 (customerCode,userType: Trường query; $customerCode: trường customerCode có thể cho người dùng lựa chọn; userType=1: trường tìm kiếm cố định)\",\r\n        recentlyDateFrom: \"Recently Date From\",\r\n        recentlyDateTo: \"Recently Date To\",\r\n        showDateDefault: \"Hiển thị thời gian hiện tại\",\r\n    },\r\n    status: {\r\n        active: \"Hoạt động\",\r\n        inactive: \"Không hoạt động\",\r\n    },\r\n    text: {\r\n        inputReportName: \"Nhập tên báo cáo\",\r\n        createTable: \"Tạo bảng\",\r\n        updateTable: \"Cập nhật bảng\",\r\n        detailTable: \"Chi tiết bảng\",\r\n        createParameter: \"Tạo mục tìm kiếm\",\r\n        updateParameter: \"Cập nhật mục tìm kiếm\",\r\n        detailParameter: \"Chi tiết mục tìm kiếm\",\r\n        inputQuery: \"Nhập truy vấn\",\r\n        inputTableName: \"Nhập tên bảng\",\r\n        selectSchema: \"Chọn schema\",\r\n        inputParamKey: \"Nhập key tìm kiếm\",\r\n        inputDisplayName: \"Nhập tên hiển thị\",\r\n        selectParamType: \"Chọn kiểu dữ liệu\",\r\n        inputDescription: \"Nhập mô tả\",\r\n        inputemails: \"Nhập Emails\",\r\n        inputsms: \"Nhập SMS\",\r\n        inputNameReceiving: \"Nhập tên nhóm nhận báo cáo động\",\r\n        selectCycle: \"Chọn số giờ lặp\",\r\n        selectHourSummary: \"Chọn giờ tổng hợp\",\r\n        selectStartTime: \"Chọn giờ bắt đầu\",\r\n        selectEndTime: \"Chọn giờ kết thúc\",\r\n        selectEmailGroup: \"Chọn nhóm nhận báo cáo\",\r\n        selectHourSend: \"Chọn giờ gửi báo cáo\",\r\n        inputEmailSubject: \"Nhập tiêu đề email\",\r\n        selectDateType: \"Chọn kiểu hiển thị\",\r\n        inputObjectKey: \"Nhập đối tượng truy vấn\",\r\n        inputInput: \"Nhập trường truy vấn\",\r\n        inputOutput: \"Nhập trường lấy giá trị\",\r\n        inputDisplayPattern: \"Nhập mẫu hiển thị\",\r\n        errorExportLimit:\"Dữ liệu xuất file vượt quá 1 triệu dòng\",\r\n        inputQueryParam:\"Nhập Query Param\",\r\n        recentlyDateFrom: \"Recently Date From\",\r\n        recentlyDateTo: \"Recently Date To\",\r\n    },\r\n    button: {\r\n        addTable: \"Thêm bảng\",\r\n        addParam: \"Thêm mục tìm kiếm\",\r\n    },\r\n    paramType: {\r\n        number: \"Number\",\r\n        string: \"String\",\r\n        date: \"Date\",\r\n        listNumber: \"Enum Number\",\r\n        listString: \"Enum String\",\r\n        timestamp: \"Timestamp\",\r\n        recentlyDateFrom: \"Recently Date From\",\r\n        recentlyDateTo: \"Recently Date To\",\r\n    },\r\n    schema: {\r\n        core: \"COREMGMT\",\r\n        sim: \"SIMMGMT\",\r\n        rule: \"RULEMGMT\",\r\n        bill: \"BILLING\",\r\n        log: \"LOGGING\",\r\n        monitor: \"MONITORING\",\r\n        report: \"REPORTING\",\r\n        elasticsearch: \"ELASTICSEARCH\"\r\n    },\r\n    receiving: {\r\n        name: \"Tên nhóm nhận báo cáo động\",\r\n        description: \"Mô tả\",\r\n        emails: \"Email\",\r\n        sms: \"SMS\",\r\n    },\r\n    datetype: {\r\n        month: \"Tháng\",\r\n        date: \"Ngày\",\r\n        datetime: \"Ngày giờ\"\r\n    },\r\n    message: {\r\n        wrongQueryParamFormat: \"Sai định dạng query param. Vui lòng kiểm tra hướng dẫn bên cạnh label\"\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,UAAU,EAAE,iBAAiB;IAC7BC,UAAU,EAAE,mBAAmB;IAC/BC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,oBAAoB;IAClCC,mBAAmB,EAAE,mBAAmB;IACxCC,WAAW,EAAE,OAAO;IACpBC,SAAS,EAAE,oBAAoB;IAC/BC,SAAS,EAAE,4BAA4B;IACvCC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,cAAc;IACzBC,YAAY,EAAE,cAAc;IAC5BC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,UAAU;IAClBC,oBAAoB,EAAE,4BAA4B;IAClDC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE,oBAAoB;IAC/BC,YAAY,EAAE,kBAAkB;IAChCC,OAAO,EAAE,wBAAwB;IACjCC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,cAAc;IACvBC,YAAY,EAAE,cAAc;IAC5BC,MAAM,EAAE,YAAY;IACpBC,SAAS,EAAE,UAAU;IACrBC,gBAAgB,EAAE,YAAY;IAC9BC,UAAU,EAAE,kBAAkB;IAC9BC,SAAS,EAAE,iBAAiB;IAC5BC,WAAW,EAAE,iBAAiB;IAC9BC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,mBAAmB;IAChCC,YAAY,EAAE,oBAAoB;IAClCC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,eAAe;IACzBC,cAAc,EAAE,cAAc;IAC9BC,aAAa,EAAE,gBAAgB;IAC/BC,SAAS,EAAE,oBAAoB;IAC/BC,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE,oBAAoB;IAC5BC,cAAc,EAAE,cAAc;IAC9BC,WAAW,EAAC,mBAAmB;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAC,kMAAkM;IACnNC,gBAAgB,EAAE,oBAAoB;IACtCC,cAAc,EAAE,kBAAkB;IAClCC,eAAe,EAAE;GACpB;EACDC,MAAM,EAAE;IACJC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE;GACb;EACDC,IAAI,EAAE;IACFC,eAAe,EAAE,kBAAkB;IACnCC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,eAAe;IAC5BC,eAAe,EAAE,kBAAkB;IACnCC,eAAe,EAAE,uBAAuB;IACxCC,eAAe,EAAE,uBAAuB;IACxCC,UAAU,EAAE,eAAe;IAC3BC,cAAc,EAAE,eAAe;IAC/BC,YAAY,EAAE,aAAa;IAC3BC,aAAa,EAAE,mBAAmB;IAClCC,gBAAgB,EAAE,mBAAmB;IACrCC,eAAe,EAAE,mBAAmB;IACpCC,gBAAgB,EAAE,YAAY;IAC9BC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,kBAAkB,EAAE,iCAAiC;IACrDC,WAAW,EAAE,iBAAiB;IAC9BC,iBAAiB,EAAE,mBAAmB;IACtCC,eAAe,EAAE,kBAAkB;IACnCC,aAAa,EAAE,mBAAmB;IAClCC,gBAAgB,EAAE,wBAAwB;IAC1CC,cAAc,EAAE,sBAAsB;IACtCC,iBAAiB,EAAE,oBAAoB;IACvCC,cAAc,EAAE,oBAAoB;IACpCC,cAAc,EAAE,yBAAyB;IACzCC,UAAU,EAAE,sBAAsB;IAClCC,WAAW,EAAE,yBAAyB;IACtCC,mBAAmB,EAAE,mBAAmB;IACxCC,gBAAgB,EAAC,yCAAyC;IAC1DC,eAAe,EAAC,kBAAkB;IAClCrC,gBAAgB,EAAE,oBAAoB;IACtCC,cAAc,EAAE;GACnB;EACDqC,MAAM,EAAE;IACJC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE;GACb;EACD/F,SAAS,EAAE;IACPgG,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,WAAW;IACtB9C,gBAAgB,EAAE,oBAAoB;IACtCC,cAAc,EAAE;GACnB;EACDrD,MAAM,EAAE;IACJmG,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE,SAAS;IACdC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE;GAClB;EACDC,SAAS,EAAE;IACPC,IAAI,EAAE,4BAA4B;IAClCpH,WAAW,EAAE,OAAO;IACpBqH,MAAM,EAAE,OAAO;IACfC,GAAG,EAAE;GACR;EACDC,QAAQ,EAAE;IACNC,KAAK,EAAE,OAAO;IACdjB,IAAI,EAAE,MAAM;IACZkB,QAAQ,EAAE;GACb;EACDC,OAAO,EAAE;IACLC,qBAAqB,EAAE;;CAE9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}