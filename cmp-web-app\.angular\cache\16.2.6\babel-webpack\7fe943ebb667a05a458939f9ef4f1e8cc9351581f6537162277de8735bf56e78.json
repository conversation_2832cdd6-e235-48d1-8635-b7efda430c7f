{"ast": null, "code": "export default {\n  label: {\n    rolename: \"<PERSON>ên nhóm quyền\",\n    usertype: \"Loại nhóm quyền\",\n    status: \"Trạng thái\",\n    rolelist: \"<PERSON><PERSON> sách quyền\",\n    errorPattern: \"<PERSON> định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - , d<PERSON><PERSON> c<PERSON>, tiếng Việt)\"\n  },\n  text: {\n    inputRoleName: \"Tên nhóm quyền\",\n    selectUserType: \"Chọn loại nhóm quyền\",\n    status: \"Chọn trạng thái\"\n  },\n  status: {\n    all: \"Tất cả\",\n    active: \"Hoạt động\",\n    inactive: \"Không hoạt động\"\n  },\n  type: {\n    all: \"All\",\n    admin: \"Admin\",\n    customer: \"<PERSON>h<PERSON>ch hàng\",\n    province: \"Tỉnh/Thành phố\",\n    teller: \"Giao dịch viên\",\n    agency: \"Đại lý\"\n  }\n};", "map": {"version": 3, "names": ["label", "rolename", "usertype", "status", "rolelist", "errorPattern", "text", "inputRoleName", "selectUserType", "all", "active", "inactive", "type", "admin", "customer", "province", "teller", "agency"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\roles.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        rolename: \"<PERSON>ên nhóm quyền\",\r\n        usertype: \"Loại nhóm quyền\",\r\n        status: \"Trạng thái\",\r\n        rolelist: \"<PERSON><PERSON> sách quyền\",\r\n        errorPattern: \"<PERSON> định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - , d<PERSON><PERSON> c<PERSON>, tiếng Việt)\",\r\n    },\r\n    text: {\r\n        inputRoleName: \"Tên nhóm quyền\",\r\n        selectUserType: \"Chọn loại nhóm quyền\",\r\n        status: \"Chọn trạng thái\",\r\n    },\r\n    status: {\r\n        all: \"Tất cả\",\r\n        active: \"Hoạt động\",\r\n        inactive: \"Không hoạt động\",\r\n    },\r\n    type: {\r\n        all: \"All\",\r\n        admin: \"Admin\",\r\n        customer: \"<PERSON>h<PERSON><PERSON> hàng\",\r\n        province: \"Tỉnh/Thành phố\",\r\n        teller: \"Giao dịch viên\",\r\n        agency: \"Đại lý\",\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,QAAQ,EAAE,gBAAgB;IAC1BC,QAAQ,EAAE,iBAAiB;IAC3BC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,iBAAiB;IAC3BC,YAAY,EAAE;GACjB;EACDC,IAAI,EAAE;IACFC,aAAa,EAAE,gBAAgB;IAC/BC,cAAc,EAAE,sBAAsB;IACtCL,MAAM,EAAE;GACX;EACDA,MAAM,EAAE;IACJM,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE;GACb;EACDC,IAAI,EAAE;IACFH,GAAG,EAAE,KAAK;IACVI,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE,gBAAgB;IAC1BC,MAAM,EAAE,gBAAgB;IACxBC,MAAM,EAAE;;CAEf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}