{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, HostListener, NgModule } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nclass FocusTrap {\n  el;\n  /**\n   * When set as true, focus wouldn't be managed.\n   * @group Props\n   */\n  pFocusTrapDisabled = false;\n  constructor(el) {\n    this.el = el;\n  }\n  onkeydown(e) {\n    if (this.pFocusTrapDisabled !== true) {\n      e.preventDefault();\n      const focusableElement = DomHandler.getNextFocusableElement(this.el.nativeElement, e.shiftKey);\n      if (focusableElement) {\n        focusableElement.focus();\n        focusableElement.select?.();\n      }\n    }\n  }\n  static ɵfac = function FocusTrap_Factory(t) {\n    return new (t || FocusTrap)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FocusTrap,\n    selectors: [[\"\", \"pFocusTrap\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    hostBindings: function FocusTrap_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.tab\", function FocusTrap_keydown_tab_HostBindingHandler($event) {\n          return ctx.onkeydown($event);\n        })(\"keydown.shift.tab\", function FocusTrap_keydown_shift_tab_HostBindingHandler($event) {\n          return ctx.onkeydown($event);\n        });\n      }\n    },\n    inputs: {\n      pFocusTrapDisabled: \"pFocusTrapDisabled\"\n    }\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrap, [{\n    type: Directive,\n    args: [{\n      selector: '[pFocusTrap]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    pFocusTrapDisabled: [{\n      type: Input\n    }],\n    onkeydown: [{\n      type: HostListener,\n      args: ['keydown.tab', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['keydown.shift.tab', ['$event']]\n    }]\n  });\n})();\nclass FocusTrapModule {\n  static ɵfac = function FocusTrapModule_Factory(t) {\n    return new (t || FocusTrapModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FocusTrapModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [FocusTrap],\n      declarations: [FocusTrap]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "CommonModule", "i0", "Directive", "Input", "HostListener", "NgModule", "FocusTrap", "el", "pFocusTrapDisabled", "constructor", "onkeydown", "e", "preventDefault", "focusableElement", "getNextFocusableElement", "nativeElement", "shift<PERSON>ey", "focus", "select", "ɵfac", "FocusTrap_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostBindings", "FocusTrap_HostBindings", "rf", "ctx", "ɵɵlistener", "FocusTrap_keydown_tab_HostBindingHandler", "$event", "FocusTrap_keydown_shift_tab_HostBindingHandler", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "FocusTrapModule", "FocusTrapModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/primeng/fesm2022/primeng-focustrap.mjs"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, HostListener, NgModule } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nclass FocusTrap {\n    el;\n    /**\n     * When set as true, focus wouldn't be managed.\n     * @group Props\n     */\n    pFocusTrapDisabled = false;\n    constructor(el) {\n        this.el = el;\n    }\n    onkeydown(e) {\n        if (this.pFocusTrapDisabled !== true) {\n            e.preventDefault();\n            const focusableElement = DomHandler.getNextFocusableElement(this.el.nativeElement, e.shiftKey);\n            if (focusableElement) {\n                focusableElement.focus();\n                focusableElement.select?.();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: FocusTrap, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.0.2\", type: FocusTrap, selector: \"[pFocusTrap]\", inputs: { pFocusTrapDisabled: \"pFocusTrapDisabled\" }, host: { listeners: { \"keydown.tab\": \"onkeydown($event)\", \"keydown.shift.tab\": \"onkeydown($event)\" }, classAttribute: \"p-element\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: FocusTrap, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pFocusTrap]',\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { pFocusTrapDisabled: [{\n                type: Input\n            }], onkeydown: [{\n                type: HostListener,\n                args: ['keydown.tab', ['$event']]\n            }, {\n                type: HostListener,\n                args: ['keydown.shift.tab', ['$event']]\n            }] } });\nclass FocusTrapModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: FocusTrapModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.2\", ngImport: i0, type: FocusTrapModule, declarations: [FocusTrap], imports: [CommonModule], exports: [FocusTrap] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: FocusTrapModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: FocusTrapModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [FocusTrap],\n                    declarations: [FocusTrap]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;;AAExE;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,EAAE;EACF;AACJ;AACA;AACA;EACIC,kBAAkB,GAAG,KAAK;EAC1BC,WAAWA,CAACF,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;EAChB;EACAG,SAASA,CAACC,CAAC,EAAE;IACT,IAAI,IAAI,CAACH,kBAAkB,KAAK,IAAI,EAAE;MAClCG,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,MAAMC,gBAAgB,GAAGd,UAAU,CAACe,uBAAuB,CAAC,IAAI,CAACP,EAAE,CAACQ,aAAa,EAAEJ,CAAC,CAACK,QAAQ,CAAC;MAC9F,IAAIH,gBAAgB,EAAE;QAClBA,gBAAgB,CAACI,KAAK,CAAC,CAAC;QACxBJ,gBAAgB,CAACK,MAAM,GAAG,CAAC;MAC/B;IACJ;EACJ;EACA,OAAOC,IAAI,YAAAC,kBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFf,SAAS,EAAnBL,EAAE,CAAAqB,iBAAA,CAAmCrB,EAAE,CAACsB,UAAU;EAAA;EAC3I,OAAOC,IAAI,kBAD8EvB,EAAE,CAAAwB,iBAAA;IAAAC,IAAA,EACJpB,SAAS;IAAAqB,SAAA;IAAAC,SAAA;IAAAC,YAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADP9B,EAAE,CAAAgC,UAAA,yBAAAC,yCAAAC,MAAA;UAAA,OACJH,GAAA,CAAAtB,SAAA,CAAAyB,MAAgB,CAAC;QAAA,iCAAAC,+CAAAD,MAAA;UAAA,OAAjBH,GAAA,CAAAtB,SAAA,CAAAyB,MAAgB,CAAC;QAAA;MAAA;IAAA;IAAAE,MAAA;MAAA7B,kBAAA;IAAA;EAAA;AAC5G;AACA;EAAA,QAAA8B,SAAA,oBAAAA,SAAA,KAH6FrC,EAAE,CAAAsC,iBAAA,CAGJjC,SAAS,EAAc,CAAC;IACvGoB,IAAI,EAAExB,SAAS;IACfsC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjB,IAAI,EAAEzB,EAAE,CAACsB;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEf,kBAAkB,EAAE,CAAC;MACtGkB,IAAI,EAAEvB;IACV,CAAC,CAAC;IAAEO,SAAS,EAAE,CAAC;MACZgB,IAAI,EAAEtB,YAAY;MAClBoC,IAAI,EAAE,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC;IACpC,CAAC,EAAE;MACCd,IAAI,EAAEtB,YAAY;MAClBoC,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMI,eAAe,CAAC;EAClB,OAAOzB,IAAI,YAAA0B,wBAAAxB,CAAA;IAAA,YAAAA,CAAA,IAAwFuB,eAAe;EAAA;EAClH,OAAOE,IAAI,kBAtB8E7C,EAAE,CAAA8C,gBAAA;IAAArB,IAAA,EAsBSkB;EAAe;EACnH,OAAOI,IAAI,kBAvB8E/C,EAAE,CAAAgD,gBAAA;IAAAC,OAAA,GAuBoClD,YAAY;EAAA;AAC/I;AACA;EAAA,QAAAsC,SAAA,oBAAAA,SAAA,KAzB6FrC,EAAE,CAAAsC,iBAAA,CAyBJK,eAAe,EAAc,CAAC;IAC7GlB,IAAI,EAAErB,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCU,OAAO,EAAE,CAAClD,YAAY,CAAC;MACvBmD,OAAO,EAAE,CAAC7C,SAAS,CAAC;MACpB8C,YAAY,EAAE,CAAC9C,SAAS;IAC5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,SAAS,EAAEsC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}