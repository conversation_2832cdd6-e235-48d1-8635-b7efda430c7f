{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ResetPasswordComponent } from \"../reset-password/reset-password.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ResetPasswordRoutingModule {\n  static {\n    this.ɵfac = function ResetPasswordRoutingModule_Factory(t) {\n      return new (t || ResetPasswordRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResetPasswordRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: '',\n        component: ResetPasswordComponent\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResetPasswordRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ResetPasswordComponent", "ResetPasswordRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\reset-password\\reset-password-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport {ResetPasswordComponent} from \"../reset-password/reset-password.component\";\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild([\r\n        { path: '', component: ResetPasswordComponent}\r\n    ])],\r\n    exports: [RouterModule]\r\n})\r\nexport class ResetPasswordRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAQC,sBAAsB,QAAO,4CAA4C;;;AAQjF,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBALzBF,YAAY,CAACG,QAAQ,CAAC,CAC5B;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAsB,CAAC,CACjD,CAAC,EACQD,YAAY;IAAA;EAAA;;;2EAEbE,0BAA0B;IAAAI,OAAA,GAAAC,EAAA,CAAAP,YAAA;IAAAQ,OAAA,GAFzBR,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}