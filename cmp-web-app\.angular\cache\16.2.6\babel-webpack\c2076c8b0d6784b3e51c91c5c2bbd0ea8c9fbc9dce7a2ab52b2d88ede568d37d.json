{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class ReportDynacmicService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/report\";\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  deleleReport(id, callback, errorCallBack, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  getById(id, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  createReport(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/create`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  updateReport(id, body, callback, errorCallback, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/update/${id}`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  changeStatus(id, params, callback, errorCallBack, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/changeStatus/${id}`, {}, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  getAllReportContent(callback) {\n    this.httpService.get(`${this.prefixApi}/all/permission`, {}, {}, callback);\n  }\n  static {\n    this.ɵfac = function ReportDynacmicService_Factory(t) {\n      return new (t || ReportDynacmicService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ReportDynacmicService,\n      factory: ReportDynacmicService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "ReportDynacmicService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "deleleReport", "id", "delete", "getById", "<PERSON><PERSON><PERSON><PERSON>", "createReport", "body", "post", "updateReport", "put", "changeStatus", "getAllReportContent", "i0", "ɵɵinject", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\report\\ReportDynacmicService.ts"], "sourcesContent": ["import {Inject, Injectable} from \"@angular/core\";\r\nimport {HttpService} from \"../comon/http.service\";\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class ReportDynacmicService {\r\n    private prefixApi: string;\r\n\r\n    constructor(@Inject(HttpService) private httpService: HttpService) {\r\n        this.prefixApi = \"/report\";\r\n    }\r\n\r\n    public search(params: {\r\n        [key: string]: any\r\n    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {\r\n        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public deleleReport(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public getById(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/${id}`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public createReport(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/create`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public updateReport(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/update/${id}`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public changeStatus(id: number, params: { [key: string]: any }, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.put(`${this.prefixApi}/changeStatus/${id}`,{},{},params,callback, errorCallBack, finallyCallback);\r\n    }\r\n    public getAllReportContent(callback){\r\n        this.httpService.get(`${this.prefixApi}/all/permission`, {}, {}, callback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,uBAAuB;;;AAKjD,OAAM,MAAOC,qBAAqB;EAG9BC,YAAyCC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,SAAS;EAC9B;EAEOC,MAAMA,CAACC,MAEb,EAAEC,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACxE,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAE,EAAE,EAAEE,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1G;EACOE,YAAYA,CAACC,EAAU,EAAEL,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAClG,IAAI,CAACN,WAAW,CAACU,MAAM,CAAC,GAAG,IAAI,CAACT,SAAS,IAAIQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEL,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;EACOK,OAAOA,CAACF,EAAU,EAAEL,QAAmB,EAAEQ,aAAuB,EAAEN,eAA0B;IAC/F,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIQ,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAAEL,QAAQ,EAAEQ,aAAa,EAAEN,eAAe,CAAC;EACpG;EACOO,YAAYA,CAACC,IAAI,EAACV,QAAmB,EAAEQ,aAAuB,EAAEN,eAA0B;IAC7F,IAAI,CAACN,WAAW,CAACe,IAAI,CAAC,GAAG,IAAI,CAACd,SAAS,SAAS,EAAE,EAAE,EAACa,IAAI,EAAC,EAAE,EAAEV,QAAQ,EAAEQ,aAAa,EAAEN,eAAe,CAAC;EAC3G;EACOU,YAAYA,CAACP,EAAE,EAACK,IAAI,EAACV,QAAmB,EAAEQ,aAAuB,EAAEN,eAA0B;IAChG,IAAI,CAACN,WAAW,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,SAAS,WAAWQ,EAAE,EAAE,EAAE,EAAE,EAACK,IAAI,EAAC,EAAE,EAAEV,QAAQ,EAAEQ,aAAa,EAAEN,eAAe,CAAC;EAChH;EACOY,YAAYA,CAACT,EAAU,EAAEN,MAA8B,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAClI,IAAI,CAACN,WAAW,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,SAAS,iBAAiBQ,EAAE,EAAE,EAAC,EAAE,EAAC,EAAE,EAACN,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACtH;EACOa,mBAAmBA,CAACf,QAAQ;IAC/B,IAAI,CAACJ,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAEG,QAAQ,CAAC;EAC9E;;;uBA7BSN,qBAAqB,EAAAsB,EAAA,CAAAC,QAAA,CAGVxB,WAAW;IAAA;EAAA;;;aAHtBC,qBAAqB;MAAAwB,OAAA,EAArBxB,qBAAqB,CAAAyB,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}