{"ast": null, "code": "import { AccountService } from \"src/app/service/account/AccountService\";\nimport { CommonModule } from \"@angular/common\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { FieldsetModule } from \"primeng/fieldset\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { InputTextModule } from \"primeng/inputtext\";\nimport { ButtonModule } from \"primeng/button\";\nimport { CommonVnptModule } from \"../common-module/common.module\";\nimport { SplitButtonModule } from \"primeng/splitbutton\";\nimport { AutoCompleteModule } from \"primeng/autocomplete\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { CardModule } from \"primeng/card\";\nimport { PanelModule } from \"primeng/panel\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { ChipsModule } from 'primeng/chips';\nimport { AppHistoryRouting } from \"./app.history-routing\";\nimport { ListHistoryActivityComponent } from \"./list/app.list.history.component\";\nimport { LogsService } from \"../../service/activity-history/LogsService\";\nimport { TableModule } from \"primeng/table\";\nimport { CustomerService } from \"../../service/customer/CustomerService\";\nimport { SimService } from \"../../service/sim/SimService\";\nimport * as i0 from \"@angular/core\";\nexport class AppHistoryModule {\n  static {\n    this.ɵfac = function AppHistoryModule_Factory(t) {\n      return new (t || AppHistoryModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppHistoryModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AccountService, LogsService, CustomerService, SimService],\n      imports: [AppHistoryRouting, CommonModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, AutoCompleteModule, CalendarModule, DropdownModule, CardModule, DialogModule, InputTextareaModule, MultiSelectModule, PanelModule, ChipsModule, TableModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppHistoryModule, {\n    declarations: [ListHistoryActivityComponent],\n    imports: [AppHistoryRouting, CommonModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, AutoCompleteModule, CalendarModule, DropdownModule, CardModule, DialogModule, InputTextareaModule, MultiSelectModule, PanelModule, ChipsModule, TableModule]\n  });\n})();", "map": {"version": 3, "names": ["AccountService", "CommonModule", "BreadcrumbModule", "FieldsetModule", "FormsModule", "ReactiveFormsModule", "InputTextModule", "ButtonModule", "CommonVnptModule", "SplitButtonModule", "AutoCompleteModule", "CalendarModule", "DropdownModule", "CardModule", "PanelModule", "DialogModule", "InputTextareaModule", "MultiSelectModule", "ChipsModule", "AppHistoryRouting", "ListHistoryActivityComponent", "LogsService", "TableModule", "CustomerService", "SimService", "AppHistoryModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\history_activity\\app.history.module.ts"], "sourcesContent": ["import {NgModule} from \"@angular/core\";\r\nimport {AccountService} from \"src/app/service/account/AccountService\";\r\nimport {CommonModule} from \"@angular/common\";\r\nimport {BreadcrumbModule} from \"primeng/breadcrumb\";\r\nimport {FieldsetModule} from \"primeng/fieldset\";\r\nimport {FormsModule, ReactiveFormsModule} from \"@angular/forms\";\r\nimport {InputTextModule} from \"primeng/inputtext\";\r\nimport {ButtonModule} from \"primeng/button\";\r\nimport {CommonVnptModule} from \"../common-module/common.module\";\r\nimport {SplitButtonModule} from \"primeng/splitbutton\";\r\nimport {AutoCompleteModule} from \"primeng/autocomplete\";\r\nimport {CalendarModule} from \"primeng/calendar\";\r\nimport {DropdownModule} from \"primeng/dropdown\";\r\nimport {CardModule} from \"primeng/card\";\r\nimport {PanelModule} from \"primeng/panel\";\r\nimport {DialogModule} from \"primeng/dialog\";\r\nimport {InputTextareaModule} from 'primeng/inputtextarea';\r\nimport {MultiSelectModule} from 'primeng/multiselect';\r\nimport {ChipsModule} from 'primeng/chips';\r\nimport {AppHistoryRouting} from \"./app.history-routing\";\r\nimport {ListHistoryActivityComponent} from \"./list/app.list.history.component\";\r\nimport {LogsService} from \"../../service/activity-history/LogsService\";\r\nimport {TableModule} from \"primeng/table\";\r\nimport {CustomerService} from \"../../service/customer/CustomerService\";\r\nimport {SimService} from \"../../service/sim/SimService\";\r\n\r\n\r\n@NgModule({\r\n  imports: [\r\n    AppHistoryRouting,\r\n    CommonModule,\r\n    BreadcrumbModule,\r\n    FieldsetModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    InputTextModule,\r\n    ButtonModule,\r\n    CommonVnptModule,\r\n    SplitButtonModule,\r\n    AutoCompleteModule,\r\n    CalendarModule,\r\n    DropdownModule,\r\n    CardModule,\r\n    DialogModule,\r\n    InputTextareaModule,\r\n    MultiSelectModule,\r\n    PanelModule,\r\n    ChipsModule,\r\n    TableModule\r\n  ],\r\n  declarations: [\r\n    ListHistoryActivityComponent\r\n  ],\r\n  exports: [],\r\n  providers: [\r\n    AccountService,\r\n    LogsService,\r\n    CustomerService,\r\n    SimService\r\n  ]\r\n})\r\nexport class AppHistoryModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,cAAc,QAAO,wCAAwC;AACrE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,gBAAgB,QAAO,oBAAoB;AACnD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAC/D,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,kBAAkB,QAAO,sBAAsB;AACvD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,mBAAmB,QAAO,uBAAuB;AACzD,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,iBAAiB,QAAO,uBAAuB;AACvD,SAAQC,4BAA4B,QAAO,mCAAmC;AAC9E,SAAQC,WAAW,QAAO,4CAA4C;AACtE,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,eAAe,QAAO,wCAAwC;AACtE,SAAQC,UAAU,QAAO,8BAA8B;;AAqCvD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;iBAPhB,CACTzB,cAAc,EACdqB,WAAW,EACXE,eAAe,EACfC,UAAU,CACX;MAAAE,OAAA,GA9BCP,iBAAiB,EACjBlB,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVE,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBH,WAAW,EACXI,WAAW,EACXI,WAAW;IAAA;EAAA;;;2EAaFG,gBAAgB;IAAAE,YAAA,GAVzBP,4BAA4B;IAAAM,OAAA,GAtB5BP,iBAAiB,EACjBlB,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVE,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBH,WAAW,EACXI,WAAW,EACXI,WAAW;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}