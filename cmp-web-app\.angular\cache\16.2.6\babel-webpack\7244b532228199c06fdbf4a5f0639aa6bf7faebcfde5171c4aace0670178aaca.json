{"ast": null, "code": "import statusHttp from \"./status.http\";\nimport permissions from \"./permissions\";\nexport const CONSTANTS = {\n  MAX_TIME_HTTP_WAIT: 30 * 1000,\n  NUMBER_IMAGE_CAPTCHA: 50,\n  HTTP_STATUS: statusHttp,\n  PERMISSIONS: permissions,\n  MAX_ROW_EXPORT: 1000000,\n  MAX_ROW_EXCEL_EXPORT: 100000,\n  OBSERVABLE: {\n    KEY_MESSAGE_COMMON: \"message-common\",\n    KEY_INPUT_FILE_VNPT: \"input-file-vnpt\",\n    KEY_LOAD_CONFIRM_POLICY_HISTORY: \"load-confirm-policy-history\",\n    KEY_EXPIRED_PASSWORD: \"expired-password\",\n    UPDATE_SHARE_INFO: \"update-share-info\"\n  },\n  SIM_STATUS: {\n    READY: 1,\n    ACTIVATED: 2,\n    INACTIVED: 3,\n    DEACTIVATED: 4,\n    PURGED: 5,\n    PROCESSING: 6\n  },\n  USER_TYPE: {\n    ADMIN: 1,\n    CUSTOMER: 7,\n    PROVINCE: 2,\n    DISTRICT: 3,\n    AGENCY: 8\n  },\n  USER_STATUS: {\n    ACTIVE: 1,\n    INACTIVE: 0\n  },\n  ROlES_STATUS: {\n    ACTIVE: 1,\n    INACTIVE: 0\n  },\n  ROLE_TYPE: {\n    ALL: 0,\n    ADMIN: 1,\n    CUSTOMER: 7,\n    PROVINCE: 2,\n    TELLER: 3,\n    AGENCY: 8\n  },\n  RATING_PLAN_STATUS: {\n    CREATE_NEW: 2,\n    PENDING: 3,\n    ACTIVATED: 1,\n    DEACTIVATED: 5,\n    INACTIVE: 0,\n    REJECTED: 4,\n    REMOVED: -1\n  },\n  RATING_PLAN_CYCLE: {\n    DAY: 1,\n    MONTH: 3\n  },\n  SUBSCRIPTION_TYPE: {\n    POSTPAID: 0,\n    PREPAID: 1\n  },\n  RATING_PLAN_SCOPE: {\n    NATION_WIDE: 0,\n    CUSTOMER: 1,\n    PROVINCE: 2\n  },\n  CUSTOMER_TYPE: {\n    INTERPRISE: 2,\n    PERSONAL: 1,\n    AGENCY: 0\n  },\n  CYCLE_TIME_UNITS: {\n    DAY: 1,\n    WEEK: 2,\n    MONTH: 3,\n    YEAR: 4\n  },\n  RELOAD: {\n    YES: 1,\n    NO: 0\n  },\n  FLEXIBLE: {\n    YES: 1,\n    NO: 0\n  },\n  IP_TYPE: {\n    DYNAMIC: 1,\n    STATIC: 2\n  },\n  ALERT_STATUS: {\n    INACTIVE: 0,\n    ACTIVE: 1\n  },\n  ALERT_STATUS_SIM: {\n    ALL: 0,\n    OUT_PLAN: 1,\n    OUT_LINE: 2,\n    DISCONNECTED: 3,\n    NEW_CONNECTION: 4\n    // PURGED: 5,\n  },\n\n  GROUP_SCOPE: {\n    GROUP_ADMIN: 0,\n    GROUP_PROVINCE: 1,\n    GROUP_CUSTOMER: 2\n  },\n  CUSTOMER_STATUS: {\n    ACTIVE: 1,\n    INACTIVE: 0,\n    CREATE_NEW: 2\n  },\n  MODE_VIEW: {\n    CREATE: 0,\n    UPDATE: 1,\n    DETAIL: 2\n  },\n  REPORT_STATUS: {\n    ACTIVE: 1,\n    INACTIVE: 0\n  },\n  WALLET_ACITVE_TYPE: {\n    BUY: 0,\n    SHARE: 1,\n    ACCURACY: 2,\n    REGISTER_NO_OTP: 3,\n    CANCEL_REGISTER_NO_OTP: 4\n  },\n  REPORT_PREVIEW: {\n    ENABLE: 1,\n    DISABLE: 0\n  },\n  PARAMETER_TYPE: {\n    NUMBER: 0,\n    STRING: 1,\n    DATE: 2,\n    LIST_NUMBER: 3,\n    LIST_STRING: 4,\n    TIMESTAMP: 5,\n    RECENTLY_DATE_FROM: 6,\n    RECENTLY_DATE_TO: 7\n  },\n  SCHEMA: {\n    //coremgmt, simmgmt, rulemgmt, billing,  logging, monitoring, reporting\n    CORE: \"coremgmt\",\n    SIM: \"simmgmt\",\n    RULE: \"rulemgmt\",\n    BILL: \"billing\",\n    LOG: \"logging\",\n    MONITOR: \"monitoring\",\n    REPORT: \"reporting\",\n    ELASTICSEARCH: \"elasticsearch\"\n  },\n  DEVICE: {\n    SUCCESS: 200,\n    WRONG_FOMAT: 201,\n    FILE_TO_BIG: 202,\n    COLUMN_INVALID: 203,\n    FILE_IS_EMPTY: 204,\n    MAX_ROW_FILE_IMPORT: 205,\n    MSISDN_NOTEXITS: 211,\n    MSISDN_ASSIGN: 212,\n    MSISDN_INVALD: 213,\n    MSISDN_IS_EMPTY: 214,\n    MSISDN_IS_DUPLICATE: 215,\n    IMEI_IS_DUPLITE: 216,\n    EXPRIRED_DATE_INVALID: 217,\n    MSISDN_NOT_PERMISSION: 218,\n    IMEI_IS_EXSIT: 219,\n    IMEI_LEN: 220,\n    DEVICE_TYPE_LEN: 221,\n    COUNTRY_LEN: 222,\n    HAS_ERROR: 300\n  },\n  ALERT_SEVERITY: {\n    CRITICAL: 0,\n    MAJOR: 1,\n    MINOR: 2,\n    INFO: 3\n  },\n  DATE_TYPE: {\n    MONTH: 2,\n    DATE: 1,\n    DATETIME: 0\n  },\n  POLICY: {\n    PERSONAL_DATA_PROTECTION_POLICY: 1\n  },\n  POLICY_STATUS: {\n    AGREE: 0,\n    DISARGREE: 1\n  },\n  CHART_TYPE: {\n    BAR: \"bar\",\n    BUBBLE: \"bubble\",\n    DOUGHNUT: \"doughnut\",\n    PIE: \"pie\",\n    LINE: \"line\",\n    COMBO: \"combo\",\n    POLAR: \"polarArea\",\n    RADAR: \"radar\",\n    SCATTER: \"scatter\"\n  },\n  CHART_SUB_TYPE: {\n    VERTICAL_BAR: \"vertical\",\n    HORIZONTAL_BAR: \"horizontal\",\n    STACKED_BAR: \"stack\",\n    GROUP_BAR: \"group\",\n    MULTI_AXIS: \"multiAxis\",\n    THRESHOLD: \"threshold\",\n    SLIDER_THRESHOLD: \"sliderThreshold\"\n  },\n  REQUEST_STATUS: {\n    NEW: 0,\n    RECEIVED: 1,\n    IN_PROGRESS: 2,\n    REJECT: 3,\n    DONE: 4\n  },\n  REQUEST_TYPE: {\n    TEST_SIM: 1,\n    REPLACE_SIM: 0,\n    ORDER_SIM: 2,\n    ACTIVE_SIM: 3,\n    DIAGNOSE: 4\n  },\n  KEY_NOTIFY: {\n    TEST_SIM: 'test-sim',\n    REPLACE_SIM: 'replace-sim',\n    ORDER_SIM: 'order-sim',\n    TICKET: 'ticket',\n    ACTIVE_SIM: 'active-sim',\n    DIAGNOSE: 'diagnose'\n  },\n  SIM_TICKET_STATUS: {\n    NOT_ACTIVATED: 0,\n    AWAITING_ACTIVATION: 1,\n    ACTIVATED: 2\n  },\n  SIM_TYPE: {\n    UNKNOWN: 0,\n    ESIM: 1,\n    SIM: 2\n  },\n  COLOURS: {\n    '--blue-50': '#f5f9ff',\n    '--blue-100': '#d0e1fd',\n    '--blue-200': '#abc9fb',\n    '--blue-300': '#85b2f9',\n    '--blue-400': '#609af8',\n    '--blue-500': '#3b82f6',\n    '--blue-600': '#326fd1',\n    '--blue-700': '#295bac',\n    '--blue-800': '#204887',\n    '--blue-900': '#183462',\n    '--green-50': '#f4fcf7',\n    '--green-100': '#caf1d8',\n    '--green-200': '#a0e6ba',\n    '--green-300': '#76db9b',\n    '--green-400': '#4cd07d',\n    '--green-500': '#22c55e',\n    '--green-600': '#1da750',\n    '--green-700': '#188a42',\n    '--green-800': '#136c34',\n    '--green-900': '#0e4f26',\n    '--yellow-50': '#fefbf3',\n    '--yellow-100': '#faedc4',\n    '--yellow-200': '#f6de95',\n    '--yellow-300': '#f2d066',\n    '--yellow-400': '#eec137',\n    '--yellow-500': '#eab308',\n    '--yellow-600': '#c79807',\n    '--yellow-700': '#a47d06',\n    '--yellow-800': '#816204',\n    '--yellow-900': '#5e4803',\n    '--cyan-50': '#f3fbfd',\n    '--cyan-100': '#c3edf5',\n    '--cyan-200': '#94e0ed',\n    '--cyan-300': '#65d2e4',\n    '--cyan-400': '#35c4dc',\n    '--cyan-500': '#06b6d4',\n    '--cyan-600': '#059bb4',\n    '--cyan-700': '#047f94',\n    '--cyan-800': '#036475',\n    '--cyan-900': '#024955',\n    '--pink-50': '#fef6fa',\n    '--pink-100': '#fad3e7',\n    '--pink-200': '#f7b0d3',\n    '--pink-300': '#f38ec0',\n    '--pink-400': '#f06bac',\n    '--pink-500': '#ec4899',\n    '--pink-600': '#c93d82',\n    '--pink-700': '#a5326b',\n    '--pink-800': '#822854',\n    '--pink-900': '#5e1d3d',\n    '--indigo-50': '#f7f7fe',\n    '--indigo-100': '#dadafc',\n    '--indigo-200': '#bcbdf9',\n    '--indigo-300': '#9ea0f6',\n    '--indigo-400': '#8183f4',\n    '--indigo-500': '#6366f1',\n    '--indigo-600': '#5457cd',\n    '--indigo-700': '#4547a9',\n    '--indigo-800': '#363885',\n    '--indigo-900': '#282960',\n    '--teal-50': '#f3fbfb',\n    '--teal-100': '#c7eeea',\n    '--teal-200': '#9ae0d9',\n    '--teal-300': '#6dd3c8',\n    '--teal-400': '#41c5b7',\n    '--teal-500': '#14b8a6',\n    '--teal-600': '#119c8d',\n    '--teal-700': '#0e8174',\n    '--teal-800': '#0b655b',\n    '--teal-900': '#084a42',\n    '--orange-50': '#fff8f3',\n    '--orange-100': '#feddc7',\n    '--orange-200': '#fcc39b',\n    '--orange-300': '#fba86f',\n    '--orange-400': '#fa8e42',\n    '--orange-500': '#f97316',\n    '--orange-600': '#d46213',\n    '--orange-700': '#ae510f',\n    '--orange-800': '#893f0c',\n    '--orange-900': '#642e09',\n    '--bluegray-50': '#f7f8f9',\n    '--bluegray-100': '#dadee3',\n    '--bluegray-200': '#bcc3cd',\n    '--bluegray-300': '#9fa9b7',\n    '--bluegray-400': '#818ea1',\n    '--bluegray-500': '#64748b',\n    '--bluegray-600': '#556376',\n    '--bluegray-700': '#465161',\n    '--bluegray-800': '#37404c',\n    '--bluegray-900': '#282e38',\n    '--purple-50': '#fbf7ff',\n    '--purple-100': '#ead6fd',\n    '--purple-200': '#dab6fc',\n    '--purple-300': '#c996fa',\n    '--purple-400': '#b975f9',\n    '--purple-500': '#a855f7',\n    '--purple-600': '#8f48d2',\n    '--purple-700': '#763cad',\n    '--purple-800': '#5c2f88',\n    '--purple-900': '#432263',\n    '--red-50': '#fff5f5',\n    '--red-100': '#ffd0ce',\n    '--red-200': '#ffaca7',\n    '--red-300': '#ff8780',\n    '--red-400': '#ff6259',\n    '--red-500': '#ff3d32',\n    '--red-600': '#d9342b',\n    '--red-700': '#b32b23',\n    '--red-800': '#8c221c',\n    '--red-900': '#661814',\n    '--primary-50': '#f7f7fe',\n    '--primary-100': '#dadafc',\n    '--primary-200': '#bcbdf9',\n    '--primary-300': '#9ea0f6',\n    '--primary-400': '#8183f4',\n    '--primary-500': '#6366f1',\n    '--primary-600': '#5457cd',\n    '--primary-700': '#4547a9',\n    '--primary-800': '#363885',\n    '--primary-900': '#282960'\n  },\n  SERVICE_TYPE: {\n    PREPAID: 0,\n    POSTPAID: 1\n  },\n  ALERT_ACTION_TYPE: {\n    ALERT: 0,\n    API: 1\n  },\n  ALERT_RULE_CATEGORY: {\n    MONITORING: 0,\n    MANAGEMENT: 1\n  },\n  ALERT_EVENT_TYPE: {\n    EXCEEDED_PACKAGE: 1,\n    EXCEEDED_VALUE: 2,\n    SESSION_END: 3,\n    SESSION_START: 4,\n    SMS_EXCEEDED_PACKAGE: 5,\n    SMS_EXCEEDED_VALUE: 6,\n    ONE_WAY_LOCK: 7,\n    TWO_WAY_LOCK: 8,\n    ONE_WAY_TWO_WAY_LOCK: 9,\n    NO_CONECTION: 10,\n    SIM_EXP: 11,\n    DATAPOOL_EXP: 12,\n    SUB_EXP: 13,\n    DATA_WALLET_EXP: 14,\n    WALLET_THRESHOLD: 15\n  },\n  ALERT_UNIT: {\n    PERCENT: 1,\n    MB: 2,\n    SMS: 3\n  },\n  RECHARGE_TYPE: {\n    TOPUP: 'topup',\n    EZPAY: 'ezpay'\n  },\n  RECHARGE_STATUS: {\n    PENDING: 0,\n    PAID: 1\n  },\n  IMPORT_ERROR: {\n    emailDuplicated: 'Trùng email',\n    phoneDuplicated: 'Trùng SĐT',\n    partitionCodeDuplicated: 'Trùng mã phân vùng',\n    invalidData: 'Không có quyền thao tác với đối tượng này',\n    colMissing: 'File tải lên thiếu cột',\n    extraCol: 'File tải lên thừa cột',\n    wrongFormat: 'Sai định dạng file mẫu',\n    wrongFormatEmail: 'Sai định dạng Email',\n    wrongFormatPhone: 'Sai định dạng SĐT',\n    wrongFormatTraffic: 'Sai định dạng lưu lượng chia sẻ',\n    nameTooLong: 'Họ tên không được lớn hơn 50 ký tự',\n    emailTooLong: 'Email không được lớn hơn 100 ký tự',\n    notVNPTPhoneNumber: 'Vui lòng nhập số điện thoại thuộc nhà mạng Vinaphone để thực hiện chia sẻ!',\n    existPhoneNumber: 'Số điện thoại đã tồn tại trong danh sách chia sẻ. Vui lòng kiểm tra lại!',\n    emptyPhoneNumber: 'Số điện thoại không được bỏ trống',\n    exceedStorage: 'Lưu lượng chia sẻ vượt quá lưu lượng còn lại của ví',\n    minimumTraffic: 'Lưu lượng chia sẻ tối thiểu là 50p thoại, 10 SMS, Data 100MB',\n    minimumTrafficMinutes: 'Lưu lượng chia sẻ tối thiểu là 50p thoại',\n    minimumTrafficSMS: 'Lưu lượng chia sẻ tối thiểu SMS là 10 SMS',\n    minimumTrafficData: 'Lưu lượng chia sẻ tối thiểu data là 100MB',\n    wrongTraffic: 'Lưu lượng data phải là bội số của 100 MB, lưu lượng SMS là bội số của 5 SMS',\n    wrongTrafficSMS: 'Lưu lượng SMS là bội số của 5 SMS',\n    wrongTrafficData: 'Lưu lượng data phải là bội số của 100 MB',\n    wrongShareAutoFormat: 'Thông tin chia sẻ tự động phải là \"Có/Không\"',\n    invalidName: 'Sai định dạng ô họ tên. Chỉ cho phép dấu cách, chữ tiếng Việt (a-z, A-Z, 0-9, - _)'\n  },\n  DIAGNOSE: {\n    COVERAGE: {\n      EXCELLENT: 5,\n      GOOD: 4,\n      AVERAGE: 3,\n      POOR: 2,\n      BAD: 1\n    },\n    TYPE_NETWORK: {\n      UMTS: 3,\n      LTE: 4\n    }\n  },\n  METHOD_AUTO_SHARE: {\n    PAY_CODE: 0,\n    SUB_CODE: 1,\n    NONE: 2\n  },\n  RENEWAL_STATUS: {\n    NOT_DUE_YET: 0,\n    DUE: 1,\n    EXPIRED: 2\n  },\n  SHARE_GROUP: {\n    //Giới hạn số thuê bao trong nhóm chia sẻ, cập nhật khi lên production là 3000\n    LIMIT: 3000,\n    // Giới hạn số thuê bao được thêm vào nhóm chia sẻ trong một lần, cập nhật khi lên production là 50\n    LIMIT_ADD: 50\n  },\n  // [STC_CMP-1018] T-1 Khoảng thời gian cho phép chọn tối đa là 31 ngày\n  MAX_DATE_SELECTION_RANGE: 30,\n  ACTIVITY_HISTORY: {\n    STATUS: {\n      PROCESSING: 2,\n      COMPLETED: 3,\n      SUCCESSFUL: 1,\n      FAILED: 0\n    },\n    TYPE: {\n      SHARED_TRAFFIC: 1\n    },\n    PHONE_SHARED_STATUS: {\n      SUCCESS: 1,\n      FAILED: 0\n    }\n  }\n};\nexport const isVinaphoneNumber = number => number.startsWith('091') || number.startsWith('094') || number.startsWith('081') || number.startsWith('082') || number.startsWith('083') || number.startsWith('084') || number.startsWith('085') || number.startsWith('088');", "map": {"version": 3, "names": ["statusHttp", "permissions", "CONSTANTS", "MAX_TIME_HTTP_WAIT", "NUMBER_IMAGE_CAPTCHA", "HTTP_STATUS", "PERMISSIONS", "MAX_ROW_EXPORT", "MAX_ROW_EXCEL_EXPORT", "OBSERVABLE", "KEY_MESSAGE_COMMON", "KEY_INPUT_FILE_VNPT", "KEY_LOAD_CONFIRM_POLICY_HISTORY", "KEY_EXPIRED_PASSWORD", "UPDATE_SHARE_INFO", "SIM_STATUS", "READY", "ACTIVATED", "INACTIVED", "DEACTIVATED", "PURGED", "PROCESSING", "USER_TYPE", "ADMIN", "CUSTOMER", "PROVINCE", "DISTRICT", "AGENCY", "USER_STATUS", "ACTIVE", "INACTIVE", "ROlES_STATUS", "ROLE_TYPE", "ALL", "TELLER", "RATING_PLAN_STATUS", "CREATE_NEW", "PENDING", "REJECTED", "REMOVED", "RATING_PLAN_CYCLE", "DAY", "MONTH", "SUBSCRIPTION_TYPE", "POSTPAID", "PREPAID", "RATING_PLAN_SCOPE", "NATION_WIDE", "CUSTOMER_TYPE", "INTERPRISE", "PERSONAL", "CYCLE_TIME_UNITS", "WEEK", "YEAR", "RELOAD", "YES", "NO", "FLEXIBLE", "IP_TYPE", "DYNAMIC", "STATIC", "ALERT_STATUS", "ALERT_STATUS_SIM", "OUT_PLAN", "OUT_LINE", "DISCONNECTED", "NEW_CONNECTION", "GROUP_SCOPE", "GROUP_ADMIN", "GROUP_PROVINCE", "GROUP_CUSTOMER", "CUSTOMER_STATUS", "MODE_VIEW", "CREATE", "UPDATE", "DETAIL", "REPORT_STATUS", "WALLET_ACITVE_TYPE", "BUY", "SHARE", "ACCURACY", "REGISTER_NO_OTP", "CANCEL_REGISTER_NO_OTP", "REPORT_PREVIEW", "ENABLE", "DISABLE", "PARAMETER_TYPE", "NUMBER", "STRING", "DATE", "LIST_NUMBER", "LIST_STRING", "TIMESTAMP", "RECENTLY_DATE_FROM", "RECENTLY_DATE_TO", "SCHEMA", "CORE", "SIM", "RULE", "BILL", "LOG", "MONITOR", "REPORT", "ELASTICSEARCH", "DEVICE", "SUCCESS", "WRONG_FOMAT", "FILE_TO_BIG", "COLUMN_INVALID", "FILE_IS_EMPTY", "MAX_ROW_FILE_IMPORT", "MSISDN_NOTEXITS", "MSISDN_ASSIGN", "MSISDN_INVALD", "MSISDN_IS_EMPTY", "MSISDN_IS_DUPLICATE", "IMEI_IS_DUPLITE", "EXPRIRED_DATE_INVALID", "MSISDN_NOT_PERMISSION", "IMEI_IS_EXSIT", "IMEI_LEN", "DEVICE_TYPE_LEN", "COUNTRY_LEN", "HAS_ERROR", "ALERT_SEVERITY", "CRITICAL", "MAJOR", "MINOR", "INFO", "DATE_TYPE", "DATETIME", "POLICY", "PERSONAL_DATA_PROTECTION_POLICY", "POLICY_STATUS", "AGREE", "DISARGREE", "CHART_TYPE", "BAR", "BUBBLE", "DOUGHNUT", "PIE", "LINE", "COMBO", "POLAR", "RADAR", "SCATTER", "CHART_SUB_TYPE", "VERTICAL_BAR", "HORIZONTAL_BAR", "STACKED_BAR", "GROUP_BAR", "MULTI_AXIS", "THRESHOLD", "SLIDER_THRESHOLD", "REQUEST_STATUS", "NEW", "RECEIVED", "IN_PROGRESS", "REJECT", "DONE", "REQUEST_TYPE", "TEST_SIM", "REPLACE_SIM", "ORDER_SIM", "ACTIVE_SIM", "DIAGNOSE", "KEY_NOTIFY", "TICKET", "SIM_TICKET_STATUS", "NOT_ACTIVATED", "AWAITING_ACTIVATION", "SIM_TYPE", "UNKNOWN", "ESIM", "COLOURS", "SERVICE_TYPE", "ALERT_ACTION_TYPE", "ALERT", "API", "ALERT_RULE_CATEGORY", "MONITORING", "MANAGEMENT", "ALERT_EVENT_TYPE", "EXCEEDED_PACKAGE", "EXCEEDED_VALUE", "SESSION_END", "SESSION_START", "SMS_EXCEEDED_PACKAGE", "SMS_EXCEEDED_VALUE", "ONE_WAY_LOCK", "TWO_WAY_LOCK", "ONE_WAY_TWO_WAY_LOCK", "NO_CONECTION", "SIM_EXP", "DATAPOOL_EXP", "SUB_EXP", "DATA_WALLET_EXP", "WALLET_THRESHOLD", "ALERT_UNIT", "PERCENT", "MB", "SMS", "RECHARGE_TYPE", "TOPUP", "EZPAY", "RECHARGE_STATUS", "PAID", "IMPORT_ERROR", "emailDuplicated", "phoneDuplicated", "partitionCodeDuplicated", "invalidData", "col<PERSON>issing", "extraCol", "wrongFormat", "wrongFormatEmail", "wrongFormatPhone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nameTooLong", "emailTooLong", "notVNPTPhoneNumber", "existPhoneNumber", "emptyPhoneNumber", "exceedStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumTrafficMinutes", "minimumTrafficSMS", "minimumTrafficData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wrongTrafficSMS", "wrongTrafficData", "wrongShareAutoFormat", "invalid<PERSON><PERSON>", "COVERAGE", "EXCELLENT", "GOOD", "AVERAGE", "POOR", "BAD", "TYPE_NETWORK", "UMTS", "LTE", "METHOD_AUTO_SHARE", "PAY_CODE", "SUB_CODE", "NONE", "RENEWAL_STATUS", "NOT_DUE_YET", "DUE", "EXPIRED", "SHARE_GROUP", "LIMIT", "LIMIT_ADD", "MAX_DATE_SELECTION_RANGE", "ACTIVITY_HISTORY", "STATUS", "COMPLETED", "SUCCESSFUL", "FAILED", "TYPE", "SHARED_TRAFFIC", "PHONE_SHARED_STATUS", "isVinaphoneNumber", "number", "startsWith"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\comon\\constants.ts"], "sourcesContent": ["import statusHttp from \"./status.http\"\r\nimport permissions from \"./permissions\"\r\nexport const CONSTANTS = {\r\n    MAX_TIME_HTTP_WAIT: 30 * 1000,\r\n    NUMBER_IMAGE_CAPTCHA: 50,\r\n    HTTP_STATUS: statusHttp,\r\n    PERMISSIONS: permissions,\r\n    MAX_ROW_EXPORT: 1000000,\r\n    MAX_ROW_EXCEL_EXPORT: 100000,\r\n    OBSERVABLE:{\r\n        KEY_MESSAGE_COMMON: \"message-common\",\r\n        KEY_INPUT_FILE_VNPT: \"input-file-vnpt\",\r\n        KEY_LOAD_CONFIRM_POLICY_HISTORY: \"load-confirm-policy-history\",\r\n        KEY_EXPIRED_PASSWORD: \"expired-password\",\r\n        UPDATE_SHARE_INFO: \"update-share-info\"\r\n    },\r\n    SIM_STATUS: {\r\n        READY: 1,\r\n        ACTIVATED: 2,\r\n        INACTIVED: 3,\r\n        DEACTIVATED: 4,\r\n        PURGED: 5,\r\n        PROCESSING: 6,\r\n    },\r\n    USER_TYPE: {\r\n        ADMIN: 1,\r\n        CUSTOMER: 7,\r\n        PROVINCE: 2,\r\n        DISTRICT: 3,\r\n        AGENCY: 8\r\n    },\r\n    USER_STATUS: {\r\n        ACTIVE: 1,\r\n        INACTIVE: 0\r\n    },\r\n\r\n    ROlES_STATUS: {\r\n        ACTIVE: 1,\r\n        INACTIVE: 0\r\n    },\r\n    ROLE_TYPE: {\r\n        ALL: 0,\r\n        ADMIN: 1,\r\n        CUSTOMER: 7,\r\n        PROVINCE: 2,\r\n        TELLER: 3,\r\n        AGENCY: 8\r\n    },\r\n\r\n    RATING_PLAN_STATUS: {\r\n        CREATE_NEW: 2,\r\n        PENDING: 3,\r\n        ACTIVATED: 1,\r\n        DEACTIVATED: 5,\r\n        INACTIVE: 0,\r\n        REJECTED: 4,\r\n        REMOVED: -1\r\n    },\r\n\r\n    RATING_PLAN_CYCLE: {\r\n        DAY: 1,\r\n        MONTH: 3\r\n    },\r\n\r\n    SUBSCRIPTION_TYPE: {\r\n        POSTPAID: 0,\r\n        PREPAID: 1\r\n    },\r\n\r\n    RATING_PLAN_SCOPE: {\r\n        NATION_WIDE: 0,\r\n        CUSTOMER: 1,\r\n        PROVINCE: 2\r\n    },\r\n\r\n    CUSTOMER_TYPE: {\r\n        INTERPRISE: 2,\r\n        PERSONAL: 1,\r\n        AGENCY: 0\r\n    },\r\n\r\n    CYCLE_TIME_UNITS: {\r\n        DAY: 1,\r\n        WEEK: 2,\r\n        MONTH: 3,\r\n        YEAR: 4\r\n    },\r\n\r\n    RELOAD: {\r\n        YES: 1,\r\n        NO: 0\r\n    },\r\n\r\n    FLEXIBLE: {\r\n        YES: 1,\r\n        NO: 0\r\n    },\r\n    IP_TYPE: {\r\n        DYNAMIC: 1,\r\n        STATIC: 2\r\n    },\r\n\r\n    ALERT_STATUS: {\r\n        INACTIVE: 0,\r\n        ACTIVE: 1,\r\n    },\r\n\r\n    ALERT_STATUS_SIM: {\r\n        ALL: 0,\r\n        OUT_PLAN: 1,\r\n        OUT_LINE: 2,\r\n        DISCONNECTED: 3,\r\n        NEW_CONNECTION: 4,\r\n        // PURGED: 5,\r\n    },\r\n    GROUP_SCOPE: {\r\n        GROUP_ADMIN: 0,\r\n        GROUP_PROVINCE: 1,\r\n        GROUP_CUSTOMER: 2\r\n    },\r\n    CUSTOMER_STATUS: {\r\n        ACTIVE:1,\r\n        INACTIVE:0,\r\n        CREATE_NEW: 2\r\n    },\r\n    MODE_VIEW: {\r\n        CREATE: 0,\r\n        UPDATE: 1,\r\n        DETAIL: 2\r\n    },\r\n    REPORT_STATUS: {\r\n        ACTIVE: 1,\r\n        INACTIVE: 0\r\n    },\r\n    WALLET_ACITVE_TYPE: {\r\n        BUY: 0,\r\n        SHARE: 1,\r\n        ACCURACY:2,\r\n        REGISTER_NO_OTP:3,\r\n        CANCEL_REGISTER_NO_OTP:4,\r\n    },\r\n    REPORT_PREVIEW: {\r\n        ENABLE: 1,\r\n        DISABLE: 0\r\n    },\r\n    PARAMETER_TYPE: {\r\n        NUMBER: 0,\r\n        STRING: 1,\r\n        DATE: 2,\r\n        LIST_NUMBER: 3,\r\n        LIST_STRING: 4,\r\n        TIMESTAMP: 5,\r\n        RECENTLY_DATE_FROM: 6,\r\n        RECENTLY_DATE_TO: 7,\r\n    },\r\n    SCHEMA: {\r\n        //coremgmt, simmgmt, rulemgmt, billing,  logging, monitoring, reporting\r\n        CORE: \"coremgmt\",\r\n        SIM: \"simmgmt\",\r\n        RULE: \"rulemgmt\",\r\n        BILL: \"billing\",\r\n        LOG: \"logging\",\r\n        MONITOR: \"monitoring\",\r\n        REPORT: \"reporting\",\r\n        ELASTICSEARCH: \"elasticsearch\"\r\n    },\r\n    DEVICE: {\r\n        SUCCESS: 200,\r\n        WRONG_FOMAT: 201,\r\n        FILE_TO_BIG: 202,\r\n        COLUMN_INVALID: 203,\r\n        FILE_IS_EMPTY: 204,\r\n        MAX_ROW_FILE_IMPORT: 205,\r\n        MSISDN_NOTEXITS: 211,\r\n        MSISDN_ASSIGN : 212,\r\n        MSISDN_INVALD: 213,\r\n        MSISDN_IS_EMPTY: 214,\r\n        MSISDN_IS_DUPLICATE: 215,\r\n        IMEI_IS_DUPLITE: 216,\r\n        EXPRIRED_DATE_INVALID: 217,\r\n        MSISDN_NOT_PERMISSION: 218,\r\n        IMEI_IS_EXSIT: 219,\r\n        IMEI_LEN: 220,\r\n        DEVICE_TYPE_LEN: 221,\r\n        COUNTRY_LEN: 222,\r\n        HAS_ERROR: 300,\r\n    },\r\n    ALERT_SEVERITY: {\r\n        CRITICAL: 0,\r\n        MAJOR: 1,\r\n        MINOR: 2,\r\n        INFO: 3,\r\n    },\r\n    DATE_TYPE: {\r\n        MONTH: 2,\r\n        DATE: 1,\r\n        DATETIME: 0\r\n    },\r\n    POLICY: {\r\n        PERSONAL_DATA_PROTECTION_POLICY: 1\r\n    },\r\n    POLICY_STATUS: {\r\n        AGREE: 0,\r\n        DISARGREE: 1\r\n    },\r\n    CHART_TYPE: {\r\n        BAR: \"bar\",\r\n        BUBBLE: \"bubble\",\r\n        DOUGHNUT: \"doughnut\",\r\n        PIE: \"pie\",\r\n        LINE: \"line\",\r\n        COMBO: \"combo\",\r\n        POLAR: \"polarArea\",\r\n        RADAR: \"radar\",\r\n        SCATTER: \"scatter\"\r\n    },\r\n    CHART_SUB_TYPE: {\r\n        VERTICAL_BAR: \"vertical\",\r\n        HORIZONTAL_BAR: \"horizontal\",\r\n        STACKED_BAR: \"stack\",\r\n        GROUP_BAR: \"group\",\r\n        MULTI_AXIS: \"multiAxis\",\r\n        THRESHOLD: \"threshold\",\r\n        SLIDER_THRESHOLD: \"sliderThreshold\"\r\n    },\r\n    REQUEST_STATUS: {\r\n        NEW: 0,\r\n        RECEIVED : 1,\r\n        IN_PROGRESS : 2,\r\n        REJECT : 3,\r\n        DONE : 4\r\n    },\r\n    REQUEST_TYPE: {\r\n        TEST_SIM: 1,\r\n        REPLACE_SIM: 0,\r\n        ORDER_SIM: 2,\r\n        ACTIVE_SIM: 3,\r\n        DIAGNOSE: 4,\r\n    },\r\n    KEY_NOTIFY : {\r\n        TEST_SIM : 'test-sim',\r\n        REPLACE_SIM : 'replace-sim',\r\n        ORDER_SIM: 'order-sim',\r\n        TICKET : 'ticket',\r\n        ACTIVE_SIM: 'active-sim',\r\n        DIAGNOSE: 'diagnose',\r\n    },\r\n    SIM_TICKET_STATUS: {\r\n        NOT_ACTIVATED: 0,\r\n        AWAITING_ACTIVATION: 1,\r\n        ACTIVATED: 2,\r\n    },\r\n    SIM_TYPE: {\r\n      UNKNOWN: 0,\r\n      ESIM: 1,\r\n      SIM: 2,\r\n    },\r\n\r\n    COLOURS: {'--blue-50': '#f5f9ff','--blue-100': '#d0e1fd','--blue-200': '#abc9fb','--blue-300': '#85b2f9','--blue-400': '#609af8','--blue-500': '#3b82f6','--blue-600': '#326fd1','--blue-700': '#295bac','--blue-800': '#204887','--blue-900': '#183462','--green-50': '#f4fcf7','--green-100': '#caf1d8','--green-200': '#a0e6ba','--green-300': '#76db9b','--green-400': '#4cd07d','--green-500': '#22c55e','--green-600': '#1da750','--green-700': '#188a42','--green-800': '#136c34','--green-900': '#0e4f26','--yellow-50': '#fefbf3','--yellow-100': '#faedc4','--yellow-200': '#f6de95','--yellow-300': '#f2d066','--yellow-400': '#eec137','--yellow-500': '#eab308','--yellow-600': '#c79807','--yellow-700': '#a47d06','--yellow-800': '#816204','--yellow-900': '#5e4803','--cyan-50': '#f3fbfd','--cyan-100': '#c3edf5','--cyan-200': '#94e0ed','--cyan-300': '#65d2e4','--cyan-400': '#35c4dc','--cyan-500': '#06b6d4','--cyan-600': '#059bb4','--cyan-700': '#047f94','--cyan-800': '#036475','--cyan-900': '#024955','--pink-50': '#fef6fa','--pink-100': '#fad3e7','--pink-200': '#f7b0d3','--pink-300': '#f38ec0','--pink-400': '#f06bac','--pink-500': '#ec4899','--pink-600': '#c93d82','--pink-700': '#a5326b','--pink-800': '#822854','--pink-900': '#5e1d3d','--indigo-50': '#f7f7fe','--indigo-100': '#dadafc','--indigo-200': '#bcbdf9','--indigo-300': '#9ea0f6','--indigo-400': '#8183f4','--indigo-500': '#6366f1','--indigo-600': '#5457cd','--indigo-700': '#4547a9','--indigo-800': '#363885','--indigo-900': '#282960','--teal-50': '#f3fbfb','--teal-100': '#c7eeea','--teal-200': '#9ae0d9','--teal-300': '#6dd3c8','--teal-400': '#41c5b7','--teal-500': '#14b8a6','--teal-600': '#119c8d','--teal-700': '#0e8174','--teal-800': '#0b655b','--teal-900': '#084a42','--orange-50': '#fff8f3','--orange-100': '#feddc7','--orange-200': '#fcc39b','--orange-300': '#fba86f','--orange-400': '#fa8e42','--orange-500': '#f97316','--orange-600': '#d46213','--orange-700': '#ae510f','--orange-800': '#893f0c','--orange-900': '#642e09','--bluegray-50': '#f7f8f9','--bluegray-100': '#dadee3','--bluegray-200': '#bcc3cd','--bluegray-300': '#9fa9b7','--bluegray-400': '#818ea1','--bluegray-500': '#64748b','--bluegray-600': '#556376','--bluegray-700': '#465161','--bluegray-800': '#37404c','--bluegray-900': '#282e38','--purple-50': '#fbf7ff','--purple-100': '#ead6fd','--purple-200': '#dab6fc','--purple-300': '#c996fa','--purple-400': '#b975f9','--purple-500': '#a855f7','--purple-600': '#8f48d2','--purple-700': '#763cad','--purple-800': '#5c2f88','--purple-900': '#432263','--red-50': '#fff5f5','--red-100': '#ffd0ce','--red-200': '#ffaca7','--red-300': '#ff8780','--red-400': '#ff6259','--red-500': '#ff3d32','--red-600': '#d9342b','--red-700': '#b32b23','--red-800': '#8c221c','--red-900': '#661814','--primary-50': '#f7f7fe','--primary-100': '#dadafc','--primary-200': '#bcbdf9','--primary-300': '#9ea0f6','--primary-400': '#8183f4','--primary-500': '#6366f1','--primary-600': '#5457cd','--primary-700': '#4547a9','--primary-800': '#363885','--primary-900': '#282960'},\r\n\r\n    SERVICE_TYPE : {\r\n        PREPAID : 0,\r\n        POSTPAID : 1,\r\n    },\r\n\r\n    ALERT_ACTION_TYPE:{\r\n        ALERT: 0,\r\n        API: 1\r\n    },\r\n\r\n    ALERT_RULE_CATEGORY:{\r\n        MONITORING: 0,\r\n        MANAGEMENT: 1\r\n    },\r\n\r\n    ALERT_EVENT_TYPE:{\r\n        EXCEEDED_PACKAGE: 1,\r\n        EXCEEDED_VALUE : 2,\r\n        SESSION_END: 3,\r\n        SESSION_START: 4,\r\n        SMS_EXCEEDED_PACKAGE: 5,\r\n        SMS_EXCEEDED_VALUE: 6,\r\n        ONE_WAY_LOCK: 7,\r\n        TWO_WAY_LOCK: 8,\r\n        ONE_WAY_TWO_WAY_LOCK: 9,\r\n        NO_CONECTION: 10,\r\n        SIM_EXP: 11,\r\n        DATAPOOL_EXP: 12,\r\n        SUB_EXP: 13,\r\n        DATA_WALLET_EXP: 14,\r\n        WALLET_THRESHOLD: 15,\r\n    },\r\n    ALERT_UNIT: {\r\n        PERCENT: 1,\r\n        MB: 2,\r\n        SMS: 3,\r\n    },\r\n    RECHARGE_TYPE: {\r\n        TOPUP: 'topup',\r\n        EZPAY: 'ezpay',\r\n    },\r\n    RECHARGE_STATUS: {\r\n        PENDING: 0,\r\n        PAID: 1,\r\n    },\r\n\r\n    IMPORT_ERROR: {\r\n        emailDuplicated: 'Trùng email',\r\n        phoneDuplicated: 'Trùng SĐT',\r\n        partitionCodeDuplicated: 'Trùng mã phân vùng',\r\n        invalidData: 'Không có quyền thao tác với đối tượng này',\r\n        colMissing: 'File tải lên thiếu cột',\r\n        extraCol: 'File tải lên thừa cột',\r\n        wrongFormat: 'Sai định dạng file mẫu',\r\n        wrongFormatEmail: 'Sai định dạng Email',\r\n        wrongFormatPhone: 'Sai định dạng SĐT',\r\n        wrongFormatTraffic: 'Sai định dạng lưu lượng chia sẻ',\r\n        nameTooLong: 'Họ tên không được lớn hơn 50 ký tự',\r\n        emailTooLong: 'Email không được lớn hơn 100 ký tự',\r\n        notVNPTPhoneNumber: 'Vui lòng nhập số điện thoại thuộc nhà mạng Vinaphone để thực hiện chia sẻ!',\r\n        existPhoneNumber: 'Số điện thoại đã tồn tại trong danh sách chia sẻ. Vui lòng kiểm tra lại!',\r\n        emptyPhoneNumber: 'Số điện thoại không được bỏ trống',\r\n        exceedStorage: 'Lưu lượng chia sẻ vượt quá lưu lượng còn lại của ví',\r\n        minimumTraffic: 'Lưu lượng chia sẻ tối thiểu là 50p thoại, 10 SMS, Data 100MB',\r\n        minimumTrafficMinutes: 'Lưu lượng chia sẻ tối thiểu là 50p thoại',\r\n        minimumTrafficSMS: 'Lưu lượng chia sẻ tối thiểu SMS là 10 SMS',\r\n        minimumTrafficData: 'Lưu lượng chia sẻ tối thiểu data là 100MB',\r\n        wrongTraffic: 'Lưu lượng data phải là bội số của 100 MB, lưu lượng SMS là bội số của 5 SMS',\r\n        wrongTrafficSMS: 'Lưu lượng SMS là bội số của 5 SMS',\r\n        wrongTrafficData: 'Lưu lượng data phải là bội số của 100 MB',\r\n        wrongShareAutoFormat: 'Thông tin chia sẻ tự động phải là \"Có/Không\"',\r\n        invalidName: 'Sai định dạng ô họ tên. Chỉ cho phép dấu cách, chữ tiếng Việt (a-z, A-Z, 0-9, - _)'\r\n    },\r\n    DIAGNOSE: {\r\n        COVERAGE: {\r\n            EXCELLENT: 5,\r\n            GOOD: 4,\r\n            AVERAGE: 3,\r\n            POOR: 2,\r\n            BAD: 1,\r\n        },\r\n        TYPE_NETWORK: {\r\n            UMTS: 3,\r\n            LTE: 4,\r\n        }\r\n    },\r\n    METHOD_AUTO_SHARE: {\r\n        PAY_CODE: 0,\r\n        SUB_CODE: 1,\r\n        NONE: 2,\r\n    },\r\n    RENEWAL_STATUS: {\r\n        NOT_DUE_YET: 0,\r\n        DUE: 1,\r\n        EXPIRED: 2,\r\n    },\r\n    SHARE_GROUP: {\r\n        //Giới hạn số thuê bao trong nhóm chia sẻ, cập nhật khi lên production là 3000\r\n        LIMIT: 3000,\r\n        // Giới hạn số thuê bao được thêm vào nhóm chia sẻ trong một lần, cập nhật khi lên production là 50\r\n        LIMIT_ADD: 50,\r\n    },\r\n    // [STC_CMP-1018] T-1 Khoảng thời gian cho phép chọn tối đa là 31 ngày\r\n    MAX_DATE_SELECTION_RANGE: 30,\r\n\r\n    ACTIVITY_HISTORY: {\r\n        STATUS: {\r\n            PROCESSING: 2,\r\n            COMPLETED: 3,\r\n            SUCCESSFUL: 1,\r\n            FAILED: 0,\r\n        },\r\n        TYPE: {\r\n            SHARED_TRAFFIC: 1,\r\n        },\r\n        PHONE_SHARED_STATUS: {\r\n            SUCCESS: 1,\r\n            FAILED: 0,\r\n        }\r\n\r\n    }\r\n}\r\n\r\nexport const isVinaphoneNumber = (number) =>\r\n    number.startsWith('091') ||\r\n    number.startsWith('094') ||\r\n    number.startsWith('081') ||\r\n    number.startsWith('082') ||\r\n    number.startsWith('083') ||\r\n    number.startsWith('084') ||\r\n    number.startsWith('085') ||\r\n    number.startsWith('088');\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,eAAe;AACtC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,MAAMC,SAAS,GAAG;EACrBC,kBAAkB,EAAE,EAAE,GAAG,IAAI;EAC7BC,oBAAoB,EAAE,EAAE;EACxBC,WAAW,EAAEL,UAAU;EACvBM,WAAW,EAAEL,WAAW;EACxBM,cAAc,EAAE,OAAO;EACvBC,oBAAoB,EAAE,MAAM;EAC5BC,UAAU,EAAC;IACPC,kBAAkB,EAAE,gBAAgB;IACpCC,mBAAmB,EAAE,iBAAiB;IACtCC,+BAA+B,EAAE,6BAA6B;IAC9DC,oBAAoB,EAAE,kBAAkB;IACxCC,iBAAiB,EAAE;GACtB;EACDC,UAAU,EAAE;IACRC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,CAAC;IACdC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;GACf;EACDC,SAAS,EAAE;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;GACX;EACDC,WAAW,EAAE;IACTC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;GACb;EAEDC,YAAY,EAAE;IACVF,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;GACb;EACDE,SAAS,EAAE;IACPC,GAAG,EAAE,CAAC;IACNV,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXS,MAAM,EAAE,CAAC;IACTP,MAAM,EAAE;GACX;EAEDQ,kBAAkB,EAAE;IAChBC,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,CAAC;IACVpB,SAAS,EAAE,CAAC;IACZE,WAAW,EAAE,CAAC;IACdW,QAAQ,EAAE,CAAC;IACXQ,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;GACb;EAEDC,iBAAiB,EAAE;IACfC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE;GACV;EAEDC,iBAAiB,EAAE;IACfC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE;GACZ;EAEDC,iBAAiB,EAAE;IACfC,WAAW,EAAE,CAAC;IACdvB,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE;GACb;EAEDuB,aAAa,EAAE;IACXC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,CAAC;IACXvB,MAAM,EAAE;GACX;EAEDwB,gBAAgB,EAAE;IACdV,GAAG,EAAE,CAAC;IACNW,IAAI,EAAE,CAAC;IACPV,KAAK,EAAE,CAAC;IACRW,IAAI,EAAE;GACT;EAEDC,MAAM,EAAE;IACJC,GAAG,EAAE,CAAC;IACNC,EAAE,EAAE;GACP;EAEDC,QAAQ,EAAE;IACNF,GAAG,EAAE,CAAC;IACNC,EAAE,EAAE;GACP;EACDE,OAAO,EAAE;IACLC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE;GACX;EAEDC,YAAY,EAAE;IACV/B,QAAQ,EAAE,CAAC;IACXD,MAAM,EAAE;GACX;EAEDiC,gBAAgB,EAAE;IACd7B,GAAG,EAAE,CAAC;IACN8B,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE;IAChB;GACH;;EACDC,WAAW,EAAE;IACTC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE;GACnB;EACDC,eAAe,EAAE;IACb1C,MAAM,EAAC,CAAC;IACRC,QAAQ,EAAC,CAAC;IACVM,UAAU,EAAE;GACf;EACDoC,SAAS,EAAE;IACPC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;GACX;EACDC,aAAa,EAAE;IACX/C,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;GACb;EACD+C,kBAAkB,EAAE;IAChBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAC,CAAC;IACVC,eAAe,EAAC,CAAC;IACjBC,sBAAsB,EAAC;GAC1B;EACDC,cAAc,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;GACZ;EACDC,cAAc,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,CAAC;IACZC,kBAAkB,EAAE,CAAC;IACrBC,gBAAgB,EAAE;GACrB;EACDC,MAAM,EAAE;IACJ;IACAC,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE,SAAS;IACdC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE;GAClB;EACDC,MAAM,EAAE;IACJC,OAAO,EAAE,GAAG;IACZC,WAAW,EAAE,GAAG;IAChBC,WAAW,EAAE,GAAG;IAChBC,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE,GAAG;IAClBC,mBAAmB,EAAE,GAAG;IACxBC,eAAe,EAAE,GAAG;IACpBC,aAAa,EAAG,GAAG;IACnBC,aAAa,EAAE,GAAG;IAClBC,eAAe,EAAE,GAAG;IACpBC,mBAAmB,EAAE,GAAG;IACxBC,eAAe,EAAE,GAAG;IACpBC,qBAAqB,EAAE,GAAG;IAC1BC,qBAAqB,EAAE,GAAG;IAC1BC,aAAa,EAAE,GAAG;IAClBC,QAAQ,EAAE,GAAG;IACbC,eAAe,EAAE,GAAG;IACpBC,WAAW,EAAE,GAAG;IAChBC,SAAS,EAAE;GACd;EACDC,cAAc,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE;GACT;EACDC,SAAS,EAAE;IACPvF,KAAK,EAAE,CAAC;IACR+C,IAAI,EAAE,CAAC;IACPyC,QAAQ,EAAE;GACb;EACDC,MAAM,EAAE;IACJC,+BAA+B,EAAE;GACpC;EACDC,aAAa,EAAE;IACXC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE;GACd;EACDC,UAAU,EAAE;IACRC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE;GACZ;EACDC,cAAc,EAAE;IACZC,YAAY,EAAE,UAAU;IACxBC,cAAc,EAAE,YAAY;IAC5BC,WAAW,EAAE,OAAO;IACpBC,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE,WAAW;IACtBC,gBAAgB,EAAE;GACrB;EACDC,cAAc,EAAE;IACZC,GAAG,EAAE,CAAC;IACNC,QAAQ,EAAG,CAAC;IACZC,WAAW,EAAG,CAAC;IACfC,MAAM,EAAG,CAAC;IACVC,IAAI,EAAG;GACV;EACDC,YAAY,EAAE;IACVC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;GACb;EACDC,UAAU,EAAG;IACTL,QAAQ,EAAG,UAAU;IACrBC,WAAW,EAAG,aAAa;IAC3BC,SAAS,EAAE,WAAW;IACtBI,MAAM,EAAG,QAAQ;IACjBH,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE;GACb;EACDG,iBAAiB,EAAE;IACfC,aAAa,EAAE,CAAC;IAChBC,mBAAmB,EAAE,CAAC;IACtBzJ,SAAS,EAAE;GACd;EACD0J,QAAQ,EAAE;IACRC,OAAO,EAAE,CAAC;IACVC,IAAI,EAAE,CAAC;IACP5E,GAAG,EAAE;GACN;EAED6E,OAAO,EAAE;IAAC,WAAW,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,YAAY,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,gBAAgB,EAAE,SAAS;IAAC,aAAa,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,UAAU,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,WAAW,EAAE,SAAS;IAAC,cAAc,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE,SAAS;IAAC,eAAe,EAAE;EAAS,CAAC;EAEp9FC,YAAY,EAAG;IACXlI,OAAO,EAAG,CAAC;IACXD,QAAQ,EAAG;GACd;EAEDoI,iBAAiB,EAAC;IACdC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;GACR;EAEDC,mBAAmB,EAAC;IAChBC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;GACf;EAEDC,gBAAgB,EAAC;IACbC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAG,CAAC;IAClBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,oBAAoB,EAAE,CAAC;IACvBC,kBAAkB,EAAE,CAAC;IACrBC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,CAAC;IACfC,oBAAoB,EAAE,CAAC;IACvBC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE;GACrB;EACDC,UAAU,EAAE;IACRC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,GAAG,EAAE;GACR;EACDC,aAAa,EAAE;IACXC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;GACV;EACDC,eAAe,EAAE;IACbxK,OAAO,EAAE,CAAC;IACVyK,IAAI,EAAE;GACT;EAEDC,YAAY,EAAE;IACVC,eAAe,EAAE,aAAa;IAC9BC,eAAe,EAAE,WAAW;IAC5BC,uBAAuB,EAAE,oBAAoB;IAC7CC,WAAW,EAAE,2CAA2C;IACxDC,UAAU,EAAE,wBAAwB;IACpCC,QAAQ,EAAE,uBAAuB;IACjCC,WAAW,EAAE,wBAAwB;IACrCC,gBAAgB,EAAE,qBAAqB;IACvCC,gBAAgB,EAAE,mBAAmB;IACrCC,kBAAkB,EAAE,iCAAiC;IACrDC,WAAW,EAAE,oCAAoC;IACjDC,YAAY,EAAE,oCAAoC;IAClDC,kBAAkB,EAAE,4EAA4E;IAChGC,gBAAgB,EAAE,0EAA0E;IAC5FC,gBAAgB,EAAE,mCAAmC;IACrDC,aAAa,EAAE,qDAAqD;IACpEC,cAAc,EAAE,8DAA8D;IAC9EC,qBAAqB,EAAE,0CAA0C;IACjEC,iBAAiB,EAAE,2CAA2C;IAC9DC,kBAAkB,EAAE,2CAA2C;IAC/DC,YAAY,EAAE,6EAA6E;IAC3FC,eAAe,EAAE,mCAAmC;IACpDC,gBAAgB,EAAE,0CAA0C;IAC5DC,oBAAoB,EAAE,8CAA8C;IACpEC,WAAW,EAAE;GAChB;EACDnE,QAAQ,EAAE;IACNoE,QAAQ,EAAE;MACNC,SAAS,EAAE,CAAC;MACZC,IAAI,EAAE,CAAC;MACPC,OAAO,EAAE,CAAC;MACVC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE;KACR;IACDC,YAAY,EAAE;MACVC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE;;GAEZ;EACDC,iBAAiB,EAAE;IACfC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,IAAI,EAAE;GACT;EACDC,cAAc,EAAE;IACZC,WAAW,EAAE,CAAC;IACdC,GAAG,EAAE,CAAC;IACNC,OAAO,EAAE;GACZ;EACDC,WAAW,EAAE;IACT;IACAC,KAAK,EAAE,IAAI;IACX;IACAC,SAAS,EAAE;GACd;EACD;EACAC,wBAAwB,EAAE,EAAE;EAE5BC,gBAAgB,EAAE;IACdC,MAAM,EAAE;MACJ1O,UAAU,EAAE,CAAC;MACb2O,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,MAAM,EAAE;KACX;IACDC,IAAI,EAAE;MACFC,cAAc,EAAE;KACnB;IACDC,mBAAmB,EAAE;MACjB5J,OAAO,EAAE,CAAC;MACVyJ,MAAM,EAAE;;;CAInB;AAED,OAAO,MAAMI,iBAAiB,GAAIC,MAAM,IACpCA,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,IACxBD,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}