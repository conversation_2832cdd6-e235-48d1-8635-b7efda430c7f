{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, EventEmitter, Component, Input, HostBinding, Output, NgModule, forwardRef, Directive, HostListener } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { of, BehaviorSubject, Subject } from 'rxjs';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nconst RECAPTCHA_LANGUAGE = new InjectionToken(\"recaptcha-language\");\nconst RECAPTCHA_BASE_URL = new InjectionToken(\"recaptcha-base-url\");\nconst RECAPTCHA_NONCE = new InjectionToken(\"recaptcha-nonce-tag\");\nconst RECAPTCHA_SETTINGS = new InjectionToken(\"recaptcha-settings\");\nconst RECAPTCHA_V3_SITE_KEY = new InjectionToken(\"recaptcha-v3-site-key\");\nfunction loadScript(renderMode, onLoaded, urlParams, url, nonce) {\n  window.ng2recaptchaloaded = () => {\n    onLoaded(grecaptcha);\n  };\n  const script = document.createElement(\"script\");\n  script.innerHTML = \"\";\n  const baseUrl = url || \"https://www.google.com/recaptcha/api.js\";\n  script.src = `${baseUrl}?render=${renderMode}&onload=ng2recaptchaloaded${urlParams}`;\n  if (nonce) {\n    script.nonce = nonce;\n  }\n  script.async = true;\n  script.defer = true;\n  document.head.appendChild(script);\n}\nconst loader = {\n  loadScript\n};\nclass RecaptchaLoaderService {\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static {\n    this.ready = null;\n  }\n  constructor(\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  platformId, language, baseUrl, nonce, v3SiteKey) {\n    this.platformId = platformId;\n    this.language = language;\n    this.baseUrl = baseUrl;\n    this.nonce = nonce;\n    this.v3SiteKey = v3SiteKey;\n    this.init();\n    this.ready = isPlatformBrowser(this.platformId) ? RecaptchaLoaderService.ready.asObservable() : of();\n  }\n  /** @internal */\n  init() {\n    if (RecaptchaLoaderService.ready) {\n      return;\n    }\n    if (isPlatformBrowser(this.platformId)) {\n      const subject = new BehaviorSubject(null);\n      RecaptchaLoaderService.ready = subject;\n      const langParam = this.language ? \"&hl=\" + this.language : \"\";\n      const renderMode = this.v3SiteKey || \"explicit\";\n      loader.loadScript(renderMode, grecaptcha => subject.next(grecaptcha), langParam, this.baseUrl, this.nonce);\n    }\n  }\n  static {\n    this.ɵfac = function RecaptchaLoaderService_Factory(t) {\n      return new (t || RecaptchaLoaderService)(i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(RECAPTCHA_LANGUAGE, 8), i0.ɵɵinject(RECAPTCHA_BASE_URL, 8), i0.ɵɵinject(RECAPTCHA_NONCE, 8), i0.ɵɵinject(RECAPTCHA_V3_SITE_KEY, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RecaptchaLoaderService,\n      factory: RecaptchaLoaderService.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaLoaderService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_LANGUAGE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_BASE_URL]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_NONCE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_V3_SITE_KEY]\n      }]\n    }];\n  }, null);\n})();\nlet nextId = 0;\nclass RecaptchaComponent {\n  constructor(elementRef, loader, zone, settings) {\n    this.elementRef = elementRef;\n    this.loader = loader;\n    this.zone = zone;\n    this.id = `ngrecaptcha-${nextId++}`;\n    this.errorMode = \"default\";\n    this.resolved = new EventEmitter();\n    /**\n     * @deprecated `(error) output will be removed in the next major version. Use (errored) instead\n     */\n    // eslint-disable-next-line @angular-eslint/no-output-native\n    this.error = new EventEmitter();\n    this.errored = new EventEmitter();\n    if (settings) {\n      this.siteKey = settings.siteKey;\n      this.theme = settings.theme;\n      this.type = settings.type;\n      this.size = settings.size;\n      this.badge = settings.badge;\n    }\n  }\n  ngAfterViewInit() {\n    this.subscription = this.loader.ready.subscribe(grecaptcha => {\n      if (grecaptcha != null && grecaptcha.render instanceof Function) {\n        this.grecaptcha = grecaptcha;\n        this.renderRecaptcha();\n      }\n    });\n  }\n  ngOnDestroy() {\n    // reset the captcha to ensure it does not leave anything behind\n    // after the component is no longer needed\n    this.grecaptchaReset();\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  /**\n   * Executes the invisible recaptcha.\n   * Does nothing if component's size is not set to \"invisible\".\n   */\n  execute() {\n    if (this.size !== \"invisible\") {\n      return;\n    }\n    if (this.widget != null) {\n      void this.grecaptcha.execute(this.widget);\n    } else {\n      // delay execution of recaptcha until it actually renders\n      this.executeRequested = true;\n    }\n  }\n  reset() {\n    if (this.widget != null) {\n      if (this.grecaptcha.getResponse(this.widget)) {\n        // Only emit an event in case if something would actually change.\n        // That way we do not trigger \"touching\" of the control if someone does a \"reset\"\n        // on a non-resolved captcha.\n        this.resolved.emit(null);\n      }\n      this.grecaptchaReset();\n    }\n  }\n  /**\n   * ⚠️ Warning! Use this property at your own risk!\n   *\n   * While this member is `public`, it is not a part of the component's public API.\n   * The semantic versioning guarantees _will not be honored_! Thus, you might find that this property behavior changes in incompatible ways in minor or even patch releases.\n   * You are **strongly advised** against using this property.\n   * Instead, use more idiomatic ways to get reCAPTCHA value, such as `resolved` EventEmitter, or form-bound methods (ngModel, formControl, and the likes).å\n   */\n  get __unsafe_widgetValue() {\n    return this.widget != null ? this.grecaptcha.getResponse(this.widget) : null;\n  }\n  /** @internal */\n  expired() {\n    this.resolved.emit(null);\n  }\n  /** @internal */\n  onError(args) {\n    this.error.emit(args);\n    this.errored.emit(args);\n  }\n  /** @internal */\n  captchaResponseCallback(response) {\n    this.resolved.emit(response);\n  }\n  /** @internal */\n  grecaptchaReset() {\n    if (this.widget != null) {\n      this.zone.runOutsideAngular(() => this.grecaptcha.reset(this.widget));\n    }\n  }\n  /** @internal */\n  renderRecaptcha() {\n    // This `any` can be removed after @types/grecaptcha get updated\n    const renderOptions = {\n      badge: this.badge,\n      callback: response => {\n        this.zone.run(() => this.captchaResponseCallback(response));\n      },\n      \"expired-callback\": () => {\n        this.zone.run(() => this.expired());\n      },\n      sitekey: this.siteKey,\n      size: this.size,\n      tabindex: this.tabIndex,\n      theme: this.theme,\n      type: this.type\n    };\n    if (this.errorMode === \"handled\") {\n      renderOptions[\"error-callback\"] = (...args) => {\n        this.zone.run(() => this.onError(args));\n      };\n    }\n    this.widget = this.grecaptcha.render(this.elementRef.nativeElement, renderOptions);\n    if (this.executeRequested === true) {\n      this.executeRequested = false;\n      this.execute();\n    }\n  }\n  static {\n    this.ɵfac = function RecaptchaComponent_Factory(t) {\n      return new (t || RecaptchaComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(RecaptchaLoaderService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(RECAPTCHA_SETTINGS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: RecaptchaComponent,\n      selectors: [[\"re-captcha\"]],\n      hostVars: 1,\n      hostBindings: function RecaptchaComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        siteKey: \"siteKey\",\n        theme: \"theme\",\n        type: \"type\",\n        size: \"size\",\n        tabIndex: \"tabIndex\",\n        badge: \"badge\",\n        errorMode: \"errorMode\"\n      },\n      outputs: {\n        resolved: \"resolved\",\n        error: \"error\",\n        errored: \"errored\"\n      },\n      exportAs: [\"reCaptcha\"],\n      decls: 0,\n      vars: 0,\n      template: function RecaptchaComponent_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaComponent, [{\n    type: Component,\n    args: [{\n      exportAs: \"reCaptcha\",\n      selector: \"re-captcha\",\n      template: ``\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: RecaptchaLoaderService\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_SETTINGS]\n      }]\n    }];\n  }, {\n    id: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: [\"attr.id\"]\n    }],\n    siteKey: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    badge: [{\n      type: Input\n    }],\n    errorMode: [{\n      type: Input\n    }],\n    resolved: [{\n      type: Output\n    }],\n    error: [{\n      type: Output\n    }],\n    errored: [{\n      type: Output\n    }]\n  });\n})();\nclass RecaptchaCommonModule {\n  static {\n    this.ɵfac = function RecaptchaCommonModule_Factory(t) {\n      return new (t || RecaptchaCommonModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RecaptchaCommonModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaCommonModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [RecaptchaComponent],\n      exports: [RecaptchaComponent]\n    }]\n  }], null, null);\n})();\nclass RecaptchaModule {\n  static {\n    this.ɵfac = function RecaptchaModule_Factory(t) {\n      return new (t || RecaptchaModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RecaptchaModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [RecaptchaLoaderService],\n      imports: [RecaptchaCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaModule, [{\n    type: NgModule,\n    args: [{\n      exports: [RecaptchaComponent],\n      imports: [RecaptchaCommonModule],\n      providers: [RecaptchaLoaderService]\n    }]\n  }], null, null);\n})();\n\n/**\n * The main service for working with reCAPTCHA v3 APIs.\n *\n * Use the `execute` method for executing a single action, and\n * `onExecute` observable for listening to all actions at once.\n */\nclass ReCaptchaV3Service {\n  constructor(zone, siteKey,\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  platformId, baseUrl, nonce, language) {\n    /** @internal */\n    this.onLoadComplete = grecaptcha => {\n      this.grecaptcha = grecaptcha;\n      if (this.actionBacklog && this.actionBacklog.length > 0) {\n        this.actionBacklog.forEach(([action, subject]) => this.executeActionWithSubject(action, subject));\n        this.actionBacklog = undefined;\n      }\n    };\n    this.zone = zone;\n    this.isBrowser = isPlatformBrowser(platformId);\n    this.siteKey = siteKey;\n    this.nonce = nonce;\n    this.language = language;\n    this.baseUrl = baseUrl;\n    this.init();\n  }\n  get onExecute() {\n    if (!this.onExecuteSubject) {\n      this.onExecuteSubject = new Subject();\n      this.onExecuteObservable = this.onExecuteSubject.asObservable();\n    }\n    return this.onExecuteObservable;\n  }\n  get onExecuteError() {\n    if (!this.onExecuteErrorSubject) {\n      this.onExecuteErrorSubject = new Subject();\n      this.onExecuteErrorObservable = this.onExecuteErrorSubject.asObservable();\n    }\n    return this.onExecuteErrorObservable;\n  }\n  /**\n   * Executes the provided `action` with reCAPTCHA v3 API.\n   * Use the emitted token value for verification purposes on the backend.\n   *\n   * For more information about reCAPTCHA v3 actions and tokens refer to the official documentation at\n   * https://developers.google.com/recaptcha/docs/v3.\n   *\n   * @param {string} action the action to execute\n   * @returns {Observable<string>} an `Observable` that will emit the reCAPTCHA v3 string `token` value whenever ready.\n   * The returned `Observable` completes immediately after emitting a value.\n   */\n  execute(action) {\n    const subject = new Subject();\n    if (this.isBrowser) {\n      if (!this.grecaptcha) {\n        if (!this.actionBacklog) {\n          this.actionBacklog = [];\n        }\n        this.actionBacklog.push([action, subject]);\n      } else {\n        this.executeActionWithSubject(action, subject);\n      }\n    }\n    return subject.asObservable();\n  }\n  /** @internal */\n  executeActionWithSubject(action, subject) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const onError = error => {\n      this.zone.run(() => {\n        subject.error(error);\n        if (this.onExecuteErrorSubject) {\n          // We don't know any better at this point, unfortunately, so have to resort to `any`\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n          this.onExecuteErrorSubject.next({\n            action,\n            error\n          });\n        }\n      });\n    };\n    this.zone.runOutsideAngular(() => {\n      try {\n        this.grecaptcha.execute(this.siteKey, {\n          action\n        }).then(token => {\n          this.zone.run(() => {\n            subject.next(token);\n            subject.complete();\n            if (this.onExecuteSubject) {\n              this.onExecuteSubject.next({\n                action,\n                token\n              });\n            }\n          });\n        }, onError);\n      } catch (e) {\n        onError(e);\n      }\n    });\n  }\n  /** @internal */\n  init() {\n    if (this.isBrowser) {\n      if (\"grecaptcha\" in window) {\n        this.grecaptcha = grecaptcha;\n      } else {\n        const langParam = this.language ? \"&hl=\" + this.language : \"\";\n        loader.loadScript(this.siteKey, this.onLoadComplete, langParam, this.baseUrl, this.nonce);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function ReCaptchaV3Service_Factory(t) {\n      return new (t || ReCaptchaV3Service)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(RECAPTCHA_V3_SITE_KEY), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(RECAPTCHA_BASE_URL, 8), i0.ɵɵinject(RECAPTCHA_NONCE, 8), i0.ɵɵinject(RECAPTCHA_LANGUAGE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ReCaptchaV3Service,\n      factory: ReCaptchaV3Service.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReCaptchaV3Service, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [RECAPTCHA_V3_SITE_KEY]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_BASE_URL]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_NONCE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [RECAPTCHA_LANGUAGE]\n      }]\n    }];\n  }, null);\n})();\nclass RecaptchaV3Module {\n  static {\n    this.ɵfac = function RecaptchaV3Module_Factory(t) {\n      return new (t || RecaptchaV3Module)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RecaptchaV3Module\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [ReCaptchaV3Service]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaV3Module, [{\n    type: NgModule,\n    args: [{\n      providers: [ReCaptchaV3Service]\n    }]\n  }], null, null);\n})();\nclass RecaptchaValueAccessorDirective {\n  constructor(host) {\n    this.host = host;\n    this.requiresControllerReset = false;\n  }\n  writeValue(value) {\n    if (!value) {\n      this.host.reset();\n    } else {\n      // In this case, it is most likely that a form controller has requested to write a specific value into the component.\n      // This isn't really a supported case - reCAPTCHA values are single-use, and, in a sense, readonly.\n      // What this means is that the form controller has recaptcha control state of X, while reCAPTCHA itself can't \"restore\"\n      // to that state. In order to make form controller aware of this discrepancy, and to fix the said misalignment,\n      // we'll be telling the controller to \"reset\" the value back to null.\n      if (this.host.__unsafe_widgetValue !== value && Boolean(this.host.__unsafe_widgetValue) === false) {\n        this.requiresControllerReset = true;\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n    if (this.requiresControllerReset) {\n      this.requiresControllerReset = false;\n      this.onChange(null);\n    }\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  onResolve($event) {\n    if (this.onChange) {\n      this.onChange($event);\n    }\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n  static {\n    this.ɵfac = function RecaptchaValueAccessorDirective_Factory(t) {\n      return new (t || RecaptchaValueAccessorDirective)(i0.ɵɵdirectiveInject(RecaptchaComponent));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RecaptchaValueAccessorDirective,\n      selectors: [[\"re-captcha\", \"formControlName\", \"\"], [\"re-captcha\", \"formControl\", \"\"], [\"re-captcha\", \"ngModel\", \"\"]],\n      hostBindings: function RecaptchaValueAccessorDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resolved\", function RecaptchaValueAccessorDirective_resolved_HostBindingHandler($event) {\n            return ctx.onResolve($event);\n          });\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => RecaptchaValueAccessorDirective)\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaValueAccessorDirective, [{\n    type: Directive,\n    args: [{\n      providers: [{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => RecaptchaValueAccessorDirective)\n      }],\n      selector: \"re-captcha[formControlName],re-captcha[formControl],re-captcha[ngModel]\"\n    }]\n  }], function () {\n    return [{\n      type: RecaptchaComponent\n    }];\n  }, {\n    onResolve: [{\n      type: HostListener,\n      args: [\"resolved\", [\"$event\"]]\n    }]\n  });\n})();\nclass RecaptchaFormsModule {\n  static {\n    this.ɵfac = function RecaptchaFormsModule_Factory(t) {\n      return new (t || RecaptchaFormsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RecaptchaFormsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [FormsModule, RecaptchaCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaFormsModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [RecaptchaValueAccessorDirective],\n      exports: [RecaptchaValueAccessorDirective],\n      imports: [FormsModule, RecaptchaCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RECAPTCHA_BASE_URL, RECAPTCHA_LANGUAGE, RECAPTCHA_NONCE, RECAPTCHA_SETTINGS, RECAPTCHA_V3_SITE_KEY, ReCaptchaV3Service, RecaptchaComponent, RecaptchaFormsModule, RecaptchaLoaderService, RecaptchaModule, RecaptchaV3Module, RecaptchaValueAccessorDirective };", "map": {"version": 3, "names": ["i0", "InjectionToken", "PLATFORM_ID", "Injectable", "Inject", "Optional", "EventEmitter", "Component", "Input", "HostBinding", "Output", "NgModule", "forwardRef", "Directive", "HostListener", "isPlatformBrowser", "of", "BehaviorSubject", "Subject", "NG_VALUE_ACCESSOR", "FormsModule", "RECAPTCHA_LANGUAGE", "RECAPTCHA_BASE_URL", "RECAPTCHA_NONCE", "RECAPTCHA_SETTINGS", "RECAPTCHA_V3_SITE_KEY", "loadScript", "renderMode", "onLoaded", "urlParams", "url", "nonce", "window", "ng2recaptchaloaded", "gre<PERSON><PERSON>a", "script", "document", "createElement", "innerHTML", "baseUrl", "src", "async", "defer", "head", "append<PERSON><PERSON><PERSON>", "loader", "RecaptchaLoaderService", "ready", "constructor", "platformId", "language", "v3SiteKey", "init", "asObservable", "subject", "langParam", "next", "ɵfac", "RecaptchaLoaderService_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "Object", "decorators", "args", "undefined", "nextId", "RecaptchaComponent", "elementRef", "zone", "settings", "id", "errorMode", "resolved", "error", "errored", "siteKey", "theme", "size", "badge", "ngAfterViewInit", "subscription", "subscribe", "render", "Function", "renderRecaptcha", "ngOnDestroy", "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unsubscribe", "execute", "widget", "executeRequested", "reset", "getResponse", "emit", "__unsafe_widgetValue", "expired", "onError", "captchaResponseCallback", "response", "runOutsideAngular", "renderOptions", "callback", "run", "expired-callback", "sitekey", "tabindex", "tabIndex", "nativeElement", "RecaptchaComponent_Factory", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "RecaptchaComponent_HostBindings", "rf", "ctx", "ɵɵattribute", "inputs", "outputs", "exportAs", "decls", "vars", "template", "RecaptchaComponent_Template", "encapsulation", "selector", "RecaptchaCommonModule", "RecaptchaCommonModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports", "RecaptchaModule", "RecaptchaModule_Factory", "providers", "imports", "ReCaptchaV3Service", "onLoadComplete", "actionBacklog", "length", "for<PERSON>ach", "action", "executeActionWithSubject", "<PERSON><PERSON><PERSON><PERSON>", "onExecute", "onExecuteSubject", "onExecuteObservable", "onExecuteError", "onExecuteErrorSubject", "onExecuteErrorObservable", "push", "then", "complete", "e", "ReCaptchaV3Service_Factory", "RecaptchaV3Module", "RecaptchaV3Module_Factory", "RecaptchaValueAccessorDirective", "host", "requiresControllerReset", "writeValue", "value", "Boolean", "registerOnChange", "fn", "onChange", "registerOnTouched", "onTouched", "onResolve", "$event", "RecaptchaValueAccessorDirective_Factory", "ɵdir", "ɵɵdefineDirective", "RecaptchaValueAccessorDirective_HostBindings", "ɵɵlistener", "RecaptchaValueAccessorDirective_resolved_HostBindingHandler", "features", "ɵɵProvidersFeature", "multi", "provide", "useExisting", "RecaptchaFormsModule", "RecaptchaFormsModule_Factory"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/ng-recaptcha/fesm2022/ng-recaptcha.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, EventEmitter, Component, Input, HostBinding, Output, NgModule, forwardRef, Directive, HostListener } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { of, BehaviorSubject, Subject } from 'rxjs';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\n\nconst RECAPTCHA_LANGUAGE = new InjectionToken(\"recaptcha-language\");\nconst RECAPTCHA_BASE_URL = new InjectionToken(\"recaptcha-base-url\");\nconst RECAPTCHA_NONCE = new InjectionToken(\"recaptcha-nonce-tag\");\nconst RECAPTCHA_SETTINGS = new InjectionToken(\"recaptcha-settings\");\nconst RECAPTCHA_V3_SITE_KEY = new InjectionToken(\"recaptcha-v3-site-key\");\n\nfunction loadScript(renderMode, onLoaded, urlParams, url, nonce) {\n    window.ng2recaptchaloaded = () => {\n        onLoaded(grecaptcha);\n    };\n    const script = document.createElement(\"script\");\n    script.innerHTML = \"\";\n    const baseUrl = url || \"https://www.google.com/recaptcha/api.js\";\n    script.src = `${baseUrl}?render=${renderMode}&onload=ng2recaptchaloaded${urlParams}`;\n    if (nonce) {\n        script.nonce = nonce;\n    }\n    script.async = true;\n    script.defer = true;\n    document.head.appendChild(script);\n}\nconst loader = { loadScript };\n\nclass RecaptchaLoaderService {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static { this.ready = null; }\n    constructor(\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    platformId, language, baseUrl, nonce, v3SiteKey) {\n        this.platformId = platformId;\n        this.language = language;\n        this.baseUrl = baseUrl;\n        this.nonce = nonce;\n        this.v3SiteKey = v3SiteKey;\n        this.init();\n        this.ready = isPlatformBrowser(this.platformId) ? RecaptchaLoaderService.ready.asObservable() : of();\n    }\n    /** @internal */\n    init() {\n        if (RecaptchaLoaderService.ready) {\n            return;\n        }\n        if (isPlatformBrowser(this.platformId)) {\n            const subject = new BehaviorSubject(null);\n            RecaptchaLoaderService.ready = subject;\n            const langParam = this.language ? \"&hl=\" + this.language : \"\";\n            const renderMode = this.v3SiteKey || \"explicit\";\n            loader.loadScript(renderMode, (grecaptcha) => subject.next(grecaptcha), langParam, this.baseUrl, this.nonce);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaLoaderService, deps: [{ token: PLATFORM_ID }, { token: RECAPTCHA_LANGUAGE, optional: true }, { token: RECAPTCHA_BASE_URL, optional: true }, { token: RECAPTCHA_NONCE, optional: true }, { token: RECAPTCHA_V3_SITE_KEY, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaLoaderService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaLoaderService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_LANGUAGE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_BASE_URL]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_NONCE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_V3_SITE_KEY]\n                }] }]; } });\n\nlet nextId = 0;\nclass RecaptchaComponent {\n    constructor(elementRef, loader, zone, settings) {\n        this.elementRef = elementRef;\n        this.loader = loader;\n        this.zone = zone;\n        this.id = `ngrecaptcha-${nextId++}`;\n        this.errorMode = \"default\";\n        this.resolved = new EventEmitter();\n        /**\n         * @deprecated `(error) output will be removed in the next major version. Use (errored) instead\n         */\n        // eslint-disable-next-line @angular-eslint/no-output-native\n        this.error = new EventEmitter();\n        this.errored = new EventEmitter();\n        if (settings) {\n            this.siteKey = settings.siteKey;\n            this.theme = settings.theme;\n            this.type = settings.type;\n            this.size = settings.size;\n            this.badge = settings.badge;\n        }\n    }\n    ngAfterViewInit() {\n        this.subscription = this.loader.ready.subscribe((grecaptcha) => {\n            if (grecaptcha != null && grecaptcha.render instanceof Function) {\n                this.grecaptcha = grecaptcha;\n                this.renderRecaptcha();\n            }\n        });\n    }\n    ngOnDestroy() {\n        // reset the captcha to ensure it does not leave anything behind\n        // after the component is no longer needed\n        this.grecaptchaReset();\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n    /**\n     * Executes the invisible recaptcha.\n     * Does nothing if component's size is not set to \"invisible\".\n     */\n    execute() {\n        if (this.size !== \"invisible\") {\n            return;\n        }\n        if (this.widget != null) {\n            void this.grecaptcha.execute(this.widget);\n        }\n        else {\n            // delay execution of recaptcha until it actually renders\n            this.executeRequested = true;\n        }\n    }\n    reset() {\n        if (this.widget != null) {\n            if (this.grecaptcha.getResponse(this.widget)) {\n                // Only emit an event in case if something would actually change.\n                // That way we do not trigger \"touching\" of the control if someone does a \"reset\"\n                // on a non-resolved captcha.\n                this.resolved.emit(null);\n            }\n            this.grecaptchaReset();\n        }\n    }\n    /**\n     * ⚠️ Warning! Use this property at your own risk!\n     *\n     * While this member is `public`, it is not a part of the component's public API.\n     * The semantic versioning guarantees _will not be honored_! Thus, you might find that this property behavior changes in incompatible ways in minor or even patch releases.\n     * You are **strongly advised** against using this property.\n     * Instead, use more idiomatic ways to get reCAPTCHA value, such as `resolved` EventEmitter, or form-bound methods (ngModel, formControl, and the likes).å\n     */\n    get __unsafe_widgetValue() {\n        return this.widget != null ? this.grecaptcha.getResponse(this.widget) : null;\n    }\n    /** @internal */\n    expired() {\n        this.resolved.emit(null);\n    }\n    /** @internal */\n    onError(args) {\n        this.error.emit(args);\n        this.errored.emit(args);\n    }\n    /** @internal */\n    captchaResponseCallback(response) {\n        this.resolved.emit(response);\n    }\n    /** @internal */\n    grecaptchaReset() {\n        if (this.widget != null) {\n            this.zone.runOutsideAngular(() => this.grecaptcha.reset(this.widget));\n        }\n    }\n    /** @internal */\n    renderRecaptcha() {\n        // This `any` can be removed after @types/grecaptcha get updated\n        const renderOptions = {\n            badge: this.badge,\n            callback: (response) => {\n                this.zone.run(() => this.captchaResponseCallback(response));\n            },\n            \"expired-callback\": () => {\n                this.zone.run(() => this.expired());\n            },\n            sitekey: this.siteKey,\n            size: this.size,\n            tabindex: this.tabIndex,\n            theme: this.theme,\n            type: this.type,\n        };\n        if (this.errorMode === \"handled\") {\n            renderOptions[\"error-callback\"] = (...args) => {\n                this.zone.run(() => this.onError(args));\n            };\n        }\n        this.widget = this.grecaptcha.render(this.elementRef.nativeElement, renderOptions);\n        if (this.executeRequested === true) {\n            this.executeRequested = false;\n            this.execute();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaComponent, deps: [{ token: i0.ElementRef }, { token: RecaptchaLoaderService }, { token: i0.NgZone }, { token: RECAPTCHA_SETTINGS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.1\", type: RecaptchaComponent, selector: \"re-captcha\", inputs: { id: \"id\", siteKey: \"siteKey\", theme: \"theme\", type: \"type\", size: \"size\", tabIndex: \"tabIndex\", badge: \"badge\", errorMode: \"errorMode\" }, outputs: { resolved: \"resolved\", error: \"error\", errored: \"errored\" }, host: { properties: { \"attr.id\": \"this.id\" } }, exportAs: [\"reCaptcha\"], ngImport: i0, template: ``, isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaComponent, decorators: [{\n            type: Component,\n            args: [{\n                    exportAs: \"reCaptcha\",\n                    selector: \"re-captcha\",\n                    template: ``,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: RecaptchaLoaderService }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_SETTINGS]\n                }] }]; }, propDecorators: { id: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: [\"attr.id\"]\n            }], siteKey: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], badge: [{\n                type: Input\n            }], errorMode: [{\n                type: Input\n            }], resolved: [{\n                type: Output\n            }], error: [{\n                type: Output\n            }], errored: [{\n                type: Output\n            }] } });\n\nclass RecaptchaCommonModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaCommonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaCommonModule, declarations: [RecaptchaComponent], exports: [RecaptchaComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaCommonModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [RecaptchaComponent],\n                    exports: [RecaptchaComponent],\n                }]\n        }] });\n\nclass RecaptchaModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaModule, imports: [RecaptchaCommonModule], exports: [RecaptchaComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaModule, providers: [RecaptchaLoaderService], imports: [RecaptchaCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [RecaptchaComponent],\n                    imports: [RecaptchaCommonModule],\n                    providers: [RecaptchaLoaderService],\n                }]\n        }] });\n\n/**\n * The main service for working with reCAPTCHA v3 APIs.\n *\n * Use the `execute` method for executing a single action, and\n * `onExecute` observable for listening to all actions at once.\n */\nclass ReCaptchaV3Service {\n    constructor(zone, siteKey, \n    // eslint-disable-next-line @typescript-eslint/ban-types\n    platformId, baseUrl, nonce, language) {\n        /** @internal */\n        this.onLoadComplete = (grecaptcha) => {\n            this.grecaptcha = grecaptcha;\n            if (this.actionBacklog && this.actionBacklog.length > 0) {\n                this.actionBacklog.forEach(([action, subject]) => this.executeActionWithSubject(action, subject));\n                this.actionBacklog = undefined;\n            }\n        };\n        this.zone = zone;\n        this.isBrowser = isPlatformBrowser(platformId);\n        this.siteKey = siteKey;\n        this.nonce = nonce;\n        this.language = language;\n        this.baseUrl = baseUrl;\n        this.init();\n    }\n    get onExecute() {\n        if (!this.onExecuteSubject) {\n            this.onExecuteSubject = new Subject();\n            this.onExecuteObservable = this.onExecuteSubject.asObservable();\n        }\n        return this.onExecuteObservable;\n    }\n    get onExecuteError() {\n        if (!this.onExecuteErrorSubject) {\n            this.onExecuteErrorSubject = new Subject();\n            this.onExecuteErrorObservable = this.onExecuteErrorSubject.asObservable();\n        }\n        return this.onExecuteErrorObservable;\n    }\n    /**\n     * Executes the provided `action` with reCAPTCHA v3 API.\n     * Use the emitted token value for verification purposes on the backend.\n     *\n     * For more information about reCAPTCHA v3 actions and tokens refer to the official documentation at\n     * https://developers.google.com/recaptcha/docs/v3.\n     *\n     * @param {string} action the action to execute\n     * @returns {Observable<string>} an `Observable` that will emit the reCAPTCHA v3 string `token` value whenever ready.\n     * The returned `Observable` completes immediately after emitting a value.\n     */\n    execute(action) {\n        const subject = new Subject();\n        if (this.isBrowser) {\n            if (!this.grecaptcha) {\n                if (!this.actionBacklog) {\n                    this.actionBacklog = [];\n                }\n                this.actionBacklog.push([action, subject]);\n            }\n            else {\n                this.executeActionWithSubject(action, subject);\n            }\n        }\n        return subject.asObservable();\n    }\n    /** @internal */\n    executeActionWithSubject(action, subject) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const onError = (error) => {\n            this.zone.run(() => {\n                subject.error(error);\n                if (this.onExecuteErrorSubject) {\n                    // We don't know any better at this point, unfortunately, so have to resort to `any`\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    this.onExecuteErrorSubject.next({ action, error });\n                }\n            });\n        };\n        this.zone.runOutsideAngular(() => {\n            try {\n                this.grecaptcha.execute(this.siteKey, { action }).then((token) => {\n                    this.zone.run(() => {\n                        subject.next(token);\n                        subject.complete();\n                        if (this.onExecuteSubject) {\n                            this.onExecuteSubject.next({ action, token });\n                        }\n                    });\n                }, onError);\n            }\n            catch (e) {\n                onError(e);\n            }\n        });\n    }\n    /** @internal */\n    init() {\n        if (this.isBrowser) {\n            if (\"grecaptcha\" in window) {\n                this.grecaptcha = grecaptcha;\n            }\n            else {\n                const langParam = this.language ? \"&hl=\" + this.language : \"\";\n                loader.loadScript(this.siteKey, this.onLoadComplete, langParam, this.baseUrl, this.nonce);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: ReCaptchaV3Service, deps: [{ token: i0.NgZone }, { token: RECAPTCHA_V3_SITE_KEY }, { token: PLATFORM_ID }, { token: RECAPTCHA_BASE_URL, optional: true }, { token: RECAPTCHA_NONCE, optional: true }, { token: RECAPTCHA_LANGUAGE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: ReCaptchaV3Service }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: ReCaptchaV3Service, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [RECAPTCHA_V3_SITE_KEY]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_BASE_URL]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_NONCE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_LANGUAGE]\n                }] }]; } });\n\nclass RecaptchaV3Module {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaV3Module, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaV3Module }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaV3Module, providers: [ReCaptchaV3Service] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaV3Module, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [ReCaptchaV3Service],\n                }]\n        }] });\n\nclass RecaptchaValueAccessorDirective {\n    constructor(host) {\n        this.host = host;\n        this.requiresControllerReset = false;\n    }\n    writeValue(value) {\n        if (!value) {\n            this.host.reset();\n        }\n        else {\n            // In this case, it is most likely that a form controller has requested to write a specific value into the component.\n            // This isn't really a supported case - reCAPTCHA values are single-use, and, in a sense, readonly.\n            // What this means is that the form controller has recaptcha control state of X, while reCAPTCHA itself can't \"restore\"\n            // to that state. In order to make form controller aware of this discrepancy, and to fix the said misalignment,\n            // we'll be telling the controller to \"reset\" the value back to null.\n            if (this.host.__unsafe_widgetValue !== value && Boolean(this.host.__unsafe_widgetValue) === false) {\n                this.requiresControllerReset = true;\n            }\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n        if (this.requiresControllerReset) {\n            this.requiresControllerReset = false;\n            this.onChange(null);\n        }\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    onResolve($event) {\n        if (this.onChange) {\n            this.onChange($event);\n        }\n        if (this.onTouched) {\n            this.onTouched();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaValueAccessorDirective, deps: [{ token: RecaptchaComponent }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.0.1\", type: RecaptchaValueAccessorDirective, selector: \"re-captcha[formControlName],re-captcha[formControl],re-captcha[ngModel]\", host: { listeners: { \"resolved\": \"onResolve($event)\" } }, providers: [\n            {\n                multi: true,\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => RecaptchaValueAccessorDirective),\n            },\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaValueAccessorDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    providers: [\n                        {\n                            multi: true,\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => RecaptchaValueAccessorDirective),\n                        },\n                    ],\n                    selector: \"re-captcha[formControlName],re-captcha[formControl],re-captcha[ngModel]\",\n                }]\n        }], ctorParameters: function () { return [{ type: RecaptchaComponent }]; }, propDecorators: { onResolve: [{\n                type: HostListener,\n                args: [\"resolved\", [\"$event\"]]\n            }] } });\n\nclass RecaptchaFormsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaFormsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaFormsModule, declarations: [RecaptchaValueAccessorDirective], imports: [FormsModule, RecaptchaCommonModule], exports: [RecaptchaValueAccessorDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaFormsModule, imports: [FormsModule, RecaptchaCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.1\", ngImport: i0, type: RecaptchaFormsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [RecaptchaValueAccessorDirective],\n                    exports: [RecaptchaValueAccessorDirective],\n                    imports: [FormsModule, RecaptchaCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RECAPTCHA_BASE_URL, RECAPTCHA_LANGUAGE, RECAPTCHA_NONCE, RECAPTCHA_SETTINGS, RECAPTCHA_V3_SITE_KEY, ReCaptchaV3Service, RecaptchaComponent, RecaptchaFormsModule, RecaptchaLoaderService, RecaptchaModule, RecaptchaV3Module, RecaptchaValueAccessorDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,QAAQ,eAAe;AAC7L,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,EAAE,EAAEC,eAAe,EAAEC,OAAO,QAAQ,MAAM;AACnD,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,gBAAgB;AAE/D,MAAMC,kBAAkB,GAAG,IAAIpB,cAAc,CAAC,oBAAoB,CAAC;AACnE,MAAMqB,kBAAkB,GAAG,IAAIrB,cAAc,CAAC,oBAAoB,CAAC;AACnE,MAAMsB,eAAe,GAAG,IAAItB,cAAc,CAAC,qBAAqB,CAAC;AACjE,MAAMuB,kBAAkB,GAAG,IAAIvB,cAAc,CAAC,oBAAoB,CAAC;AACnE,MAAMwB,qBAAqB,GAAG,IAAIxB,cAAc,CAAC,uBAAuB,CAAC;AAEzE,SAASyB,UAAUA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC7DC,MAAM,CAACC,kBAAkB,GAAG,MAAM;IAC9BL,QAAQ,CAACM,UAAU,CAAC;EACxB,CAAC;EACD,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CF,MAAM,CAACG,SAAS,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGT,GAAG,IAAI,yCAAyC;EAChEK,MAAM,CAACK,GAAG,GAAI,GAAED,OAAQ,WAAUZ,UAAW,6BAA4BE,SAAU,EAAC;EACpF,IAAIE,KAAK,EAAE;IACPI,MAAM,CAACJ,KAAK,GAAGA,KAAK;EACxB;EACAI,MAAM,CAACM,KAAK,GAAG,IAAI;EACnBN,MAAM,CAACO,KAAK,GAAG,IAAI;EACnBN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,MAAM,CAAC;AACrC;AACA,MAAMU,MAAM,GAAG;EAAEnB;AAAW,CAAC;AAE7B,MAAMoB,sBAAsB,CAAC;EACzB;AACJ;AACA;AACA;EACI;IAAS,IAAI,CAACC,KAAK,GAAG,IAAI;EAAE;EAC5BC,WAAWA;EACX;EACAC,UAAU,EAAEC,QAAQ,EAAEX,OAAO,EAAER,KAAK,EAAEoB,SAAS,EAAE;IAC7C,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACX,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACR,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACoB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAI,CAACL,KAAK,GAAGhC,iBAAiB,CAAC,IAAI,CAACkC,UAAU,CAAC,GAAGH,sBAAsB,CAACC,KAAK,CAACM,YAAY,CAAC,CAAC,GAAGrC,EAAE,CAAC,CAAC;EACxG;EACA;EACAoC,IAAIA,CAAA,EAAG;IACH,IAAIN,sBAAsB,CAACC,KAAK,EAAE;MAC9B;IACJ;IACA,IAAIhC,iBAAiB,CAAC,IAAI,CAACkC,UAAU,CAAC,EAAE;MACpC,MAAMK,OAAO,GAAG,IAAIrC,eAAe,CAAC,IAAI,CAAC;MACzC6B,sBAAsB,CAACC,KAAK,GAAGO,OAAO;MACtC,MAAMC,SAAS,GAAG,IAAI,CAACL,QAAQ,GAAG,MAAM,GAAG,IAAI,CAACA,QAAQ,GAAG,EAAE;MAC7D,MAAMvB,UAAU,GAAG,IAAI,CAACwB,SAAS,IAAI,UAAU;MAC/CN,MAAM,CAACnB,UAAU,CAACC,UAAU,EAAGO,UAAU,IAAKoB,OAAO,CAACE,IAAI,CAACtB,UAAU,CAAC,EAAEqB,SAAS,EAAE,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACR,KAAK,CAAC;IAChH;EACJ;EACA;IAAS,IAAI,CAAC0B,IAAI,YAAAC,+BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFb,sBAAsB,EAAhC9C,EAAE,CAAA4D,QAAA,CAAgD1D,WAAW,GAA7DF,EAAE,CAAA4D,QAAA,CAAwEvC,kBAAkB,MAA5FrB,EAAE,CAAA4D,QAAA,CAAuHtC,kBAAkB,MAA3ItB,EAAE,CAAA4D,QAAA,CAAsKrC,eAAe,MAAvLvB,EAAE,CAAA4D,QAAA,CAAkNnC,qBAAqB;IAAA,CAA6D;EAAE;EACxY;IAAS,IAAI,CAACoC,KAAK,kBAD6E7D,EAAE,CAAA8D,kBAAA;MAAAC,KAAA,EACYjB,sBAAsB;MAAAkB,OAAA,EAAtBlB,sBAAsB,CAAAW;IAAA,EAAG;EAAE;AAC7I;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAHoGjE,EAAE,CAAAkE,iBAAA,CAGXpB,sBAAsB,EAAc,CAAC;IACpHqB,IAAI,EAAEhE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgE,IAAI,EAAEC,MAAM;MAAEC,UAAU,EAAE,CAAC;QAC3DF,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAACpE,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEiE,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAACjD,kBAAkB;MAC7B,CAAC;IAAE,CAAC,EAAE;MAAE8C,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAChD,kBAAkB;MAC7B,CAAC;IAAE,CAAC,EAAE;MAAE6C,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAC/C,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAE4C,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAC7C,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,IAAI+C,MAAM,GAAG,CAAC;AACd,MAAMC,kBAAkB,CAAC;EACrBzB,WAAWA,CAAC0B,UAAU,EAAE7B,MAAM,EAAE8B,IAAI,EAAEC,QAAQ,EAAE;IAC5C,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC7B,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC8B,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,EAAE,GAAI,eAAcL,MAAM,EAAG,EAAC;IACnC,IAAI,CAACM,SAAS,GAAG,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAG,IAAIzE,YAAY,CAAC,CAAC;IAClC;AACR;AACA;IACQ;IACA,IAAI,CAAC0E,KAAK,GAAG,IAAI1E,YAAY,CAAC,CAAC;IAC/B,IAAI,CAAC2E,OAAO,GAAG,IAAI3E,YAAY,CAAC,CAAC;IACjC,IAAIsE,QAAQ,EAAE;MACV,IAAI,CAACM,OAAO,GAAGN,QAAQ,CAACM,OAAO;MAC/B,IAAI,CAACC,KAAK,GAAGP,QAAQ,CAACO,KAAK;MAC3B,IAAI,CAAChB,IAAI,GAAGS,QAAQ,CAACT,IAAI;MACzB,IAAI,CAACiB,IAAI,GAAGR,QAAQ,CAACQ,IAAI;MACzB,IAAI,CAACC,KAAK,GAAGT,QAAQ,CAACS,KAAK;IAC/B;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC1C,MAAM,CAACE,KAAK,CAACyC,SAAS,CAAEtD,UAAU,IAAK;MAC5D,IAAIA,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACuD,MAAM,YAAYC,QAAQ,EAAE;QAC7D,IAAI,CAACxD,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAACyD,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,IAAI,CAACN,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACO,WAAW,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACX,IAAI,KAAK,WAAW,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACY,MAAM,IAAI,IAAI,EAAE;MACrB,KAAK,IAAI,CAAC9D,UAAU,CAAC6D,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC;IAC7C,CAAC,MACI;MACD;MACA,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAChC;EACJ;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,IAAI,CAAC9D,UAAU,CAACiE,WAAW,CAAC,IAAI,CAACH,MAAM,CAAC,EAAE;QAC1C;QACA;QACA;QACA,IAAI,CAACjB,QAAQ,CAACqB,IAAI,CAAC,IAAI,CAAC;MAC5B;MACA,IAAI,CAACP,eAAe,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIQ,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACL,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC9D,UAAU,CAACiE,WAAW,CAAC,IAAI,CAACH,MAAM,CAAC,GAAG,IAAI;EAChF;EACA;EACAM,OAAOA,CAAA,EAAG;IACN,IAAI,CAACvB,QAAQ,CAACqB,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAG,OAAOA,CAACjC,IAAI,EAAE;IACV,IAAI,CAACU,KAAK,CAACoB,IAAI,CAAC9B,IAAI,CAAC;IACrB,IAAI,CAACW,OAAO,CAACmB,IAAI,CAAC9B,IAAI,CAAC;EAC3B;EACA;EACAkC,uBAAuBA,CAACC,QAAQ,EAAE;IAC9B,IAAI,CAAC1B,QAAQ,CAACqB,IAAI,CAACK,QAAQ,CAAC;EAChC;EACA;EACAZ,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACG,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,CAACrB,IAAI,CAAC+B,iBAAiB,CAAC,MAAM,IAAI,CAACxE,UAAU,CAACgE,KAAK,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC;IACzE;EACJ;EACA;EACAL,eAAeA,CAAA,EAAG;IACd;IACA,MAAMgB,aAAa,GAAG;MAClBtB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBuB,QAAQ,EAAGH,QAAQ,IAAK;QACpB,IAAI,CAAC9B,IAAI,CAACkC,GAAG,CAAC,MAAM,IAAI,CAACL,uBAAuB,CAACC,QAAQ,CAAC,CAAC;MAC/D,CAAC;MACD,kBAAkB,EAAEK,CAAA,KAAM;QACtB,IAAI,CAACnC,IAAI,CAACkC,GAAG,CAAC,MAAM,IAAI,CAACP,OAAO,CAAC,CAAC,CAAC;MACvC,CAAC;MACDS,OAAO,EAAE,IAAI,CAAC7B,OAAO;MACrBE,IAAI,EAAE,IAAI,CAACA,IAAI;MACf4B,QAAQ,EAAE,IAAI,CAACC,QAAQ;MACvB9B,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBhB,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,IAAI,IAAI,CAACW,SAAS,KAAK,SAAS,EAAE;MAC9B6B,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAGrC,IAAI,KAAK;QAC3C,IAAI,CAACK,IAAI,CAACkC,GAAG,CAAC,MAAM,IAAI,CAACN,OAAO,CAACjC,IAAI,CAAC,CAAC;MAC3C,CAAC;IACL;IACA,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAAC9D,UAAU,CAACuD,MAAM,CAAC,IAAI,CAACf,UAAU,CAACwC,aAAa,EAAEP,aAAa,CAAC;IAClF,IAAI,IAAI,CAACV,gBAAgB,KAAK,IAAI,EAAE;MAChC,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACF,OAAO,CAAC,CAAC;IAClB;EACJ;EACA;IAAS,IAAI,CAACtC,IAAI,YAAA0D,2BAAAxD,CAAA;MAAA,YAAAA,CAAA,IAAwFc,kBAAkB,EA1J5BzE,EAAE,CAAAoH,iBAAA,CA0J4CpH,EAAE,CAACqH,UAAU,GA1J3DrH,EAAE,CAAAoH,iBAAA,CA0JsEtE,sBAAsB,GA1J9F9C,EAAE,CAAAoH,iBAAA,CA0JyGpH,EAAE,CAACsH,MAAM,GA1JpHtH,EAAE,CAAAoH,iBAAA,CA0J+H5F,kBAAkB;IAAA,CAA4D;EAAE;EACjT;IAAS,IAAI,CAAC+F,IAAI,kBA3J8EvH,EAAE,CAAAwH,iBAAA;MAAArD,IAAA,EA2JJM,kBAAkB;MAAAgD,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3JhB7H,EAAE,CAAA+H,WAAA,OAAAD,GAAA,CAAAjD,EAAA;QAAA;MAAA;MAAAmD,MAAA;QAAAnD,EAAA;QAAAK,OAAA;QAAAC,KAAA;QAAAhB,IAAA;QAAAiB,IAAA;QAAA6B,QAAA;QAAA5B,KAAA;QAAAP,SAAA;MAAA;MAAAmD,OAAA;QAAAlD,QAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA;MAAAiD,QAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,4BAAAT,EAAA,EAAAC,GAAA;MAAAS,aAAA;IAAA,EA2JyX;EAAE;AACje;AACA;EAAA,QAAAtE,SAAA,oBAAAA,SAAA,KA7JoGjE,EAAE,CAAAkE,iBAAA,CA6JXO,kBAAkB,EAAc,CAAC;IAChHN,IAAI,EAAE5D,SAAS;IACf+D,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,WAAW;MACrBM,QAAQ,EAAE,YAAY;MACtBH,QAAQ,EAAG;IACf,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElE,IAAI,EAAEnE,EAAE,CAACqH;IAAW,CAAC,EAAE;MAAElD,IAAI,EAAErB;IAAuB,CAAC,EAAE;MAAEqB,IAAI,EAAEnE,EAAE,CAACsH;IAAO,CAAC,EAAE;MAAEnD,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAC9IF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAC9C,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqD,EAAE,EAAE,CAAC;MACjCV,IAAI,EAAE3D;IACV,CAAC,EAAE;MACC2D,IAAI,EAAE1D,WAAW;MACjB6D,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEY,OAAO,EAAE,CAAC;MACVf,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE2E,KAAK,EAAE,CAAC;MACRhB,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE2D,IAAI,EAAE,CAAC;MACPA,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE4E,IAAI,EAAE,CAAC;MACPjB,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEyG,QAAQ,EAAE,CAAC;MACX9C,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE6E,KAAK,EAAE,CAAC;MACRlB,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEsE,SAAS,EAAE,CAAC;MACZX,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAEuE,QAAQ,EAAE,CAAC;MACXZ,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEsE,KAAK,EAAE,CAAC;MACRb,IAAI,EAAEzD;IACV,CAAC,CAAC;IAAEuE,OAAO,EAAE,CAAC;MACVd,IAAI,EAAEzD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+H,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAAChF,IAAI,YAAAiF,8BAAA/E,CAAA;MAAA,YAAAA,CAAA,IAAwF8E,qBAAqB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACE,IAAI,kBAtM8E3I,EAAE,CAAA4I,gBAAA;MAAAzE,IAAA,EAsMSsE;IAAqB,EAAsE;EAAE;EACxM;IAAS,IAAI,CAACI,IAAI,kBAvM8E7I,EAAE,CAAA8I,gBAAA,IAuMiC;EAAE;AACzI;AACA;EAAA,QAAA7E,SAAA,oBAAAA,SAAA,KAzMoGjE,EAAE,CAAAkE,iBAAA,CAyMXuE,qBAAqB,EAAc,CAAC;IACnHtE,IAAI,EAAExD,QAAQ;IACd2D,IAAI,EAAE,CAAC;MACCyE,YAAY,EAAE,CAACtE,kBAAkB,CAAC;MAClCuE,OAAO,EAAE,CAACvE,kBAAkB;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMwE,eAAe,CAAC;EAClB;IAAS,IAAI,CAACxF,IAAI,YAAAyF,wBAAAvF,CAAA;MAAA,YAAAA,CAAA,IAAwFsF,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACN,IAAI,kBAnN8E3I,EAAE,CAAA4I,gBAAA;MAAAzE,IAAA,EAmNS8E;IAAe,EAAoE;EAAE;EAChM;IAAS,IAAI,CAACJ,IAAI,kBApN8E7I,EAAE,CAAA8I,gBAAA;MAAAK,SAAA,EAoNqC,CAACrG,sBAAsB,CAAC;MAAAsG,OAAA,GAAYX,qBAAqB;IAAA,EAAI;EAAE;AAC1M;AACA;EAAA,QAAAxE,SAAA,oBAAAA,SAAA,KAtNoGjE,EAAE,CAAAkE,iBAAA,CAsNX+E,eAAe,EAAc,CAAC;IAC7G9E,IAAI,EAAExD,QAAQ;IACd2D,IAAI,EAAE,CAAC;MACC0E,OAAO,EAAE,CAACvE,kBAAkB,CAAC;MAC7B2E,OAAO,EAAE,CAACX,qBAAqB,CAAC;MAChCU,SAAS,EAAE,CAACrG,sBAAsB;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuG,kBAAkB,CAAC;EACrBrG,WAAWA,CAAC2B,IAAI,EAAEO,OAAO;EACzB;EACAjC,UAAU,EAAEV,OAAO,EAAER,KAAK,EAAEmB,QAAQ,EAAE;IAClC;IACA,IAAI,CAACoG,cAAc,GAAIpH,UAAU,IAAK;MAClC,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAI,IAAI,CAACqH,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QACrD,IAAI,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,CAACC,MAAM,EAAEpG,OAAO,CAAC,KAAK,IAAI,CAACqG,wBAAwB,CAACD,MAAM,EAAEpG,OAAO,CAAC,CAAC;QACjG,IAAI,CAACiG,aAAa,GAAGhF,SAAS;MAClC;IACJ,CAAC;IACD,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiF,SAAS,GAAG7I,iBAAiB,CAACkC,UAAU,CAAC;IAC9C,IAAI,CAACiC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACnD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACmB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACX,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACa,IAAI,CAAC,CAAC;EACf;EACA,IAAIyG,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI5I,OAAO,CAAC,CAAC;MACrC,IAAI,CAAC6I,mBAAmB,GAAG,IAAI,CAACD,gBAAgB,CAACzG,YAAY,CAAC,CAAC;IACnE;IACA,OAAO,IAAI,CAAC0G,mBAAmB;EACnC;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI/I,OAAO,CAAC,CAAC;MAC1C,IAAI,CAACgJ,wBAAwB,GAAG,IAAI,CAACD,qBAAqB,CAAC5G,YAAY,CAAC,CAAC;IAC7E;IACA,OAAO,IAAI,CAAC6G,wBAAwB;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInE,OAAOA,CAAC2D,MAAM,EAAE;IACZ,MAAMpG,OAAO,GAAG,IAAIpC,OAAO,CAAC,CAAC;IAC7B,IAAI,IAAI,CAAC0I,SAAS,EAAE;MAChB,IAAI,CAAC,IAAI,CAAC1H,UAAU,EAAE;QAClB,IAAI,CAAC,IAAI,CAACqH,aAAa,EAAE;UACrB,IAAI,CAACA,aAAa,GAAG,EAAE;QAC3B;QACA,IAAI,CAACA,aAAa,CAACY,IAAI,CAAC,CAACT,MAAM,EAAEpG,OAAO,CAAC,CAAC;MAC9C,CAAC,MACI;QACD,IAAI,CAACqG,wBAAwB,CAACD,MAAM,EAAEpG,OAAO,CAAC;MAClD;IACJ;IACA,OAAOA,OAAO,CAACD,YAAY,CAAC,CAAC;EACjC;EACA;EACAsG,wBAAwBA,CAACD,MAAM,EAAEpG,OAAO,EAAE;IACtC;IACA,MAAMiD,OAAO,GAAIvB,KAAK,IAAK;MACvB,IAAI,CAACL,IAAI,CAACkC,GAAG,CAAC,MAAM;QAChBvD,OAAO,CAAC0B,KAAK,CAACA,KAAK,CAAC;QACpB,IAAI,IAAI,CAACiF,qBAAqB,EAAE;UAC5B;UACA;UACA,IAAI,CAACA,qBAAqB,CAACzG,IAAI,CAAC;YAAEkG,MAAM;YAAE1E;UAAM,CAAC,CAAC;QACtD;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACL,IAAI,CAAC+B,iBAAiB,CAAC,MAAM;MAC9B,IAAI;QACA,IAAI,CAACxE,UAAU,CAAC6D,OAAO,CAAC,IAAI,CAACb,OAAO,EAAE;UAAEwE;QAAO,CAAC,CAAC,CAACU,IAAI,CAAErG,KAAK,IAAK;UAC9D,IAAI,CAACY,IAAI,CAACkC,GAAG,CAAC,MAAM;YAChBvD,OAAO,CAACE,IAAI,CAACO,KAAK,CAAC;YACnBT,OAAO,CAAC+G,QAAQ,CAAC,CAAC;YAClB,IAAI,IAAI,CAACP,gBAAgB,EAAE;cACvB,IAAI,CAACA,gBAAgB,CAACtG,IAAI,CAAC;gBAAEkG,MAAM;gBAAE3F;cAAM,CAAC,CAAC;YACjD;UACJ,CAAC,CAAC;QACN,CAAC,EAAEwC,OAAO,CAAC;MACf,CAAC,CACD,OAAO+D,CAAC,EAAE;QACN/D,OAAO,CAAC+D,CAAC,CAAC;MACd;IACJ,CAAC,CAAC;EACN;EACA;EACAlH,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACwG,SAAS,EAAE;MAChB,IAAI,YAAY,IAAI5H,MAAM,EAAE;QACxB,IAAI,CAACE,UAAU,GAAGA,UAAU;MAChC,CAAC,MACI;QACD,MAAMqB,SAAS,GAAG,IAAI,CAACL,QAAQ,GAAG,MAAM,GAAG,IAAI,CAACA,QAAQ,GAAG,EAAE;QAC7DL,MAAM,CAACnB,UAAU,CAAC,IAAI,CAACwD,OAAO,EAAE,IAAI,CAACoE,cAAc,EAAE/F,SAAS,EAAE,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACR,KAAK,CAAC;MAC7F;IACJ;EACJ;EACA;IAAS,IAAI,CAAC0B,IAAI,YAAA8G,2BAAA5G,CAAA;MAAA,YAAAA,CAAA,IAAwF0F,kBAAkB,EA3U5BrJ,EAAE,CAAA4D,QAAA,CA2U4C5D,EAAE,CAACsH,MAAM,GA3UvDtH,EAAE,CAAA4D,QAAA,CA2UkEnC,qBAAqB,GA3UzFzB,EAAE,CAAA4D,QAAA,CA2UoG1D,WAAW,GA3UjHF,EAAE,CAAA4D,QAAA,CA2U4HtC,kBAAkB,MA3UhJtB,EAAE,CAAA4D,QAAA,CA2U2KrC,eAAe,MA3U5LvB,EAAE,CAAA4D,QAAA,CA2UuNvC,kBAAkB;IAAA,CAA6D;EAAE;EAC1Y;IAAS,IAAI,CAACwC,KAAK,kBA5U6E7D,EAAE,CAAA8D,kBAAA;MAAAC,KAAA,EA4UYsF,kBAAkB;MAAArF,OAAA,EAAlBqF,kBAAkB,CAAA5F;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA9UoGjE,EAAE,CAAAkE,iBAAA,CA8UXmF,kBAAkB,EAAc,CAAC;IAChHlF,IAAI,EAAEhE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgE,IAAI,EAAEnE,EAAE,CAACsH;IAAO,CAAC,EAAE;MAAEnD,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QACnFF,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAC7C,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE0C,IAAI,EAAEC,MAAM;MAAEC,UAAU,EAAE,CAAC;QAC/BF,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAACpE,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEiE,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAChD,kBAAkB;MAC7B,CAAC;IAAE,CAAC,EAAE;MAAE6C,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAAC/C,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAE4C,IAAI,EAAEI,SAAS;MAAEF,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAE9D;MACV,CAAC,EAAE;QACC8D,IAAI,EAAE/D,MAAM;QACZkE,IAAI,EAAE,CAACjD,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMmJ,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAAC/G,IAAI,YAAAgH,0BAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAwF6G,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAAC7B,IAAI,kBAzW8E3I,EAAE,CAAA4I,gBAAA;MAAAzE,IAAA,EAyWSqG;IAAiB,EAAG;EAAE;EACjI;IAAS,IAAI,CAAC3B,IAAI,kBA1W8E7I,EAAE,CAAA8I,gBAAA;MAAAK,SAAA,EA0WuC,CAACE,kBAAkB;IAAC,EAAG;EAAE;AACtK;AACA;EAAA,QAAApF,SAAA,oBAAAA,SAAA,KA5WoGjE,EAAE,CAAAkE,iBAAA,CA4WXsG,iBAAiB,EAAc,CAAC;IAC/GrG,IAAI,EAAExD,QAAQ;IACd2D,IAAI,EAAE,CAAC;MACC6E,SAAS,EAAE,CAACE,kBAAkB;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMqB,+BAA+B,CAAC;EAClC1H,WAAWA,CAAC2H,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACxC;EACAC,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACH,IAAI,CAACzE,KAAK,CAAC,CAAC;IACrB,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACyE,IAAI,CAACtE,oBAAoB,KAAKyE,KAAK,IAAIC,OAAO,CAAC,IAAI,CAACJ,IAAI,CAACtE,oBAAoB,CAAC,KAAK,KAAK,EAAE;QAC/F,IAAI,CAACuE,uBAAuB,GAAG,IAAI;MACvC;IACJ;EACJ;EACAI,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGD,EAAE;IAClB,IAAI,IAAI,CAACL,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,GAAG,KAAK;MACpC,IAAI,CAACM,QAAQ,CAAC,IAAI,CAAC;IACvB;EACJ;EACAC,iBAAiBA,CAACF,EAAE,EAAE;IAClB,IAAI,CAACG,SAAS,GAAGH,EAAE;EACvB;EACAI,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,IAAI,CAACJ,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACI,MAAM,CAAC;IACzB;IACA,IAAI,IAAI,CAACF,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC,CAAC;IACpB;EACJ;EACA;IAAS,IAAI,CAAC3H,IAAI,YAAA8H,wCAAA5H,CAAA;MAAA,YAAAA,CAAA,IAAwF+G,+BAA+B,EAzZzC1K,EAAE,CAAAoH,iBAAA,CAyZyD3C,kBAAkB;IAAA,CAA4C;EAAE;EAC3N;IAAS,IAAI,CAAC+G,IAAI,kBA1Z8ExL,EAAE,CAAAyL,iBAAA;MAAAtH,IAAA,EA0ZJuG,+BAA+B;MAAAjD,SAAA;MAAAE,YAAA,WAAA+D,6CAAA7D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1Z7B7H,EAAE,CAAA2L,UAAA,sBAAAC,4DAAAN,MAAA;YAAA,OA0ZJxD,GAAA,CAAAuD,SAAA,CAAAC,MAAgB,CAAC;UAAA;QAAA;MAAA;MAAAO,QAAA,GA1Zf7L,EAAE,CAAA8L,kBAAA,CA0ZuL,CACjR;QACIC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE7K,iBAAiB;QAC1B8K,WAAW,EAAErL,UAAU,CAAC,MAAM8J,+BAA+B;MACjE,CAAC,CACJ;IAAA,EAAiB;EAAE;AAC5B;AACA;EAAA,QAAAzG,SAAA,oBAAAA,SAAA,KAlaoGjE,EAAE,CAAAkE,iBAAA,CAkaXwG,+BAA+B,EAAc,CAAC;IAC7HvG,IAAI,EAAEtD,SAAS;IACfyD,IAAI,EAAE,CAAC;MACC6E,SAAS,EAAE,CACP;QACI4C,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE7K,iBAAiB;QAC1B8K,WAAW,EAAErL,UAAU,CAAC,MAAM8J,+BAA+B;MACjE,CAAC,CACJ;MACDlC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErE,IAAI,EAAEM;IAAmB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE4G,SAAS,EAAE,CAAC;MAClGlH,IAAI,EAAErD,YAAY;MAClBwD,IAAI,EAAE,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACjC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4H,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACzI,IAAI,YAAA0I,6BAAAxI,CAAA;MAAA,YAAAA,CAAA,IAAwFuI,oBAAoB;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACvD,IAAI,kBArb8E3I,EAAE,CAAA4I,gBAAA;MAAAzE,IAAA,EAqbS+H;IAAoB,EAA+I;EAAE;EAChR;IAAS,IAAI,CAACrD,IAAI,kBAtb8E7I,EAAE,CAAA8I,gBAAA;MAAAM,OAAA,GAsbyChI,WAAW,EAAEqH,qBAAqB;IAAA,EAAI;EAAE;AACvL;AACA;EAAA,QAAAxE,SAAA,oBAAAA,SAAA,KAxboGjE,EAAE,CAAAkE,iBAAA,CAwbXgI,oBAAoB,EAAc,CAAC;IAClH/H,IAAI,EAAExD,QAAQ;IACd2D,IAAI,EAAE,CAAC;MACCyE,YAAY,EAAE,CAAC2B,+BAA+B,CAAC;MAC/C1B,OAAO,EAAE,CAAC0B,+BAA+B,CAAC;MAC1CtB,OAAO,EAAE,CAAChI,WAAW,EAAEqH,qBAAqB;IAChD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASnH,kBAAkB,EAAED,kBAAkB,EAAEE,eAAe,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAE4H,kBAAkB,EAAE5E,kBAAkB,EAAEyH,oBAAoB,EAAEpJ,sBAAsB,EAAEmG,eAAe,EAAEuB,iBAAiB,EAAEE,+BAA+B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}