{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class DeviceService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/device\";\n  }\n  detailDevice(msisdn, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  getById(msisdn, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  getByKey(key, value, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/getByKey`, {}, {\n      key,\n      value\n    }, callback, errorCallback, finallyCallback);\n  }\n  createDevice(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  updateDevice(msisdn, body, callback, errorCallback, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/${msisdn}`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  getListSubscription(msi, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(this.prefixApi + \"/simIsvalid\", {}, {\n      msi\n    }, callback, errorCallBack, finallyCallback);\n  }\n  uploadRegisterByFile(objectFile, callback) {\n    this.httpService.uploadFile(`${this.prefixApi}/import-device`, objectFile, {}, {}, callback);\n  }\n  downloadTemplate() {\n    this.httpService.downloadLocal(`/assets/data/device.xlsx`, \"device.xlsx\");\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  deleleDevice(msisdn, callback, errorCallBack, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  checkMsisdnAndDevice(msisdn, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(this.prefixApi + \"/checkExit/\" + msisdn, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  checkExistsImeiDevice(imei, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(this.prefixApi + \"/checkImei/\" + imei, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  demo(params, callback) {}\n  getLocation(msisdn, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`/media/getLocation`, {}, {\n      msisdn\n    }, callback, errorCallback, finallyCallback);\n  }\n  findCellId(params, callback, errorCallback, finallyCallback) {\n    this.httpService.get(this.prefixApi + \"/getLatLng\", {}, params, callback, errorCallback, finallyCallback);\n  }\n  findAddress(lat, lon, callback, errorCallback, finallyCallback) {\n    this.httpService.findAddress(lat, lon, callback, errorCallback, finallyCallback);\n  }\n  static {\n    this.ɵfac = function DeviceService_Factory(t) {\n      return new (t || DeviceService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DeviceService,\n      factory: DeviceService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "DeviceService", "constructor", "httpService", "prefixApi", "detailDevice", "msisdn", "callback", "<PERSON><PERSON><PERSON><PERSON>", "finally<PERSON><PERSON><PERSON>", "get", "getById", "get<PERSON><PERSON><PERSON><PERSON>", "key", "value", "createDevice", "body", "post", "updateDevice", "put", "getListSubscription", "msi", "errorCallBack", "uploadRegisterByFile", "objectFile", "uploadFile", "downloadTemplate", "downloadLocal", "search", "params", "deleleDevice", "delete", "checkMsisdnAndDevice", "checkExistsImeiDevice", "imei", "demo", "getLocation", "findCellId", "<PERSON><PERSON><PERSON><PERSON>", "lat", "lon", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\device\\DeviceService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class DeviceService{\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/device\";\r\n    }\r\n    public detailDevice(msisdn: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/${msisdn}`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public getById(msisdn: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/${msisdn}`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public createDevice(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public updateDevice(msisdn,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/${msisdn}`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public getListSubscription(msi: number,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n    this.httpService.get(this.prefixApi+\"/simIsvalid\",{},{msi}, callback,errorCallBack,finallyCallback);\r\n}\r\n    public uploadRegisterByFile(objectFile, callback:Function){\r\n        this.httpService.uploadFile(`${this.prefixApi}/import-device`, objectFile,{}, {}, callback);\r\n    }\r\n    public downloadTemplate(){\r\n        this.httpService.downloadLocal(`/assets/data/device.xlsx`, \"device.xlsx\");\r\n    }\r\n    public search(params: {\r\n        [key: string]: any\r\n    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {\r\n        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public deleleDevice(msisdn: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.delete(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public checkMsisdnAndDevice(msisdn: number,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(this.prefixApi+\"/checkExit/\" +msisdn,{},{}, callback,errorCallBack,finallyCallback);\r\n    }\r\n    public checkExistsImeiDevice(imei: string,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(this.prefixApi+\"/checkImei/\" +imei,{},{}, callback,errorCallBack,finallyCallback);\r\n    }\r\n    public demo(params, callback){\r\n\r\n    }\r\n\r\n    public getLocation(msisdn: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`/media/getLocation`,{}, {msisdn}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public findCellId(params: {\r\n        [key: string]: any\r\n    }, callback?:Function, errorCallback?:Function, finallyCallback?:Function){\r\n        this.httpService.get(this.prefixApi+\"/getLatLng\",{}, params, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public findAddress(lat, lon, callback?:Function, errorCallback?:Function, finallyCallback?:Function){\r\n        this.httpService.findAddress(lat, lon, callback, errorCallback, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,aAAa;EAEtBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,SAAS;EAC9B;EACOC,YAAYA,CAACC,MAAc,EAAEC,QAAmB,EAAEC,aAAuB,EAAEC,eAA0B;IACxG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIE,MAAM,EAAE,EAAC,EAAE,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;EAEOE,OAAOA,CAACL,MAAc,EAAEC,QAAmB,EAAEC,aAAuB,EAAEC,eAA0B;IACnG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIE,MAAM,EAAE,EAAC,EAAE,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;EAEOG,QAAQA,CAACC,GAAW,EAAEC,KAAa,EAAEP,QAAmB,EAAEC,aAAuB,EAAEC,eAA0B;IAChH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,WAAW,EAAC,EAAE,EAAE;MAACS,GAAG;MAAEC;IAAK,CAAC,EAAEP,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACjH;EAEOM,YAAYA,CAACC,IAAI,EAACT,QAAmB,EAAEC,aAAuB,EAAEC,eAA0B;IAC7F,IAAI,CAACN,WAAW,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,SAAS,EAAE,EAAE,EAAE,EAACY,IAAI,EAAC,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACpG;EAEOS,YAAYA,CAACZ,MAAM,EAACU,IAAI,EAACT,QAAmB,EAAEC,aAAuB,EAAEC,eAA0B;IACpG,IAAI,CAACN,WAAW,CAACgB,GAAG,CAAC,GAAG,IAAI,CAACf,SAAS,IAAIE,MAAM,EAAE,EAAE,EAAE,EAACU,IAAI,EAAC,EAAE,EAAET,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC7G;EACOW,mBAAmBA,CAACC,GAAW,EAACd,QAAkB,EAAEe,aAAuB,EAAEb,eAAyB;IAC7G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,aAAa,EAAC,EAAE,EAAC;MAACiB;IAAG,CAAC,EAAEd,QAAQ,EAACe,aAAa,EAACb,eAAe,CAAC;EACvG;EACWc,oBAAoBA,CAACC,UAAU,EAAEjB,QAAiB;IACrD,IAAI,CAACJ,WAAW,CAACsB,UAAU,CAAC,GAAG,IAAI,CAACrB,SAAS,gBAAgB,EAAEoB,UAAU,EAAC,EAAE,EAAE,EAAE,EAAEjB,QAAQ,CAAC;EAC/F;EACOmB,gBAAgBA,CAAA;IACnB,IAAI,CAACvB,WAAW,CAACwB,aAAa,CAAC,0BAA0B,EAAE,aAAa,CAAC;EAC7E;EACOC,MAAMA,CAACC,MAEb,EAAEtB,QAAmB,EAAEe,aAAwB,EAAEb,eAA0B;IACxE,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAE,EAAE,EAAEyB,MAAM,EAAEtB,QAAQ,EAAEe,aAAa,EAAEb,eAAe,CAAC;EAC1G;EACOqB,YAAYA,CAACxB,MAAc,EAAEC,QAAkB,EAAEe,aAAuB,EAAEb,eAAyB;IACtG,IAAI,CAACN,WAAW,CAAC4B,MAAM,CAAC,GAAG,IAAI,CAAC3B,SAAS,IAAIE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEC,QAAQ,EAAEe,aAAa,EAAEb,eAAe,CAAC;EAC5G;EACOuB,oBAAoBA,CAAC1B,MAAc,EAACC,QAAkB,EAAEe,aAAuB,EAAEb,eAAyB;IAC7G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,aAAa,GAAEE,MAAM,EAAC,EAAE,EAAC,EAAE,EAAEC,QAAQ,EAACe,aAAa,EAACb,eAAe,CAAC;EAC5G;EACOwB,qBAAqBA,CAACC,IAAY,EAAC3B,QAAkB,EAAEe,aAAuB,EAAEb,eAAyB;IAC5G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,aAAa,GAAE8B,IAAI,EAAC,EAAE,EAAC,EAAE,EAAE3B,QAAQ,EAACe,aAAa,EAACb,eAAe,CAAC;EAC1G;EACO0B,IAAIA,CAACN,MAAM,EAAEtB,QAAQ,GAE5B;EAEO6B,WAAWA,CAAC9B,MAAc,EAAEC,QAAmB,EAAEC,aAAuB,EAAEC,eAA0B;IACvG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,oBAAoB,EAAC,EAAE,EAAE;MAACJ;IAAM,CAAC,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrG;EAEO4B,UAAUA,CAACR,MAEjB,EAAEtB,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACrE,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,YAAY,EAAC,EAAE,EAAEyB,MAAM,EAAEtB,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1G;EAEO6B,WAAWA,CAACC,GAAG,EAAEC,GAAG,EAAEjC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC/F,IAAI,CAACN,WAAW,CAACmC,WAAW,CAACC,GAAG,EAAEC,GAAG,EAAEjC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACpF;;;uBA/DSR,aAAa,EAAAwC,EAAA,CAAAC,QAAA,CAEF1C,WAAW;IAAA;EAAA;;;aAFtBC,aAAa;MAAA0C,OAAA,EAAb1C,aAAa,CAAA2C;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}