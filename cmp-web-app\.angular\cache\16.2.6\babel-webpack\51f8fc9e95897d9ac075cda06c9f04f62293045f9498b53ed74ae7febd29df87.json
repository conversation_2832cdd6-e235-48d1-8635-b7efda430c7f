{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { CustomerService } from 'src/app/service/customer/CustomerService';\nimport { GroupSimService } from 'src/app/service/group-sim/GroupSimService';\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { ComponentBase } from 'src/app/component.base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/account/AccountService\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"src/app/service/group-sim/GroupSimService\";\nimport * as i9 from \"src/app/service/customer/CustomerService\";\nfunction UpdateGroupSimComponent_div_6_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"groupSim.scope.admin\"));\n  }\n}\nfunction UpdateGroupSimComponent_div_6_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"groupSim.scope.province\"));\n  }\n}\nfunction UpdateGroupSimComponent_div_6_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"groupSim.scope.customer\"));\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.tranService.translate(\"groupSim.error.requiredError\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.tranService.translate(\"groupSim.error.lengthError_16\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.tranService.translate(\"groupSim.error.characterError_code\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.tranService.translate(\"groupSim.error.existedError\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.tranService.translate(\"groupSim.error.requiredError\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.tranService.translate(\"groupSim.error.lengthError_255\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.tranService.translate(\"groupSim.error.characterError_name\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_37_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r15.customerDetail.customerName, \" - \", ctx_r15.customerDetail.customerCode, \"\");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"label\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtemplate(7, UpdateGroupSimComponent_div_6_div_37_span_7_Template, 2, 2, \"span\", 12);\n    i0.ɵɵtext(8, \"\\u00A0 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"groupSim.label.customer\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.customerDetail);\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_38_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.contractCode);\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"label\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtemplate(7, UpdateGroupSimComponent_div_6_div_38_span_7_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtext(8, \"\\u00A0 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"groupSim.label.contractCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.contractCode);\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_39_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.tranService.translate(\"groupSim.error.requiredError\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 9)(2, \"label\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9);\n    i0.ɵɵelement(9, \"div\", 16);\n    i0.ɵɵelementStart(10, \"div\", 17);\n    i0.ɵɵtemplate(11, UpdateGroupSimComponent_div_6_div_39_div_11_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getDisplayProvince(), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r13.updateGroupForm.controls[\"provinceCode\"] == null ? null : ctx_r13.updateGroupForm.controls[\"provinceCode\"].dirty) && ctx_r13.updateGroupForm.controls[\"provinceCode\"].hasError(\"required\"));\n  }\n}\nfunction UpdateGroupSimComponent_div_6_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.tranService.translate(\"groupSim.error.lengthError_255\"), \" \");\n  }\n}\nfunction UpdateGroupSimComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"form\", 7);\n    i0.ɵɵlistener(\"submit\", function UpdateGroupSimComponent_div_6_Template_form_submit_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.submitForm());\n    });\n    i0.ɵɵelementStart(2, \"div\", 8)(3, \"div\", 9)(4, \"label\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtemplate(7, UpdateGroupSimComponent_div_6_span_7_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(8, UpdateGroupSimComponent_div_6_span_8_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(9, UpdateGroupSimComponent_div_6_span_9_Template, 2, 1, \"span\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"label\", 13);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementStart(13, \"span\", 14);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 11)(16, \"input\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function UpdateGroupSimComponent_div_6_Template_input_ngModelChange_16_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.checkExistGroupKey());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 9);\n    i0.ɵɵelement(18, \"div\", 16);\n    i0.ɵɵelementStart(19, \"div\", 17);\n    i0.ɵɵtemplate(20, UpdateGroupSimComponent_div_6_div_20_Template, 2, 1, \"div\", 18);\n    i0.ɵɵtemplate(21, UpdateGroupSimComponent_div_6_div_21_Template, 2, 1, \"div\", 18);\n    i0.ɵɵtemplate(22, UpdateGroupSimComponent_div_6_div_22_Template, 2, 1, \"div\", 18);\n    i0.ɵɵtemplate(23, UpdateGroupSimComponent_div_6_div_23_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 9)(25, \"label\", 19);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementStart(27, \"span\", 14);\n    i0.ɵɵtext(28, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 11);\n    i0.ɵɵelement(30, \"input\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 9);\n    i0.ɵɵelement(32, \"div\", 16);\n    i0.ɵɵelementStart(33, \"div\", 17);\n    i0.ɵɵtemplate(34, UpdateGroupSimComponent_div_6_div_34_Template, 2, 1, \"div\", 18);\n    i0.ɵɵtemplate(35, UpdateGroupSimComponent_div_6_div_35_Template, 2, 1, \"div\", 18);\n    i0.ɵɵtemplate(36, UpdateGroupSimComponent_div_6_div_36_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, UpdateGroupSimComponent_div_6_div_37_Template, 9, 2, \"div\", 21);\n    i0.ɵɵtemplate(38, UpdateGroupSimComponent_div_6_div_38_Template, 9, 2, \"div\", 21);\n    i0.ɵɵtemplate(39, UpdateGroupSimComponent_div_6_div_39_Template, 12, 3, \"div\", 21);\n    i0.ɵɵelementStart(40, \"div\", 9)(41, \"label\", 22);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 11);\n    i0.ɵɵelement(44, \"textarea\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 9);\n    i0.ɵɵelement(46, \"div\", 16);\n    i0.ɵɵelementStart(47, \"div\", 24);\n    i0.ɵɵtemplate(48, UpdateGroupSimComponent_div_6_div_48_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 25)(50, \"a\", 26);\n    i0.ɵɵelement(51, \"button\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"p-button\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.updateGroupForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"groupSim.label.groupScope\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.groupScope == ctx_r0.groupScopeObjects.GROUP_ADMIN);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.groupScope == ctx_r0.groupScopeObjects.GROUP_PROVINCE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.groupScope == ctx_r0.groupScopeObjects.GROUP_CUSTOMER);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"groupSim.label.groupKey\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolderGroupKey);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.updateGroupForm.controls[\"groupKey\"] == null ? null : ctx_r0.updateGroupForm.controls[\"groupKey\"].dirty) && ctx_r0.updateGroupForm.controls[\"groupKey\"].hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updateGroupForm.controls[\"groupKey\"].hasError(\"maxlength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updateGroupForm.controls[\"groupKey\"].hasError(\"invalidCharacters\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isGroupKeyExists);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"groupSim.label.groupName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolderGroupName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.updateGroupForm.controls[\"name\"] == null ? null : ctx_r0.updateGroupForm.controls[\"name\"].dirty) && ctx_r0.updateGroupForm.controls[\"name\"].hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updateGroupForm.controls[\"name\"].hasError(\"maxlength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updateGroupForm.controls[\"name\"].hasError(\"invalidCharacters\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.groupScope == ctx_r0.groupScopeObjects.GROUP_CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.groupScope == ctx_r0.groupScopeObjects.GROUP_CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.groupScope == ctx_r0.groupScopeObjects.GROUP_PROVINCE);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"groupSim.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"ng-dirty\", ctx_r0.updateGroupForm.controls[\"description\"].invalid);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolderDescription);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updateGroupForm.controls[\"description\"].hasError(\"maxlength\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", ctx_r0.labelBtnCancel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r0.labelBtnSave)(\"disabled\", ctx_r0.updateGroupForm.invalid || ctx_r0.isGroupKeyExists);\n  }\n}\nexport class UpdateGroupSimComponent extends ComponentBase {\n  constructor(groupSimService, customerService, accountService, injector) {\n    super(injector);\n    this.groupSimService = groupSimService;\n    this.customerService = customerService;\n    this.accountService = accountService;\n    this.isDisableSave = false;\n    this.labelBtnSave = this.tranService.translate(\"groupSim.label.buttonSave\");\n    this.labelBtnCancel = this.tranService.translate(\"groupSim.label.buttonCancel\");\n    this.labelPlaceholderCustomer = this.tranService.translate(\"groupSim.placeHolder.customer\");\n    this.placeHolderGroupKey = this.tranService.translate(\"groupSim.placeHolder.groupKey\");\n    this.placeHolderGroupName = this.tranService.translate(\"groupSim.placeHolder.groupName\");\n    this.placeHolderDescription = this.tranService.translate(\"groupSim.placeHolder.description\");\n    this.groupScopeObjects = CONSTANTS.GROUP_SCOPE;\n    this.isGroupKeyExists = false;\n  }\n  customCharacterValidator() {\n    return control => {\n      const value = control.value;\n      const isValid = /^[a-zA-Z0-9 \\-_,\\s\\u00C0-\\u1EF9]*$/.test(value);\n      return isValid ? null : {\n        'invalidCharacters': {\n          value\n        }\n      };\n    };\n  }\n  customCodeCharacterValidator() {\n    return control => {\n      const value = control.value;\n      const isValid = /^[a-zA-Z0-9\\-_]*$/.test(value);\n      return isValid ? null : {\n        'invalidCharacters': {\n          value\n        }\n      };\n    };\n  }\n  checkExisted(query) {\n    return new Observable(observer => {\n      this.groupSimService.groupkeyCheckExisted({}, query, response => {\n        observer.next(response);\n        observer.complete();\n      });\n    });\n  }\n  submitForm() {\n    if (this.isGroupKeyExists) {\n      return this.messageCommonService.error(this.tranService.translate(\"groupSim.error.existedError\"));\n    }\n    let dataParams = {\n      ...this.groupDetail,\n      ...this.updateGroupForm.value\n    };\n    let me = this;\n    this.groupSimService.updateSimGroup(this.groupId + \"\", {}, dataParams, {}, response => {\n      me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      this.router.navigate(['/sims/group']);\n    });\n  }\n  ngOnInit() {\n    // let me = this;\n    this.getGroupDetail();\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.simmgmt\")\n    }, {\n      label: this.tranService.translate(\"groupSim.breadCrumb.group\"),\n      routerLink: '/sims/group'\n    }, {\n      label: this.tranService.translate(\"groupSim.breadCrumb.update\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  checkExistGroupKey() {\n    this.isGroupKeyExists = false;\n    let me = this;\n    if (this.updateGroupForm.value[\"groupKey\"] == this.groupDetail.groupKey) return;\n    this.debounceService.set(\"groupKey\", this.groupSimService.groupkeyCheckExisted.bind(this.groupSimService, {}, {\n      query: this.updateGroupForm.value[\"groupKey\"]\n    }, response => {\n      me.isGroupKeyExists = response == 1;\n    }));\n  }\n  getGroupDetail() {\n    let me = this;\n    this.groupId = parseInt(this.route.snapshot.paramMap.get(\"idgroup\"));\n    this.messageCommonService.onload();\n    this.groupSimService.getSimGroupById(this.groupId + \"\", {}, {}, response => {\n      me.groupDetail = response;\n      me.groupScope = me.groupDetail.scope;\n      me.groupScopeObjects = CONSTANTS.GROUP_SCOPE;\n      me.contractCode = response.contractCode;\n      if (me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER) {\n        this.getCustomerDetail();\n      }\n      if (me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE) {\n        me.accountService.getListProvince(response => {\n          this.provinces = response.map(el => {\n            return {\n              ...el,\n              display: `${el.name} - ${el.code}`\n            };\n          });\n        });\n      }\n      me.updateGroupForm = new FormGroup({\n        groupKey: new FormControl(me.groupDetail.groupKey, [Validators.required, Validators.maxLength(16), this.customCodeCharacterValidator()]),\n        name: new FormControl(me.groupDetail.name, [Validators.required, Validators.maxLength(255), this.customCharacterValidator()]),\n        description: new FormControl(me.groupDetail.description, [Validators.maxLength(255)])\n      });\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getCustomerDetail(name = \"\") {\n    let me = this;\n    me.customerService.getByKey(\"customerCode\", this.groupDetail.customerCode, response => {\n      if (response) {\n        me.customerDetail = response[0];\n      }\n    });\n  }\n  getDisplayProvince() {\n    if (this.groupDetail.provinceCode) {\n      let provinces = (this.provinces || []).filter(el => el.code == this.groupDetail.provinceCode);\n      if (provinces.length > 0) {\n        let province = provinces[0];\n        return province.display;\n      }\n    }\n    return \"\";\n  }\n  static {\n    this.ɵfac = function UpdateGroupSimComponent_Factory(t) {\n      return new (t || UpdateGroupSimComponent)(i0.ɵɵdirectiveInject(GroupSimService), i0.ɵɵdirectiveInject(CustomerService), i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateGroupSimComponent,\n      selectors: [[\"app-update-group-sim\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 7,\n      vars: 4,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-14\", \"py-3\"], [\"class\", \"card responsive-form\", 4, \"ngIf\"], [1, \"card\", \"responsive-form\"], [\"action\", \"\", 3, \"formGroup\", \"submit\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"grid-1\"], [1, \"flex\", \"justify-content-between\", \"col-12\", \"md:col-12\", \"py-0\"], [\"htmlFor\", \"groupScope\", 1, \"col-fixed\", \"pl-0\", 2, \"min-width\", \"110px\"], [1, \"col-11\", \"md:col-11\", \"pb-0\"], [4, \"ngIf\"], [\"htmlFor\", \"groupCode\", 1, \"my-auto\", 2, \"min-width\", \"110px\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"groupKey\", \"formControlName\", \"groupKey\", \"type\", \"text\", 3, \"placeholder\", \"ngModelChange\"], [1, \"my-auto\", 2, \"min-width\", \"110px\"], [1, \"col-11\", \"md:col-11\", \"py-0\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"groupName\", 1, \"my-auto\", 2, \"min-width\", \"110px\"], [\"pInputText\", \"\", \"id\", \"name\", \"formControlName\", \"name\", \"type\", \"text\", 3, \"placeholder\"], [\"class\", \"w-full\", 4, \"ngIf\"], [\"htmlFor\", \"description\", 1, \"py-3\", 2, \"min-width\", \"110px\"], [\"id\", \"description\", \"rows\", \"5\", \"cols\", \"30\", \"formControlName\", \"description\", \"pInputText\", \"\", 3, \"placeholder\"], [1, \"col-11\", \"md:col-11\", \"pt-0\"], [1, \"flex\", \"justify-content-center\", \"col-12\", \"md:col-12\", \"py-0\", \"gap-3\"], [\"routerLink\", \"/sims/group\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-button-secondary\", 3, \"label\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [1, \"w-full\"], [1, \"flex\", \"justify-content-between\", \"col-12\", \"md:col-12\", \"py-0\", \"flex-row\"], [\"htmlFor\", \"customerCode\", 1, \"my-auto\", 2, \"min-width\", \"110px\", \"height\", \"fit-content\"], [\"htmlFor\", \"customer\", 1, \"my-auto\", 2, \"min-width\", \"110px\"]],\n      template: function UpdateGroupSimComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, UpdateGroupSimComponent_div_6_Template, 53, 27, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"groupSim.breadCrumb.group\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.updateGroupForm);\n        }\n      },\n      dependencies: [i2.NgIf, i3.RouterLink, i4.Breadcrumb, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i6.InputText, i7.ButtonDirective, i7.Button],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "FormControl", "FormGroup", "Validators", "CustomerService", "GroupSimService", "CONSTANTS", "ComponentBase", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "tranService", "translate", "ctx_r2", "ctx_r3", "ɵɵtextInterpolate1", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r8", "ctx_r9", "ctx_r10", "ɵɵtextInterpolate2", "ctx_r15", "customerDetail", "customerName", "customerCode", "ɵɵtemplate", "UpdateGroupSimComponent_div_6_div_37_span_7_Template", "ctx_r11", "ɵɵproperty", "ctx_r16", "contractCode", "UpdateGroupSimComponent_div_6_div_38_span_7_Template", "ctx_r12", "ctx_r17", "ɵɵelement", "UpdateGroupSimComponent_div_6_div_39_div_11_Template", "ctx_r13", "getDisplayProvince", "updateGroupForm", "controls", "dirty", "<PERSON><PERSON><PERSON><PERSON>", "ctx_r14", "ɵɵlistener", "UpdateGroupSimComponent_div_6_Template_form_submit_1_listener", "ɵɵrestoreView", "_r19", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "submitForm", "UpdateGroupSimComponent_div_6_span_7_Template", "UpdateGroupSimComponent_div_6_span_8_Template", "UpdateGroupSimComponent_div_6_span_9_Template", "UpdateGroupSimComponent_div_6_Template_input_ngModelChange_16_listener", "ctx_r20", "checkExistGroupKey", "UpdateGroupSimComponent_div_6_div_20_Template", "UpdateGroupSimComponent_div_6_div_21_Template", "UpdateGroupSimComponent_div_6_div_22_Template", "UpdateGroupSimComponent_div_6_div_23_Template", "UpdateGroupSimComponent_div_6_div_34_Template", "UpdateGroupSimComponent_div_6_div_35_Template", "UpdateGroupSimComponent_div_6_div_36_Template", "UpdateGroupSimComponent_div_6_div_37_Template", "UpdateGroupSimComponent_div_6_div_38_Template", "UpdateGroupSimComponent_div_6_div_39_Template", "UpdateGroupSimComponent_div_6_div_48_Template", "ctx_r0", "groupScope", "groupScopeObjects", "GROUP_ADMIN", "GROUP_PROVINCE", "GROUP_CUSTOMER", "placeHolderGroupKey", "isGroupKeyExists", "placeHolderGroupName", "ɵɵclassProp", "invalid", "placeHolderDescription", "labelBtnCancel", "labelBtnSave", "UpdateGroupSimComponent", "constructor", "groupSimService", "customerService", "accountService", "injector", "isDisableSave", "labelPlaceholderCustomer", "GROUP_SCOPE", "customCharacterValidator", "control", "value", "<PERSON><PERSON><PERSON><PERSON>", "test", "customCodeCharacterValidator", "checkExisted", "query", "observer", "groupkeyCheckExisted", "response", "next", "complete", "messageCommonService", "error", "dataParams", "groupDetail", "me", "updateSimGroup", "groupId", "success", "router", "navigate", "ngOnInit", "getGroupDetail", "items", "label", "routerLink", "home", "icon", "groupKey", "debounceService", "set", "bind", "parseInt", "route", "snapshot", "paramMap", "get", "onload", "getSimGroupById", "scope", "getCustomerDetail", "getListProvince", "provinces", "map", "el", "display", "name", "code", "required", "max<PERSON><PERSON><PERSON>", "description", "offload", "get<PERSON><PERSON><PERSON><PERSON>", "provinceCode", "filter", "length", "province", "ɵɵdirectiveInject", "i1", "AccountService", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "UpdateGroupSimComponent_Template", "rf", "ctx", "UpdateGroupSimComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\group-sim\\update-group-sim\\update-group-sim.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\group-sim\\update-group-sim\\update-group-sim.component.html"], "sourcesContent": ["import { Observable } from 'rxjs';\r\nimport { Component, Inject, Injector } from '@angular/core';\r\nimport { AbstractControl, AsyncValidatorFn, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { TranslateService } from 'src/app/service/comon/translate.service';\r\nimport { CustomerService } from 'src/app/service/customer/CustomerService';\r\nimport { GroupSimService } from 'src/app/service/group-sim/GroupSimService';\r\nimport { debounceTime, switchMap, map } from 'rxjs/operators';\r\nimport { take } from 'rxjs/operators';\r\nimport { MessageCommonService } from 'src/app/service/comon/message-common.service';\r\nimport { CONSTANTS } from 'src/app/service/comon/constants';\r\nimport { AccountService } from 'src/app/service/account/AccountService';\r\nimport { DebounceInputService } from 'src/app/service/comon/debounce.input.service';\r\nimport { ComponentBase } from 'src/app/component.base';\r\n\r\n@Component({\r\n  selector: 'app-update-group-sim',\r\n  templateUrl: './update-group-sim.component.html',\r\n  // styleUrls: ['./create-group-sim.component.scss']\r\n})\r\nexport class UpdateGroupSimComponent extends ComponentBase {\r\n  customerDetail: any;\r\n  provinces: Array<any>;\r\n\r\n  isDisableSave = false;\r\n\r\n  labelBtnSave: string = this.tranService.translate(\"groupSim.label.buttonSave\");\r\n  labelBtnCancel: string = this.tranService.translate(\"groupSim.label.buttonCancel\");\r\n  labelPlaceholderCustomer: string = this.tranService.translate(\"groupSim.placeHolder.customer\");\r\n  placeHolderGroupKey:string = this.tranService.translate(\"groupSim.placeHolder.groupKey\")\r\n  placeHolderGroupName:string = this.tranService.translate(\"groupSim.placeHolder.groupName\")\r\n  placeHolderDescription: string = this.tranService.translate(\"groupSim.placeHolder.description\")\r\n  items: MenuItem[];\r\n  home: MenuItem\r\n  groupScope: number;\r\n  groupScopeObjects: any = CONSTANTS.GROUP_SCOPE;\r\n  isGroupKeyExists: boolean = false;\r\n  groupDetail: any;\r\n  contractCode: any;\r\n  groupId: number;\r\n  constructor(@Inject(GroupSimService) private groupSimService: GroupSimService,\r\n              @Inject(CustomerService) private customerService: CustomerService,\r\n              private accountService: AccountService, injector: Injector){super(injector);}\r\n\r\n  customCharacterValidator(): ValidatorFn {\r\n    return (control: AbstractControl): ValidationErrors | null => {\r\n      const value = control.value;\r\n      const isValid = /^[a-zA-Z0-9 \\-_,\\s\\u00C0-\\u1EF9]*$/.test(value);\r\n      return isValid ? null : { 'invalidCharacters': { value } };\r\n    };\r\n  }\r\n\r\n  customCodeCharacterValidator(): ValidatorFn {\r\n    return (control: AbstractControl): ValidationErrors | null => {\r\n      const value = control.value;\r\n      const isValid = /^[a-zA-Z0-9\\-_]*$/.test(value);\r\n      return isValid ? null : { 'invalidCharacters': { value } };\r\n    };\r\n  }\r\n\r\n  checkExisted(query: {}): Observable<number> {\r\n    return new Observable(observer => {\r\n      this.groupSimService.groupkeyCheckExisted({},query, (response) => {\r\n        observer.next(response);\r\n        observer.complete();\r\n      });\r\n    });\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * ^[a-zA-Z0-9 .\\-_,\\s\\u00C0-\\u1EF9]*$ cho biết những kí tự được phép\r\n   *    \\u00C0-\\u1EF9 là range của Vietnamese'Unicode characters\r\n   *    a-zA-Z0-9 cho phép kí tự chữ và số\r\n   *    .\\-_, cho phép _ và - còn \\s cho phép blankspace\r\n   */\r\n\r\n  updateGroupForm: any;\r\n\r\n  submitForm(){\r\n    if(this.isGroupKeyExists){\r\n      return this.messageCommonService.error(this.tranService.translate(\"groupSim.error.existedError\"))\r\n    }\r\n    let dataParams = {...this.groupDetail, ...this.updateGroupForm.value}\r\n    let me = this;\r\n    this.groupSimService.updateSimGroup(this.groupId+\"\",{}, dataParams,{},(response)=>{\r\n      me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"))\r\n      this.router.navigate(['/sims/group']);\r\n    })\r\n  }\r\n\r\n  ngOnInit(){\r\n    // let me = this;\r\n    this.getGroupDetail();\r\n    this.items = [{ label: this.tranService.translate(\"global.menu.simmgmt\") }, { label: this.tranService.translate(\"groupSim.breadCrumb.group\"), routerLink: '/sims/group' }, { label: this.tranService.translate(\"groupSim.breadCrumb.update\") }];\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n  }\r\n\r\n  checkExistGroupKey(){\r\n    this.isGroupKeyExists = false;\r\n    let me = this;\r\n    if(this.updateGroupForm.value[\"groupKey\"] == this.groupDetail.groupKey) return;\r\n    this.debounceService.set(\"groupKey\", this.groupSimService.groupkeyCheckExisted.bind(this.groupSimService,{},{query: this.updateGroupForm.value[\"groupKey\"]},(response)=>{\r\n      me.isGroupKeyExists = response == 1;\r\n    }));\r\n  }\r\n\r\n  getGroupDetail(){\r\n    let me = this;\r\n    this.groupId = parseInt(this.route.snapshot.paramMap.get(\"idgroup\"));\r\n    this.messageCommonService.onload();\r\n    this.groupSimService.getSimGroupById(this.groupId+\"\",{}, {}, (response)=>{\r\n        me.groupDetail = response;\r\n        me.groupScope = me.groupDetail.scope;\r\n        me.groupScopeObjects = CONSTANTS.GROUP_SCOPE;\r\n        me.contractCode = response.contractCode;\r\n        if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){\r\n          this.getCustomerDetail();\r\n        }\r\n        if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){\r\n          me.accountService.getListProvince((response)=>{\r\n            this.provinces = response.map(el => {\r\n              return {\r\n                ...el,\r\n                display: `${el.name} - ${el.code}`\r\n              }\r\n            })\r\n          })\r\n        }\r\n        me.updateGroupForm = new FormGroup({\r\n          groupKey: new FormControl(me.groupDetail.groupKey, [Validators.required,Validators.maxLength(16), this.customCodeCharacterValidator()]),\r\n          name: new FormControl(me.groupDetail.name, [Validators.required, Validators.maxLength(255), this.customCharacterValidator()]),\r\n          description: new FormControl(me.groupDetail.description, [Validators.maxLength(255)])\r\n        });\r\n    }, null, ()=>{\r\n      me.messageCommonService.offload();\r\n    })\r\n  }\r\n\r\n  getCustomerDetail(name:string = \"\"){\r\n    let me = this;\r\n    me.customerService.getByKey(\"customerCode\", this.groupDetail.customerCode ,(response)=>{\r\n      if(response){\r\n        me.customerDetail = response[0];\r\n      }\r\n    })\r\n  }\r\n\r\n  getDisplayProvince(){\r\n    if(this.groupDetail.provinceCode){\r\n        let provinces = (this.provinces || []).filter(el => el.code == this.groupDetail.provinceCode);\r\n        if(provinces.length > 0){\r\n          let province = provinces[0];\r\n          return province.display;\r\n        }\r\n    }\r\n    return \"\";\r\n  }\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"groupSim.breadCrumb.group\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n<!--    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">-->\r\n<!--        &lt;!&ndash; <a routerLink=\"/sims/createGroup\">-->\r\n<!--            <button pButton [label]=\"buttonAdd\" ></button>-->\r\n<!--        </a> &ndash;&gt;-->\r\n<!--    </div>-->\r\n</div>\r\n<div class=\"col-14 py-3\">\r\n    <div class=\"card responsive-form\" *ngIf=\"updateGroupForm\" >\r\n        <!-- <h5>Create Group</h5> -->\r\n        <form\r\n        action=\"\"\r\n        [formGroup]=\"updateGroupForm\"\r\n        (submit)=\"submitForm()\">\r\n            <div class=\"p-fluid p-formgrid grid grid-1\">\r\n                <!-- group scope -->\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupScope\" class=\"col-fixed pl-0\" style=\"min-width: 110px;\">{{tranService.translate(\"groupSim.label.groupScope\")}}</label>\r\n                    <div class=\"col-11 md:col-11 pb-0\">\r\n                        <span *ngIf=\"groupScope == groupScopeObjects.GROUP_ADMIN\">{{tranService.translate(\"groupSim.scope.admin\")}}</span>\r\n                        <span *ngIf=\"groupScope == groupScopeObjects.GROUP_PROVINCE\">{{tranService.translate(\"groupSim.scope.province\")}}</span>\r\n                        <span *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\">{{tranService.translate(\"groupSim.scope.customer\")}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- group key code -->\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupCode\" class=\"my-auto\" style=\"min-width: 110px;\">{{tranService.translate(\"groupSim.label.groupKey\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-11 md:col-11 pb-0\">\r\n                        <input pInputText id=\"groupKey\" formControlName=\"groupKey\" type=\"text\" [placeholder]=\"placeHolderGroupKey\"  (ngModelChange)=\"checkExistGroupKey()\"/>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <div class=\"my-auto\" style=\"min-width: 110px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"updateGroupForm.controls['groupKey']?.dirty && updateGroupForm.controls['groupKey'].hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"updateGroupForm.controls['groupKey'].hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.lengthError_16\")}}\r\n                        </div>\r\n                        <div *ngIf=\"updateGroupForm.controls['groupKey'].hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.characterError_code\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isGroupKeyExists\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.existedError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <!-- groupname -->\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"groupName\" class=\"my-auto\" style=\"min-width: 110px;\">{{tranService.translate(\"groupSim.label.groupName\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-11 md:col-11 pb-0\">\r\n                        <input pInputText id=\"name\" formControlName=\"name\" type=\"text\" [placeholder]=\"placeHolderGroupName\"/>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <div class=\"my-auto\" style=\"min-width: 110px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"updateGroupForm.controls['name']?.dirty && updateGroupForm.controls['name'].hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"updateGroupForm.controls['name'].hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.lengthError_255\")}}\r\n                        </div>\r\n                        <div *ngIf=\"updateGroupForm.controls['name'].hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.characterError_name\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <!-- customer -->\r\n                <div *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\" class=\"w-full\">\r\n                    <div class=\"flex justify-content-between col-12 md:col-12 py-0 flex-row\">\r\n                        <label htmlFor=\"customerCode\" class=\"my-auto\" style=\"min-width: 110px;height: fit-content;\">{{tranService.translate(\"groupSim.label.customer\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col-11 md:col-11 pb-0\">\r\n                            <span *ngIf=\"customerDetail\">{{customerDetail.customerName}} - {{customerDetail.customerCode}}</span>&nbsp;\r\n                        </div>\r\n                    </div>\r\n<!--                    <div class=\"flex justify-content-between col-12 md:col-12 py-0\">-->\r\n<!--                        <div class=\"my-auto\" style=\"min-width: 110px;\"></div>-->\r\n<!--                        <div class=\"col-11 md:col-11 py-0\">-->\r\n<!--                            <div *ngIf=\"updateGroupForm.controls['customerCode']?.dirty && updateGroupForm.controls['customerCode'].hasError('required')\" class=\"text-red-500\">-->\r\n<!--                                {{tranService.translate(\"groupSim.error.requiredError\")}}-->\r\n<!--                            </div>-->\r\n<!--                        </div>-->\r\n<!--                    </div>-->\r\n                </div>\r\n                <div *ngIf=\"groupScope == groupScopeObjects.GROUP_CUSTOMER\" class=\"w-full\">\r\n                    <div class=\"flex justify-content-between col-12 md:col-12 py-0 flex-row\">\r\n                        <label htmlFor=\"customerCode\" class=\"my-auto\" style=\"min-width: 110px;height: fit-content;\">{{tranService.translate(\"groupSim.label.contractCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col-11 md:col-11 pb-0\">\r\n                            <span *ngIf=\"contractCode\">{{contractCode}}</span>&nbsp;\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <!-- province -->\r\n                <div *ngIf=\"groupScope == groupScopeObjects.GROUP_PROVINCE\" class=\"w-full\">\r\n                    <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                        <label htmlFor=\"customer\" class=\"my-auto\" style=\"min-width: 110px;\">{{tranService.translate(\"account.label.province\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col-11 md:col-11 pb-0\">\r\n                            {{getDisplayProvince()}}\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                        <div class=\"my-auto\" style=\"min-width: 110px;\"></div>\r\n                        <div class=\"col-11 md:col-11 py-0\">\r\n                            <div *ngIf=\"updateGroupForm.controls['provinceCode']?.dirty && updateGroupForm.controls['provinceCode'].hasError('required')\" class=\"text-red-500\">\r\n                                {{tranService.translate(\"groupSim.error.requiredError\")}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <!-- description -->\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <label htmlFor=\"description\" class=\"py-3\" style=\"min-width: 110px;\">{{tranService.translate(\"groupSim.label.description\")}}</label>\r\n                    <div class=\"col-11 md:col-11 pb-0\">\r\n                        <textarea id=\"description\" rows=\"5\" cols=\"30\" formControlName=\"description\" [class.ng-dirty]=\"updateGroupForm.controls['description'].invalid \" [placeholder]=\"placeHolderDescription\" pInputText></textarea>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex justify-content-between col-12 md:col-12 py-0\">\r\n                    <div class=\"my-auto\" style=\"min-width: 110px;\"></div>\r\n                    <div class=\"col-11 md:col-11 pt-0\">\r\n                        <div *ngIf=\"updateGroupForm.controls['description'].hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"groupSim.error.lengthError_255\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex justify-content-center col-12 md:col-12 py-0 gap-3\">\r\n                <a routerLink=\"/sims/group\">\r\n                    <button pButton pRipple type=\"button\" [label]=\"labelBtnCancel\" class=\"p-button-outlined p-button-secondary\"></button>\r\n                </a>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"labelBtnSave\" type=\"submit\" [disabled]=\"updateGroupForm.invalid||isGroupKeyExists\"></p-button>\r\n            </div>\r\n        </form>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,MAAM;AAEjC,SAA4CC,WAAW,EAAEC,SAAS,EAAiCC,UAAU,QAAQ,gBAAgB;AAIrI,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,eAAe,QAAQ,2CAA2C;AAI3E,SAASC,SAAS,QAAQ,iCAAiC;AAG3D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;ICS9BC,EAAA,CAAAC,cAAA,WAA0D;IAAAD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxDH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAiD;;;;;IAC3GR,EAAA,CAAAC,cAAA,WAA6D;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA3DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACjHR,EAAA,CAAAC,cAAA,WAA6D;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA3DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAK,MAAA,CAAAH,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAajHR,EAAA,CAAAC,cAAA,cAA2I;IACvID,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAC,MAAA,CAAAL,WAAA,CAAAC,SAAA,sCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA6F;IACzFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAE,MAAA,CAAAN,WAAA,CAAAC,SAAA,uCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAqG;IACjGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAG,MAAA,CAAAP,WAAA,CAAAC,SAAA,4CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAmD;IAC/CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAI,MAAA,CAAAR,WAAA,CAAAC,SAAA,qCACJ;;;;;IAaAR,EAAA,CAAAC,cAAA,cAAmI;IAC/HD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAK,MAAA,CAAAT,WAAA,CAAAC,SAAA,sCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyF;IACrFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAM,MAAA,CAAAV,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAiG;IAC7FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAO,OAAA,CAAAX,WAAA,CAAAC,SAAA,4CACJ;;;;;IAQIR,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxEH,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAmB,kBAAA,KAAAC,OAAA,CAAAC,cAAA,CAAAC,YAAA,SAAAF,OAAA,CAAAC,cAAA,CAAAE,YAAA,KAAiE;;;;;IAJ1GvB,EAAA,CAAAC,cAAA,cAA2E;IAEyBD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnLH,EAAA,CAAAC,cAAA,cAAmC;IAC/BD,EAAA,CAAAwB,UAAA,IAAAC,oDAAA,mBAAqG;IAAAzB,EAAA,CAAAE,MAAA,cACzG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHsFH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAnB,WAAA,CAAAC,SAAA,4BAAoD;IAErIR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA2B,UAAA,SAAAD,OAAA,CAAAL,cAAA,CAAoB;;;;;IAgB3BrB,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAuB,OAAA,CAAAC,YAAA,CAAgB;;;;;IAJvD7B,EAAA,CAAAC,cAAA,cAA2E;IAEyBD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvLH,EAAA,CAAAC,cAAA,cAAmC;IAC/BD,EAAA,CAAAwB,UAAA,IAAAM,oDAAA,mBAAkD;IAAA9B,EAAA,CAAAE,MAAA,cACtD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHsFH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAA0B,OAAA,CAAAxB,WAAA,CAAAC,SAAA,gCAAwD;IAEzIR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA2B,UAAA,SAAAI,OAAA,CAAAF,YAAA,CAAkB;;;;;IAezB7B,EAAA,CAAAC,cAAA,cAAmJ;IAC/ID,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAqB,OAAA,CAAAzB,WAAA,CAAAC,SAAA,sCACJ;;;;;IAZZR,EAAA,CAAAC,cAAA,cAA2E;IAECD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1JH,EAAA,CAAAC,cAAA,cAAmC;IAC/BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,aAAgE;IAC5DD,EAAA,CAAAiC,SAAA,cAAqD;IACrDjC,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAwB,UAAA,KAAAU,oDAAA,kBAEM;IACVlC,EAAA,CAAAG,YAAA,EAAM;;;;IAX8DH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAA8B,OAAA,CAAA5B,WAAA,CAAAC,SAAA,2BAAmD;IAEnHR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAAwB,OAAA,CAAAC,kBAAA,QACJ;IAKUpC,EAAA,CAAAI,SAAA,GAAsH;IAAtHJ,EAAA,CAAA2B,UAAA,UAAAQ,OAAA,CAAAE,eAAA,CAAAC,QAAA,kCAAAH,OAAA,CAAAE,eAAA,CAAAC,QAAA,iBAAAC,KAAA,KAAAJ,OAAA,CAAAE,eAAA,CAAAC,QAAA,iBAAAE,QAAA,aAAsH;;;;;IAgBhIxC,EAAA,CAAAC,cAAA,cAAgG;IAC5FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAW,kBAAA,MAAA8B,OAAA,CAAAlC,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAnHpBR,EAAA,CAAAC,cAAA,aAA2D;IAKvDD,EAAA,CAAA0C,UAAA,oBAAAC,8DAAA;MAAA3C,EAAA,CAAA4C,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAA+C,aAAA;MAAA,OAAU/C,EAAA,CAAAgD,WAAA,CAAAF,OAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IACnBjD,EAAA,CAAAC,cAAA,aAA4C;IAGyCD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3IH,EAAA,CAAAC,cAAA,cAAmC;IAC/BD,EAAA,CAAAwB,UAAA,IAAA0B,6CAAA,mBAAkH;IAClHlD,EAAA,CAAAwB,UAAA,IAAA2B,6CAAA,mBAAwH;IACxHnD,EAAA,CAAAwB,UAAA,IAAA4B,6CAAA,mBAAwH;IAC5HpD,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,cAAgE;IACSD,EAAA,CAAAE,MAAA,IAAoD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5JH,EAAA,CAAAC,cAAA,eAAmC;IAC6ED,EAAA,CAAA0C,UAAA,2BAAAW,uEAAA;MAAArD,EAAA,CAAA4C,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAAtD,EAAA,CAAA+C,aAAA;MAAA,OAAiB/C,EAAA,CAAAgD,WAAA,CAAAM,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAAlJvD,EAAA,CAAAG,YAAA,EAAoJ;IAG5JH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAiC,SAAA,eAAqD;IACrDjC,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAwB,UAAA,KAAAgC,6CAAA,kBAEM;IACNxD,EAAA,CAAAwB,UAAA,KAAAiC,6CAAA,kBAEM;IACNzD,EAAA,CAAAwB,UAAA,KAAAkC,6CAAA,kBAEM;IACN1D,EAAA,CAAAwB,UAAA,KAAAmC,6CAAA,kBAEM;IACV3D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,cAAgE;IACSD,EAAA,CAAAE,MAAA,IAAqD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7JH,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAiC,SAAA,iBAAqG;IACzGjC,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAiC,SAAA,eAAqD;IACrDjC,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAwB,UAAA,KAAAoC,6CAAA,kBAEM;IACN5D,EAAA,CAAAwB,UAAA,KAAAqC,6CAAA,kBAEM;IACN7D,EAAA,CAAAwB,UAAA,KAAAsC,6CAAA,kBAEM;IACV9D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAwB,UAAA,KAAAuC,6CAAA,kBAeM;IACN/D,EAAA,CAAAwB,UAAA,KAAAwC,6CAAA,kBAOM;IAENhE,EAAA,CAAAwB,UAAA,KAAAyC,6CAAA,mBAeM;IAENjE,EAAA,CAAAC,cAAA,cAAgE;IACQD,EAAA,CAAAE,MAAA,IAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnIH,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAiC,SAAA,oBAA6M;IACjNjC,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAiC,SAAA,eAAqD;IACrDjC,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAwB,UAAA,KAAA0C,6CAAA,kBAEM;IACVlE,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAqE;IAE7DD,EAAA,CAAAiC,SAAA,kBAAqH;IACzHjC,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAiC,SAAA,oBAA4I;IAChJjC,EAAA,CAAAG,YAAA,EAAM;;;;IAxHVH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAA2B,UAAA,cAAAwC,MAAA,CAAA9B,eAAA,CAA6B;IAK4DrC,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAA8D,MAAA,CAAA5D,WAAA,CAAAC,SAAA,8BAAsD;IAExHR,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAC,WAAA,CAAiD;IACjDtE,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAE,cAAA,CAAoD;IACpDvE,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAG,cAAA,CAAoD;IAKMxE,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAA8D,MAAA,CAAA5D,WAAA,CAAAC,SAAA,4BAAoD;IAE9CR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA2B,UAAA,gBAAAwC,MAAA,CAAAM,mBAAA,CAAmC;IAMpGzE,EAAA,CAAAI,SAAA,GAA8G;IAA9GJ,EAAA,CAAA2B,UAAA,UAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,8BAAA6B,MAAA,CAAA9B,eAAA,CAAAC,QAAA,aAAAC,KAAA,KAAA4B,MAAA,CAAA9B,eAAA,CAAAC,QAAA,aAAAE,QAAA,aAA8G;IAG9GxC,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,aAAAE,QAAA,cAAgE;IAGhExC,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,aAAAE,QAAA,sBAAwE;IAGxExC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAO,gBAAA,CAAsB;IAOqC1E,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAA8D,MAAA,CAAA5D,WAAA,CAAAC,SAAA,6BAAqD;IAEvDR,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAA2B,UAAA,gBAAAwC,MAAA,CAAAQ,oBAAA,CAAoC;IAM7F3E,EAAA,CAAAI,SAAA,GAAsG;IAAtGJ,EAAA,CAAA2B,UAAA,UAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,0BAAA6B,MAAA,CAAA9B,eAAA,CAAAC,QAAA,SAAAC,KAAA,KAAA4B,MAAA,CAAA9B,eAAA,CAAAC,QAAA,SAAAE,QAAA,aAAsG;IAGtGxC,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,SAAAE,QAAA,cAA4D;IAG5DxC,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,SAAAE,QAAA,sBAAoE;IAM5ExC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAG,cAAA,CAAoD;IAgBpDxE,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAG,cAAA,CAAoD;IASpDxE,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAAC,UAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAAE,cAAA,CAAoD;IAkBcvE,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAK,iBAAA,CAAA8D,MAAA,CAAA5D,WAAA,CAAAC,SAAA,+BAAuD;IAE3CR,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAA4E,WAAA,aAAAT,MAAA,CAAA9B,eAAA,CAAAC,QAAA,gBAAAuC,OAAA,CAAmE;IAAC7E,EAAA,CAAA2B,UAAA,gBAAAwC,MAAA,CAAAW,sBAAA,CAAsC;IAMhL9E,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAA2B,UAAA,SAAAwC,MAAA,CAAA9B,eAAA,CAAAC,QAAA,gBAAAE,QAAA,cAAmE;IAQvCxC,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA2B,UAAA,UAAAwC,MAAA,CAAAY,cAAA,CAAwB;IAE7B/E,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA2B,UAAA,UAAAwC,MAAA,CAAAa,YAAA,CAAsB,aAAAb,MAAA,CAAA9B,eAAA,CAAAwC,OAAA,IAAAV,MAAA,CAAAO,gBAAA;;;ADlH3E,OAAM,MAAOO,uBAAwB,SAAQlF,aAAa;EAoBxDmF,YAA6CC,eAAgC,EAChCC,eAAgC,EACzDC,cAA8B,EAAEC,QAAkB;IAAE,KAAK,CAACA,QAAQ,CAAC;IAF1C,KAAAH,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACxC,KAAAC,cAAc,GAAdA,cAAc;IAlBlC,KAAAE,aAAa,GAAG,KAAK;IAErB,KAAAP,YAAY,GAAW,IAAI,CAACzE,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;IAC9E,KAAAuE,cAAc,GAAW,IAAI,CAACxE,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;IAClF,KAAAgF,wBAAwB,GAAW,IAAI,CAACjF,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;IAC9F,KAAAiE,mBAAmB,GAAU,IAAI,CAAClE,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;IACxF,KAAAmE,oBAAoB,GAAU,IAAI,CAACpE,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;IAC1F,KAAAsE,sBAAsB,GAAW,IAAI,CAACvE,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;IAI/F,KAAA6D,iBAAiB,GAAQvE,SAAS,CAAC2F,WAAW;IAC9C,KAAAf,gBAAgB,GAAY,KAAK;EAMuD;EAExFgB,wBAAwBA,CAAA;IACtB,OAAQC,OAAwB,IAA6B;MAC3D,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;MAC3B,MAAMC,OAAO,GAAG,oCAAoC,CAACC,IAAI,CAACF,KAAK,CAAC;MAChE,OAAOC,OAAO,GAAG,IAAI,GAAG;QAAE,mBAAmB,EAAE;UAAED;QAAK;MAAE,CAAE;IAC5D,CAAC;EACH;EAEAG,4BAA4BA,CAAA;IAC1B,OAAQJ,OAAwB,IAA6B;MAC3D,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;MAC3B,MAAMC,OAAO,GAAG,mBAAmB,CAACC,IAAI,CAACF,KAAK,CAAC;MAC/C,OAAOC,OAAO,GAAG,IAAI,GAAG;QAAE,mBAAmB,EAAE;UAAED;QAAK;MAAE,CAAE;IAC5D,CAAC;EACH;EAEAI,YAAYA,CAACC,KAAS;IACpB,OAAO,IAAIzG,UAAU,CAAC0G,QAAQ,IAAG;MAC/B,IAAI,CAACf,eAAe,CAACgB,oBAAoB,CAAC,EAAE,EAACF,KAAK,EAAGG,QAAQ,IAAI;QAC/DF,QAAQ,CAACG,IAAI,CAACD,QAAQ,CAAC;QACvBF,QAAQ,CAACI,QAAQ,EAAE;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAaArD,UAAUA,CAAA;IACR,IAAG,IAAI,CAACyB,gBAAgB,EAAC;MACvB,OAAO,IAAI,CAAC6B,oBAAoB,CAACC,KAAK,CAAC,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC,CAAC;;IAEnG,IAAIiG,UAAU,GAAG;MAAC,GAAG,IAAI,CAACC,WAAW;MAAE,GAAG,IAAI,CAACrE,eAAe,CAACuD;IAAK,CAAC;IACrE,IAAIe,EAAE,GAAG,IAAI;IACb,IAAI,CAACxB,eAAe,CAACyB,cAAc,CAAC,IAAI,CAACC,OAAO,GAAC,EAAE,EAAC,EAAE,EAAEJ,UAAU,EAAC,EAAE,EAAEL,QAAQ,IAAG;MAChFO,EAAE,CAACJ,oBAAoB,CAACO,OAAO,CAACH,EAAE,CAACpG,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;MACvF,IAAI,CAACuG,MAAM,CAACC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAAC7G,WAAW,CAACC,SAAS,CAAC,qBAAqB;IAAC,CAAE,EAAE;MAAE4G,KAAK,EAAE,IAAI,CAAC7G,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAE6G,UAAU,EAAE;IAAa,CAAE,EAAE;MAAED,KAAK,EAAE,IAAI,CAAC7G,WAAW,CAACC,SAAS,CAAC,4BAA4B;IAAC,CAAE,CAAC;IAC/O,IAAI,CAAC8G,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE;IAAG,CAAE;EACrD;EAEA9D,kBAAkBA,CAAA;IAChB,IAAI,CAACmB,gBAAgB,GAAG,KAAK;IAC7B,IAAIiC,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACtE,eAAe,CAACuD,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,CAACc,WAAW,CAACc,QAAQ,EAAE;IACxE,IAAI,CAACC,eAAe,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACvC,eAAe,CAACgB,oBAAoB,CAACwB,IAAI,CAAC,IAAI,CAACxC,eAAe,EAAC,EAAE,EAAC;MAACc,KAAK,EAAE,IAAI,CAAC5D,eAAe,CAACuD,KAAK,CAAC,UAAU;IAAC,CAAC,EAAEQ,QAAQ,IAAG;MACtKO,EAAE,CAACjC,gBAAgB,GAAG0B,QAAQ,IAAI,CAAC;IACrC,CAAC,CAAC,CAAC;EACL;EAEAc,cAAcA,CAAA;IACZ,IAAIP,EAAE,GAAG,IAAI;IACb,IAAI,CAACE,OAAO,GAAGe,QAAQ,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,CAAC;IACpE,IAAI,CAACzB,oBAAoB,CAAC0B,MAAM,EAAE;IAClC,IAAI,CAAC9C,eAAe,CAAC+C,eAAe,CAAC,IAAI,CAACrB,OAAO,GAAC,EAAE,EAAC,EAAE,EAAE,EAAE,EAAGT,QAAQ,IAAG;MACrEO,EAAE,CAACD,WAAW,GAAGN,QAAQ;MACzBO,EAAE,CAACvC,UAAU,GAAGuC,EAAE,CAACD,WAAW,CAACyB,KAAK;MACpCxB,EAAE,CAACtC,iBAAiB,GAAGvE,SAAS,CAAC2F,WAAW;MAC5CkB,EAAE,CAAC9E,YAAY,GAAGuE,QAAQ,CAACvE,YAAY;MACvC,IAAG8E,EAAE,CAACvC,UAAU,IAAItE,SAAS,CAAC2F,WAAW,CAACjB,cAAc,EAAC;QACvD,IAAI,CAAC4D,iBAAiB,EAAE;;MAE1B,IAAGzB,EAAE,CAACvC,UAAU,IAAItE,SAAS,CAAC2F,WAAW,CAAClB,cAAc,EAAC;QACvDoC,EAAE,CAACtB,cAAc,CAACgD,eAAe,CAAEjC,QAAQ,IAAG;UAC5C,IAAI,CAACkC,SAAS,GAAGlC,QAAQ,CAACmC,GAAG,CAACC,EAAE,IAAG;YACjC,OAAO;cACL,GAAGA,EAAE;cACLC,OAAO,EAAE,GAAGD,EAAE,CAACE,IAAI,MAAMF,EAAE,CAACG,IAAI;aACjC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC;;MAEJhC,EAAE,CAACtE,eAAe,GAAG,IAAI3C,SAAS,CAAC;QACjC8H,QAAQ,EAAE,IAAI/H,WAAW,CAACkH,EAAE,CAACD,WAAW,CAACc,QAAQ,EAAE,CAAC7H,UAAU,CAACiJ,QAAQ,EAACjJ,UAAU,CAACkJ,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC9C,4BAA4B,EAAE,CAAC,CAAC;QACvI2C,IAAI,EAAE,IAAIjJ,WAAW,CAACkH,EAAE,CAACD,WAAW,CAACgC,IAAI,EAAE,CAAC/I,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACkJ,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAACnD,wBAAwB,EAAE,CAAC,CAAC;QAC7HoD,WAAW,EAAE,IAAIrJ,WAAW,CAACkH,EAAE,CAACD,WAAW,CAACoC,WAAW,EAAE,CAACnJ,UAAU,CAACkJ,SAAS,CAAC,GAAG,CAAC,CAAC;OACrF,CAAC;IACN,CAAC,EAAE,IAAI,EAAE,MAAI;MACXlC,EAAE,CAACJ,oBAAoB,CAACwC,OAAO,EAAE;IACnC,CAAC,CAAC;EACJ;EAEAX,iBAAiBA,CAACM,IAAA,GAAc,EAAE;IAChC,IAAI/B,EAAE,GAAG,IAAI;IACbA,EAAE,CAACvB,eAAe,CAAC4D,QAAQ,CAAC,cAAc,EAAE,IAAI,CAACtC,WAAW,CAACnF,YAAY,EAAG6E,QAAQ,IAAG;MACrF,IAAGA,QAAQ,EAAC;QACVO,EAAE,CAACtF,cAAc,GAAG+E,QAAQ,CAAC,CAAC,CAAC;;IAEnC,CAAC,CAAC;EACJ;EAEAhE,kBAAkBA,CAAA;IAChB,IAAG,IAAI,CAACsE,WAAW,CAACuC,YAAY,EAAC;MAC7B,IAAIX,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS,IAAI,EAAE,EAAEY,MAAM,CAACV,EAAE,IAAIA,EAAE,CAACG,IAAI,IAAI,IAAI,CAACjC,WAAW,CAACuC,YAAY,CAAC;MAC7F,IAAGX,SAAS,CAACa,MAAM,GAAG,CAAC,EAAC;QACtB,IAAIC,QAAQ,GAAGd,SAAS,CAAC,CAAC,CAAC;QAC3B,OAAOc,QAAQ,CAACX,OAAO;;;IAG7B,OAAO,EAAE;EACX;;;uBA1IWxD,uBAAuB,EAAAjF,EAAA,CAAAqJ,iBAAA,CAoBdxJ,eAAe,GAAAG,EAAA,CAAAqJ,iBAAA,CACfzJ,eAAe,GAAAI,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAArJ,EAAA,CAAAwJ,QAAA;IAAA;EAAA;;;YArBxBvE,uBAAuB;MAAAwE,SAAA;MAAAC,QAAA,GAAA1J,EAAA,CAAA2J,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBpCjK,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChGH,EAAA,CAAAiC,SAAA,sBAAoF;UACxFjC,EAAA,CAAAG,YAAA,EAAM;UAOVH,EAAA,CAAAC,cAAA,aAAyB;UACrBD,EAAA,CAAAwB,UAAA,IAAA2I,sCAAA,mBA8HM;UACVnK,EAAA,CAAAG,YAAA,EAAM;;;UAzIsCH,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAK,iBAAA,CAAA6J,GAAA,CAAA3J,WAAA,CAAAC,SAAA,8BAAsD;UACnDR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA2B,UAAA,UAAAuI,GAAA,CAAA/C,KAAA,CAAe,SAAA+C,GAAA,CAAA5C,IAAA;UASvBtH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAA2B,UAAA,SAAAuI,GAAA,CAAA7H,eAAA,CAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}