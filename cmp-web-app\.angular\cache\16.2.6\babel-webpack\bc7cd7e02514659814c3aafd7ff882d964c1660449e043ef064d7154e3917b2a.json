{"ast": null, "code": "import { Observable, of } from 'rxjs';\nimport { map, catchError, debounceTime, switchMap, take } from 'rxjs/operators';\nexport function digitValidator(digitCount) {\n  return control => {\n    const value = control.value;\n    const regex = new RegExp(`^\\\\d{${digitCount}}$`);\n    const isValid = regex.test(value);\n    return isValid ? null : {\n      digitCount: true\n    };\n  };\n}\nexport function numericLengthValidator(minLength, maxLength) {\n  return control => {\n    const value = control.value;\n    // Kiểm tra nếu giá trị không tồn tại hoặc không phải là chuỗi\n    if (!value || typeof value !== 'string') {\n      return {\n        numericLength: {\n          requiredLength: `${minLength}-${maxLength}`,\n          actualLength: 0\n        }\n      };\n    }\n    const regex = new RegExp(`^\\\\d{${minLength},${maxLength}}$`);\n    const isValid = regex.test(value);\n    return isValid ? null : {\n      numericLength: {\n        requiredLength: `${minLength}-${maxLength}`,\n        actualLength: value.length\n      }\n    };\n  };\n}\nexport function numericMinLengthValidator(minLength) {\n  return control => {\n    const value = control.value;\n    if (!value || typeof value !== 'string') {\n      return {\n        numericLength: {\n          requiredLength: minLength,\n          actualLength: 0\n        }\n      };\n    }\n    const isValid = value.length >= minLength && /^\\d+$/.test(value);\n    return isValid ? null : {\n      numericMinLength: {\n        requiredLength: minLength,\n        actualLength: value.length\n      }\n    };\n  };\n}\nexport function numericMaxLengthValidator(maxLength) {\n  return control => {\n    const value = control.value;\n    // Allow empty values or values that are not strings\n    if (!value || typeof value !== 'string') {\n      return null;\n    }\n    // Check if the value is numeric\n    if (/^\\d+$/.test(value)) {\n      const isValid = value.length <= maxLength;\n      return isValid ? null : {\n        numericMaxLength: {\n          requiredLength: maxLength,\n          actualLength: value.length\n        }\n      };\n    }\n    // If the value is not numeric, do not apply any validation errors\n    return null;\n  };\n}\nexport function checkExistedStaticList(list) {\n  return control => {\n    const value = control.value;\n    if (!value) {\n      return null; // Không kiểm tra nếu giá trị là null hoặc undefined\n    }\n\n    const duplicateItem = list.find(item => item === value);\n    return duplicateItem ? {\n      'duplicateItem': {\n        value: value\n      }\n    } : null;\n  };\n}\nfunction validateData(data, service) {\n  console.log(\"Trigger API call\");\n  let isValid = true;\n  return new Observable(observer => {\n    service.checkExisted([data], response => {\n      console.log(response);\n      if (response === 0) {\n        observer.next(true);\n      } else if (response > 0) {\n        observer.next(false);\n      }\n      observer.complete();\n    });\n  });\n}\nexport function checkExistedDynamicListNumber(service, debounceTimeValue = 300) {\n  return control => {\n    const value = control.value;\n    if (!value) {\n      return of(null); // Không kiểm tra nếu giá trị là null hoặc undefined\n    }\n\n    return validateData(control.value, service).pipe(map(isValid => {\n      return isValid ? null : {\n        'existed': true\n      };\n    }));\n  };\n}\nexport function checkExistedDynamicListArray(service, debounceTimeValue = 300) {\n  return control => {\n    const value = control.value;\n    if (!value) {\n      return of(null); // Không kiểm tra nếu giá trị là null hoặc undefined\n    }\n\n    return control.valueChanges.pipe(debounceTime(debounceTimeValue),\n    // Chờ 300ms sau khi người dùng nhập xong trước khi gọi service\n    switchMap(val => {\n      let obs = new Observable(observer => {\n        service.checkExisted([val], response => {\n          observer.next(response);\n          observer.complete();\n        });\n      });\n      return obs;\n    }), take(1), map(res => {\n      if (Array.isArray(res) && res.length === 0) {\n        return null;\n      } else {\n        return {\n          'existed': true\n        };\n      }\n    }), catchError(error => {\n      console.log(error);\n      return error;\n    }));\n  };\n}\nexport function noneWhitespaceValidator() {\n  return control => {\n    const isWhitespace = (control.value || '').trim().length === 0;\n    const isValid = !isWhitespace;\n    return isValid ? null : {\n      whitespace: true\n    };\n  };\n}", "map": {"version": 3, "names": ["Observable", "of", "map", "catchError", "debounceTime", "switchMap", "take", "digitValidator", "digitCount", "control", "value", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "test", "numericLengthValidator", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "numericLength", "<PERSON><PERSON><PERSON><PERSON>", "actualLength", "length", "numericMinLengthValidator", "numericMinLength", "numericMaxLengthValidator", "numericMaxLength", "checkExistedStaticList", "list", "duplicateItem", "find", "item", "validateData", "data", "service", "console", "log", "observer", "checkExisted", "response", "next", "complete", "checkExistedDynamicListNumber", "debounceTimeValue", "pipe", "checkExistedDynamicListArray", "valueChanges", "val", "obs", "res", "Array", "isArray", "error", "noneWhitespaceValidator", "isWhitespace", "trim", "whitespace"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\validatorCustoms.ts"], "sourcesContent": ["import { AbstractControl, AsyncValidatorFn, ValidationErrors, ValidatorFn } from '@angular/forms';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map, catchError, debounceTime, switchMap, take } from 'rxjs/operators';\r\n\r\nexport function digitValidator(digitCount: number): ValidatorFn {\r\n  return (control: AbstractControl): ValidationErrors | null => {\r\n    const value = control.value;\r\n    const regex = new RegExp(`^\\\\d{${digitCount}}$`);\r\n    const isValid = regex.test(value);\r\n    return isValid ? null : { digitCount: true };\r\n  };\r\n}\r\n\r\nexport function numericLengthValidator(minLength: number, maxLength: number): ValidatorFn {\r\n  return (control: AbstractControl): ValidationErrors | null => {\r\n    const value = control.value;\r\n\r\n    // Kiểm tra nếu giá trị không tồn tại hoặc không phải là chuỗi\r\n    if (!value || typeof value !== 'string') {\r\n      return { numericLength: { requiredLength: `${minLength}-${maxLength}`, actualLength: 0 } };\r\n    }\r\n\r\n    const regex = new RegExp(`^\\\\d{${minLength},${maxLength}}$`);\r\n    const isValid = regex.test(value);\r\n    return isValid ? null : { numericLength: { requiredLength: `${minLength}-${maxLength}`, actualLength: value.length } };\r\n  };\r\n}\r\n\r\nexport function numericMinLengthValidator(minLength: number): ValidatorFn {\r\n  return (control: AbstractControl): ValidationErrors | null => {\r\n    const value = control.value;\r\n    if (!value || typeof value !== 'string') {\r\n      return { numericLength: { requiredLength: minLength, actualLength: 0 } };\r\n    }\r\n    const isValid = value.length >= minLength && /^\\d+$/.test(value);\r\n    return isValid ? null : { numericMinLength: { requiredLength: minLength, actualLength: value.length } };\r\n  };\r\n}\r\n\r\nexport function numericMaxLengthValidator(maxLength: number): ValidatorFn {\r\n  return (control: AbstractControl): ValidationErrors | null => {\r\n    const value = control.value;\r\n\r\n    // Allow empty values or values that are not strings\r\n    if (!value || typeof value !== 'string') {\r\n      return null;\r\n    }\r\n\r\n    // Check if the value is numeric\r\n    if (/^\\d+$/.test(value)) {\r\n      const isValid = value.length <= maxLength;\r\n      return isValid ? null : { numericMaxLength: { requiredLength: maxLength, actualLength: value.length } };\r\n    }\r\n\r\n    // If the value is not numeric, do not apply any validation errors\r\n    return null;\r\n  };\r\n}\r\n\r\nexport function checkExistedStaticList(list: string[]): ValidatorFn {\r\n    return (control: AbstractControl): { [key: string]: any } | null => {\r\n      const value = control.value;\r\n      if (!value) {\r\n        return null; // Không kiểm tra nếu giá trị là null hoặc undefined\r\n      }\r\n      const duplicateItem = list.find(item => item === value);\r\n      return duplicateItem ? { 'duplicateItem': { value: value } } : null;\r\n    };\r\n}\r\n\r\nfunction validateData(data: any, service: any): Observable<boolean> {\r\n  console.log(\"Trigger API call\");\r\n  let isValid = true;\r\n  return new Observable<boolean>((observer) => {\r\n    service.checkExisted([data], (response) => {\r\n      console.log(response);\r\n      if (response === 0) {\r\n        observer.next(true);\r\n      } else if(response > 0) {\r\n        observer.next(false);\r\n      }\r\n      observer.complete();\r\n    });\r\n  });\r\n}\r\n\r\nexport function checkExistedDynamicListNumber(service: any, debounceTimeValue: number = 300): AsyncValidatorFn {\r\n  return (control: AbstractControl): Observable<ValidationErrors | null> => {\r\n    const value = control.value;\r\n    if (!value) {\r\n      return of(null); // Không kiểm tra nếu giá trị là null hoặc undefined\r\n    }\r\n    return validateData(control.value, service).pipe(\r\n      map((isValid: boolean) => {\r\n        return isValid ? null : { 'existed': true };\r\n      })\r\n    )\r\n  };\r\n}\r\n\r\nexport function checkExistedDynamicListArray(service: any, debounceTimeValue: number = 300): AsyncValidatorFn {\r\n  return (control: AbstractControl): Observable<ValidationErrors | null> => {\r\n    const value = control.value;\r\n    if (!value) {\r\n      return of(null); // Không kiểm tra nếu giá trị là null hoặc undefined\r\n    }\r\n    return control.valueChanges.pipe(\r\n      debounceTime(debounceTimeValue), // Chờ 300ms sau khi người dùng nhập xong trước khi gọi service\r\n      switchMap(val => {\r\n        let obs = new Observable(observer => {\r\n          service.checkExisted([val], (response) => {\r\n            observer.next(response);\r\n            observer.complete();\r\n          });\r\n        });\r\n        return obs\r\n      }),\r\n      take(1),\r\n      map((res: any) => {\r\n        if (Array.isArray(res) && res.length === 0) {\r\n          return null;\r\n        }else{\r\n          return { 'existed': true };\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        console.log(error)\r\n        return error\r\n      })\r\n    );\r\n  };\r\n}\r\n\r\nexport function noneWhitespaceValidator(): ValidatorFn {\r\n    return (control: AbstractControl): ValidationErrors | null => {\r\n        const isWhitespace = (control.value || '').trim().length === 0;\r\n        const isValid = !isWhitespace;\r\n        return isValid ? null : { whitespace: true };\r\n    };\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,EAAEC,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAE/E,OAAM,SAAUC,cAAcA,CAACC,UAAkB;EAC/C,OAAQC,OAAwB,IAA6B;IAC3D,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,QAAQJ,UAAU,IAAI,CAAC;IAChD,MAAMK,OAAO,GAAGF,KAAK,CAACG,IAAI,CAACJ,KAAK,CAAC;IACjC,OAAOG,OAAO,GAAG,IAAI,GAAG;MAAEL,UAAU,EAAE;IAAI,CAAE;EAC9C,CAAC;AACH;AAEA,OAAM,SAAUO,sBAAsBA,CAACC,SAAiB,EAAEC,SAAiB;EACzE,OAAQR,OAAwB,IAA6B;IAC3D,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAE3B;IACA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvC,OAAO;QAAEQ,aAAa,EAAE;UAAEC,cAAc,EAAE,GAAGH,SAAS,IAAIC,SAAS,EAAE;UAAEG,YAAY,EAAE;QAAC;MAAE,CAAE;;IAG5F,MAAMT,KAAK,GAAG,IAAIC,MAAM,CAAC,QAAQI,SAAS,IAAIC,SAAS,IAAI,CAAC;IAC5D,MAAMJ,OAAO,GAAGF,KAAK,CAACG,IAAI,CAACJ,KAAK,CAAC;IACjC,OAAOG,OAAO,GAAG,IAAI,GAAG;MAAEK,aAAa,EAAE;QAAEC,cAAc,EAAE,GAAGH,SAAS,IAAIC,SAAS,EAAE;QAAEG,YAAY,EAAEV,KAAK,CAACW;MAAM;IAAE,CAAE;EACxH,CAAC;AACH;AAEA,OAAM,SAAUC,yBAAyBA,CAACN,SAAiB;EACzD,OAAQP,OAAwB,IAA6B;IAC3D,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvC,OAAO;QAAEQ,aAAa,EAAE;UAAEC,cAAc,EAAEH,SAAS;UAAEI,YAAY,EAAE;QAAC;MAAE,CAAE;;IAE1E,MAAMP,OAAO,GAAGH,KAAK,CAACW,MAAM,IAAIL,SAAS,IAAI,OAAO,CAACF,IAAI,CAACJ,KAAK,CAAC;IAChE,OAAOG,OAAO,GAAG,IAAI,GAAG;MAAEU,gBAAgB,EAAE;QAAEJ,cAAc,EAAEH,SAAS;QAAEI,YAAY,EAAEV,KAAK,CAACW;MAAM;IAAE,CAAE;EACzG,CAAC;AACH;AAEA,OAAM,SAAUG,yBAAyBA,CAACP,SAAiB;EACzD,OAAQR,OAAwB,IAA6B;IAC3D,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAE3B;IACA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvC,OAAO,IAAI;;IAGb;IACA,IAAI,OAAO,CAACI,IAAI,CAACJ,KAAK,CAAC,EAAE;MACvB,MAAMG,OAAO,GAAGH,KAAK,CAACW,MAAM,IAAIJ,SAAS;MACzC,OAAOJ,OAAO,GAAG,IAAI,GAAG;QAAEY,gBAAgB,EAAE;UAAEN,cAAc,EAAEF,SAAS;UAAEG,YAAY,EAAEV,KAAK,CAACW;QAAM;MAAE,CAAE;;IAGzG;IACA,OAAO,IAAI;EACb,CAAC;AACH;AAEA,OAAM,SAAUK,sBAAsBA,CAACC,IAAc;EACjD,OAAQlB,OAAwB,IAAmC;IACjE,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI,CAAC,CAAC;;;IAEf,MAAMkB,aAAa,GAAGD,IAAI,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAKpB,KAAK,CAAC;IACvD,OAAOkB,aAAa,GAAG;MAAE,eAAe,EAAE;QAAElB,KAAK,EAAEA;MAAK;IAAE,CAAE,GAAG,IAAI;EACrE,CAAC;AACL;AAEA,SAASqB,YAAYA,CAACC,IAAS,EAAEC,OAAY;EAC3CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EAC/B,IAAItB,OAAO,GAAG,IAAI;EAClB,OAAO,IAAIb,UAAU,CAAWoC,QAAQ,IAAI;IAC1CH,OAAO,CAACI,YAAY,CAAC,CAACL,IAAI,CAAC,EAAGM,QAAQ,IAAI;MACxCJ,OAAO,CAACC,GAAG,CAACG,QAAQ,CAAC;MACrB,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBF,QAAQ,CAACG,IAAI,CAAC,IAAI,CAAC;OACpB,MAAM,IAAGD,QAAQ,GAAG,CAAC,EAAE;QACtBF,QAAQ,CAACG,IAAI,CAAC,KAAK,CAAC;;MAEtBH,QAAQ,CAACI,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,6BAA6BA,CAACR,OAAY,EAAES,iBAAA,GAA4B,GAAG;EACzF,OAAQjC,OAAwB,IAAyC;IACvE,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAI,CAACA,KAAK,EAAE;MACV,OAAOT,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;;;IAEnB,OAAO8B,YAAY,CAACtB,OAAO,CAACC,KAAK,EAAEuB,OAAO,CAAC,CAACU,IAAI,CAC9CzC,GAAG,CAAEW,OAAgB,IAAI;MACvB,OAAOA,OAAO,GAAG,IAAI,GAAG;QAAE,SAAS,EAAE;MAAI,CAAE;IAC7C,CAAC,CAAC,CACH;EACH,CAAC;AACH;AAEA,OAAM,SAAU+B,4BAA4BA,CAACX,OAAY,EAAES,iBAAA,GAA4B,GAAG;EACxF,OAAQjC,OAAwB,IAAyC;IACvE,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAI,CAACA,KAAK,EAAE;MACV,OAAOT,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;;;IAEnB,OAAOQ,OAAO,CAACoC,YAAY,CAACF,IAAI,CAC9BvC,YAAY,CAACsC,iBAAiB,CAAC;IAAE;IACjCrC,SAAS,CAACyC,GAAG,IAAG;MACd,IAAIC,GAAG,GAAG,IAAI/C,UAAU,CAACoC,QAAQ,IAAG;QAClCH,OAAO,CAACI,YAAY,CAAC,CAACS,GAAG,CAAC,EAAGR,QAAQ,IAAI;UACvCF,QAAQ,CAACG,IAAI,CAACD,QAAQ,CAAC;UACvBF,QAAQ,CAACI,QAAQ,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOO,GAAG;IACZ,CAAC,CAAC,EACFzC,IAAI,CAAC,CAAC,CAAC,EACPJ,GAAG,CAAE8C,GAAQ,IAAI;MACf,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAAIA,GAAG,CAAC3B,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAO,IAAI;OACZ,MAAI;QACH,OAAO;UAAE,SAAS,EAAE;QAAI,CAAE;;IAE9B,CAAC,CAAC,EACFlB,UAAU,CAAEgD,KAAK,IAAI;MACnBjB,OAAO,CAACC,GAAG,CAACgB,KAAK,CAAC;MAClB,OAAOA,KAAK;IACd,CAAC,CAAC,CACH;EACH,CAAC;AACH;AAEA,OAAM,SAAUC,uBAAuBA,CAAA;EACnC,OAAQ3C,OAAwB,IAA6B;IACzD,MAAM4C,YAAY,GAAG,CAAC5C,OAAO,CAACC,KAAK,IAAI,EAAE,EAAE4C,IAAI,EAAE,CAACjC,MAAM,KAAK,CAAC;IAC9D,MAAMR,OAAO,GAAG,CAACwC,YAAY;IAC7B,OAAOxC,OAAO,GAAG,IAAI,GAAG;MAAE0C,UAAU,EAAE;IAAI,CAAE;EAChD,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}