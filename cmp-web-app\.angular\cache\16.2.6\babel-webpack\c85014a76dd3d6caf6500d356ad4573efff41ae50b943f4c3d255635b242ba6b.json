{"ast": null, "code": "import { AccountService } from \"src/app/service/account/AccountService\";\nimport { AppProfileRoutingModule } from \"./app.profile-routing\";\nimport { CommonModule } from \"@angular/common\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { FieldsetModule } from \"primeng/fieldset\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { InputTextModule } from \"primeng/inputtext\";\nimport { ButtonModule } from \"primeng/button\";\nimport { CommonVnptModule } from \"../common-module/common.module\";\nimport { SplitButtonModule } from \"primeng/splitbutton\";\nimport { AutoCompleteModule } from \"primeng/autocomplete\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { CardModule } from \"primeng/card\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { AppProfileDetailComponent } from \"./detail/app.profile.detail.component\";\nimport { AppProfileEditComponent } from \"./edit/app.profile.edit.component\";\nimport { AppProfileChangePasswordComponent } from \"./change-password/app.profile.change-password.component\";\nimport { TabViewModule } from \"primeng/tabview\";\nimport { CustomerService } from \"../../service/customer/CustomerService\";\nimport { ContractService } from \"../../service/contract/ContractService\";\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport * as i0 from \"@angular/core\";\nexport class AppProfileModule {\n  static {\n    this.ɵfac = function AppProfileModule_Factory(t) {\n      return new (t || AppProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AccountService, CustomerService, ContractService],\n      imports: [AppProfileRoutingModule, CommonModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, AutoCompleteModule, CalendarModule, DropdownModule, CardModule, DialogModule, InputTextareaModule, MultiSelectModule, TabViewModule, RadioButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppProfileModule, {\n    declarations: [AppProfileDetailComponent, AppProfileEditComponent, AppProfileChangePasswordComponent],\n    imports: [AppProfileRoutingModule, CommonModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, AutoCompleteModule, CalendarModule, DropdownModule, CardModule, DialogModule, InputTextareaModule, MultiSelectModule, TabViewModule, RadioButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["AccountService", "AppProfileRoutingModule", "CommonModule", "BreadcrumbModule", "FieldsetModule", "FormsModule", "ReactiveFormsModule", "InputTextModule", "ButtonModule", "CommonVnptModule", "SplitButtonModule", "AutoCompleteModule", "CalendarModule", "DropdownModule", "CardModule", "DialogModule", "InputTextareaModule", "MultiSelectModule", "AppProfileDetailComponent", "AppProfileEditComponent", "AppProfileChangePasswordComponent", "TabViewModule", "CustomerService", "ContractService", "RadioButtonModule", "AppProfileModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\profile\\app.profile.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { AccountService } from \"src/app/service/account/AccountService\";\r\nimport { AppProfileRoutingModule } from \"./app.profile-routing\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\r\nimport { FieldsetModule } from \"primeng/fieldset\";\r\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\r\nimport { InputTextModule } from \"primeng/inputtext\";\r\nimport { ButtonModule } from \"primeng/button\";\r\nimport { CommonVnptModule } from \"../common-module/common.module\";\r\nimport { SplitButtonModule } from \"primeng/splitbutton\";\r\nimport { AutoCompleteModule } from \"primeng/autocomplete\";\r\nimport { CalendarModule } from \"primeng/calendar\";\r\nimport { DropdownModule } from \"primeng/dropdown\";\r\nimport { CardModule } from \"primeng/card\";\r\nimport { DialogModule } from \"primeng/dialog\";\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\nimport { AppProfileDetailComponent } from \"./detail/app.profile.detail.component\";\r\nimport { AppProfileEditComponent } from \"./edit/app.profile.edit.component\";\r\nimport {AppProfileChangePasswordComponent} from \"./change-password/app.profile.change-password.component\";\r\nimport {TabViewModule} from \"primeng/tabview\";\r\nimport {CustomerService} from \"../../service/customer/CustomerService\";\r\nimport {ContractService} from \"../../service/contract/ContractService\";\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\n\r\n@NgModule({\r\n    imports: [\r\n        AppProfileRoutingModule,\r\n        CommonModule,\r\n        BreadcrumbModule,\r\n        FieldsetModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        InputTextModule,\r\n        ButtonModule,\r\n        CommonVnptModule,\r\n        SplitButtonModule,\r\n        AutoCompleteModule,\r\n        CalendarModule,\r\n        DropdownModule,\r\n        CardModule,\r\n        DialogModule,\r\n        InputTextareaModule,\r\n        MultiSelectModule,\r\n        TabViewModule,\r\n        RadioButtonModule\r\n    ],\r\n    declarations: [\r\n        AppProfileDetailComponent,\r\n        AppProfileEditComponent,\r\n        AppProfileChangePasswordComponent\r\n    ],\r\n    providers: [\r\n        AccountService,\r\n        CustomerService,\r\n        ContractService\r\n    ]\r\n})\r\nexport class AppProfileModule{}\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,wCAAwC;AACvE,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAAQC,iCAAiC,QAAO,yDAAyD;AACzG,SAAQC,aAAa,QAAO,iBAAiB;AAC7C,SAAQC,eAAe,QAAO,wCAAwC;AACtE,SAAQC,eAAe,QAAO,wCAAwC;AACtE,SAASC,iBAAiB,QAAQ,qBAAqB;;AAmCvD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;iBANd,CACPzB,cAAc,EACdsB,eAAe,EACfC,eAAe,CAClB;MAAAG,OAAA,GA7BGzB,uBAAuB,EACvBC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVC,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBI,aAAa,EACbG,iBAAiB;IAAA;EAAA;;;2EAaZC,gBAAgB;IAAAE,YAAA,GAVrBT,yBAAyB,EACzBC,uBAAuB,EACvBC,iCAAiC;IAAAM,OAAA,GAvBjCzB,uBAAuB,EACvBC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVC,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBI,aAAa,EACbG,iBAAiB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}