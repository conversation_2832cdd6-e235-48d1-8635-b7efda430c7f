{"ast": null, "code": "export default {\n  text: {\n    templateTextPagination: \"Hiển thị {first} tới {last} của {totalRecords} bản ghi\",\n    stt: \"STT\",\n    page: \"Trang\",\n    action: \"<PERSON><PERSON> tác\",\n    nodata: \"Không có kết quả phù hợp\",\n    itemselected: \"Đã chọn:\",\n    filter: \"Bộ lọc\",\n    advanceSearch: \"Tìm kiếm nâng cao\",\n    createGroupSim: \"Tạo nhóm thuê bao\",\n    createGroupSimAndPushSimToGroup: \"Tạo nhóm thuê bao và gán thuê bao vào nhóm\",\n    all: \"Tất cả\",\n    resultRegister: \"Kết quả đăng ký\",\n    inputText: \"Nhập dữ liệu chuỗi\",\n    inputNumber: \"Nhập dữ liệu số\",\n    selectValue: \"Chọn giá trị\",\n    inputDate: \"Chọn ngày\",\n    inputTimestamp: \"Chọn thời gian\",\n    homepage: \"Trang chủ\",\n    selectOption: \"Chọn giá trị\",\n    selectMoreItem: \"+${maxDisplay} giá trị được chọn\",\n    clearSelected: \"Xoá các mục đã chọn\",\n    textCaptcha: \"Kéo sang để hoàn thành câu đố\",\n    readandagree: \"Tôi đã đọc và đồng ý với\",\n    changeManageData: \"Chuyển quyền quản lý dữ liệu khách hàng cho GDV\"\n  },\n  field: {},\n  lang: {\n    vi: \"Tiếng Việt\",\n    en: \"Tiếng Anh\"\n  },\n  menu: {\n    accountmgmt: \"Quản lý tài khoản\",\n    listaccount: \"Danh sách tài khoản\",\n    listpermission: \"Danh sách quyền\",\n    billmgmt: \"Quản lý hợp đồng\",\n    listbill: \"Danh sách hợp đồng\",\n    configuration: \"Cấu hình\",\n    customermgmt: \"Quản lý khách hàng\",\n    listcustomer: \"Danh sách khách hàng\",\n    dashboard: \"Dashboard\",\n    devicemgmt: \"Quản lý thiết bị\",\n    listdevice: \"Danh sách thiết bị\",\n    extraservice: \"Dịch vụ mở rộng\",\n    guide: \"Hướng dẫn\",\n    manual: \"Hướng dẫn sử dụng\",\n    log: \"Nhật ký\",\n    ordermgmt: \"Quản lý đơn hàng\",\n    listorder: \"Danh sách đơn hàng\",\n    report: \"Báo cáo\",\n    dynamicreport: \"Cấu hình báo cáo động\",\n    dynamicreportgroup: \"Nhóm báo cáo động\",\n    simmgmt: \"Quản lý thuê bao\",\n    listsim: \"Danh sách thuê bao\",\n    subscriptionmgmt: \"Quản lý gói cước\",\n    listsubscription: \"Danh sách gói cước\",\n    troubleshoot: \"Sự cố\",\n    listroles: \"Danh sách nhóm quyền\",\n    listpermissions: \"Danh sách quyền\",\n    detailroles: \"Chi tiết nhóm quyền\",\n    editroles: \"Chỉnh sửa nhóm quyền\",\n    groupSim: \"Nhóm thuê bao\",\n    contract: \"Danh sách hợp đồng\",\n    ratingplanmgmt: \"Quản lý gói cước\",\n    listplan: \"Danh sách gói cước\",\n    registerplan: \"Đăng ký gói cước\",\n    detailplan: \"Chi tiết gói cước\",\n    historyRegister: \"Lịch sử đăng ký\",\n    apnsim: \"APN thuê bao\",\n    apnsimlist: \"Danh sách APN thuê bao\",\n    apnsimdetail: \"Chi tiết APN thuê bao\",\n    account: \"Tài khoản\",\n    detailAccount: \"Thông tin tài khoản\",\n    editAccount: \"Cập nhật\",\n    rule: \"Quản lý cảnh báo\",\n    alerts: \"Cảnh báo\",\n    alertreceivinggroup: \"Nhóm nhận cảnh báo\",\n    alerthistory: \"Lịch sử cảnh báo\",\n    devicedetail: \"Chi tiết thiết bị\",\n    deviceupdate: \"Cập nhật thiết bị\",\n    devicecreate: \"Tạo mới thiết bị\",\n    changePass: \"Đổi mật khẩu\",\n    alert: \"Cảnh báo\",\n    alertSettings: \"Thiết lập quy tắc\",\n    alertReceivingGroup: \"Nhóm nhận cảnh báo\",\n    alertHistory: \"Lịch sử cảnh báo\",\n    alertList: \"Danh sách quy tắc\",\n    groupReceiving: \"Nhóm nhận cảnh báo\",\n    groupReceivingList: \"Danh sách nhóm nhận cảnh báo\",\n    reportGroupReceivingList: \"Danh sách nhóm nhận báo cáo động\",\n    termpolicy: \"Điều khoản và chính sách\",\n    termpolicyhistory: \"Lịch sử xác nhận chính sách\",\n    cmpManagement: \"Hệ thống quản lý thuê bao M2M\",\n    charts: \"Cấu hình biểu đồ\",\n    chartList: \"Danh sách cấu hình biểu đồ\",\n    trafficManagement: \"Quản lý chia sẻ lưu lượng\",\n    subTrafficManagement: \"Quản lý ví lưu lượng\",\n    walletList: \"Danh sách ví lưu lượng\",\n    shareManagement: \"Quản lý chia sẻ\",\n    shareList: \"Danh sách chia sẻ\",\n    walletConfig: \"Cấu hình quản lý lưu lượng\",\n    historyWallet: \"Lịch sử hoạt động\",\n    listGroupSub: \"Nhóm chia sẻ\",\n    autoShareGroup: \"Nhóm chia sẻ tự động\",\n    apiLogs: \"Nhật ký sử dụng API\",\n    userGuide: \"Hướng dẫn chức năng người dùng\",\n    integrationGuide: \"Hướng dẫn tích hợp\"\n  },\n  button: {\n    export: \"Xuất file\",\n    exportSelect: \"Xuất CSV các mục được chọn\",\n    exportFilter: \"Xuất CSV toàn bộ danh sách\",\n    exportExelSelect: \"Xuất Excel các mục được chọn\",\n    exportExelFilter: \"Xuất Excel toàn bộ danh sách\",\n    pushGroupSim: \"Gán thuê bao vào nhóm\",\n    pushToGroupAvailable: \"Gán vào nhóm có sẵn\",\n    pushToNewGroup: \"Gán vào nhóm mới\",\n    cancel: \"Hủy\",\n    registerRatingPlan: \"Đăng ký gói cước\",\n    changeRatingPlan: \"Đổi gói cước\",\n    cancelRatingPlan: \"Hủy gói cước\",\n    assignPlan: \"Gán gói cước\",\n    historyRegisterPlan: \"Lịch sử đăng ký gói cước\",\n    registerPlanForGroup: \"Đăng ký gói cước cho nhóm thuê bao\",\n    registerPlanByFile: \"Đăng ký gói cước bằng nhập file\",\n    create: \"Tạo mới\",\n    edit: \"Sửa\",\n    yes: \"Có\",\n    agree: \"Đồng Ý\",\n    no: \"Không\",\n    save: \"Lưu\",\n    changeStatus: \"Chuyển trạng thái\",\n    delete: \"Xóa\",\n    active: \"Kích hoạt\",\n    approve: \"Phê duyệt\",\n    suspend: \"Tạm ngưng\",\n    uploadFile: \"Kéo thả file hoặc chọn file trong hệ thống\",\n    upFile: \"Upload File\",\n    downloadTemp: \"Tải file mẫu\",\n    back: \"Quay lại\",\n    add: \"Tạo mới\",\n    add2: \"Thêm\",\n    addDefault: \"Mặc định\",\n    view: \"Xem chi tiết\",\n    import: \"Nhập bằng file\",\n    changePass: \"Đổi mật khẩu\",\n    update: \"Cập nhật\",\n    copy: \"Sao chép\",\n    reset: \"Khôi phục\",\n    clear: \"Xóa tất cả\",\n    preview: \"Xem trước\",\n    pushUp: \"Đẩy lên\",\n    pushDown: \"Đẩy xuống\",\n    confirm: \"Xác nhận\",\n    changeManageData: \"Chuyển quyền quản lý dữ liệu\",\n    addSubToGroup: \"Thêm từng thuê bao\",\n    deleteSubInGroup: \"Xóa thuê bao\"\n  },\n  message: {\n    copied: \"Đã sao chép\",\n    required: \"Trường này là bắt buộc\",\n    requiredField: \"${field} là bắt buộc\",\n    maxLength: \"Trường này không được vượt quá ${len} ký tự\",\n    minLength: \"Trường này không được ít hơn ${len} ký tự\",\n    max: \"Trường này có giá trị tối đa là ${value}\",\n    min: \"Trường này có giá trị tối thiểu là ${value}\",\n    numbericMin: \"Trường này không được ít hơn ${length} ký tự số\",\n    numbericMax: \"Trường này không được vượt quá ${length} ký tự số\",\n    duplicated: \"Dữ liệu đã tồn tại\",\n    invalidValue: \"Dữ liệu không đúng định dạng\",\n    formatContainVN: \"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - _, dấu cách, tiếng Việt)\",\n    formatCode: \"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, - _)\",\n    formatCodeNotSub: \"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, _)\",\n    invalidEmail: \"Email sai định dạng\",\n    formatEmail: \"Định dạng email phải là <EMAIL>\",\n    invalidPhone: \"Số điện thoại sai định dạng\",\n    formatPhone: \"Số điện thoại phải là số có đầu 0 (10-11 kí tự) hoặc 84 (11-12 kí tự)\",\n    invalidSubsciption: \"Thuê bao không đúng định dạng\",\n    exists: \"Đã tồn tại ${type} này\",\n    success: \"Thao tác thành công\",\n    error: \"Thao tác thất bại\",\n    saveSuccess: \"Lưu thành công\",\n    saveError: \"Lưu thất bại\",\n    addGroupSuccess: \"Gán thuê bao vào nhóm thành công\",\n    timeout: \"Thao tác quá thời gian\",\n    errorMatchCaptcha: \"Đặt miếng ghép vào đúng vị trí của nó\",\n    confirmDeleteAccount: \"Bạn có chắc chắc muốn xóa tài khoản này không?\",\n    titleConfirmDeleteAccount: \"Xóa tài khoản\",\n    confirmDeletePlan: \"Bạn có chắc chắc muốn xóa gói cước này không?\",\n    titleConfirmDeletePlan: \"Xóa gói cước\",\n    deleteSuccess: \"Xóa thành công\",\n    deleteFail: \"Xóa thất bại\",\n    confirmChangeStatusAccount: \"Bạn có chắc chắn muốn chuyển trạng thái cho tài khoản này không?\",\n    confirmChangeStatusAlert: \"Bạn có chắc chắn muốn chuyển trạng thái cho quy tắc này không?\",\n    titleConfirmChangeStatusAccount: \"Chuyển trạng thái tài khoản\",\n    titleConfirmChangeStatusAlert: \"Chuyển trạng thái quy tắc\",\n    changeStatusSuccess: \"Chuyển trạng thái thành công\",\n    changeStatusFail: \"Chuyển trạng thái thất bại\",\n    titleConfirmDeleteRoles: \"Xóa nhóm quyền\",\n    titleConfirmDeleteAlert: \"Xoá quy tắc\",\n    confirmDeleteRoles: \"Bạn có chắc chắn muốn xóa nhóm quyền này không?\",\n    confirmDeleteAlert: \"Bạn có chắc chắn muốn xóa quy tắc này không?\",\n    titleConfirmChangeStatusRole: \"Chuyển trạng thái nhóm quyền\",\n    confirmChangeStatusRole: \"Bạn có chắc chắn muốn chuyển trạng thái cho nhóm quyền này không?\",\n    conditionExportChoose: \"Giới hạn 1 triệu dòng\",\n    conditionExportFilter: \"Số lượng thuê bao tối đa không được vượt quá 1 triệu\",\n    conditionExportExelFilter: \"Danh sách xuất file vượt quá 100 nghìn bản ghi\",\n    conditionExportFilterEmpty: \"Chưa có thuê bao nào được chọn\",\n    titleConfirmActivePlan: \"Bạn có chắc chắn muốn kích hoạt gói cước này không?\",\n    confirmActivePlan: \"Kích hoạt gói cước\",\n    titleConfirmApprovePlan: \"Bạn có chắc chắn muốn phê duyệt gói cước này không?\",\n    confirmApprovePlan: \"Phê duyệt gói cước\",\n    titleConfirmSuspendPlan: \"Bạn có chắc chắn muốn tạm ngưng gói cước này không?\",\n    confirmSuspendPlan: \"Tạm ngưng gói cước\",\n    titleConfirmDeleteDevice: \"Xóa thiết bị\",\n    confirmDeleteDevice: \"Bạn có chắc chắn muốn xóa thiết bị này không\",\n    activeSuccess: \"Kích hoạt thành công\",\n    approveSuccess: \"Phê duyệt thành công\",\n    suspendSuuccess: \"Tạm ngưng thành công\",\n    activeError: \"Kích hoạt không thành công\",\n    approveError: \"Phê duyệt không thành công\",\n    maxSizeRecordRow: \"Số lượng bản ghi quá giới hạn ${row}\",\n    wrongFileExcel: \"Sai định dạng file, hãy nhập file excel\",\n    invalidFile: \"Định dạng file không hợp lệ\",\n    planNotExists: \"Gói cước không tồn tại\",\n    planNoPermit: \"Không có quyền trên gói cước\",\n    titleConfirmDeleteAlertReceivingGroup: \"Xoá nhóm nhận cảnh báo\",\n    titleConfirmDeleteReportReceivingGroup: \"Xoá nhóm nhận báo cáo động\",\n    titleConfirmDeleteShareGroup: \"Xoá nhóm chia sẻ\",\n    confirmDeleteAlertReceivingGroup: \"Bạn có chắc muốn xoá cảnh báo nhóm nhận cảnh báo này không?\",\n    confirmDeleteReportReceivingGroup: \"Bạn có chắc muốn xoá nhóm nhận báo cáo động này không?\",\n    confirmDeleteShareGroup: \"Bạn có chắc muốn xoá nhóm chia sẻ này không?\",\n    invalidinformation64: \"Thông tin không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký  tự đặc biệt\",\n    invalidinformation32: \"Thông tin không hợp lệ. Vui lòng nhập từ 2 đến 32 ký tự không bao gồm ký  tự đặc biệt\",\n    invalidPasswordFomat: \"Mật khẩu 6-20 ký tự, bao gồm ít nhất 1 chữ cái và 1 số và 1 ký tự đặc biệt\",\n    passwordNotMatch: \"Mật khẩu không khớp\",\n    wrongCurrentPassword: \"Mật khẩu hiện tại sai!\",\n    forgotPassSendMailSuccess: \"Mật khẩu khôi phục đã được gửi tới email của bạn. Xin vui lòng kiểm tra email!.\",\n    notPermissionMisidn: \"Số thuê bao đã được gán cho thiết bị khác hoặc Không có quyền đối với thuê bao, vui lòng nhập lại\",\n    titleConfirmDeleteReport: \"Xoá báo cáo\",\n    confirmDeleteReport: \"Bạn có chắc muốn xoá báo cáo này?\",\n    titleConfirmChangeStatusReport: \"Thay đổi trạng thái báo cáo\",\n    confirmChangeStatusReport: \"Bạn có chắc muốn thay đổi trạng thái báo cáo này?\",\n    confirmCancelPlan: \"Bạn có chắc muốn hủy gói cước \\\"${planName}\\\" cho thuê bao ${msisdn}?\",\n    accuracySuccess: \"Xác thực ví thành công\",\n    accuracyFail: \"Xác thực ví thất bại\",\n    twentydigitlength: \"Trường này không được vượt quá 10 ký tự số\",\n    oneHundredLength: \"Trường này không được vượt quá giá trị 100\",\n    onlySelectGroupOrSub: \"Bạn chỉ được phép nhập Nhóm thuê bao hoặc Thuê bao\",\n    max50Emails: \"Chỉ cho phép tối đa 50 email\",\n    max50Sms: \"Chỉ cho phép tối đa 50 số điện thoại\",\n    emailExist: \"Email đã tồn tại\",\n    phoneExist: \"Số điện thoại đã tồn tại\",\n    urlNotValid: \"URL không đúng định dạng\",\n    onlyPositiveInteger: \"Chỉ cho phép nhập số nguyên dương\",\n    maxsizeupload: \"Dung lượng vượt quá dung lượng tối đa\",\n    titleRejectPolicy: \"XÁC NHẬN VỀ VIỆC PHẢN ĐỐI - HẠN CHẾ - RÚT LẠI SỰ ĐỒNG Ý XỬ LÝ DỮ LIỆU CÁ NHÂN\",\n    messageRejectPolicy1: \"Kính gửi Quý khách hàng,\",\n    messageRejectPolicy2: \"Khách hàng có quyền phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng. Tuy nhiên, việc phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng có thể dẫn tới việc VNPT/Công ty con của VNPT không thể cung cấp Sản phẩm, dịch vụ cho Khách hàng, điều này đồng nghĩa với việc VNPT/Công ty con của VNPT có thể đơn phương chấm dứt hợp đồng mà không cần phải bồi thường cho Khách hàng do các điều kiện để thực hiện hợp đồng đã thay đổi. Do đó, VNPT/Công ty con của VNPT khuyến nghị Khách hàng cân nhắc kĩ lưỡng trước khi phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng\",\n    messageRejectPolicy3: \" Tôi đã đọc và đồng ý với việc Phản đối, hạn chế, rút lại sự đồng ý xử lý dữ liệu cá nhân\",\n    confirmationHistory: \"Lịch sử xác nhận Chính sách bảo vệ dữ liệu cá nhân\",\n    confirmationUserInfo: \"Thông tin tài khoản xác nhận\",\n    confirmationDevice: \"Thông tin thiết bị xác nhận\",\n    wrongFormatName: \"Sai định dạng. Chỉ cho phép dấu cách, chữ tiếng Việt (a-z, A-Z, 0-9, - _)\",\n    notChartData: \"Không có dữ liệu\",\n    isErrorQuery: \"Lỗi query\",\n    errorLoading: \"Có lỗi trong quá trình hiển thị dữ liệu, mời thử lại\"\n  },\n  searchSeperate: {\n    button: {\n      add: \"Thêm bộ lọc\",\n      reset: \"Reset bộ lọc\"\n    },\n    placeholder: {\n      dropdownFlter: \"Chọn bộ lọc\",\n      input: \"Tìm kiếm\",\n      dropdown: \"Chọn giá trị\",\n      calendar: \"Chọn ngày\",\n      rangeCalendar: \"Chọn khoảng ngày\"\n    }\n  },\n  titlepage: {\n    createAlertReceivingGroup: \"Tạo nhóm nhận cảnh báo\",\n    listAlertReceivingGroup: \"Nhóm nhận cảnh báo\",\n    detailAlertReceivingGroup: \"Chi tiết nhóm nhận cảnh báo\",\n    editAlertReceivingGroup: \"Sửa nhóm nhận cảnh báo\",\n    deleteAlertReceivingGroup: \"Xoá nhóm nhận cảnh báo\",\n    listApnSim: \"APN Thuê bao\",\n    detailApnSim: \"Chi tiết APN Thuê bao\",\n    listAlertHistory: \"Lịch sử cảnh báo\",\n    listDevice: \"Thiết bị\",\n    createDevice: \"Tạo mới Thiết bị\",\n    detailDevice: \"Chi tiết thiết bị\",\n    editDevice: \"Chỉnh sửa thiết bị\",\n    deleteDevice: \"Xoá thiết bị\",\n    createaccount: \"Tạo tài khoản\",\n    editaccount: \"Chỉnh sửa tài khoản\",\n    detailaccount: \"Thông tin chi tiết tài khoản\",\n    createRole: \"Tạo nhóm quyền\",\n    detailCustomer: \"Thông tin chi tiết khách hàng\",\n    editCustomer: \"Chỉnh sửa thông tin khách hàng\",\n    detailsim: \"Thông tin chi tiết thuê bao\",\n    listGroupSim: \"Danh sách nhóm thuê bao\",\n    createGroupSim: \"Tạo nhóm thuê bao\",\n    detailGroupSim: \"Thông tin chi tiết nhóm thuê bao\",\n    editGroupSim: \"Chỉnh sửa nhóm thuê bao\",\n    listContract: \"Danh sách hợp đồng\",\n    createRatingPlan: \"Tạo gói cước\",\n    editRatingPlan: \"Chỉnh sửa gói cước\",\n    historyRegisterPlan: \"Danh sách lịch sử đăng ký gói cước\",\n    createAlarm: \"Tạo cảnh báo\",\n    detailAlarm: \"Thông tin chi tiết cảnh báo\",\n    editAlarm: \"Chỉnh sửa cảnh báo\",\n    reportDynamic: \"Danh sách báo cáo động\",\n    listGroupReportDynamic: \"Danh sách nhóm báo cáo động\",\n    editGroupReportDynamic: \"Chỉnh sửa nhóm báo cáo động\",\n    detailGroupReportDynamic: \"Chi tiết nhóm báo cáo động\",\n    createGroupReportDynamic: \"Tạo nhóm báo cáo động\",\n    m2SubscriptionManagementSystem: \"Hệ thống quản lý thuê bao M2M\",\n    apiLogs: \"Log API\"\n  }\n};", "map": {"version": 3, "names": ["text", "templateTextPagination", "stt", "page", "action", "nodata", "itemselected", "filter", "advanceSearch", "createGroupSim", "createGroupSimAndPushSimToGroup", "all", "resultRegister", "inputText", "inputNumber", "selectValue", "inputDate", "inputTimestamp", "homepage", "selectOption", "selectMoreItem", "clearSelected", "textCaptcha", "read<PERSON><PERSON><PERSON>", "changeManageData", "field", "lang", "vi", "en", "menu", "accountmgmt", "listaccount", "listpermission", "billmgmt", "listbill", "configuration", "customermgmt", "list<PERSON><PERSON><PERSON>", "dashboard", "devicemgmt", "listdevice", "extraservice", "guide", "manual", "log", "ordermgmt", "listorder", "report", "dynamicreport", "dynamicreportgroup", "simmgmt", "listsim", "subscriptionmgmt", "listsubscription", "troubleshoot", "listroles", "listpermissions", "detailroles", "editroles", "groupSim", "contract", "ratingplanmgmt", "listplan", "registerplan", "detailplan", "historyRegister", "apnsim", "apnsimlist", "apnsimdetail", "account", "detailAccount", "editAccount", "rule", "alerts", "alertreceivinggroup", "alerthistory", "devicedetail", "deviceupdate", "devicecreate", "changePass", "alert", "alertSettings", "alertReceivingGroup", "alertHistory", "alertList", "groupReceiving", "groupReceivingList", "reportGroupReceivingList", "termpolicy", "termpolicyhistory", "cmpManagement", "charts", "chartList", "trafficManagement", "subTrafficManagement", "walletList", "shareManagement", "shareList", "walletConfig", "historyWallet", "listGroupSub", "autoShareGroup", "apiLogs", "userGuide", "integrationGuide", "button", "export", "exportSelect", "exportFilter", "exportExelSelect", "exportExelFilter", "pushGroupSim", "pushToGroupAvailable", "pushToNewGroup", "cancel", "registerRatingPlan", "changeRatingPlan", "cancelRatingPlan", "assignPlan", "historyRegisterPlan", "registerPlanForGroup", "registerPlanByFile", "create", "edit", "yes", "agree", "no", "save", "changeStatus", "delete", "active", "approve", "suspend", "uploadFile", "upFile", "downloadTemp", "back", "add", "add2", "addDefault", "view", "import", "update", "copy", "reset", "clear", "preview", "pushUp", "pushDown", "confirm", "addSubToGroup", "deleteSubInGroup", "message", "copied", "required", "requiredField", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "max", "min", "numbericMin", "numbericMax", "duplicated", "invalidV<PERSON>ue", "formatContainVN", "formatCode", "formatCodeNotSub", "invalidEmail", "formatEmail", "invalidPhone", "formatPhone", "invalidSubsciption", "exists", "success", "error", "saveSuccess", "saveError", "addGroupSuccess", "timeout", "errorMatchCaptcha", "confirmDeleteAccount", "titleConfirmDeleteAccount", "confirmDeletePlan", "titleConfirmDeletePlan", "deleteSuccess", "deleteFail", "confirm<PERSON><PERSON>eStatusAccount", "confirmChangeStatusAlert", "titleConfirmChangeStatusAccount", "titleConfirmChangeStatusAlert", "changeStatusSuccess", "changeStatusFail", "titleConfirmDeleteRoles", "titleConfirmDeleteAlert", "confirmDeleteRoles", "confirmDeleteAlert", "titleConfirmChangeStatusRole", "confirmChangeStatusRole", "conditionExportChoose", "conditionExportFilter", "conditionExportExelFilter", "conditionExportFilterEmpty", "titleConfirmActivePlan", "confirmActivePlan", "titleConfirmApprovePlan", "confirmApprovePlan", "titleConfirmSuspendPlan", "confirmSuspendPlan", "titleConfirmDeleteDevice", "confirmDeleteDevice", "activeSuccess", "approveSuccess", "suspendSuuccess", "activeError", "approveError", "maxSizeRecordRow", "wrongFileExcel", "invalidFile", "planNotExists", "planNoPermit", "titleConfirmDeleteAlertReceivingGroup", "titleConfirmDeleteReportReceivingGroup", "titleConfirmDeleteShareGroup", "confirmDeleteAlertReceivingGroup", "confirmDeleteReportReceivingGroup", "confirmDeleteShareGroup", "invalidinformation64", "invalidinformation32", "invalidPasswordFomat", "passwordNotMatch", "wrongCurrentPassword", "forgotPassSendMailSuccess", "notPermissionMisidn", "titleConfirmDeleteReport", "confirmDeleteReport", "titleConfirmChangeStatusReport", "confirmChangeStatusReport", "confirmCancelPlan", "accuracySuccess", "accuracyFail", "twentydigitlength", "oneHundredLength", "onlySelectGroupOrSub", "max50Emails", "max50Sms", "emailExist", "phoneExist", "urlNotValid", "onlyPositiveInteger", "maxsizeupload", "titleRejectPolicy", "messageRejectPolicy1", "messageRejectPolicy2", "messageRejectPolicy3", "confirmationHistory", "confirmationUserInfo", "confirmationDevice", "wrongFormatName", "notChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorLoading", "searchSeperate", "placeholder", "dropdownFlter", "input", "dropdown", "calendar", "rangeCalendar", "titlepage", "createAlertReceivingGroup", "listAlertReceivingGroup", "detailAlertReceivingGroup", "editAlertReceivingGroup", "deleteAlertReceivingGroup", "listApnSim", "detailApnSim", "listAlertHistory", "listDevice", "createDevice", "detailDevice", "editDevice", "deleteDevice", "createaccount", "editaccount", "detailaccount", "createRole", "detailCustomer", "editCustomer", "detailsim", "listGroupSim", "detailGroupSim", "editGroupSim", "listContract", "createRatingPlan", "editRatingPlan", "createAlarm", "detailAlarm", "editAlarm", "reportDynamic", "listGroupReportDynamic", "editGroupReportDynamic", "detailGroupReportDynamic", "createGroupReportDynamic", "m2SubscriptionManagementSystem"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\global.ts"], "sourcesContent": ["export default {\r\n    text:{\r\n        templateTextPagination: \"Hiển thị {first} tới {last} của {totalRecords} bản ghi\",\r\n        stt: \"STT\",\r\n        page: \"Trang\",\r\n        action: \"<PERSON><PERSON> tác\",\r\n        nodata: \"Không có kết quả phù hợp\",\r\n        itemselected: \"Đã chọn:\",\r\n        filter: \"Bộ lọc\",\r\n        advanceSearch: \"Tìm kiếm nâng cao\",\r\n        createGroupSim: \"Tạo nhóm thuê bao\",\r\n        createGroupSimAndPushSimToGroup: \"Tạo nhóm thuê bao và gán thuê bao vào nhóm\",\r\n        all: \"Tất cả\",\r\n        resultRegister: \"Kết quả đăng ký\",\r\n        inputText: \"Nhập dữ liệu chuỗi\",\r\n        inputNumber: \"Nhập dữ liệu số\",\r\n        selectValue: \"Chọn giá trị\",\r\n        inputDate: \"Chọn ngày\",\r\n        inputTimestamp: \"Chọn thời gian\",\r\n        homepage: \"Trang chủ\",\r\n        selectOption: \"Chọn giá trị\",\r\n        selectMoreItem: \"+${maxDisplay} giá trị được chọn\",\r\n        clearSelected:\"Xoá các mục đã chọn\",\r\n        textCaptcha: \"Kéo sang để hoàn thành câu đố\",\r\n        readandagree: \"Tôi đã đọc và đồng ý với\",\r\n        changeManageData: \"Chuyển quyền quản lý dữ liệu khách hàng cho GDV\",\r\n    },\r\n    field: {\r\n\r\n    },\r\n    lang: {\r\n        vi: \"Tiếng Việt\",\r\n        en: \"Tiếng Anh\"\r\n    },\r\n    menu:{\r\n        accountmgmt: \"Quản lý tài khoản\",\r\n        listaccount: \"Danh sách tài khoản\",\r\n        listpermission: \"Danh sách quyền\",\r\n        billmgmt: \"Quản lý hợp đồng\",\r\n        listbill: \"Danh sách hợp đồng\",\r\n        configuration: \"Cấu hình\",\r\n        customermgmt: \"Quản lý khách hàng\",\r\n        listcustomer: \"Danh sách khách hàng\",\r\n        dashboard: \"Dashboard\",\r\n        devicemgmt: \"Quản lý thiết bị\",\r\n        listdevice: \"Danh sách thiết bị\",\r\n        extraservice: \"Dịch vụ mở rộng\",\r\n        guide: \"Hướng dẫn\",\r\n        manual: \"Hướng dẫn sử dụng\",\r\n        log: \"Nhật ký\",\r\n        ordermgmt: \"Quản lý đơn hàng\",\r\n        listorder: \"Danh sách đơn hàng\",\r\n        report: \"Báo cáo\",\r\n        dynamicreport: \"Cấu hình báo cáo động\",\r\n        dynamicreportgroup: \"Nhóm báo cáo động\",\r\n        simmgmt: \"Quản lý thuê bao\",\r\n        listsim: \"Danh sách thuê bao\",\r\n        subscriptionmgmt: \"Quản lý gói cước\",\r\n        listsubscription: \"Danh sách gói cước\",\r\n        troubleshoot: \"Sự cố\",\r\n        listroles: \"Danh sách nhóm quyền\",\r\n        listpermissions: \"Danh sách quyền\",\r\n        detailroles: \"Chi tiết nhóm quyền\",\r\n        editroles: \"Chỉnh sửa nhóm quyền\",\r\n        groupSim: \"Nhóm thuê bao\",\r\n        contract:\"Danh sách hợp đồng\",\r\n        ratingplanmgmt: \"Quản lý gói cước\",\r\n        listplan: \"Danh sách gói cước\",\r\n        registerplan: \"Đăng ký gói cước\",\r\n        detailplan: \"Chi tiết gói cước\",\r\n        historyRegister: \"Lịch sử đăng ký\",\r\n        apnsim: \"APN thuê bao\",\r\n        apnsimlist: \"Danh sách APN thuê bao\",\r\n        apnsimdetail: \"Chi tiết APN thuê bao\",\r\n        account : \"Tài khoản\",\r\n        detailAccount : \"Thông tin tài khoản\",\r\n        editAccount : \"Cập nhật\",\r\n        rule: \"Quản lý cảnh báo\",\r\n        alerts: \"Cảnh báo\",\r\n        alertreceivinggroup: \"Nhóm nhận cảnh báo\",\r\n        alerthistory: \"Lịch sử cảnh báo\",\r\n        devicedetail: \"Chi tiết thiết bị\",\r\n        deviceupdate: \"Cập nhật thiết bị\",\r\n        devicecreate: \"Tạo mới thiết bị\",\r\n        changePass: \"Đổi mật khẩu\",\r\n        alert: \"Cảnh báo\",\r\n        alertSettings: \"Thiết lập quy tắc\",\r\n        alertReceivingGroup: \"Nhóm nhận cảnh báo\",\r\n        alertHistory: \"Lịch sử cảnh báo\",\r\n        alertList: \"Danh sách quy tắc\",\r\n        groupReceiving: \"Nhóm nhận cảnh báo\",\r\n        groupReceivingList: \"Danh sách nhóm nhận cảnh báo\",\r\n        reportGroupReceivingList: \"Danh sách nhóm nhận báo cáo động\",\r\n        termpolicy: \"Điều khoản và chính sách\",\r\n        termpolicyhistory: \"Lịch sử xác nhận chính sách\",\r\n        cmpManagement: \"Hệ thống quản lý thuê bao M2M\",\r\n        charts: \"Cấu hình biểu đồ\",\r\n        chartList: \"Danh sách cấu hình biểu đồ\",\r\n        trafficManagement: \"Quản lý chia sẻ lưu lượng\",\r\n        subTrafficManagement: \"Quản lý ví lưu lượng\",\r\n        walletList: \"Danh sách ví lưu lượng\",\r\n        shareManagement: \"Quản lý chia sẻ\",\r\n        shareList:\"Danh sách chia sẻ\",\r\n        walletConfig: \"Cấu hình quản lý lưu lượng\",\r\n        historyWallet: \"Lịch sử hoạt động\",\r\n        listGroupSub: \"Nhóm chia sẻ\",\r\n        autoShareGroup: \"Nhóm chia sẻ tự động\",\r\n        apiLogs: \"Nhật ký sử dụng API\",\r\n        userGuide: \"Hướng dẫn chức năng người dùng\",\r\n        integrationGuide: \"Hướng dẫn tích hợp\",\r\n    },\r\n    button: {\r\n        export: \"Xuất file\",\r\n        exportSelect: \"Xuất CSV các mục được chọn\",\r\n        exportFilter: \"Xuất CSV toàn bộ danh sách\",\r\n        exportExelSelect: \"Xuất Excel các mục được chọn\",\r\n        exportExelFilter: \"Xuất Excel toàn bộ danh sách\",\r\n        pushGroupSim: \"Gán thuê bao vào nhóm\",\r\n        pushToGroupAvailable: \"Gán vào nhóm có sẵn\",\r\n        pushToNewGroup: \"Gán vào nhóm mới\",\r\n        cancel: \"Hủy\",\r\n        registerRatingPlan: \"Đăng ký gói cước\",\r\n        changeRatingPlan: \"Đổi gói cước\",\r\n        cancelRatingPlan: \"Hủy gói cước\",\r\n        assignPlan: \"Gán gói cước\",\r\n        historyRegisterPlan: \"Lịch sử đăng ký gói cước\",\r\n        registerPlanForGroup: \"Đăng ký gói cước cho nhóm thuê bao\",\r\n        registerPlanByFile: \"Đăng ký gói cước bằng nhập file\",\r\n        create: \"Tạo mới\",\r\n        edit: \"Sửa\",\r\n        yes: \"Có\",\r\n        agree: \"Đồng Ý\",\r\n        no: \"Không\",\r\n        save: \"Lưu\",\r\n        changeStatus: \"Chuyển trạng thái\",\r\n        delete: \"Xóa\",\r\n        active: \"Kích hoạt\",\r\n        approve: \"Phê duyệt\",\r\n        suspend: \"Tạm ngưng\",\r\n        uploadFile: \"Kéo thả file hoặc chọn file trong hệ thống\",\r\n        upFile: \"Upload File\",\r\n        downloadTemp: \"Tải file mẫu\",\r\n        back: \"Quay lại\",\r\n        add: \"Tạo mới\",\r\n        add2: \"Thêm\",\r\n        addDefault: \"Mặc định\",\r\n        view: \"Xem chi tiết\",\r\n        import: \"Nhập bằng file\",\r\n        changePass : \"Đổi mật khẩu\",\r\n        update: \"Cập nhật\",\r\n        copy: \"Sao chép\",\r\n        reset: \"Khôi phục\",\r\n        clear: \"Xóa tất cả\",\r\n        preview: \"Xem trước\",\r\n        pushUp: \"Đẩy lên\",\r\n        pushDown: \"Đẩy xuống\",\r\n        confirm: \"Xác nhận\",\r\n        changeManageData: \"Chuyển quyền quản lý dữ liệu\",\r\n        addSubToGroup: \"Thêm từng thuê bao\",\r\n        deleteSubInGroup: \"Xóa thuê bao\"\r\n    },\r\n    message:{\r\n        copied: \"Đã sao chép\",\r\n        required: \"Trường này là bắt buộc\",\r\n        requiredField: \"${field} là bắt buộc\",\r\n        maxLength: \"Trường này không được vượt quá ${len} ký tự\",\r\n        minLength: \"Trường này không được ít hơn ${len} ký tự\",\r\n        max: \"Trường này có giá trị tối đa là ${value}\",\r\n        min: \"Trường này có giá trị tối thiểu là ${value}\",\r\n        numbericMin: \"Trường này không được ít hơn ${length} ký tự số\",\r\n        numbericMax: \"Trường này không được vượt quá ${length} ký tự số\",\r\n        duplicated: \"Dữ liệu đã tồn tại\",\r\n        invalidValue: \"Dữ liệu không đúng định dạng\",\r\n        formatContainVN: \"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, . - _, dấu cách, tiếng Việt)\",\r\n        formatCode: \"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, - _)\",\r\n        formatCodeNotSub: \"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, _)\",\r\n        invalidEmail: \"Email sai định dạng\",\r\n        formatEmail: \"Định dạng email phải là <EMAIL>\",\r\n        invalidPhone: \"Số điện thoại sai định dạng\",\r\n        formatPhone: \"Số điện thoại phải là số có đầu 0 (10-11 kí tự) hoặc 84 (11-12 kí tự)\",\r\n        invalidSubsciption: \"Thuê bao không đúng định dạng\",\r\n        exists: \"Đã tồn tại ${type} này\",\r\n        success: \"Thao tác thành công\",\r\n        error: \"Thao tác thất bại\",\r\n        saveSuccess: \"Lưu thành công\",\r\n        saveError: \"Lưu thất bại\",\r\n        addGroupSuccess: \"Gán thuê bao vào nhóm thành công\",\r\n        timeout: \"Thao tác quá thời gian\",\r\n        errorMatchCaptcha: \"Đặt miếng ghép vào đúng vị trí của nó\",\r\n        confirmDeleteAccount: \"Bạn có chắc chắc muốn xóa tài khoản này không?\",\r\n        titleConfirmDeleteAccount: \"Xóa tài khoản\",\r\n        confirmDeletePlan: \"Bạn có chắc chắc muốn xóa gói cước này không?\",\r\n        titleConfirmDeletePlan: \"Xóa gói cước\",\r\n        deleteSuccess: \"Xóa thành công\",\r\n        deleteFail: \"Xóa thất bại\",\r\n        confirmChangeStatusAccount: \"Bạn có chắc chắn muốn chuyển trạng thái cho tài khoản này không?\",\r\n        confirmChangeStatusAlert: \"Bạn có chắc chắn muốn chuyển trạng thái cho quy tắc này không?\",\r\n        titleConfirmChangeStatusAccount: \"Chuyển trạng thái tài khoản\",\r\n        titleConfirmChangeStatusAlert: \"Chuyển trạng thái quy tắc\",\r\n        changeStatusSuccess: \"Chuyển trạng thái thành công\",\r\n        changeStatusFail: \"Chuyển trạng thái thất bại\",\r\n        titleConfirmDeleteRoles: \"Xóa nhóm quyền\",\r\n        titleConfirmDeleteAlert: \"Xoá quy tắc\",\r\n        confirmDeleteRoles: \"Bạn có chắc chắn muốn xóa nhóm quyền này không?\",\r\n        confirmDeleteAlert: \"Bạn có chắc chắn muốn xóa quy tắc này không?\",\r\n        titleConfirmChangeStatusRole: \"Chuyển trạng thái nhóm quyền\",\r\n        confirmChangeStatusRole: \"Bạn có chắc chắn muốn chuyển trạng thái cho nhóm quyền này không?\",\r\n        conditionExportChoose: \"Giới hạn 1 triệu dòng\",\r\n        conditionExportFilter: \"Số lượng thuê bao tối đa không được vượt quá 1 triệu\",\r\n        conditionExportExelFilter: \"Danh sách xuất file vượt quá 100 nghìn bản ghi\",\r\n        conditionExportFilterEmpty: \"Chưa có thuê bao nào được chọn\",\r\n        titleConfirmActivePlan: \"Bạn có chắc chắn muốn kích hoạt gói cước này không?\",\r\n        confirmActivePlan: \"Kích hoạt gói cước\",\r\n        titleConfirmApprovePlan: \"Bạn có chắc chắn muốn phê duyệt gói cước này không?\",\r\n        confirmApprovePlan: \"Phê duyệt gói cước\",\r\n        titleConfirmSuspendPlan: \"Bạn có chắc chắn muốn tạm ngưng gói cước này không?\",\r\n        confirmSuspendPlan: \"Tạm ngưng gói cước\",\r\n        titleConfirmDeleteDevice: \"Xóa thiết bị\",\r\n        confirmDeleteDevice: \"Bạn có chắc chắn muốn xóa thiết bị này không\",\r\n        activeSuccess: \"Kích hoạt thành công\",\r\n        approveSuccess: \"Phê duyệt thành công\",\r\n        suspendSuuccess: \"Tạm ngưng thành công\",\r\n        activeError: \"Kích hoạt không thành công\",\r\n        approveError: \"Phê duyệt không thành công\",\r\n        maxSizeRecordRow: \"Số lượng bản ghi quá giới hạn ${row}\",\r\n        wrongFileExcel: \"Sai định dạng file, hãy nhập file excel\",\r\n        invalidFile: \"Định dạng file không hợp lệ\",\r\n        planNotExists: \"Gói cước không tồn tại\",\r\n        planNoPermit: \"Không có quyền trên gói cước\",\r\n        titleConfirmDeleteAlertReceivingGroup: \"Xoá nhóm nhận cảnh báo\",\r\n        titleConfirmDeleteReportReceivingGroup: \"Xoá nhóm nhận báo cáo động\",\r\n        titleConfirmDeleteShareGroup: \"Xoá nhóm chia sẻ\",\r\n        confirmDeleteAlertReceivingGroup: \"Bạn có chắc muốn xoá cảnh báo nhóm nhận cảnh báo này không?\",\r\n        confirmDeleteReportReceivingGroup: \"Bạn có chắc muốn xoá nhóm nhận báo cáo động này không?\",\r\n        confirmDeleteShareGroup: \"Bạn có chắc muốn xoá nhóm chia sẻ này không?\",\r\n        invalidinformation64: \"Thông tin không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký  tự đặc biệt\",\r\n        invalidinformation32: \"Thông tin không hợp lệ. Vui lòng nhập từ 2 đến 32 ký tự không bao gồm ký  tự đặc biệt\",\r\n        invalidPasswordFomat : \"Mật khẩu 6-20 ký tự, bao gồm ít nhất 1 chữ cái và 1 số và 1 ký tự đặc biệt\",\r\n        passwordNotMatch: \"Mật khẩu không khớp\",\r\n        wrongCurrentPassword : \"Mật khẩu hiện tại sai!\",\r\n        forgotPassSendMailSuccess : \"Mật khẩu khôi phục đã được gửi tới email của bạn. Xin vui lòng kiểm tra email!.\",\r\n        notPermissionMisidn: \"Số thuê bao đã được gán cho thiết bị khác hoặc Không có quyền đối với thuê bao, vui lòng nhập lại\",\r\n        titleConfirmDeleteReport: \"Xoá báo cáo\",\r\n        confirmDeleteReport: \"Bạn có chắc muốn xoá báo cáo này?\",\r\n        titleConfirmChangeStatusReport: \"Thay đổi trạng thái báo cáo\",\r\n        confirmChangeStatusReport: \"Bạn có chắc muốn thay đổi trạng thái báo cáo này?\",\r\n        confirmCancelPlan: \"Bạn có chắc muốn hủy gói cước \\\"${planName}\\\" cho thuê bao ${msisdn}?\",\r\n        accuracySuccess:\"Xác thực ví thành công\",\r\n        accuracyFail: \"Xác thực ví thất bại\",\r\n        twentydigitlength: \"Trường này không được vượt quá 10 ký tự số\",\r\n        oneHundredLength : \"Trường này không được vượt quá giá trị 100\",\r\n        onlySelectGroupOrSub : \"Bạn chỉ được phép nhập Nhóm thuê bao hoặc Thuê bao\",\r\n        max50Emails: \"Chỉ cho phép tối đa 50 email\",\r\n        max50Sms : \"Chỉ cho phép tối đa 50 số điện thoại\",\r\n        emailExist : \"Email đã tồn tại\",\r\n        phoneExist : \"Số điện thoại đã tồn tại\",\r\n        urlNotValid : \"URL không đúng định dạng\",\r\n        onlyPositiveInteger : \"Chỉ cho phép nhập số nguyên dương\",\r\n        maxsizeupload:\"Dung lượng vượt quá dung lượng tối đa\",\r\n        titleRejectPolicy: \"XÁC NHẬN VỀ VIỆC PHẢN ĐỐI - HẠN CHẾ - RÚT LẠI SỰ ĐỒNG Ý XỬ LÝ DỮ LIỆU CÁ NHÂN\",\r\n        messageRejectPolicy1: \"Kính gửi Quý khách hàng,\",\r\n        messageRejectPolicy2: \"Khách hàng có quyền phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng. Tuy nhiên, việc phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng có thể dẫn tới việc VNPT/Công ty con của VNPT không thể cung cấp Sản phẩm, dịch vụ cho Khách hàng, điều này đồng nghĩa với việc VNPT/Công ty con của VNPT có thể đơn phương chấm dứt hợp đồng mà không cần phải bồi thường cho Khách hàng do các điều kiện để thực hiện hợp đồng đã thay đổi. Do đó, VNPT/Công ty con của VNPT khuyến nghị Khách hàng cân nhắc kĩ lưỡng trước khi phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng\",\r\n        messageRejectPolicy3: \" Tôi đã đọc và đồng ý với việc Phản đối, hạn chế, rút lại sự đồng ý xử lý dữ liệu cá nhân\",\r\n        confirmationHistory: \"Lịch sử xác nhận Chính sách bảo vệ dữ liệu cá nhân\",\r\n        confirmationUserInfo: \"Thông tin tài khoản xác nhận\",\r\n        confirmationDevice: \"Thông tin thiết bị xác nhận\",\r\n        wrongFormatName: \"Sai định dạng. Chỉ cho phép dấu cách, chữ tiếng Việt (a-z, A-Z, 0-9, - _)\",\r\n        notChartData: \"Không có dữ liệu\",\r\n        isErrorQuery: \"Lỗi query\",\r\n        errorLoading: \"Có lỗi trong quá trình hiển thị dữ liệu, mời thử lại\"\r\n    },\r\n    searchSeperate:{\r\n        button:{\r\n            add:\"Thêm bộ lọc\",\r\n            reset:\"Reset bộ lọc\"\r\n        },\r\n        placeholder:{\r\n            dropdownFlter:\"Chọn bộ lọc\",\r\n            input:\"Tìm kiếm\",\r\n            dropdown:\"Chọn giá trị\",\r\n            calendar:\"Chọn ngày\",\r\n            rangeCalendar:\"Chọn khoảng ngày\"\r\n        }\r\n    },\r\n    titlepage: {\r\n        createAlertReceivingGroup: \"Tạo nhóm nhận cảnh báo\",\r\n        listAlertReceivingGroup: \"Nhóm nhận cảnh báo\",\r\n        detailAlertReceivingGroup: \"Chi tiết nhóm nhận cảnh báo\",\r\n        editAlertReceivingGroup: \"Sửa nhóm nhận cảnh báo\",\r\n        deleteAlertReceivingGroup: \"Xoá nhóm nhận cảnh báo\",\r\n        listApnSim: \"APN Thuê bao\",\r\n        detailApnSim: \"Chi tiết APN Thuê bao\",\r\n        listAlertHistory: \"Lịch sử cảnh báo\",\r\n        listDevice: \"Thiết bị\",\r\n        createDevice: \"Tạo mới Thiết bị\",\r\n        detailDevice: \"Chi tiết thiết bị\",\r\n        editDevice: \"Chỉnh sửa thiết bị\",\r\n        deleteDevice: \"Xoá thiết bị\",\r\n        createaccount: \"Tạo tài khoản\",\r\n        editaccount: \"Chỉnh sửa tài khoản\",\r\n        detailaccount: \"Thông tin chi tiết tài khoản\",\r\n        createRole: \"Tạo nhóm quyền\",\r\n        detailCustomer: \"Thông tin chi tiết khách hàng\",\r\n        editCustomer: \"Chỉnh sửa thông tin khách hàng\",\r\n        detailsim: \"Thông tin chi tiết thuê bao\",\r\n        listGroupSim: \"Danh sách nhóm thuê bao\",\r\n        createGroupSim: \"Tạo nhóm thuê bao\",\r\n        detailGroupSim: \"Thông tin chi tiết nhóm thuê bao\",\r\n        editGroupSim: \"Chỉnh sửa nhóm thuê bao\",\r\n        listContract: \"Danh sách hợp đồng\",\r\n        createRatingPlan: \"Tạo gói cước\",\r\n        editRatingPlan: \"Chỉnh sửa gói cước\",\r\n        historyRegisterPlan: \"Danh sách lịch sử đăng ký gói cước\",\r\n        createAlarm: \"Tạo cảnh báo\",\r\n        detailAlarm: \"Thông tin chi tiết cảnh báo\",\r\n        editAlarm: \"Chỉnh sửa cảnh báo\",\r\n        reportDynamic: \"Danh sách báo cáo động\",\r\n        listGroupReportDynamic: \"Danh sách nhóm báo cáo động\",\r\n        editGroupReportDynamic: \"Chỉnh sửa nhóm báo cáo động\",\r\n        detailGroupReportDynamic: \"Chi tiết nhóm báo cáo động\",\r\n        createGroupReportDynamic: \"Tạo nhóm báo cáo động\",\r\n        m2SubscriptionManagementSystem: \"Hệ thống quản lý thuê bao M2M\",\r\n        apiLogs: \"Log API\"\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAC;IACDC,sBAAsB,EAAE,wDAAwD;IAChFC,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,UAAU;IAClBC,MAAM,EAAE,0BAA0B;IAClCC,YAAY,EAAE,UAAU;IACxBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,mBAAmB;IAClCC,cAAc,EAAE,mBAAmB;IACnCC,+BAA+B,EAAE,4CAA4C;IAC7EC,GAAG,EAAE,QAAQ;IACbC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,oBAAoB;IAC/BC,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,WAAW;IACtBC,cAAc,EAAE,gBAAgB;IAChCC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,kCAAkC;IAClDC,aAAa,EAAC,qBAAqB;IACnCC,WAAW,EAAE,+BAA+B;IAC5CC,YAAY,EAAE,0BAA0B;IACxCC,gBAAgB,EAAE;GACrB;EACDC,KAAK,EAAE,EAEN;EACDC,IAAI,EAAE;IACFC,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE;GACP;EACDC,IAAI,EAAC;IACDC,WAAW,EAAE,mBAAmB;IAChCC,WAAW,EAAE,qBAAqB;IAClCC,cAAc,EAAE,iBAAiB;IACjCC,QAAQ,EAAE,kBAAkB;IAC5BC,QAAQ,EAAE,oBAAoB;IAC9BC,aAAa,EAAE,UAAU;IACzBC,YAAY,EAAE,oBAAoB;IAClCC,YAAY,EAAE,sBAAsB;IACpCC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,kBAAkB;IAC9BC,UAAU,EAAE,oBAAoB;IAChCC,YAAY,EAAE,iBAAiB;IAC/BC,KAAK,EAAE,WAAW;IAClBC,MAAM,EAAE,mBAAmB;IAC3BC,GAAG,EAAE,SAAS;IACdC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE,oBAAoB;IAC/BC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,uBAAuB;IACtCC,kBAAkB,EAAE,mBAAmB;IACvCC,OAAO,EAAE,kBAAkB;IAC3BC,OAAO,EAAE,oBAAoB;IAC7BC,gBAAgB,EAAE,kBAAkB;IACpCC,gBAAgB,EAAE,oBAAoB;IACtCC,YAAY,EAAE,OAAO;IACrBC,SAAS,EAAE,sBAAsB;IACjCC,eAAe,EAAE,iBAAiB;IAClCC,WAAW,EAAE,qBAAqB;IAClCC,SAAS,EAAE,sBAAsB;IACjCC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAC,oBAAoB;IAC7BC,cAAc,EAAE,kBAAkB;IAClCC,QAAQ,EAAE,oBAAoB;IAC9BC,YAAY,EAAE,kBAAkB;IAChCC,UAAU,EAAE,mBAAmB;IAC/BC,eAAe,EAAE,iBAAiB;IAClCC,MAAM,EAAE,cAAc;IACtBC,UAAU,EAAE,wBAAwB;IACpCC,YAAY,EAAE,uBAAuB;IACrCC,OAAO,EAAG,WAAW;IACrBC,aAAa,EAAG,qBAAqB;IACrCC,WAAW,EAAG,UAAU;IACxBC,IAAI,EAAE,kBAAkB;IACxBC,MAAM,EAAE,UAAU;IAClBC,mBAAmB,EAAE,oBAAoB;IACzCC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,mBAAmB;IACjCC,YAAY,EAAE,mBAAmB;IACjCC,YAAY,EAAE,kBAAkB;IAChCC,UAAU,EAAE,cAAc;IAC1BC,KAAK,EAAE,UAAU;IACjBC,aAAa,EAAE,mBAAmB;IAClCC,mBAAmB,EAAE,oBAAoB;IACzCC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE,mBAAmB;IAC9BC,cAAc,EAAE,oBAAoB;IACpCC,kBAAkB,EAAE,8BAA8B;IAClDC,wBAAwB,EAAE,kCAAkC;IAC5DC,UAAU,EAAE,0BAA0B;IACtCC,iBAAiB,EAAE,6BAA6B;IAChDC,aAAa,EAAE,+BAA+B;IAC9CC,MAAM,EAAE,kBAAkB;IAC1BC,SAAS,EAAE,4BAA4B;IACvCC,iBAAiB,EAAE,2BAA2B;IAC9CC,oBAAoB,EAAE,sBAAsB;IAC5CC,UAAU,EAAE,wBAAwB;IACpCC,eAAe,EAAE,iBAAiB;IAClCC,SAAS,EAAC,mBAAmB;IAC7BC,YAAY,EAAE,4BAA4B;IAC1CC,aAAa,EAAE,mBAAmB;IAClCC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,sBAAsB;IACtCC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,gCAAgC;IAC3CC,gBAAgB,EAAE;GACrB;EACDC,MAAM,EAAE;IACJC,MAAM,EAAE,WAAW;IACnBC,YAAY,EAAE,4BAA4B;IAC1CC,YAAY,EAAE,4BAA4B;IAC1CC,gBAAgB,EAAE,8BAA8B;IAChDC,gBAAgB,EAAE,8BAA8B;IAChDC,YAAY,EAAE,uBAAuB;IACrCC,oBAAoB,EAAE,qBAAqB;IAC3CC,cAAc,EAAE,kBAAkB;IAClCC,MAAM,EAAE,KAAK;IACbC,kBAAkB,EAAE,kBAAkB;IACtCC,gBAAgB,EAAE,cAAc;IAChCC,gBAAgB,EAAE,cAAc;IAChCC,UAAU,EAAE,cAAc;IAC1BC,mBAAmB,EAAE,0BAA0B;IAC/CC,oBAAoB,EAAE,oCAAoC;IAC1DC,kBAAkB,EAAE,iCAAiC;IACrDC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,QAAQ;IACfC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,mBAAmB;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAE,4CAA4C;IACxDC,MAAM,EAAE,aAAa;IACrBC,YAAY,EAAE,cAAc;IAC5BC,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,gBAAgB;IACxB9D,UAAU,EAAG,cAAc;IAC3B+D,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,YAAY;IACnBC,OAAO,EAAE,WAAW;IACpBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,WAAW;IACrBC,OAAO,EAAE,UAAU;IACnB7H,gBAAgB,EAAE,8BAA8B;IAChD8H,aAAa,EAAE,oBAAoB;IACnCC,gBAAgB,EAAE;GACrB;EACDC,OAAO,EAAC;IACJC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,wBAAwB;IAClCC,aAAa,EAAE,sBAAsB;IACrCC,SAAS,EAAE,6CAA6C;IACxDC,SAAS,EAAE,2CAA2C;IACtDC,GAAG,EAAE,0CAA0C;IAC/CC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE,iDAAiD;IAC9DC,WAAW,EAAE,mDAAmD;IAChEC,UAAU,EAAE,oBAAoB;IAChCC,YAAY,EAAE,8BAA8B;IAC5CC,eAAe,EAAE,0EAA0E;IAC3FC,UAAU,EAAE,kDAAkD;IAC9DC,gBAAgB,EAAE,gDAAgD;IAClEC,YAAY,EAAE,qBAAqB;IACnCC,WAAW,EAAE,qCAAqC;IAClDC,YAAY,EAAE,6BAA6B;IAC3CC,WAAW,EAAE,uEAAuE;IACpFC,kBAAkB,EAAE,+BAA+B;IACnDC,MAAM,EAAE,wBAAwB;IAChCC,OAAO,EAAE,qBAAqB;IAC9BC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,gBAAgB;IAC7BC,SAAS,EAAE,cAAc;IACzBC,eAAe,EAAE,kCAAkC;IACnDC,OAAO,EAAE,wBAAwB;IACjCC,iBAAiB,EAAE,uCAAuC;IAC1DC,oBAAoB,EAAE,gDAAgD;IACtEC,yBAAyB,EAAE,eAAe;IAC1CC,iBAAiB,EAAE,+CAA+C;IAClEC,sBAAsB,EAAE,cAAc;IACtCC,aAAa,EAAE,gBAAgB;IAC/BC,UAAU,EAAE,cAAc;IAC1BC,0BAA0B,EAAE,kEAAkE;IAC9FC,wBAAwB,EAAE,gEAAgE;IAC1FC,+BAA+B,EAAE,6BAA6B;IAC9DC,6BAA6B,EAAE,2BAA2B;IAC1DC,mBAAmB,EAAE,8BAA8B;IACnDC,gBAAgB,EAAE,4BAA4B;IAC9CC,uBAAuB,EAAE,gBAAgB;IACzCC,uBAAuB,EAAE,aAAa;IACtCC,kBAAkB,EAAE,iDAAiD;IACrEC,kBAAkB,EAAE,8CAA8C;IAClEC,4BAA4B,EAAE,8BAA8B;IAC5DC,uBAAuB,EAAE,mEAAmE;IAC5FC,qBAAqB,EAAE,uBAAuB;IAC9CC,qBAAqB,EAAE,sDAAsD;IAC7EC,yBAAyB,EAAE,gDAAgD;IAC3EC,0BAA0B,EAAE,gCAAgC;IAC5DC,sBAAsB,EAAE,qDAAqD;IAC7EC,iBAAiB,EAAE,oBAAoB;IACvCC,uBAAuB,EAAE,qDAAqD;IAC9EC,kBAAkB,EAAE,oBAAoB;IACxCC,uBAAuB,EAAE,qDAAqD;IAC9EC,kBAAkB,EAAE,oBAAoB;IACxCC,wBAAwB,EAAE,cAAc;IACxCC,mBAAmB,EAAE,8CAA8C;IACnEC,aAAa,EAAE,sBAAsB;IACrCC,cAAc,EAAE,sBAAsB;IACtCC,eAAe,EAAE,sBAAsB;IACvCC,WAAW,EAAE,4BAA4B;IACzCC,YAAY,EAAE,4BAA4B;IAC1CC,gBAAgB,EAAE,sCAAsC;IACxDC,cAAc,EAAE,yCAAyC;IACzDC,WAAW,EAAE,6BAA6B;IAC1CC,aAAa,EAAE,wBAAwB;IACvCC,YAAY,EAAE,8BAA8B;IAC5CC,qCAAqC,EAAE,wBAAwB;IAC/DC,sCAAsC,EAAE,4BAA4B;IACpEC,4BAA4B,EAAE,kBAAkB;IAChDC,gCAAgC,EAAE,6DAA6D;IAC/FC,iCAAiC,EAAE,wDAAwD;IAC3FC,uBAAuB,EAAE,8CAA8C;IACvEC,oBAAoB,EAAE,uFAAuF;IAC7GC,oBAAoB,EAAE,uFAAuF;IAC7GC,oBAAoB,EAAG,4EAA4E;IACnGC,gBAAgB,EAAE,qBAAqB;IACvCC,oBAAoB,EAAG,wBAAwB;IAC/CC,yBAAyB,EAAG,iFAAiF;IAC7GC,mBAAmB,EAAE,mGAAmG;IACxHC,wBAAwB,EAAE,aAAa;IACvCC,mBAAmB,EAAE,mCAAmC;IACxDC,8BAA8B,EAAE,6BAA6B;IAC7DC,yBAAyB,EAAE,mDAAmD;IAC9EC,iBAAiB,EAAE,uEAAuE;IAC1FC,eAAe,EAAC,wBAAwB;IACxCC,YAAY,EAAE,sBAAsB;IACpCC,iBAAiB,EAAE,4CAA4C;IAC/DC,gBAAgB,EAAG,4CAA4C;IAC/DC,oBAAoB,EAAG,oDAAoD;IAC3EC,WAAW,EAAE,8BAA8B;IAC3CC,QAAQ,EAAG,sCAAsC;IACjDC,UAAU,EAAG,kBAAkB;IAC/BC,UAAU,EAAG,0BAA0B;IACvCC,WAAW,EAAG,0BAA0B;IACxCC,mBAAmB,EAAG,mCAAmC;IACzDC,aAAa,EAAC,uCAAuC;IACrDC,iBAAiB,EAAE,+EAA+E;IAClGC,oBAAoB,EAAE,2BAA2B;IACjDC,oBAAoB,EAAE,koBAAkoB;IACxpBC,oBAAoB,EAAE,gGAAgG;IACtHC,mBAAmB,EAAE,oDAAoD;IACzEC,oBAAoB,EAAE,8BAA8B;IACpDC,kBAAkB,EAAE,6BAA6B;IACjDC,eAAe,EAAE,2EAA2E;IAC5FC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE;GACjB;EACDC,cAAc,EAAC;IACX5J,MAAM,EAAC;MACHgC,GAAG,EAAC,aAAa;MACjBO,KAAK,EAAC;KACT;IACDsH,WAAW,EAAC;MACRC,aAAa,EAAC,aAAa;MAC3BC,KAAK,EAAC,UAAU;MAChBC,QAAQ,EAAC,cAAc;MACvBC,QAAQ,EAAC,WAAW;MACpBC,aAAa,EAAC;;GAErB;EACDC,SAAS,EAAE;IACPC,yBAAyB,EAAE,wBAAwB;IACnDC,uBAAuB,EAAE,oBAAoB;IAC7CC,yBAAyB,EAAE,6BAA6B;IACxDC,uBAAuB,EAAE,wBAAwB;IACjDC,yBAAyB,EAAE,wBAAwB;IACnDC,UAAU,EAAE,cAAc;IAC1BC,YAAY,EAAE,uBAAuB;IACrCC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,mBAAmB;IACjCC,UAAU,EAAE,oBAAoB;IAChCC,YAAY,EAAE,cAAc;IAC5BC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,8BAA8B;IAC7CC,UAAU,EAAE,gBAAgB;IAC5BC,cAAc,EAAE,+BAA+B;IAC/CC,YAAY,EAAE,gCAAgC;IAC9CC,SAAS,EAAE,6BAA6B;IACxCC,YAAY,EAAE,yBAAyB;IACvCxR,cAAc,EAAE,mBAAmB;IACnCyR,cAAc,EAAE,kCAAkC;IAClDC,YAAY,EAAE,yBAAyB;IACvCC,YAAY,EAAE,oBAAoB;IAClCC,gBAAgB,EAAE,cAAc;IAChCC,cAAc,EAAE,oBAAoB;IACpC/K,mBAAmB,EAAE,oCAAoC;IACzDgL,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,6BAA6B;IAC1CC,SAAS,EAAE,oBAAoB;IAC/BC,aAAa,EAAE,wBAAwB;IACvCC,sBAAsB,EAAE,6BAA6B;IACrDC,sBAAsB,EAAE,6BAA6B;IACrDC,wBAAwB,EAAE,4BAA4B;IACtDC,wBAAwB,EAAE,uBAAuB;IACjDC,8BAA8B,EAAE,+BAA+B;IAC/DzM,OAAO,EAAE;;CAEhB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}