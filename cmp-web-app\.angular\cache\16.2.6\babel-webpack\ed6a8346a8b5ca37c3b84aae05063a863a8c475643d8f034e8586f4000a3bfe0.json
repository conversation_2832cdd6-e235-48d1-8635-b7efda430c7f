{"ast": null, "code": "export default {\n  label: {\n    centerCode: \"Mã trung tâm\",\n    paymentName: \"<PERSON>ên thanh toán\",\n    contactPhone: \"<PERSON><PERSON><PERSON>n thoại liên hệ\",\n    contractor: \"Người làm hợp đồng\",\n    contractCode: \"<PERSON><PERSON> hợp đồng\",\n    customerCode: \"<PERSON><PERSON> khách hàng\",\n    customerName: \"<PERSON>ê<PERSON> khách hàng\",\n    contractDate: \"<PERSON><PERSON><PERSON> làm hợp đồng\",\n    title: \"<PERSON><PERSON> sách hợp đồng\",\n    contactAddress: \"Địa chỉ liên hệ\",\n    paymentAddress: \"Địa chỉ thanh toán\",\n    routeCode: \"Mã tuyến đường\",\n    customerBirthday: \"<PERSON><PERSON><PERSON> sinh khách hàng\",\n    viewSimList: \"Xem danh sách Sim\",\n    headerModal: \"Danh sách Sim\"\n  }\n};", "map": {"version": 3, "names": ["label", "centerCode", "paymentName", "contactPhone", "contractor", "contractCode", "customerCode", "customerName", "contractDate", "title", "contactAddress", "paymentAddress", "routeCode", "customerBirthday", "viewSimList", "headerModal"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\contract.ts"], "sourcesContent": ["export default {\r\n    label:{\r\n        centerCode:\"Mã trung tâm\",\r\n        paymentName:\"<PERSON>ên thanh toán\",\r\n        contactPhone:\"<PERSON><PERSON><PERSON>n thoại liên hệ\",\r\n        contractor:\"Người làm hợp đồng\",\r\n        contractCode:\"<PERSON><PERSON> hợp đồng\",\r\n        customerCode:\"<PERSON><PERSON> khách hàng\",\r\n        customerName:\"<PERSON>ê<PERSON> khách hàng\",\r\n        contractDate:\"<PERSON><PERSON><PERSON> làm hợp đồng\",\r\n        title:\"<PERSON><PERSON> sách hợp đồng\",\r\n        contactAddress:\"Địa chỉ liên hệ\",\r\n        paymentAddress:\"Địa chỉ thanh toán\",\r\n        routeCode:\"Mã tuyến đường\",\r\n        customerBirthday:\"<PERSON><PERSON><PERSON> sinh khách hàng\",\r\n        viewSimList:\"Xem danh sách Sim\",\r\n        headerModal:\"Danh sách Sim\",\r\n    }\r\n}"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAC;IACFC,UAAU,EAAC,cAAc;IACzBC,WAAW,EAAC,gBAAgB;IAC5BC,YAAY,EAAC,oBAAoB;IACjCC,UAAU,EAAC,oBAAoB;IAC/BC,YAAY,EAAC,aAAa;IAC1BC,YAAY,EAAC,eAAe;IAC5BC,YAAY,EAAC,gBAAgB;IAC7BC,YAAY,EAAC,mBAAmB;IAChCC,KAAK,EAAC,oBAAoB;IAC1BC,cAAc,EAAC,iBAAiB;IAChCC,cAAc,EAAC,oBAAoB;IACnCC,SAAS,EAAC,gBAAgB;IAC1BC,gBAAgB,EAAC,sBAAsB;IACvCC,WAAW,EAAC,mBAAmB;IAC/BC,WAAW,EAAC;;CAEnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}