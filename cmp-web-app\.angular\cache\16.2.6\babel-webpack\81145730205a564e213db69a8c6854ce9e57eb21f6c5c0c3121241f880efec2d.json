{"ast": null, "code": "import { ComponentBase } from 'src/app/component.base';\nimport { ReportReceivingGroupService } from 'src/app/service/report-receiving-group/ReportReceivingGroup';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"../../../common-module/table/table.component\";\nimport * as i7 from \"primeng/card\";\nimport * as i8 from \"src/app/service/report-receiving-group/ReportReceivingGroup\";\nconst _c0 = [\"class\", \"group-receiving detail\"];\nfunction ReportGroupReceivingDetailComponent_small_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c1 = function () {\n  return {\n    len: 50\n  };\n};\nfunction ReportGroupReceivingDetailComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c1)));\n  }\n}\nfunction ReportGroupReceivingDetailComponent_small_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nexport class ReportGroupReceivingDetailComponent extends ComponentBase {\n  constructor(reportReceivingGroupService, formBuilder, injector) {\n    super(injector);\n    this.reportReceivingGroupService = reportReceivingGroupService;\n    this.formBuilder = formBuilder;\n    this.selectItems = [];\n    this.rgId = parseInt(this.route.snapshot.paramMap.get(\"id\"));\n  }\n  ngOnInit() {\n    let me = this;\n    this.messageCommonService.onload();\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.dynamicreportgroup\")\n    }, {\n      label: this.tranService.translate(\"global.menu.reportGroupReceivingList\"),\n      routerLink: \"/reports/group-report-dynamic\"\n    }, {\n      label: this.tranService.translate(\"global.button.view\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.receivingGroupInfo = {\n      name: \"nhom1\",\n      description: null,\n      emails: []\n    };\n    this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);\n    this.formMailInput = this.formBuilder.group({\n      email: \"\"\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.selectItems = [];\n    this.columns = [{\n      name: this.tranService.translate(\"report.receiving.emails\"),\n      key: \"emails\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.formReceivingGroup.get('name').disable();\n    this.formReceivingGroup.get('description').disable();\n    this.messageCommonService.onload();\n    this.reportReceivingGroupService.getDetailReceivingGroup(this.rgId, response => {\n      me.receivingGroupInfo = response;\n      me.receivingGroupInfo.emails = response.emails;\n      if (response.emails != null) {\n        for (let i = 0; i < response.emails.split(\", \").length; i++) {\n          me.dataSet.content.push({\n            emails: response.emails.split(\", \")[i]\n          });\n          // me.myEmails.push(response.emails.split(\", \")[i])\n        }\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n  }\n  ngAfterContentChecked() {}\n  // onSubmitCreate(){\n  //     let dataBody = {\n  //         // username: this.accountInfo.accountName,\n  //     }\n  //     this.messageCommonService.onload();\n  //     let me = this;\n  //     this.reportReceivingGroup.createAccount(dataBody, (response)=>{\n  //         me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n  //         // me.router.navigate(['/accounts/edit/'+response.id]);\n  //     })\n  // }\n  closeForm() {\n    this.router.navigate(['/reports/group-report-dynamic']);\n  }\n  addEmail(val) {\n    let me = this;\n    me.dataSet.content.push({\n      emails: val\n    });\n    me.receivingGroupInfo.emails.push({\n      emails: val\n    });\n    // me.dataSet.content.push(me.receivingGroupInfo)\n  }\n\n  search() {\n    let me = this;\n    me.dataSet = {\n      content: [],\n      total: 0\n    };\n  }\n  removeEmail(val) {\n    let me = this;\n    me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1);\n    me.receivingGroupInfo.emails.splice(me.receivingGroupInfo.emails.indexOf(val), 1);\n  }\n  deleteReceivingGroup() {\n    let me = this;\n    me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmDeleteReportReceivingGroup\"), me.tranService.translate(\"global.message.confirmDeleteReportReceivingGroup\"), {\n      ok: () => {\n        me.reportReceivingGroupService.deleteReportGroup(me.rgId, response => {\n          me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\n          me.router.navigate(['/reports/group-report-dynamic']);\n        });\n      },\n      cancel: () => {\n        // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n      }\n    });\n  }\n  onEdit() {\n    let me = this;\n    let receivingGroupId = this.route.snapshot.paramMap.get(\"id\");\n    me.router.navigate([`/reports/group-report-dynamic/edit/${receivingGroupId}`]);\n  }\n  static {\n    this.ɵfac = function ReportGroupReceivingDetailComponent_Factory(t) {\n      return new (t || ReportGroupReceivingDetailComponent)(i0.ɵɵdirectiveInject(ReportReceivingGroupService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportGroupReceivingDetailComponent,\n      selectors: [[\"report\", 8, \"group-receiving\", \"detail\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c0,\n      decls: 36,\n      vars: 25,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"p-2\"], [\"pButton\", \"\", \"type\", \"submit\", \"icon\", \"\", 1, \"p-button-success\", 3, \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"icon\", \"pi pi-trash\", 1, \"p-button-danger\", 3, \"label\", \"click\"], [1, \"p-4\"], [\"action\", \"\", 3, \"formGroup\"], [1, \"pt-0\", \"shadow-2\", \"border-round-md\", \"m-1\", \"flex\", \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"col-8\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"name\", 1, \"col-fixed\", 2, \"width\", \"250px\"], [1, \"text-red-500\"], [1, \"col\"], [\"pInputText\", \"\", \"id\", \"name\", \"formControlName\", \"name\", \"pattern\", \"^[a-zA-Z0-9\\\\-_]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"name\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"col-fixed\", 2, \"width\", \"250px\"], [\"pInputText\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 3, \"ngModel\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [1, \"ml-2\"], [1, \"flex-1\"], [1, \"field\", \"px-4\", \"pt-4\", \"flex-row\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"scrollHeight\", \"selectItemsChange\"]],\n      template: function ReportGroupReceivingDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ReportGroupReceivingDetailComponent_Template_button_click_7_listener() {\n            return ctx.onEdit();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ReportGroupReceivingDetailComponent_Template_button_click_8_listener() {\n            return ctx.deleteReceivingGroup();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(9, \"p-card\", 8)(10, \"form\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"div\", 12)(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementStart(16, \"span\", 14);\n          i0.ɵɵtext(17, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ReportGroupReceivingDetailComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.receivingGroupInfo.name = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"label\", 17);\n          i0.ɵɵelementStart(21, \"div\", 15);\n          i0.ɵɵtemplate(22, ReportGroupReceivingDetailComponent_small_22_Template, 2, 1, \"small\", 18);\n          i0.ɵɵtemplate(23, ReportGroupReceivingDetailComponent_small_23_Template, 2, 2, \"small\", 18);\n          i0.ɵɵtemplate(24, ReportGroupReceivingDetailComponent_small_24_Template, 2, 1, \"small\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 12)(26, \"label\", 19);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 15)(29, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function ReportGroupReceivingDetailComponent_Template_input_ngModelChange_29_listener($event) {\n            return ctx.receivingGroupInfo.description = $event;\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(30, \"h4\", 21);\n          i0.ɵɵelementStart(31, \"div\", 10)(32, \"div\", 22)(33, \"div\", 23)(34, \"div\", 23)(35, \"table-vnpt\", 24);\n          i0.ɵɵlistener(\"selectItemsChange\", function ReportGroupReceivingDetailComponent_Template_table_vnpt_selectItemsChange_35_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.reportGroupReceivingList\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.edit\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.delete\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.formReceivingGroup);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"report.receiving.name\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.receivingGroupInfo.name)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"report.text.inputNameReceiving\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.formReceivingGroup.controls.name.dirty && (ctx.formReceivingGroup.controls.name.errors == null ? null : ctx.formReceivingGroup.controls.name.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formReceivingGroup.controls.name.errors == null ? null : ctx.formReceivingGroup.controls.name.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formReceivingGroup.controls.name.errors == null ? null : ctx.formReceivingGroup.controls.name.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"report.receiving.description\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.receivingGroupInfo.description)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"report.text.inputDescription\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"scrollHeight\", \"200px\");\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.PatternValidator, i1.FormGroupDirective, i1.FormControlName, i4.InputText, i5.ButtonDirective, i6.TableVnptComponent, i7.Card],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "ReportReceivingGroupService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "tranService", "translate", "ctx_r1", "ɵɵpureFunction0", "_c1", "ctx_r2", "ReportGroupReceivingDetailComponent", "constructor", "reportReceivingGroupService", "formBuilder", "injector", "selectItems", "rgId", "parseInt", "route", "snapshot", "paramMap", "get", "ngOnInit", "me", "messageCommonService", "onload", "items", "label", "routerLink", "home", "icon", "receivingGroupInfo", "name", "description", "emails", "formReceivingGroup", "group", "formMailInput", "email", "dataSet", "content", "total", "columns", "key", "size", "align", "isShow", "isSort", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "disable", "getDetailReceivingGroup", "response", "i", "split", "length", "push", "offload", "ngAfterContentChecked", "closeForm", "router", "navigate", "addEmail", "val", "search", "removeEmail", "splice", "indexOf", "deleteReceivingGroup", "confirm", "ok", "deleteReportGroup", "success", "cancel", "onEdit", "receivingGroupId", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "attrs", "_c0", "decls", "vars", "consts", "template", "ReportGroupReceivingDetailComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ReportGroupReceivingDetailComponent_Template_button_click_7_listener", "ReportGroupReceivingDetailComponent_Template_button_click_8_listener", "ReportGroupReceivingDetailComponent_Template_input_ngModelChange_19_listener", "$event", "ɵɵtemplate", "ReportGroupReceivingDetailComponent_small_22_Template", "ReportGroupReceivingDetailComponent_small_23_Template", "ReportGroupReceivingDetailComponent_small_24_Template", "ReportGroupReceivingDetailComponent_Template_input_ngModelChange_29_listener", "ReportGroupReceivingDetailComponent_Template_table_vnpt_selectItemsChange_35_listener", "ɵɵproperty", "controls", "dirty", "errors", "required", "max<PERSON><PERSON><PERSON>", "pattern", "bind"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\report-receiving-group\\detail\\app.group-receiving.detail.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\report-receiving-group\\detail\\app.group-receiving.detail.component.html"], "sourcesContent": ["import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';\r\nimport {FormBuilder} from \"@angular/forms\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport { ComponentBase } from 'src/app/component.base';\r\nimport { ReportReceivingGroupService } from 'src/app/service/report-receiving-group/ReportReceivingGroup';\r\n\r\n@Component({\r\n  selector: 'report.group-receiving.detail',\r\n  templateUrl: './app.group-receiving.detail.component.html',\r\n})\r\nexport class ReportGroupReceivingDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n    constructor(@Inject(ReportReceivingGroupService) private reportReceivingGroupService: ReportReceivingGroupService,\r\n                private formBuilder: FormBuilder, injector: Injector) {\r\n                    super(injector)\r\n    }\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    formReceivingGroup : any;\r\n    formMailInput : any;\r\n\r\n    receivingGroupInfo: {\r\n        name: string|null,\r\n        description: string|null,\r\n        emails: Array<any>|null,\r\n    };\r\n    selectItems: Array<any> = [];\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTable: OptionTable;\r\n    email: {}\r\n    rgId = parseInt(this.route.snapshot.paramMap.get(\"id\"));\r\n\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.messageCommonService.onload()\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.dynamicreportgroup\")},{ label: this.tranService.translate(\"global.menu.reportGroupReceivingList\"), routerLink:\"/reports/group-report-dynamic\"  }, { label: this.tranService.translate(\"global.button.view\") }];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        this.receivingGroupInfo = {\r\n            name: \"nhom1\",\r\n            description: null,\r\n            emails: [],\r\n        }\r\n        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);\r\n        this.formMailInput = this.formBuilder.group({email: \"\"});\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        this.selectItems = [];\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"report.receiving.emails\"),\r\n                key: \"emails\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ];\r\n        this.optionTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        };\r\n\r\n        this.formReceivingGroup.get('name').disable()\r\n        this.formReceivingGroup.get('description').disable()\r\n        this.messageCommonService.onload()\r\n        this.reportReceivingGroupService.getDetailReceivingGroup(this.rgId,(response)=>{\r\n            me.receivingGroupInfo = response;\r\n            me.receivingGroupInfo.emails = response.emails\r\n            \r\n            if (response.emails != null){\r\n                for (let i = 0; i <response.emails.split(\", \").length; i++) {\r\n                    me.dataSet.content.push({emails :response.emails.split(\", \")[i]})\r\n                    // me.myEmails.push(response.emails.split(\", \")[i])\r\n                }\r\n            }\r\n            \r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n    }\r\n    ngAfterContentChecked(): void {\r\n    }\r\n    // onSubmitCreate(){\r\n    //     let dataBody = {\r\n    //         // username: this.accountInfo.accountName,\r\n\r\n    //     }\r\n    //     this.messageCommonService.onload();\r\n    //     let me = this;\r\n    //     this.reportReceivingGroup.createAccount(dataBody, (response)=>{\r\n    //         me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n    //         // me.router.navigate(['/accounts/edit/'+response.id]);\r\n    //     })\r\n    // }\r\n    closeForm(){\r\n        this.router.navigate(['/reports/group-report-dynamic'])\r\n    }\r\n\r\n    addEmail(val){\r\n        let me = this;\r\n\r\n\r\n        me.dataSet.content.push({emails :val})\r\n        me.receivingGroupInfo.emails.push({emails :val})\r\n        // me.dataSet.content.push(me.receivingGroupInfo)\r\n    }\r\n    search(){\r\n        let me = this\r\n        me.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n    }\r\n    removeEmail(val){\r\n        let me = this\r\n        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)\r\n        me.receivingGroupInfo.emails.splice(me.receivingGroupInfo.emails.indexOf(val), 1)\r\n    }\r\n\r\n    deleteReceivingGroup(){\r\n        let me = this;\r\n        me.messageCommonService.confirm(\r\n            me.tranService.translate(\"global.message.titleConfirmDeleteReportReceivingGroup\"),\r\n            me.tranService.translate(\"global.message.confirmDeleteReportReceivingGroup\"),\r\n            {\r\n                ok:()=>{\r\n                    me.reportReceivingGroupService.deleteReportGroup(me.rgId, (response) => {\r\n                        me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\r\n                        me.router.navigate(['/reports/group-report-dynamic'])\r\n                    })\r\n                },\r\n                cancel: ()=>{\r\n                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                }\r\n            }\r\n        )\r\n    }\r\n\r\n    onEdit(){\r\n        let me = this;\r\n        let receivingGroupId = this.route.snapshot.paramMap.get(\"id\");\r\n        me.router.navigate([`/reports/group-report-dynamic/edit/${receivingGroupId}`]);\r\n    }\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.reportGroupReceivingList\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <!--        <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.create')\" icon=\"\" [routerLink]=\"['/alert/create']\" routerLinkActive=\"router-link-active\" ></p-button>-->\r\n        <div class=\"flex flex-row justify-content-center gap-3 p-2\">\r\n            <button pButton type=\"submit\" class=\"p-button-success\" style=\"\"  [label]=\"tranService.translate('global.button.edit')\" icon=\"\"  (click)=\"onEdit()\"></button>\r\n            <button pButton class=\"p-button-danger\" type=\"submit\" style=\"\" [label]=\"tranService.translate('global.button.delete')\" icon=\"pi pi-trash\"  (click)=\"deleteReceivingGroup()\"></button>\r\n            <!-- <button pButton class=\"p-button-help\" type=\"button\" style=\"\" (click)=\"closeForm()\">Huỷ</button> -->\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<p-card class=\"p-4\">\r\n    <form action=\"\" [formGroup]=\"formReceivingGroup\">\r\n        <div class=\"pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid\">\r\n            <div class=\"col-8\">\r\n                <div class=\"w-full field grid\">\r\n                    <!--  name -->\r\n                    <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:250px\">{{tranService.translate(\"report.receiving.name\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"name\"\r\n                               [(ngModel)]=\"receivingGroupInfo.name\"\r\n                               formControlName=\"name\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"50\"\r\n                               pattern=\"^[a-zA-Z0-9\\-_]*$\"\r\n                               [placeholder]=\"tranService.translate('report.text.inputNameReceiving')\"\r\n                        />\r\n                        <!-- error name -->\r\n                        <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formReceivingGroup.controls.name.dirty && formReceivingGroup.controls.name.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formReceivingGroup.controls.name.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:50})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formReceivingGroup.controls.name.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                            <!--                        <small class=\"text-red-500\" *ngIf=\"isUsernameExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.username\")})}}</small>-->\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"w-full field grid\">\r\n                    <!--  description -->\r\n                    <label for=\"description\" class=\"col-fixed\" style=\"width:250px\">{{tranService.translate(\"report.receiving.description\")}}</label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"description\"\r\n                               [(ngModel)]=\"receivingGroupInfo.description\"\r\n                               formControlName=\"description\"\r\n                               [maxLength]=\"50\"\r\n                               [placeholder]=\"tranService.translate('report.text.inputDescription')\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <h4 class=\"ml-2\"></h4>\r\n        <div class=\"pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid\">\r\n            <div class=\"flex-1\">\r\n                <div class=\"field  px-4 pt-4  flex-row \">\r\n                    <!-- email -->\r\n                    <div class=\"field  px-4 pt-4  flex-row \">\r\n                        <table-vnpt\r\n                            [fieldId]=\"'id'\"\r\n                            [(selectItems)]=\"selectItems\"\r\n                            [columns]=\"columns\"\r\n                            [dataSet]=\"dataSet\"\r\n                            [options]=\"optionTable\"\r\n                            [loadData]=\"search.bind(this)\"\r\n                            [scrollHeight]=\"'200px'\"\r\n                        ></table-vnpt>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </form>\r\n</p-card>\r\n"], "mappings": "AAIA,SAASA,aAAa,QAAQ,wBAAwB;AACtD,SAASC,2BAA2B,QAAQ,6DAA6D;;;;;;;;;;;;;IC8B7EC,EAAA,CAAAC,cAAA,gBAAgI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACpLR,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAtEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA8D;;;;;IACrJX,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,8BAAsD;;;AD1BvK,OAAM,MAAOK,mCAAoC,SAAQf,aAAa;EAClEgB,YAAyDC,2BAAwD,EAC7FC,WAAwB,EAAEC,QAAkB;IAChD,KAAK,CAACA,QAAQ,CAAC;IAF0B,KAAAF,2BAA2B,GAA3BA,2BAA2B;IAChE,KAAAC,WAAW,GAAXA,WAAW;IAa/B,KAAAE,WAAW,GAAe,EAAE;IAQ5B,KAAAC,IAAI,GAAGC,QAAQ,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;EAnBvD;EAqBAC,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACC,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,gCAAgC;IAAC,CAAC,EAAC;MAAEsB,KAAK,EAAE,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,sCAAsC,CAAC;MAAEuB,UAAU,EAAC;IAA+B,CAAG,EAAE;MAAED,KAAK,EAAE,IAAI,CAACvB,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAE,CAAC;IAC7Q,IAAI,CAACwB,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAI,CAACG,kBAAkB,GAAG;MACtBC,IAAI,EAAE,OAAO;MACbC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE;KACX;IACD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACtB,WAAW,CAACuB,KAAK,CAAC,IAAI,CAACL,kBAAkB,CAAC;IACzE,IAAI,CAACM,aAAa,GAAG,IAAI,CAACxB,WAAW,CAACuB,KAAK,CAAC;MAACE,KAAK,EAAE;IAAE,CAAC,CAAC;IACxD,IAAI,CAACC,OAAO,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC1B,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC2B,OAAO,GAAG,CACX;MACIV,IAAI,EAAE,IAAI,CAAC5B,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3DsC,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACC,WAAW,GAAG;MACfC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IAED,IAAI,CAACjB,kBAAkB,CAACd,GAAG,CAAC,MAAM,CAAC,CAACgC,OAAO,EAAE;IAC7C,IAAI,CAAClB,kBAAkB,CAACd,GAAG,CAAC,aAAa,CAAC,CAACgC,OAAO,EAAE;IACpD,IAAI,CAAC7B,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACb,2BAA2B,CAAC0C,uBAAuB,CAAC,IAAI,CAACtC,IAAI,EAAEuC,QAAQ,IAAG;MAC3EhC,EAAE,CAACQ,kBAAkB,GAAGwB,QAAQ;MAChChC,EAAE,CAACQ,kBAAkB,CAACG,MAAM,GAAGqB,QAAQ,CAACrB,MAAM;MAE9C,IAAIqB,QAAQ,CAACrB,MAAM,IAAI,IAAI,EAAC;QACxB,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAED,QAAQ,CAACrB,MAAM,CAACuB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UACxDjC,EAAE,CAACgB,OAAO,CAACC,OAAO,CAACmB,IAAI,CAAC;YAACzB,MAAM,EAAEqB,QAAQ,CAACrB,MAAM,CAACuB,KAAK,CAAC,IAAI,CAAC,CAACD,CAAC;UAAC,CAAC,CAAC;UACjE;;;IAIZ,CAAC,EAAE,IAAI,EAAE,MAAI;MACTjC,EAAE,CAACC,oBAAoB,CAACoC,OAAO,EAAE;IACrC,CAAC,CAAC;IACF,IAAI,CAACrB,OAAO,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;EACL;EACAoB,qBAAqBA,CAAA,GACrB;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,SAASA,CAAA;IACL,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC;EAC3D;EAEAC,QAAQA,CAACC,GAAG;IACR,IAAI3C,EAAE,GAAG,IAAI;IAGbA,EAAE,CAACgB,OAAO,CAACC,OAAO,CAACmB,IAAI,CAAC;MAACzB,MAAM,EAAEgC;IAAG,CAAC,CAAC;IACtC3C,EAAE,CAACQ,kBAAkB,CAACG,MAAM,CAACyB,IAAI,CAAC;MAACzB,MAAM,EAAEgC;IAAG,CAAC,CAAC;IAChD;EACJ;;EACAC,MAAMA,CAAA;IACF,IAAI5C,EAAE,GAAG,IAAI;IACbA,EAAE,CAACgB,OAAO,GAAG;MACTC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;EACL;EACA2B,WAAWA,CAACF,GAAG;IACX,IAAI3C,EAAE,GAAG,IAAI;IACbA,EAAE,CAACgB,OAAO,CAACC,OAAO,CAAC6B,MAAM,CAAC9C,EAAE,CAACgB,OAAO,CAACC,OAAO,CAAC8B,OAAO,CAACJ,GAAG,CAAC,EAAE,CAAC,CAAC;IAC7D3C,EAAE,CAACQ,kBAAkB,CAACG,MAAM,CAACmC,MAAM,CAAC9C,EAAE,CAACQ,kBAAkB,CAACG,MAAM,CAACoC,OAAO,CAACJ,GAAG,CAAC,EAAE,CAAC,CAAC;EACrF;EAEAK,oBAAoBA,CAAA;IAChB,IAAIhD,EAAE,GAAG,IAAI;IACbA,EAAE,CAACC,oBAAoB,CAACgD,OAAO,CAC3BjD,EAAE,CAACnB,WAAW,CAACC,SAAS,CAAC,uDAAuD,CAAC,EACjFkB,EAAE,CAACnB,WAAW,CAACC,SAAS,CAAC,kDAAkD,CAAC,EAC5E;MACIoE,EAAE,EAACA,CAAA,KAAI;QACHlD,EAAE,CAACX,2BAA2B,CAAC8D,iBAAiB,CAACnD,EAAE,CAACP,IAAI,EAAGuC,QAAQ,IAAI;UACnEhC,EAAE,CAACC,oBAAoB,CAACmD,OAAO,CAACpD,EAAE,CAACnB,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;UACzFkB,EAAE,CAACwC,MAAM,CAACC,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC;QACzD,CAAC,CAAC;MACN,CAAC;MACDY,MAAM,EAAEA,CAAA,KAAI;QACR;MAAA;KAEP,CACJ;EACL;EAEAC,MAAMA,CAAA;IACF,IAAItD,EAAE,GAAG,IAAI;IACb,IAAIuD,gBAAgB,GAAG,IAAI,CAAC5D,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAC7DE,EAAE,CAACwC,MAAM,CAACC,QAAQ,CAAC,CAAC,sCAAsCc,gBAAgB,EAAE,CAAC,CAAC;EAClF;;;uBAhJSpE,mCAAmC,EAAAb,EAAA,CAAAkF,iBAAA,CACxBnF,2BAA2B,GAAAC,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAAqF,QAAA;IAAA;EAAA;;;YADtCxE,mCAAmC;MAAAyE,SAAA;MAAAC,QAAA,GAAAvF,EAAA,CAAAwF,0BAAA;MAAAC,KAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXhDhG,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAAiE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3GH,EAAA,CAAAkG,SAAA,sBAAoF;UACxFlG,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAwE;UAGgED,EAAA,CAAAmG,UAAA,mBAAAC,qEAAA;YAAA,OAASH,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAAChF,EAAA,CAAAG,YAAA,EAAS;UAC5JH,EAAA,CAAAC,cAAA,gBAA4K;UAAjCD,EAAA,CAAAmG,UAAA,mBAAAE,qEAAA;YAAA,OAASJ,GAAA,CAAAvB,oBAAA,EAAsB;UAAA,EAAC;UAAC1E,EAAA,CAAAG,YAAA,EAAS;UAMjMH,EAAA,CAAAC,cAAA,gBAAoB;UAM4DD,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjJH,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAmG,UAAA,2BAAAG,6EAAAC,MAAA;YAAA,OAAAN,GAAA,CAAA/D,kBAAA,CAAAC,IAAA,GAAAoE,MAAA;UAAA,EAAqC;UAF5CvG,EAAA,CAAAG,YAAA,EAQE;UAEFH,EAAA,CAAAkG,SAAA,iBAAoE;UACpElG,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwG,UAAA,KAAAC,qDAAA,oBAA4L;UAC5LzG,EAAA,CAAAwG,UAAA,KAAAE,qDAAA,oBAA6J;UAC7J1G,EAAA,CAAAwG,UAAA,KAAAG,qDAAA,oBAAmJ;UAEvJ3G,EAAA,CAAAG,YAAA,EAAM;UAGdH,EAAA,CAAAC,cAAA,eAA+B;UAEoCD,EAAA,CAAAE,MAAA,IAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChIH,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAmG,UAAA,2BAAAS,6EAAAL,MAAA;YAAA,OAAAN,GAAA,CAAA/D,kBAAA,CAAAE,WAAA,GAAAmE,MAAA;UAAA,EAA4C;UAFnDvG,EAAA,CAAAG,YAAA,EAME;UAKlBH,EAAA,CAAAkG,SAAA,cAAsB;UACtBlG,EAAA,CAAAC,cAAA,eAA4E;UAOxDD,EAAA,CAAAmG,UAAA,+BAAAU,sFAAAN,MAAA;YAAA,OAAAN,GAAA,CAAA/E,WAAA,GAAAqF,MAAA;UAAA,EAA6B;UAMhCvG,EAAA,CAAAG,YAAA,EAAa;;;UArEMH,EAAA,CAAAI,SAAA,GAAiE;UAAjEJ,EAAA,CAAAK,iBAAA,CAAA4F,GAAA,CAAA1F,WAAA,CAAAC,SAAA,yCAAiE;UAC9DR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA8G,UAAA,UAAAb,GAAA,CAAApE,KAAA,CAAe,SAAAoE,GAAA,CAAAjE,IAAA;UAKehC,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAA8G,UAAA,UAAAb,GAAA,CAAA1F,WAAA,CAAAC,SAAA,uBAAqD;UACvDR,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAA8G,UAAA,UAAAb,GAAA,CAAA1F,WAAA,CAAAC,SAAA,yBAAuD;UAO9GR,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAA8G,UAAA,cAAAb,GAAA,CAAA3D,kBAAA,CAAgC;UAK4BtC,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAK,iBAAA,CAAA4F,GAAA,CAAA1F,WAAA,CAAAC,SAAA,0BAAkD;UAInGR,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA8G,UAAA,YAAAb,GAAA,CAAA/D,kBAAA,CAAAC,IAAA,CAAqC,mDAAA8D,GAAA,CAAA1F,WAAA,CAAAC,SAAA;UAUXR,EAAA,CAAAI,SAAA,GAAiG;UAAjGJ,EAAA,CAAA8G,UAAA,SAAAb,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA6E,KAAA,KAAAf,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA8E,MAAA,kBAAAhB,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA8E,MAAA,CAAAC,QAAA,EAAiG;UACjGlH,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAA8G,UAAA,SAAAb,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA8E,MAAA,kBAAAhB,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA8E,MAAA,CAAAE,SAAA,CAAwD;UACxDnH,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAA8G,UAAA,SAAAb,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA8E,MAAA,kBAAAhB,GAAA,CAAA3D,kBAAA,CAAAyE,QAAA,CAAA5E,IAAA,CAAA8E,MAAA,CAAAG,OAAA,CAAsD;UAO5BpH,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAK,iBAAA,CAAA4F,GAAA,CAAA1F,WAAA,CAAAC,SAAA,iCAAyD;UAI7GR,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAA8G,UAAA,YAAAb,GAAA,CAAA/D,kBAAA,CAAAE,WAAA,CAA4C,iCAAA6D,GAAA,CAAA1F,WAAA,CAAAC,SAAA;UAgB/CR,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAA8G,UAAA,iBAAgB,gBAAAb,GAAA,CAAA/E,WAAA,aAAA+E,GAAA,CAAApD,OAAA,aAAAoD,GAAA,CAAAvD,OAAA,aAAAuD,GAAA,CAAA9C,WAAA,cAAA8C,GAAA,CAAA3B,MAAA,CAAA+C,IAAA,CAAApB,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}