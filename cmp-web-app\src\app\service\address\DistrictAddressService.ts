import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class DistrictAddressService {
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/address";
    }
    public search(params: any, callback: Function, errorCallBack?: Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/district`, {}, params,callback, errorCallBack, finallyCallback);
    }
}
