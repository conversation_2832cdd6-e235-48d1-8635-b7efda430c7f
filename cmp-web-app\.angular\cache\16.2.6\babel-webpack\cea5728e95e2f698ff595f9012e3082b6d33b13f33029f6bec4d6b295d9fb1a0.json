{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport DataPage from \"src/app/service/data.page\";\nimport { ShareDataComponent } from \"./share-mgmt/share-data/share-data.component\";\nimport { ShareDataDetailComponent } from \"./share-mgmt/share-data-detail/share-data-detail.component\";\nimport { DataPoolListComponent } from \"./trafficWallet/list/data-pool.list.component\";\nimport { WalletConfigComponent } from \"./wallet-config/wallet-config.component\";\nimport { ListShareComponent } from \"./share-mgmt/list-share/list-share.component\";\nimport { HistoryWalletListComponent } from \"./history-wallet/history-wallet.list.component\";\nimport { DataPoolDetailComponent } from \"./trafficWallet/detail/data-pool.detail.component\";\nimport { ShareWalletComponent } from \"./trafficWallet/share-wallet/share-wallet.component\";\nimport { GroupSubWalletListComponent } from \"./group-sub/list/group-sub-wallet.list.component\";\nimport { CONSTANTS } from \"../../service/comon/constants\";\nimport { GroupSubWalletCreateComponent } from \"./group-sub/create/group-sub-wallet.create.component\";\nimport { GroupSubWalletEditComponent } from \"./group-sub/edit/group-sub-wallet.edit.component\";\nimport { AutoShareWalletDetailComponent } from \"./auto-share-wallet/detail/auto-share-wallet.detail.component\";\nimport { AutoShareGroupListComponent } from \"./auto-share-group/list/auto-share-group.list.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DataPoolRoutingModule {\n  static {\n    this.ɵfac = function DataPoolRoutingModule_Factory(t) {\n      return new (t || DataPoolRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DataPoolRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: 'shareMgmt/listShare',\n        component: ListShareComponent,\n        data: new DataPage(\"global.menu.shareList\", [CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE])\n      }, {\n        path: 'shareMgmt/share',\n        component: ShareDataComponent,\n        data: new DataPage(\"datapool.label.shareData\")\n      }, {\n        path: 'shareMgmt/detail/:id',\n        component: ShareDataDetailComponent,\n        data: new DataPage(\"datapool.label.detailSharing\")\n      }, {\n        path: 'walletMgmt/share/:id',\n        component: ShareWalletComponent,\n        data: new DataPage(\"datapool.label.shareData\")\n      }, {\n        path: 'config',\n        component: WalletConfigComponent,\n        data: new DataPage(\"global.menu.walletConfig\")\n      }, {\n        path: 'walletMgmt/list',\n        component: DataPoolListComponent,\n        data: new DataPage(\"global.menu.walletList\", [CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET])\n      }, {\n        path: 'walletMgmt/detail/:id',\n        component: DataPoolDetailComponent,\n        data: new DataPage(\"global.menu.shareList\")\n      }, {\n        path: 'history',\n        component: HistoryWalletListComponent,\n        data: new DataPage(\"global.menu.historyWallet\", [CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET])\n      }, {\n        path: 'group/listGroupSub',\n        component: GroupSubWalletListComponent,\n        data: new DataPage(\"global.menu.listGroupSub\", [CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])\n      }, {\n        path: 'group/create',\n        component: GroupSubWalletCreateComponent,\n        data: new DataPage(\"datapool.label.createGroupShare\", [CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])\n      }, {\n        path: 'group/edit/:id',\n        component: GroupSubWalletEditComponent,\n        data: new DataPage(\"datapool.label.editGroupShare\", [CONSTANTS.PERMISSIONS.SHARE_GROUP.EDIT])\n      }, {\n        path: 'auto-share-group/detail/:id',\n        component: AutoShareWalletDetailComponent,\n        data: new DataPage(\"datapool.label.autoShareWalletDetail\", [CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_DETAIL])\n      }, {\n        path: 'auto-share-group/list',\n        component: AutoShareGroupListComponent,\n        data: new DataPage(\"datapool.label.autoShareGroup\", [CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_LIST])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DataPoolRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DataPage", "ShareDataComponent", "ShareDataDetailComponent", "DataPoolListComponent", "WalletConfigComponent", "ListShareComponent", "HistoryWalletListComponent", "DataPoolDetailComponent", "ShareWalletComponent", "GroupSubWalletListComponent", "CONSTANTS", "GroupSubWalletCreateComponent", "GroupSubWalletEditComponent", "AutoShareWalletDetailComponent", "AutoShareGroupListComponent", "DataPoolRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "DATAPOOL", "VIEW_SHARE", "VIEW_WALLET", "VIEW_HISTORY_WALLET", "SHARE_GROUP", "VIEW_LIST", "CREATE", "EDIT", "AUTO_SHARE_GROUP", "VIEW_DETAIL", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\data-pool.routing.module.ts"], "sourcesContent": ["import {NgModule} from \"@angular/core\";\r\nimport {RouterModule} from \"@angular/router\";\r\nimport DataPage from \"src/app/service/data.page\";\r\nimport { ShareDataComponent } from \"./share-mgmt/share-data/share-data.component\";\r\nimport { ShareDataDetailComponent } from \"./share-mgmt/share-data-detail/share-data-detail.component\";\r\nimport {DataPoolListComponent} from \"./trafficWallet/list/data-pool.list.component\";\r\nimport { WalletConfigComponent } from \"./wallet-config/wallet-config.component\";\r\nimport { ListShareComponent } from \"./share-mgmt/list-share/list-share.component\";\r\nimport {HistoryWalletListComponent} from \"./history-wallet/history-wallet.list.component\";\r\nimport { DataPoolDetailComponent } from \"./trafficWallet/detail/data-pool.detail.component\";\r\nimport { ShareWalletComponent } from \"./trafficWallet/share-wallet/share-wallet.component\";\r\nimport {GroupSubWalletListComponent} from \"./group-sub/list/group-sub-wallet.list.component\";\r\nimport {CONSTANTS} from \"../../service/comon/constants\";\r\nimport {GroupSubWalletCreateComponent} from \"./group-sub/create/group-sub-wallet.create.component\";\r\nimport {GroupSubWalletEditComponent} from \"./group-sub/edit/group-sub-wallet.edit.component\";\r\nimport {AutoShareWalletDetailComponent} from \"./auto-share-wallet/detail/auto-share-wallet.detail.component\";\r\nimport {AutoShareGroupListComponent} from \"./auto-share-group/list/auto-share-group.list.component\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            { path: 'shareMgmt/listShare', component: ListShareComponent , data: new DataPage(\"global.menu.shareList\", [CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE]) },\r\n            { path: 'shareMgmt/share', component: ShareDataComponent , data: new DataPage(\"datapool.label.shareData\") },\r\n            { path: 'shareMgmt/detail/:id', component: ShareDataDetailComponent , data: new DataPage(\"datapool.label.detailSharing\") },\r\n            { path: 'walletMgmt/share/:id', component: ShareWalletComponent , data: new DataPage(\"datapool.label.shareData\")},\r\n            { path: 'config', component: WalletConfigComponent , data: new DataPage(\"global.menu.walletConfig\")},\r\n            { path: 'walletMgmt/list', component: DataPoolListComponent , data: new DataPage(\"global.menu.walletList\",[CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET])},\r\n            { path: 'walletMgmt/detail/:id', component: DataPoolDetailComponent , data: new DataPage(\"global.menu.shareList\")},\r\n            { path: 'history', component: HistoryWalletListComponent , data: new DataPage(\"global.menu.historyWallet\",[CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET])},\r\n            { path: 'group/listGroupSub', component: GroupSubWalletListComponent , data: new DataPage(\"global.menu.listGroupSub\", [CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])},\r\n            { path: 'group/create', component: GroupSubWalletCreateComponent, data: new DataPage(\"datapool.label.createGroupShare\", [CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])},\r\n            { path: 'group/edit/:id', component: GroupSubWalletEditComponent, data: new DataPage(\"datapool.label.editGroupShare\", [CONSTANTS.PERMISSIONS.SHARE_GROUP.EDIT])},\r\n            { path: 'auto-share-group/detail/:id', component: AutoShareWalletDetailComponent, data: new DataPage(\"datapool.label.autoShareWalletDetail\", [CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_DETAIL])},\r\n            {path: 'auto-share-group/list', component: AutoShareGroupListComponent, data: new DataPage(\"datapool.label.autoShareGroup\", [CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_LIST])},\r\n        ]),\r\n    ],\r\n    exports: [RouterModule],\r\n})\r\n\r\nexport class DataPoolRoutingModule {}\r\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,iBAAiB;AAC5C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,wBAAwB,QAAQ,4DAA4D;AACrG,SAAQC,qBAAqB,QAAO,+CAA+C;AACnF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAAQC,0BAA0B,QAAO,gDAAgD;AACzF,SAASC,uBAAuB,QAAQ,mDAAmD;AAC3F,SAASC,oBAAoB,QAAQ,qDAAqD;AAC1F,SAAQC,2BAA2B,QAAO,kDAAkD;AAC5F,SAAQC,SAAS,QAAO,+BAA+B;AACvD,SAAQC,6BAA6B,QAAO,sDAAsD;AAClG,SAAQC,2BAA2B,QAAO,kDAAkD;AAC5F,SAAQC,8BAA8B,QAAO,+DAA+D;AAC5G,SAAQC,2BAA2B,QAAO,yDAAyD;;;AAuBnG,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAnB1BhB,YAAY,CAACiB,QAAQ,CAAC,CAClB;QAAEC,IAAI,EAAE,qBAAqB;QAAEC,SAAS,EAAEb,kBAAkB;QAAGc,IAAI,EAAE,IAAInB,QAAQ,CAAC,uBAAuB,EAAE,CAACU,SAAS,CAACU,WAAW,CAACC,QAAQ,CAACC,UAAU,CAAC;MAAC,CAAE,EACzJ;QAAEL,IAAI,EAAE,iBAAiB;QAAEC,SAAS,EAAEjB,kBAAkB;QAAGkB,IAAI,EAAE,IAAInB,QAAQ,CAAC,0BAA0B;MAAC,CAAE,EAC3G;QAAEiB,IAAI,EAAE,sBAAsB;QAAEC,SAAS,EAAEhB,wBAAwB;QAAGiB,IAAI,EAAE,IAAInB,QAAQ,CAAC,8BAA8B;MAAC,CAAE,EAC1H;QAAEiB,IAAI,EAAE,sBAAsB;QAAEC,SAAS,EAAEV,oBAAoB;QAAGW,IAAI,EAAE,IAAInB,QAAQ,CAAC,0BAA0B;MAAC,CAAC,EACjH;QAAEiB,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAEd,qBAAqB;QAAGe,IAAI,EAAE,IAAInB,QAAQ,CAAC,0BAA0B;MAAC,CAAC,EACpG;QAAEiB,IAAI,EAAE,iBAAiB;QAAEC,SAAS,EAAEf,qBAAqB;QAAGgB,IAAI,EAAE,IAAInB,QAAQ,CAAC,wBAAwB,EAAC,CAACU,SAAS,CAACU,WAAW,CAACC,QAAQ,CAACE,WAAW,CAAC;MAAC,CAAC,EACxJ;QAAEN,IAAI,EAAE,uBAAuB;QAAEC,SAAS,EAAEX,uBAAuB;QAAGY,IAAI,EAAE,IAAInB,QAAQ,CAAC,uBAAuB;MAAC,CAAC,EAClH;QAAEiB,IAAI,EAAE,SAAS;QAAEC,SAAS,EAAEZ,0BAA0B;QAAGa,IAAI,EAAE,IAAInB,QAAQ,CAAC,2BAA2B,EAAC,CAACU,SAAS,CAACU,WAAW,CAACC,QAAQ,CAACG,mBAAmB,CAAC;MAAC,CAAC,EAChK;QAAEP,IAAI,EAAE,oBAAoB;QAAEC,SAAS,EAAET,2BAA2B;QAAGU,IAAI,EAAE,IAAInB,QAAQ,CAAC,0BAA0B,EAAE,CAACU,SAAS,CAACU,WAAW,CAACK,WAAW,CAACC,SAAS,CAAC;MAAC,CAAC,EACrK;QAAET,IAAI,EAAE,cAAc;QAAEC,SAAS,EAAEP,6BAA6B;QAAEQ,IAAI,EAAE,IAAInB,QAAQ,CAAC,iCAAiC,EAAE,CAACU,SAAS,CAACU,WAAW,CAACK,WAAW,CAACE,MAAM,CAAC;MAAC,CAAC,EACpK;QAAEV,IAAI,EAAE,gBAAgB;QAAEC,SAAS,EAAEN,2BAA2B;QAAEO,IAAI,EAAE,IAAInB,QAAQ,CAAC,+BAA+B,EAAE,CAACU,SAAS,CAACU,WAAW,CAACK,WAAW,CAACG,IAAI,CAAC;MAAC,CAAC,EAChK;QAAEX,IAAI,EAAE,6BAA6B;QAAEC,SAAS,EAAEL,8BAA8B;QAAEM,IAAI,EAAE,IAAInB,QAAQ,CAAC,sCAAsC,EAAE,CAACU,SAAS,CAACU,WAAW,CAACS,gBAAgB,CAACC,WAAW,CAAC;MAAC,CAAC,EACnM;QAACb,IAAI,EAAE,uBAAuB;QAAEC,SAAS,EAAEJ,2BAA2B;QAAEK,IAAI,EAAE,IAAInB,QAAQ,CAAC,+BAA+B,EAAE,CAACU,SAAS,CAACU,WAAW,CAACS,gBAAgB,CAACH,SAAS,CAAC;MAAC,CAAC,CACnL,CAAC,EAEI3B,YAAY;IAAA;EAAA;;;2EAGbgB,qBAAqB;IAAAgB,OAAA,GAAAC,EAAA,CAAAjC,YAAA;IAAAkC,OAAA,GAHpBlC,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}