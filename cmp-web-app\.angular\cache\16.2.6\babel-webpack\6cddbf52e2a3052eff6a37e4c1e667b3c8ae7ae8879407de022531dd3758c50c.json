{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CustomerManagementRoutingModule } from './customer-management-routing.module';\nimport { ListCustomerComponent } from './list-customer/list-customer.component';\nimport { DetailCustomerComponent } from './detail-customer/detail-customer.component';\nimport { UpdateCustomerComponent } from './update-customer/update-customer.component';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FieldsetModule } from 'primeng/fieldset';\nimport { ButtonModule } from 'primeng/button';\nimport { TableModule } from 'primeng/table';\nimport { CommonVnptModule } from '../common-module/common.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { PanelModule } from 'primeng/panel';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DialogModule } from 'primeng/dialog';\nimport { CustomerService } from 'src/app/service/customer/CustomerService';\nimport { AccountService } from 'src/app/service/account/AccountService';\nimport * as i0 from \"@angular/core\";\nexport class CustomerManagementModule {\n  static {\n    this.ɵfac = function CustomerManagementModule_Factory(t) {\n      return new (t || CustomerManagementModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerManagementModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CustomerService, AccountService],\n      imports: [CommonModule, CustomerManagementRoutingModule, BreadcrumbModule, FieldsetModule, ButtonModule, TableModule, FormsModule, CommonVnptModule, ReactiveFormsModule, InputTextModule, DropdownModule, PanelModule, DialogModule, InputTextareaModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerManagementModule, {\n    declarations: [ListCustomerComponent, DetailCustomerComponent, UpdateCustomerComponent],\n    imports: [CommonModule, CustomerManagementRoutingModule, BreadcrumbModule, FieldsetModule, ButtonModule, TableModule, FormsModule, CommonVnptModule, ReactiveFormsModule, InputTextModule, DropdownModule, PanelModule, DialogModule, InputTextareaModule, CalendarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "CustomerManagementRoutingModule", "ListCustomerComponent", "DetailCustomerComponent", "UpdateCustomerComponent", "BreadcrumbModule", "FieldsetModule", "ButtonModule", "TableModule", "CommonVnptModule", "FormsModule", "ReactiveFormsModule", "InputTextModule", "DropdownModule", "PanelModule", "InputTextareaModule", "CalendarModule", "DialogModule", "CustomerService", "AccountService", "CustomerManagementModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\customer-management\\customer-management.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { CustomerManagementRoutingModule } from './customer-management-routing.module';\r\nimport { ListCustomerComponent } from './list-customer/list-customer.component';\r\nimport { DetailCustomerComponent } from './detail-customer/detail-customer.component';\r\nimport { UpdateCustomerComponent } from './update-customer/update-customer.component';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { FieldsetModule } from 'primeng/fieldset';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { TableModule } from 'primeng/table';\r\nimport { CommonVnptModule } from '../common-module/common.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { CustomerService } from 'src/app/service/customer/CustomerService';\r\nimport { AccountService } from 'src/app/service/account/AccountService';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ListCustomerComponent,\r\n    DetailCustomerComponent,\r\n    UpdateCustomerComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    CustomerManagementRoutingModule,\r\n    BreadcrumbModule,\r\n    FieldsetModule,\r\n    ButtonModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CommonVnptModule,\r\n    ReactiveFormsModule,\r\n    InputTextModule,\r\n    DropdownModule,\r\n    PanelModule,\r\n    DialogModule,\r\n    InputTextareaModule,\r\n    CalendarModule \r\n  ],\r\n  providers: [CustomerService, AccountService]\r\n})\r\nexport class CustomerManagementModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,+BAA+B,QAAQ,sCAAsC;AACtF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,cAAc,QAAQ,wCAAwC;;AA2BvE,OAAM,MAAOC,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;iBAFxB,CAACF,eAAe,EAAEC,cAAc,CAAC;MAAAE,OAAA,GAhB1CrB,YAAY,EACZC,+BAA+B,EAC/BI,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,WAAW,EACXE,WAAW,EACXD,gBAAgB,EAChBE,mBAAmB,EACnBC,eAAe,EACfC,cAAc,EACdC,WAAW,EACXG,YAAY,EACZF,mBAAmB,EACnBC,cAAc;IAAA;EAAA;;;2EAILI,wBAAwB;IAAAE,YAAA,GAvBjCpB,qBAAqB,EACrBC,uBAAuB,EACvBC,uBAAuB;IAAAiB,OAAA,GAGvBrB,YAAY,EACZC,+BAA+B,EAC/BI,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,WAAW,EACXE,WAAW,EACXD,gBAAgB,EAChBE,mBAAmB,EACnBC,eAAe,EACfC,cAAc,EACdC,WAAW,EACXG,YAAY,EACZF,mBAAmB,EACnBC,cAAc;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}