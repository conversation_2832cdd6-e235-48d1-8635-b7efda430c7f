{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"primeng/tabview\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"./personal-data-protection-policy/app.personal.data.protection.policy.content.component\";\nexport class TermPolicyListComponent extends ComponentBase {\n  constructor(formBuilder, injector) {\n    super(injector);\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.activeIndex = 0;\n  }\n  ngOnInit() {\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.accountmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.termpolicy\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.activeIndex = parseInt(this.route.snapshot.queryParams[\"index\"] || 0);\n  }\n  static {\n    this.ɵfac = function TermPolicyListComponent_Factory(t) {\n      return new (t || TermPolicyListComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TermPolicyListComponent,\n      selectors: [[\"term-policy-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 10,\n      vars: 5,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [1, \"bg-white\", \"p-2\", \"border-round\", \"mt-2\"], [3, \"scrollable\", \"activeIndex\", \"activeIndexChange\"], [\"header\", \"Ch\\u00EDnh s\\u00E1ch b\\u1EA3o v\\u1EC7 d\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n - N\\u0110 13\"]],\n      template: function TermPolicyListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"p-tabView\", 6);\n          i0.ɵɵlistener(\"activeIndexChange\", function TermPolicyListComponent_Template_p_tabView_activeIndexChange_7_listener($event) {\n            return ctx.activeIndex = $event;\n          });\n          i0.ɵɵelementStart(8, \"p-tabPanel\", 7);\n          i0.ɵɵelement(9, \"personal-data-protection-policy-content\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.termpolicy\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true)(\"activeIndex\", ctx.activeIndex);\n        }\n      },\n      dependencies: [i2.TabView, i2.TabPanel, i3.Breadcrumb, i4.PersonalDataProtectionContentComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "TermPolicyListComponent", "constructor", "formBuilder", "injector", "activeIndex", "ngOnInit", "items", "label", "tranService", "translate", "home", "icon", "routerLink", "parseInt", "route", "snapshot", "queryParams", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "TermPolicyListComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "TermPolicyListComponent_Template_p_tabView_activeIndexChange_7_listener", "$event", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\term-policy\\app.term.policy.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\term-policy\\app.term.policy.list.component.html"], "sourcesContent": ["import { Component, Inject, Injector, OnInit } from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\n\r\n@Component({\r\n    selector: \"term-policy-list\",\r\n    templateUrl: './app.term.policy.list.component.html'\r\n})\r\nexport class TermPolicyListComponent extends ComponentBase implements OnInit{\r\n    constructor(\r\n        private formBuilder: FormBuilder,\r\n        private injector: Injector) {\r\n            super(injector);\r\n    }\r\n\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    activeIndex: number = 0;\r\n\r\n    ngOnInit(): void {\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.accountmgmt\") }, { label: this.tranService.translate(\"global.menu.termpolicy\") },];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.activeIndex = parseInt(this.route.snapshot.queryParams[\"index\"] || 0);\r\n    }\r\n}", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.termpolicy\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        \r\n    </div>\r\n</div>\r\n\r\n<div class=\"bg-white p-2 border-round mt-2\">\r\n    <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\">\r\n        <p-tabPanel header=\"Chính sách bảo vệ dữ liệu cá nhân - NĐ 13\">\r\n            <personal-data-protection-policy-content></personal-data-protection-policy-content>\r\n        </p-tabPanel>\r\n    </p-tabView>\r\n</div>"], "mappings": "AAGA,SAASA,aAAa,QAAQ,wBAAwB;;;;;;AAMtD,OAAM,MAAOC,uBAAwB,SAAQD,aAAa;EACtDE,YACYC,WAAwB,EACxBC,QAAkB;IACtB,KAAK,CAACA,QAAQ,CAAC;IAFX,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAMpB,KAAAC,WAAW,GAAW,CAAC;EAJvB;EAMAC,QAAQA,CAAA;IACJ,IAAI,CAACC,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,EAAE;MAAEF,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB;IAAC,CAAE,CAAE;IACjJ,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACR,WAAW,GAAGS,QAAQ,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;EAC9E;;;uBAfShB,uBAAuB,EAAAiB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,QAAA;IAAA;EAAA;;;YAAvBrB,uBAAuB;MAAAsB,SAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTpCb,EAAA,CAAAe,cAAA,aAAqG;UAEzDf,EAAA,CAAAgB,MAAA,GAAmD;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC7FjB,EAAA,CAAAkB,SAAA,sBAAoF;UACxFlB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,SAAA,aAEM;UACVlB,EAAA,CAAAiB,YAAA,EAAM;UAENjB,EAAA,CAAAe,cAAA,aAA4C;UACTf,EAAA,CAAAmB,UAAA,+BAAAC,wEAAAC,MAAA;YAAA,OAAAP,GAAA,CAAA3B,WAAA,GAAAkC,MAAA;UAAA,EAA6B;UACxDrB,EAAA,CAAAe,cAAA,oBAA+D;UAC3Df,EAAA,CAAAkB,SAAA,8CAAmF;UACvFlB,EAAA,CAAAiB,YAAA,EAAa;;;UAZuBjB,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAuB,iBAAA,CAAAT,GAAA,CAAAvB,WAAA,CAAAC,SAAA,2BAAmD;UAChDQ,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAAwB,UAAA,UAAAV,GAAA,CAAAzB,KAAA,CAAe,SAAAyB,GAAA,CAAArB,IAAA;UAQ/CO,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAwB,UAAA,oBAAmB,gBAAAV,GAAA,CAAA3B,WAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}