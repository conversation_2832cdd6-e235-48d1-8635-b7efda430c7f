{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class ReportReceivingGroupService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/report/email-group\";\n  }\n  searchReportReceivingGroup(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  getDetailReceivingGroup(id, callback, errorCallback, finallyCallback) {\n    this.httpService.get(this.prefixApi + \"/\" + id, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  creteReportReceivingGroup(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(this.prefixApi, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  updateReportReceivingGroup(id, body, callback, errorCallback, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/${id}`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  checkName(param, callback) {\n    this.httpService.get(this.prefixApi + \"/checkName\", {}, param, callback);\n  }\n  deleteReportGroup(id, callback) {\n    this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback);\n  }\n  deleleListReceivingGroup(listId, callback) {\n    this.httpService.post(`${this.prefixApi}/delete-many`, {}, listId, {}, callback);\n  }\n  static {\n    this.ɵfac = function ReportReceivingGroupService_Factory(t) {\n      return new (t || ReportReceivingGroupService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ReportReceivingGroupService,\n      factory: ReportReceivingGroupService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "ReportReceivingGroupService", "constructor", "httpService", "prefixApi", "searchReportReceivingGroup", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "getDetailReceivingGroup", "id", "<PERSON><PERSON><PERSON><PERSON>", "creteReportReceivingGroup", "body", "post", "updateReportReceivingGroup", "put", "checkName", "param", "deleteReportGroup", "delete", "deleleListReceivingGroup", "listId", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\report-receiving-group\\ReportReceivingGroup.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class ReportReceivingGroupService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/report/email-group\";\r\n    }\r\n\r\n    public searchReportReceivingGroup(params:any,callback: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/search`,{},params,callback,errorCallBack,finallyCallback);\r\n    }\r\n\r\n    public getDetailReceivingGroup(id:number, callback: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(this.prefixApi+\"/\"+id, {}, {}, callback ,errorCallback, finallyCallback);\r\n    }\r\n\r\n    public creteReportReceivingGroup(body:any, callback:Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(this.prefixApi,{},body,{},callback, errorCallback, finallyCallback);\r\n    }\r\n    \r\n    public updateReportReceivingGroup(id:number, body:any,callback:Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/${id}`,{},body,{},callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public checkName(param,callback){\r\n        this.httpService.get(this.prefixApi+\"/checkName\",{},param,callback)\r\n    }\r\n\r\n    public deleteReportGroup(id, callback){\r\n        this.httpService.delete(`${this.prefixApi}/${id}`,{},{},callback)\r\n    }\r\n\r\n    public deleleListReceivingGroup(listId: Array<number>, callback?: Function) {\r\n        this.httpService.post(`${this.prefixApi}/delete-many`, {}, listId,{}, callback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,2BAA2B;EAEpCC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,qBAAqB;EAC1C;EAEOC,0BAA0BA,CAACC,MAAU,EAACC,QAAkB,EAAEC,aAAwB,EAAEC,eAA0B;IACjH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAC,EAAE,EAACE,MAAM,EAACC,QAAQ,EAACC,aAAa,EAACC,eAAe,CAAC;EACrG;EAEOE,uBAAuBA,CAACC,EAAS,EAAEL,QAAkB,EAAEM,aAAuB,EAAEJ,eAA0B;IAC7G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,GAAG,GAACQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEL,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EACjG;EAEOK,yBAAyBA,CAACC,IAAQ,EAAER,QAAiB,EAAEM,aAAuB,EAAEJ,eAA0B;IAC7G,IAAI,CAACN,WAAW,CAACa,IAAI,CAAC,IAAI,CAACZ,SAAS,EAAC,EAAE,EAACW,IAAI,EAAC,EAAE,EAACR,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EAC7F;EAEOQ,0BAA0BA,CAACL,EAAS,EAAEG,IAAQ,EAACR,QAAiB,EAAEM,aAAuB,EAAEJ,eAA0B;IACxH,IAAI,CAACN,WAAW,CAACe,GAAG,CAAC,GAAG,IAAI,CAACd,SAAS,IAAIQ,EAAE,EAAE,EAAC,EAAE,EAACG,IAAI,EAAC,EAAE,EAACR,QAAQ,EAAEM,aAAa,EAAEJ,eAAe,CAAC;EACvG;EAEOU,SAASA,CAACC,KAAK,EAACb,QAAQ;IAC3B,IAAI,CAACJ,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,YAAY,EAAC,EAAE,EAACgB,KAAK,EAACb,QAAQ,CAAC;EACvE;EAEOc,iBAAiBA,CAACT,EAAE,EAAEL,QAAQ;IACjC,IAAI,CAACJ,WAAW,CAACmB,MAAM,CAAC,GAAG,IAAI,CAAClB,SAAS,IAAIQ,EAAE,EAAE,EAAC,EAAE,EAAC,EAAE,EAACL,QAAQ,CAAC;EACrE;EAEOgB,wBAAwBA,CAACC,MAAqB,EAAEjB,QAAmB;IACtE,IAAI,CAACJ,WAAW,CAACa,IAAI,CAAC,GAAG,IAAI,CAACZ,SAAS,cAAc,EAAE,EAAE,EAAEoB,MAAM,EAAC,EAAE,EAAEjB,QAAQ,CAAC;EACnF;;;uBAhCSN,2BAA2B,EAAAwB,EAAA,CAAAC,QAAA,CAEhB1B,WAAW;IAAA;EAAA;;;aAFtBC,2BAA2B;MAAA0B,OAAA,EAA3B1B,2BAA2B,CAAA2B;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}