{"ast": null, "code": "export default {\n  label: {\n    shareMgmt: \"Share Management\",\n    sharePhoneNumber: \"Shared Phone Number\",\n    shareData: \"Share Data\",\n    fullName: \"Full Name\",\n    email: \"Email\",\n    generalInfomation: \"General Infomation\",\n    dataWallet: \"Data Wallet\",\n    dataWalletPlaceHolder: \"Choose data wallet\",\n    shareDate: \"Share Date\",\n    description: \"Description\",\n    receiverPhone: \"Receiver Phone\",\n    remainData: \"Remaining Data\",\n    purchasedData: \"Purchased Data\",\n    revokeMessage: \"Are you really sure to revoke sharing ?\",\n    phone: \"Phone\",\n    phoneFull: \"Phone\",\n    revokeData: \"Revoke ${data}\",\n    sharingData: \"Sharing Data (${type})\",\n    sharingDataNotType: \"Sharing Data\",\n    percentage: \"Percentage\",\n    shareInfo: \"Share Info\",\n    walletCode: \"Wallet Code\",\n    packageName: \"Subscription\",\n    detailSharing: \"Detail Sharing\",\n    payCode: \"Pay Code\",\n    trafficType: \"Traffic Type\",\n    packageCode: \"Pakage Code\",\n    accuracyDateFrom: \"Accuracy Date From\",\n    accuracyDateTo: \"Accuracy Date To\",\n    usedTimeFrom: \"Used Time From\",\n    usedTimeTo: \"Used Time To\",\n    usedTime: \"Used Time\",\n    accuracyDate: \"Accuracy Date\",\n    tax: \"Tax\",\n    accuracyWallet: \"Accuracy Wallet\",\n    sendType: \"Send Type\",\n    appliedSubscription: \"Applied Subscription\",\n    cycle: \"Cycle\",\n    sendNoticeExpired: \"Send Notice Expired\",\n    sendNoticeExpiredBefore: \"Send Notice Expired Before\",\n    day: \"Day\",\n    noticeFrequency: \"Auto Notice Frequency\",\n    time: \"Times\",\n    sharedPhone: \"Shared Phone\",\n    creator: \"Creator\",\n    otpCode: \"OTP Code\",\n    enterOtpCode: \"Enter OTP code\",\n    authenMethod: \"Authentication Method\",\n    subCode: \"Subscription Code\",\n    stt: \"STT\",\n    created: \"CREATED\",\n    activity: \"ACTIVITY\",\n    content: \"CONTENT\",\n    operator: \"OPERATOR\",\n    sharedTime: \"Shared Time\",\n    usedDate: \"Share usage until\",\n    shared: \"Shared Data\",\n    detailWallet: \"Detail Wallet\",\n    selectWallet: \"Select Wallet\",\n    transactionCode: \"Transaction Code\",\n    groupCode: \"Group Code\",\n    groupName: \"Group Name\",\n    detailGroup: \"Detail Group Sub\",\n    editGroupShare: \"Edit Group Share\",\n    createGroupShare: \"Create Group Share\",\n    shareGroup: \"Share Group\",\n    nameSub: \"Name Subscription\",\n    shareNormal: \"Share Normal\",\n    shareByGroup: \"Share By Group\",\n    generalInfo: \"General Info\",\n    listSubOfGroup: \"List Sub Of Group\",\n    autoShareWalletDetail: \"Group details shared automatically\",\n    renewalStatus: \"Renewal status\",\n    autoShareWallet: \"List of wallet groups shared automatically\",\n    walletList: \"Wallet List\",\n    sharePhoneList: \"Share Phone Number List\",\n    methodAutoShare: \"Automatic sharing method\",\n    notification: \"Notification\",\n    registerByPayCode: \"Register by Payment Code\",\n    registerBySubCode: \"Register by Wallet Code\",\n    autoShareGroup: \"Auto Share Group List\",\n    autoShareGroupDetail: \"Auto Share Wallet Group Details\",\n    lstWallet: \"Wallet List\",\n    lstSub: \"Shared Phone Number List\",\n    dateFrom: \"From Date\",\n    dateTo: \"Up To Date\",\n    timeUpTo: \"Time Up To\",\n    autoSharing: \"Automatic Sharing\",\n    typeShare: \"Type Share\",\n    active: \"Active Type\",\n    listShareError: \"Error Share Phone Number List\",\n    downloadErrorFile: \"Download Error File\",\n    addSharePhone: \"Add sharing recipients\",\n    walletCodeShare: \"Wallet code/ Payment code\",\n    viewPhoneList: \"View Phone List.\",\n    phoneShareList: \"Shared Phone List\",\n    status: \"Status\",\n    msisdnShareSucess: \"Phone number share success\",\n    msisdnShareFail: \"Phone number share fail\"\n  },\n  button: {\n    share: \"Share Data\",\n    equalSharing: \"Equal Sharing\",\n    fixedAllocation: \"Fixed Allocation\",\n    revokeSharing: \"Revoke Sharing\",\n    createWallet: \"Add Wallet\",\n    shareTraffic: \"Share Traffic\",\n    add: \"Add\",\n    delete: \"Delete\",\n    importFile: \"Import Receiver\",\n    shareByGroup: \"ShareBy Group\",\n    deleteSub: \"Delete Sub\",\n    editSub: \"Edit Sub\",\n    addSharePhone: \"Add phone numbers to share automatically\",\n    removeSharePhone: \"Remove phone numbers to share automatically\",\n    dateFrom: \"Usage period ends - From date\",\n    dateTo: \"Usage period ends - Until date\",\n    no: \"NO\",\n    register: \"REGISTER\",\n    yes: \"YES\",\n    addSubToGroupAuto: \"Add auto-shared phone number\",\n    deleteSubInGroupAuto: \"Delete auto-shared phone number\",\n    addShare: \"Add each subscriber\"\n  },\n  text: {\n    tax: \"Enter tax code\",\n    payCode: \"Enter payment code\",\n    subCode: \"Enter subscription code\",\n    importByFile: \"Enter sharing information by file\",\n    importReceive: \"Import recipient file\",\n    learnMore: \"Learn more\",\n    notify: \"Notification\",\n    nonOTPPayCode: \"Will apply OTP-free sharing to all wallets under this payment code\",\n    nonOTPSubCode: \"Will apply OTP-free sharing to this wallet code\",\n    noteAutoShare: \"(When registering for OTP-free sharing, the wallet will be allowed to share automatically)\",\n    noteRegisPayCode: \"(When registering for sharing without OTP, all wallets under this Payment Code will be allowed to share automatically)\",\n    noteCancelSubCode: \"(When canceling OTP-free sharing, the wallet will not be allowed to share automatically. The subscribers who are being shared automatically from this wallet will be canceled)\",\n    noteCancelPayCode: \"(When canceling OTP-free sharing by Payment Code, the wallets under this code will not be shared automatically. The subscribers who are being shared automatically from these wallets will be canceled)\",\n    hasPayCode: \"This wallet has registered for OTP-free sharing by Payment Code '${payCode}'\",\n    hasntAuto: \"This wallet hasn't registered for Auto Sharing\",\n    chooseRegistrationMethod: \"Please choose a registration method\",\n    downloadErrorMessage: \"The list contains invalid fields. Please check the downloaded file for more details.\"\n  },\n  placeholder: {\n    fullName: \"Enter Fullname\",\n    phone: \"Enter Phone\",\n    email: \"Enter Email\",\n    registerShare: \"Register to share without OTP\",\n    cancelShare: \"Cancel sharing without OTP\",\n    activeType: \"Choose Active Type\",\n    typeShare: \"Choose Type Share\"\n  },\n  message: {\n    otp: \"You will receive a message that contains otp code from M2M\",\n    resendOtp: \"Resend OTP\",\n    in: \"in\",\n    sec: \"second\",\n    patternError: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_)\",\n    digitError: \"Phone number must be a number starting with 0 (10-11 characters)\",\n    delete: \"Delete Share Info\",\n    confirmDelete: \"Do you want to delete this share infomation ?\",\n    confirmRevoke: \"Do you want to revoke sharing data ?\",\n    miniumSMS: \"Minium share data is 10 SMS\",\n    miniumData: \"Minium share data is 100 MB\",\n    exceededData: \"Shared data exceeds remaining data\",\n    errorSearch: \"Please select atleast 1 search item\",\n    existed: \"The subscription already exists in the sharing list\",\n    notValidPhone: \"Please choose Vinaphone number\",\n    existedPhone: \"Phone already exists\",\n    otpIncorrect: \"OTP is Incorrect\",\n    dataError: \"Data traffic is multiple of 100 and has minium of 100 MB\",\n    smsError: \"SMS traffic is multiple of 5 and has minium of 5 SMS\",\n    deleteSub: \"Are you sure you want to delete the selected subscription?\",\n    maximumSubAdd: \"Maximum of 50 subscribers can be added only\",\n    maximumSubDisplay: \"Each group can only add a maximum of 3000 subscribers\",\n    duplicateSub: \"Each subscriber is only allowed to belong to 1 group, subscriber ${data} is in group '${groupName}' so cannot be added to this group\",\n    dublicateShareInfo: \"The subscriber number has been added to the list.\",\n    confirmRegisterAutoShareWallet: \"Do you want to register OTP-free sharing for this wallet?\",\n    confirmRegisterAutoSharePayCode: \"Do you want to register for OTP-free sharing for this Payment Code?\",\n    readMore: \"Learn more\",\n    confirmCancelSubCode: \"Do you want to cancel OTP-free sharing for this wallet?\",\n    confirmCancelPayCode: \"Do you want to cancel OTP-free sharing for Payment Code '${payCode}'?\",\n    registerSuccess: \"Registered successfully\",\n    cancelSuccess: \"Cancelled successfully\",\n    invalidDataUsage: \"The minimum data traffic is 100MB and is a multiple of 100\",\n    invalidSmsUsage: \"The minimum data traffic is 100 SMS and is a multiple of 5\",\n    existSharePhone: \"The phone number ${phoneNumber} is already in the group. If you add it multiple times, it will be automatically shared multiple times\",\n    deleteAutoShare: \"Are you sure you want to remove automatic sharing of selected numbers to this wallet group?\",\n    deleteWarnAutoShare: \"If removed from the group, automatic sharing to phone numbers from this wallet group will stop\",\n    existPhoneInTable: \"The phone number ${phoneNumber} is already exist in the table\",\n    exceedTrafficShare: \"Shared traffic for subscribers exceeds ${remainDataWallet} ${typeWallet} remaining in the wallet. Please reduce sharing traffic or delete subscriptions or choose another wallet\",\n    shareNotifySuccess: \"Shared and added to group successfully ${success}/${total} phone number with valid information\",\n    shareNotifyFail: \"Shared and added to group successfully ${success}/${total} phone number with valid information. Invalid information in error list\",\n    addSuccess: \"Add successfully\",\n    shareOK: \"Share successfully\",\n    shareNotifyBackground: \"Request in progress. Processing will take a few minutes due to the large number of shared subscribers (>50). Please check the results later in the Activity History\"\n  },\n  activeType: {\n    buy: \"Buy data\",\n    share: \"Share data\",\n    accuracy: \"Accuracy wallet\",\n    registerNonOTP: \"Register sharing without OTP\",\n    cancelRegisterNonOTP: \"Cancel register without OTP\"\n  },\n  error: {\n    existedGroupCode: \"This shared group code already exists\"\n  },\n  renewStatus: {\n    notDueYet: \"Not due yet\",\n    dueDate: \"Due date\",\n    expired: \"(Expired\"\n  },\n  methodAutoShare: {\n    none: \"None\",\n    subCode: \"Wallet Code\",\n    payCode: \"Pay Code\"\n  },\n  renewalStatus: {\n    notDueYet: \"Chưa đến hạn\",\n    due: \"Đến hạn\",\n    expired: \"Hết hạn\"\n  },\n  typeShare: {\n    manual: \"Manual\",\n    auto: \"Auto\"\n  },\n  activityHistoryStatus: {\n    fail: \"Fail\",\n    success: \"Success\",\n    processing: \"Processing\",\n    completed: \"Completed\"\n  }\n};", "map": {"version": 3, "names": ["label", "shareMgmt", "sharePhoneNumber", "shareData", "fullName", "email", "generalInfomation", "dataWallet", "dataWalletPlaceHolder", "shareDate", "description", "receiverPhone", "remainData", "purchasedData", "revokeMessage", "phone", "phoneFull", "revokeData", "sharingData", "sharingDataNotType", "percentage", "shareInfo", "walletCode", "packageName", "detailSharing", "payCode", "trafficType", "packageCode", "accuracyDateFrom", "accuracyDateTo", "usedTimeFrom", "usedTimeTo", "usedTime", "accuracyDate", "tax", "accuracyWallet", "sendType", "appliedSubscription", "cycle", "sendNoticeExpired", "sendNoticeExpiredBefore", "day", "noticeFrequency", "time", "sharedPhone", "creator", "otpCode", "enterOtpCode", "authen<PERSON><PERSON><PERSON>", "subCode", "stt", "created", "activity", "content", "operator", "sharedTime", "usedDate", "shared", "detailWallet", "selectWallet", "transactionCode", "groupCode", "groupName", "detailGroup", "editGroupShare", "createGroupShare", "shareGroup", "nameSub", "shareNormal", "shareByGroup", "generalInfo", "listSubOfGroup", "autoShareWalletDetail", "renewalStatus", "autoShareWallet", "walletList", "sharePhoneList", "methodAutoShare", "notification", "registerByPayCode", "registerBySubCode", "autoShareGroup", "autoShareGroupDetail", "lstWallet", "lstSub", "dateFrom", "dateTo", "timeUpTo", "autoSharing", "typeShare", "active", "listShareError", "downloadErrorFile", "addSharePhone", "walletCodeShare", "viewPhoneList", "phoneShareList", "status", "msisdnShareSucess", "msisdnShareFail", "button", "share", "equalSharing", "fixedAllocation", "revokeSharing", "createWallet", "shareTraffic", "add", "delete", "importFile", "deleteSub", "editSub", "removeSharePhone", "no", "register", "yes", "addSubToGroupAuto", "deleteSubInGroupAuto", "addShare", "text", "importByFile", "importReceive", "learnMore", "notify", "nonOTPPayCode", "nonOTPSubCode", "noteAutoShare", "noteRegisPayCode", "noteCancelSubCode", "noteCancelPayCode", "hasPayCode", "hasntAuto", "chooseRegistrationMethod", "downloadErrorMessage", "placeholder", "registerShare", "cancelShare", "activeType", "message", "otp", "resendOtp", "in", "sec", "patternError", "digitError", "confirmDelete", "confirmRevoke", "miniumSMS", "miniumData", "exceededData", "errorSearch", "existed", "notValidPhone", "existedPhone", "otpIncorrect", "dataError", "smsError", "maximumSubAdd", "maximumSubDisplay", "duplicateSub", "dublicateShareInfo", "confirmRegisterAutoShareWallet", "confirmRegisterAutoSharePayCode", "readMore", "confirmCancelSubCode", "confirmCancelPayCode", "registerSuccess", "cancelSuccess", "invalidDataUsage", "invalidSmsUsage", "existSharePhone", "deleteAutoShare", "deleteWarnAutoShare", "existPhoneInTable", "exceedTrafficShare", "shareNotifySuccess", "shareNotifyFail", "addSuccess", "shareOK", "shareNotifyBackground", "buy", "accuracy", "registerNonOTP", "cancelRegisterNonOTP", "error", "existedGroupCode", "renewStatus", "notDueYet", "dueDate", "expired", "none", "due", "manual", "auto", "activityHistoryStatus", "fail", "success", "processing", "completed"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\datapool.ts"], "sourcesContent": ["export default{\r\n    label:{\r\n        shareMgmt:\"Share Management\",\r\n        sharePhoneNumber:\"Shared Phone Number\",\r\n        shareData:\"Share Data\",\r\n        fullName:\"Full Name\",\r\n        email:\"Email\",\r\n        generalInfomation:\"General Infomation\",\r\n        dataWallet:\"Data Wallet\",\r\n        dataWalletPlaceHolder:\"Choose data wallet\",\r\n        shareDate:\"Share Date\",\r\n        description:\"Description\",\r\n        receiverPhone:\"Receiver Phone\",\r\n        remainData:\"Remaining Data\",\r\n        purchasedData: \"Purchased Data\",\r\n        revokeMessage:\"Are you really sure to revoke sharing ?\",\r\n        phone:\"Phone\",\r\n        phoneFull:\"Phone\",\r\n        revokeData:\"Revoke ${data}\",\r\n        sharingData:\"Sharing Data (${type})\",\r\n        sharingDataNotType:\"Sharing Data\",\r\n        percentage:\"Percentage\",\r\n        shareInfo:\"Share Info\",\r\n        walletCode: \"Wallet Code\",\r\n        packageName: \"Subscription\",\r\n        detailSharing:\"Detail Sharing\",\r\n        payCode: \"Pay Code\",\r\n        trafficType: \"Traffic Type\",\r\n        packageCode: \"Pakage Code\",\r\n        accuracyDateFrom: \"Accuracy Date From\",\r\n        accuracyDateTo: \"Accuracy Date To\",\r\n        usedTimeFrom: \"Used Time From\",\r\n        usedTimeTo: \"Used Time To\",\r\n        usedTime: \"Used Time\",\r\n        accuracyDate: \"Accuracy Date\",\r\n        tax: \"Tax\",\r\n        accuracyWallet: \"Accuracy Wallet\",\r\n        sendType:\"Send Type\",\r\n        appliedSubscription:\"Applied Subscription\",\r\n        cycle:\"Cycle\",\r\n        sendNoticeExpired:\"Send Notice Expired\",\r\n        sendNoticeExpiredBefore:\"Send Notice Expired Before\",\r\n        day: \"Day\",\r\n        noticeFrequency:\"Auto Notice Frequency\",\r\n        time: \"Times\",\r\n        sharedPhone:\"Shared Phone\",\r\n        creator:\"Creator\",\r\n        otpCode:\"OTP Code\",\r\n        enterOtpCode:\"Enter OTP code\",\r\n        authenMethod: \"Authentication Method\",\r\n        subCode: \"Subscription Code\",\r\n        stt: \"STT\",\r\n        created: \"CREATED\",\r\n        activity: \"ACTIVITY\",\r\n        content: \"CONTENT\",\r\n        operator: \"OPERATOR\",\r\n        sharedTime: \"Shared Time\",\r\n        usedDate: \"Share usage until\",\r\n        shared: \"Shared Data\",\r\n        detailWallet: \"Detail Wallet\",\r\n        selectWallet:\"Select Wallet\",\r\n        transactionCode:\"Transaction Code\",\r\n        groupCode: \"Group Code\",\r\n        groupName: \"Group Name\",\r\n        detailGroup: \"Detail Group Sub\",\r\n        editGroupShare: \"Edit Group Share\",\r\n        createGroupShare: \"Create Group Share\",\r\n        shareGroup: \"Share Group\",\r\n        nameSub: \"Name Subscription\",\r\n        shareNormal: \"Share Normal\",\r\n        shareByGroup: \"Share By Group\",\r\n        generalInfo: \"General Info\",\r\n        listSubOfGroup: \"List Sub Of Group\",\r\n        autoShareWalletDetail : \"Group details shared automatically\",\r\n        renewalStatus : \"Renewal status\",\r\n        autoShareWallet : \"List of wallet groups shared automatically\",\r\n        walletList : \"Wallet List\",\r\n        sharePhoneList : \"Share Phone Number List\",\r\n        methodAutoShare: \"Automatic sharing method\",\r\n        notification: \"Notification\",\r\n        registerByPayCode: \"Register by Payment Code\",\r\n        registerBySubCode: \"Register by Wallet Code\",\r\n        autoShareGroup: \"Auto Share Group List\",\r\n        autoShareGroupDetail: \"Auto Share Wallet Group Details\",\r\n        lstWallet: \"Wallet List\",\r\n        lstSub: \"Shared Phone Number List\",\r\n        dateFrom: \"From Date\",\r\n        dateTo: \"Up To Date\",\r\n        timeUpTo: \"Time Up To\",\r\n        autoSharing:\"Automatic Sharing\",\r\n        typeShare: \"Type Share\",\r\n        active: \"Active Type\",\r\n        listShareError : \"Error Share Phone Number List\",\r\n        downloadErrorFile : \"Download Error File\",\r\n        addSharePhone : \"Add sharing recipients\",\r\n        walletCodeShare : \"Wallet code/ Payment code\",\r\n        viewPhoneList: \"View Phone List.\",\r\n        phoneShareList: \"Shared Phone List\",\r\n        status: \"Status\",\r\n        msisdnShareSucess: \"Phone number share success\",\r\n        msisdnShareFail: \"Phone number share fail\",\r\n    },\r\n    button:{\r\n        share:\"Share Data\",\r\n        equalSharing:\"Equal Sharing\",\r\n        fixedAllocation:\"Fixed Allocation\",\r\n        revokeSharing:\"Revoke Sharing\",\r\n        createWallet: \"Add Wallet\",\r\n        shareTraffic: \"Share Traffic\",\r\n        add:\"Add\",\r\n        delete:\"Delete\",\r\n        importFile:\"Import Receiver\",\r\n        shareByGroup: \"ShareBy Group\",\r\n        deleteSub: \"Delete Sub\",\r\n        editSub: \"Edit Sub\",\r\n        addSharePhone : \"Add phone numbers to share automatically\",\r\n        removeSharePhone : \"Remove phone numbers to share automatically\",\r\n        dateFrom : \"Usage period ends - From date\",\r\n        dateTo : \"Usage period ends - Until date\",\r\n        no: \"NO\",\r\n        register: \"REGISTER\",\r\n        yes: \"YES\",\r\n        addSubToGroupAuto: \"Add auto-shared phone number\",\r\n        deleteSubInGroupAuto: \"Delete auto-shared phone number\",\r\n        addShare : \"Add each subscriber\"\r\n    },\r\n    text: {\r\n        tax: \"Enter tax code\",\r\n        payCode: \"Enter payment code\",\r\n        subCode: \"Enter subscription code\",\r\n        importByFile: \"Enter sharing information by file\",\r\n        importReceive: \"Import recipient file\",\r\n        learnMore : \"Learn more\",\r\n        notify : \"Notification\",\r\n        nonOTPPayCode: \"Will apply OTP-free sharing to all wallets under this payment code\",\r\n        nonOTPSubCode: \"Will apply OTP-free sharing to this wallet code\",\r\n        noteAutoShare: \"(When registering for OTP-free sharing, the wallet will be allowed to share automatically)\",\r\n        noteRegisPayCode: \"(When registering for sharing without OTP, all wallets under this Payment Code will be allowed to share automatically)\",\r\n        noteCancelSubCode: \"(When canceling OTP-free sharing, the wallet will not be allowed to share automatically. The subscribers who are being shared automatically from this wallet will be canceled)\",\r\n        noteCancelPayCode: \"(When canceling OTP-free sharing by Payment Code, the wallets under this code will not be shared automatically. The subscribers who are being shared automatically from these wallets will be canceled)\",\r\n        hasPayCode: \"This wallet has registered for OTP-free sharing by Payment Code '${payCode}'\",\r\n        hasntAuto: \"This wallet hasn't registered for Auto Sharing\",\r\n        chooseRegistrationMethod : \"Please choose a registration method\",\r\n        downloadErrorMessage : \"The list contains invalid fields. Please check the downloaded file for more details.\"\r\n    },\r\n    placeholder:{\r\n        fullName:\"Enter Fullname\",\r\n        phone:\"Enter Phone\",\r\n        email:\"Enter Email\",\r\n        registerShare: \"Register to share without OTP\",\r\n        cancelShare: \"Cancel sharing without OTP\",\r\n        activeType: \"Choose Active Type\",\r\n        typeShare: \"Choose Type Share\",\r\n    },\r\n    message:{\r\n        otp:\"You will receive a message that contains otp code from M2M\",\r\n        resendOtp:\"Resend OTP\",\r\n        in:\"in\",\r\n        sec:\"second\",\r\n        patternError:\"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_)\",\r\n        digitError: \"Phone number must be a number starting with 0 (10-11 characters)\",\r\n        delete: \"Delete Share Info\",\r\n        confirmDelete: \"Do you want to delete this share infomation ?\",\r\n        confirmRevoke:\"Do you want to revoke sharing data ?\",\r\n        miniumSMS:\"Minium share data is 10 SMS\",\r\n        miniumData:\"Minium share data is 100 MB\",\r\n        exceededData:\"Shared data exceeds remaining data\",\r\n        errorSearch:\"Please select atleast 1 search item\",\r\n        existed:\"The subscription already exists in the sharing list\",\r\n        notValidPhone: \"Please choose Vinaphone number\",\r\n        existedPhone:\"Phone already exists\",\r\n        otpIncorrect:\"OTP is Incorrect\",\r\n        dataError: \"Data traffic is multiple of 100 and has minium of 100 MB\",\r\n        smsError: \"SMS traffic is multiple of 5 and has minium of 5 SMS\",\r\n        deleteSub: \"Are you sure you want to delete the selected subscription?\",\r\n        maximumSubAdd: \"Maximum of 50 subscribers can be added only\",\r\n        maximumSubDisplay: \"Each group can only add a maximum of 3000 subscribers\",\r\n        duplicateSub: \"Each subscriber is only allowed to belong to 1 group, subscriber ${data} is in group '${groupName}' so cannot be added to this group\",\r\n        dublicateShareInfo: \"The subscriber number has been added to the list.\",\r\n        confirmRegisterAutoShareWallet: \"Do you want to register OTP-free sharing for this wallet?\",\r\n        confirmRegisterAutoSharePayCode: \"Do you want to register for OTP-free sharing for this Payment Code?\",\r\n        readMore: \"Learn more\",\r\n        confirmCancelSubCode:\"Do you want to cancel OTP-free sharing for this wallet?\",\r\n        confirmCancelPayCode:\"Do you want to cancel OTP-free sharing for Payment Code '${payCode}'?\",\r\n        registerSuccess: \"Registered successfully\",\r\n        cancelSuccess: \"Cancelled successfully\",\r\n        invalidDataUsage : \"The minimum data traffic is 100MB and is a multiple of 100\",\r\n        invalidSmsUsage : \"The minimum data traffic is 100 SMS and is a multiple of 5\",\r\n        existSharePhone : \"The phone number ${phoneNumber} is already in the group. If you add it multiple times, it will be automatically shared multiple times\",\r\n        deleteAutoShare : \"Are you sure you want to remove automatic sharing of selected numbers to this wallet group?\",\r\n        deleteWarnAutoShare : \"If removed from the group, automatic sharing to phone numbers from this wallet group will stop\",\r\n        existPhoneInTable: \"The phone number ${phoneNumber} is already exist in the table\",\r\n        exceedTrafficShare: \"Shared traffic for subscribers exceeds ${remainDataWallet} ${typeWallet} remaining in the wallet. Please reduce sharing traffic or delete subscriptions or choose another wallet\",\r\n        shareNotifySuccess : \"Shared and added to group successfully ${success}/${total} phone number with valid information\",\r\n        shareNotifyFail : \"Shared and added to group successfully ${success}/${total} phone number with valid information. Invalid information in error list\",\r\n        addSuccess : \"Add successfully\",\r\n        shareOK: \"Share successfully\",\r\n        shareNotifyBackground: \"Request in progress. Processing will take a few minutes due to the large number of shared subscribers (>50). Please check the results later in the Activity History\"\r\n    },\r\n    activeType:{\r\n        buy: \"Buy data\",\r\n        share: \"Share data\",\r\n        accuracy: \"Accuracy wallet\",\r\n        registerNonOTP: \"Register sharing without OTP\",\r\n        cancelRegisterNonOTP: \"Cancel register without OTP\",\r\n    },\r\n    error:{\r\n        existedGroupCode: \"This shared group code already exists\"\r\n    },\r\n    renewStatus : {\r\n        notDueYet : \"Not due yet\",\r\n        dueDate : \"Due date\",\r\n        expired : \"(Expired\"\r\n    },\r\n    methodAutoShare: {\r\n        none: \"None\",\r\n        subCode: \"Wallet Code\",\r\n        payCode: \"Pay Code\",\r\n    },\r\n    renewalStatus: {\r\n        notDueYet: \"Chưa đến hạn\",\r\n        due: \"Đến hạn\",\r\n        expired: \"Hết hạn\",\r\n    },\r\n    typeShare:{\r\n        manual: \"Manual\",\r\n        auto: \"Auto\"\r\n    },\r\n    activityHistoryStatus: {\r\n        fail: \"Fail\",\r\n        success: \"Success\",\r\n        processing: \"Processing\",\r\n        completed: \"Completed\",\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAc;EACVA,KAAK,EAAC;IACFC,SAAS,EAAC,kBAAkB;IAC5BC,gBAAgB,EAAC,qBAAqB;IACtCC,SAAS,EAAC,YAAY;IACtBC,QAAQ,EAAC,WAAW;IACpBC,KAAK,EAAC,OAAO;IACbC,iBAAiB,EAAC,oBAAoB;IACtCC,UAAU,EAAC,aAAa;IACxBC,qBAAqB,EAAC,oBAAoB;IAC1CC,SAAS,EAAC,YAAY;IACtBC,WAAW,EAAC,aAAa;IACzBC,aAAa,EAAC,gBAAgB;IAC9BC,UAAU,EAAC,gBAAgB;IAC3BC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAC,yCAAyC;IACvDC,KAAK,EAAC,OAAO;IACbC,SAAS,EAAC,OAAO;IACjBC,UAAU,EAAC,gBAAgB;IAC3BC,WAAW,EAAC,wBAAwB;IACpCC,kBAAkB,EAAC,cAAc;IACjCC,UAAU,EAAC,YAAY;IACvBC,SAAS,EAAC,YAAY;IACtBC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,cAAc;IAC3BC,aAAa,EAAC,gBAAgB;IAC9BC,OAAO,EAAE,UAAU;IACnBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,aAAa;IAC1BC,gBAAgB,EAAE,oBAAoB;IACtCC,cAAc,EAAE,kBAAkB;IAClCC,YAAY,EAAE,gBAAgB;IAC9BC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,eAAe;IAC7BC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,iBAAiB;IACjCC,QAAQ,EAAC,WAAW;IACpBC,mBAAmB,EAAC,sBAAsB;IAC1CC,KAAK,EAAC,OAAO;IACbC,iBAAiB,EAAC,qBAAqB;IACvCC,uBAAuB,EAAC,4BAA4B;IACpDC,GAAG,EAAE,KAAK;IACVC,eAAe,EAAC,uBAAuB;IACvCC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAC,cAAc;IAC1BC,OAAO,EAAC,SAAS;IACjBC,OAAO,EAAC,UAAU;IAClBC,YAAY,EAAC,gBAAgB;IAC7BC,YAAY,EAAE,uBAAuB;IACrCC,OAAO,EAAE,mBAAmB;IAC5BC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,mBAAmB;IAC7BC,MAAM,EAAE,aAAa;IACrBC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAC,eAAe;IAC5BC,eAAe,EAAC,kBAAkB;IAClCC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,kBAAkB;IAClCC,gBAAgB,EAAE,oBAAoB;IACtCC,UAAU,EAAE,aAAa;IACzBC,OAAO,EAAE,mBAAmB;IAC5BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,gBAAgB;IAC9BC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,mBAAmB;IACnCC,qBAAqB,EAAG,oCAAoC;IAC5DC,aAAa,EAAG,gBAAgB;IAChCC,eAAe,EAAG,4CAA4C;IAC9DC,UAAU,EAAG,aAAa;IAC1BC,cAAc,EAAG,yBAAyB;IAC1CC,eAAe,EAAE,0BAA0B;IAC3CC,YAAY,EAAE,cAAc;IAC5BC,iBAAiB,EAAE,0BAA0B;IAC7CC,iBAAiB,EAAE,yBAAyB;IAC5CC,cAAc,EAAE,uBAAuB;IACvCC,oBAAoB,EAAE,iCAAiC;IACvDC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,0BAA0B;IAClCC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,YAAY;IACtBC,WAAW,EAAC,mBAAmB;IAC/BC,SAAS,EAAE,YAAY;IACvBC,MAAM,EAAE,aAAa;IACrBC,cAAc,EAAG,+BAA+B;IAChDC,iBAAiB,EAAG,qBAAqB;IACzCC,aAAa,EAAG,wBAAwB;IACxCC,eAAe,EAAG,2BAA2B;IAC7CC,aAAa,EAAE,kBAAkB;IACjCC,cAAc,EAAE,mBAAmB;IACnCC,MAAM,EAAE,QAAQ;IAChBC,iBAAiB,EAAE,4BAA4B;IAC/CC,eAAe,EAAE;GACpB;EACDC,MAAM,EAAC;IACHC,KAAK,EAAC,YAAY;IAClBC,YAAY,EAAC,eAAe;IAC5BC,eAAe,EAAC,kBAAkB;IAClCC,aAAa,EAAC,gBAAgB;IAC9BC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,eAAe;IAC7BC,GAAG,EAAC,KAAK;IACTC,MAAM,EAAC,QAAQ;IACfC,UAAU,EAAC,iBAAiB;IAC5BxC,YAAY,EAAE,eAAe;IAC7ByC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,UAAU;IACnBlB,aAAa,EAAG,0CAA0C;IAC1DmB,gBAAgB,EAAG,6CAA6C;IAChE3B,QAAQ,EAAG,+BAA+B;IAC1CC,MAAM,EAAG,gCAAgC;IACzC2B,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVC,iBAAiB,EAAE,8BAA8B;IACjDC,oBAAoB,EAAE,iCAAiC;IACvDC,QAAQ,EAAG;GACd;EACDC,IAAI,EAAE;IACFrF,GAAG,EAAE,gBAAgB;IACrBT,OAAO,EAAE,oBAAoB;IAC7BwB,OAAO,EAAE,yBAAyB;IAClCuE,YAAY,EAAE,mCAAmC;IACjDC,aAAa,EAAE,uBAAuB;IACtCC,SAAS,EAAG,YAAY;IACxBC,MAAM,EAAG,cAAc;IACvBC,aAAa,EAAE,oEAAoE;IACnFC,aAAa,EAAE,iDAAiD;IAChEC,aAAa,EAAE,4FAA4F;IAC3GC,gBAAgB,EAAE,wHAAwH;IAC1IC,iBAAiB,EAAE,gLAAgL;IACnMC,iBAAiB,EAAE,yMAAyM;IAC5NC,UAAU,EAAE,8EAA8E;IAC1FC,SAAS,EAAE,gDAAgD;IAC3DC,wBAAwB,EAAG,qCAAqC;IAChEC,oBAAoB,EAAG;GAC1B;EACDC,WAAW,EAAC;IACRlI,QAAQ,EAAC,gBAAgB;IACzBW,KAAK,EAAC,aAAa;IACnBV,KAAK,EAAC,aAAa;IACnBkI,aAAa,EAAE,+BAA+B;IAC9CC,WAAW,EAAE,4BAA4B;IACzCC,UAAU,EAAE,oBAAoB;IAChChD,SAAS,EAAE;GACd;EACDiD,OAAO,EAAC;IACJC,GAAG,EAAC,4DAA4D;IAChEC,SAAS,EAAC,YAAY;IACtBC,EAAE,EAAC,IAAI;IACPC,GAAG,EAAC,QAAQ;IACZC,YAAY,EAAC,+CAA+C;IAC5DC,UAAU,EAAE,kEAAkE;IAC9EpC,MAAM,EAAE,mBAAmB;IAC3BqC,aAAa,EAAE,+CAA+C;IAC9DC,aAAa,EAAC,sCAAsC;IACpDC,SAAS,EAAC,6BAA6B;IACvCC,UAAU,EAAC,6BAA6B;IACxCC,YAAY,EAAC,oCAAoC;IACjDC,WAAW,EAAC,qCAAqC;IACjDC,OAAO,EAAC,qDAAqD;IAC7DC,aAAa,EAAE,gCAAgC;IAC/CC,YAAY,EAAC,sBAAsB;IACnCC,YAAY,EAAC,kBAAkB;IAC/BC,SAAS,EAAE,0DAA0D;IACrEC,QAAQ,EAAE,sDAAsD;IAChE9C,SAAS,EAAE,4DAA4D;IACvE+C,aAAa,EAAE,6CAA6C;IAC5DC,iBAAiB,EAAE,uDAAuD;IAC1EC,YAAY,EAAE,sIAAsI;IACpJC,kBAAkB,EAAE,mDAAmD;IACvEC,8BAA8B,EAAE,2DAA2D;IAC3FC,+BAA+B,EAAE,qEAAqE;IACtGC,QAAQ,EAAE,YAAY;IACtBC,oBAAoB,EAAC,yDAAyD;IAC9EC,oBAAoB,EAAC,uEAAuE;IAC5FC,eAAe,EAAE,yBAAyB;IAC1CC,aAAa,EAAE,wBAAwB;IACvCC,gBAAgB,EAAG,4DAA4D;IAC/EC,eAAe,EAAG,4DAA4D;IAC9EC,eAAe,EAAG,uIAAuI;IACzJC,eAAe,EAAG,6FAA6F;IAC/GC,mBAAmB,EAAG,gGAAgG;IACtHC,iBAAiB,EAAE,+DAA+D;IAClFC,kBAAkB,EAAE,kLAAkL;IACtMC,kBAAkB,EAAG,gGAAgG;IACrHC,eAAe,EAAG,mIAAmI;IACrJC,UAAU,EAAG,kBAAkB;IAC/BC,OAAO,EAAE,oBAAoB;IAC7BC,qBAAqB,EAAE;GAC1B;EACD1C,UAAU,EAAC;IACP2C,GAAG,EAAE,UAAU;IACf/E,KAAK,EAAE,YAAY;IACnBgF,QAAQ,EAAE,iBAAiB;IAC3BC,cAAc,EAAE,8BAA8B;IAC9CC,oBAAoB,EAAE;GACzB;EACDC,KAAK,EAAC;IACFC,gBAAgB,EAAE;GACrB;EACDC,WAAW,EAAG;IACVC,SAAS,EAAG,aAAa;IACzBC,OAAO,EAAG,UAAU;IACpBC,OAAO,EAAG;GACb;EACDhH,eAAe,EAAE;IACbiH,IAAI,EAAE,MAAM;IACZ7I,OAAO,EAAE,aAAa;IACtBxB,OAAO,EAAE;GACZ;EACDgD,aAAa,EAAE;IACXkH,SAAS,EAAE,cAAc;IACzBI,GAAG,EAAE,SAAS;IACdF,OAAO,EAAE;GACZ;EACDpG,SAAS,EAAC;IACNuG,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE;GACT;EACDC,qBAAqB,EAAE;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE;;CAElB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}