{"ast": null, "code": "import { ActivatedRoute, Router } from \"@angular/router\";\nimport { TranslateService } from \"./service/comon/translate.service\";\nimport { UtilService } from \"./service/comon/util.service\";\nimport { SessionService } from \"./service/session/SessionService\";\nimport { MessageCommonService } from \"./service/comon/message-common.service\";\nimport { DebounceInputService } from \"./service/comon/debounce.input.service\";\nimport { RouterService } from \"./service/comon/router.service\";\nimport { ObservableService } from \"./service/comon/observable.service\";\nimport { CONSTANTS } from \"./service/comon/constants\";\nimport { DataPageWithUserType } from \"./service/DataPageWithUserType\";\nexport class ComponentBase {\n  constructor(injector) {\n    this.pathNotCheckPolicy = [\"/policies\", \"/error\", \"/access\", \"/login\", \"/reset-password\", \"/notfound\", \"/landing\"];\n    this.pathNotCheckLogin = [\"/error\", \"/access\", \"/login\", \"/reset-password\", \"/notfound\", \"/landing\"];\n    this.router = injector.get(Router);\n    this.route = injector.get(ActivatedRoute);\n    this.tranService = injector.get(TranslateService);\n    this.utilService = injector.get(UtilService);\n    this.messageCommonService = injector.get(MessageCommonService);\n    this.sessionService = injector.get(SessionService);\n    this.debounceService = injector.get(DebounceInputService);\n    this.routerService = injector.get(RouterService);\n    this.observableService = injector.get(ObservableService);\n    let me = this;\n    this.router.events.subscribe(event => {\n      // console.log(event.url, me.router.url, event.url == me.router.url)\n      if (event.url == me.router.url) {\n        if (!this.pathNotCheckLogin.includes(event.urlAfterRedirects.split(\"?\")[0])) {\n          if (!localStorage.getItem(\"token\")) {\n            window.location.hash = '/login';\n          }\n        }\n        me.executeChangeRouting(event);\n        if (!me.pathNotCheckPolicy.includes(event.url) && !me.pathNotCheckPolicy.includes(event.url.split(\";\")[0]) && !me.pathNotCheckPolicy.includes(event.url.split(\"?\")[0])) {\n          if (me.observableService.next) {\n            me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {\n              forceClose: false\n            });\n          }\n        } else {\n          if (me.observableService.next) {\n            me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {\n              forceClose: true\n            });\n          }\n        }\n      }\n    });\n  }\n  executeChangeRouting(event) {\n    let dataPage = this.routerService.getDataPage();\n    if (dataPage == null || dataPage == undefined) return;\n    let title = dataPage.pageTitle;\n    if ((title || \"\").length > 0) {\n      document.title = this.tranService.translate(title);\n    } else {\n      document.title = \"CMP\";\n    }\n    let result = this.checkAuthen(dataPage.authorities);\n    if (result && dataPage instanceof DataPageWithUserType) {\n      result = this.checkType(dataPage.userTypes);\n    }\n    if (!result) {\n      window.location.hash = \"/access\";\n    }\n  }\n  checkAuthen(authens, isReportDynamic = false) {\n    if ((authens || []).length == 0) return true;\n    if (this.sessionService.userInfo.authorities == undefined) {\n      window.location.hash = '/login';\n    }\n    try {\n      let userAuthens = this.sessionService.userInfo.authorities;\n      if ((userAuthens || []).length == 0) {\n        return false;\n      }\n      let hasViewContentReportDynamic = false;\n      if ((authens || []).length > 0) {\n        let flag = false;\n        for (let i = 0; i < authens.length; i++) {\n          if (userAuthens.includes(authens[i])) {\n            flag = true;\n            break;\n          }\n        }\n        if (!flag) {\n          if (isReportDynamic) {\n            for (let i = 0; i < userAuthens.length; i++) {\n              if (userAuthens[i].startsWith(\"getReport\")) {\n                return true;\n              }\n            }\n          } else if (authens[0] == \"getReport\") {\n            let url = window.location.hash.split(\"?\")[0];\n            let idReport = url.substring(url.lastIndexOf(\"/\") + 1);\n            console.log(idReport);\n            for (let i = 0; i < userAuthens.length; i++) {\n              if (userAuthens[i] == `getReport_${idReport}`) {\n                return true;\n              }\n            }\n          }\n          return false;\n        }\n      }\n      return true;\n    } catch (error) {\n      window.location.hash = '/login';\n      return false;\n    }\n  }\n  checkType(userTypes) {\n    if (this.sessionService.userInfo.type == undefined) {\n      window.location.hash = '/login';\n    }\n    try {\n      let type = this.sessionService.userInfo.type;\n      return userTypes.includes(type) ? true : false;\n    } catch (error) {\n      window.location.hash = '/login';\n      return false;\n    }\n  }\n}", "map": {"version": 3, "names": ["ActivatedRoute", "Router", "TranslateService", "UtilService", "SessionService", "MessageCommonService", "DebounceInputService", "RouterService", "ObservableService", "CONSTANTS", "DataPageWithUserType", "ComponentBase", "constructor", "injector", "pathNotCheckPolicy", "pathNotCheckLogin", "router", "get", "route", "tranService", "utilService", "messageCommonService", "sessionService", "debounceService", "routerService", "observableService", "me", "events", "subscribe", "event", "url", "includes", "urlAfterRedirects", "split", "localStorage", "getItem", "window", "location", "hash", "executeChangeRouting", "next", "OBSERVABLE", "KEY_LOAD_CONFIRM_POLICY_HISTORY", "forceClose", "dataPage", "getDataPage", "undefined", "title", "pageTitle", "length", "document", "translate", "result", "<PERSON><PERSON><PERSON><PERSON>", "authorities", "checkType", "userTypes", "authens", "isReportDynamic", "userInfo", "userAuthens", "hasViewContentReportDynamic", "flag", "i", "startsWith", "idReport", "substring", "lastIndexOf", "console", "log", "error", "type"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\component.base.ts"], "sourcesContent": ["import { Component, Inject, Injectable, Injector, OnInit } from \"@angular/core\";\r\nimport { ActivatedRoute, NavigationEnd, Router, RouterModule } from \"@angular/router\";\r\nimport DataPage from \"./service/data.page\";\r\nimport { TranslateService } from \"./service/comon/translate.service\";\r\nimport { UtilService } from \"./service/comon/util.service\";\r\nimport { SessionService } from \"./service/session/SessionService\";\r\nimport { MessageCommonService } from \"./service/comon/message-common.service\";\r\nimport { DebounceInputService } from \"./service/comon/debounce.input.service\";\r\nimport { RouterService } from \"./service/comon/router.service\";\r\nimport { ObservableService } from \"./service/comon/observable.service\";\r\nimport { CONSTANTS } from \"./service/comon/constants\";\r\nimport {DataPageWithUserType} from \"./service/DataPageWithUserType\";\r\n\r\nexport class ComponentBase{\r\n    public tranService: TranslateService;\r\n    public utilService: UtilService;\r\n    protected messageCommonService: MessageCommonService;\r\n    protected sessionService: SessionService;\r\n    protected debounceService: DebounceInputService;\r\n    protected router: Router;\r\n    protected route: ActivatedRoute;\r\n    protected routerService: RouterService;\r\n    protected observableService: ObservableService;\r\n\r\n    pathNotCheckPolicy: Array<string> = [\"/policies\",\"/error\", \"/access\", \"/login\", \"/reset-password\", \"/notfound\", \"/landing\"];\r\n    pathNotCheckLogin: Array<string> = [\"/error\", \"/access\", \"/login\", \"/reset-password\", \"/notfound\", \"/landing\"];\r\n\r\n    constructor(injector: Injector) {\r\n        this.router = injector.get(Router);\r\n        this.route = injector.get(ActivatedRoute);\r\n        this.tranService = injector.get(TranslateService);\r\n        this.utilService = injector.get(UtilService);\r\n        this.messageCommonService = injector.get(MessageCommonService);\r\n        this.sessionService = injector.get(SessionService);\r\n        this.debounceService = injector.get(DebounceInputService);\r\n        this.routerService = injector.get(RouterService);\r\n        this.observableService = injector.get(ObservableService);\r\n        let me = this;\r\n        this.router.events.subscribe((event:NavigationEnd)=>{\r\n            // console.log(event.url, me.router.url, event.url == me.router.url)\r\n            if(event.url == me.router.url){\r\n                if(!this.pathNotCheckLogin.includes(event.urlAfterRedirects.split(\"?\")[0])){\r\n                    if(!localStorage.getItem(\"token\")){\r\n                        window.location.hash = '/login';\r\n                    }\r\n                }\r\n                me.executeChangeRouting(event);\r\n                if(!me.pathNotCheckPolicy.includes(event.url) && !me.pathNotCheckPolicy.includes(event.url.split(\";\")[0]) && !me.pathNotCheckPolicy.includes(event.url.split(\"?\")[0])){\r\n                    if(me.observableService.next){\r\n                        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {forceClose: false});\r\n                    }\r\n                }else{\r\n                    if(me.observableService.next){\r\n                        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {forceClose: true});\r\n                    }\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n    private executeChangeRouting(event:NavigationEnd){\r\n        let dataPage: DataPage = this.routerService.getDataPage();\r\n        if(dataPage == null || dataPage == undefined) return;\r\n        let title: string = dataPage.pageTitle;\r\n        if((title || \"\").length > 0){\r\n            document.title = this.tranService.translate(title);\r\n        }else{\r\n            document.title = \"CMP\";\r\n        }\r\n        let result = this.checkAuthen(dataPage.authorities);\r\n\r\n        if (result && dataPage instanceof DataPageWithUserType) {\r\n            result = this.checkType(dataPage.userTypes)\r\n        }\r\n\r\n        if(!result){\r\n            window.location.hash = \"/access\";\r\n        }\r\n    }\r\n\r\n    public checkAuthen(authens: Array<string>, isReportDynamic: boolean = false):boolean{\r\n        if((authens || []).length == 0) return true;\r\n        if(this.sessionService.userInfo.authorities == undefined){\r\n            window.location.hash = '/login';\r\n        }\r\n        try {\r\n            let userAuthens = this.sessionService.userInfo.authorities;\r\n            if((userAuthens || []).length == 0){\r\n                return false;\r\n            }\r\n            let hasViewContentReportDynamic = false;\r\n            if((authens || []).length > 0){\r\n                let flag = false;\r\n                for(let i = 0; i < authens.length;i++){\r\n                    if(userAuthens.includes(authens[i])){\r\n                        flag = true;\r\n                        break;\r\n                    }\r\n                }\r\n                if(!flag){\r\n                    if(isReportDynamic){\r\n                        for(let i = 0; i < userAuthens.length;i++){\r\n                            if(userAuthens[i].startsWith(\"getReport\")){\r\n                                return true;\r\n                            }\r\n                        }\r\n                    }else if(authens[0] == \"getReport\"){\r\n                        let url = window.location.hash.split(\"?\")[0];\r\n                        let idReport = url.substring(url.lastIndexOf(\"/\")+1);\r\n                        console.log(idReport)\r\n                        for(let i = 0; i < userAuthens.length;i++){\r\n                            if(userAuthens[i] == `getReport_${idReport}`){\r\n                                return true;\r\n                            }\r\n                        }\r\n                    }\r\n                    return false;\r\n                }\r\n            }\r\n            return true;\r\n        } catch (error) {\r\n            window.location.hash = '/login';\r\n            return false;\r\n        }\r\n    }\r\n\r\n    public checkType(userTypes: Array<number>) {\r\n        if(this.sessionService.userInfo.type == undefined){\r\n            window.location.hash = '/login';\r\n        }\r\n        try {\r\n            let type = this.sessionService.userInfo.type;\r\n            return userTypes.includes(type) ? true : false\r\n        }catch (error) {\r\n            window.location.hash = '/login';\r\n            return false;\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,cAAc,EAAiBC,MAAM,QAAsB,iBAAiB;AAErF,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAAQC,oBAAoB,QAAO,gCAAgC;AAEnE,OAAM,MAAOC,aAAa;EActBC,YAAYC,QAAkB;IAH9B,KAAAC,kBAAkB,GAAkB,CAAC,WAAW,EAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC;IAC3H,KAAAC,iBAAiB,GAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC;IAG1G,IAAI,CAACC,MAAM,GAAGH,QAAQ,CAACI,GAAG,CAAChB,MAAM,CAAC;IAClC,IAAI,CAACiB,KAAK,GAAGL,QAAQ,CAACI,GAAG,CAACjB,cAAc,CAAC;IACzC,IAAI,CAACmB,WAAW,GAAGN,QAAQ,CAACI,GAAG,CAACf,gBAAgB,CAAC;IACjD,IAAI,CAACkB,WAAW,GAAGP,QAAQ,CAACI,GAAG,CAACd,WAAW,CAAC;IAC5C,IAAI,CAACkB,oBAAoB,GAAGR,QAAQ,CAACI,GAAG,CAACZ,oBAAoB,CAAC;IAC9D,IAAI,CAACiB,cAAc,GAAGT,QAAQ,CAACI,GAAG,CAACb,cAAc,CAAC;IAClD,IAAI,CAACmB,eAAe,GAAGV,QAAQ,CAACI,GAAG,CAACX,oBAAoB,CAAC;IACzD,IAAI,CAACkB,aAAa,GAAGX,QAAQ,CAACI,GAAG,CAACV,aAAa,CAAC;IAChD,IAAI,CAACkB,iBAAiB,GAAGZ,QAAQ,CAACI,GAAG,CAACT,iBAAiB,CAAC;IACxD,IAAIkB,EAAE,GAAG,IAAI;IACb,IAAI,CAACV,MAAM,CAACW,MAAM,CAACC,SAAS,CAAEC,KAAmB,IAAG;MAChD;MACA,IAAGA,KAAK,CAACC,GAAG,IAAIJ,EAAE,CAACV,MAAM,CAACc,GAAG,EAAC;QAC1B,IAAG,CAAC,IAAI,CAACf,iBAAiB,CAACgB,QAAQ,CAACF,KAAK,CAACG,iBAAiB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;UACvE,IAAG,CAACC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAC;YAC9BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;;;QAGvCZ,EAAE,CAACa,oBAAoB,CAACV,KAAK,CAAC;QAC9B,IAAG,CAACH,EAAE,CAACZ,kBAAkB,CAACiB,QAAQ,CAACF,KAAK,CAACC,GAAG,CAAC,IAAI,CAACJ,EAAE,CAACZ,kBAAkB,CAACiB,QAAQ,CAACF,KAAK,CAACC,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACP,EAAE,CAACZ,kBAAkB,CAACiB,QAAQ,CAACF,KAAK,CAACC,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;UAClK,IAAGP,EAAE,CAACD,iBAAiB,CAACe,IAAI,EAAC;YACzBd,EAAE,CAACD,iBAAiB,CAACe,IAAI,CAAC/B,SAAS,CAACgC,UAAU,CAACC,+BAA+B,EAAE;cAACC,UAAU,EAAE;YAAK,CAAC,CAAC;;SAE3G,MAAI;UACD,IAAGjB,EAAE,CAACD,iBAAiB,CAACe,IAAI,EAAC;YACzBd,EAAE,CAACD,iBAAiB,CAACe,IAAI,CAAC/B,SAAS,CAACgC,UAAU,CAACC,+BAA+B,EAAE;cAACC,UAAU,EAAE;YAAI,CAAC,CAAC;;;;IAInH,CAAC,CAAC;EACN;EAEQJ,oBAAoBA,CAACV,KAAmB;IAC5C,IAAIe,QAAQ,GAAa,IAAI,CAACpB,aAAa,CAACqB,WAAW,EAAE;IACzD,IAAGD,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAIE,SAAS,EAAE;IAC9C,IAAIC,KAAK,GAAWH,QAAQ,CAACI,SAAS;IACtC,IAAG,CAACD,KAAK,IAAI,EAAE,EAAEE,MAAM,GAAG,CAAC,EAAC;MACxBC,QAAQ,CAACH,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAACgC,SAAS,CAACJ,KAAK,CAAC;KACrD,MAAI;MACDG,QAAQ,CAACH,KAAK,GAAG,KAAK;;IAE1B,IAAIK,MAAM,GAAG,IAAI,CAACC,WAAW,CAACT,QAAQ,CAACU,WAAW,CAAC;IAEnD,IAAIF,MAAM,IAAIR,QAAQ,YAAYlC,oBAAoB,EAAE;MACpD0C,MAAM,GAAG,IAAI,CAACG,SAAS,CAACX,QAAQ,CAACY,SAAS,CAAC;;IAG/C,IAAG,CAACJ,MAAM,EAAC;MACPhB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;;EAExC;EAEOe,WAAWA,CAACI,OAAsB,EAAEC,eAAA,GAA2B,KAAK;IACvE,IAAG,CAACD,OAAO,IAAI,EAAE,EAAER,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;IAC3C,IAAG,IAAI,CAAC3B,cAAc,CAACqC,QAAQ,CAACL,WAAW,IAAIR,SAAS,EAAC;MACrDV,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;;IAEnC,IAAI;MACA,IAAIsB,WAAW,GAAG,IAAI,CAACtC,cAAc,CAACqC,QAAQ,CAACL,WAAW;MAC1D,IAAG,CAACM,WAAW,IAAI,EAAE,EAAEX,MAAM,IAAI,CAAC,EAAC;QAC/B,OAAO,KAAK;;MAEhB,IAAIY,2BAA2B,GAAG,KAAK;MACvC,IAAG,CAACJ,OAAO,IAAI,EAAE,EAAER,MAAM,GAAG,CAAC,EAAC;QAC1B,IAAIa,IAAI,GAAG,KAAK;QAChB,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,OAAO,CAACR,MAAM,EAACc,CAAC,EAAE,EAAC;UAClC,IAAGH,WAAW,CAAC7B,QAAQ,CAAC0B,OAAO,CAACM,CAAC,CAAC,CAAC,EAAC;YAChCD,IAAI,GAAG,IAAI;YACX;;;QAGR,IAAG,CAACA,IAAI,EAAC;UACL,IAAGJ,eAAe,EAAC;YACf,KAAI,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACX,MAAM,EAACc,CAAC,EAAE,EAAC;cACtC,IAAGH,WAAW,CAACG,CAAC,CAAC,CAACC,UAAU,CAAC,WAAW,CAAC,EAAC;gBACtC,OAAO,IAAI;;;WAGtB,MAAK,IAAGP,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,EAAC;YAC/B,IAAI3B,GAAG,GAAGM,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAIgC,QAAQ,GAAGnC,GAAG,CAACoC,SAAS,CAACpC,GAAG,CAACqC,WAAW,CAAC,GAAG,CAAC,GAAC,CAAC,CAAC;YACpDC,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;YACrB,KAAI,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACX,MAAM,EAACc,CAAC,EAAE,EAAC;cACtC,IAAGH,WAAW,CAACG,CAAC,CAAC,IAAI,aAAaE,QAAQ,EAAE,EAAC;gBACzC,OAAO,IAAI;;;;UAIvB,OAAO,KAAK;;;MAGpB,OAAO,IAAI;KACd,CAAC,OAAOK,KAAK,EAAE;MACZlC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAO,KAAK;;EAEpB;EAEOiB,SAASA,CAACC,SAAwB;IACrC,IAAG,IAAI,CAAClC,cAAc,CAACqC,QAAQ,CAACY,IAAI,IAAIzB,SAAS,EAAC;MAC9CV,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;;IAEnC,IAAI;MACA,IAAIiC,IAAI,GAAG,IAAI,CAACjD,cAAc,CAACqC,QAAQ,CAACY,IAAI;MAC5C,OAAOf,SAAS,CAACzB,QAAQ,CAACwC,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK;KACjD,QAAOD,KAAK,EAAE;MACXlC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAO,KAAK;;EAEpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}