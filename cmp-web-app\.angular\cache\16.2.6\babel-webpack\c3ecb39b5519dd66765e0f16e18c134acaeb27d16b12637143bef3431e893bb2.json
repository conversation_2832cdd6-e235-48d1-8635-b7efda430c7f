{"ast": null, "code": "export default {\n  titlepage: {\n    seardDiagnose: \"Diagnose\"\n  },\n  menu: {\n    diagnose: \"Diagnosis\"\n  },\n  label: {\n    msisdn: \"Subscriber Number*\",\n    fromDate: \"From Date\",\n    toDate: \"To Date\",\n    lastActivity: \"Last Activity\",\n    celLName: \"Cell Name\",\n    class: \"Membership Class\",\n    ratingPlan: \"Plan\",\n    numericalOrder: \"No.\",\n    volumeDownlink: \"Amount of Downloaded Data (MB)\",\n    volumeUplink: \"Lượng dữ liệu tải lên (MB)\",\n    typeNetwork: \"Loại mạng\",\n    typeNetwork4G: \"LTE 4G\",\n    typeNetwork3G: \"UMTS 3G\",\n    coverageLevel: \"CEI\",\n    coverageExcellent: \"Excellent\",\n    coverageGood: \"Good\",\n    coverageAverage: \"Average\",\n    coveragePoor: \"Poor\",\n    coverageBad: \"Bad\",\n    time: \"Time\"\n  }\n};", "map": {"version": 3, "names": ["titlepage", "<PERSON>rd<PERSON>ia<PERSON>se", "menu", "diagnose", "label", "msisdn", "fromDate", "toDate", "lastActivity", "celLName", "class", "ratingPlan", "numericalOrder", "volumeDownlink", "volumeUplink", "typeNetwork", "typeNetwork4G", "typeNetwork3G", "coverageLevel", "coverageExcellent", "coverageGood", "coverageAverage", "coveragePoor", "coverageBad", "time"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\diagnose.ts"], "sourcesContent": ["export default {\r\n    titlepage: {\r\n        seardDiagnose: \"Diagnose\",\r\n    },\r\n    menu: {\r\n        diagnose: \"Diagnosis\",\r\n    },\r\n    label: {\r\n        msisdn: \"Subscriber Number*\",\r\n        fromDate: \"From Date\",\r\n        toDate: \"To Date\",\r\n        lastActivity: \"Last Activity\",\r\n        celLName: \"Cell Name\",\r\n        class: \"Membership Class\",\r\n        ratingPlan: \"Plan\",\r\n        numericalOrder: \"No.\",\r\n        volumeDownlink: \"Amount of Downloaded Data (MB)\",\r\n        volumeUplink: \"Lượng dữ liệu tải lên (MB)\",\r\n        typeNetwork: \"Loại mạng\",\r\n        typeNetwork4G: \"LTE 4G\",\r\n        typeNetwork3G: \"UMTS 3G\",\r\n        coverageLevel: \"CEI\",\r\n        coverageExcellent: \"Excellent\",\r\n        coverageGood: \"Good\",\r\n        coverageAverage: \"Average\",\r\n        coveragePoor: \"Poor\",\r\n        coverageBad: \"Bad\",\r\n        time: \"Time\",\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,SAAS,EAAE;IACPC,aAAa,EAAE;GAClB;EACDC,IAAI,EAAE;IACFC,QAAQ,EAAE;GACb;EACDC,KAAK,EAAE;IACHC,MAAM,EAAE,oBAAoB;IAC5BC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,kBAAkB;IACzBC,UAAU,EAAE,MAAM;IAClBC,cAAc,EAAE,KAAK;IACrBC,cAAc,EAAE,gCAAgC;IAChDC,YAAY,EAAE,4BAA4B;IAC1CC,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,KAAK;IACpBC,iBAAiB,EAAE,WAAW;IAC9BC,YAAY,EAAE,MAAM;IACpBC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,KAAK;IAClBC,IAAI,EAAE;;CAEb"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}