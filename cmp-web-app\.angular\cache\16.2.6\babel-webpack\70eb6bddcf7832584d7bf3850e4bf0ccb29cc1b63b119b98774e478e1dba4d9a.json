{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FieldsetModule } from 'primeng/fieldset';\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ButtonModule } from 'primeng/button';\nimport { AppApnSimListComponent } from \"./list/app.apnsim.list.component\";\nimport { SplitButtonModule } from \"primeng/splitbutton\";\nimport { AppApnSimRoutingModule } from \"./app.apnsim.routing\";\nimport { CustomerService } from \"../../service/customer/CustomerService\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { CommonVnptModule } from \"../common-module/common.module\";\nimport { AppApnSimDetailComponent } from \"./detail/app.apnsim.detail.component\";\nimport { CardModule } from \"primeng/card\";\nimport { ApnSimService } from \"../../service/apn/ApnSimService\";\nimport { PanelModule } from \"primeng/panel\";\nimport { DialogModule } from \"primeng/dialog\";\nimport * as i0 from \"@angular/core\";\nexport class AppApnSimModule {\n  static {\n    this.ɵfac = function AppApnSimModule_Factory(t) {\n      return new (t || AppApnSimModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppApnSimModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CustomerService, ApnSimService],\n      imports: [CommonModule, AppApnSimRoutingModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, SplitButtonModule, CalendarModule, DropdownModule, CommonVnptModule, CardModule, PanelModule, DialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppApnSimModule, {\n    declarations: [AppApnSimListComponent, AppApnSimDetailComponent],\n    imports: [CommonModule, AppApnSimRoutingModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, SplitButtonModule, CalendarModule, DropdownModule, CommonVnptModule, CardModule, PanelModule, DialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "BreadcrumbModule", "FieldsetModule", "FormsModule", "ReactiveFormsModule", "InputTextModule", "ButtonModule", "AppApnSimListComponent", "SplitButtonModule", "AppApnSimRoutingModule", "CustomerService", "CalendarModule", "DropdownModule", "CommonVnptModule", "AppApnSimDetailComponent", "CardModule", "ApnSimService", "PanelModule", "DialogModule", "AppApnSimModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\apn-sim\\app.apnsim.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { FieldsetModule } from 'primeng/fieldset';\r\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { AppApnSimListComponent} from \"./list/app.apnsim.list.component\";\r\nimport {SplitButtonModule} from \"primeng/splitbutton\";\r\nimport {AppApnSimRoutingModule} from \"./app.apnsim.routing\";\r\nimport {SimService} from \"../../service/sim/SimService\";\r\nimport {CustomerService} from \"../../service/customer/CustomerService\";\r\nimport {CalendarModule} from \"primeng/calendar\";\r\nimport {DropdownModule} from \"primeng/dropdown\";\r\nimport {CommonVnptModule} from \"../common-module/common.module\";\r\nimport {AppApnSimDetailComponent} from \"./detail/app.apnsim.detail.component\";\r\nimport {CardModule} from \"primeng/card\";\r\nimport {ApnSimService} from \"../../service/apn/ApnSimService\";\r\nimport {PanelModule} from \"primeng/panel\";\r\nimport {DialogModule} from \"primeng/dialog\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        AppApnSimRoutingModule,\r\n        BreadcrumbModule,\r\n        FieldsetModule,\r\n        FormsModule, ReactiveFormsModule,\r\n        InputTextModule,\r\n        ButtonModule, SplitButtonModule, CalendarModule, DropdownModule, CommonVnptModule, CardModule, PanelModule, DialogModule,\r\n    ],\r\n    declarations: [\r\n        AppApnSimListComponent,\r\n        AppApnSimDetailComponent\r\n    ],\r\n\r\n    providers:[\r\n        CustomerService,\r\n        ApnSimService,\r\n    ]\r\n})\r\nexport class AppApnSimModule{}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,sBAAsB,QAAO,kCAAkC;AACxE,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,sBAAsB,QAAO,sBAAsB;AAE3D,SAAQC,eAAe,QAAO,wCAAwC;AACtE,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,wBAAwB,QAAO,sCAAsC;AAC7E,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,aAAa,QAAO,iCAAiC;AAC7D,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,YAAY,QAAO,gBAAgB;;AAsB3C,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBALd,CACNT,eAAe,EACfM,aAAa,CAChB;MAAAI,OAAA,GAhBGpB,YAAY,EACZS,sBAAsB,EACtBR,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EAAEC,mBAAmB,EAChCC,eAAe,EACfC,YAAY,EAAEE,iBAAiB,EAAEG,cAAc,EAAEC,cAAc,EAAEC,gBAAgB,EAAEE,UAAU,EAAEE,WAAW,EAAEC,YAAY;IAAA;EAAA;;;2EAYnHC,eAAe;IAAAE,YAAA,GATpBd,sBAAsB,EACtBO,wBAAwB;IAAAM,OAAA,GAVxBpB,YAAY,EACZS,sBAAsB,EACtBR,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EAAEC,mBAAmB,EAChCC,eAAe,EACfC,YAAY,EAAEE,iBAAiB,EAAEG,cAAc,EAAEC,cAAc,EAAEC,gBAAgB,EAAEE,UAAU,EAAEE,WAAW,EAAEC,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}