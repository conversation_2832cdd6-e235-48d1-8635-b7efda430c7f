{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { AppSimRoutingModule } from \"./app.diagnose.routing\";\nimport { FieldsetModule } from 'primeng/fieldset';\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ButtonModule } from 'primeng/button';\nimport { CommonVnptModule } from \"../common-module/common.module\";\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DialogModule } from 'primeng/dialog';\nimport { CardModule } from 'primeng/card';\nimport { SplitterModule } from 'primeng/splitter';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { PanelModule } from 'primeng/panel';\nimport { TableModule } from \"primeng/table\";\nimport { DiagnoseComponent } from \"./app.diagnose.comppnent\";\nimport { DiagnoseService } from \"../../service/diagnose/DiagnoseService\";\nimport { SelectButtonModule } from \"primeng/selectbutton\";\nimport * as i0 from \"@angular/core\";\nexport class AppDiagnoseModule {\n  static {\n    this.ɵfac = function AppDiagnoseModule_Factory(t) {\n      return new (t || AppDiagnoseModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppDiagnoseModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [DiagnoseService],\n      imports: [CommonModule, AppSimRoutingModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, DropdownModule, AutoCompleteModule, CalendarModule, DialogModule, CardModule, SplitterModule, ToggleButtonModule, RadioButtonModule, MultiSelectModule, InputTextareaModule, PanelModule, TableModule, SelectButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppDiagnoseModule, {\n    declarations: [DiagnoseComponent],\n    imports: [CommonModule, AppSimRoutingModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, DropdownModule, AutoCompleteModule, CalendarModule, DialogModule, CardModule, SplitterModule, ToggleButtonModule, RadioButtonModule, MultiSelectModule, InputTextareaModule, PanelModule, TableModule, SelectButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "BreadcrumbModule", "AppSimRoutingModule", "FieldsetModule", "FormsModule", "ReactiveFormsModule", "InputTextModule", "ButtonModule", "CommonVnptModule", "SplitButtonModule", "DropdownModule", "AutoCompleteModule", "CalendarModule", "DialogModule", "CardModule", "SplitterModule", "ToggleButtonModule", "RadioButtonModule", "InputTextareaModule", "MultiSelectModule", "PanelModule", "TableModule", "DiagnoseComponent", "DiagnoseService", "SelectButtonModule", "AppDiagnoseModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\diagnose\\app.diagnose.module.ts"], "sourcesContent": ["import {NgModule} from \"@angular/core\";\r\nimport {CommonModule} from \"@angular/common\";\r\nimport {BreadcrumbModule} from 'primeng/breadcrumb';\r\nimport {AppSimRoutingModule} from \"./app.diagnose.routing\";\r\nimport {FieldsetModule} from 'primeng/fieldset';\r\nimport {FormsModule, ReactiveFormsModule} from \"@angular/forms\";\r\nimport {InputTextModule} from 'primeng/inputtext';\r\nimport {ButtonModule} from 'primeng/button';\r\nimport {CommonVnptModule} from \"../common-module/common.module\";\r\nimport {SplitButtonModule} from 'primeng/splitbutton';\r\nimport {DropdownModule} from 'primeng/dropdown';\r\nimport {AutoCompleteModule} from 'primeng/autocomplete';\r\nimport {CalendarModule} from 'primeng/calendar';\r\nimport {DialogModule} from 'primeng/dialog';\r\nimport {CardModule} from 'primeng/card';\r\nimport {SplitterModule} from 'primeng/splitter';\r\nimport {ToggleButtonModule} from 'primeng/togglebutton';\r\nimport {RadioButtonModule} from 'primeng/radiobutton';\r\nimport {InputTextareaModule} from 'primeng/inputtextarea';\r\nimport {MultiSelectModule} from 'primeng/multiselect';\r\nimport {PanelModule} from 'primeng/panel';\r\nimport {TableModule} from \"primeng/table\";\r\nimport {DiagnoseComponent} from \"./app.diagnose.comppnent\";\r\nimport {DiagnoseService} from \"../../service/diagnose/DiagnoseService\";\r\nimport {SelectButtonModule} from \"primeng/selectbutton\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        AppSimRoutingModule,\r\n        BreadcrumbModule,\r\n        FieldsetModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        InputTextModule,\r\n        ButtonModule,\r\n        CommonVnptModule,\r\n        SplitButtonModule,\r\n        DropdownModule,\r\n        AutoCompleteModule,\r\n        CalendarModule,\r\n        DialogModule,\r\n        CardModule,\r\n        SplitterModule,\r\n        ToggleButtonModule,\r\n        RadioButtonModule,\r\n        MultiSelectModule,\r\n        InputTextareaModule,\r\n        PanelModule,\r\n        TableModule,\r\n        SelectButtonModule\r\n    ],\r\n    declarations: [\r\n        DiagnoseComponent,\r\n    ],\r\n    providers: [\r\n        DiagnoseService,\r\n    ]\r\n})\r\nexport class AppDiagnoseModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,gBAAgB,QAAO,oBAAoB;AACnD,SAAQC,mBAAmB,QAAO,wBAAwB;AAC1D,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAC/D,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,kBAAkB,QAAO,sBAAsB;AACvD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,kBAAkB,QAAO,sBAAsB;AACvD,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,mBAAmB,QAAO,uBAAuB;AACzD,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,iBAAiB,QAAO,0BAA0B;AAC1D,SAAQC,eAAe,QAAO,wCAAwC;AACtE,SAAQC,kBAAkB,QAAO,sBAAsB;;AAmCvD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;iBAJf,CACPF,eAAe,CAClB;MAAAG,OAAA,GA7BG1B,YAAY,EACZE,mBAAmB,EACnBD,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,UAAU,EACVC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBE,iBAAiB,EACjBD,mBAAmB,EACnBE,WAAW,EACXC,WAAW,EACXG,kBAAkB;IAAA;EAAA;;;2EASbC,iBAAiB;IAAAE,YAAA,GANtBL,iBAAiB;IAAAI,OAAA,GAzBjB1B,YAAY,EACZE,mBAAmB,EACnBD,gBAAgB,EAChBE,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,UAAU,EACVC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBE,iBAAiB,EACjBD,mBAAmB,EACnBE,WAAW,EACXC,WAAW,EACXG,kBAAkB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}