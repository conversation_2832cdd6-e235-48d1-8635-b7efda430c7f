{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { TestComponent } from \"./test.component\";\nimport { SimService } from \"src/app/service/sim/SimService\";\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class TestRoutingModule {\n  static {\n    this.ɵfac = function TestRoutingModule_Factory(t) {\n      return new (t || TestRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TestRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [SimService, CustomerService],\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: TestComponent\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TestRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "TestComponent", "SimService", "CustomerService", "TestRoutingModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\test\\test.routing.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { TestComponent } from \"./test.component\";\r\nimport { SimService } from \"src/app/service/sim/SimService\";\r\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {path: \"\", component: TestComponent}\r\n        ])\r\n    ],\r\n    exports:[\r\n        RouterModule\r\n    ],\r\n    providers: [\r\n        SimService, CustomerService\r\n    ]\r\n})\r\nexport class TestRoutingModule{}"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0CAA0C;;;AAe1E,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;iBAJf,CACPF,UAAU,EAAEC,eAAe,CAC9B;MAAAE,OAAA,GATGL,YAAY,CAACM,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEP;MAAa,CAAC,CACvC,CAAC,EAGFD,YAAY;IAAA;EAAA;;;2EAMPI,iBAAiB;IAAAC,OAAA,GAAAI,EAAA,CAAAT,YAAA;IAAAU,OAAA,GANtBV,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}