{"ast": null, "code": "export class CustomValidator {\n  static cannotContainSpace(control) {\n    if (control.value) {\n      if (control.value.indexOf(' ') >= 0) {\n        return {\n          cannotContainSpace: true\n        };\n      }\n      return null;\n    }\n    return null;\n  }\n  static numeric(control) {\n    const val = control.value;\n    if (val !== '' && val !== null) {\n      if (!val.toString().match(/^[0-9]+(\\.?[0-9]+)?$/)) {\n        return {\n          invalidNumber: true\n        };\n      }\n      if (val.toString().match(/^[0-9]+(\\.?[0-9]+)?$/) && val < 0) {\n        return {\n          invalidNumber: true\n        };\n      }\n    }\n    return null;\n  }\n  static isInvalidDataUsage(control) {\n    if (control.value) {\n      const value = Number(control.value);\n      if (isNaN(value) || value < 100 || value % 100 !== 0) {\n        return {\n          isInvalidDataUsage: true\n        };\n      }\n    }\n    return null;\n  }\n  static isInvalidSmsUsage(control) {\n    if (control.value) {\n      const value = Number(control.value);\n      if (isNaN(value) || value < 10 || value % 5 !== 0) {\n        return {\n          isInvalidDataUsage: true\n        };\n      }\n    }\n    return null;\n  }\n  static onlySpace(control) {\n    const val = control.value;\n    if (val !== null) {\n      if (!val.replace(/\\s/g, '').length) {\n        return {\n          onlySpace: true\n        };\n      }\n    }\n    return null;\n  }\n  static isBoolean(control) {\n    let booleanValue = ['true', 'false', '0', '1'];\n    if (control.value) {\n      if (!booleanValue.includes(control.value)) {\n        return {\n          isBoolean: true\n        };\n      }\n      return null;\n    }\n    return null;\n  }\n  static isInt(control) {\n    if (control.value !== undefined && control.value !== null && control.value !== '') {\n      if (control.value.includes('.')) return {\n        isInt: true\n      };\n      const value = Number(control.value);\n      const MAX_INT = 2147483647;\n      const MIN_INT = -2147483648;\n      if (!Number.isInteger(value) || value > MAX_INT || value < MIN_INT) {\n        return {\n          isInt: true\n        };\n      }\n    }\n    return null;\n  }\n  static isUnsignedInt(control) {\n    if (control.value !== undefined && control.value !== null && control.value !== '') {\n      if (control.value.includes('.')) return {\n        isUnsignedInt: true\n      };\n      const value = Number(control.value);\n      const MAX_INT = 65535;\n      const MIN_INT = 0;\n      if (!Number.isInteger(value) || value < MIN_INT || value > MAX_INT) {\n        return {\n          isUnsignedInt: true\n        };\n      }\n    }\n    return null;\n  }\n  static isUnsignedLong(control) {\n    if (control.value !== undefined && control.value !== null && control.value !== '') {\n      if (control.value.includes('.')) return {\n        isUnsignedLong: true\n      };\n      const value = Number(control.value);\n      const MAX_UNSIGNED_LONG = 4294967295;\n      const MIN_UNSIGNED_LONG = 0;\n      if (!Number.isInteger(value) || value < 0 || value > MAX_UNSIGNED_LONG || value < MIN_UNSIGNED_LONG) {\n        return {\n          isUnsignedLong: true\n        };\n      }\n    }\n    return null;\n  }\n}", "map": {"version": 3, "names": ["CustomValidator", "cannotContainSpace", "control", "value", "indexOf", "numeric", "val", "toString", "match", "invalidNumber", "isInvalidDataUsage", "Number", "isNaN", "isInvalidSmsUsage", "onlySpace", "replace", "length", "isBoolean", "booleanValue", "includes", "isInt", "undefined", "MAX_INT", "MIN_INT", "isInteger", "isUnsignedInt", "isUnsignedLong", "MAX_UNSIGNED_LONG", "MIN_UNSIGNED_LONG"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\custom-validator\\custom-validator.class.ts"], "sourcesContent": ["import {AbstractControl, ValidationErrors} from '@angular/forms';\r\n\r\nexport class CustomValidator {\r\n    static cannotContainSpace(control: AbstractControl): ValidationErrors | null {\r\n        if (control.value) {\r\n            if ((control.value as string).indexOf(' ') >= 0) {\r\n                return {cannotContainSpace: true};\r\n            }\r\n            return null;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static numeric(control: AbstractControl) {\r\n        const val = control.value;\r\n\r\n        if (val !== '' && val !== null) {\r\n            if (!val.toString().match(/^[0-9]+(\\.?[0-9]+)?$/)) {\r\n                return {invalidNumber: true};\r\n            }\r\n\r\n            if (val.toString().match(/^[0-9]+(\\.?[0-9]+)?$/) && val < 0) {\r\n                return {invalidNumber: true};\r\n            }\r\n\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static isInvalidDataUsage(control: AbstractControl): ValidationErrors | null {\r\n        if(control.value) {\r\n            const value = Number(control.value);\r\n            if (isNaN(value) || value < 100 || value % 100 !== 0) {\r\n                return {isInvalidDataUsage: true};\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static isInvalidSmsUsage(control: AbstractControl): ValidationErrors | null {\r\n        if(control.value) {\r\n            const value = Number(control.value);\r\n            if (isNaN(value) || value < 10 || value % 5 !== 0) {\r\n                return {isInvalidDataUsage: true};\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n\r\n    static onlySpace(control: AbstractControl) {\r\n        const val = control.value;\r\n        if (val !== null) {\r\n            if (!val.replace(/\\s/g, '').length) {\r\n                return {onlySpace: true};\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static isBoolean(control: AbstractControl): ValidationErrors | null {\r\n        let booleanValue: string[] = ['true', 'false', '0', '1']\r\n        if (control.value) {\r\n            if (!booleanValue.includes(control.value)) {\r\n                return {isBoolean: true};\r\n            }\r\n            return null;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static isInt(control: AbstractControl): ValidationErrors | null {\r\n        if (control.value !== undefined && control.value !== null && control.value !== '') {\r\n            if (control.value.includes('.')) return {isInt: true};\r\n            const value = Number(control.value);\r\n            const MAX_INT = 2147483647;\r\n            const MIN_INT = -2147483648;\r\n            if (!Number.isInteger(value) || value > MAX_INT || value < MIN_INT) {\r\n                return {isInt: true};\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static isUnsignedInt(control: AbstractControl): ValidationErrors | null {\r\n        if (control.value !== undefined && control.value !== null && control.value !== '') {\r\n            if (control.value.includes('.')) return {isUnsignedInt: true};\r\n\r\n            const value = Number(control.value);\r\n            const MAX_INT = 65535;\r\n            const MIN_INT = 0;\r\n            if (!Number.isInteger(value) || value < MIN_INT || value > MAX_INT) {\r\n                return {isUnsignedInt: true};\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    static isUnsignedLong(control: AbstractControl): ValidationErrors | null {\r\n        if (control.value !== undefined && control.value !== null && control.value !== '') {\r\n            if (control.value.includes('.')) return {isUnsignedLong: true};\r\n\r\n            const value = Number(control.value);\r\n            const MAX_UNSIGNED_LONG = 4294967295;\r\n            const MIN_UNSIGNED_LONG = 0;\r\n            if (!Number.isInteger(value) || value < 0 || value > MAX_UNSIGNED_LONG || value < MIN_UNSIGNED_LONG) {\r\n                return {isUnsignedLong: true};\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAOA,eAAe;EACxB,OAAOC,kBAAkBA,CAACC,OAAwB;IAC9C,IAAIA,OAAO,CAACC,KAAK,EAAE;MACf,IAAKD,OAAO,CAACC,KAAgB,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC7C,OAAO;UAACH,kBAAkB,EAAE;QAAI,CAAC;;MAErC,OAAO,IAAI;;IAEf,OAAO,IAAI;EACf;EAEA,OAAOI,OAAOA,CAACH,OAAwB;IACnC,MAAMI,GAAG,GAAGJ,OAAO,CAACC,KAAK;IAEzB,IAAIG,GAAG,KAAK,EAAE,IAAIA,GAAG,KAAK,IAAI,EAAE;MAC5B,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC,EAAE;QAC/C,OAAO;UAACC,aAAa,EAAE;QAAI,CAAC;;MAGhC,IAAIH,GAAG,CAACC,QAAQ,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC,IAAIF,GAAG,GAAG,CAAC,EAAE;QACzD,OAAO;UAACG,aAAa,EAAE;QAAI,CAAC;;;IAIpC,OAAO,IAAI;EACf;EAEA,OAAOC,kBAAkBA,CAACR,OAAwB;IAC9C,IAAGA,OAAO,CAACC,KAAK,EAAE;MACd,MAAMA,KAAK,GAAGQ,MAAM,CAACT,OAAO,CAACC,KAAK,CAAC;MACnC,IAAIS,KAAK,CAACT,KAAK,CAAC,IAAIA,KAAK,GAAG,GAAG,IAAIA,KAAK,GAAG,GAAG,KAAK,CAAC,EAAE;QAClD,OAAO;UAACO,kBAAkB,EAAE;QAAI,CAAC;;;IAGzC,OAAO,IAAI;EACf;EAEA,OAAOG,iBAAiBA,CAACX,OAAwB;IAC7C,IAAGA,OAAO,CAACC,KAAK,EAAE;MACd,MAAMA,KAAK,GAAGQ,MAAM,CAACT,OAAO,CAACC,KAAK,CAAC;MACnC,IAAIS,KAAK,CAACT,KAAK,CAAC,IAAIA,KAAK,GAAG,EAAE,IAAIA,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;QAC/C,OAAO;UAACO,kBAAkB,EAAE;QAAI,CAAC;;;IAGzC,OAAO,IAAI;EACf;EAGA,OAAOI,SAASA,CAACZ,OAAwB;IACrC,MAAMI,GAAG,GAAGJ,OAAO,CAACC,KAAK;IACzB,IAAIG,GAAG,KAAK,IAAI,EAAE;MACd,IAAI,CAACA,GAAG,CAACS,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,MAAM,EAAE;QAChC,OAAO;UAACF,SAAS,EAAE;QAAI,CAAC;;;IAGhC,OAAO,IAAI;EACf;EAEA,OAAOG,SAASA,CAACf,OAAwB;IACrC,IAAIgB,YAAY,GAAa,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC;IACxD,IAAIhB,OAAO,CAACC,KAAK,EAAE;MACf,IAAI,CAACe,YAAY,CAACC,QAAQ,CAACjB,OAAO,CAACC,KAAK,CAAC,EAAE;QACvC,OAAO;UAACc,SAAS,EAAE;QAAI,CAAC;;MAE5B,OAAO,IAAI;;IAEf,OAAO,IAAI;EACf;EAEA,OAAOG,KAAKA,CAAClB,OAAwB;IACjC,IAAIA,OAAO,CAACC,KAAK,KAAKkB,SAAS,IAAInB,OAAO,CAACC,KAAK,KAAK,IAAI,IAAID,OAAO,CAACC,KAAK,KAAK,EAAE,EAAE;MAC/E,IAAID,OAAO,CAACC,KAAK,CAACgB,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO;QAACC,KAAK,EAAE;MAAI,CAAC;MACrD,MAAMjB,KAAK,GAAGQ,MAAM,CAACT,OAAO,CAACC,KAAK,CAAC;MACnC,MAAMmB,OAAO,GAAG,UAAU;MAC1B,MAAMC,OAAO,GAAG,CAAC,UAAU;MAC3B,IAAI,CAACZ,MAAM,CAACa,SAAS,CAACrB,KAAK,CAAC,IAAIA,KAAK,GAAGmB,OAAO,IAAInB,KAAK,GAAGoB,OAAO,EAAE;QAChE,OAAO;UAACH,KAAK,EAAE;QAAI,CAAC;;;IAG5B,OAAO,IAAI;EACf;EAEA,OAAOK,aAAaA,CAACvB,OAAwB;IACzC,IAAIA,OAAO,CAACC,KAAK,KAAKkB,SAAS,IAAInB,OAAO,CAACC,KAAK,KAAK,IAAI,IAAID,OAAO,CAACC,KAAK,KAAK,EAAE,EAAE;MAC/E,IAAID,OAAO,CAACC,KAAK,CAACgB,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO;QAACM,aAAa,EAAE;MAAI,CAAC;MAE7D,MAAMtB,KAAK,GAAGQ,MAAM,CAACT,OAAO,CAACC,KAAK,CAAC;MACnC,MAAMmB,OAAO,GAAG,KAAK;MACrB,MAAMC,OAAO,GAAG,CAAC;MACjB,IAAI,CAACZ,MAAM,CAACa,SAAS,CAACrB,KAAK,CAAC,IAAIA,KAAK,GAAGoB,OAAO,IAAIpB,KAAK,GAAGmB,OAAO,EAAE;QAChE,OAAO;UAACG,aAAa,EAAE;QAAI,CAAC;;;IAGpC,OAAO,IAAI;EACf;EAEA,OAAOC,cAAcA,CAACxB,OAAwB;IAC1C,IAAIA,OAAO,CAACC,KAAK,KAAKkB,SAAS,IAAInB,OAAO,CAACC,KAAK,KAAK,IAAI,IAAID,OAAO,CAACC,KAAK,KAAK,EAAE,EAAE;MAC/E,IAAID,OAAO,CAACC,KAAK,CAACgB,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO;QAACO,cAAc,EAAE;MAAI,CAAC;MAE9D,MAAMvB,KAAK,GAAGQ,MAAM,CAACT,OAAO,CAACC,KAAK,CAAC;MACnC,MAAMwB,iBAAiB,GAAG,UAAU;MACpC,MAAMC,iBAAiB,GAAG,CAAC;MAC3B,IAAI,CAACjB,MAAM,CAACa,SAAS,CAACrB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGwB,iBAAiB,IAAIxB,KAAK,GAAGyB,iBAAiB,EAAE;QACjG,OAAO;UAACF,cAAc,EAAE;QAAI,CAAC;;;IAGrC,OAAO,IAAI;EACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}