{"ast": null, "code": "export default {\n  label: {\n    config: {\n      provinceName: \"Province name\",\n      provinceCode: \"Province code\",\n      email: \"Email list\",\n      clue: \"Đầu mối\"\n    },\n    customerName: \"Contact name\",\n    email: \"Contact email\",\n    phone: \"Contact phone\",\n    content: \"Content\",\n    type: \"Type request\",\n    createdDate: \"Created date\",\n    status: \"Status\",\n    typeRequest: \"Type request\",\n    changeSim: \"Subscriber number change\",\n    note: \"Note\",\n    noteAdmin: \"Note admin\",\n    createRequest: \"Create request\",\n    updateRequest: \"Update status request\",\n    transferProcessing: 'Assignee',\n    province: 'Province',\n    fullName: 'Contact name',\n    requestConfigUpdate: \"Edit Config Province\",\n    emailSearch: 'Email',\n    address: 'Address',\n    quantity: 'Quantity SIM',\n    listImsi: 'List IMSI',\n    orderAddress: 'Order Address',\n    detailAddress: 'Detailed Address',\n    district: 'District',\n    commune: 'Commune',\n    updateBy: 'Update By',\n    dateFrom: \"Date From\",\n    dateTo: \"Date To\",\n    imsi: \"IMSI\",\n    allocationDate: \"Allocation Date\",\n    activedDate: \"Actived Date\",\n    notActivated: \"Not Activated\",\n    awaitingActivation: \"Awaiting Activation\",\n    activated: \"Activated\",\n    updateOrderSim: \"Update request status\",\n    processingNotes: \"Processing notes\",\n    orderHistory: \"Order History\",\n    updatedDate: \"Updated Date\",\n    requestActiveSim: \"Create a SIM activation request\",\n    deliveryAddress: 'Delivery Address',\n    viewOrderSim: 'Request Status Details',\n    listNotes: \"List of processing notes\",\n    listImsis: \"List SIM issued\",\n    listNote: \"List Note\",\n    viewDetailReplaceSim: \"View Detail Replace SIM\",\n    viewDetailTestSim: \"View Detail Test SIM\",\n    time: \"Time\",\n    implementer: \"Implementer\",\n    historyOrder: \"Order History\",\n    generalInfo: \"General Information\",\n    enterImsi: \"Enter IMSI information\",\n    listactiveImsis: \"List IMSI\",\n    viewActiveSim: 'Detail request active SIM',\n    imsiByFile: \"Nhập IMSI bằng file\",\n    viewDetailDiagnose: \"View Detail Diagnose\"\n  },\n  menu: {\n    config: \"Config List\",\n    requestMgmt: \"Manage Requests\",\n    requestConfig: \"Edit Config Province\",\n    requestList: \"Request List\",\n    detail: \"Xem chi tiết\",\n    testSim: 'Test SIM Request',\n    replaceSim: 'Replace SIM Request',\n    orderSim: 'Order SIM Request',\n    activeSim: 'Active SIM',\n    listIssuedSim: \"List of issued SIM cards\",\n    errorContent: \"Error content\",\n    diagnose: \"Diagnose Request\"\n  },\n  text: {\n    selectEmail: \"Select email\",\n    addImsi: \"Add Imsi\"\n  },\n  status: {\n    new: 'NEW',\n    received: 'RECEIVED',\n    inProgress: 'IN-PROGRESS',\n    reject: 'REJECT',\n    done: 'DONE'\n  },\n  type: {\n    replaceSim: 'Replace SIM',\n    testSim: 'Test SIM',\n    orderSim: 'Order SIM',\n    activeSim: 'Active SIM'\n  },\n  message: {\n    invalidPhone: 'Subcriber is invalid format',\n    noteChangeSim: 'It is allowed to enter multiple subscription numbers, separated by commas',\n    minQuantity: 'The smallest value is 1',\n    invalidListImsi: 'Incorrect format, imsi series separated by commas',\n    large: \"The file list is larger than \\${limitRow}\\ lines\",\n    empty: \"The list is empty\",\n    searchInfoNull: \"Please enter search information before downloading the file\",\n    largeFile: \"File size must not exceed 100MB\",\n    emptyFile: \"Empty file\",\n    redundantColumns: \"File upload is redundant columns\",\n    missingColumns: \"File upload is missing columns\",\n    wrongSample: \"Wrong sample file format\",\n    wrongImsiFormat: \"IMSI is in wrong format, IMSI must be numeric and cannot exceed 18 characters\",\n    missingImsiInfo: \"Missing IMSI info\",\n    imsiNotExist: \"IMSI not exist\",\n    imsiIsActivated: \"IMSI is Activated\",\n    downloadFile: \"Download file\",\n    isError: \"An error occurred!\",\n    isDownloadMessage: \"The uploaded file has incorrect information, please correct it!\",\n    uploadFile: \"Import file\",\n    maxQuantity: \"This field should have 5 numeric characters or fewer\",\n    imsiMaxLength: \"This field should have 18 numeric characters or fewer\",\n    imsiIsExist: \"IMSI is exist\"\n  },\n  diagnose: {\n    label: {\n      name: \"Full name\",\n      email: \"Email\",\n      phone: \"Phone number\",\n      content: \"Request content\",\n      number: \"Diagnosis subscriber number\",\n      diagnoseNumber: \"Diagnosis phone number\"\n    }\n  }\n};", "map": {"version": 3, "names": ["label", "config", "provinceName", "provinceCode", "email", "clue", "customerName", "phone", "content", "type", "createdDate", "status", "typeRequest", "changeSim", "note", "noteAdmin", "createRequest", "updateRequest", "transferProcessing", "province", "fullName", "requestConfigUpdate", "emailSearch", "address", "quantity", "listImsi", "orderAddress", "detail<PERSON><PERSON><PERSON>", "district", "commune", "updateBy", "dateFrom", "dateTo", "imsi", "allocationDate", "activedDate", "notActivated", "awaitingActivation", "activated", "updateOrderSim", "processingNotes", "orderHistory", "updatedDate", "requestActiveSim", "deliveryAddress", "viewOrderSim", "listNotes", "listImsis", "listNote", "viewDetailReplaceSim", "viewDetailTestSim", "time", "implementer", "historyOrder", "generalInfo", "enterImsi", "listactiveImsis", "viewActiveSim", "imsiByFile", "viewDetailDiagnose", "menu", "requestMgmt", "requestConfig", "requestList", "detail", "testSim", "<PERSON><PERSON><PERSON>", "orderSim", "activeSim", "listIssuedSim", "errorContent", "diagnose", "text", "selectEmail", "addImsi", "new", "received", "inProgress", "reject", "done", "message", "invalidPhone", "noteChangeSim", "minQuantity", "invalidListImsi", "large", "empty", "searchInfoNull", "largeFile", "emptyFile", "redundantColumns", "missingColumns", "wrongSample", "wrongImsiFormat", "missingImsiInfo", "imsiNotExist", "imsiIsActivated", "downloadFile", "isError", "isDownloadMessage", "uploadFile", "maxQuantity", "imsiMaxLength", "imsiIsExist", "name", "number", "diagnoseN<PERSON>ber"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\ticket.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        config: {\r\n            provinceName: \"Province name\",\r\n            provinceCode: \"Province code\",\r\n            email: \"Email list\",\r\n            clue: \"Đầu mối\",\r\n        },\r\n        customerName : \"Contact name\",\r\n        email : \"Contact email\",\r\n        phone : \"Contact phone\",\r\n        content : \"Content\",\r\n        type : \"Type request\",\r\n        createdDate : \"Created date\",\r\n        status : \"Status\",\r\n        typeRequest : \"Type request\",\r\n        changeSim : \"Subscriber number change\",\r\n        note : \"Note\",\r\n        noteAdmin: \"Note admin\",\r\n        createRequest : \"Create request\",\r\n        updateRequest : \"Update status request\",\r\n        transferProcessing : 'Assignee',\r\n        province: 'Province',\r\n        fullName : 'Contact name',\r\n        requestConfigUpdate : \"Edit Config Province\",\r\n        emailSearch : 'Email',\r\n        address: 'Address',\r\n        quantity: 'Quantity SIM',\r\n        listImsi: 'List IMSI',\r\n        orderAddress: 'Order Address',\r\n        detailAddress: 'Detailed Address',\r\n        district: 'District',\r\n        commune: 'Commune',\r\n        updateBy: 'Update By',\r\n        dateFrom: \"Date From\",\r\n        dateTo: \"Date To\",\r\n        imsi: \"IMSI\",\r\n        allocationDate: \"Allocation Date\",\r\n        activedDate: \"Actived Date\",\r\n        notActivated: \"Not Activated\",\r\n        awaitingActivation: \"Awaiting Activation\",\r\n        activated: \"Activated\",\r\n        updateOrderSim: \"Update request status\",\r\n        processingNotes: \"Processing notes\",\r\n        orderHistory: \"Order History\",\r\n        updatedDate: \"Updated Date\",\r\n        requestActiveSim: \"Create a SIM activation request\",\r\n        deliveryAddress: 'Delivery Address',\r\n        viewOrderSim: 'Request Status Details',\r\n        listNotes: \"List of processing notes\",\r\n        listImsis: \"List SIM issued\",\r\n        listNote : \"List Note\",\r\n        viewDetailReplaceSim : \"View Detail Replace SIM\",\r\n        viewDetailTestSim : \"View Detail Test SIM\",\r\n        time : \"Time\",\r\n        implementer :\"Implementer\",\r\n        historyOrder : \"Order History\",\r\n        generalInfo: \"General Information\",\r\n        enterImsi: \"Enter IMSI information\",\r\n        listactiveImsis: \"List IMSI\",\r\n        viewActiveSim: 'Detail request active SIM',\r\n        imsiByFile: \"Nhập IMSI bằng file\",\r\n        viewDetailDiagnose: \"View Detail Diagnose\"\r\n    },\r\n    menu : {\r\n        config : \"Config List\",\r\n        requestMgmt : \"Manage Requests\",\r\n        requestConfig : \"Edit Config Province\",\r\n        requestList :\"Request List\",\r\n        detail : \"Xem chi tiết\",\r\n        testSim : 'Test SIM Request',\r\n        replaceSim : 'Replace SIM Request',\r\n        orderSim: 'Order SIM Request',\r\n        activeSim: 'Active SIM',\r\n        listIssuedSim: \"List of issued SIM cards\",\r\n        errorContent: \"Error content\",\r\n        diagnose: \"Diagnose Request\",\r\n    },\r\n    text : {\r\n        selectEmail : \"Select email\",\r\n        addImsi: \"Add Imsi\",\r\n    },\r\n    status : {\r\n        new : 'NEW',\r\n        received : 'RECEIVED',\r\n        inProgress : 'IN-PROGRESS',\r\n        reject : 'REJECT',\r\n        done : 'DONE'\r\n    },\r\n    type : {\r\n        replaceSim : 'Replace SIM',\r\n        testSim : 'Test SIM',\r\n        orderSim: 'Order SIM',\r\n        activeSim: 'Active SIM'\r\n    }\r\n    ,message : {\r\n        invalidPhone : 'Subcriber is invalid format',\r\n        noteChangeSim : 'It is allowed to enter multiple subscription numbers, separated by commas',\r\n        minQuantity: 'The smallest value is 1',\r\n        invalidListImsi: 'Incorrect format, imsi series separated by commas',\r\n        large: \"The file list is larger than \\${limitRow}\\ lines\",\r\n        empty: \"The list is empty\",\r\n        searchInfoNull: \"Please enter search information before downloading the file\",\r\n        largeFile: \"File size must not exceed 100MB\",\r\n        emptyFile: \"Empty file\",\r\n        redundantColumns: \"File upload is redundant columns\",\r\n        missingColumns: \"File upload is missing columns\",\r\n        wrongSample: \"Wrong sample file format\",\r\n        wrongImsiFormat: \"IMSI is in wrong format, IMSI must be numeric and cannot exceed 18 characters\",\r\n        missingImsiInfo: \"Missing IMSI info\",\r\n        imsiNotExist: \"IMSI not exist\",\r\n        imsiIsActivated: \"IMSI is Activated\",\r\n        downloadFile: \"Download file\",\r\n        isError: \"An error occurred!\",\r\n        isDownloadMessage: \"The uploaded file has incorrect information, please correct it!\",\r\n        uploadFile: \"Import file\",\r\n        maxQuantity: \"This field should have 5 numeric characters or fewer\",\r\n        imsiMaxLength: \"This field should have 18 numeric characters or fewer\",\r\n        imsiIsExist: \"IMSI is exist\",\r\n    },\r\n    diagnose: {\r\n        label: {\r\n            name: \"Full name\",\r\n            email: \"Email\",\r\n            phone: \"Phone number\",\r\n            content: \"Request content\",\r\n            number: \"Diagnosis subscriber number\",\r\n            diagnoseNumber: \"Diagnosis phone number\",\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,MAAM,EAAE;MACJC,YAAY,EAAE,eAAe;MAC7BC,YAAY,EAAE,eAAe;MAC7BC,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE;KACT;IACDC,YAAY,EAAG,cAAc;IAC7BF,KAAK,EAAG,eAAe;IACvBG,KAAK,EAAG,eAAe;IACvBC,OAAO,EAAG,SAAS;IACnBC,IAAI,EAAG,cAAc;IACrBC,WAAW,EAAG,cAAc;IAC5BC,MAAM,EAAG,QAAQ;IACjBC,WAAW,EAAG,cAAc;IAC5BC,SAAS,EAAG,0BAA0B;IACtCC,IAAI,EAAG,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAG,gBAAgB;IAChCC,aAAa,EAAG,uBAAuB;IACvCC,kBAAkB,EAAG,UAAU;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAG,cAAc;IACzBC,mBAAmB,EAAG,sBAAsB;IAC5CC,WAAW,EAAG,OAAO;IACrBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,kBAAkB;IACjCC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,iBAAiB;IACjCC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,eAAe;IAC7BC,kBAAkB,EAAE,qBAAqB;IACzCC,SAAS,EAAE,WAAW;IACtBC,cAAc,EAAE,uBAAuB;IACvCC,eAAe,EAAE,kBAAkB;IACnCC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,cAAc;IAC3BC,gBAAgB,EAAE,iCAAiC;IACnDC,eAAe,EAAE,kBAAkB;IACnCC,YAAY,EAAE,wBAAwB;IACtCC,SAAS,EAAE,0BAA0B;IACrCC,SAAS,EAAE,iBAAiB;IAC5BC,QAAQ,EAAG,WAAW;IACtBC,oBAAoB,EAAG,yBAAyB;IAChDC,iBAAiB,EAAG,sBAAsB;IAC1CC,IAAI,EAAG,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAG,eAAe;IAC9BC,WAAW,EAAE,qBAAqB;IAClCC,SAAS,EAAE,wBAAwB;IACnCC,eAAe,EAAE,WAAW;IAC5BC,aAAa,EAAE,2BAA2B;IAC1CC,UAAU,EAAE,qBAAqB;IACjCC,kBAAkB,EAAE;GACvB;EACDC,IAAI,EAAG;IACH3D,MAAM,EAAG,aAAa;IACtB4D,WAAW,EAAG,iBAAiB;IAC/BC,aAAa,EAAG,sBAAsB;IACtCC,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAG,cAAc;IACvBC,OAAO,EAAG,kBAAkB;IAC5BC,UAAU,EAAG,qBAAqB;IAClCC,QAAQ,EAAE,mBAAmB;IAC7BC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAE,0BAA0B;IACzCC,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE;GACb;EACDC,IAAI,EAAG;IACHC,WAAW,EAAG,cAAc;IAC5BC,OAAO,EAAE;GACZ;EACD/D,MAAM,EAAG;IACLgE,GAAG,EAAG,KAAK;IACXC,QAAQ,EAAG,UAAU;IACrBC,UAAU,EAAG,aAAa;IAC1BC,MAAM,EAAG,QAAQ;IACjBC,IAAI,EAAG;GACV;EACDtE,IAAI,EAAG;IACHyD,UAAU,EAAG,aAAa;IAC1BD,OAAO,EAAG,UAAU;IACpBE,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;GACd;EACAY,OAAO,EAAG;IACPC,YAAY,EAAG,6BAA6B;IAC5CC,aAAa,EAAG,2EAA2E;IAC3FC,WAAW,EAAE,yBAAyB;IACtCC,eAAe,EAAE,mDAAmD;IACpEC,KAAK,EAAE,kDAAkD;IACzDC,KAAK,EAAE,mBAAmB;IAC1BC,cAAc,EAAE,6DAA6D;IAC7EC,SAAS,EAAE,iCAAiC;IAC5CC,SAAS,EAAE,YAAY;IACvBC,gBAAgB,EAAE,kCAAkC;IACpDC,cAAc,EAAE,gCAAgC;IAChDC,WAAW,EAAE,0BAA0B;IACvCC,eAAe,EAAE,+EAA+E;IAChGC,eAAe,EAAE,mBAAmB;IACpCC,YAAY,EAAE,gBAAgB;IAC9BC,eAAe,EAAE,mBAAmB;IACpCC,YAAY,EAAE,eAAe;IAC7BC,OAAO,EAAE,oBAAoB;IAC7BC,iBAAiB,EAAE,iEAAiE;IACpFC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,sDAAsD;IACnEC,aAAa,EAAE,uDAAuD;IACtEC,WAAW,EAAE;GAChB;EACDhC,QAAQ,EAAE;IACNvE,KAAK,EAAE;MACHwG,IAAI,EAAE,WAAW;MACjBpG,KAAK,EAAE,OAAO;MACdG,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,iBAAiB;MAC1BiG,MAAM,EAAE,6BAA6B;MACrCC,cAAc,EAAE;;;CAG3B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}