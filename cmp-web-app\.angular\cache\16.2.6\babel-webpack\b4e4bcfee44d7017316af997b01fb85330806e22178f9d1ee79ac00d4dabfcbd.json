{"ast": null, "code": "import global from \"./global\";\nimport dashboard from \"./dashboard\";\nimport sim from \"./sim\";\nimport contract from \"./contract\";\nimport groupSim from \"./groupSim\";\nimport account from \"./account\";\nimport roles from \"./roles\";\nimport error from \"./error\";\nimport permission from \"./permission\";\nimport ratingPlan from \"./rating-plan\";\nimport historyRegisterPlan from \"./history-register-plan\";\nimport device from \"./device\";\nimport login from \"./login\";\nimport alert from \"./alert\";\nimport customer from \"./customer\";\nimport report from \"./report\";\nimport chart from \"./chart\";\nimport ticket from \"./ticket\";\nimport datapool from \"./datapool\";\nimport logs from \"./logs\";\nimport recharge from \"./recharge\";\nimport diagnose from \"./diagnose\";\nimport apiLog from \"./apiLog\";\nexport const vi = {\n  global,\n  dashboard,\n  sim,\n  groupSim,\n  contract,\n  account,\n  error,\n  roles,\n  permission,\n  ratingPlan,\n  historyRegisterPlan,\n  device,\n  login,\n  alert,\n  customer,\n  report,\n  chart,\n  ticket,\n  datapool,\n  logs,\n  recharge,\n  diagnose,\n  apiLog\n};", "map": {"version": 3, "names": ["global", "dashboard", "sim", "contract", "groupSim", "account", "roles", "error", "permission", "ratingPlan", "historyRegisterPlan", "device", "login", "alert", "customer", "report", "chart", "ticket", "datapool", "logs", "recharge", "diagnose", "apiLog", "vi"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\index.ts"], "sourcesContent": ["import global from \"./global\";\r\nimport dashboard from \"./dashboard\";\r\nimport sim from \"./sim\";\r\nimport contract from \"./contract\";\r\nimport groupSim from \"./groupSim\";\r\n\r\nimport account from \"./account\";\r\nimport roles from \"./roles\";\r\nimport error from \"./error\";\r\nimport permission from \"./permission\";\r\nimport ratingPlan from \"./rating-plan\";\r\nimport historyRegisterPlan from \"./history-register-plan\";\r\nimport device from \"./device\";\r\nimport login from \"./login\";\r\nimport alert from \"./alert\";\r\nimport customer from \"./customer\";\r\nimport report from \"./report\";\r\nimport chart from \"./chart\";\r\nimport ticket from \"./ticket\";\r\nimport datapool from \"./datapool\";\r\nimport logs from \"./logs\";\r\nimport recharge from \"./recharge\";\r\nimport diagnose from \"./diagnose\"\r\nimport apiLog from \"./apiLog\";\r\n\r\nexport const vi = {\r\n    global,\r\n    dashboard,\r\n    sim,\r\n    groupSim,\r\n    contract,\r\n    account,\r\n    error,\r\n    roles,\r\n    permission,\r\n    ratingPlan,\r\n    historyRegisterPlan,\r\n    device,\r\n    login,\r\n    alert,\r\n    customer,\r\n    report,\r\n    chart,\r\n    ticket,\r\n    datapool,\r\n    logs,\r\n    recharge,\r\n    diagnose,\r\n    apiLog\r\n}\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AAEjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,mBAAmB,MAAM,yBAAyB;AACzD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAE7B,OAAO,MAAMC,EAAE,GAAG;EACdvB,MAAM;EACNC,SAAS;EACTC,GAAG;EACHE,QAAQ;EACRD,QAAQ;EACRE,OAAO;EACPE,KAAK;EACLD,KAAK;EACLE,UAAU;EACVC,UAAU;EACVC,mBAAmB;EACnBC,MAAM;EACNC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNC,KAAK;EACLC,MAAM;EACNC,QAAQ;EACRC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC;CACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}