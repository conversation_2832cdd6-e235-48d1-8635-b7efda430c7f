{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nexport class PersonalDataProtectionContentComponent extends ComponentBase {\n  constructor(formBuilder, injector) {\n    super(injector);\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function PersonalDataProtectionContentComponent_Factory(t) {\n      return new (t || PersonalDataProtectionContentComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalDataProtectionContentComponent,\n      selectors: [[\"personal-data-protection-policy-content\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 570,\n      vars: 0,\n      consts: [[\"id\", \"page_content_inner\"], [2, \"text-align\", \"center\"], [2, \"font-size\", \"13.999999999999998pt\"], [2, \"font-size\", \"13pt\"], [2, \"text-align\", \"justify\"], [2, \"font-size\", \"12pt\"], [2, \"font-size\", \"6.999999999999999pt\"], [\"id\", \"_com_1\", \"language\", \"JavaScript\"]],\n      template: function PersonalDataProtectionContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"p\", 1)(3, \"strong\")(4, \"span\", 2);\n          i0.ɵɵtext(5, \"CH\\u00CDNH S\\u00C1CH B\\u1EA2O V\\u1EC6 D\\u1EEE LI\\u1EC6U C\\u00C1 NH\\u00C2N \\u0110\\u1ED0I V\\u1EDAI KH\\u00C1CH H\\u00C0NG C\\u1EE6A VNPT V\\u00C0 C\\u00D4NG TY CON C\\u1EE6A VNPT\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"p\", 1)(7, \"span\", 3);\n          i0.ɵɵtext(8, \"Ng\\u00E0y c\\u1EADp nh\\u1EADt: 01/07/2023\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"p\", 4)(10, \"strong\")(11, \"span\", 5);\n          i0.ɵɵtext(12, \"\\u00A0\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"p\", 4)(14, \"span\", 5);\n          i0.ɵɵtext(15, \"Ch\\u00EDnh s\\u00E1ch b\\u1EA3o v\\u1EC7 d\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n \\u0111\\u1ED1i v\\u1EDBi Kh\\u00E1ch h\\u00E0ng c\\u1EE7a T\\u1EADp \\u0111o\\u00E0n B\\u01B0u ch\\u00EDnh Vi\\u1EC5n th\\u00F4ng Vi\\u1EC7t Nam, c\\u00E1c C\\u00F4ng ty con c\\u1EE7a T\\u1EADp \\u0111o\\u00E0n B\\u01B0u ch\\u00EDnh Vi\\u1EC5n th\\u00F4ng Vi\\u1EC7t Nam (sau \\u0111\\u00E2y g\\u1ECDi t\\u1EAFt l\\u00E0 \\u201C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"strong\")(17, \"span\", 5);\n          i0.ɵɵtext(18, \"Ch\\u00EDnh s\\u00E1ch\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"span\", 5);\n          i0.ɵɵtext(20, \"\\u201D) nh\\u1EB1m m\\u1EE5c \\u0111\\u00EDch th\\u00F4ng b\\u00E1o v\\u1EDBi Kh\\u00E1ch h\\u00E0ng nh\\u1EEFng D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng do T\\u1EADp \\u0111o\\u00E0n B\\u01B0u ch\\u00EDnh Vi\\u1EC5n th\\u00F4ng Vi\\u1EC7t Nam\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"strong\")(22, \"span\", 5);\n          i0.ɵɵtext(23, \"(\\u201CVNPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"span\", 5);\n          i0.ɵɵtext(25, \"\\u201D) v\\u00E0/ho\\u1EB7c do c\\u00E1c C\\u00F4ng ty con c\\u00F3 100% v\\u1ED1n \\u0111i\\u1EC1u l\\u1EC7 c\\u1EE7a VNPT (\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"strong\")(27, \"span\", 5);\n          i0.ɵɵtext(28, \"\\u201CC\\u00F4ng ty con c\\u1EE7a VNPT\\u201D)\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"span\", 5);\n          i0.ɵɵtext(30, \"x\\u1EED l\\u00FD, m\\u1EE5c \\u0111\\u00EDch x\\u1EED l\\u00FD, c\\u00E1ch th\\u1EE9c x\\u1EED l\\u00FD, th\\u1EDDi gian l\\u01B0u tr\\u1EEF, quy\\u1EC1n, ngh\\u0129a v\\u1EE5 c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u1ED1i v\\u1EDBi D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a m\\u00ECnh theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt Vi\\u1EC7t Nam v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n v.v. Ch\\u00EDnh s\\u00E1ch n\\u00E0y \\u0111\\u1ED3ng th\\u1EDDi \\u0111\\u01B0a ra c\\u00E1c khuy\\u1EBFn ngh\\u1ECB \\u0111\\u1EC3 gi\\u00FAp Kh\\u00E1ch h\\u00E0ng n\\u00E2ng cao nh\\u1EADn th\\u1EE9c v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"p\", 4)(32, \"span\", 5);\n          i0.ɵɵtext(33, \"Ch\\u00EDnh s\\u00E1ch n\\u00E0y l\\u00E0 m\\u1ED9t ph\\u1EA7n kh\\u00F4ng th\\u1EC3 t\\u00E1ch r\\u1EDDi c\\u1EE7a c\\u00E1c b\\u1EA3n H\\u1EE3p \\u0111\\u1ED3ng, \\u0110i\\u1EC1u ki\\u1EC7n giao d\\u1ECBch chung, \\u0110i\\u1EC1u kho\\u1EA3n v\\u00E0 \\u0110i\\u1EC1u ki\\u1EC7n s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. Ch\\u00EDnh s\\u00E1ch n\\u00E0y \\u0111\\u01B0\\u1EE3c \\u00E1p d\\u1EE5ng cho to\\u00E0n b\\u1ED9 c\\u00E1c n\\u1EC1n t\\u1EA3ng bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n b\\u1EDFi: trang th\\u00F4ng tin \\u0111i\\u1EC7n t\\u1EED, k\\u00EAnh giao di\\u1EC7n, ph\\u01B0\\u01A1ng ti\\u1EC7n, c\\u00F4ng c\\u1EE5 tr\\u00EAn c\\u00E1c website/wapsite/\\u1EE9ng d\\u1EE5ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, vi\\u1EC7c t\\u01B0 v\\u1EA5n v\\u00E0 k\\u00FD k\\u1EBFt h\\u1EE3p \\u0111\\u1ED3ng cung c\\u1EA5p d\\u1ECBch v\\u1EE5 t\\u1EA1i c\\u00E1c \\u0111i\\u1EC3m cung c\\u1EA5p d\\u1ECBch v\\u1EE5 vi\\u1EC5n th\\u00F4ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, t\\u1EA1i \\u0111\\u1ECBa ch\\u1EC9 do Kh\\u00E1ch h\\u00E0ng ch\\u1EC9 \\u0111\\u1ECBnh v\\u00E0/ho\\u1EB7c t\\u1EA1i m\\u1ED9t \\u0111\\u1ECBa \\u0111i\\u1EC3m kh\\u00E1c do Kh\\u00E1ch h\\u00E0ng ho\\u1EB7c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT l\\u1EF1a ch\\u1ECDn. \\u00A0\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"p\", 4)(35, \"span\", 5);\n          i0.ɵɵtext(36, \"Trong ph\\u1EA1m vi ph\\u00E1p lu\\u1EADt cho ph\\u00E9p, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 \\u0111i\\u1EC1u ch\\u1EC9nh Ch\\u00EDnh s\\u00E1ch n\\u00E0y v\\u00E0o b\\u1EA5t k\\u1EF3 th\\u1EDDi \\u0111i\\u1EC3m n\\u00E0o \\u0111\\u1ED3ng th\\u1EDDi \\u0111\\u0103ng t\\u1EA3i c\\u00F4ng khai Ch\\u00EDnh s\\u00E1ch \\u0111\\u01B0\\u1EE3c \\u0111i\\u1EC1u ch\\u1EC9nh tr\\u00EAn c\\u00E1c k\\u00EAnh th\\u00F4ng tin ch\\u00EDnh th\\u1ED1ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"p\", 4)(38, \"span\", 5);\n          i0.ɵɵtext(39, \"B\\u1EB1ng vi\\u1EC7c t\\u00EDch v\\u00E0o \\u00F4\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"em\")(41, \"span\", 5);\n          i0.ɵɵtext(42, \"\\\"T\\u00F4i \\u0111\\u00E3 \\u0111\\u1ECDc v\\u00E0 ch\\u1EA5p thu\\u1EADn\\\"\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"span\", 5);\n          i0.ɵɵtext(44, \"ho\\u1EB7c\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"em\")(46, \"span\", 5);\n          i0.ɵɵtext(47, \"\\\"T\\u00F4i \\u0111\\u1ED3ng \\u00FD v\\u1EDBi Ch\\u00EDnh s\\u00E1ch, \\u0110i\\u1EC1u kho\\u1EA3n s\\u1EED d\\u1EE5ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT\\u201D\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"span\", 5);\n          i0.ɵɵtext(49, \"ho\\u1EB7c b\\u1EB1ng vi\\u1EC7c k\\u00FD k\\u1EBFt h\\u1EE3p \\u0111\\u1ED3ng v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 d\\u1EABn chi\\u1EBFu t\\u1EDBi Ch\\u00EDnh s\\u00E1ch n\\u00E0y (v\\u00E0 c\\u00E1c b\\u1EA3n s\\u1EEDa \\u0111\\u1ED5i k\\u00E8m theo), ho\\u1EB7c b\\u1EB1ng vi\\u1EC7c ti\\u1EBFp t\\u1EE5c \\u0111\\u0103ng k\\u00FD, \\u0111\\u0103ng nh\\u1EADp, s\\u1EED d\\u1EE5ng website/wapsite/\\u1EE9ng d\\u1EE5ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ho\\u1EB7c s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT m\\u00E0 kh\\u00F4ng c\\u00F3 b\\u1EA5t k\\u00EC khi\\u1EBFu n\\u1EA1i n\\u00E0o \\u0111\\u1ED1i v\\u1EDBi Ch\\u00EDnh s\\u00E1ch n\\u00E0y (v\\u00E0 c\\u00E1c b\\u1EA3n s\\u1EEDa \\u0111\\u1ED5i k\\u00E8m theo), Kh\\u00E1ch h\\u00E0ng x\\u00E1c nh\\u1EADn r\\u1EB1ng \\u0111\\u00E3 \\u0111\\u1ECDc k\\u1EF9, hi\\u1EC3u r\\u00F5 v\\u00E0 ch\\u1EA5p thu\\u1EADn to\\u00E0n b\\u1ED9 n\\u1ED9i dung Ch\\u00EDnh s\\u00E1ch b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"p\", 4)(51, \"strong\")(52, \"span\", 5);\n          i0.ɵɵtext(53, \"\\u0110i\\u1EC1u 1.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"span\", 5);\n          i0.ɵɵtext(55, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"strong\")(57, \"span\", 5);\n          i0.ɵɵtext(58, \"Gi\\u1EA3i th\\u00EDch t\\u1EEB ng\\u1EEF v\\u00E0 c\\u00E1c t\\u1EEB vi\\u1EBFt t\\u1EAFt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"p\", 4)(60, \"span\", 5);\n          i0.ɵɵtext(61, \"Trong ph\\u1EA1m vi Ch\\u00EDnh s\\u00E1ch n\\u00E0y, c\\u00E1c thu\\u1EADt ng\\u1EEF d\\u01B0\\u1EDBi \\u0111\\u00E2y \\u0111\\u01B0\\u1EE3c hi\\u1EC3u v\\u00E0 gi\\u1EA3i th\\u00EDch nh\\u01B0 sau:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"p\", 4)(63, \"span\", 5);\n          i0.ɵɵtext(64, \"1.1.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\", 6);\n          i0.ɵɵtext(66, \"\\u00A0 \\u00A0 \\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"strong\")(68, \"span\", 5);\n          i0.ɵɵtext(69, \"\\u00A0VNPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"span\", 5);\n          i0.ɵɵtext(71, \"\\u00A0l\\u00E0 T\\u1EADp \\u0110o\\u00E0n B\\u01B0u Ch\\u00EDnh Vi\\u1EC5n Th\\u00F4ng Vi\\u1EC7t Nam v\\u00E0 c\\u00E1c \\u0111\\u01A1n v\\u1ECB tr\\u1EF1c thu\\u1ED9c T\\u1EADp \\u0110o\\u00E0n B\\u01B0u Ch\\u00EDnh Vi\\u1EC5n Th\\u00F4ng Vi\\u1EC7t Nam.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"p\", 4)(73, \"span\", 5);\n          i0.ɵɵtext(74, \"1.2.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"span\", 6);\n          i0.ɵɵtext(76, \"\\u00A0 \\u00A0 \\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"strong\")(78, \"span\", 5);\n          i0.ɵɵtext(79, \"C\\u00F4ng ty con c\\u1EE7a VNPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"span\", 5);\n          i0.ɵɵtext(81, \"\\u00A0l\\u00E0 c\\u00E1c doanh nghi\\u1EC7p do VNPT s\\u1EDF h\\u1EEFu 100% v\\u1ED1n \\u0111i\\u1EC1u l\\u1EC7. C\\u00F4ng ty con c\\u1EE7a VNPT bao g\\u1ED3m T\\u1ED5ng c\\u00F4ng ty D\\u1ECBch v\\u1EE5 Vi\\u1EC5n th\\u00F4ng (VNPT VinaPhone) v\\u00E0 T\\u1ED5ng c\\u00F4ng ty Truy\\u1EC1n th\\u00F4ng (VNPT Media).\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"p\", 4)(83, \"span\", 5);\n          i0.ɵɵtext(84, \"1.3.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"span\", 6);\n          i0.ɵɵtext(86, \"\\u00A0 \\u00A0 \\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"strong\")(88, \"span\", 5);\n          i0.ɵɵtext(89, \"Kh\\u00E1ch h\\u00E0ng\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(90, \"span\", 5);\n          i0.ɵɵtext(91, \"l\\u00E0:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"p\", 4)(93, \"span\", 5);\n          i0.ɵɵtext(94, \"- C\\u00E1 nh\\u00E2n ho\\u1EB7c ng\\u01B0\\u1EDDi \\u0111\\u1EA1i di\\u1EC7n h\\u1EE3p ph\\u00E1p c\\u1EE7a c\\u00E1 nh\\u00E2n s\\u1EED d\\u1EE5ng v\\u00E0/ho\\u1EB7c quan t\\u00E2m t\\u1EDBi c\\u00E1c s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"p\", 4)(96, \"span\", 5);\n          i0.ɵɵtext(97, \"- C\\u00E1 nh\\u00E2n ho\\u1EB7c ng\\u01B0\\u1EDDi \\u0111\\u1EA1i di\\u1EC7n h\\u1EE3p ph\\u00E1p c\\u1EE7a c\\u00E1 nh\\u00E2n \\u0111\\u00E3 truy c\\u1EADp v\\u00E0/ho\\u1EB7c \\u0111\\u0103ng k\\u00FD t\\u00E0i kho\\u1EA3n t\\u1EA1i c\\u00E1c website/wapsite/\\u1EE9ng d\\u1EE5ng thu\\u1ED9c quy\\u1EC1n s\\u1EDF h\\u1EEFu c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"p\", 4)(99, \"span\", 5);\n          i0.ɵɵtext(100, \"1.4.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"span\", 6);\n          i0.ɵɵtext(102, \"\\u00A0 \\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"span\", 5);\n          i0.ɵɵtext(104, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"strong\")(106, \"span\", 5);\n          i0.ɵɵtext(107, \"S\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"span\", 5);\n          i0.ɵɵtext(109, \"\\u00A0l\\u00E0:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(110, \"p\", 4)(111, \"span\", 5);\n          i0.ɵɵtext(112, \"- S\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 do VNPT ho\\u1EB7c c\\u00E1c C\\u00F4ng ty con c\\u1EE7a VNPT tr\\u1EF1c ti\\u1EBFp nghi\\u00EAn c\\u1EE9u, ph\\u00E1t tri\\u1EC3n v\\u00E0 cung c\\u1EA5p cho Kh\\u00E1ch h\\u00E0ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"p\", 4)(114, \"span\", 5);\n          i0.ɵɵtext(115, \"- S\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 do VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT h\\u1EE3p t\\u00E1c v\\u1EDBi \\u0111\\u1ED1i t\\u00E1c \\u0111\\u1EC3 cung c\\u1EA5p cho Kh\\u00E1ch h\\u00E0ng.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(116, \"p\", 4)(117, \"strong\")(118, \"span\", 5);\n          i0.ɵɵtext(119, \"\\u0110i\\u1EC1u 2. X\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(120, \"p\", 4)(121, \"span\", 5);\n          i0.ɵɵtext(122, \"1.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"strong\")(124, \"span\", 5);\n          i0.ɵɵtext(125, \"\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"span\", 5);\n          i0.ɵɵtext(127, \"VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ti\\u1EBFn h\\u00E0nh x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n trong nh\\u1EEFng tr\\u01B0\\u1EDDng h\\u1EE3p d\\u01B0\\u1EDBi \\u0111\\u00E2y bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n b\\u1EDFi:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(128, \"p\", 4)(129, \"span\", 5);\n          i0.ɵɵtext(130, \"a) Khi Kh\\u00E1ch h\\u00E0ng ho\\u1EB7c ng\\u01B0\\u1EDDi \\u0111\\u1EA1i di\\u1EC7n h\\u1EE3p ph\\u00E1p c\\u1EE7a Kh\\u00E1ch h\\u00E0ng li\\u00EAn h\\u1EC7 v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u1EC3 y\\u00EAu c\\u1EA7u t\\u01B0 v\\u1EA5n s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ho\\u1EB7c b\\u00E0y t\\u1ECF s\\u1EF1 quan t\\u00E2m t\\u1EDBi c\\u00E1c s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(131, \"p\", 4)(132, \"span\", 5);\n          i0.ɵɵtext(133, \"b) Khi Kh\\u00E1ch h\\u00E0ng k\\u00FD k\\u1EBFt h\\u1EE3p \\u0111\\u1ED3ng, \\u0111\\u0103ng k\\u00FD, s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(134, \"p\", 4)(135, \"span\", 5);\n          i0.ɵɵtext(136, \"c) Khi Kh\\u00E1ch h\\u00E0ng truy c\\u1EADp v\\u00E0/ho\\u1EB7c \\u0111\\u0103ng k\\u00FD t\\u00E0i kho\\u1EA3n t\\u1EA1i c\\u00E1c website/wapsite/\\u1EE9ng d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(137, \"p\", 4)(138, \"span\", 5);\n          i0.ɵɵtext(139, \"d) Khi Kh\\u00E1ch h\\u00E0ng \\u0111\\u1ED3ng thu\\u1EADn cung c\\u1EA5p D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n cho VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT qua c\\u00E1c ngu\\u1ED3n c\\u00F4ng khai, bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n: website/wapsite/\\u1EE9ng d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT; cu\\u1ED9c h\\u1ECDp, s\\u1EF1 ki\\u1EC7n, h\\u1ED9i th\\u1EA3o, h\\u1ED9i ngh\\u1ECB, c\\u00E1c m\\u1EA1ng x\\u00E3 h\\u1ED9i, hay ch\\u01B0\\u01A1ng tr\\u00ECnh \\u0111\\u1ED1i tho\\u1EA1i, th\\u1EA3o lu\\u1EADn do VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT t\\u1ED5 ch\\u1EE9c, t\\u00E0i tr\\u1EE3 ho\\u1EB7c tham d\\u1EF1 v\\u00E0/ho\\u1EB7c t\\u1EEB c\\u00E1c t\\u1EC7p l\\u01B0u tr\\u1EEF (cookies) ghi nh\\u1EADn \\u0111\\u01B0\\u1EE3c tr\\u00EAn website c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(140, \"p\", 4)(141, \"span\", 5);\n          i0.ɵɵtext(142, \"e) Khi kh\\u00E1ch h\\u00E0ng c\\u1EE7a m\\u1ED9t t\\u1ED5 ch\\u1EE9c, doanh nghi\\u1EC7p cho ph\\u00E9p t\\u1ED5 ch\\u1EE9c, doanh nghi\\u1EC7p \\u0111\\u00F3 chia s\\u1EBB d\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a kh\\u00E1ch h\\u00E0ng v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(143, \"p\", 4)(144, \"span\", 5);\n          i0.ɵɵtext(145, \"g) L\\u00E0 kh\\u00E1ch h\\u00E0ng c\\u1EE7a m\\u1ED9t t\\u1ED5 ch\\u1EE9c, doanh nghi\\u1EC7p \\u0111\\u01B0\\u1EE3c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT th\\u1EF1c hi\\u1EC7n g\\u00F3p v\\u1ED1n, mua c\\u1ED5 ph\\u1EA7n; ho\\u1EB7c l\\u00E0 kh\\u00E1ch h\\u00E0ng c\\u1EE7a m\\u1ED9t t\\u1ED5 ch\\u1EE9c, doanh nghi\\u1EC7p c\\u00F3 ho\\u1EA1t \\u0111\\u1ED9ng h\\u1EE3p t\\u00E1c cung c\\u1EA5p s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(146, \"p\", 4)(147, \"span\", 5);\n          i0.ɵɵtext(148, \"h) Khi c\\u00F3 y\\u00EAu c\\u1EA7u c\\u1EE7a c\\u00E1c c\\u01A1 quan nh\\u00E0 n\\u01B0\\u1EDBc c\\u00F3 th\\u1EA9m quy\\u1EC1n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(149, \"p\", 4)(150, \"span\", 5);\n          i0.ɵɵtext(151, \"i) Khi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ti\\u1EBFn h\\u00E0nh c\\u00E1c c\\u00F4ng vi\\u1EC7c theo m\\u1EE5c \\u0111\\u00EDch x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n \\u0111\\u01B0\\u1EE3c quy \\u0111\\u1ECBnh t\\u1EA1i \\u0110i\\u1EC1u 3 Ch\\u00EDnh s\\u00E1ch n\\u00E0y.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(152, \"p\", 4)(153, \"span\", 5);\n          i0.ɵɵtext(154, \"2. D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ti\\u1EBFn h\\u00E0nh x\\u1EED l\\u00FD (sau \\u0111\\u00E2y g\\u1ECDi t\\u1EAFt l\\u00E0 \\u201C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"strong\")(156, \"span\", 5);\n          i0.ɵɵtext(157, \"D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(158, \"span\", 5);\n          i0.ɵɵtext(159, \"\\u201D) bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n nh\\u1EEFng th\\u00F4ng tin d\\u01B0\\u1EDBi \\u0111\\u00E2y v\\u00E0 c\\u00F3 th\\u1EC3 thay \\u0111\\u1ED5i t\\u00F9y thu\\u1ED9c v\\u00E0o lo\\u1EA1i s\\u1EA3n ph\\u1EA9m ho\\u1EB7c d\\u1ECBch v\\u1EE5, c\\u00E1ch th\\u1EE9c t\\u01B0\\u01A1ng t\\u00E1c c\\u1EE7a Kh\\u00E1ch h\\u00E0ng v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(160, \"p\", 4)(161, \"span\", 5);\n          i0.ɵɵtext(162, \"2.1. D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u01A1 b\\u1EA3n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(163, \"p\", 4)(164, \"span\", 5);\n          i0.ɵɵtext(165, \"a) H\\u1ECD, ch\\u1EEF \\u0111\\u1EC7m v\\u00E0 t\\u00EAn khai sinh, t\\u00EAn g\\u1ECDi kh\\u00E1c (n\\u1EBFu c\\u00F3);\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"p\", 4)(167, \"span\", 5);\n          i0.ɵɵtext(168, \"b) Ng\\u00E0y, th\\u00E1ng, n\\u0103m sinh; ng\\u00E0y, th\\u00E1ng, n\\u0103m ch\\u1EBFt ho\\u1EB7c m\\u1EA5t t\\u00EDch;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(169, \"p\", 4)(170, \"span\", 5);\n          i0.ɵɵtext(171, \"c) Gi\\u1EDBi t\\u00EDnh;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(172, \"p\", 4)(173, \"span\", 5);\n          i0.ɵɵtext(174, \"d) N\\u01A1i sinh, n\\u01A1i \\u0111\\u0103ng k\\u00FD khai sinh, n\\u01A1i th\\u01B0\\u1EDDng tr\\u00FA, n\\u01A1i t\\u1EA1m tr\\u00FA, n\\u01A1i \\u1EDF hi\\u1EC7n t\\u1EA1i, qu\\u00EA qu\\u00E1n, \\u0111\\u1ECBa ch\\u1EC9 li\\u00EAn h\\u1EC7;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(175, \"p\", 4)(176, \"span\", 5);\n          i0.ɵɵtext(177, \"\\u0111) Qu\\u1ED1c t\\u1ECBch;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(178, \"p\", 4)(179, \"span\", 5);\n          i0.ɵɵtext(180, \"e) H\\u00ECnh \\u1EA3nh c\\u1EE7a c\\u00E1 nh\\u00E2n;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(181, \"p\", 4)(182, \"span\", 5);\n          i0.ɵɵtext(183, \"g) S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i, s\\u1ED1 ch\\u1EE9ng minh nh\\u00E2n d\\u00E2n, s\\u1ED1 \\u0111\\u1ECBnh danh c\\u00E1 nh\\u00E2n, s\\u1ED1 h\\u1ED9 chi\\u1EBFu, s\\u1ED1 gi\\u1EA5y ph\\u00E9p l\\u00E1i xe, s\\u1ED1 bi\\u1EC3n s\\u1ED1 xe, s\\u1ED1 m\\u00E3 s\\u1ED1 thu\\u1EBF c\\u00E1 nh\\u00E2n, s\\u1ED1 b\\u1EA3o hi\\u1EC3m x\\u00E3 h\\u1ED9i, s\\u1ED1 th\\u1EBB b\\u1EA3o hi\\u1EC3m y t\\u1EBF;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(184, \"p\", 4)(185, \"span\", 5);\n          i0.ɵɵtext(186, \"h) T\\u00ECnh tr\\u1EA1ng h\\u00F4n nh\\u00E2n;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(187, \"p\", 4)(188, \"span\", 5);\n          i0.ɵɵtext(189, \"i) Th\\u00F4ng tin v\\u1EC1 m\\u1ED1i quan h\\u1EC7 gia \\u0111\\u00ECnh (cha m\\u1EB9, con c\\u00E1i);\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(190, \"p\", 4)(191, \"span\", 5);\n          i0.ɵɵtext(192, \"k) Th\\u00F4ng tin v\\u1EC1 t\\u00E0i kho\\u1EA3n s\\u1ED1 c\\u1EE7a c\\u00E1 nh\\u00E2n; d\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n ph\\u1EA3n \\u00E1nh ho\\u1EA1t \\u0111\\u1ED9ng, l\\u1ECBch s\\u1EED ho\\u1EA1t \\u0111\\u1ED9ng tr\\u00EAn kh\\u00F4ng gian m\\u1EA1ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(193, \"p\", 4)(194, \"span\", 5);\n          i0.ɵɵtext(195, \"2.2. D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n nh\\u1EA1y c\\u1EA3m\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"p\", 4)(197, \"span\", 5);\n          i0.ɵɵtext(198, \"a) D\\u1EEF li\\u1EC7u v\\u1EC1 t\\u1ED9i ph\\u1EA1m, h\\u00E0nh vi ph\\u1EA1m t\\u1ED9i \\u0111\\u01B0\\u1EE3c thu th\\u1EADp, l\\u01B0u tr\\u1EEF b\\u1EDFi c\\u00E1c c\\u01A1 quan th\\u1EF1c thi ph\\u00E1p lu\\u1EADt;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(199, \"p\", 4)(200, \"span\", 5);\n          i0.ɵɵtext(201, \"b) Th\\u00F4ng tin Kh\\u00E1ch h\\u00E0ng c\\u1EE7a t\\u1ED5 ch\\u1EE9c t\\u00EDn d\\u1EE5ng, chi nh\\u00E1nh ng\\u00E2n h\\u00E0ng n\\u01B0\\u1EDBc ngo\\u00E0i, t\\u1ED5 ch\\u1EE9c cung \\u1EE9ng d\\u1ECBch v\\u1EE5 trung gian thanh to\\u00E1n, c\\u00E1c t\\u1ED5 ch\\u1EE9c \\u0111\\u01B0\\u1EE3c ph\\u00E9p kh\\u00E1c, g\\u1ED3m: th\\u00F4ng tin \\u0111\\u1ECBnh danh Kh\\u00E1ch h\\u00E0ng theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt, th\\u00F4ng tin v\\u1EC1 t\\u00E0i kho\\u1EA3n, th\\u00F4ng tin v\\u1EC1 ti\\u1EC1n g\\u1EEDi, th\\u00F4ng tin v\\u1EC1 t\\u00E0i s\\u1EA3n g\\u1EEDi, th\\u00F4ng tin v\\u1EC1 giao d\\u1ECBch, th\\u00F4ng tin v\\u1EC1 t\\u1ED5 ch\\u1EE9c, c\\u00E1 nh\\u00E2n l\\u00E0 b\\u00EAn b\\u1EA3o \\u0111\\u1EA3m t\\u1EA1i t\\u1ED5 ch\\u1EE9c t\\u00EDn d\\u1EE5ng, chi nh\\u00E1nh ng\\u00E2n h\\u00E0ng, t\\u1ED5 ch\\u1EE9c cung \\u1EE9ng d\\u1ECBch v\\u1EE5 trung gian thanh to\\u00E1n;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(202, \"p\", 4)(203, \"span\", 5);\n          i0.ɵɵtext(204, \"c) D\\u1EEF li\\u1EC7u v\\u1EC1 v\\u1ECB tr\\u00ED c\\u1EE7a c\\u00E1 nh\\u00E2n \\u0111\\u01B0\\u1EE3c x\\u00E1c \\u0111\\u1ECBnh qua d\\u1ECBch v\\u1EE5 \\u0111\\u1ECBnh v\\u1ECB.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(205, \"p\", 4)(206, \"span\", 5);\n          i0.ɵɵtext(207, \"2.3. D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n kh\\u00E1c kh\\u00F4ng thu\\u1ED9c nh\\u00F3m D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u01A1 b\\u1EA3n v\\u00E0 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n nh\\u1EA1y c\\u1EA3m quy \\u0111\\u1ECBnh t\\u1EA1i kho\\u1EA3n 2.1 v\\u00E0 kho\\u1EA3n 2.2 \\u0110i\\u1EC1u n\\u00E0y.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(208, \"p\", 4)(209, \"span\", 5);\n          i0.ɵɵtext(210, \"a) D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n ph\\u00E1i sinh: \\u0110i\\u1EC3m t\\u00EDn nhi\\u1EC7m vi\\u1EC5n th\\u00F4ng \\u0111\\u01B0\\u1EE3c th\\u1EC3 hi\\u1EC7n d\\u01B0\\u1EDBi d\\u1EA1ng \\u0111i\\u1EC3m s\\u1ED1, l\\u00E0 th\\u00F4ng tin \\u0111\\u00E1nh gi\\u00E1 x\\u1EBFp h\\u1EA1ng t\\u1EEB vi\\u1EC7c t\\u1ED5ng h\\u1EE3p v\\u00E0 ph\\u00E2n t\\u00EDch d\\u1EEF li\\u1EC7u s\\u1EED d\\u1EE5ng d\\u1ECBch v\\u1EE5 vi\\u1EC5n th\\u00F4ng c\\u1EE7a Kh\\u00E1ch h\\u00E0ng nh\\u1EB1m \\u0111\\u00E1nh gi\\u00E1, ph\\u00E2n t\\u00EDch, d\\u1EF1 \\u0111o\\u00E1n th\\u00F3i quen, s\\u1EDF th\\u00EDch, m\\u1EE9c \\u0111\\u1ED9 tin c\\u1EADy, t\\u00EDn nhi\\u1EC7m, h\\u00E0nh vi, xu h\\u01B0\\u1EDBng \\u2026 v\\u00E0 c\\u00E1c th\\u00F4ng tin kh\\u00E1c c\\u1EE7a Kh\\u00E1ch h\\u00E0ng, h\\u1ED7 tr\\u1EE3 cho vi\\u1EC7c cung c\\u1EA5p c\\u00E1c cung c\\u1EA5p s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT m\\u1ED9t c\\u00E1ch ph\\u00F9 h\\u1EE3p v\\u00E0 t\\u1ED1t nh\\u1EA5t.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(211, \"p\", 4)(212, \"span\", 5);\n          i0.ɵɵtext(213, \"b) C\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n kh\\u00E1c ph\\u00E1t sinh trong qu\\u00E1 tr\\u00ECnh VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT cung c\\u1EA5p s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 v\\u00E0 trong qu\\u00E1 tr\\u00ECnh Kh\\u00E1ch h\\u00E0ng s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5, t\\u01B0\\u01A1ng t\\u00E1c v\\u1EDBi nh\\u00E0 cung c\\u1EA5p s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(214, \"p\", 4)(215, \"span\", 5);\n          i0.ɵɵtext(216, \"2.4. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT s\\u1EBD th\\u00F4ng b\\u00E1o cho Kh\\u00E1ch h\\u00E0ng c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n b\\u1EAFt bu\\u1ED9c ph\\u1EA3i cung c\\u1EA5p v\\u00E0/ho\\u1EB7c t\\u00F9y ch\\u1ECDn cung c\\u1EA5p t\\u1EA1i th\\u1EDDi \\u0111i\\u1EC3m Kh\\u00E1ch h\\u00E0ng li\\u00EAn h\\u1EC7, trao \\u0111\\u1ED5i ho\\u1EB7c \\u0111\\u0103ng k\\u00FD, k\\u00FD k\\u1EBFt h\\u1EE3p \\u0111\\u1ED3ng v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. N\\u1EBFu c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n b\\u1EAFt bu\\u1ED9c kh\\u00F4ng \\u0111\\u01B0\\u1EE3c cung c\\u1EA5p theo y\\u00EAu c\\u1EA7u, Kh\\u00E1ch h\\u00E0ng s\\u1EBD kh\\u00F4ng th\\u1EC3 s\\u1EED d\\u1EE5ng m\\u1ED9t s\\u1ED1 s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. Trong tr\\u01B0\\u1EDDng h\\u1EE3p n\\u00E0y, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 t\\u1EEB ch\\u1ED1i cung c\\u1EA5p s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 cho Kh\\u00E1ch h\\u00E0ng m\\u00E0 kh\\u00F4ng ph\\u1EA3i ch\\u1ECBu b\\u1EA5t k\\u00EC m\\u1ED9t kho\\u1EA3n b\\u1ED3i th\\u01B0\\u1EDDng v\\u00E0/ho\\u1EB7c ph\\u1EA1t vi ph\\u1EA1m n\\u00E0o.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(217, \"p\", 4)(218, \"span\", 5);\n          i0.ɵɵtext(219, \"2.5. T\\u1EA1i t\\u1EEBng th\\u1EDDi \\u0111i\\u1EC3m, Kh\\u00E1ch h\\u00E0ng c\\u00F3 th\\u1EC3 t\\u1EF1 nguy\\u1EC7n cung c\\u1EA5p cho VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n n\\u1EB1m ngo\\u00E0i y\\u00EAu c\\u1EA7u c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. Khi Kh\\u00E1ch h\\u00E0ng cung c\\u1EA5p D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n n\\u1EB1m ngo\\u00E0i y\\u00EAu c\\u1EA7u c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u1ED3ng ngh\\u0129a v\\u1EDBi vi\\u1EC7c Kh\\u00E1ch h\\u00E0ng cho ph\\u00E9p VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng v\\u1EDBi M\\u1EE5c \\u0110\\u00EDch \\u0111\\u01B0\\u1EE3c n\\u00EAu trong Ch\\u00EDnh s\\u00E1ch n\\u00E0y ho\\u1EB7c v\\u1EDBi m\\u1EE5c \\u0111\\u00EDch \\u0111\\u01B0\\u1EE3c n\\u00EAu t\\u1EA1i th\\u1EDDi \\u0111i\\u1EC3m Kh\\u00E1ch h\\u00E0ng cung c\\u1EA5p nh\\u1EEFng D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n \\u0111\\u00F3. Ngo\\u00E0i ra, khi Kh\\u00E1ch h\\u00E0ng ch\\u1EE7 \\u0111\\u1ED9ng cung c\\u1EA5p th\\u00F4ng tin n\\u1EB1m ngo\\u00E0i y\\u00EAu c\\u1EA7u c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, Kh\\u00E1ch h\\u00E0ng vui l\\u00F2ng kh\\u00F4ng cung c\\u1EA5p D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n nh\\u1EA1y c\\u1EA3m theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt t\\u1EA1i t\\u1EEBng th\\u1EDDi \\u0111i\\u1EC3m. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT s\\u1EBD kh\\u00F4ng th\\u1EF1c hi\\u1EC7n x\\u1EED l\\u00FD v\\u00E0 kh\\u00F4ng ch\\u1ECBu b\\u1EA5t k\\u00EC tr\\u00E1ch nhi\\u1EC7m ph\\u00E1p l\\u00FD n\\u00E0o \\u0111\\u1ED1i v\\u1EDBi c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n nh\\u1EA1y c\\u1EA3m do Kh\\u00E1ch h\\u00E0ng t\\u1EF1 nguy\\u1EC7n cung c\\u1EA5p n\\u1EB1m ngo\\u00E0i y\\u00EAu c\\u1EA7u c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(220, \"p\", 4)(221, \"strong\")(222, \"span\", 5);\n          i0.ɵɵtext(223, \"\\u0110i\\u1EC1u 3.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(224, \"span\", 5);\n          i0.ɵɵtext(225, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(226, \"strong\")(227, \"span\", 5);\n          i0.ɵɵtext(228, \"M\\u1EE5c \\u0111\\u00EDch x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(229, \"p\", 4)(230, \"span\", 5);\n          i0.ɵɵtext(231, \"VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng cho m\\u1ED9t ho\\u1EB7c m\\u1ED9t s\\u1ED1 m\\u1EE5c \\u0111\\u00EDch \\u0111\\u01B0\\u1EE3c li\\u1EC7t k\\u00EA sau \\u0111\\u00E2y (\\u201C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(232, \"strong\")(233, \"span\", 5);\n          i0.ɵɵtext(234, \"M\\u1EE5c \\u0110\\u00EDch\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(235, \"span\", 5);\n          i0.ɵɵtext(236, \"\\u201D):\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(237, \"p\", 4)(238, \"span\", 5);\n          i0.ɵɵtext(239, \"1. X\\u00E1c minh t\\u00EDnh ch\\u00EDnh x\\u00E1c, \\u0111\\u1EA7y \\u0111\\u1EE7 c\\u1EE7a c\\u00E1c th\\u00F4ng tin \\u0111\\u01B0\\u1EE3c Kh\\u00E1ch h\\u00E0ng cung c\\u1EA5p; x\\u00E1c \\u0111\\u1ECBnh ho\\u1EB7c x\\u00E1c th\\u1EF1c danh t\\u00EDnh c\\u1EE7a Kh\\u00E1ch h\\u00E0ng v\\u00E0 th\\u1EF1c hi\\u1EC7n quy tr\\u00ECnh x\\u00E1c th\\u1EF1c Kh\\u00E1ch h\\u00E0ng; X\\u1EED l\\u00FD vi\\u1EC7c \\u0111\\u0103ng k\\u00FD s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(240, \"p\", 4)(241, \"span\", 5);\n          i0.ɵɵtext(242, \"2. Th\\u1EA9m \\u0111\\u1ECBnh h\\u1ED3 s\\u01A1 v\\u00E0 kh\\u1EA3 n\\u0103ng \\u0111\\u1EE7 \\u0111i\\u1EC1u ki\\u1EC7n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u1ED1i v\\u1EDBi vi\\u1EC7c s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 s\\u1EED d\\u1EE5ng c\\u00E1c ph\\u01B0\\u01A1ng ph\\u00E1p ch\\u1EA5m \\u0111i\\u1EC3m, g\\u00E1n ng\\u01B0\\u1EE1ng c\\u01B0\\u1EDBc n\\u00F3ng, ki\\u1EC3m tra l\\u1ECBch s\\u1EED Kh\\u00E1ch h\\u00E0ng s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u1EC3 \\u0111\\u00E1nh gi\\u00E1 v\\u00E0 qu\\u1EA3n tr\\u1ECB r\\u1EE7i ro t\\u00EDn d\\u1EE5ng, \\u0111\\u1EA3m b\\u1EA3o kh\\u1EA3 n\\u0103ng thanh to\\u00E1n \\u0111\\u1ED1i v\\u1EDBi c\\u00E1c ngh\\u0129a v\\u1EE5 thanh to\\u00E1n v\\u00E0 c\\u00E1c ngh\\u0129a v\\u1EE5 kh\\u00E1c c\\u00F3 li\\u00EAn quan trong su\\u1ED1t qu\\u00E1 tr\\u00ECnh cung c\\u1EA5p s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT cho Kh\\u00E1ch h\\u00E0ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(243, \"p\", 4)(244, \"span\", 5);\n          i0.ɵɵtext(245, \"3. Qu\\u1EA3n l\\u00FD v\\u00E0 \\u0111\\u00E1nh gi\\u00E1 c\\u00E1c ho\\u1EA1t \\u0111\\u1ED9ng kinh doanh bao g\\u1ED3m thi\\u1EBFt k\\u1EBF, c\\u1EA3i ti\\u1EBFn v\\u00E0 n\\u00E2ng cao ch\\u1EA5t l\\u01B0\\u1EE3ng c\\u00E1c s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ho\\u1EB7c th\\u1EF1c hi\\u1EC7n c\\u00E1c ho\\u1EA1t \\u0111\\u1ED9ng truy\\u1EC1n th\\u00F4ng ti\\u1EBFp th\\u1ECB; Th\\u1EF1c hi\\u1EC7n nghi\\u00EAn c\\u1EE9u th\\u1ECB tr\\u01B0\\u1EDDng, kh\\u1EA3o s\\u00E1t v\\u00E0 ph\\u00E2n t\\u00EDch d\\u1EEF li\\u1EC7u li\\u00EAn quan \\u0111\\u1EBFn s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT; nghi\\u00EAn c\\u1EE9u, ph\\u00E1t tri\\u1EC3n c\\u00E1c s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 m\\u1EDBi, m\\u00F4 h\\u00ECnh cung c\\u1EA5p m\\u1EDBi v.v. \\u0111\\u00E1p \\u1EE9ng nhu c\\u1EA7u c\\u1EE7a Kh\\u00E1ch h\\u00E0ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(246, \"p\", 4)(247, \"span\", 5);\n          i0.ɵɵtext(248, \"4. Cung c\\u1EA5p d\\u1ECBch v\\u1EE5 cho Kh\\u00E1ch h\\u00E0ng, li\\u00EAn h\\u1EC7 v\\u1EDBi Kh\\u00E1ch h\\u00E0ng nh\\u1EB1m t\\u01B0 v\\u1EA5n, trao \\u0111\\u1ED5i th\\u00F4ng tin, gi\\u1EA3i quy\\u1EBFt y\\u00EAu c\\u1EA7u, khi\\u1EBFu n\\u1EA1i, giao c\\u00E1c h\\u00F3a \\u0111\\u01A1n, c\\u00E1c sao k\\u00EA, c\\u00E1c b\\u00E1o c\\u00E1o ho\\u1EB7c c\\u00E1c t\\u00E0i li\\u1EC7u kh\\u00E1c li\\u00EAn quan t\\u1EDBi s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT th\\u00F4ng qua c\\u00E1c k\\u00EAnh kh\\u00E1c nhau (v\\u00ED d\\u1EE5: email, chat) v\\u00E0 \\u0111\\u1EC3 tr\\u1EA3 l\\u1EDDi y\\u00EAu c\\u1EA7u c\\u1EE7a Kh\\u00E1ch h\\u00E0ng. Li\\u00EAn h\\u1EC7 v\\u1EDBi Kh\\u00E1ch h\\u00E0ng (ho\\u1EB7c c\\u00E1c b\\u00EAn c\\u1EA7n thi\\u1EBFt kh\\u00E1c) \\u0111\\u1EC3 th\\u00F4ng b\\u00E1o cho Kh\\u00E1ch h\\u00E0ng v\\u1EC1 th\\u00F4ng tin li\\u00EAn quan \\u0111\\u1EBFn vi\\u1EC7c s\\u1EED d\\u1EE5ng s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(249, \"p\", 4)(250, \"span\", 5);\n          i0.ɵɵtext(251, \"5. Qu\\u1EA3ng c\\u00E1o, ti\\u1EBFp th\\u1ECB d\\u1EF1a tr\\u00EAn s\\u1EDF th\\u00EDch, th\\u00F3i quen s\\u1EED d\\u1EE5ng d\\u1ECBch v\\u1EE5 c\\u1EE7a Kh\\u00E1ch h\\u00E0ng: VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 s\\u1EED d\\u1EE5ng D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n \\u0111\\u1EC3 qu\\u1EA3ng c\\u00E1o, ti\\u1EBFp th\\u1ECB v\\u1EDBi Kh\\u00E1ch h\\u00E0ng v\\u1EC1 c\\u00E1c s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, ch\\u01B0\\u01A1ng tr\\u00ECnh khuy\\u1EBFn m\\u1EA1i, nghi\\u00EAn c\\u1EE9u, kh\\u1EA3o s\\u00E1t, tin t\\u1EE9c, th\\u00F4ng tin c\\u1EADp nh\\u1EADt, c\\u00E1c s\\u1EF1 ki\\u1EC7n, cu\\u1ED9c thi c\\u00F3 th\\u01B0\\u1EDFng, trao c\\u00E1c ph\\u1EA7n th\\u01B0\\u1EDFng c\\u00F3 li\\u00EAn quan, c\\u00E1c qu\\u1EA3ng c\\u00E1o v\\u00E0 n\\u1ED9i dung c\\u00F3 li\\u00EAn quan v\\u1EC1 s\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ho\\u1EB7c c\\u1EE7a c\\u00E1c \\u0111\\u1ED1i t\\u00E1c h\\u1EE3p t\\u00E1c v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(252, \"p\", 4)(253, \"span\", 5);\n          i0.ɵɵtext(254, \"Tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng kh\\u00F4ng mu\\u1ED1n nh\\u1EADn email, tin nh\\u1EAFn v\\u00E0/ho\\u1EB7c b\\u1EA3n tin \\u0111\\u1ECBnh k\\u1EF3 v\\u1EDBi m\\u1EE5c \\u0111\\u00EDch qu\\u1EA3ng c\\u00E1o, ti\\u1EBFp th\\u1ECB c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u1EDBi t\\u1EA7n su\\u1EA5t t\\u00F9y thu\\u1ED9c v\\u00E0o Ch\\u00EDnh s\\u00E1ch c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT theo t\\u1EEBng th\\u1EDDi k\\u1EF3 v\\u00E0 ph\\u00F9 h\\u1EE3p v\\u1EDBi quy \\u0111\\u1ECBnh ph\\u00E1p lu\\u1EADt, Kh\\u00E1ch h\\u00E0ng c\\u00F3 th\\u1EC3 t\\u1EEB ch\\u1ED1i theo c\\u00E1ch th\\u1EE9c \\u0111\\u00E3 \\u0111\\u01B0\\u1EE3c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT h\\u01B0\\u1EDBng d\\u1EABn tr\\u00EAn c\\u00E1c k\\u00EAnh, ph\\u01B0\\u01A1ng ti\\u1EC7n nh\\u01B0 tin nh\\u1EAFn SMS, cu\\u1ED9c g\\u1ECDi, d\\u1EA5u t\\u00EDch tr\\u00EAn website/wapsite/\\u1EE9ng d\\u1EE5ng v.v. ho\\u1EB7c li\\u00EAn h\\u1EC7 v\\u1EDBi t\\u1ED5ng \\u0111\\u00E0i ch\\u0103m s\\u00F3c Kh\\u00E1ch h\\u00E0ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(255, \"p\", 4)(256, \"span\", 5);\n          i0.ɵɵtext(257, \"6. L\\u1EADp c\\u00E1c b\\u00E1o c\\u00E1o t\\u00E0i ch\\u00EDnh, b\\u00E1o c\\u00E1o ho\\u1EA1t \\u0111\\u1ED9ng ho\\u1EB7c c\\u00E1c lo\\u1EA1i b\\u00E1o c\\u00E1o li\\u00EAn quan kh\\u00E1c m\\u00E0 ph\\u00E1p lu\\u1EADt quy \\u0111\\u1ECBnh;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(258, \"p\", 4)(259, \"span\", 5);\n          i0.ɵɵtext(260, \"7. Tu\\u00E2n th\\u1EE7 c\\u00E1c ngh\\u0129a v\\u1EE5 ph\\u00E1p l\\u00FD theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(261, \"p\", 4)(262, \"span\", 5);\n          i0.ɵɵtext(263, \"8. Ng\\u0103n ch\\u1EB7n gian l\\u1EADn ho\\u1EB7c gi\\u1EA3m thi\\u1EC3u m\\u1ED1i \\u0111e do\\u1EA1 \\u0111\\u1ED1i v\\u1EDBi t\\u00EDnh m\\u1EA1ng, s\\u1EE9c kh\\u1ECFe c\\u1EE7a ng\\u01B0\\u1EDDi kh\\u00E1c v\\u00E0 l\\u1EE3i \\u00EDch c\\u00F4ng c\\u1ED9ng: VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 s\\u1EED d\\u1EE5ng th\\u00F4ng tin c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u1EC3 ng\\u0103n ch\\u1EB7n v\\u00E0 ph\\u00E1t hi\\u1EC7n gian l\\u1EADn, l\\u1EA1m d\\u1EE5ng nh\\u1EB1m b\\u1EA3o v\\u1EC7 Kh\\u00E1ch h\\u00E0ng, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u00E0 c\\u00E1c ch\\u1EE7 th\\u1EC3 li\\u00EAn quan;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(264, \"p\", 4)(265, \"span\", 5);\n          i0.ɵɵtext(266, \"9. Qu\\u1EA3n tr\\u1ECB n\\u1ED9i b\\u1ED9;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(267, \"p\", 4)(268, \"span\", 5);\n          i0.ɵɵtext(269, \"10. C\\u00E1c m\\u1EE5c \\u0111\\u00EDch kh\\u00E1c c\\u00F3 li\\u00EAn quan \\u0111\\u1EBFn nh\\u1EEFng m\\u1EE5c \\u0111\\u00EDch \\u0111\\u01B0\\u1EE3c n\\u00EAu tr\\u00EAn.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(270, \"p\", 4)(271, \"strong\")(272, \"span\", 5);\n          i0.ɵɵtext(273, \"\\u0110i\\u1EC1u 4. C\\u00E1ch th\\u1EE9c x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(274, \"p\", 4)(275, \"span\", 5);\n          i0.ɵɵtext(276, \"VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u00E1p d\\u1EE5ng m\\u1ED9t ho\\u1EB7c nhi\\u1EC1u ho\\u1EA1t \\u0111\\u1ED9ng t\\u00E1c \\u0111\\u1ED9ng t\\u1EDBi D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n nh\\u01B0: thu th\\u1EADp, ghi, ph\\u00E2n t\\u00EDch, x\\u00E1c nh\\u1EADn, l\\u01B0u tr\\u1EEF, ch\\u1EC9nh s\\u1EEDa, c\\u00F4ng khai, k\\u1EBFt h\\u1EE3p, truy c\\u1EADp, truy xu\\u1EA5t, thu h\\u1ED3i, m\\u00E3 h\\u00F3a, gi\\u1EA3i m\\u00E3, sao ch\\u00E9p, chia s\\u1EBB, truy\\u1EC1n \\u0111\\u01B0a, cung c\\u1EA5p, chuy\\u1EC3n giao, x\\u00F3a, h\\u1EE7y D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n ho\\u1EB7c c\\u00E1c h\\u00E0nh \\u0111\\u1ED9ng kh\\u00E1c c\\u00F3 li\\u00EAn quan.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(277, \"p\", 4)(278, \"strong\")(279, \"span\", 5);\n          i0.ɵɵtext(280, \"\\u0110i\\u1EC1u 5. Th\\u1EDDi gian b\\u1EAFt \\u0111\\u1EA7u, th\\u1EDDi gian k\\u1EBFt th\\u00FAc x\\u1EED l\\u00FD d\\u1EEF li\\u1EC7u\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(281, \"p\", 4)(282, \"span\", 5);\n          i0.ɵɵtext(283, \"1. Th\\u1EDDi gian b\\u1EAFt \\u0111\\u1EA7u x\\u1EED l\\u00FD d\\u1EEF li\\u1EC7u\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(284, \"p\", 4)(285, \"span\", 5);\n          i0.ɵɵtext(286, \"K\\u1EC3 t\\u1EEB th\\u1EDDi \\u0111i\\u1EC3m ph\\u00E1t sinh c\\u00E1c M\\u1EE5c \\u0110\\u00EDch quy \\u0111\\u1ECBnh t\\u1EA1i \\u0110i\\u1EC1u 3 Ch\\u00EDnh s\\u00E1ch n\\u00E0y.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(287, \"p\", 4)(288, \"span\", 5);\n          i0.ɵɵtext(289, \"2. Th\\u1EDDi gian k\\u1EBFt th\\u00FAc x\\u1EED l\\u00FD d\\u1EEF li\\u1EC7u\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(290, \"p\", 4)(291, \"span\", 5);\n          i0.ɵɵtext(292, \"VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ch\\u1EA5m d\\u1EE9t vi\\u1EC7c x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n khi \\u0111\\u00E3 ho\\u00E0n th\\u00E0nh M\\u1EE5c \\u0110\\u00EDch quy \\u0111\\u1ECBnh t\\u1EA1i Ch\\u00EDnh s\\u00E1ch n\\u00E0y, tr\\u1EEB tr\\u01B0\\u1EDDng h\\u1EE3p ph\\u00E1p lu\\u1EADt c\\u00F3 quy \\u0111\\u1ECBnh kh\\u00E1c ho\\u1EB7c Kh\\u00E1ch h\\u00E0ng r\\u00FAt l\\u1EA1i s\\u1EF1 \\u0111\\u1ED3ng \\u00FD vi\\u1EC7c x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n ho\\u1EB7c khi c\\u01A1 quan nh\\u00E0 n\\u01B0\\u1EDBc c\\u00F3 th\\u1EA9m quy\\u1EC1n y\\u00EAu c\\u1EA7u b\\u1EB1ng v\\u0103n b\\u1EA3n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(293, \"p\", 4)(294, \"strong\")(295, \"span\", 5);\n          i0.ɵɵtext(296, \"\\u0110i\\u1EC1u 6. Chia s\\u1EBB D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(297, \"p\", 4)(298, \"span\", 5);\n          i0.ɵɵtext(299, \"B\\u1EB1ng vi\\u1EC7c ch\\u1EA5p thu\\u1EADn Ch\\u00EDnh s\\u00E1ch n\\u00E0y, Kh\\u00E1ch h\\u00E0ng hi\\u1EC3u v\\u00E0 \\u0111\\u1ED3ng \\u00FD r\\u1EB1ng VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 chia s\\u1EBB D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng cho c\\u00E1c t\\u1ED5 ch\\u1EE9c, c\\u00E1 nh\\u00E2n d\\u01B0\\u1EDBi \\u0111\\u00E2y \\u0111\\u1EC3 th\\u1EF1c hi\\u1EC7n c\\u00E1c\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(300, \"strong\")(301, \"span\", 5);\n          i0.ɵɵtext(302, \"\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(303, \"span\", 5);\n          i0.ɵɵtext(304, \"M\\u1EE5c \\u0110\\u00EDch quy \\u0111\\u1ECBnh t\\u1EA1i Ch\\u00EDnh s\\u00E1ch, c\\u1EE5 th\\u1EC3:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(305, \"p\", 4)(306, \"span\", 5);\n          i0.ɵɵtext(307, \"1. VNPT, C\\u00F4ng ty con c\\u1EE7a VNPT, c\\u00F4ng ty li\\u00EAn k\\u1EBFt c\\u1EE7a VNPT, c\\u00F4ng ty li\\u00EAn k\\u1EBFt c\\u1EE7a C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(308, \"p\", 4)(309, \"span\", 5);\n          i0.ɵɵtext(310, \"2. B\\u00EAn th\\u1EE9 ba cung c\\u1EA5p d\\u1ECBch v\\u1EE5 ho\\u1EB7c c\\u00E1c \\u0111\\u1ED1i t\\u00E1c trong c\\u00E1c h\\u1EE3p \\u0111\\u1ED3ng h\\u1EE3p t\\u00E1c kinh doanh (c\\u00F3 ph\\u00E2n chia l\\u1EE3i nhu\\u1EADn ho\\u1EB7c kh\\u00F4ng ph\\u00E2n chia l\\u1EE3i nhu\\u1EADn): VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT s\\u1EED d\\u1EE5ng v\\u00E0/ho\\u1EB7c h\\u1EE3p t\\u00E1c v\\u1EDBi c\\u00E1c c\\u00F4ng ty v\\u00E0 c\\u00E1 nh\\u00E2n kh\\u00E1c \\u0111\\u1EC3 th\\u1EF1c hi\\u1EC7n m\\u1ED9t s\\u1ED1 c\\u00F4ng vi\\u1EC7c v\\u00E0 ch\\u01B0\\u01A1ng tr\\u00ECnh nh\\u01B0 ch\\u01B0\\u01A1ng tr\\u00ECnh qu\\u1EA3ng c\\u00E1o, khuy\\u1EBFn m\\u1EA1i d\\u00E0nh cho Kh\\u00E1ch h\\u00E0ng, nghi\\u00EAn c\\u1EE9u th\\u1ECB tr\\u01B0\\u1EDDng, ph\\u00E2n t\\u00EDch v\\u00E0 ph\\u00E1t tri\\u1EC3n s\\u1EA3n ph\\u1EA9m, t\\u01B0 v\\u1EA5n chi\\u1EBFn l\\u01B0\\u1EE3c, cung c\\u1EA5p d\\u1ECBch v\\u1EE5 thu c\\u01B0\\u1EDBc v.v. C\\u00E1c B\\u00EAn th\\u1EE9 ba cung c\\u1EA5p d\\u1ECBch v\\u1EE5 v\\u00E0/ho\\u1EB7c c\\u00E1c \\u0111\\u1ED1i t\\u00E1c n\\u00E0y c\\u00F3 quy\\u1EC1n truy c\\u1EADp, thu th\\u1EADp, s\\u1EED d\\u1EE5ng v\\u00E0 x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng trong ph\\u1EA1m vi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT cho ph\\u00E9p \\u0111\\u1EC3 th\\u1EF1c hi\\u1EC7n c\\u00E1c ch\\u1EE9c n\\u0103ng c\\u1EE7a h\\u1ECD v\\u00E0 ph\\u1EA3i tu\\u00E2n th\\u1EE7 quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n v\\u1EDBi t\\u01B0 c\\u00E1ch l\\u00E0 B\\u00EAn X\\u1EED l\\u00FD D\\u1EEF Li\\u1EC7u;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(311, \"p\", 4)(312, \"span\", 5);\n          i0.ɵɵtext(313, \"3. T\\u00E1i c\\u1EA5u tr\\u00FAc doanh nghi\\u1EC7p: Trong qu\\u00E1 tr\\u00ECnh ph\\u00E1t tri\\u1EC3n kinh doanh, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 b\\u00E1n ho\\u1EB7c mua c\\u00E1c doanh nghi\\u1EC7p ho\\u1EB7c t\\u00E1i c\\u1EA5u tr\\u00FAc doanh nghi\\u1EC7p ph\\u00F9 h\\u1EE3p v\\u1EDBi quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt v\\u00E0 nhu c\\u1EA7u s\\u1EA3n xu\\u1EA5t kinh doanh. Trong c\\u00E1c giao d\\u1ECBch nh\\u01B0 v\\u1EADy, D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n s\\u1EBD \\u0111\\u01B0\\u1EE3c chuy\\u1EC3n nh\\u01B0\\u1EE3ng v\\u00E0 b\\u00EAn nh\\u1EADn chuy\\u1EC3n nh\\u01B0\\u1EE3ng v\\u1EABn ph\\u1EA3i tu\\u00E2n theo c\\u00E1c quy \\u0111\\u1ECBnh c\\u1EE7a Ch\\u00EDnh s\\u00E1ch n\\u00E0y;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(314, \"p\", 4)(315, \"span\", 5);\n          i0.ɵɵtext(316, \"4. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u01B0\\u1EE3c ph\\u00E9p ti\\u1EBFt l\\u1ED9 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n theo y\\u00EAu c\\u1EA7u c\\u1EE7a ph\\u00E1p lu\\u1EADt, y\\u00EAu c\\u1EA7u c\\u1EE7a c\\u01A1 quan qu\\u1EA3n l\\u00FD nh\\u00E0 n\\u01B0\\u1EDBc c\\u00F3 th\\u1EA9m quy\\u1EC1n;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(317, \"p\", 4)(318, \"span\", 5);\n          i0.ɵɵtext(319, \"5. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u01B0\\u1EE3c ph\\u00E9p ti\\u1EBFt l\\u1ED9 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n cho c\\u00E1c doanh nghi\\u1EC7p vi\\u1EC5n th\\u00F4ng kh\\u00E1c\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(320, \"span\", 3);\n          i0.ɵɵtext(321, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(322, \"span\", 5);\n          i0.ɵɵtext(323, \"\\u0111\\u1EC3 ph\\u1EE5c v\\u1EE5 cho vi\\u1EC7c t\\u00EDnh gi\\u00E1 c\\u01B0\\u1EDBc, l\\u1EADp ho\\u00E1 \\u0111\\u01A1n v\\u00E0 ng\\u0103n ch\\u1EB7n h\\u00E0nh vi tr\\u1ED1n tr\\u00E1nh th\\u1EF1c hi\\u1EC7n ngh\\u0129a v\\u1EE5 theo h\\u1EE3p \\u0111\\u1ED3ng c\\u1EE7a Kh\\u00E1ch h\\u00E0ng.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(324, \"p\", 4)(325, \"strong\")(326, \"span\", 5);\n          i0.ɵɵtext(327, \"\\u0110i\\u1EC1u 7. Quy\\u1EC1n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(328, \"p\", 4)(329, \"span\", 5);\n          i0.ɵɵtext(330, \"1. Quy\\u1EC1n \\u0111\\u01B0\\u1EE3c bi\\u1EBFt v\\u00E0 Quy\\u1EC1n \\u0111\\u1ED3ng \\u00FD\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(331, \"p\", 4)(332, \"span\", 5);\n          i0.ɵɵtext(333, \"B\\u1EB1ng Ch\\u00EDnh s\\u00E1ch n\\u00E0y, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT th\\u00F4ng b\\u00E1o cho Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c bi\\u1EBFt v\\u1EC1 ho\\u1EA1t \\u0111\\u1ED9ng x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n tr\\u01B0\\u1EDBc khi th\\u1EF1c hi\\u1EC7n x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n. \\u0110\\u1ED3ng th\\u1EDDi, Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n \\u0111\\u1ED3ng \\u00FD ho\\u1EB7c kh\\u00F4ng \\u0111\\u1ED3ng \\u00FD v\\u1EDBi c\\u00E1c \\u0111i\\u1EC1u kho\\u1EA3n v\\u00E0 \\u0111i\\u1EC1u ki\\u1EC7n c\\u1EE7a Ch\\u00EDnh s\\u00E1ch n\\u00E0y theo c\\u00E1ch th\\u1EE9c \\u0111\\u00E3 \\u0111\\u01B0\\u1EE3c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT h\\u01B0\\u1EDBng d\\u1EABn tr\\u00EAn c\\u00E1c k\\u00EAnh, ph\\u01B0\\u01A1ng ti\\u1EC7n nh\\u01B0 tin nh\\u1EAFn SMS, cu\\u1ED9c g\\u1ECDi, d\\u1EA5u t\\u00EDch tr\\u00EAn website/wapsite/\\u1EE9ng d\\u1EE5ng v.v. ho\\u1EB7c li\\u00EAn h\\u1EC7 v\\u1EDBi t\\u1ED5ng \\u0111\\u00E0i ch\\u0103m s\\u00F3c kh\\u00E1ch h\\u00E0ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(334, \"p\", 4)(335, \"span\", 5);\n          i0.ɵɵtext(336, \"2. Quy\\u1EC1n truy c\\u1EADp v\\u00E0 y\\u00EAu c\\u1EA7u cung c\\u1EA5p D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(337, \"p\", 4)(338, \"span\", 5);\n          i0.ɵɵtext(339, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n truy c\\u1EADp v\\u00E0o c\\u00E1c \\u1EE9ng d\\u1EE5ng/website/wapsite c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u00E0/ho\\u1EB7c li\\u00EAn h\\u1EC7 tr\\u1EF1c ti\\u1EBFp v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u1EC3 xem, tr\\u00EDch xu\\u1EA5t c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n m\\u00E0 Kh\\u00E1ch h\\u00E0ng \\u0111\\u00E3 cung c\\u1EA5p cho VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ph\\u1EE5c v\\u1EE5 c\\u00E1c M\\u1EE5c \\u0110\\u00EDch quy \\u0111\\u1ECBnh t\\u1EA1i Ch\\u00EDnh s\\u00E1ch n\\u00E0y.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(340, \"p\", 4)(341, \"span\", 5);\n          i0.ɵɵtext(342, \"Tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng kh\\u00F4ng th\\u1EC3 t\\u1EF1 truy c\\u1EADp, tr\\u00EDch xu\\u1EA5t ho\\u1EB7c g\\u1EB7p kh\\u00F3 kh\\u0103n trong vi\\u1EC7c truy c\\u1EADp, tr\\u00EDch xu\\u1EA5t c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n, Kh\\u00E1ch h\\u00E0ng c\\u00F3 th\\u1EC3 li\\u00EAn h\\u1EC7 v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u1EC3 \\u0111\\u01B0\\u1EE3c h\\u1ED7 tr\\u1EE3.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(343, \"p\", 4)(344, \"span\", 5);\n          i0.ɵɵtext(345, \"3. Quy\\u1EC1n ch\\u1EC9nh s\\u1EEDa\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(346, \"p\", 4)(347, \"span\", 5);\n          i0.ɵɵtext(348, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n ch\\u1EC9nh s\\u1EEDa c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a m\\u00ECnh v\\u1EDBi \\u0111i\\u1EC1u ki\\u1EC7n vi\\u1EC7c ch\\u1EC9nh s\\u1EEDa n\\u00E0y kh\\u00F4ng vi ph\\u1EA1m c\\u00E1c quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt. Tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng kh\\u00F4ng th\\u1EC3 t\\u1EF1 ch\\u1EC9nh s\\u1EEDa ho\\u1EB7c g\\u1EB7p kh\\u00F3 kh\\u0103n trong vi\\u1EC7c ch\\u1EC9nh s\\u1EEDa c\\u00E1c D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n, Kh\\u00E1ch h\\u00E0ng c\\u00F3 th\\u1EC3 li\\u00EAn h\\u1EC7 v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u0111\\u1EC3 \\u0111\\u01B0\\u1EE3c h\\u1ED7 tr\\u1EE3.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(349, \"p\", 4)(350, \"span\", 5);\n          i0.ɵɵtext(351, \"4. Quy\\u1EC1n ph\\u1EA3n \\u0111\\u1ED1i, h\\u1EA1n ch\\u1EBF, r\\u00FAt l\\u1EA1i s\\u1EF1 \\u0111\\u1ED3ng \\u00FD x\\u1EED l\\u00FD d\\u1EEF li\\u1EC7u\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(352, \"p\", 4)(353, \"span\", 5);\n          i0.ɵɵtext(354, \"a) Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n ph\\u1EA3n \\u0111\\u1ED1i, h\\u1EA1n ch\\u1EBF ho\\u1EB7c r\\u00FAt l\\u1EA1i s\\u1EF1 \\u0111\\u1ED3ng \\u00FD x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng. Tuy nhi\\u00EAn, vi\\u1EC7c ph\\u1EA3n \\u0111\\u1ED1i, h\\u1EA1n ch\\u1EBF ho\\u1EB7c r\\u00FAt l\\u1EA1i s\\u1EF1 \\u0111\\u1ED3ng \\u00FD x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng trong m\\u1ED9t s\\u1ED1 tr\\u01B0\\u1EDDng h\\u1EE3p c\\u00F3 th\\u1EC3 d\\u1EABn t\\u1EDBi vi\\u1EC7c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT kh\\u00F4ng th\\u1EC3 cung c\\u1EA5p S\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 cho Kh\\u00E1ch h\\u00E0ng, \\u0111i\\u1EC1u n\\u00E0y \\u0111\\u1ED3ng ngh\\u0129a v\\u1EDBi vi\\u1EC7c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 \\u0111\\u01A1n ph\\u01B0\\u01A1ng ch\\u1EA5m d\\u1EE9t h\\u1EE3p \\u0111\\u1ED3ng m\\u00E0 kh\\u00F4ng c\\u1EA7n ph\\u1EA3i b\\u1ED3i th\\u01B0\\u1EDDng cho Kh\\u00E1ch h\\u00E0ng do c\\u00E1c \\u0111i\\u1EC1u ki\\u1EC7n \\u0111\\u1EC3 th\\u1EF1c hi\\u1EC7n h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u00E3 thay \\u0111\\u1ED5i. Do \\u0111\\u00F3, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT khuy\\u1EBFn ngh\\u1ECB Kh\\u00E1ch h\\u00E0ng c\\u00E2n nh\\u1EAFc k\\u0129 l\\u01B0\\u1EE1ng tr\\u01B0\\u1EDBc khi ph\\u1EA3n \\u0111\\u1ED1i, h\\u1EA1n ch\\u1EBF ho\\u1EB7c r\\u00FAt l\\u1EA1i s\\u1EF1 \\u0111\\u1ED3ng \\u00FD x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(355, \"p\", 4)(356, \"span\", 5);\n          i0.ɵɵtext(357, \"b) Tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng mu\\u1ED1n h\\u1EA1n ch\\u1EBF nh\\u1EADn n\\u1ED9i dung ti\\u1EBFp th\\u1ECB qu\\u1EA3ng c\\u00E1o, khuy\\u1EBFn m\\u1EA1i t\\u1EEB VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u00E0 mu\\u1ED1n r\\u00FAt l\\u1EA1i s\\u1EF1 ch\\u1EA5p thu\\u1EADn tr\\u01B0\\u1EDBc \\u0111\\u00F3 (n\\u1EBFu c\\u00F3) v\\u00E0/ho\\u1EB7c ph\\u1EA3n \\u0111\\u1ED1i vi\\u1EC7c ti\\u1EBFp t\\u1EE5c s\\u1EED d\\u1EE5ng th\\u00F4ng tin c\\u00E1 nh\\u00E2n c\\u1EE7a m\\u00ECnh cho m\\u1EE5c \\u0111\\u00EDch quy \\u0111\\u1ECBnh t\\u1EA1i kho\\u1EA3n 5, \\u0110i\\u1EC1u 3 Ch\\u00EDnh s\\u00E1ch n\\u00E0y, Kh\\u00E1ch h\\u00E0ng vui l\\u00F2ng th\\u1EF1c hi\\u1EC7n theo h\\u01B0\\u1EDBng d\\u1EABn c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT t\\u1EA1i th\\u1EDDi \\u0111i\\u1EC3m VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT thu th\\u1EADp D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n ho\\u1EB7c li\\u00EAn h\\u1EC7 v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT theo c\\u00E1c th\\u00F4ng tin \\u0111\\u01B0\\u1EE3c cung c\\u1EA5p t\\u1EA1i Ch\\u00EDnh s\\u00E1ch n\\u00E0y. N\\u1EBFu Kh\\u00E1ch h\\u00E0ng kh\\u00F4ng mu\\u1ED1n nh\\u1EADn th\\u00F4ng b\\u00E1o t\\u1EEB \\u1EE9ng d\\u1EE5ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, vui l\\u00F2ng \\u0111i\\u1EC1u ch\\u1EC9nh c\\u00E0i \\u0111\\u1EB7t th\\u00F4ng b\\u00E1o trong \\u1EE9ng d\\u1EE5ng ho\\u1EB7c thi\\u1EBFt b\\u1ECB c\\u1EE7a m\\u00ECnh.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(358, \"p\", 4)(359, \"span\", 5);\n          i0.ɵɵtext(360, \"5. Quy\\u1EC1n x\\u00F3a D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(361, \"p\", 4)(362, \"span\", 5);\n          i0.ɵɵtext(363, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n y\\u00EAu c\\u1EA7u VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT th\\u1EF1c hi\\u1EC7n x\\u00F3a D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng v\\u1EDBi \\u0111i\\u1EC1u ki\\u1EC7n l\\u00E0 y\\u00EAu c\\u1EA7u c\\u1EE7a Kh\\u00E1ch h\\u00E0ng ph\\u1EA3i ph\\u00F9 h\\u1EE3p v\\u1EDBi quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt. Tuy nhi\\u00EAn, y\\u00EAu c\\u1EA7u x\\u00F3a D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng trong m\\u1ED9t s\\u1ED1 tr\\u01B0\\u1EDDng h\\u1EE3p c\\u00F3 th\\u1EC3 d\\u1EABn t\\u1EDBi vi\\u1EC7c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT kh\\u00F4ng th\\u1EC3 cung c\\u1EA5p S\\u1EA3n ph\\u1EA9m, d\\u1ECBch v\\u1EE5 cho Kh\\u00E1ch h\\u00E0ng, \\u0111i\\u1EC1u n\\u00E0y \\u0111\\u1ED3ng ngh\\u0129a v\\u1EDBi vi\\u1EC7c VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 \\u0111\\u01A1n ph\\u01B0\\u01A1ng ch\\u1EA5m d\\u1EE9t h\\u1EE3p \\u0111\\u1ED3ng m\\u00E0 kh\\u00F4ng c\\u1EA7n ph\\u1EA3i b\\u1ED3i th\\u01B0\\u1EDDng cho Kh\\u00E1ch h\\u00E0ng do c\\u00E1c \\u0111i\\u1EC1u ki\\u1EC7n \\u0111\\u1EC3 th\\u1EF1c hi\\u1EC7n h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u00E3 thay \\u0111\\u1ED5i. Do \\u0111\\u00F3, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT khuy\\u1EBFn ngh\\u1ECB Kh\\u00E1ch h\\u00E0ng c\\u00E2n nh\\u1EAFc k\\u0129 l\\u01B0\\u1EE1ng tr\\u01B0\\u1EDBc khi y\\u00EAu c\\u1EA7u VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT th\\u1EF1c hi\\u1EC7n x\\u00F3a D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(364, \"p\", 4)(365, \"span\", 5);\n          i0.ɵɵtext(366, \"6. Quy\\u1EC1n khi\\u1EBFu n\\u1EA1i, t\\u1ED1 c\\u00E1o, kh\\u1EDFi ki\\u1EC7n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(367, \"p\", 4)(368, \"span\", 5);\n          i0.ɵɵtext(369, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n khi\\u1EBFu n\\u1EA1i, t\\u1ED1 c\\u00E1o ho\\u1EB7c kh\\u1EDFi ki\\u1EC7n theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(370, \"p\", 4)(371, \"span\", 5);\n          i0.ɵɵtext(372, \"7. Quy\\u1EC1n y\\u00EAu c\\u1EA7u b\\u1ED3i th\\u01B0\\u1EDDng thi\\u1EC7t h\\u1EA1i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(373, \"p\", 4)(374, \"span\", 5);\n          i0.ɵɵtext(375, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n y\\u00EAu c\\u1EA7u VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT b\\u1ED3i th\\u01B0\\u1EDDng thi\\u1EC7t h\\u1EA1i theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt khi x\\u1EA3y ra vi ph\\u1EA1m quy \\u0111\\u1ECBnh v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n khi th\\u1ECFa m\\u00E3n \\u0111\\u1ED3ng th\\u1EDDi c\\u00E1c \\u0111i\\u1EC1u ki\\u1EC7n sau:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(376, \"p\", 4)(377, \"span\", 5);\n          i0.ɵɵtext(378, \"- C\\u00F3 h\\u00E0nh vi vi ph\\u1EA1m quy \\u0111\\u1ECBnh ph\\u00E1p lu\\u1EADt v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(379, \"p\", 4)(380, \"span\", 5);\n          i0.ɵɵtext(381, \"- H\\u00E0nh vi vi ph\\u1EA1m n\\u00EAu tr\\u00EAn d\\u1EABn t\\u1EDBi thi\\u1EC7t h\\u1EA1i th\\u1EF1c t\\u1EBF ph\\u00E1t sinh cho Kh\\u00E1ch h\\u00E0ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(382, \"p\", 4)(383, \"span\", 5);\n          i0.ɵɵtext(384, \"- Kh\\u00E1ch h\\u00E0ng \\u0111\\u00E3 th\\u1EF1c hi\\u1EC7n \\u0111\\u1EA7y \\u0111\\u1EE7 c\\u00E1c ngh\\u0129a v\\u1EE5 v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a m\\u00ECnh theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt, Ch\\u00EDnh s\\u00E1ch n\\u00E0y v\\u00E0 theo th\\u1ECFa thu\\u1EADn kh\\u00E1c gi\\u1EEFa VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u00E0 Kh\\u00E1ch h\\u00E0ng.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(385, \"p\", 4)(386, \"span\", 5);\n          i0.ɵɵtext(387, \"8. Quy\\u1EC1n t\\u1EF1 b\\u1EA3o v\\u1EC7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(388, \"p\", 4)(389, \"span\", 5);\n          i0.ɵɵtext(390, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 quy\\u1EC1n t\\u1EF1 b\\u1EA3o v\\u1EC7 theo quy \\u0111\\u1ECBnh c\\u1EE7a B\\u1ED9 lu\\u1EADt D\\u00E2n s\\u1EF1, lu\\u1EADt kh\\u00E1c c\\u00F3 li\\u00EAn quan v\\u00E0 Ngh\\u1ECB \\u0111\\u1ECBnh 13/2023/N\\u0110-CP v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n (v\\u00E0 c\\u00E1c b\\u1EA3n s\\u1EEDa \\u0111\\u1ED5i k\\u00E8m theo), ho\\u1EB7c y\\u00EAu c\\u1EA7u c\\u01A1 quan, t\\u1ED5 ch\\u1EE9c c\\u00F3 th\\u1EA9m quy\\u1EC1n th\\u1EF1c hi\\u1EC7n c\\u00E1c ph\\u01B0\\u01A1ng th\\u1EE9c b\\u1EA3o v\\u1EC7 quy\\u1EC1n d\\u00E2n s\\u1EF1 theo quy \\u0111\\u1ECBnh t\\u1EA1i \\u0110i\\u1EC1u 11 B\\u1ED9 lu\\u1EADt D\\u00E2n s\\u1EF1.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(391, \"p\", 4)(392, \"strong\")(393, \"span\", 5);\n          i0.ɵɵtext(394, \"\\u0110i\\u1EC1u 8. Ngh\\u0129a v\\u1EE5 c\\u1EE7a Kh\\u00E1ch h\\u00E0ng\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(395, \"p\", 4)(396, \"span\", 5);\n          i0.ɵɵtext(397, \"Kh\\u00E1ch h\\u00E0ng c\\u00F3 tr\\u00E1ch nhi\\u1EC7m b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a m\\u00ECnh nh\\u01B0 sau:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(398, \"p\", 4)(399, \"span\", 5);\n          i0.ɵɵtext(400, \"1.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(401, \"span\", 2);\n          i0.ɵɵtext(402, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(403, \"span\", 5);\n          i0.ɵɵtext(404, \"Ch\\u1EE7 \\u0111\\u1ED9ng th\\u1EF1c hi\\u1EC7n c\\u00E1c bi\\u1EC7n ph\\u00E1p b\\u1EA3o v\\u1EC7, qu\\u1EA3n l\\u00FD v\\u00E0 s\\u1EED d\\u1EE5ng an to\\u00E0n t\\u00E0i kho\\u1EA3n, thi\\u1EBFt b\\u1ECB di \\u0111\\u1ED9ng c\\u1EE7a m\\u00ECnh \\u00A0(bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n b\\u1EDFi \\u0111i\\u1EC7n tho\\u1EA1i th\\u00F4ng minh, m\\u00E1y t\\u00EDnh, m\\u00E1y t\\u00EDnh b\\u1EA3ng, laptop v.v.) \\u00A0b\\u1EB1ng c\\u00E1ch \\u0111\\u0103ng xu\\u1EA5t t\\u00E0i kho\\u1EA3n sau khi s\\u1EED d\\u1EE5ng, \\u0111\\u1EB7t m\\u1ED9t m\\u1EADt kh\\u1EA9u m\\u1EA1nh v\\u00E0 kh\\u00F3 \\u0111o\\u00E1n v\\u00E0 gi\\u1EEF b\\u00ED m\\u1EADt th\\u00F4ng tin \\u0111\\u0103ng nh\\u1EADp c\\u0169ng nh\\u01B0 m\\u1EADt kh\\u1EA9u c\\u1EE7a m\\u00ECnh. C\\u00E1c bi\\u1EC7n ph\\u00E1p b\\u1EA3o v\\u1EC7 v\\u00E0 qu\\u1EA3n l\\u00FD s\\u1EED d\\u1EE5ng an to\\u00E0n t\\u00E0i kho\\u1EA3n, thi\\u1EBFt b\\u1ECB di \\u0111\\u1ED9ng n\\u00F3i tr\\u00EAn gi\\u00FAp ng\\u0103n ch\\u1EB7n vi\\u1EC7c truy c\\u1EADp tr\\u00E1i ph\\u00E9p v\\u00E0o t\\u00E0i kho\\u1EA3n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(405, \"span\", 2);\n          i0.ɵɵtext(406, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(407, \"span\", 5);\n          i0.ɵɵtext(408, \"VNPT \\u0111\\u01B0\\u1EE3c lo\\u1EA1i tr\\u1EEB tr\\u00E1ch nhi\\u1EC7m v\\u1EDBi c\\u00E1c thi\\u1EC7t h\\u1EA1i c\\u1EE7a Kh\\u00E1ch h\\u00E0ng trong tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng b\\u1ECB l\\u1ED9/m\\u1EA5t, b\\u1ECB \\u0111\\u00E1nh c\\u1EAFp m\\u1EADt kh\\u1EA9u, d\\u1EABn t\\u1EDBi vi\\u1EC7c b\\u1ECB truy c\\u1EADp tr\\u00E1i ph\\u00E9p v\\u00E0o t\\u00E0i kho\\u1EA3n, ho\\u1EB7c b\\u1EA5t k\\u1EF3 ho\\u1EA1t \\u0111\\u1ED9ng n\\u00E0o tr\\u00EAn t\\u00E0i kho\\u1EA3n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng s\\u1EED d\\u1EE5ng tr\\u00EAn thi\\u1EBFt b\\u1ECB di \\u0111\\u1ED9ng b\\u1ECB m\\u1EA5t, th\\u1EA5t l\\u1EA1c d\\u1EABn \\u0111\\u1EBFn ng\\u01B0\\u1EDDi kh\\u00F4ng c\\u00F3 th\\u1EA9m quy\\u1EC1n t\\u1EF1 \\u00FD s\\u1EED d\\u1EE5ng d\\u1ECBch v\\u1EE5, ho\\u1EB7c h\\u1EC7 th\\u1ED1ng c\\u1EE7a VNPT b\\u1ECB x\\u00E2m ph\\u1EA1m b\\u1EA5t h\\u1EE3p ph\\u00E1p b\\u1EDFi b\\u00EAn th\\u1EE9 ba m\\u1EB7c d\\u00F9 VNPT \\u0111\\u00E3 th\\u1EF1c hi\\u1EC7n \\u0111\\u1EA7y \\u0111\\u1EE7 c\\u00E1c bi\\u1EC7n ph\\u00E1p \\u0111\\u1EC3 b\\u1EA3o v\\u1EC7 h\\u1EC7 th\\u1ED1ng; \\u00A0 \\u00A0 \\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(409, \"span\", 5);\n          i0.ɵɵtext(410, \"\\u00A0 \\u00A0\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(411, \"p\", 4)(412, \"span\", 5);\n          i0.ɵɵtext(413, \"2. Kh\\u00E1ch h\\u00E0ng c\\u00F3 tr\\u00E1ch nhi\\u1EC7m c\\u1EADp nh\\u1EADp c\\u00E1c v\\u0103n b\\u1EA3n \\u0111i\\u1EC1u ch\\u1EC9nh Ch\\u00EDnh s\\u00E1ch n\\u00E0y (n\\u1EBFu c\\u00F3) tr\\u00EAn c\\u00E1c k\\u00EAnh th\\u00F4ng tin ch\\u00EDnh th\\u1ED1ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(414, \"p\", 4)(415, \"span\", 5);\n          i0.ɵɵtext(416, \"3. Khi \\u0111\\u00E3 ch\\u1EA5p thu\\u1EADn to\\u00E0n b\\u1ED9 \\u0111i\\u1EC1u kho\\u1EA3n v\\u00E0 \\u0111i\\u1EC1u ki\\u1EC7n c\\u1EE7a c\\u1EE7a Ch\\u00EDnh s\\u00E1ch n\\u00E0y, Kh\\u00E1ch h\\u00E0ng c\\u00F3 tr\\u00E1ch nhi\\u1EC7m cung c\\u1EA5p D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n \\u0111\\u1EA7y \\u0111\\u1EE7, ch\\u00EDnh x\\u00E1c theo y\\u00EAu c\\u1EA7u c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u00E0 c\\u00F3 tr\\u00E1ch nhi\\u1EC7m th\\u00F4ng b\\u00E1o cho VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ngay khi ph\\u00E1t hi\\u1EC7n h\\u00E0nh vi vi ph\\u1EA1m quy \\u0111\\u1ECBnh v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n. Kh\\u00E1ch h\\u00E0ng c\\u00F3 th\\u1EC3 ch\\u1EE7 \\u0111\\u1ED9ng cung c\\u1EA5p D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n n\\u1EB1m ngo\\u00E0i y\\u00EAu c\\u1EA7u c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT v\\u1EDBi \\u0111i\\u1EC1u ki\\u1EC7n Kh\\u00E1ch h\\u00E0ng ph\\u1EA3i tu\\u00E2n th\\u1EE7 quy \\u0111\\u1ECBnh t\\u1EA1i kho\\u1EA3n 2.5, \\u0110i\\u1EC1u 2 Ch\\u00EDnh s\\u00E1ch n\\u00E0y;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(417, \"p\", 4)(418, \"span\", 5);\n          i0.ɵɵtext(419, \"4. Kh\\u00E1ch h\\u00E0ng c\\u00F3 tr\\u00E1ch nhi\\u1EC7m t\\u00F4n tr\\u1ECDng D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a ch\\u1EE7 th\\u1EC3 kh\\u00E1c v\\u00E0 th\\u1EF1c hi\\u1EC7n quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n, tham gia ph\\u00F2ng, ch\\u1ED1ng c\\u00E1c h\\u00E0nh vi vi ph\\u1EA1m quy \\u0111\\u1ECBnh v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(420, \"p\", 4)(421, \"strong\")(422, \"span\", 5);\n          i0.ɵɵtext(423, \"\\u0110i\\u1EC1u 9. L\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(424, \"p\", 4)(425, \"span\", 5);\n          i0.ɵɵtext(426, \"1. D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng do VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT l\\u01B0u tr\\u1EEF s\\u1EBD \\u0111\\u01B0\\u1EE3c b\\u1EA3o m\\u1EADt. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT trong kh\\u1EA3 n\\u0103ng c\\u1EE7a m\\u00ECnh, s\\u1EBD n\\u1ED7 l\\u1EF1c th\\u1EF1c hi\\u1EC7n c\\u00E1c bi\\u1EC7n ph\\u00E1p h\\u1EE3p l\\u00FD \\u0111\\u1EC3 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(427, \"p\", 4)(428, \"span\", 5);\n          i0.ɵɵtext(429, \"2. \\u0110\\u1ECBa \\u0111i\\u1EC3m l\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(430, \"p\", 4)(431, \"span\", 5);\n          i0.ɵɵtext(432, \"Trong ph\\u1EA1m vi ph\\u00E1p lu\\u1EADt cho ph\\u00E9p, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 l\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng t\\u1EA1i Vi\\u1EC7t Nam v\\u00E0 \\u1EDF n\\u01B0\\u1EDBc ngo\\u00E0i, k\\u1EC3 c\\u1EA3 tr\\u00EAn gi\\u1EA3i ph\\u00E1p l\\u01B0u tr\\u1EEF \\u0111i\\u1EC7n to\\u00E1n \\u0111\\u00E1m m\\u00E2y. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT \\u00E1p d\\u1EE5ng c\\u00E1c ti\\u00EAu chu\\u1EA9n v\\u1EC1 b\\u1EA3o m\\u1EADt d\\u1EEF li\\u1EC7u ph\\u00F9 h\\u1EE3p v\\u1EDBi quy \\u0111\\u1ECBnh ph\\u00E1p lu\\u1EADt hi\\u1EC7n h\\u00E0nh.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(433, \"p\", 4)(434, \"span\", 5);\n          i0.ɵɵtext(435, \"3. Th\\u1EDDi gian l\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(436, \"p\", 4)(437, \"span\", 5);\n          i0.ɵɵtext(438, \"VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT ch\\u1EC9 th\\u1EF1c hi\\u1EC7n l\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng trong kho\\u1EA3ng th\\u1EDDi gian c\\u1EA7n thi\\u1EBFt v\\u00E0 h\\u1EE3p l\\u00FD \\u0111\\u1EC3 ho\\u00E0n th\\u00E0nh c\\u00E1c M\\u1EE5c \\u0110\\u00EDch quy \\u0111\\u1ECBnh t\\u1EA1i Ch\\u00EDnh s\\u00E1ch n\\u00E0y. Tuy nhi\\u00EAn, tr\\u01B0\\u1EDDng h\\u1EE3p ph\\u00E1p lu\\u1EADt hi\\u1EC7n h\\u00E0nh c\\u00F3 quy \\u0111\\u1ECBnh kh\\u00E1c v\\u1EC1 th\\u1EDDi h\\u1EA1n l\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 ngh\\u0129a v\\u1EE5 tu\\u00E2n th\\u1EE7 quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(439, \"p\", 4)(440, \"strong\")(441, \"span\", 5);\n          i0.ɵɵtext(442, \"\\u0110i\\u1EC1u 10. Ngh\\u0129a v\\u1EE5 c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(443, \"p\", 4)(444, \"span\", 5);\n          i0.ɵɵtext(445, \"1. D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c cam k\\u1EBFt b\\u1EA3o m\\u1EADt theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt, Ch\\u00EDnh s\\u00E1ch b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(446, \"p\", 4)(447, \"span\", 5);\n          i0.ɵɵtext(448, \"2. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT n\\u1ED7 l\\u1EF1c \\u0111\\u1EA3m b\\u1EA3o D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c b\\u1EA3o v\\u1EC7 kh\\u1ECFi c\\u00E1c h\\u00E0nh vi vi ph\\u1EA1m quy \\u0111\\u1ECBnh v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT duy tr\\u00EC cam k\\u1EBFt b\\u1EA3o m\\u1EADt D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n b\\u1EB1ng c\\u00E1ch \\u00E1p d\\u1EE5ng nh\\u1EEFng bi\\u1EC7n ph\\u00E1p v\\u1EADt l\\u00FD, \\u0111i\\u1EC7n t\\u1EED v\\u00E0 qu\\u1EA3n l\\u00FD \\u0111\\u1EC3 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n, bao g\\u1ED3m:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(449, \"p\", 4)(450, \"span\", 5);\n          i0.ɵɵtext(451, \"a) C\\u00E1c m\\u00E1y ch\\u1EE7 trang th\\u00F4ng tin \\u0111i\\u1EC7n t\\u1EED ch\\u00EDnh th\\u1EE9c c\\u1EE7a VNPT v\\u00E0 c\\u00E1c h\\u1EC7 th\\u1ED1ng th\\u00F4ng tin ch\\u1EE9a d\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a VNPT \\u0111\\u1EC1u \\u0111\\u01B0\\u1EE3c b\\u1EA3o v\\u1EC7 b\\u1EDFi c\\u00E1c bi\\u1EC7n ph\\u00E1p, c\\u00F4ng ngh\\u1EC7 b\\u1EA3o m\\u1EADt nh\\u01B0 t\\u01B0\\u1EDDng l\\u1EEDa, m\\u00E3 h\\u00F3a, ch\\u1ED1ng x\\u00E2m nh\\u1EADp tr\\u00E1i ph\\u00E9p v.v.; ban h\\u00E0nh c\\u00E1c bi\\u1EC7n ph\\u00E1p ki\\u1EC3m so\\u00E1t v\\u1EC1 con ng\\u01B0\\u1EDDi, x\\u00E2y d\\u1EF1ng quy tr\\u00ECnh ki\\u1EC3m tra, \\u0111\\u00E1nh gi\\u00E1, r\\u00E0 so\\u00E1t \\u0111\\u1EC3 ph\\u00F2ng tr\\u00E1nh c\\u00E1c h\\u00E0nh vi vi ph\\u1EA1m quy \\u0111\\u1ECBnh v\\u1EC1 b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(452, \"p\", 4)(453, \"span\", 5);\n          i0.ɵɵtext(454, \"b) VNPT s\\u1EBD th\\u1EF1c hi\\u1EC7n t\\u1EA5t c\\u1EA3 c\\u00E1c bi\\u1EC7n ph\\u00E1p ph\\u00F9 h\\u1EE3p \\u0111\\u1EC3 \\u0111\\u1EA3m b\\u1EA3o r\\u1EB1ng D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c x\\u1EED l\\u00FD \\u0111\\u00FAng v\\u1EDBi M\\u1EE5c \\u0110\\u00EDch \\u0111\\u00E3 th\\u00F4ng b\\u00E1o. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT s\\u1EBD lu\\u00F4n tu\\u00E2n th\\u1EE7 nh\\u1EEFng y\\u00EAu c\\u1EA7u c\\u1EE7a ph\\u00E1p lu\\u1EADt li\\u00EAn quan \\u0111\\u1EBFn vi\\u1EC7c l\\u01B0u tr\\u1EEF D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(455, \"p\", 4)(456, \"span\", 5);\n          i0.ɵɵtext(457, \"3. Th\\u1EF1c hi\\u1EC7n c\\u00E1c y\\u00EAu c\\u1EA7u c\\u1EE7a Kh\\u00E1ch h\\u00E0ng li\\u00EAn quan \\u0111\\u1EBFn d\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng v\\u1EDBi \\u0111i\\u1EC1u ki\\u1EC7n c\\u00E1c y\\u00EAu c\\u1EA7u c\\u1EE7a Kh\\u00E1ch h\\u00E0ng ph\\u1EA3i ph\\u00F9 h\\u1EE3p v\\u1EDBi quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(458, \"p\", 4)(459, \"span\", 5);\n          i0.ɵɵtext(460, \"4. C\\u00E1c ngh\\u0129a v\\u1EE5 kh\\u00E1c theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt v\\u00E0 c\\u1EE7a Ch\\u00EDnh s\\u00E1ch n\\u00E0y.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(461, \"p\", 4)(462, \"strong\")(463, \"span\", 5);\n          i0.ɵɵtext(464, \"\\u0110i\\u1EC1u 11. H\\u1EADu qu\\u1EA3, thi\\u1EC7t h\\u1EA1i kh\\u00F4ng mong mu\\u1ED1n c\\u00F3 kh\\u1EA3 n\\u0103ng x\\u1EA3y ra\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(465, \"p\", 4)(466, \"span\", 5);\n          i0.ɵɵtext(467, \"1. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT s\\u1EED d\\u1EE5ng nhi\\u1EC1u bi\\u1EC7n ph\\u00E1p, c\\u00F4ng ngh\\u1EC7 b\\u1EA3o m\\u1EADt th\\u00F4ng tin kh\\u00E1c nhau nh\\u1EB1m b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng kh\\u00F4ng b\\u1ECB s\\u1EED d\\u1EE5ng ho\\u1EB7c chia s\\u1EBB ngo\\u00E0i \\u00FD mu\\u1ED1n. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT cam k\\u1EBFt s\\u1EBD b\\u1EA3o m\\u1EADt m\\u1ED9t c\\u00E1ch t\\u1ED1i \\u0111a D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng. M\\u1ED9t s\\u1ED1 h\\u1EADu qu\\u1EA3, thi\\u1EC7t h\\u1EA1i kh\\u00F4ng mong mu\\u1ED1n c\\u00F3 th\\u1EC3 x\\u1EA3y ra bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(468, \"p\", 4)(469, \"span\", 5);\n          i0.ɵɵtext(470, \"a) L\\u1ED7i ph\\u1EA7n c\\u1EE9ng, ph\\u1EA7n m\\u1EC1m trong qu\\u00E1 tr\\u00ECnh x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n g\\u00E2y \\u1EA3nh h\\u01B0\\u1EDFng kh\\u00F4ng mong mu\\u1ED1n (l\\u1ED7i, h\\u1ECFng, m\\u1EA5t v.v.) D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(471, \"p\", 4)(472, \"span\", 5);\n          i0.ɵɵtext(473, \"b) L\\u1ED7 h\\u1ED5ng b\\u1EA3o m\\u1EADt n\\u1EB1m ngo\\u00E0i kh\\u1EA3 n\\u0103ng ki\\u1EC3m so\\u00E1t c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, h\\u1EC7 th\\u1ED1ng b\\u1ECB hacker t\\u1EA5n c\\u00F4ng g\\u00E2y l\\u1ED9 l\\u1ECDt D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(474, \"p\", 4)(475, \"span\", 5);\n          i0.ɵɵtext(476, \"c) Kh\\u00E1ch h\\u00E0ng t\\u1EF1 l\\u00E0m l\\u1ED9 l\\u1ECDt D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng do: b\\u1EA5t c\\u1EA9n ho\\u1EB7c b\\u1ECB l\\u1EEBa \\u0111\\u1EA3o; truy c\\u1EADp c\\u00E1c website/t\\u1EA3i c\\u00E1c \\u1EE9ng d\\u1EE5ng c\\u00F3 ch\\u1EE9a ph\\u1EA7n m\\u1EC1m \\u0111\\u1ED9c h\\u1EA1i; t\\u1EF1 \\u00FD chia s\\u1EBB th\\u00F4ng tin v\\u1EDBi ng\\u01B0\\u1EDDi kh\\u00E1c v.v.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(477, \"p\", 4)(478, \"span\", 5);\n          i0.ɵɵtext(479, \"2. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT khuy\\u1EBFn c\\u00E1o Kh\\u00E1ch h\\u00E0ng th\\u1EF1c hi\\u1EC7n nghi\\u00EAm ng\\u1EB7t c\\u00E1c tr\\u00E1ch nhi\\u1EC7m b\\u1EA3o v\\u1EC7 D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n theo quy \\u0111\\u1ECBnh t\\u1EA1i \\u0110i\\u1EC1u 8 Ch\\u00EDnh s\\u00E1ch n\\u00E0y v\\u00E0 theo quy \\u0111\\u1ECBnh c\\u1EE7a ph\\u00E1p lu\\u1EADt.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(480, \"p\", 4)(481, \"span\", 5);\n          i0.ɵɵtext(482, \"3. Tr\\u01B0\\u1EDDng h\\u1EE3p m\\u00E1y ch\\u1EE7 l\\u01B0u tr\\u1EEF d\\u1EEF li\\u1EC7u b\\u1ECB hacker t\\u1EA5n c\\u00F4ng d\\u1EABn \\u0111\\u1EBFn m\\u1EA5t m\\u00E1t D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 tr\\u00E1ch nhi\\u1EC7m th\\u00F4ng b\\u00E1o v\\u1EE5 vi\\u1EC7c cho c\\u01A1 quan c\\u00F3 th\\u1EA9m quy\\u1EC1n \\u0111\\u1EC3 \\u0111i\\u1EC1u tra x\\u1EED l\\u00FD k\\u1ECBp th\\u1EDDi v\\u00E0 th\\u00F4ng b\\u00E1o cho Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c bi\\u1EBFt.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(483, \"p\", 4)(484, \"strong\")(485, \"span\", 5);\n          i0.ɵɵtext(486, \"\\u0110i\\u1EC1u 12. S\\u1EED d\\u1EE5ng cookies\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(487, \"p\", 4)(488, \"span\", 5);\n          i0.ɵɵtext(489, \"1. Cookies c\\u00F3 th\\u1EC3 \\u0111\\u01B0\\u1EE3c s\\u1EED d\\u1EE5ng tr\\u00EAn website c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. \\u201CCookies\\u201D l\\u00E0 c\\u00E1c t\\u1EC7p v\\u0103n b\\u1EA3n nh\\u1ECF \\u0111\\u01B0\\u1EE3c l\\u01B0u tr\\u00EAn \\u1ED5 c\\u1EE9ng m\\u00E1y t\\u00EDnh c\\u1EE7a Kh\\u00E1ch h\\u00E0ng nh\\u1EB1m gi\\u00FAp t\\u00F9y bi\\u1EBFn tr\\u1EA3i nghi\\u1EC7m website cho ng\\u01B0\\u1EDDi d\\u00F9ng. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT s\\u1EED d\\u1EE5ng cookies \\u0111\\u1EC3 vi\\u1EC7c \\u0111i\\u1EC1u h\\u01B0\\u1EDBng trang web d\\u1EC5 d\\u00E0ng h\\u01A1n v\\u00E0 t\\u1EA1o \\u0111i\\u1EC1u ki\\u1EC7n thu\\u1EADn l\\u1EE3i cho qu\\u00E1 tr\\u00ECnh \\u0111\\u0103ng nh\\u1EADp.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(490, \"span\", 2);\n          i0.ɵɵtext(491, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(492, \"span\", 5);\n          i0.ɵɵtext(493, \"D\\u1EEF li\\u1EC7u th\\u1ED1ng k\\u00EA tr\\u00EAn trang web c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 \\u0111\\u01B0\\u1EE3c x\\u1EED l\\u00FD b\\u1EDFi c\\u00E1c b\\u00EAn th\\u1EE9 ba, v\\u00E0 do \\u0111\\u00F3, \\u0111\\u1ECBa ch\\u1EC9 IP c\\u1EE7a Kh\\u00E1ch h\\u00E0ng khi truy c\\u1EADp trang web s\\u1EBD \\u0111\\u01B0\\u1EE3c chuy\\u1EC3n t\\u1EDBi b\\u00EAn th\\u1EE9 ba ch\\u1EC9 v\\u1EDBi m\\u1EE5c \\u0111\\u00EDch b\\u00E1o c\\u00E1o th\\u1ED1ng k\\u00EA.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(494, \"p\", 4)(495, \"span\", 5);\n          i0.ɵɵtext(496, \"2. Khi thu th\\u1EADp th\\u00F4ng tin qua website c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT t\\u1EF1 \\u0111\\u1ED9ng thu th\\u1EADp nh\\u1EEFng th\\u00F4ng tin kh\\u00F4ng mang t\\u00EDnh c\\u00E1 nh\\u00E2n, kh\\u00F4ng x\\u00E1c \\u0111\\u1ECBnh danh t\\u00EDnh nh\\u01B0ng c\\u00F3 li\\u00EAn quan t\\u1EDBi vi\\u1EC7c s\\u1EED d\\u1EE5ng trang web bao g\\u1ED3m nh\\u01B0ng kh\\u00F4ng gi\\u1EDBi h\\u1EA1n nh\\u01B0: \\u0111\\u1ECBa ch\\u1EC9 IP c\\u1EE7a m\\u00E1y t\\u00EDnh c\\u00E1 nh\\u00E2n, \\u0111\\u1ECBa ch\\u1EC9 IP c\\u1EE7a nh\\u00E0 cung c\\u1EA5p d\\u1ECBch v\\u1EE5 Internet, ng\\u00E0y gi\\u1EDD truy c\\u1EADp trang web, h\\u1EC7 \\u0111i\\u1EC1u h\\u00E0nh, c\\u00E1c m\\u1EE5c \\u0111\\u00E3 truy c\\u1EADp tr\\u00EAn trang web, n\\u1ED9i dung t\\u1EA3i v\\u1EC1 t\\u1EEB trang web v\\u00E0 \\u0111\\u1ECBa ch\\u1EC9 trang web \\u0111\\u00E3 tr\\u1EF1c ti\\u1EBFp d\\u1EABn Kh\\u00E1ch h\\u00E0ng t\\u1EDBi trang web c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 d\\u00F9ng nh\\u1EEFng th\\u00F4ng tin n\\u00E0y \\u0111\\u1EC3 ph\\u1EE5c v\\u1EE5 cho nh\\u1EEFng b\\u00E1o c\\u00E1o th\\u1ED1ng k\\u00EA v\\u1EC1 l\\u01B0u l\\u01B0\\u1EE3ng ng\\u01B0\\u1EDDi truy c\\u1EADp trang web v\\u00E0 h\\u00E0nh vi s\\u1EED d\\u1EE5ng trang web.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(497, \"p\", 4)(498, \"span\", 5);\n          i0.ɵɵtext(499, \"3. Tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng kh\\u00F4ng mu\\u1ED1n cho ph\\u00E9p s\\u1EED d\\u1EE5ng cookies, Kh\\u00E1ch h\\u00E0ng c\\u00F3 th\\u1EC3 t\\u1EEB ch\\u1ED1i b\\u1EB1ng c\\u00E1ch thay \\u0111\\u1ED5i c\\u00E0i \\u0111\\u1EB7t ri\\u00EAng t\\u01B0 tr\\u00EAn tr\\u00ECnh duy\\u1EC7t web. Vi\\u1EC7c t\\u1EEB ch\\u1ED1i cho ph\\u00E9p s\\u1EED d\\u1EE5ng cookies h\\u1EA7u nh\\u01B0 kh\\u00F4ng g\\u00E2y \\u1EA3nh h\\u01B0\\u1EDFng t\\u1EDBi vi\\u1EC7c \\u0111i\\u1EC1u h\\u01B0\\u1EDBng trang web. Tuy nhi\\u00EAn m\\u1ED9t s\\u1ED1 ch\\u1EE9c n\\u0103ng c\\u1EE7a trang web c\\u00F3 th\\u1EC3 b\\u1ECB \\u1EA3nh h\\u01B0\\u1EDFng. Sau khi ho\\u00E0n th\\u00E0nh vi\\u1EC7c truy c\\u1EADp trang web, Kh\\u00E1ch h\\u00E0ng v\\u1EABn c\\u00F3 th\\u1EC3 x\\u00F3a cookies kh\\u1ECFi h\\u1EC7 th\\u1ED1ng n\\u1EBFu mu\\u1ED1n.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(500, \"p\", 4)(501, \"strong\")(502, \"span\", 5);\n          i0.ɵɵtext(503, \"\\u0110i\\u1EC1u 13. Qu\\u1EA3ng c\\u00E1o tr\\u00EAn internet v\\u00E0 b\\u00EAn th\\u1EE9 ba\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(504, \"p\", 4)(505, \"span\", 5);\n          i0.ɵɵtext(506, \"C\\u00E1c website/wapsite/\\u1EE9ng d\\u1EE5ng c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 bao g\\u1ED3m qu\\u1EA3ng c\\u00E1o c\\u1EE7a b\\u00EAn th\\u1EE9 ba v\\u00E0 li\\u00EAn k\\u1EBFt t\\u1EDBi c\\u00E1c website/wapsite/\\u1EE9ng d\\u1EE5ng kh\\u00E1c. C\\u00E1c \\u0111\\u1ED1i t\\u00E1c qu\\u1EA3ng c\\u00E1o b\\u00EAn th\\u1EE9 ba c\\u00F3 th\\u1EC3 thu th\\u1EADp th\\u00F4ng tin v\\u1EC1 Kh\\u00E1ch h\\u00E0ng khi Kh\\u00E1ch h\\u00E0ng t\\u01B0\\u01A1ng t\\u00E1c v\\u1EDBi n\\u1ED9i dung, qu\\u1EA3ng c\\u00E1o ho\\u1EB7c d\\u1ECBch v\\u1EE5 c\\u1EE7a h\\u1ECD. M\\u1ECDi quy\\u1EC1n truy c\\u1EADp v\\u00E0 s\\u1EED d\\u1EE5ng c\\u00E1c li\\u00EAn k\\u1EBFt ho\\u1EB7c trang website c\\u1EE7a b\\u00EAn th\\u1EE9 ba kh\\u00F4ng b\\u1ECB \\u0111i\\u1EC1u ch\\u1EC9nh b\\u1EDFi Ch\\u00EDnh s\\u00E1ch n\\u00E0y, m\\u00E0 thay v\\u00E0o \\u0111\\u00F3 \\u0111\\u01B0\\u1EE3c \\u0111i\\u1EC1u ch\\u1EC9nh b\\u1EDFi Ch\\u00EDnh s\\u00E1ch quy\\u1EC1n ri\\u00EAng t\\u01B0 c\\u1EE7a c\\u00E1c b\\u00EAn th\\u1EE9 ba \\u0111\\u00F3. VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT kh\\u00F4ng ch\\u1ECBu tr\\u00E1ch nhi\\u1EC7m v\\u1EC1 c\\u00E1c quy \\u0111\\u1ECBnh v\\u1EC1 th\\u00F4ng tin c\\u1EE7a c\\u00E1c b\\u00EAn th\\u1EE9 ba.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(507, \"p\", 4)(508, \"strong\")(509, \"span\", 5);\n          i0.ɵɵtext(510, \"\\u0110i\\u1EC1u 14. X\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n kh\\u00F4ng c\\u1EA7n s\\u1EF1 \\u0111\\u1ED3ng \\u00FD c\\u1EE7a ch\\u1EE7 th\\u1EC3 d\\u1EEF li\\u1EC7u\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(511, \"p\", 4)(512, \"span\", 5);\n          i0.ɵɵtext(513, \"VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT c\\u00F3 th\\u1EC3 ti\\u1EBFn h\\u00E0nh x\\u1EED l\\u00FD D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n m\\u00E0 kh\\u00F4ng c\\u1EA7n s\\u1EF1 \\u0111\\u1ED3ng \\u00FD c\\u1EE7a ch\\u1EE7 th\\u1EC3 d\\u1EEF li\\u1EC7u trong c\\u00E1c tr\\u01B0\\u1EDDng h\\u1EE3p sau:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(514, \"p\", 4)(515, \"span\", 5);\n          i0.ɵɵtext(516, \"1.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(517, \"strong\")(518, \"span\", 5);\n          i0.ɵɵtext(519, \"\\u00A0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(520, \"span\", 5);\n          i0.ɵɵtext(521, \"Trong tr\\u01B0\\u1EDDng h\\u1EE3p kh\\u1EA9n c\\u1EA5p, c\\u1EA7n x\\u1EED l\\u00FD ngay D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n c\\u00F3 li\\u00EAn quan \\u0111\\u1EC3 b\\u1EA3o v\\u1EC7 t\\u00EDnh m\\u1EA1ng, s\\u1EE9c kh\\u1ECFe c\\u1EE7a ch\\u1EE7 th\\u1EC3 d\\u1EEF li\\u1EC7u ho\\u1EB7c ng\\u01B0\\u1EDDi kh\\u00E1c;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(522, \"p\", 4)(523, \"span\", 5);\n          i0.ɵɵtext(524, \"2. Vi\\u1EC7c c\\u00F4ng khai D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n theo quy \\u0111\\u1ECBnh c\\u1EE7a lu\\u1EADt;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(525, \"p\", 4)(526, \"span\", 5);\n          i0.ɵɵtext(527, \"3. Vi\\u1EC7c x\\u1EED l\\u00FD d\\u1EEF li\\u1EC7u c\\u1EE7a c\\u01A1 quan nh\\u00E0 n\\u01B0\\u1EDBc c\\u00F3 th\\u1EA9m quy\\u1EC1n trong tr\\u01B0\\u1EDDng h\\u1EE3p t\\u00ECnh tr\\u1EA1ng kh\\u1EA9n c\\u1EA5p v\\u1EC1 qu\\u1ED1c ph\\u00F2ng, an ninh qu\\u1ED1c gia, tr\\u1EADt t\\u1EF1 an to\\u00E0n x\\u00E3 h\\u1ED9i, th\\u1EA3m h\\u1ECDa l\\u1EDBn, d\\u1ECBch b\\u1EC7nh nguy hi\\u1EC3m; khi c\\u00F3 nguy c\\u01A1 \\u0111e d\\u1ECDa an ninh, qu\\u1ED1c ph\\u00F2ng nh\\u01B0ng ch\\u01B0a \\u0111\\u1EBFn m\\u1EE9c ban b\\u1ED1 t\\u00ECnh tr\\u1EA1ng kh\\u1EA9n c\\u1EA5p; ph\\u00F2ng, ch\\u1ED1ng b\\u1EA1o lo\\u1EA1n, kh\\u1EE7ng b\\u1ED1, ph\\u00F2ng, ch\\u1ED1ng t\\u1ED9i ph\\u1EA1m v\\u00E0 vi ph\\u1EA1m ph\\u00E1p lu\\u1EADt theo quy \\u0111\\u1ECBnh c\\u1EE7a lu\\u1EADt;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(528, \"p\", 4)(529, \"span\", 5);\n          i0.ɵɵtext(530, \"4. \\u0110\\u1EC3 th\\u1EF1c hi\\u1EC7n ngh\\u0129a v\\u1EE5 theo h\\u1EE3p \\u0111\\u1ED3ng c\\u1EE7a ch\\u1EE7 th\\u1EC3 d\\u1EEF li\\u1EC7u v\\u1EDBi c\\u01A1 quan, t\\u1ED5 ch\\u1EE9c, c\\u00E1 nh\\u00E2n c\\u00F3 li\\u00EAn quan theo quy \\u0111\\u1ECBnh c\\u1EE7a lu\\u1EADt;\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(531, \"p\", 4)(532, \"span\", 5);\n          i0.ɵɵtext(533, \"5. Ph\\u1EE5c v\\u1EE5 ho\\u1EA1t \\u0111\\u1ED9ng c\\u1EE7a c\\u01A1 quan nh\\u00E0 n\\u01B0\\u1EDBc \\u0111\\u00E3 \\u0111\\u01B0\\u1EE3c quy \\u0111\\u1ECBnh theo lu\\u1EADt chuy\\u00EAn ng\\u00E0nh.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(534, \"p\", 4)(535, \"strong\")(536, \"span\", 5);\n          i0.ɵɵtext(537, \"\\u0110i\\u1EC1u 15. Th\\u00F4ng tin li\\u00EAn l\\u1EA1c\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(538, \"p\", 4)(539, \"span\", 5);\n          i0.ɵɵtext(540, \"Tr\\u01B0\\u1EDDng h\\u1EE3p Kh\\u00E1ch h\\u00E0ng c\\u00F3 b\\u1EA5t k\\u1EF3 c\\u00E2u h\\u1ECFi n\\u00E0o v\\u1EC1 Ch\\u00EDnh s\\u00E1ch n\\u00E0y ho\\u1EB7c mu\\u1ED1n th\\u1EF1c hi\\u1EC7n c\\u00E1c quy\\u1EC1n c\\u1EE7a Kh\\u00E1ch h\\u00E0ng li\\u00EAn quan t\\u1EDBi D\\u1EEF li\\u1EC7u c\\u00E1 nh\\u00E2n, vui l\\u00F2ng li\\u00EAn h\\u1EC7 v\\u1EDBi VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT theo c\\u00E1c ph\\u01B0\\u01A1ng th\\u1EE9c v\\u00E0 th\\u00F4ng tin d\\u01B0\\u1EDBi \\u0111\\u00E2y:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(541, \"p\", 4)(542, \"span\", 5);\n          i0.ɵɵtext(543, \"1. Li\\u00EAn h\\u1EC7 t\\u1EDBi t\\u1ED5ng \\u0111\\u00E0i theo th\\u00F4ng tin t\\u1EA1i c\\u00E1c website/wapsite/\\u1EE9ng d\\u1EE5ng ch\\u00EDnh th\\u1EE9c c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT t\\u1EA1i t\\u1EEBng th\\u1EDDi \\u0111i\\u1EC3m.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(544, \"p\", 4)(545, \"span\", 5);\n          i0.ɵɵtext(546, \"2. G\\u1EEDi c\\u00F4ng v\\u0103n t\\u1EDBi c\\u00E1c \\u0111\\u1ECBa ch\\u1EC9 sau \\u0111\\u00E2y:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(547, \"p\", 4)(548, \"span\", 5);\n          i0.ɵɵtext(549, \"a) T\\u1ED5ng C\\u00F4ng ty D\\u1ECBch v\\u1EE5 Vi\\u1EC5n th\\u00F4ng (VNPT VinaPhone): t\\u1EA1i To\\u00E0 nh\\u00E0 VinaPhone, \\u0111\\u01B0\\u1EDDng Xu\\u00E2n T\\u1EA3o, Ph\\u01B0\\u1EDDng Xu\\u00E2n La, Qu\\u1EADn T\\u00E2y H\\u1ED3, H\\u00E0 N\\u1ED9i.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(550, \"p\", 4)(551, \"span\", 5);\n          i0.ɵɵtext(552, \"b) T\\u1ED5ng C\\u00F4ng ty Truy\\u1EC1n th\\u00F4ng (VNPT Media): t\\u1EA1i T\\u00F2a nh\\u00E0 VNPT, 57 Hu\\u1EF3nh Th\\u00FAc Kh\\u00E1ng, Ph\\u01B0\\u1EDDng L\\u00E1ng H\\u1EA1, Qu\\u1EADn \\u0110\\u1ED1ng \\u0110a, H\\u00E0 N\\u1ED9i.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(553, \"p\", 4)(554, \"span\", 5);\n          i0.ɵɵtext(555, \"c) C\\u00F4ng ty C\\u00F4ng ngh\\u1EC7 th\\u00F4ng tin VNPT (VNPT IT): t\\u1EA1i T\\u00F2a nh\\u00E0 VNPT, 57 Hu\\u1EF3nh Th\\u00FAc Kh\\u00E1ng, Ph\\u01B0\\u1EDDng L\\u00E1ng H\\u1EA1, Qu\\u1EADn \\u0110\\u1ED1ng \\u0110a, H\\u00E0 N\\u1ED9i.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(556, \"p\", 4)(557, \"span\", 5);\n          i0.ɵɵtext(558, \"3. Li\\u00EAn h\\u1EC7 tr\\u1EF1c ti\\u1EBFp t\\u1EA1i c\\u00E1c \\u0111i\\u1EC3m giao d\\u1ECBch c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT tr\\u00EAn ph\\u1EA1m vi to\\u00E0n qu\\u1ED1c.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(559, \"p\", 4)(560, \"span\", 5);\n          i0.ɵɵtext(561, \"4. C\\u00E1c c\\u00E1ch th\\u1EE9c li\\u00EAn h\\u1EC7 kh\\u00E1c nh\\u01B0 Livechat, li\\u00EAn h\\u1EC7 qua fanpage ch\\u00EDnh th\\u1EE9c c\\u1EE7a VNPT/C\\u00F4ng ty con c\\u1EE7a VNPT, email ch\\u0103m s\\u00F3c Kh\\u00E1ch h\\u00E0ng \\u0111\\u01B0\\u1EE3c cung c\\u1EA5p cho Kh\\u00E1ch h\\u00E0ng t\\u1EA1i m\\u1ECDi th\\u1EDDi \\u0111i\\u1EC3m\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(562, \"p\");\n          i0.ɵɵelement(563, \"br\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(564, \"p\");\n          i0.ɵɵelement(565, \"br\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(566, \"p\");\n          i0.ɵɵelement(567, \"br\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(568, \"div\", 7);\n          i0.ɵɵelement(569, \"br\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\"#page_content_inner[_ngcontent-%COMP%]{\\n        padding: 24px;\\n    }\\n    em[_ngcontent-%COMP%] {\\n        color: #d05;\\n        font-style: italic;\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "PersonalDataProtectionContentComponent", "constructor", "formBuilder", "injector", "ngOnInit", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "PersonalDataProtectionContentComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\term-policy\\personal-data-protection-policy\\app.personal.data.protection.policy.content.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\term-policy\\personal-data-protection-policy\\app.personal.data.protection.policy.content.component.html"], "sourcesContent": ["import { Component, Inject, Injector, OnInit } from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\n\r\n@Component({\r\n    selector: \"personal-data-protection-policy-content\",\r\n    templateUrl: './app.personal.data.protection.policy.content.component.html'\r\n})\r\nexport class PersonalDataProtectionContentComponent extends ComponentBase implements OnInit{\r\n    constructor(\r\n        private formBuilder: FormBuilder,\r\n        private injector: Injector) {\r\n            super(injector);\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        \r\n    }\r\n}", "<style>\r\n    #page_content_inner{\r\n        padding: 24px;\r\n    }\r\n    em {\r\n        color: #d05;\r\n        font-style: italic;\r\n    }\r\n</style>\r\n<div>\r\n    <div id=\"page_content_inner\">\r\n        <p style=\"text-align: center;\"><strong><span style=\"font-size:13.999999999999998pt;\">CHÍNH SÁCH BẢO VỆ DỮ LIỆU CÁ NHÂN ĐỐI VỚI KHÁCH HÀNG CỦA VNPT VÀ CÔNG TY CON CỦA VNPT</span></strong></p>\r\n        <p style=\"text-align: center;\"><span style=\"font-size:13pt;\">Ngày cập nhật: 01/07/2023&nbsp;</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">&nbsp;</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Chính sách bảo vệ dữ liệu cá nhân đối với Khách hàng của Tập đoà<PERSON> ch<PERSON>h Viễn thông <PERSON>, các <PERSON> ty con của Tập đoàn <PERSON> ch<PERSON> Viễ<PERSON> thông Việt Nam (sau đây gọi tắt là “</span><strong><span style=\"font-size:12pt;\">Chính sách</span></strong><span style=\"font-size:12pt;\">”) nhằm mục đích thông báo với Khách hàng những Dữ liệu cá nhân của Khách hàng do Tập đoàn Bưu chính Viễn thông Việt Nam&nbsp;</span><strong><span style=\"font-size:12pt;\">(“VNPT</span></strong><span style=\"font-size:12pt;\">”) và/hoặc do các Công ty con có 100% vốn điều lệ của VNPT (</span><strong><span style=\"font-size:12pt;\">“Công ty con của VNPT”)&nbsp;</span></strong><span style=\"font-size:12pt;\">xử lý, mục đích xử lý, cách thức xử lý, thời gian lưu trữ, quyền, nghĩa vụ của Khách hàng đối với Dữ liệu cá nhân của mình theo quy định của pháp luật Việt Nam về bảo vệ Dữ liệu cá nhân v.v. Chính sách này đồng thời đưa ra các khuyến nghị để giúp Khách hàng nâng cao nhận thức về bảo vệ Dữ liệu cá nhân.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Chính sách này là một phần không thể tách rời của các bản Hợp đồng, Điều kiện giao dịch chung, Điều khoản và Điều kiện sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT. Chính sách này được áp dụng cho toàn bộ các nền tảng bao gồm nhưng không giới hạn bởi: trang thông tin điện tử, kênh giao diện, phương tiện, công cụ trên các website/wapsite/ứng dụng của VNPT/Công ty con của VNPT, việc tư vấn và ký kết hợp đồng cung cấp dịch vụ tại các điểm cung cấp dịch vụ viễn thông của VNPT/Công ty con của VNPT, tại địa chỉ do Khách hàng chỉ định và/hoặc tại một địa điểm khác do Khách hàng hoặc VNPT/Công ty con của VNPT lựa chọn. &nbsp;&nbsp;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Trong phạm vi pháp luật cho phép, VNPT/Công ty con của VNPT có thể điều chỉnh Chính sách này vào bất kỳ thời điểm nào đồng thời đăng tải công khai Chính sách được điều chỉnh trên các kênh thông tin chính thống của VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Bằng việc tích vào ô&nbsp;</span><em><span style=\"font-size:12pt;\">\"Tôi đã đọc và chấp thuận\"&nbsp;</span></em><span style=\"font-size:12pt;\">hoặc&nbsp;</span><em><span style=\"font-size:12pt;\">\"Tôi đồng ý với Chính sách, Điều khoản sử dụng của VNPT/Công ty con của VNPT”&nbsp;</span></em><span style=\"font-size:12pt;\">hoặc bằng việc ký kết hợp đồng với VNPT/Công ty con của VNPT có dẫn chiếu tới Chính sách này (và các bản sửa đổi kèm theo), hoặc bằng việc tiếp tục đăng ký, đăng nhập, sử dụng website/wapsite/ứng dụng của VNPT/Công ty con của VNPT hoặc sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT mà không có bất kì khiếu nại nào đối với Chính sách này (và các bản sửa đổi kèm theo), Khách hàng xác nhận rằng đã đọc kỹ, hiểu rõ và chấp thuận toàn bộ nội dung Chính sách bảo vệ Dữ liệu cá nhân của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 1.</span></strong><span style=\"font-size:12pt;\">&nbsp;</span><strong><span style=\"font-size:12pt;\">Giải thích từ ngữ và các từ viết tắt</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Trong phạm vi Chính sách này, các thuật ngữ dưới đây được hiểu và giải thích như sau:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.1.</span><span style=\"font-size:6.999999999999999pt;\">&nbsp; &nbsp; &nbsp;</span><strong><span style=\"font-size:12pt;\">&nbsp;VNPT</span></strong><span style=\"font-size:12pt;\">&nbsp;là Tập Đoàn Bưu Chính Viễn Thông Việt Nam và các đơn vị trực thuộc Tập Đoàn Bưu Chính Viễn Thông Việt Nam.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.2.</span><span style=\"font-size:6.999999999999999pt;\">&nbsp; &nbsp; &nbsp;</span><strong><span style=\"font-size:12pt;\">Công ty con của VNPT</span></strong><span style=\"font-size:12pt;\">&nbsp;là các doanh nghiệp do VNPT sở hữu 100% vốn điều lệ. Công ty con của VNPT bao gồm Tổng công ty Dịch vụ Viễn thông (VNPT VinaPhone) và Tổng công ty Truyền thông (VNPT Media).</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.3.</span><span style=\"font-size:6.999999999999999pt;\">&nbsp; &nbsp; &nbsp;</span><strong><span style=\"font-size:12pt;\">Khách hàng&nbsp;</span></strong><span style=\"font-size:12pt;\">là:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Cá nhân hoặc người đại diện hợp pháp của cá nhân sử dụng và/hoặc quan tâm tới các sản phẩm, dịch vụ của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Cá nhân hoặc người đại diện hợp pháp của cá nhân đã truy cập và/hoặc đăng ký tài khoản tại các website/wapsite/ứng dụng thuộc quyền sở hữu của VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.4.</span><span style=\"font-size:6.999999999999999pt;\">&nbsp; &nbsp;</span><span style=\"font-size:12pt;\">&nbsp;</span><strong><span style=\"font-size:12pt;\">Sản phẩm, dịch vụ của VNPT/Công ty con của VNPT</span></strong><span style=\"font-size:12pt;\">&nbsp;là:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Sản phẩm, dịch vụ do VNPT hoặc các Công ty con của VNPT trực tiếp nghiên cứu, phát triển và cung cấp cho Khách hàng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Sản phẩm, dịch vụ do VNPT/Công ty con của VNPT hợp tác với đối tác để cung cấp cho Khách hàng.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 2. Xử lý Dữ liệu cá nhân</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.</span><strong><span style=\"font-size:12pt;\">&nbsp;</span></strong><span style=\"font-size:12pt;\">VNPT/Công ty con của VNPT tiến hành xử lý Dữ liệu cá nhân trong những trường hợp dưới đây bao gồm nhưng không giới hạn bởi:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Khi Khách hàng hoặc người đại diện hợp pháp của Khách hàng liên hệ với VNPT/Công ty con của VNPT để yêu cầu tư vấn sản phẩm, dịch vụ của VNPT/Công ty con của VNPT hoặc bày tỏ sự quan tâm tới các sản phẩm, dịch vụ của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Khi Khách hàng ký kết hợp đồng, đăng ký, sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">c) Khi Khách hàng truy cập và/hoặc đăng ký tài khoản tại các website/wapsite/ứng dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">d) Khi Khách hàng đồng thuận cung cấp Dữ liệu cá nhân cho VNPT/Công ty con của VNPT qua các nguồn công khai, bao gồm nhưng không giới hạn: website/wapsite/ứng dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT; cuộc họp, sự kiện, hội thảo, hội nghị, các mạng xã hội, hay chương trình đối thoại, thảo luận do VNPT/Công ty con của VNPT tổ chức, tài trợ hoặc tham dự và/hoặc từ các tệp lưu trữ (cookies) ghi nhận được trên website của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">e) Khi khách hàng của một tổ chức, doanh nghiệp cho phép tổ chức, doanh nghiệp đó chia sẻ dữ liệu cá nhân của khách hàng với VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">g) Là khách hàng của một tổ chức, doanh nghiệp được VNPT/Công ty con của VNPT thực hiện góp vốn, mua cổ phần; hoặc là khách hàng của một tổ chức, doanh nghiệp có hoạt động hợp tác cung cấp sản phẩm, dịch vụ với VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">h) Khi có yêu cầu của các cơ quan nhà nước có thẩm quyền.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">i) Khi VNPT/Công ty con của VNPT tiến hành các công việc theo mục đích xử lý Dữ liệu cá nhân được quy định tại Điều 3 Chính sách này.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Dữ liệu cá nhân của Khách hàng được VNPT/Công ty con của VNPT tiến hành xử lý (sau đây gọi tắt là “</span><strong><span style=\"font-size:12pt;\">Dữ liệu cá nhân</span></strong><span style=\"font-size:12pt;\">”) bao gồm nhưng không giới hạn những thông tin dưới đây và có thể thay đổi tùy thuộc vào loại sản phẩm hoặc dịch vụ, cách thức tương tác của Khách hàng với VNPT/Công ty con của VNPT:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2.1. Dữ liệu cá nhân cơ bản</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Họ, chữ đệm và tên khai sinh, tên gọi khác (nếu có);</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Ngày, tháng, năm sinh; ngày, tháng, năm chết hoặc mất tích;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">c) Giới tính;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">d) Nơi sinh, nơi đăng ký khai sinh, nơi thường trú, nơi tạm trú, nơi ở hiện tại, quê quán, địa chỉ liên hệ;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">đ) Quốc tịch;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">e) Hình ảnh của cá nhân;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">g) Số điện thoại, số chứng minh nhân dân, số định danh cá nhân, số hộ chiếu, số giấy phép lái xe, số biển số xe, số mã số thuế cá nhân, số bảo hiểm xã hội, số thẻ bảo hiểm y tế;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">h) Tình trạng hôn nhân;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">i) Thông tin về mối quan hệ gia đình (cha mẹ, con cái);</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">k) Thông tin về tài khoản số của cá nhân; dữ liệu cá nhân phản ánh hoạt động, lịch sử hoạt động trên không gian mạng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2.2. Dữ liệu cá nhân nhạy cảm</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Dữ liệu về tội phạm, hành vi phạm tội được thu thập, lưu trữ bởi các cơ quan thực thi pháp luật;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Thông tin Khách hàng của tổ chức tín dụng, chi nhánh ngân hàng nước ngoài, tổ chức cung ứng dịch vụ trung gian thanh toán, các tổ chức được phép khác, gồm: thông tin định danh Khách hàng theo quy định của pháp luật, thông tin về tài khoản, thông tin về tiền gửi, thông tin về tài sản gửi, thông tin về giao dịch, thông tin về tổ chức, cá nhân là bên bảo đảm tại tổ chức tín dụng, chi nhánh ngân hàng, tổ chức cung ứng dịch vụ trung gian thanh toán;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">c) Dữ liệu về vị trí của cá nhân được xác định qua dịch vụ định vị.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2.3. Dữ liệu cá nhân khác không thuộc nhóm Dữ liệu cá nhân cơ bản và Dữ liệu cá nhân nhạy cảm quy định tại khoản 2.1 và khoản 2.2 Điều này.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Dữ liệu cá nhân phái sinh: Điểm tín nhiệm viễn thông được thể hiện dưới dạng điểm số, là thông tin đánh giá xếp hạng từ việc tổng hợp và phân tích dữ liệu sử dụng dịch vụ viễn thông của Khách hàng nhằm đánh giá, phân tích, dự đoán thói quen, sở thích, mức độ tin cậy, tín nhiệm, hành vi, xu hướng … và các thông tin khác của Khách hàng, hỗ trợ cho việc cung cấp các cung cấp sản phẩm, dịch vụ của VNPT/Công ty con của VNPT một cách phù hợp và tốt nhất.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Các Dữ liệu cá nhân khác phát sinh trong quá trình VNPT/Công ty con của VNPT cung cấp sản phẩm, dịch vụ và trong quá trình Khách hàng sử dụng sản phẩm, dịch vụ, tương tác với nhà cung cấp sản phẩm, dịch vụ.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2.4. VNPT/Công ty con của VNPT sẽ thông báo cho Khách hàng các Dữ liệu cá nhân bắt buộc phải cung cấp và/hoặc tùy chọn cung cấp tại thời điểm Khách hàng liên hệ, trao đổi hoặc đăng ký, ký kết hợp đồng với VNPT/Công ty con của VNPT. Nếu các Dữ liệu cá nhân bắt buộc không được cung cấp theo yêu cầu, Khách hàng sẽ không thể sử dụng một số sản phẩm, dịch vụ của VNPT/Công ty con của VNPT. Trong trường hợp này, VNPT/Công ty con của VNPT có thể từ chối cung cấp sản phẩm, dịch vụ cho Khách hàng mà không phải chịu bất kì một khoản bồi thường và/hoặc phạt vi phạm nào.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2.5. Tại từng thời điểm, Khách hàng có thể tự nguyện cung cấp cho VNPT/Công ty con của VNPT các Dữ liệu cá nhân nằm ngoài yêu cầu của VNPT/Công ty con của VNPT. Khi Khách hàng cung cấp Dữ liệu cá nhân nằm ngoài yêu cầu của VNPT/Công ty con của VNPT đồng nghĩa với việc Khách hàng cho phép VNPT/Công ty con của VNPT xử lý Dữ liệu cá nhân của Khách hàng với Mục Đích được nêu trong Chính sách này hoặc với mục đích được nêu tại thời điểm Khách hàng cung cấp những Dữ liệu cá nhân đó. Ngoài ra, khi Khách hàng chủ động cung cấp thông tin nằm ngoài yêu cầu của VNPT/Công ty con của VNPT, Khách hàng vui lòng không cung cấp Dữ liệu cá nhân nhạy cảm theo quy định của pháp luật tại từng thời điểm. VNPT/Công ty con của VNPT sẽ không thực hiện xử lý và không chịu bất kì trách nhiệm pháp lý nào đối với các Dữ liệu cá nhân nhạy cảm do Khách hàng tự nguyện cung cấp nằm ngoài yêu cầu của VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 3.</span></strong><span style=\"font-size:12pt;\">&nbsp;</span><strong><span style=\"font-size:12pt;\">Mục đích xử lý Dữ liệu cá nhân</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">VNPT/Công ty con của VNPT có thể xử lý Dữ liệu cá nhân của Khách hàng cho một hoặc một số mục đích được liệt kê sau đây (“</span><strong><span style=\"font-size:12pt;\">Mục Đích</span></strong><span style=\"font-size:12pt;\">”):</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Xác minh tính chính xác, đầy đủ của các thông tin được Khách hàng cung cấp; xác định hoặc xác thực danh tính của Khách hàng và thực hiện quy trình xác thực Khách hàng; Xử lý việc đăng ký sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Thẩm định hồ sơ và khả năng đủ điều kiện của Khách hàng đối với việc sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT. VNPT/Công ty con của VNPT có thể sử dụng các phương pháp chấm điểm, gán ngưỡng cước nóng, kiểm tra lịch sử Khách hàng sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT để đánh giá và quản trị rủi ro tín dụng, đảm bảo khả năng thanh toán đối với các nghĩa vụ thanh toán và các nghĩa vụ khác có liên quan trong suốt quá trình cung cấp sản phẩm, dịch vụ của VNPT/Công ty con của VNPT cho Khách hàng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Quản lý và đánh giá các hoạt động kinh doanh bao gồm thiết kế, cải tiến và nâng cao chất lượng các sản phẩm, dịch vụ của VNPT/Công ty con của VNPT hoặc thực hiện các hoạt động truyền thông tiếp thị; Thực hiện nghiên cứu thị trường, khảo sát và phân tích dữ liệu liên quan đến sản phẩm, dịch vụ của VNPT/Công ty con của VNPT; nghiên cứu, phát triển các sản phẩm, dịch vụ mới, mô hình cung cấp mới v.v. đáp ứng nhu cầu của Khách hàng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. Cung cấp dịch vụ cho Khách hàng, liên hệ với Khách hàng nhằm tư vấn, trao đổi thông tin, giải quyết yêu cầu, khiếu nại, giao các hóa đơn, các sao kê, các báo cáo hoặc các tài liệu khác liên quan tới sản phẩm, dịch vụ của VNPT/Công ty con của VNPT thông qua các kênh khác nhau (ví dụ: email, chat) và để trả lời yêu cầu của Khách hàng. Liên hệ với Khách hàng (hoặc các bên cần thiết khác) để thông báo cho Khách hàng về thông tin liên quan đến việc sử dụng sản phẩm, dịch vụ của VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">5. Quảng cáo, tiếp thị dựa trên sở thích, thói quen sử dụng dịch vụ của Khách hàng: VNPT/Công ty con của VNPT có thể sử dụng Dữ liệu cá nhân để quảng cáo, tiếp thị với Khách hàng về các sản phẩm, dịch vụ của VNPT/Công ty con của VNPT, chương trình khuyến mại, nghiên cứu, khảo sát, tin tức, thông tin cập nhật, các sự kiện, cuộc thi có thưởng, trao các phần thưởng có liên quan, các quảng cáo và nội dung có liên quan về sản phẩm, dịch vụ của VNPT/Công ty con của VNPT hoặc của các đối tác hợp tác với VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Trường hợp Khách hàng không muốn nhận email, tin nhắn và/hoặc bản tin định kỳ với mục đích quảng cáo, tiếp thị của VNPT/Công ty con của VNPT với tần suất tùy thuộc vào Chính sách của VNPT/Công ty con của VNPT theo từng thời kỳ và phù hợp với quy định pháp luật, Khách hàng có thể từ chối theo cách thức đã được VNPT/Công ty con của VNPT hướng dẫn trên các kênh, phương tiện như tin nhắn SMS, cuộc gọi, dấu tích trên website/wapsite/ứng dụng v.v. hoặc liên hệ với tổng đài chăm sóc Khách hàng của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">6. Lập các báo cáo tài chính, báo cáo hoạt động hoặc các loại báo cáo liên quan khác mà pháp luật quy định;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">7. Tuân thủ các nghĩa vụ pháp lý theo quy định của pháp luật;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">8. Ngăn chặn gian lận hoặc giảm thiểu mối đe doạ đối với tính mạng, sức khỏe của người khác và lợi ích công cộng: VNPT/Công ty con của VNPT có thể sử dụng thông tin cá nhân của Khách hàng để ngăn chặn và phát hiện gian lận, lạm dụng nhằm bảo vệ Khách hàng, VNPT/Công ty con của VNPT và các chủ thể liên quan;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">9. Quản trị nội bộ;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">10. Các mục đích khác có liên quan đến những mục đích được nêu trên.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 4. Cách thức xử lý Dữ liệu cá nhân</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">VNPT/Công ty con của VNPT áp dụng một hoặc nhiều hoạt động tác động tới Dữ liệu cá nhân như: thu thập, ghi, phân tích, xác nhận, lưu trữ, chỉnh sửa, công khai, kết hợp, truy cập, truy xuất, thu hồi, mã hóa, giải mã, sao chép, chia sẻ, truyền đưa, cung cấp, chuyển giao, xóa, hủy Dữ liệu cá nhân hoặc các hành động khác có liên quan.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 5. Thời gian bắt đầu, thời gian kết thúc xử lý dữ liệu</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Thời gian bắt đầu xử lý dữ liệu</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Kể từ thời điểm phát sinh các Mục Đích quy định tại Điều 3 Chính sách này.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Thời gian kết thúc xử lý dữ liệu</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">VNPT/Công ty con của VNPT chấm dứt việc xử lý Dữ liệu cá nhân khi đã hoàn thành Mục Đích quy định tại Chính sách này, trừ trường hợp pháp luật có quy định khác hoặc Khách hàng rút lại sự đồng ý việc xử lý Dữ liệu cá nhân hoặc khi cơ quan nhà nước có thẩm quyền yêu cầu bằng văn bản.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 6. Chia sẻ Dữ liệu cá nhân</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Bằng việc chấp thuận Chính sách này, Khách hàng hiểu và đồng ý rằng VNPT/Công ty con của VNPT có thể chia sẻ Dữ liệu cá nhân của Khách hàng cho các tổ chức, cá nhân dưới đây để thực hiện các</span><strong><span style=\"font-size:12pt;\">&nbsp;</span></strong><span style=\"font-size:12pt;\">Mục Đích quy định tại Chính sách, cụ thể:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. VNPT, Công ty con của VNPT, công ty liên kết của VNPT, công ty liên kết của Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Bên thứ ba cung cấp dịch vụ hoặc các đối tác trong các hợp đồng hợp tác kinh doanh (có phân chia lợi nhuận hoặc không phân chia lợi nhuận): VNPT/Công ty con của VNPT sử dụng và/hoặc hợp tác với các công ty và cá nhân khác để thực hiện một số công việc và chương trình như chương trình quảng cáo, khuyến mại dành cho Khách hàng, nghiên cứu thị trường, phân tích và phát triển sản phẩm, tư vấn chiến lược, cung cấp dịch vụ thu cước v.v. Các Bên thứ ba cung cấp dịch vụ và/hoặc các đối tác này có quyền truy cập, thu thập, sử dụng và xử lý Dữ liệu cá nhân của Khách hàng trong phạm vi VNPT/Công ty con của VNPT cho phép để thực hiện các chức năng của họ và phải tuân thủ quy định của pháp luật về bảo vệ Dữ liệu cá nhân với tư cách là Bên Xử lý Dữ Liệu;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Tái cấu trúc doanh nghiệp: Trong quá trình phát triển kinh doanh, VNPT/Công ty con của VNPT có thể bán hoặc mua các doanh nghiệp hoặc tái cấu trúc doanh nghiệp phù hợp với quy định của pháp luật và nhu cầu sản xuất kinh doanh. Trong các giao dịch như vậy, Dữ liệu cá nhân sẽ được chuyển nhượng và bên nhận chuyển nhượng vẫn phải tuân theo các quy định của Chính sách này;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. VNPT/Công ty con của VNPT được phép tiết lộ Dữ liệu cá nhân theo yêu cầu của pháp luật, yêu cầu của cơ quan quản lý nhà nước có thẩm quyền;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">5. VNPT/Công ty con của VNPT được phép tiết lộ Dữ liệu cá nhân cho các doanh nghiệp viễn thông khác</span><span style=\"font-size:13pt;\">&nbsp;</span><span style=\"font-size:12pt;\">để phục vụ cho việc tính giá cước, lập hoá đơn và ngăn chặn hành vi trốn tránh thực hiện nghĩa vụ theo hợp đồng của Khách hàng.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 7. Quyền của Khách hàng</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Quyền được biết và Quyền đồng ý</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Bằng Chính sách này, VNPT/Công ty con của VNPT thông báo cho Khách hàng được biết về hoạt động xử lý Dữ liệu cá nhân trước khi thực hiện xử lý Dữ liệu cá nhân. Đồng thời, Khách hàng có quyền đồng ý hoặc không đồng ý với các điều khoản và điều kiện của Chính sách này theo cách thức đã được VNPT/Công ty con của VNPT hướng dẫn trên các kênh, phương tiện như tin nhắn SMS, cuộc gọi, dấu tích trên website/wapsite/ứng dụng v.v. hoặc liên hệ với tổng đài chăm sóc khách hàng của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Quyền truy cập và yêu cầu cung cấp Dữ liệu cá nhân</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có quyền truy cập vào các ứng dụng/website/wapsite của VNPT/Công ty con của VNPT và/hoặc liên hệ trực tiếp với VNPT/Công ty con của VNPT để xem, trích xuất các Dữ liệu cá nhân mà Khách hàng đã cung cấp cho VNPT/Công ty con của VNPT phục vụ các Mục Đích quy định tại Chính sách này.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Trường hợp Khách hàng không thể tự truy cập, trích xuất hoặc gặp khó khăn trong việc truy cập, trích xuất các Dữ liệu cá nhân, Khách hàng có thể liên hệ với VNPT/Công ty con của VNPT để được hỗ trợ.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Quyền chỉnh sửa</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có quyền chỉnh sửa các Dữ liệu cá nhân của mình với điều kiện việc chỉnh sửa này không vi phạm các quy định của pháp luật. Trường hợp Khách hàng không thể tự chỉnh sửa hoặc gặp khó khăn trong việc chỉnh sửa các Dữ liệu cá nhân, Khách hàng có thể liên hệ với VNPT/Công ty con của VNPT để được hỗ trợ.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. Quyền phản đối, hạn chế, rút lại sự đồng ý xử lý dữ liệu</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Khách hàng có quyền phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng. Tuy nhiên, việc phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng trong một số trường hợp có thể dẫn tới việc VNPT/Công ty con của VNPT không thể cung cấp Sản phẩm, dịch vụ cho Khách hàng, điều này đồng nghĩa với việc VNPT/Công ty con của VNPT có thể đơn phương chấm dứt hợp đồng mà không cần phải bồi thường cho Khách hàng do các điều kiện để thực hiện hợp đồng đã thay đổi. Do đó, VNPT/Công ty con của VNPT khuyến nghị Khách hàng cân nhắc kĩ lưỡng trước khi phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Trường hợp Khách hàng muốn hạn chế nhận nội dung tiếp thị quảng cáo, khuyến mại từ VNPT/Công ty con của VNPT và muốn rút lại sự chấp thuận trước đó (nếu có) và/hoặc phản đối việc tiếp tục sử dụng thông tin cá nhân của mình cho mục đích quy định tại khoản 5, Điều 3 Chính sách này, Khách hàng vui lòng thực hiện theo hướng dẫn của VNPT/Công ty con của VNPT tại thời điểm VNPT/Công ty con của VNPT thu thập Dữ liệu cá nhân hoặc liên hệ với VNPT/Công ty con của VNPT theo các thông tin được cung cấp tại Chính sách này. Nếu Khách hàng không muốn nhận thông báo từ ứng dụng của VNPT/Công ty con của VNPT, vui lòng điều chỉnh cài đặt thông báo trong ứng dụng hoặc thiết bị của mình.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">5. Quyền xóa Dữ liệu cá nhân</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có quyền yêu cầu VNPT/Công ty con của VNPT thực hiện xóa Dữ liệu cá nhân của Khách hàng với điều kiện là yêu cầu của Khách hàng phải phù hợp với quy định của pháp luật. Tuy nhiên, yêu cầu xóa Dữ liệu cá nhân của Khách hàng trong một số trường hợp có thể dẫn tới việc VNPT/Công ty con của VNPT không thể cung cấp Sản phẩm, dịch vụ cho Khách hàng, điều này đồng nghĩa với việc VNPT/Công ty con của VNPT có thể đơn phương chấm dứt hợp đồng mà không cần phải bồi thường cho Khách hàng do các điều kiện để thực hiện hợp đồng đã thay đổi. Do đó, VNPT/Công ty con của VNPT khuyến nghị Khách hàng cân nhắc kĩ lưỡng trước khi yêu cầu VNPT/Công ty con của VNPT thực hiện xóa Dữ liệu cá nhân.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">6. Quyền khiếu nại, tố cáo, khởi kiện</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có quyền khiếu nại, tố cáo hoặc khởi kiện theo quy định của pháp luật</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">7. Quyền yêu cầu bồi thường thiệt hại</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có quyền yêu cầu VNPT/Công ty con của VNPT bồi thường thiệt hại theo quy định của pháp luật khi xảy ra vi phạm quy định về bảo vệ Dữ liệu cá nhân khi thỏa mãn đồng thời các điều kiện sau:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Có hành vi vi phạm quy định pháp luật về bảo vệ Dữ liệu cá nhân của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Hành vi vi phạm nêu trên dẫn tới thiệt hại thực tế phát sinh cho Khách hàng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">- Khách hàng đã thực hiện đầy đủ các nghĩa vụ về bảo vệ Dữ liệu cá nhân của mình theo quy định của pháp luật, Chính sách này và theo thỏa thuận khác giữa VNPT/Công ty con của VNPT và Khách hàng.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">8. Quyền tự bảo vệ</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có quyền tự bảo vệ theo quy định của Bộ luật Dân sự, luật khác có liên quan và Nghị định 13/2023/NĐ-CP về bảo vệ Dữ liệu cá nhân (và các bản sửa đổi kèm theo), hoặc yêu cầu cơ quan, tổ chức có thẩm quyền thực hiện các phương thức bảo vệ quyền dân sự theo quy định tại Điều 11 Bộ luật Dân sự.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 8. Nghĩa vụ của Khách hàng</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Khách hàng có trách nhiệm bảo vệ Dữ liệu cá nhân của mình như sau:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.</span><span style=\"font-size:13.999999999999998pt;\">&nbsp;</span><span style=\"font-size:12pt;\">Chủ động thực hiện các biện pháp bảo vệ, quản lý và sử dụng an toàn tài khoản, thiết bị di động của mình &nbsp;(bao gồm nhưng không giới hạn bởi điện thoại thông minh, máy tính, máy tính bảng, laptop v.v.) &nbsp;bằng cách đăng xuất tài khoản sau khi sử dụng, đặt một mật khẩu mạnh và khó đoán và giữ bí mật thông tin đăng nhập cũng như mật khẩu của mình. Các biện pháp bảo vệ và quản lý sử dụng an toàn tài khoản, thiết bị di động nói trên giúp ngăn chặn việc truy cập trái phép vào tài khoản của Khách hàng.</span><span style=\"font-size:13.999999999999998pt;\">&nbsp;</span><span style=\"font-size:12pt;\">VNPT được loại trừ trách nhiệm với các thiệt hại của Khách hàng trong trường hợp Khách hàng bị lộ/mất, bị đánh cắp mật khẩu, dẫn tới việc bị truy cập trái phép vào tài khoản, hoặc bất kỳ hoạt động nào trên tài khoản của Khách hàng sử dụng trên thiết bị di động bị mất, thất lạc dẫn đến người không có thẩm quyền tự ý sử dụng dịch vụ, hoặc hệ thống của VNPT bị xâm phạm bất hợp pháp bởi bên thứ ba mặc dù VNPT đã thực hiện đầy đủ các biện pháp để bảo vệ hệ thống; &nbsp; &nbsp; &nbsp;</span><span style=\"font-size:12pt;\">&nbsp; &nbsp;&nbsp;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Khách hàng có trách nhiệm cập nhập các văn bản điều chỉnh Chính sách này (nếu có) trên các kênh thông tin chính thống của VNPT/Công ty con của VNPT;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Khi đã chấp thuận toàn bộ điều khoản và điều kiện của của Chính sách này, Khách hàng có trách nhiệm cung cấp Dữ liệu cá nhân đầy đủ, chính xác theo yêu cầu của VNPT/Công ty con của VNPT và có trách nhiệm thông báo cho VNPT/Công ty con của VNPT ngay khi phát hiện hành vi vi phạm quy định về bảo vệ Dữ liệu cá nhân. Khách hàng có thể chủ động cung cấp Dữ liệu cá nhân nằm ngoài yêu cầu của VNPT/Công ty con của VNPT với điều kiện Khách hàng phải tuân thủ quy định tại khoản 2.5, Điều 2 Chính sách này;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. Khách hàng có trách nhiệm tôn trọng Dữ liệu cá nhân của chủ thể khác và thực hiện quy định của pháp luật về bảo vệ Dữ liệu cá nhân, tham gia phòng, chống các hành vi vi phạm quy định về bảo vệ Dữ liệu cá nhân.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 9. Lưu trữ Dữ liệu cá nhân</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Dữ liệu cá nhân của Khách hàng do VNPT/Công ty con của VNPT lưu trữ sẽ được bảo mật. VNPT/Công ty con của VNPT trong khả năng của mình, sẽ nỗ lực thực hiện các biện pháp hợp lý để bảo vệ Dữ liệu cá nhân của Khách hàng.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Địa điểm lưu trữ Dữ liệu cá nhân</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Trong phạm vi pháp luật cho phép, VNPT/Công ty con của VNPT có thể lưu trữ Dữ liệu cá nhân của Khách hàng tại Việt Nam và ở nước ngoài, kể cả trên giải pháp lưu trữ điện toán đám mây. VNPT/Công ty con của VNPT áp dụng các tiêu chuẩn về bảo mật dữ liệu phù hợp với quy định pháp luật hiện hành.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Thời gian lưu trữ Dữ liệu cá nhân</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">VNPT/Công ty con của VNPT chỉ thực hiện lưu trữ Dữ liệu cá nhân của Khách hàng trong khoảng thời gian cần thiết và hợp lý để hoàn thành các Mục Đích quy định tại Chính sách này. Tuy nhiên, trường hợp pháp luật hiện hành có quy định khác về thời hạn lưu trữ Dữ liệu cá nhân, VNPT/Công ty con của VNPT có nghĩa vụ tuân thủ quy định của pháp luật.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 10. Nghĩa vụ của VNPT/Công ty con của VNPT</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Dữ liệu cá nhân của Khách hàng được cam kết bảo mật theo quy định của pháp luật, Chính sách bảo vệ Dữ liệu cá nhân của VNPT/Công ty con của VNPT.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. VNPT/Công ty con của VNPT nỗ lực đảm bảo Dữ liệu cá nhân của Khách hàng được bảo vệ khỏi các hành vi vi phạm quy định về bảo vệ Dữ liệu cá nhân. VNPT/Công ty con của VNPT duy trì cam kết bảo mật Dữ liệu cá nhân bằng cách áp dụng những biện pháp vật lý, điện tử và quản lý để bảo vệ Dữ liệu cá nhân, bao gồm:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Các máy chủ trang thông tin điện tử chính thức của VNPT và các hệ thống thông tin chứa dữ liệu cá nhân của VNPT đều được bảo vệ bởi các biện pháp, công nghệ bảo mật như tường lửa, mã hóa, chống xâm nhập trái phép v.v.; ban hành các biện pháp kiểm soát về con người, xây dựng quy trình kiểm tra, đánh giá, rà soát để phòng tránh các hành vi vi phạm quy định về bảo vệ Dữ liệu cá nhân.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) VNPT sẽ thực hiện tất cả các biện pháp phù hợp để đảm bảo rằng Dữ liệu cá nhân của Khách hàng được xử lý đúng với Mục Đích đã thông báo. VNPT/Công ty con của VNPT sẽ luôn tuân thủ những yêu cầu của pháp luật liên quan đến việc lưu trữ Dữ liệu cá nhân.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Thực hiện các yêu cầu của Khách hàng liên quan đến dữ liệu cá nhân của Khách hàng với điều kiện các yêu cầu của Khách hàng phải phù hợp với quy định của pháp luật.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. Các nghĩa vụ khác theo quy định của pháp luật và của Chính sách này.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 11. Hậu quả, thiệt hại không mong muốn có khả năng xảy ra</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. VNPT/Công ty con của VNPT sử dụng nhiều biện pháp, công nghệ bảo mật thông tin khác nhau nhằm bảo vệ Dữ liệu cá nhân của Khách hàng không bị sử dụng hoặc chia sẻ ngoài ý muốn. VNPT/Công ty con của VNPT cam kết sẽ bảo mật một cách tối đa Dữ liệu cá nhân của Khách hàng. Một số hậu quả, thiệt hại không mong muốn có thể xảy ra bao gồm nhưng không giới hạn:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Lỗi phần cứng, phần mềm trong quá trình xử lý Dữ liệu cá nhân gây ảnh hưởng không mong muốn (lỗi, hỏng, mất v.v.) Dữ liệu cá nhân của Khách hàng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Lỗ hổng bảo mật nằm ngoài khả năng kiểm soát của VNPT/Công ty con của VNPT, hệ thống bị hacker tấn công gây lộ lọt Dữ liệu cá nhân của Khách hàng;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">c) Khách hàng tự làm lộ lọt Dữ liệu cá nhân của Khách hàng do: bất cẩn hoặc bị lừa đảo; truy cập các website/tải các ứng dụng có chứa phần mềm độc hại; tự ý chia sẻ thông tin với người khác v.v.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. VNPT/Công ty con của VNPT khuyến cáo Khách hàng thực hiện nghiêm ngặt các trách nhiệm bảo vệ Dữ liệu cá nhân theo quy định tại Điều 8 Chính sách này và theo quy định của pháp luật.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Trường hợp máy chủ lưu trữ dữ liệu bị hacker tấn công dẫn đến mất mát Dữ liệu cá nhân của Khách hàng, VNPT/Công ty con của VNPT có trách nhiệm thông báo vụ việc cho cơ quan có thẩm quyền để điều tra xử lý kịp thời và thông báo cho Khách hàng được biết.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 12. Sử dụng cookies</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Cookies có thể được sử dụng trên website của VNPT/Công ty con của VNPT. “Cookies” là các tệp văn bản nhỏ được lưu trên ổ cứng máy tính của Khách hàng nhằm giúp tùy biến trải nghiệm website cho người dùng. VNPT/Công ty con của VNPT sử dụng cookies để việc điều hướng trang web dễ dàng hơn và tạo điều kiện thuận lợi cho quá trình đăng nhập.</span><span style=\"font-size:13.999999999999998pt;\">&nbsp;</span><span style=\"font-size:12pt;\">Dữ liệu thống kê trên trang web của VNPT/Công ty con của VNPT có thể được xử lý bởi các bên thứ ba, và do đó, địa chỉ IP của Khách hàng khi truy cập trang web sẽ được chuyển tới bên thứ ba chỉ với mục đích báo cáo thống kê.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Khi thu thập thông tin qua website của VNPT/Công ty con của VNPT, VNPT/Công ty con của VNPT tự động thu thập những thông tin không mang tính cá nhân, không xác định danh tính nhưng có liên quan tới việc sử dụng trang web bao gồm nhưng không giới hạn như: địa chỉ IP của máy tính cá nhân, địa chỉ IP của nhà cung cấp dịch vụ Internet, ngày giờ truy cập trang web, hệ điều hành, các mục đã truy cập trên trang web, nội dung tải về từ trang web và địa chỉ trang web đã trực tiếp dẫn Khách hàng tới trang web của VNPT/Công ty con của VNPT. VNPT/Công ty con của VNPT có thể dùng những thông tin này để phục vụ cho những báo cáo thống kê về lưu lượng người truy cập trang web và hành vi sử dụng trang web.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Trường hợp Khách hàng không muốn cho phép sử dụng cookies, Khách hàng có thể từ chối bằng cách thay đổi cài đặt riêng tư trên trình duyệt web. Việc từ chối cho phép sử dụng cookies hầu như không gây ảnh hưởng tới việc điều hướng trang web. Tuy nhiên một số chức năng của trang web có thể bị ảnh hưởng. Sau khi hoàn thành việc truy cập trang web, Khách hàng vẫn có thể xóa cookies khỏi hệ thống nếu muốn.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 13. Quảng cáo trên internet và bên thứ ba</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Các website/wapsite/ứng dụng của VNPT/Công ty con của VNPT có thể bao gồm quảng cáo của bên thứ ba và liên kết tới các website/wapsite/ứng dụng khác. Các đối tác quảng cáo bên thứ ba có thể thu thập thông tin về Khách hàng khi Khách hàng tương tác với nội dung, quảng cáo hoặc dịch vụ của họ. Mọi quyền truy cập và sử dụng các liên kết hoặc trang website của bên thứ ba không bị điều chỉnh bởi Chính sách này, mà thay vào đó được điều chỉnh bởi Chính sách quyền riêng tư của các bên thứ ba đó. VNPT/Công ty con của VNPT không chịu trách nhiệm về các quy định về thông tin của các bên thứ ba.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 14. Xử lý Dữ liệu cá nhân không cần sự đồng ý của chủ thể dữ liệu</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">VNPT/Công ty con của VNPT có thể tiến hành xử lý Dữ liệu cá nhân mà không cần sự đồng ý của chủ thể dữ liệu trong các trường hợp sau:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1.</span><strong><span style=\"font-size:12pt;\">&nbsp;</span></strong><span style=\"font-size:12pt;\">Trong trường hợp khẩn cấp, cần xử lý ngay Dữ liệu cá nhân có liên quan để bảo vệ tính mạng, sức khỏe của chủ thể dữ liệu hoặc người khác;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Việc công khai Dữ liệu cá nhân theo quy định của luật;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Việc xử lý dữ liệu của cơ quan nhà nước có thẩm quyền trong trường hợp tình trạng khẩn cấp về quốc phòng, an ninh quốc gia, trật tự an toàn xã hội, thảm họa lớn, dịch bệnh nguy hiểm; khi có nguy cơ đe dọa an ninh, quốc phòng nhưng chưa đến mức ban bố tình trạng khẩn cấp; phòng, chống bạo loạn, khủng bố, phòng, chống tội phạm và vi phạm pháp luật theo quy định của luật;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. Để thực hiện nghĩa vụ theo hợp đồng của chủ thể dữ liệu với cơ quan, tổ chức, cá nhân có liên quan theo quy định của luật;</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">5. Phục vụ hoạt động của cơ quan nhà nước đã được quy định theo luật chuyên ngành.</span></p>\r\n        <p style=\"text-align: justify;\"><strong><span style=\"font-size:12pt;\">Điều 15. Thông tin liên lạc</span></strong></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">Trường hợp Khách hàng có bất kỳ câu hỏi nào về Chính sách này hoặc muốn thực hiện các quyền của Khách hàng liên quan tới Dữ liệu cá nhân, vui lòng liên hệ với VNPT/Công ty con của VNPT theo các phương thức và thông tin dưới đây:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">1. Liên hệ tới tổng đài theo thông tin tại các website/wapsite/ứng dụng chính thức của VNPT/Công ty con của VNPT tại từng thời điểm.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">2. Gửi công văn tới các địa chỉ sau đây:</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">a) Tổng Công ty Dịch vụ Viễn thông (VNPT VinaPhone): tại Toà nhà VinaPhone, đường Xuân Tảo, Phường Xuân La, Quận Tây Hồ, Hà Nội.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">b) Tổng Công ty Truyền thông (VNPT Media): tại Tòa nhà VNPT, 57 Huỳnh Thúc Kháng, Phường Láng Hạ, Quận Đống Đa, Hà Nội.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">c) Công ty Công nghệ thông tin VNPT (VNPT IT): tại Tòa nhà VNPT, 57 Huỳnh Thúc Kháng, Phường Láng Hạ, Quận Đống Đa, Hà Nội.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">3. Liên hệ trực tiếp tại các điểm giao dịch của VNPT/Công ty con của VNPT trên phạm vi toàn quốc.</span></p>\r\n        <p style=\"text-align: justify;\"><span style=\"font-size:12pt;\">4. Các cách thức liên hệ khác như Livechat, liên hệ qua fanpage chính thức của VNPT/Công ty con của VNPT, email chăm sóc Khách hàng được cung cấp cho Khách hàng tại mọi thời điểm</span></p>\r\n        <p><br></p>\r\n        <p><br></p>\r\n        <p><br></p>\r\n        <div id=\"_com_1\" language=\"JavaScript\"><br></div>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,aAAa,QAAQ,wBAAwB;;;AAMtD,OAAM,MAAOC,sCAAuC,SAAQD,aAAa;EACrEE,YACYC,WAAwB,EACxBC,QAAkB;IACtB,KAAK,CAACA,QAAQ,CAAC;IAFX,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;EAEpB;EAEAC,QAAQA,CAAA,GAER;;;uBATSJ,sCAAsC,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,QAAA;IAAA;EAAA;;;YAAtCT,sCAAsC;MAAAU,SAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCCnDb,EAAA,CAAAe,cAAA,UAAK;UAEwFf,EAAA,CAAAgB,MAAA,iLAAqF;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACjLjB,EAAA,CAAAe,cAAA,WAA+B;UAA8Bf,EAAA,CAAAgB,MAAA,qDAA+B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnGjB,EAAA,CAAAe,cAAA,WAAgC;UAAsCf,EAAA,CAAAgB,MAAA,cAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnFjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4XAAiL;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,4BAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,wQAA8H;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,mBAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,2HAA4D;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,yDAA6B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,2oBAA+S;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC7hCjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4uCAAkoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvsBjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,ydAAgP;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrTjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,2DAA0B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,UAAI;UAA8Bf,EAAA,CAAAgB,MAAA,kFAAgC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAKjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,uBAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,UAAI;UAA8Bf,EAAA,CAAAgB,MAAA,4KAAmF;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAKjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,k/BAAif;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACn3BjB,EAAA,CAAAe,cAAA,YAAgC;UAAsCf,EAAA,CAAAgB,MAAA,yBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,cAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,yFAAoC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzNjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4LAAqF;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1JjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,YAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAA6C;UAAAf,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,kBAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,gPAAgH;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACtWjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,YAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAA6C;UAAAf,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,sCAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,8SAAmL;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnbjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,YAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAA6C;UAAAf,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,cAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,kCAAgB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,eAA8B;UAAAf,EAAA,CAAAgB,MAAA,gBAAG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC/PjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mRAAoI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzMjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6VAA2K;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChPjB,EAAA,CAAAe,cAAA,YAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,aAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA6C;UAAAf,EAAA,CAAAgB,MAAA,sBAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,2FAA+C;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,uBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxUjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yNAAsH;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3LjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8LAAgG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrKjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,8EAA6B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,WAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,4PAA2H;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnSjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,geAAsP;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3TjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4MAAoG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzKjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wPAAsI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3MjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6zBAA4c;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACjhBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wRAAuJ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5NjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wcAA6O;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClTjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8HAAyD;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC9HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,qRAAqI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1MjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6NAAsG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,4CAAe;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,yXAAuL;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5cjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,kEAA2B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uHAAuD;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yHAA8D;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnIjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,gCAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClFjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uOAA2G;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChLjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,qCAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClFjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,0DAAwB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC7FjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6XAAiL;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACtPjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,oDAAuB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5FjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wGAAuD;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,2PAAqH;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1LjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,oEAA6B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,gNAAmG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxKjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,41BAAmc;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxgBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,2KAAmE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxIjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,0SAA2I;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChNjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,g7BAAuc;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5gBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mZAAiN;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACtRjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,olCAAojB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACznBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,guDAA04B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC/8BjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,0BAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,oFAA8B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnNjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,+QAA0H;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,gCAAQ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,iBAAG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrSjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yfAAsP;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3TjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8iCAAmhB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxlBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,g2BAAmb;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxfjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,q9BAA2f;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChkBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,y/BAAghB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrlBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8+BAA0gB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC/kBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uOAA2G;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChLjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,kIAA6D;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClIjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,omBAAoT;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzXjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,gDAAmB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxFjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uKAAoE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzIjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,kGAAuC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACpHjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4nBAA4U;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACjZjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,qIAA2D;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxIjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mFAAkC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6KAA0E;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC/IjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,+EAAmC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8lBAA0R;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC/VjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,2EAA+B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,oZAA8L;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,oGAAyC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC7YjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yKAAoG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzKjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,k/CAAivB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACtzBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,+rBAAsX;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3bjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wSAA8I;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnNjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,iMAAmG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,yRAA+H;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvXjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,mEAA4B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6FAAkC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,igCAAqf;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1jBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,gHAAqD;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uiBAAoS;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzWjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uZAAsM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3QjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,0CAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvFjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8oBAAsT;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3XjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,oJAA2D;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChIjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,k5CAA2pB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChuBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4yCAAwqB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC7uBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mEAA4B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACjGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,y3CAAorB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzvBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,iFAAqC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yKAAgF;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrJjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,sFAAqC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,6YAAsM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3QjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8LAAgG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrKjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wJAA8E;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnJjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mZAAkM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvQjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,+CAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvFjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,ioBAA8S;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnXjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,2EAA+B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,iJAAkE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvIjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,WAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8C;UAAAf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,6gCAA4f;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8C;UAAAf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,6gCAAme;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,4BAAmB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC9xCjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uSAAuJ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5NjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,q+BAAuf;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5jBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,ybAAoN;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzRjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,gFAA+B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,kcAA6N;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClSjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8FAAmC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,glBAAqS;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1WjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,gFAAoC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,qrBAAwV;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC7ZjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,2FAA+C;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8SAAoJ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzNjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yoBAAsT;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3XjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,gyBAAkY;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvcjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mjBAA8P;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnUjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wWAAsK;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3OjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,sJAAuE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5IjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,mIAA8D;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3IjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mrBAAqW;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1ajB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,8SAAoJ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzNjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,0SAAqJ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1NjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wZAAkM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvQjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,qWAAuL;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5PjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,shBAA+P;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACpUjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,qDAAwB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,yqBAAsV;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8C;UAAAf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,8cAA+N;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1tBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,kvCAA8rB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnwBjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,ixBAAsZ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3djB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,+FAA8C;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,moCAA+kB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACppBjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,8KAAsE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACnJjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,0RAAqI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1MjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,WAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAe,cAAA,eAAQ;UAA8Bf,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAASjB,EAAA,CAAAe,cAAA,gBAA8B;UAAAf,EAAA,CAAAgB,MAAA,6SAAyI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACjTjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,oHAAyD;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC9HjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wtBAAsX;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC3bjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wQAA6H;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAClMjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,+LAAkF;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvJjB,EAAA,CAAAe,cAAA,aAAgC;UAAsCf,EAAA,CAAAgB,MAAA,6DAA2B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxGjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mdAAoO;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzSjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,sPAAoI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACzMjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,mGAAwC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC7GjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,uPAAgI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACrMjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,oOAAuH;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5LjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,wOAA2H;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAChMjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,0LAAiG;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACtKjB,EAAA,CAAAe,cAAA,aAAgC;UAA8Bf,EAAA,CAAAgB,MAAA,4UAAkL;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvPjB,EAAA,CAAAe,cAAA,UAAG;UAAAf,EAAA,CAAAkB,SAAA,WAAI;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACXjB,EAAA,CAAAe,cAAA,UAAG;UAAAf,EAAA,CAAAkB,SAAA,WAAI;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACXjB,EAAA,CAAAe,cAAA,UAAG;UAAAf,EAAA,CAAAkB,SAAA,WAAI;UAAAlB,EAAA,CAAAiB,YAAA,EAAI;UACXjB,EAAA,CAAAe,cAAA,eAAuC;UAAAf,EAAA,CAAAkB,SAAA,WAAI;UAAAlB,EAAA,CAAAiB,YAAA,EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}