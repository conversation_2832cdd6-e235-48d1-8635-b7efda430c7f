{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/account/AccountService\";\nimport * as i2 from \"../../../service/customer/CustomerService\";\nimport * as i3 from \"../../../service/contract/ContractService\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"../../common-module/table/table.component\";\nimport * as i10 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/inputtextarea\";\nimport * as i14 from \"primeng/multiselect\";\nimport * as i15 from \"primeng/panel\";\nimport * as i16 from \"primeng/radiobutton\";\nimport * as i17 from \"primeng/tabview\";\nimport * as i18 from \"primeng/progressspinner\";\nfunction AppAccountCreateComponent_small_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 50\n  };\n};\nfunction AppAccountCreateComponent_small_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction AppAccountCreateComponent_small_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    type: a0\n  };\n};\nfunction AppAccountCreateComponent_small_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r3.tranService.translate(\"account.label.username\").toLowerCase())));\n  }\n}\nfunction AppAccountCreateComponent_small_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c2 = function () {\n  return {\n    len: 255\n  };\n};\nfunction AppAccountCreateComponent_small_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppAccountCreateComponent_small_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction AppAccountCreateComponent_small_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.invalidPhone\"));\n  }\n}\nfunction AppAccountCreateComponent_small_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r8.tranService.translate(\"account.label.phone\").toLowerCase())));\n  }\n}\nfunction AppAccountCreateComponent_small_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountCreateComponent_small_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppAccountCreateComponent_small_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nfunction AppAccountCreateComponent_small_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r12.tranService.translate(\"account.label.email\").toLowerCase())));\n  }\n}\nfunction AppAccountCreateComponent_small_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppAccountCreateComponent_small_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountCreateComponent_small_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountCreateComponent_div_105_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r23.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountCreateComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"label\", 33);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵtemplate(3, AppAccountCreateComponent_div_105_small_3_Template, 2, 1, \"small\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.controlComboSelectManager.dirty && ctx_r16.controlComboSelectManager.error.required);\n  }\n}\nfunction AppAccountCreateComponent_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"label\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"vnpt-select\", 43);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountCreateComponent_div_106_Template_vnpt_select_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.accountInfo.customerAccounts = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r17.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r17.controlComboSelectCustomerAccount)(\"value\", ctx_r17.accountInfo.customerAccounts)(\"placeholder\", ctx_r17.tranService.translate(\"account.text.selectCustomerAccount\"))(\"paramDefault\", ctx_r17.accountInfo.manager != null ? ctx_r17.paramSearchCustomerAccount : ctx_r17.paramSearchCustomerAccount)(\"loadData\", ctx_r17.loadCustomerAccount.bind(ctx_r17));\n  }\n}\nfunction AppAccountCreateComponent_div_107_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r26.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountCreateComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"label\", 33);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵtemplate(3, AppAccountCreateComponent_div_107_small_3_Template, 2, 1, \"small\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.controlComboSelectCustomerAccount.dirty && ctx_r18.controlComboSelectCustomerAccount.error.required);\n  }\n}\nfunction AppAccountCreateComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"label\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"vnpt-select\", 44);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountCreateComponent_div_108_Template_vnpt_select_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.accountInfo.accountRootId = $event);\n    })(\"onchange\", function AppAccountCreateComponent_div_108_Template_vnpt_select_onchange_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.getListCustomer(false));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r19.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r19.controlComboSelectCustomerAccount)(\"value\", ctx_r19.accountInfo.accountRootId)(\"placeholder\", ctx_r19.tranService.translate(\"account.text.selectCustomerAccount\"))(\"paramDefault\", ctx_r19.paramSearchCustomerAccount)(\"loadData\", ctx_r19.loadCustomerAccount.bind(ctx_r19))(\"isMultiChoice\", false);\n  }\n}\nconst _c3 = function () {\n  return {\n    \"top\": \"50%\",\n    \"left\": \"50%\",\n    \"transform\": \"translate(-50%, -50%)\",\n    \"z-index\": \"10\",\n    \"background\": \"rgba(0, 0, 0, 0.1)\"\n  };\n};\nfunction AppAccountCreateComponent_p_tabPanel_114_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nconst _c4 = function () {\n  return {\n    standalone: true\n  };\n};\nconst _c5 = function () {\n  return [5, 10, 20, 25, 50];\n};\nfunction AppAccountCreateComponent_p_tabPanel_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 11)(1, \"div\", 45)(2, \"input\", 46);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountCreateComponent_p_tabPanel_114_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r31.onSearchCustomer(true));\n    })(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_114_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.paramQuickSearchCustomer.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 47);\n    i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_p_tabPanel_114_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onSearchCustomer(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 48);\n    i0.ɵɵtemplate(5, AppAccountCreateComponent_p_tabPanel_114_div_5_Template, 2, 2, \"div\", 49);\n    i0.ɵɵelementStart(6, \"div\", 50)(7, \"table-vnpt\", 51);\n    i0.ɵɵlistener(\"selectItemsChange\", function AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.selectItemCustomer = $event);\n    })(\"selectItemsChange\", function AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.checkSelectItemChangeCustomer($event));\n    })(\"onChangeCustomSelectAllEmmiter\", function AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onChangeSelectAllItemsCustomer());\n    })(\"customSelectAllChange\", function AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_customSelectAllChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.customSelectAllCustomer = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"header\", \"\", ctx_r20.tranService.translate(\"account.text.addCustomer\"), \"*\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r20.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r20.paramQuickSearchCustomer.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(18, _c4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.loadingCustomer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r20.paginationCustomer.page)(\"pageSize\", ctx_r20.paginationCustomer.size)(\"selectItems\", ctx_r20.selectItemCustomer)(\"columns\", ctx_r20.columnInfoCustomer)(\"dataSet\", ctx_r20.dataSetCustomer)(\"options\", ctx_r20.optionTableCustomer)(\"loadData\", ctx_r20.searchCustomer.bind(ctx_r20))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(19, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r20.paginationCustomer.sortBy)(\"params\", ctx_r20.paramQuickSearchCustomer)(\"customSelectAll\", ctx_r20.customSelectAllCustomer);\n  }\n}\nfunction AppAccountCreateComponent_p_tabPanel_115_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction AppAccountCreateComponent_p_tabPanel_115_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 11)(1, \"div\", 45)(2, \"input\", 46);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountCreateComponent_p_tabPanel_115_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r40.onSearchContract(true));\n    })(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_115_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.paramQuickSearchContract.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 47);\n    i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_p_tabPanel_115_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onSearchContract(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 48);\n    i0.ɵɵtemplate(5, AppAccountCreateComponent_p_tabPanel_115_div_5_Template, 2, 2, \"div\", 49);\n    i0.ɵɵelementStart(6, \"div\", 50)(7, \"table-vnpt\", 51);\n    i0.ɵɵlistener(\"selectItemsChange\", function AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.selectItemContract = $event);\n    })(\"selectItemsChange\", function AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.checkSelectItemChangeContract($event));\n    })(\"onChangeCustomSelectAllEmmiter\", function AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onChangeSelectAllItemsContract());\n    })(\"customSelectAllChange\", function AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_customSelectAllChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.customSelectAllContract = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r21.tranService.translate(\"account.text.addContract\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r21.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r21.paramQuickSearchContract.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(18, _c4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.loadingContract);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r21.paginationContract.page)(\"pageSize\", ctx_r21.paginationContract.size)(\"selectItems\", ctx_r21.selectItemContract)(\"columns\", ctx_r21.columnInfoContract)(\"dataSet\", ctx_r21.dataSetContract)(\"options\", ctx_r21.optionTableContract)(\"loadData\", ctx_r21.searchContract.bind(ctx_r21))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(19, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r21.paginationContract.sortBy)(\"params\", ctx_r21.paramQuickSearchContract)(\"customSelectAll\", ctx_r21.customSelectAllContract);\n  }\n}\nfunction AppAccountCreateComponent_p_tabPanel_116_label_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 71);\n    i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_p_tabPanel_116_label_18_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.isShowSecretKey = true);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountCreateComponent_p_tabPanel_116_label_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 72);\n    i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_p_tabPanel_116_label_19_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r53.isShowSecretKey = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-panel\", 73)(1, \"div\", 59)(2, \"div\", 74)(3, \"p-dropdown\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.paramsSearchGrantApi.module = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 74)(5, \"input\", 76);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.paramsSearchGrantApi.api = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p-button\", 47);\n    i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_p_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.onSearchGrantApi(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"table-vnpt\", 77);\n    i0.ɵɵlistener(\"selectItemsChange\", function AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.selectItemGrantApi = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"showClear\", true)(\"ngModel\", ctx_r50.paramsSearchGrantApi.module)(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c4))(\"options\", ctx_r50.listModule)(\"emptyFilterMessage\", ctx_r50.tranService.translate(\"global.text.nodata\"))(\"placeholder\", ctx_r50.tranService.translate(\"account.text.module\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r50.paramsSearchGrantApi.api)(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c4));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r50.paginationGrantApi.page)(\"pageSize\", ctx_r50.paginationGrantApi.size)(\"selectItems\", ctx_r50.selectItemGrantApi)(\"columns\", ctx_r50.columnInfoGrantApi)(\"dataSet\", ctx_r50.dataSetGrantApi)(\"options\", ctx_r50.optionTableGrantApi)(\"loadData\", ctx_r50.searchGrantApi.bind(ctx_r50))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(23, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r50.paginationGrantApi.sortBy)(\"params\", ctx_r50.paramsSearchGrantApi);\n  }\n}\nfunction AppAccountCreateComponent_p_tabPanel_116_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 53)(1, \"div\", 54)(2, \"p-panel\", 55)(3, \"div\", 56)(4, \"p-radioButton\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_116_Template_p_radioButton_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-radioButton\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_116_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"div\", 60)(8, \"div\", 61)(9, \"label\", 62);\n    i0.ɵɵtext(10, \"Client ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 63);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_116_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.genGrantApi.clientId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 60)(13, \"div\", 61)(14, \"label\", 62);\n    i0.ɵɵtext(15, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 64)(17, \"input\", 65);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_p_tabPanel_116_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.genGrantApi.secretKey = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AppAccountCreateComponent_p_tabPanel_116_label_18_Template, 1, 0, \"label\", 66);\n    i0.ɵɵtemplate(19, AppAccountCreateComponent_p_tabPanel_116_label_19_Template, 1, 0, \"label\", 67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 68)(21, \"p-button\", 69);\n    i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_p_tabPanel_116_Template_p_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.genToken());\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtemplate(23, AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template, 8, 24, \"p-panel\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r22.tranService.translate(\"account.text.grantApi\"));\n    i0.ɵɵproperty(\"pt\", \"ProfileTab\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r22.tranService.translate(\"account.text.working\"))(\"ngModel\", ctx_r22.statusGrantApi)(\"ngModelOptions\", i0.ɵɵpureFunction0(20, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r22.tranService.translate(\"account.text.notWorking\"))(\"ngModel\", ctx_r22.statusGrantApi)(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c4));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r22.genGrantApi.clientId)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c4));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r22.genGrantApi.secretKey)(\"ngModelOptions\", i0.ɵɵpureFunction0(23, _c4))(\"type\", ctx_r22.isShowSecretKey ? \"text\" : \"password\")(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.isShowSecretKey == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.isShowSecretKey == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r22.tranService.translate(\"account.text.gen\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.genGrantApi.secretKey);\n  }\n}\nconst _c6 = function (a0) {\n  return [a0];\n};\nexport class AppAccountCreateComponent extends ComponentBase {\n  constructor(accountService, customerService, contractService, formBuilder, cdr, injector) {\n    super(injector);\n    this.accountService = accountService;\n    this.customerService = customerService;\n    this.contractService = contractService;\n    this.formBuilder = formBuilder;\n    this.cdr = cdr;\n    this.isUsernameExisted = false;\n    this.isEmailExisted = false;\n    this.isPhoneExisted = false;\n    this.oldUserType = null;\n    this.paramSearchCustomerProvince = {\n      provinceCode: \"\"\n    };\n    this.paramSearchManager = {\n      type: 3,\n      provinceCode: \"\"\n    };\n    this.paramSearchCustomerAccount = {\n      provinceCode: \"\",\n      managerId: -1\n    };\n    this.paramSearchRole = {\n      accountCustomerId: -1,\n      type: -1\n    };\n    this.controlComboSelect = new ComboLazyControl();\n    this.controlComboSelectManager = new ComboLazyControl();\n    this.controlComboSelectCustomerAccount = new ComboLazyControl();\n    this.controlComboSelectCustomerRoles = new ComboLazyControl();\n    this.customSelectAllCustomer = false;\n    this.customSelectAllContract = false;\n    this.loadingCustomer = false;\n    this.loadingContract = false;\n    this.isShowSecretKey = true;\n    this.listModule = [];\n    //sẽ lưu lại list api sau khi đã chọn\n    this.selectItemGrantApi = [];\n    this.paramsSearchGrantApi = {\n      api: null,\n      module: null\n    };\n    this.genGrantApi = {\n      clientId: null,\n      secretKey: null\n    };\n    this.statusGrantApi = 1; // 1: Hoạt động default\n    this.userInfo = this.sessionService.userInfo;\n    this.accountCurrentDetail = {};\n    this.CONSTANTS = CONSTANTS;\n    this.onclick = onclick;\n  }\n  ngOnInit() {\n    if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])) {\n      window.location.hash = \"/access\";\n    }\n    this.userType = this.sessionService.userInfo.type;\n    this.accountId = this.sessionService.userInfo.id;\n    this.optionUserType = CONSTANTS.USER_TYPE;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.accountmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listaccount\"),\n      routerLink: \"/accounts\"\n    }, {\n      label: this.tranService.translate(\"global.button.create\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    let fullTypeAccount = [{\n      name: this.tranService.translate(\"account.usertype.admin\"),\n      value: CONSTANTS.USER_TYPE.ADMIN,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN]\n    },\n    // {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},\n    {\n      name: this.tranService.translate(\"account.usertype.customer\"),\n      value: CONSTANTS.USER_TYPE.CUSTOMER,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER]\n    }, {\n      name: this.tranService.translate(\"account.usertype.province\"),\n      value: CONSTANTS.USER_TYPE.PROVINCE,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN]\n    }, {\n      name: this.tranService.translate(\"account.usertype.district\"),\n      value: CONSTANTS.USER_TYPE.DISTRICT,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE]\n    }\n    // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},\n    ];\n\n    this.statusAccounts = fullTypeAccount.filter(el => el.accepts.includes(this.userType));\n    this.accountInfo = {\n      accountName: null,\n      fullName: null,\n      email: null,\n      phone: null,\n      userType: this.statusAccounts[0].value,\n      province: this.sessionService.userInfo.provinceCode,\n      roles: null,\n      description: null,\n      manager: null,\n      customers: null,\n      customerAccounts: null,\n      accountRootId: null\n    };\n    this.paramQuickSearchCustomer = {\n      keyword: null,\n      accountRootId: null,\n      provinceCode: this.accountInfo.province,\n      managerId: -1\n    };\n    if (this.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.paramQuickSearchCustomer.accountRootId = this.accountId;\n    }\n    this.columnInfoCustomer = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"code\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"name\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetCustomer = {\n      content: [],\n      total: 0\n    };\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.paginationGrantApi = {\n      page: 0,\n      size: 10,\n      sortBy: \"id,desc\"\n    };\n    this.optionTableCustomer = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.selectItemCustomer = [];\n    this.paramQuickSearchContract = {\n      keyword: null,\n      customerIds: []\n    };\n    this.columnInfoContract = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"customerCode\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"customerName\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"contract.label.contractCode\"),\n      key: \"contractCode\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.columnInfoGrantApi = [{\n      name: \"API\",\n      key: \"name\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: \"Module\",\n      key: \"module\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.dataSetContract = {\n      content: [],\n      total: 0\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n    this.dataSetGrantApi = {\n      content: [],\n      total: 0\n    };\n    this.optionTableContract = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.optionTableGrantApi = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.selectItemContract = [];\n    this.deselectedContracts = new Set();\n    this.formAccount = this.formBuilder.group(this.accountInfo);\n    this.getListAppIdSelected();\n    this.getListProvince();\n    let me = this;\n  }\n  getListAppIdSelected() {\n    let me = this;\n    this.accountService.viewProfile(response => {\n      me.accountCurrentDetail = response;\n    });\n  }\n  ngAfterContentChecked() {\n    if (this.accountInfo.userType != this.oldUserType) {\n      this.oldUserType = this.accountInfo.userType;\n      // this.formAccount.get(\"province\").reset();\n      this.formAccount.get(\"customers\").reset();\n    }\n  }\n  checkExistAccount(type) {\n    let email = null;\n    let username = null;\n    if (type == \"accountName\") {\n      username = this.accountInfo.accountName;\n    } else if (type == \"email\") {\n      email = this.accountInfo.email;\n    }\n    let me = this;\n    this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username, response => {\n      if (response >= 1) {\n        if (type == \"accountName\") {\n          me.isUsernameExisted = true;\n        } else {\n          me.isEmailExisted = true;\n        }\n      } else {\n        if (type == \"accountName\") {\n          me.isUsernameExisted = false;\n        } else {\n          me.isEmailExisted = false;\n        }\n      }\n    });\n  }\n  onSubmitCreate() {\n    let me = this;\n    if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && me.selectItemCustomer.length == 0) {\n      me.messageCommonService.warning(me.tranService.translate('account.message.customerRequired'));\n      return;\n    }\n    this.messageCommonService.onload();\n    setTimeout(function () {\n      me.handleCreate();\n    });\n  }\n  handleCreate() {\n    if (this.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.statusGrantApi = null;\n      this.selectItemGrantApi = [];\n    }\n    let dataBody = {\n      username: this.accountInfo.accountName,\n      fullName: this.accountInfo.fullName,\n      description: this.accountInfo.description,\n      email: this.accountInfo.email,\n      phone: this.accountInfo.phone,\n      type: this.accountInfo.userType,\n      provinceCode: this.accountInfo.province,\n      roleLst: (this.accountInfo.roles || []).map(el => el.id),\n      customerIdLst: (this.accountInfo.customers || []).map(customer => customer.id),\n      idManager: this.accountInfo.manager || null,\n      idUserManageList: this.accountInfo.customerAccounts || [],\n      contractIdLst: (this.selectItemContract || []).map(contract => contract.id),\n      accountRootId: this.accountInfo.accountRootId,\n      statusApi: this.statusGrantApi,\n      listApiId: (this.selectItemGrantApi || []).map(el => el.id)\n    };\n    if (dataBody.phone != null) {\n      if (dataBody.phone.startsWith('0')) {\n        dataBody.phone = \"84\" + dataBody.phone.substring(1, dataBody.phone.length);\n      } else if (dataBody.phone.length == 9 || dataBody.phone.length == 10) {\n        dataBody.phone = \"84\" + dataBody.phone;\n      }\n    }\n    let me = this;\n    this.accountService.createAccount(dataBody, response => {\n      let body111 = {\n        clientId: response.username,\n        secretId: this.genGrantApi.secretKey,\n        name: response.username,\n        active: this.statusGrantApi,\n        userId: response.id\n      };\n      if (this.statusGrantApi) {\n        this.accountService.createClientAuthen(body111, () => {\n          me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n          me.router.navigate(['/accounts/']);\n        }, null, () => {\n          me.messageCommonService.offload();\n        });\n      } else {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.router.navigate(['/accounts/']);\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  closeForm() {\n    this.router.navigate(['/accounts']);\n  }\n  getListRole(isClear) {\n    if (isClear) {\n      this.accountInfo.roles = null;\n    }\n    let type = -1;\n    if (this.accountInfo.userType != null) {\n      type = this.accountInfo.userType;\n    }\n    this.accountService.getListRole({\n      type: type,\n      accountRootId: this.accountInfo.accountRootId ? this.accountInfo.accountRootId : -1\n    }, response => {\n      this.listRole = response.map(el => {\n        return {\n          id: el.id,\n          name: el.name\n        };\n      });\n    });\n  }\n  getListCustomer(isClear, name = \"\") {\n    let me = this;\n    if (this.accountInfo.userType == this.optionUserType.CUSTOMER) {\n      me.paginationCustomer.page = 0;\n      me.paginationContract.page = 0;\n      if (isClear) {\n        this.accountInfo.customers = [];\n        this.accountInfo.manager = null;\n        this.accountInfo.accountRootId = null;\n      }\n      this.selectItemCustomer = [];\n      this.selectItemContract = [];\n      if (this.accountInfo.province != null) {\n        this.paramSearchCustomerProvince = {\n          provinceCode: this.accountInfo.province\n        };\n        this.paramSearchManager = {\n          type: 3,\n          provinceCode: this.accountInfo.province\n        };\n        me.paramSearchCustomerAccount.provinceCode = me.accountInfo.province;\n      }\n      // nếu tài khoản thực hiện tạo là gdv thì gán gdv\n      if (me.userType == CONSTANTS.USER_TYPE.DISTRICT) {\n        me.accountInfo.manager = me.accountId;\n      }\n      if (me.accountInfo.manager) {\n        me.paramSearchCustomerAccount.managerId = me.accountInfo.manager;\n      } else {\n        me.paramSearchCustomerAccount.managerId = -1;\n      }\n      me.getListRole(true);\n      // reload and clear selected when changer root account\n      if (this.genGrantApi.secretKey != null) {\n        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n        this.selectItemGrantApi = [];\n      }\n    }\n    if (this.accountInfo.userType == this.optionUserType.DISTRICT) {\n      if (isClear) {\n        this.accountInfo.customerAccounts = null;\n      }\n      if (this.accountInfo.province != null) {\n        me.paramSearchCustomerAccount.provinceCode = me.accountInfo.province;\n      }\n      if (me.accountInfo.manager) {\n        me.paramSearchCustomerAccount.managerId = me.accountInfo.manager;\n      } else {\n        me.paramSearchCustomerAccount.managerId = -1;\n      }\n    }\n  }\n  onChangeTeller() {\n    let me = this;\n    this.accountInfo.customers = [];\n    this.selectItemCustomer = [];\n    this.selectItemContract = [];\n    this.accountInfo.accountRootId = null;\n    if (me.accountInfo.manager) {\n      me.paramSearchCustomerAccount.managerId = me.accountInfo.manager;\n      me.paramQuickSearchCustomer.managerId = me.accountInfo.manager;\n    } else {\n      me.paramSearchCustomerAccount.managerId = -1;\n      me.paramQuickSearchCustomer.managerId = me.accountInfo.manager;\n    }\n  }\n  filterCustomerByName(event) {\n    this.debounceService.set(\"filterCustomer\", this.getListCustomer.bind(this), false, event.filter);\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          id: el.code,\n          name: `${el.name} (${el.code})`\n        };\n      });\n    });\n  }\n  loadCustomerAccount(params, callback) {\n    return this.accountService.getCustomerAccount(params, callback);\n  }\n  onSearchCustomer(back) {\n    let me = this;\n    if (back) {\n      me.paginationCustomer.page = 0;\n    }\n    me.paramQuickSearchCustomer.provinceCode = this.accountInfo.province, me.paramQuickSearchCustomer.accountRootId = me.userType == CONSTANTS.USER_TYPE.CUSTOMER ? me.accountId : me.accountInfo.accountRootId;\n    me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\n  }\n  searchCustomer(page, limit, sort, params) {\n    let me = this;\n    this.paginationCustomer.page = page;\n    this.paginationCustomer.size = limit;\n    this.paginationCustomer.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        dataParams[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    me.messageCommonService.onload();\n    this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer, response => {\n      me.dataSetCustomer = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (this.selectItemCustomer.length == response.totalElements && response.totalElements != 0) {\n        this.customSelectAllCustomer = true;\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    // console.log(this.selectItemCustomer)\n  }\n\n  onSearchContract(back) {\n    let me = this;\n    if (back) {\n      me.paginationContract.page = 0;\n    }\n    me.paramQuickSearchContract.customerIds = (me.selectItemCustomer || []).map(customer => customer.id), me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\n  }\n  onSearchGrantApi(back) {\n    let me = this;\n    if (back) {\n      me.paginationGrantApi.page = 0;\n    }\n    // me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),\n    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n  }\n  searchContract(page, limit, sort, params) {\n    let me = this;\n    this.paginationContract.page = page;\n    this.paginationContract.size = limit;\n    this.paginationContract.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    // Object.keys(this.paramQuickSearchContract).forEach(key => {\n    //     if(this.paramQuickSearchContract[key] != null){\n    //         dataParams[key] = this.paramQuickSearchContract[key];\n    //     }\n    // })\n    me.messageCommonService.onload();\n    this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract, response => {\n      me.dataSetContract = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (this.selectItemContract.length == response.totalElements && response.totalElements != 0) {\n        this.customSelectAllContract = true;\n      }\n      // console.log(response)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  searchGrantApi(page, limit, sort, params) {\n    let me = this;\n    this.paginationGrantApi.page = page;\n    this.paginationGrantApi.size = limit;\n    this.paginationGrantApi.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramsSearchGrantApi).forEach(key => {\n      if (this.paramsSearchGrantApi[key] != null) {\n        dataParams[key] = this.paramsSearchGrantApi[key];\n      }\n    });\n    me.messageCommonService.onload();\n    // tài khoản tỉnh , admin, GDV khi tạo KH chưa chọn accountRoot ->lấy full\n    if ((this.userType == CONSTANTS.USER_TYPE.PROVINCE || this.userType == CONSTANTS.USER_TYPE.ADMIN || this.userType == CONSTANTS.USER_TYPE.DISTRICT) && !this.accountInfo.accountRootId) {\n      this.accountService.searchGrantApi(dataParams, response => {\n        me.dataSetGrantApi = {\n          content: response.content,\n          total: response.totalElements\n        };\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      let copyParam = {\n        ...dataParams\n      };\n      copyParam.size = *********;\n      this.accountService.searchGrantApi(copyParam, response => {\n        me.listModule = [...new Set(response.content.map(el => el.module))];\n        me.listModule = me.listModule.map(el => ({\n          name: el,\n          value: el\n        }));\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      // tài khoản tỉnh , admin, GDV khi tạo KH đã chọn accountRoot -> lấy theo accountRoot\n      // tài khoản tạo là KH và là tài khoản con -> lấy theo cha\n    } else if ((this.userType == CONSTANTS.USER_TYPE.PROVINCE || this.userType == CONSTANTS.USER_TYPE.ADMIN || this.userType == CONSTANTS.USER_TYPE.DISTRICT || this.userType == CONSTANTS.USER_TYPE.CUSTOMER) && this.accountInfo.accountRootId) {\n      dataParams['userCustomerParent'] = this.accountInfo.accountRootId;\n      this.accountService.getListAPI2(dataParams, response => {\n        me.dataSetGrantApi = {\n          content: response.content,\n          total: response.totalElements\n        };\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      let copyParam = {\n        ...dataParams\n      };\n      copyParam.size = *********;\n      this.accountService.getListAPI2(copyParam, response => {\n        me.listModule = [...new Set(response.content.map(el => el.module))];\n        me.listModule = me.listModule.map(el => ({\n          name: el,\n          value: el\n        }));\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      // tài khoản tạo là KH và là tài khoản cha -> lấy theo cái nó được tài khoản cấp trên gán\n    } else if (this.userType == CONSTANTS.USER_TYPE.CUSTOMER && !this.accountInfo.accountRootId) {\n      dataParams['selectedApiIds'] = (this.accountCurrentDetail.listApiId || [-99]).join(\",\");\n      this.accountService.searchGrantApi(dataParams, response => {\n        me.dataSetGrantApi = {\n          content: response.content,\n          total: response.totalElements\n        };\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      let copyParam = {\n        ...dataParams\n      };\n      copyParam.size = *********;\n      this.accountService.searchGrantApi(copyParam, response => {\n        me.listModule = [...new Set(response.content.map(el => el.module))];\n        me.listModule = me.listModule.map(el => ({\n          name: el,\n          value: el\n        }));\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  onTabChange(event) {\n    const tabName = event.originalEvent.target.innerText;\n    let me = this;\n    if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\n      this.genGrantApi.clientId = this.accountInfo.accountName;\n    } else if (event && tabName.includes(this.tranService.translate('account.text.addContract'))) {\n      me.onSearchContract();\n    } else if (event && tabName.includes(this.tranService.translate('account.text.addCustomer'))) {\n      me.onSearchCustomer();\n    }\n  }\n  generateToken(n) {\n    var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    var token = '';\n    for (var i = 0; i < n; i++) {\n      token += chars[Math.floor(Math.random() * chars.length)];\n    }\n    return token;\n  }\n  checkSelectItemChangeCustomer(event) {\n    let me = this;\n    if (this.selectItemCustomer.length == this.dataSetCustomer.total) {\n      this.customSelectAllCustomer = true;\n    } else {\n      this.customSelectAllCustomer = false;\n    }\n    const currentCustomerIds = new Set((event || []).map(customer => customer.id));\n    const previousCustomerIds = new Set((this.accountInfo.customers || []).map(customer => customer.id));\n    const addedCustomers = (event || []).filter(customer => !previousCustomerIds.has(customer.id));\n    const removedCustomers = this.accountInfo.customers.filter(customer => !currentCustomerIds.has(customer.id));\n    this.fetchContractsByCustomerId((addedCustomers || []).map(customer => customer.id));\n    removedCustomers.forEach(customer => {\n      this.selectItemContract = this.selectItemContract.filter(contract => contract.customerCode != customer.code);\n    });\n    this.accountInfo.customers = event;\n  }\n  fetchContractsByCustomerId(customerIds) {\n    let me = this;\n    this.messageCommonService.onload();\n    this.paginationContract.page = 0;\n    let dataParams = {\n      page: '0',\n      size: '10000',\n      sort: this.paginationContract.sortBy\n    };\n    this.contractService.quickSearchContract(dataParams, {\n      keyword: null,\n      accountRootId: this.accountInfo.accountRootId,\n      provinceCode: this.accountInfo.province,\n      customerIds: customerIds\n    }, res => {\n      if (res.totalElements > 0) {\n        const newContracts = res.content.filter(contract => !this.deselectedContracts.has(contract.contractCode) && !this.selectItemContract.some(existingContract => existingContract.contractCode === contract.contractCode));\n        this.selectItemContract.push(...newContracts);\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  checkSelectItemChangeContract(event) {\n    if (this.selectItemContract.length == this.dataSetContract.total) {\n      this.customSelectAllContract = true;\n    } else {\n      this.customSelectAllContract = false;\n    }\n  }\n  onChangeSelectAllItemsCustomer() {\n    // console.log(this.selectItemCustomer);\n    let me = this;\n    let params = {\n      page: \"0\",\n      size: \"********\",\n      sort: \"name,asc;id,asc\"\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        params[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    this.loadingCustomer = true;\n    this.customerService.quickSearchCustomer(params, this.paramQuickSearchCustomer, response => {\n      if (this.selectItemCustomer.length == response.totalElements) {\n        this.selectItemCustomer = [];\n        this.customSelectAllCustomer = false;\n        return;\n      }\n      this.selectItemCustomer = response.content;\n      this.customSelectAllCustomer = true;\n    }, null, () => {\n      this.loadingCustomer = false;\n    });\n  }\n  onChangeSelectAllItemsContract() {\n    // console.log(this.selectItemCustomer);\n    let me = this;\n    let params = {\n      page: \"0\",\n      size: \"********\",\n      sort: \"customerName,asc;id,asc\"\n    };\n    this.loadingContract = true;\n    this.contractService.quickSearchContract(params, this.paramQuickSearchContract, response => {\n      if (this.selectItemContract.length == response.totalElements) {\n        this.selectItemContract = [];\n        this.customSelectAllContract = false;\n        return;\n      }\n      this.selectItemContract = response.content;\n      this.customSelectAllContract = true;\n    }, null, () => {\n      this.loadingContract = false;\n    });\n  }\n  checkShowTabAddCustomerAndContract() {\n    let me = this;\n    if (me.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) return false;\n    if (me.formAccount.invalid || me.isEmailExisted || me.isPhoneExisted || me.isUsernameExisted) {\n      // Object.keys(this.formAccount.controls).forEach(key => {\n      //     const control = this.formAccount.get(key);\n      //     if (control.invalid) {\n      //         console.log('Field:', key, 'is invalid. Errors:', control.errors);\n      //     }\n      // });\n      // console.log(me.isEmailExisted)\n      // console.log(me.isPhoneExisted)\n      // console.log(me.isUsernameExisted)\n      return false;\n    }\n    if (me.userType == CONSTANTS.USER_TYPE.ADMIN && this.accountInfo.province == null || (me.userType == CONSTANTS.USER_TYPE.ADMIN || me.userType == CONSTANTS.USER_TYPE.PROVINCE) && me.accountInfo.manager == null) {\n      return false;\n    }\n    return true;\n  }\n  genToken() {\n    let me = this;\n    if (this.genGrantApi.secretKey) {\n      this.genGrantApi.secretKey = this.generateToken(20);\n    } else {\n      this.genGrantApi.secretKey = this.generateToken(20);\n      me.onSearchGrantApi();\n    }\n  }\n  static {\n    this.ɵfac = function AppAccountCreateComponent_Factory(t) {\n      return new (t || AppAccountCreateComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContractService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppAccountCreateComponent,\n      selectors: [[\"app-account-create\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 117,\n      vars: 91,\n      consts: [[2, \"position\", \"relative\", 3, \"formGroup\", \"keydown.enter\", \"ngSubmit\"], [1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [1, \"flex\", \"flex-row\", \"justify-content-right\", \"align-items-center\", \"mr-6\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [\"styleClass\", \"p-button-secondary p-button-outlined ml-3\", 3, \"label\", \"click\"], [\"styleClass\", \"mt-3 responsive-pcard\"], [3, \"onChange\"], [3, \"header\"], [1, \"flex\", \"flex-row\", \"justify-content-between\"], [2, \"width\", \"49%\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"accountName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\"], [\"pInputText\", \"\", \"id\", \"accountName\", \"formControlName\", \"accountName\", \"pattern\", \"^[a-zA-Z0-9\\\\-_]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"phone\", \"formControlName\", \"phone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"for\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"userType\", \"formControlName\", \"userType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"province\", \"formControlName\", \"province\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"optionValue\", \"id\", 3, \"showClear\", \"readonly\", \"disabled\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"filter\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"objectKey\", \"account\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${username}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"isMultiChoice\", \"required\", \"paramDefault\", \"valueChange\", \"onchange\"], [\"class\", \"w-full field grid text-error-field\", 4, \"ngIf\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px)\"], [\"styleClass\", \"w-full\", \"id\", \"roles\", \"optionLabel\", \"name\", \"display\", \"chip\", \"formControlName\", \"roles\", 3, \"ngModel\", \"options\", \"defaultLabel\", \"resetFilterOnHide\", \"ngModelChange\"], [3, \"header\", 4, \"ngIf\"], [3, \"header\", \"pt\", 4, \"ngIf\"], [\"objectKey\", \"account\", \"paramKey\", \"username\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${username}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"paramDefault\", \"loadData\", \"valueChange\"], [\"objectKey\", \"account\", \"paramKey\", \"username\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${username}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"paramDefault\", \"loadData\", \"isMultiChoice\", \"valueChange\", \"onchange\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"mt-4\"], [\"type\", \"text\", \"pInputText\", \"\", 2, \"min-width\", \"35vw\", 3, \"placeholder\", \"ngModel\", \"ngModelOptions\", \"keydown.enter\", \"ngModelChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"button\", 3, \"click\"], [1, \"flex\", \"relative\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"h-full\"], [\"class\", \"absolute flex justify-content-center align-items-center w-full h-full\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\"], [\"isUseCustomSelectAll\", \"true\", 3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"customSelectAll\", \"selectItemsChange\", \"onChangeCustomSelectAllEmmiter\", \"customSelectAllChange\"], [1, \"absolute\", \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"h-full\", 3, \"ngStyle\"], [3, \"header\", \"pt\"], [1, \"mb-3\"], [3, \"showHeader\"], [1, \"flex\", \"gap-2\"], [\"value\", \"1\", \"inputId\", \"1\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"value\", \"0\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"flex\", \"gap-3\", \"align-items-center\"], [1, \"col-5\"], [1, \"flex\", \"align-items-center\"], [1, \"mr-3\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"w-full\", \"flex\", \"align-items-center\"], [\"pInputText\", \"\", 1, \"w-full\", \"mr-2\", 2, \"padding-right\", \"30px\", 3, \"ngModel\", \"ngModelOptions\", \"type\", \"disabled\", \"ngModelChange\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye toggle-password\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye-slash toggle-password\", 3, \"click\", 4, \"ngIf\"], [1, \"col-2\"], [\"styleClass\", \"p-button-primary mr-2\", 3, \"label\", \"click\"], [\"class\", \"  \", 3, \"showHeader\", 4, \"ngIf\"], [1, \"pi\", \"pi-eye\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"pi\", \"pi-eye-slash\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"\", 3, \"showHeader\"], [1, \"col-3\"], [\"optionLabel\", \"name\", \"optionValue\", \"value\", \"filter\", \"true\", 1, \"w-full\", 3, \"showClear\", \"ngModel\", \"ngModelOptions\", \"options\", \"emptyFilterMessage\", \"placeholder\", \"ngModelChange\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"API\", 1, \"w-full\", \"mr-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"selectItemsChange\"]],\n      template: function AppAccountCreateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"keydown.enter\", function AppAccountCreateComponent_Template_form_keydown_enter_0_listener($event) {\n            return $event.preventDefault();\n          })(\"ngSubmit\", function AppAccountCreateComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmitCreate();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵelement(8, \"p-button\", 7);\n          i0.ɵɵelementStart(9, \"p-button\", 8);\n          i0.ɵɵlistener(\"click\", function AppAccountCreateComponent_Template_p_button_click_9_listener() {\n            return ctx.closeForm();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"p-card\", 9)(11, \"p-tabView\", 10);\n          i0.ɵɵlistener(\"onChange\", function AppAccountCreateComponent_Template_p_tabView_onChange_11_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementStart(12, \"p-tabPanel\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"label\", 15);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementStart(18, \"span\", 16);\n          i0.ɵɵtext(19, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 17)(21, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.accountInfo.accountName = $event;\n          })(\"ngModelChange\", function AppAccountCreateComponent_Template_input_ngModelChange_21_listener() {\n            return ctx.checkExistAccount(\"accountName\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 19);\n          i0.ɵɵelement(23, \"label\", 15);\n          i0.ɵɵelementStart(24, \"div\", 17);\n          i0.ɵɵtemplate(25, AppAccountCreateComponent_small_25_Template, 2, 1, \"small\", 20);\n          i0.ɵɵtemplate(26, AppAccountCreateComponent_small_26_Template, 2, 2, \"small\", 20);\n          i0.ɵɵtemplate(27, AppAccountCreateComponent_small_27_Template, 2, 1, \"small\", 20);\n          i0.ɵɵtemplate(28, AppAccountCreateComponent_small_28_Template, 2, 3, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 14)(30, \"label\", 21);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementStart(32, \"span\", 16);\n          i0.ɵɵtext(33, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 17)(35, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.accountInfo.fullName = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 19);\n          i0.ɵɵelement(37, \"label\", 21);\n          i0.ɵɵelementStart(38, \"div\", 17);\n          i0.ɵɵtemplate(39, AppAccountCreateComponent_small_39_Template, 2, 1, \"small\", 20);\n          i0.ɵɵtemplate(40, AppAccountCreateComponent_small_40_Template, 2, 2, \"small\", 20);\n          i0.ɵɵtemplate(41, AppAccountCreateComponent_small_41_Template, 2, 1, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 14)(43, \"label\", 23);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 17)(46, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.accountInfo.phone = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 19);\n          i0.ɵɵelement(48, \"label\", 23);\n          i0.ɵɵelementStart(49, \"div\", 17);\n          i0.ɵɵtemplate(50, AppAccountCreateComponent_small_50_Template, 2, 1, \"small\", 20);\n          i0.ɵɵtemplate(51, AppAccountCreateComponent_small_51_Template, 2, 3, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 14)(53, \"label\", 25);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementStart(55, \"span\", 16);\n          i0.ɵɵtext(56, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 17)(58, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_input_ngModelChange_58_listener($event) {\n            return ctx.accountInfo.email = $event;\n          })(\"ngModelChange\", function AppAccountCreateComponent_Template_input_ngModelChange_58_listener() {\n            return ctx.checkExistAccount(\"email\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 19);\n          i0.ɵɵelement(60, \"label\", 25);\n          i0.ɵɵelementStart(61, \"div\", 17);\n          i0.ɵɵtemplate(62, AppAccountCreateComponent_small_62_Template, 2, 1, \"small\", 20);\n          i0.ɵɵtemplate(63, AppAccountCreateComponent_small_63_Template, 2, 2, \"small\", 20);\n          i0.ɵɵtemplate(64, AppAccountCreateComponent_small_64_Template, 2, 1, \"small\", 20);\n          i0.ɵɵtemplate(65, AppAccountCreateComponent_small_65_Template, 2, 3, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 14)(67, \"label\", 27);\n          i0.ɵɵtext(68);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 17)(70, \"textarea\", 28);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_textarea_ngModelChange_70_listener($event) {\n            return ctx.accountInfo.description = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"div\", 19);\n          i0.ɵɵelement(72, \"label\", 29);\n          i0.ɵɵelementStart(73, \"div\", 17);\n          i0.ɵɵtemplate(74, AppAccountCreateComponent_small_74_Template, 2, 2, \"small\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"div\", 13)(76, \"div\", 14)(77, \"label\", 30);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementStart(79, \"span\", 16);\n          i0.ɵɵtext(80, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 17)(82, \"p-dropdown\", 31);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_p_dropdown_ngModelChange_82_listener($event) {\n            return ctx.accountInfo.userType = $event;\n          })(\"ngModelChange\", function AppAccountCreateComponent_Template_p_dropdown_ngModelChange_82_listener() {\n            return ctx.getListRole(true);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"div\", 19);\n          i0.ɵɵelement(84, \"label\", 32);\n          i0.ɵɵelementStart(85, \"div\", 17);\n          i0.ɵɵtemplate(86, AppAccountCreateComponent_small_86_Template, 2, 1, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 14)(88, \"label\", 33);\n          i0.ɵɵtext(89);\n          i0.ɵɵelementStart(90, \"span\", 16);\n          i0.ɵɵtext(91, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"div\", 17)(93, \"p-dropdown\", 34);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_p_dropdown_ngModelChange_93_listener($event) {\n            return ctx.accountInfo.province = $event;\n          })(\"ngModelChange\", function AppAccountCreateComponent_Template_p_dropdown_ngModelChange_93_listener() {\n            return ctx.getListCustomer(true);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 19);\n          i0.ɵɵelement(95, \"label\", 33);\n          i0.ɵɵelementStart(96, \"div\", 17);\n          i0.ɵɵtemplate(97, AppAccountCreateComponent_small_97_Template, 2, 1, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 14)(99, \"label\", 35);\n          i0.ɵɵtext(100);\n          i0.ɵɵelementStart(101, \"span\", 16);\n          i0.ɵɵtext(102, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"div\", 17)(104, \"vnpt-select\", 36);\n          i0.ɵɵlistener(\"valueChange\", function AppAccountCreateComponent_Template_vnpt_select_valueChange_104_listener($event) {\n            return ctx.accountInfo.manager = $event;\n          })(\"onchange\", function AppAccountCreateComponent_Template_vnpt_select_onchange_104_listener() {\n            return ctx.onChangeTeller();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(105, AppAccountCreateComponent_div_105_Template, 4, 1, \"div\", 37);\n          i0.ɵɵtemplate(106, AppAccountCreateComponent_div_106_Template, 5, 6, \"div\", 38);\n          i0.ɵɵtemplate(107, AppAccountCreateComponent_div_107_Template, 4, 1, \"div\", 37);\n          i0.ɵɵtemplate(108, AppAccountCreateComponent_div_108_Template, 5, 7, \"div\", 38);\n          i0.ɵɵelementStart(109, \"div\", 14)(110, \"label\", 35);\n          i0.ɵɵtext(111);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"div\", 39)(113, \"p-multiSelect\", 40);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountCreateComponent_Template_p_multiSelect_ngModelChange_113_listener($event) {\n            return ctx.accountInfo.roles = $event;\n          });\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(114, AppAccountCreateComponent_p_tabPanel_114_Template, 8, 20, \"p-tabPanel\", 41);\n          i0.ɵɵtemplate(115, AppAccountCreateComponent_p_tabPanel_115_Template, 8, 20, \"p-tabPanel\", 41);\n          i0.ɵɵtemplate(116, AppAccountCreateComponent_p_tabPanel_116_Template, 24, 24, \"p-tabPanel\", 42);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.formAccount);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listaccount\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.formAccount.invalid || ctx.isEmailExisted || ctx.isPhoneExisted || ctx.isUsernameExisted || (ctx.userType == ctx.CONSTANTS.USER_TYPE.PROVINCE || ctx.userType == ctx.CONSTANTS.USER_TYPE.ADMIN) && ctx.accountInfo.userType === ctx.CONSTANTS.USER_TYPE.CUSTOMER && !ctx.accountInfo.manager);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"header\", ctx.tranService.translate(\"account.label.generalInfo\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.username\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.accountInfo.accountName)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputUsername\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.accountName.dirty && (ctx.formAccount.controls.accountName.errors == null ? null : ctx.formAccount.controls.accountName.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.accountName.errors == null ? null : ctx.formAccount.controls.accountName.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.accountName.errors == null ? null : ctx.formAccount.controls.accountName.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUsernameExisted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.fullname\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.accountInfo.fullName)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx.tranService.translate(\"account.text.inputFullname\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.fullName.dirty && (ctx.formAccount.controls.fullName.errors == null ? null : ctx.formAccount.controls.fullName.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.fullName.errors == null ? null : ctx.formAccount.controls.fullName.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.fullName.errors == null ? null : ctx.formAccount.controls.fullName.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.phone\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.accountInfo.phone)(\"placeholder\", ctx.tranService.translate(\"account.text.inputPhone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.phone.errors == null ? null : ctx.formAccount.controls.phone.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPhoneExisted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.accountInfo.email)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx.tranService.translate(\"account.text.inputEmail\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.email.dirty && (ctx.formAccount.controls.email.errors == null ? null : ctx.formAccount.controls.email.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.email.errors == null ? null : ctx.formAccount.controls.email.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.email.errors == null ? null : ctx.formAccount.controls.email.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEmailExisted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.description\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx.accountInfo.description)(\"maxlength\", 255)(\"placeholder\", ctx.tranService.translate(\"sim.text.inputDescription\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.description.errors == null ? null : ctx.formAccount.controls.description.errors.maxLength);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.userType\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.accountInfo.userType)(\"required\", true)(\"options\", ctx.statusAccounts)(\"placeholder\", ctx.tranService.translate(\"account.text.selectUserType\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.userType.dirty && (ctx.formAccount.controls.userType.errors == null ? null : ctx.formAccount.controls.userType.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.accountInfo.userType == ctx.optionUserType.ADMIN ? \"hidden\" : \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.province\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"showClear\", ctx.userType == ctx.optionUserType.ADMIN && ctx.accountInfo.userType != ctx.optionUserType.ADMIN && ctx.accountInfo.userType != ctx.optionUserType.AGENCY)(\"readonly\", ctx.userType != ctx.optionUserType.ADMIN)(\"disabled\", ctx.userType != ctx.optionUserType.ADMIN)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.accountInfo.province)(\"required\", ctx.userType == ctx.optionUserType.ADMIN && ctx.accountInfo.userType != ctx.optionUserType.ADMIN && ctx.accountInfo.userType != ctx.optionUserType.AGENCY)(\"options\", ctx.listProvince)(\"filter\", true)(\"placeholder\", ctx.tranService.translate(\"account.text.selectProvince\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.userType == ctx.optionUserType.ADMIN && ctx.accountInfo.userType != ctx.optionUserType.ADMIN && ctx.accountInfo.userType != ctx.optionUserType.AGENCY ? \"\" : \"hidden\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount.controls.province.dirty && (ctx.formAccount.controls.province.errors == null ? null : ctx.formAccount.controls.province.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.accountInfo.userType == ctx.optionUserType.CUSTOMER && (ctx.userType == ctx.optionUserType.ADMIN || ctx.userType == ctx.optionUserType.PROVINCE) ? \"\" : \"hidden\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.managerName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"control\", ctx.controlComboSelectManager)(\"value\", ctx.accountInfo.manager)(\"placeholder\", ctx.tranService.translate(\"account.text.selectGDV\"))(\"isMultiChoice\", false)(\"required\", ctx.accountInfo.userType == ctx.optionUserType.CUSTOMER ? true : false)(\"paramDefault\", ctx.paramSearchManager);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountInfo.userType == ctx.optionUserType.CUSTOMER);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountInfo.userType == ctx.optionUserType.DISTRICT);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountInfo.userType == ctx.optionUserType.DISTRICT);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountInfo.userType == ctx.optionUserType.CUSTOMER && (ctx.userType == ctx.CONSTANTS.USER_TYPE.ADMIN || ctx.userType == ctx.CONSTANTS.USER_TYPE.PROVINCE || ctx.userType == ctx.CONSTANTS.USER_TYPE.DISTRICT));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.role\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.accountInfo.roles)(\"options\", ctx.listRole)(\"defaultLabel\", ctx.tranService.translate(\"account.text.selectRoles\"))(\"resetFilterOnHide\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkShowTabAddCustomerAndContract());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkShowTabAddCustomerAndContract() && ctx.selectItemCustomer && ctx.selectItemCustomer.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(89, _c6, ctx.CONSTANTS.PERMISSIONS.THIRD_PARTY_API.GRANT_PERMISSION_3RD_API)) && ctx.accountInfo.userType == ctx.optionUserType.CUSTOMER);\n        }\n      },\n      dependencies: [i5.NgIf, i5.NgStyle, i6.Breadcrumb, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MaxLengthValidator, i4.PatternValidator, i4.NgModel, i4.FormGroupDirective, i4.FormControlName, i7.InputText, i8.Button, i9.TableVnptComponent, i10.VnptCombobox, i11.Dropdown, i12.Card, i13.InputTextarea, i14.MultiSelect, i15.Panel, i16.RadioButton, i17.TabView, i17.TabPanel, i18.ProgressSpinner],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "tranService", "translate", "ctx_r1", "ɵɵpureFunction0", "_c0", "ctx_r2", "ctx_r3", "ɵɵpureFunction1", "_c1", "toLowerCase", "ctx_r4", "ctx_r5", "_c2", "ctx_r6", "ctx_r7", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ctx_r15", "ctx_r23", "ɵɵelement", "ɵɵtemplate", "AppAccountCreateComponent_div_105_small_3_Template", "ɵɵproperty", "ctx_r16", "controlComboSelectManager", "dirty", "error", "required", "ɵɵlistener", "AppAccountCreateComponent_div_106_Template_vnpt_select_valueChange_4_listener", "$event", "ɵɵrestoreView", "_r25", "ctx_r24", "ɵɵnextContext", "ɵɵresetView", "accountInfo", "customerAccounts", "ctx_r17", "controlComboSelectCustomerAccount", "manager", "paramSearchCustomerAccount", "loadCustomerAccount", "bind", "ctx_r26", "AppAccountCreateComponent_div_107_small_3_Template", "ctx_r18", "AppAccountCreateComponent_div_108_Template_vnpt_select_valueChange_4_listener", "_r28", "ctx_r27", "accountRootId", "AppAccountCreateComponent_div_108_Template_vnpt_select_onchange_4_listener", "ctx_r29", "getListCustomer", "ctx_r19", "_c3", "AppAccountCreateComponent_p_tabPanel_114_Template_input_keydown_enter_2_listener", "_r32", "ctx_r31", "preventDefault", "onSearchCustomer", "AppAccountCreateComponent_p_tabPanel_114_Template_input_ngModelChange_2_listener", "ctx_r33", "paramQuickSearchCustomer", "keyword", "AppAccountCreateComponent_p_tabPanel_114_Template_p_button_click_3_listener", "ctx_r34", "AppAccountCreateComponent_p_tabPanel_114_div_5_Template", "AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_selectItemsChange_7_listener", "ctx_r35", "selectItemCustomer", "ctx_r36", "checkSelectItemChangeCustomer", "AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener", "ctx_r37", "onChangeSelectAllItemsCustomer", "AppAccountCreateComponent_p_tabPanel_114_Template_table_vnpt_customSelectAllChange_7_listener", "ctx_r38", "customSelectAllCustomer", "ɵɵpropertyInterpolate1", "ctx_r20", "_c4", "loadingCustomer", "paginationCustomer", "page", "size", "columnInfoCustomer", "dataSetCustomer", "optionTableCustomer", "searchCustomer", "_c5", "sortBy", "AppAccountCreateComponent_p_tabPanel_115_Template_input_keydown_enter_2_listener", "_r41", "ctx_r40", "onSearchContract", "AppAccountCreateComponent_p_tabPanel_115_Template_input_ngModelChange_2_listener", "ctx_r42", "paramQuickSearchContract", "AppAccountCreateComponent_p_tabPanel_115_Template_p_button_click_3_listener", "ctx_r43", "AppAccountCreateComponent_p_tabPanel_115_div_5_Template", "AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_selectItemsChange_7_listener", "ctx_r44", "selectItemContract", "ctx_r45", "checkSelectItemChangeContract", "AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener", "ctx_r46", "onChangeSelectAllItemsContract", "AppAccountCreateComponent_p_tabPanel_115_Template_table_vnpt_customSelectAllChange_7_listener", "ctx_r47", "customSelectAllContract", "ɵɵpropertyInterpolate", "ctx_r21", "loadingContract", "paginationContract", "columnInfoContract", "dataSetContract", "optionTableContract", "searchContract", "AppAccountCreateComponent_p_tabPanel_116_label_18_Template_label_click_0_listener", "_r52", "ctx_r51", "isShowSecretKey", "AppAccountCreateComponent_p_tabPanel_116_label_19_Template_label_click_0_listener", "_r54", "ctx_r53", "AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_p_dropdown_ngModelChange_3_listener", "_r56", "ctx_r55", "paramsSearchGrantApi", "module", "AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_input_ngModelChange_5_listener", "ctx_r57", "api", "AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_p_button_click_6_listener", "ctx_r58", "onSearchGrantApi", "AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template_table_vnpt_selectItemsChange_7_listener", "ctx_r59", "selectItemGrantApi", "ctx_r50", "listModule", "paginationGrantApi", "columnInfoGrantApi", "dataSetGrantApi", "optionTableGrantApi", "searchGrantApi", "AppAccountCreateComponent_p_tabPanel_116_Template_p_radioButton_ngModelChange_4_listener", "_r61", "ctx_r60", "statusGrantApi", "AppAccountCreateComponent_p_tabPanel_116_Template_p_radioButton_ngModelChange_5_listener", "ctx_r62", "AppAccountCreateComponent_p_tabPanel_116_Template_input_ngModelChange_11_listener", "ctx_r63", "genGrant<PERSON>pi", "clientId", "AppAccountCreateComponent_p_tabPanel_116_Template_input_ngModelChange_17_listener", "ctx_r64", "secret<PERSON>ey", "AppAccountCreateComponent_p_tabPanel_116_label_18_Template", "AppAccountCreateComponent_p_tabPanel_116_label_19_Template", "AppAccountCreateComponent_p_tabPanel_116_Template_p_button_click_21_listener", "ctx_r65", "genToken", "AppAccountCreateComponent_p_tabPanel_116_p_panel_23_Template", "ctx_r22", "AppAccountCreateComponent", "constructor", "accountService", "customerService", "contractService", "formBuilder", "cdr", "injector", "isUsernameExisted", "isEmailExisted", "isPhoneExisted", "oldUserType", "paramSearchCustomerProvince", "provinceCode", "paramSearchManager", "type", "managerId", "paramSearchRole", "accountCustomerId", "controlComboSelect", "controlComboSelectCustomerRoles", "userInfo", "sessionService", "accountCurrentDetail", "onclick", "ngOnInit", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "ACCOUNT", "CREATE", "window", "location", "hash", "userType", "accountId", "id", "optionUserType", "USER_TYPE", "items", "label", "routerLink", "home", "icon", "fullTypeAccount", "name", "value", "ADMIN", "accepts", "CUSTOMER", "PROVINCE", "DISTRICT", "statusAccounts", "filter", "el", "includes", "accountName", "fullName", "email", "phone", "province", "roles", "description", "customers", "key", "align", "isShow", "isSort", "content", "total", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "customerIds", "deselectedContracts", "Set", "formAccount", "group", "getListAppIdSelected", "getListProvince", "me", "viewProfile", "response", "ngAfterContentChecked", "get", "reset", "checkExistAccount", "username", "debounceService", "set", "checkAccount", "onSubmitCreate", "length", "messageCommonService", "warning", "onload", "setTimeout", "handleCreate", "dataBody", "roleLst", "map", "customerIdLst", "customer", "idManager", "idUserManageList", "contractIdLst", "contract", "statusApi", "listApiId", "startsWith", "substring", "createAccount", "body111", "secretId", "active", "userId", "createClientAuthen", "success", "router", "navigate", "offload", "closeForm", "getListRole", "isClear", "listRole", "onChangeTeller", "filterCustomerByName", "event", "listProvince", "code", "params", "callback", "getCustomerAccount", "back", "limit", "sort", "dataParams", "Object", "keys", "for<PERSON>ach", "quickSearchCustomer", "totalElements", "quickSearchContract", "copyParam", "getListAPI2", "join", "onTabChange", "tabName", "originalEvent", "target", "innerText", "generateToken", "n", "chars", "token", "i", "Math", "floor", "random", "currentCustomerIds", "previousCustomerIds", "addedCustomers", "has", "removedCustomers", "fetchContractsByCustomerId", "customerCode", "res", "newContracts", "contractCode", "some", "existingContract", "push", "checkShowTabAddCustomerAndContract", "invalid", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "CustomerService", "i3", "ContractService", "i4", "FormBuilder", "ChangeDetectorRef", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppAccountCreateComponent_Template", "rf", "ctx", "AppAccountCreateComponent_Template_form_keydown_enter_0_listener", "AppAccountCreateComponent_Template_form_ngSubmit_0_listener", "AppAccountCreateComponent_Template_p_button_click_9_listener", "AppAccountCreateComponent_Template_p_tabView_onChange_11_listener", "AppAccountCreateComponent_Template_input_ngModelChange_21_listener", "AppAccountCreateComponent_small_25_Template", "AppAccountCreateComponent_small_26_Template", "AppAccountCreateComponent_small_27_Template", "AppAccountCreateComponent_small_28_Template", "AppAccountCreateComponent_Template_input_ngModelChange_35_listener", "AppAccountCreateComponent_small_39_Template", "AppAccountCreateComponent_small_40_Template", "AppAccountCreateComponent_small_41_Template", "AppAccountCreateComponent_Template_input_ngModelChange_46_listener", "AppAccountCreateComponent_small_50_Template", "AppAccountCreateComponent_small_51_Template", "AppAccountCreateComponent_Template_input_ngModelChange_58_listener", "AppAccountCreateComponent_small_62_Template", "AppAccountCreateComponent_small_63_Template", "AppAccountCreateComponent_small_64_Template", "AppAccountCreateComponent_small_65_Template", "AppAccountCreateComponent_Template_textarea_ngModelChange_70_listener", "AppAccountCreateComponent_small_74_Template", "AppAccountCreateComponent_Template_p_dropdown_ngModelChange_82_listener", "AppAccountCreateComponent_small_86_Template", "AppAccountCreateComponent_Template_p_dropdown_ngModelChange_93_listener", "AppAccountCreateComponent_small_97_Template", "AppAccountCreateComponent_Template_vnpt_select_valueChange_104_listener", "AppAccountCreateComponent_Template_vnpt_select_onchange_104_listener", "AppAccountCreateComponent_div_105_Template", "AppAccountCreateComponent_div_106_Template", "AppAccountCreateComponent_div_107_Template", "AppAccountCreateComponent_div_108_Template", "AppAccountCreateComponent_Template_p_multiSelect_ngModelChange_113_listener", "AppAccountCreateComponent_p_tabPanel_114_Template", "AppAccountCreateComponent_p_tabPanel_115_Template", "AppAccountCreateComponent_p_tabPanel_116_Template", "controls", "errors", "max<PERSON><PERSON><PERSON>", "pattern", "ɵɵclassMap", "AGENCY", "_c6", "THIRD_PARTY_API", "GRANT_PERMISSION_3RD_API"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\create\\app.account.create.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\create\\app.account.create.component.html"], "sourcesContent": ["import {AfterContentChecked, ChangeDetector<PERSON>ef, Component, Injector, OnInit} from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport {MenuItem, SelectItem} from \"primeng/api\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { AccountService } from \"src/app/service/account/AccountService\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {CustomerService} from \"../../../service/customer/CustomerService\";\r\nimport {ContractService} from \"../../../service/contract/ContractService\";\r\nimport {de} from \"@fullcalendar/core/internal-common\";\r\n\r\n@Component({\r\n    selector: \"app-account-create\",\r\n    templateUrl: './app.account.create.component.html'\r\n})\r\nexport class AppAccountCreateComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n    constructor(public accountService: AccountService,\r\n                public customerService: CustomerService,\r\n                public contractService: ContractService,\r\n                private formBuilder: FormBuilder,\r\n                private cdr: ChangeDetectorRef,\r\n                injector: Injector) {\r\n        super(injector);\r\n    }\r\n    items: Array<MenuItem>;\r\n    home: MenuItem;\r\n    accountInfo: {\r\n        accountName: string| null,\r\n        fullName: string|null,\r\n        email: string|null,\r\n        phone: string|null,\r\n        userType: number| null,\r\n        province: any,\r\n        roles: Array<any>,\r\n        description: string|null,\r\n        manager: any,\r\n        //Lưu lại list customer cuối cùng để gửi đi\r\n        customers: Array<any>\r\n        customerAccounts : null\r\n        accountRootId: null\r\n    };\r\n    formAccount: any;\r\n    statusAccounts: Array<any>;\r\n    listRole: Array<any>;\r\n    listProvince: Array<any>;\r\n    listCustomer: Array<any>;\r\n    userType: number;\r\n    optionUserType: any;\r\n    isUsernameExisted: boolean = false;\r\n    isEmailExisted: boolean = false;\r\n    isPhoneExisted: boolean = false;\r\n    oldUserType: number | null = null;\r\n    paramSearchCustomerProvince: {provinceCode: string} = {provinceCode: \"\"};\r\n    paramSearchManager :{type: number, provinceCode: string} = {type: 3, provinceCode: \"\"};\r\n    paramSearchCustomerAccount : {provinceCode: string, managerId: number} = {provinceCode: \"\", managerId: -1};\r\n    paramSearchRole : {accountCustomerId: number, type: number} = {accountCustomerId: -1, type: -1};\r\n    controlComboSelect: ComboLazyControl = new ComboLazyControl();\r\n    controlComboSelectManager : ComboLazyControl = new ComboLazyControl();\r\n    controlComboSelectCustomerAccount : ComboLazyControl = new ComboLazyControl();\r\n    controlComboSelectCustomerRoles : ComboLazyControl = new ComboLazyControl();\r\n    paramQuickSearchCustomer: {\r\n        keyword: string|null,\r\n        provinceCode: string|null,\r\n        accountRootId: number| null,\r\n        managerId: number | null\r\n    }\r\n\r\n    customSelectAllCustomer = false;\r\n    customSelectAllContract = false;\r\n    loadingCustomer: boolean = false;\r\n    loadingContract: boolean = false;\r\n\r\n    columnInfoCustomer: Array<ColumnInfo>;\r\n    optionTableCustomer: OptionTable;\r\n    //Lưu lại list customer đã chọn\r\n    selectItemCustomer: Array<any>\r\n    //sẽ lưu lại list contract sau khi đã chọn\r\n    selectItemContract: Array<any>\r\n    //lưu lại contract bỏ chọn\r\n    deselectedContracts: Set<string>\r\n    dataSetCustomer: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationCustomer: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n\r\n    paramQuickSearchContract: {\r\n        keyword: string|null,\r\n        customerIds: Array<{ id: number }>|null,\r\n    }\r\n    columnInfoContract: Array<ColumnInfo>;\r\n    optionTableContract: OptionTable;\r\n    dataSetContract: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationContract: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    accountId: number | null\r\n    isShowSecretKey = true\r\n    listModule = []\r\n    //sẽ lưu lại list api sau khi đã chọn\r\n    selectItemGrantApi: Array<any> = []\r\n    paginationGrantApi: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    columnInfoGrantApi: Array<ColumnInfo>;\r\n\r\n    dataSetGrantApi: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    optionTableGrantApi: OptionTable;\r\n\r\n    paramsSearchGrantApi = {api : null, module : null}\r\n\r\n    genGrantApi = {clientId: null, secretKey: null}\r\n\r\n    statusGrantApi : any = 1; // 1: Hoạt động default\r\n\r\n    userInfo = this.sessionService.userInfo;\r\n\r\n    accountCurrentDetail : any = {}\r\n\r\n    ngOnInit(): void {\r\n        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])) {window.location.hash = \"/access\";}\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.accountId = this.sessionService.userInfo.id\r\n        this.optionUserType = CONSTANTS.USER_TYPE;\r\n        this.items = [\r\n            { label: this.tranService.translate(\"global.menu.accountmgmt\") },\r\n            { label: this.tranService.translate(\"global.menu.listaccount\"), routerLink:\"/accounts\" },\r\n            { label: this.tranService.translate(\"global.button.create\") }\r\n        ];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        let fullTypeAccount = [\r\n            {name: this.tranService.translate(\"account.usertype.admin\"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},\r\n            // {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},\r\n            {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER]},\r\n            {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE,accepts:[CONSTANTS.USER_TYPE.ADMIN]},\r\n            {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE]},\r\n            // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},\r\n        ]\r\n        this.statusAccounts = fullTypeAccount.filter(el => el.accepts.includes(this.userType));\r\n        this.accountInfo = {\r\n            accountName: null,\r\n            fullName: null,\r\n            email: null,\r\n            phone: null,\r\n            userType: this.statusAccounts[0].value,\r\n            province: this.sessionService.userInfo.provinceCode,\r\n            roles: null,\r\n            description: null,\r\n            manager: null,\r\n            customers: null,\r\n            customerAccounts : null,\r\n            accountRootId: null\r\n        }\r\n        this.paramQuickSearchCustomer = {\r\n            keyword: null,\r\n            accountRootId: null,\r\n            provinceCode: this.accountInfo.province,\r\n            managerId: -1,\r\n        }\r\n        if (this.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n            this.paramQuickSearchCustomer.accountRootId = this.accountId;\r\n        }\r\n        this.columnInfoCustomer = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"code\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"name\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetCustomer = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.paginationGrantApi = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"id,desc\",\r\n        }\r\n        this.optionTableCustomer = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.selectItemCustomer = []\r\n\r\n        this.paramQuickSearchContract = {\r\n            keyword: null,\r\n            customerIds: [],\r\n        }\r\n        this.columnInfoContract = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"customerCode\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"customerName\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"contract.label.contractCode\"),\r\n                key: \"contractCode\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n\r\n        this.columnInfoGrantApi = [\r\n            {\r\n                name: \"API\",\r\n                key: \"name\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: \"Module\",\r\n                key: \"module\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            }\r\n        ]\r\n        this.dataSetContract = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n        this.dataSetGrantApi = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.optionTableContract = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.optionTableGrantApi = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.selectItemContract = []\r\n        this.deselectedContracts = new Set<string>();\r\n        this.formAccount = this.formBuilder.group(this.accountInfo);\r\n        this.getListAppIdSelected()\r\n        this.getListProvince();\r\n        let me = this;\r\n    }\r\n\r\n    getListAppIdSelected(){\r\n        let me = this;\r\n        this.accountService.viewProfile( (response)=>{\r\n            me.accountCurrentDetail = response;\r\n        })\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        if(this.accountInfo.userType != this.oldUserType){\r\n            this.oldUserType = this.accountInfo.userType;\r\n            // this.formAccount.get(\"province\").reset();\r\n            this.formAccount.get(\"customers\").reset();\r\n        }\r\n    }\r\n\r\n    checkExistAccount(type){\r\n        let email = null;\r\n        let username = null;\r\n        if(type == \"accountName\"){\r\n            username = this.accountInfo.accountName;\r\n        }else if(type == \"email\"){\r\n            email = this.accountInfo.email;\r\n        }\r\n\r\n        let me = this;\r\n\r\n        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{\r\n            if(response >= 1){\r\n                if(type == \"accountName\"){\r\n                    me.isUsernameExisted = true;\r\n                }else{\r\n                    me.isEmailExisted = true;\r\n                }\r\n            }else{\r\n                if(type == \"accountName\"){\r\n                    me.isUsernameExisted = false;\r\n                }else{\r\n                    me.isEmailExisted = false;\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n    onSubmitCreate(){\r\n        let me = this;\r\n        if(me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && me.selectItemCustomer.length == 0) {\r\n            me.messageCommonService.warning(me.tranService.translate('account.message.customerRequired'))\r\n            return;\r\n        }\r\n        this.messageCommonService.onload();\r\n        setTimeout(function(){\r\n            me.handleCreate();\r\n        })\r\n    }\r\n\r\n    handleCreate(){\r\n        if(this.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) {\r\n            this.statusGrantApi = null\r\n            this.selectItemGrantApi = []\r\n        }\r\n        let dataBody = {\r\n            username: this.accountInfo.accountName,\r\n            fullName: this.accountInfo.fullName,\r\n            description: this.accountInfo.description,\r\n            email: this.accountInfo.email,\r\n            phone: this.accountInfo.phone,\r\n            type: this.accountInfo.userType,\r\n            provinceCode: this.accountInfo.province,\r\n            roleLst: (this.accountInfo.roles || []).map(el => el.id),\r\n            customerIdLst: (this.accountInfo.customers || []).map(customer => customer.id),\r\n            idManager: (this.accountInfo.manager || null),\r\n            idUserManageList: (this.accountInfo.customerAccounts || []),\r\n            contractIdLst: (this.selectItemContract || []).map(contract => contract.id),\r\n            accountRootId: this.accountInfo.accountRootId,\r\n            statusApi: this.statusGrantApi,\r\n            listApiId: (this.selectItemGrantApi || []).map(el=>el.id)\r\n        }\r\n        if(dataBody.phone != null){\r\n            if(dataBody.phone.startsWith('0')){\r\n                dataBody.phone = \"84\"+dataBody.phone.substring(1, dataBody.phone.length);\r\n            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){\r\n                dataBody.phone = \"84\"+dataBody.phone;\r\n            }\r\n        }\r\n\r\n        let me = this;\r\n        this.accountService.createAccount(dataBody, (response)=>{\r\n            let body111 = {\r\n                clientId: response.username,\r\n                secretId : this.genGrantApi.secretKey,\r\n                name : response.username,\r\n                active : this.statusGrantApi,\r\n                userId : response.id\r\n            }\r\n            if(this.statusGrantApi) {\r\n                this.accountService.createClientAuthen(body111,()=>{\r\n                    me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                    me.router.navigate(['/accounts/']);\r\n                }, null, ()=>{\r\n                    me.messageCommonService.offload();\r\n                })\r\n            }else {\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.router.navigate(['/accounts/']);\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    closeForm(){\r\n        this.router.navigate(['/accounts'])\r\n    }\r\n\r\n    getListRole(isClear){\r\n        if(isClear){\r\n            this.accountInfo.roles = null;\r\n        }\r\n        let type = -1;\r\n        if(this.accountInfo.userType != null){\r\n            type = this.accountInfo.userType;\r\n        }\r\n        this.accountService.getListRole({type: type, accountRootId: this.accountInfo.accountRootId ? this.accountInfo.accountRootId : -1}, (response)=>{\r\n            this.listRole = response.map(el => {\r\n                return {\r\n                    id: el.id,\r\n                    name: el.name\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    getListCustomer(isClear, name:string=\"\"){\r\n        let me = this;\r\n        if(this.accountInfo.userType == this.optionUserType.CUSTOMER) {\r\n            me.paginationCustomer.page = 0;\r\n            me.paginationContract.page = 0;\r\n            if(isClear){\r\n                this.accountInfo.customers = [];\r\n                this.accountInfo.manager = null;\r\n                this.accountInfo.accountRootId = null\r\n            }\r\n            this.selectItemCustomer = []\r\n            this.selectItemContract = []\r\n            if(this.accountInfo.province != null){\r\n                this.paramSearchCustomerProvince = {provinceCode: this.accountInfo.province}\r\n                this.paramSearchManager = {type: 3, provinceCode: this.accountInfo.province}\r\n                me.paramSearchCustomerAccount.provinceCode = me.accountInfo.province\r\n            }\r\n            // nếu tài khoản thực hiện tạo là gdv thì gán gdv\r\n            if (me.userType == CONSTANTS.USER_TYPE.DISTRICT){\r\n                me.accountInfo.manager = me.accountId\r\n            }\r\n            if (me.accountInfo.manager) {\r\n                me.paramSearchCustomerAccount.managerId = me.accountInfo.manager\r\n            } else {\r\n                me.paramSearchCustomerAccount.managerId = -1;\r\n            }\r\n            me.getListRole(true)\r\n            // reload and clear selected when changer root account\r\n            if(this.genGrantApi.secretKey != null) {\r\n                me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n                this.selectItemGrantApi = []\r\n            }\r\n        }\r\n        if(this.accountInfo.userType == this.optionUserType.DISTRICT) {\r\n            if(isClear){\r\n                this.accountInfo.customerAccounts = null;\r\n            }\r\n            if(this.accountInfo.province != null){\r\n                me.paramSearchCustomerAccount.provinceCode = me.accountInfo.province\r\n            }\r\n            if (me.accountInfo.manager) {\r\n                me.paramSearchCustomerAccount.managerId = me.accountInfo.manager\r\n            } else {\r\n                me.paramSearchCustomerAccount.managerId = -1;\r\n            }\r\n        }\r\n    }\r\n    onChangeTeller() {\r\n        let me = this;\r\n        this.accountInfo.customers = [];\r\n        this.selectItemCustomer = []\r\n        this.selectItemContract = []\r\n        this.accountInfo.accountRootId = null\r\n\r\n        if (me.accountInfo.manager) {\r\n            me.paramSearchCustomerAccount.managerId = me.accountInfo.manager\r\n            me.paramQuickSearchCustomer.managerId = me.accountInfo.manager\r\n        } else {\r\n            me.paramSearchCustomerAccount.managerId = -1;\r\n            me.paramQuickSearchCustomer.managerId = me.accountInfo.manager\r\n        }\r\n    }\r\n\r\n    filterCustomerByName(event){\r\n        this.debounceService.set(\"filterCustomer\", this.getListCustomer.bind(this), false, event.filter);\r\n    }\r\n\r\n    getListProvince(){\r\n        this.accountService.getListProvince((response)=>{\r\n            this.listProvince = response.map(el => {\r\n                return {\r\n                    id: el.code,\r\n                    name: `${el.name} (${el.code})`\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    loadCustomerAccount(params, callback) {\r\n        return this.accountService.getCustomerAccount(params, callback)\r\n    }\r\n    onSearchCustomer(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationCustomer.page = 0;\r\n        }\r\n        me.paramQuickSearchCustomer.provinceCode = this.accountInfo.province,\r\n        me.paramQuickSearchCustomer.accountRootId = me.userType == CONSTANTS.USER_TYPE.CUSTOMER ? me.accountId : me.accountInfo.accountRootId\r\n        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\r\n    }\r\n    searchCustomer(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationCustomer.page = page;\r\n        this.paginationCustomer.size = limit;\r\n        this.paginationCustomer.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                dataParams[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{\r\n            me.dataSetCustomer = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            if(this.selectItemCustomer.length==response.totalElements && response.totalElements != 0){\r\n                this.customSelectAllCustomer = true\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        // console.log(this.selectItemCustomer)\r\n    }\r\n\r\n    onSearchContract(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationContract.page = 0;\r\n        }\r\n        me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),\r\n        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\r\n    }\r\n\r\n    onSearchGrantApi(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationGrantApi.page = 0;\r\n        }\r\n        // me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),\r\n            me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n    }\r\n    searchContract(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationContract.page = page;\r\n        this.paginationContract.size = limit;\r\n        this.paginationContract.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        // Object.keys(this.paramQuickSearchContract).forEach(key => {\r\n        //     if(this.paramQuickSearchContract[key] != null){\r\n        //         dataParams[key] = this.paramQuickSearchContract[key];\r\n        //     }\r\n        // })\r\n        me.messageCommonService.onload();\r\n        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{\r\n            me.dataSetContract = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            if(this.selectItemContract.length==response.totalElements && response.totalElements !=0){\r\n                this.customSelectAllContract = true\r\n            }\r\n            // console.log(response)\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    searchGrantApi(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationGrantApi.page = page;\r\n        this.paginationGrantApi.size = limit;\r\n        this.paginationGrantApi.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramsSearchGrantApi).forEach(key => {\r\n            if(this.paramsSearchGrantApi[key] != null){\r\n                dataParams[key] = this.paramsSearchGrantApi[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        // tài khoản tỉnh , admin, GDV khi tạo KH chưa chọn accountRoot ->lấy full\r\n        if((this.userType == CONSTANTS.USER_TYPE.PROVINCE ||\r\n            this.userType == CONSTANTS.USER_TYPE.ADMIN ||\r\n            this.userType == CONSTANTS.USER_TYPE.DISTRICT) && !this.accountInfo.accountRootId) {\r\n            this.accountService.searchGrantApi(dataParams,(response)=>{\r\n                me.dataSetGrantApi = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            let copyParam = {...dataParams};\r\n            copyParam.size = *********;\r\n            this.accountService.searchGrantApi(copyParam,(response)=>{\r\n                me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n                me.listModule = me.listModule.map(el=>({\r\n                    name : el,\r\n                    value : el\r\n                }))\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            // tài khoản tỉnh , admin, GDV khi tạo KH đã chọn accountRoot -> lấy theo accountRoot\r\n            // tài khoản tạo là KH và là tài khoản con -> lấy theo cha\r\n        }else if((this.userType == CONSTANTS.USER_TYPE.PROVINCE ||\r\n            this.userType == CONSTANTS.USER_TYPE.ADMIN ||\r\n            this.userType == CONSTANTS.USER_TYPE.DISTRICT || this.userType == CONSTANTS.USER_TYPE.CUSTOMER) && this.accountInfo.accountRootId) {\r\n            dataParams['userCustomerParent'] = this.accountInfo.accountRootId\r\n            this.accountService.getListAPI2(dataParams,(response)=>{\r\n                me.dataSetGrantApi = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            let copyParam = {...dataParams};\r\n            copyParam.size = *********;\r\n            this.accountService.getListAPI2(copyParam,(response)=>{\r\n                me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n                me.listModule = me.listModule.map(el=>({\r\n                    name : el,\r\n                    value : el\r\n                }))\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n        // tài khoản tạo là KH và là tài khoản cha -> lấy theo cái nó được tài khoản cấp trên gán\r\n        }else if(this.userType == CONSTANTS.USER_TYPE.CUSTOMER && !this.accountInfo.accountRootId) {\r\n            dataParams['selectedApiIds'] = (this.accountCurrentDetail.listApiId || [-99]).join(\",\");\r\n            this.accountService.searchGrantApi(dataParams,(response)=>{\r\n                me.dataSetGrantApi = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            let copyParam = {...dataParams};\r\n            copyParam.size = *********;\r\n            this.accountService.searchGrantApi(copyParam,(response)=>{\r\n                me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n                me.listModule = me.listModule.map(el=>({\r\n                    name : el,\r\n                    value : el\r\n                }))\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n    protected readonly onclick = onclick;\r\n    onTabChange(event) {\r\n        const tabName = event.originalEvent.target.innerText;\r\n        let me = this;\r\n        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\r\n            this.genGrantApi.clientId = this.accountInfo.accountName;\r\n        } else if (event && tabName.includes(this.tranService.translate('account.text.addContract'))) {\r\n            me.onSearchContract()\r\n        } else if (event && tabName.includes(this.tranService.translate('account.text.addCustomer'))) {\r\n            me.onSearchCustomer()\r\n        }\r\n    }\r\n\r\n    generateToken(n) {\r\n        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n        var token = '';\r\n        for(var i = 0; i < n; i++) {\r\n            token += chars[Math.floor(Math.random() * chars.length)];\r\n        }\r\n        return token;\r\n    }\r\n\r\n    checkSelectItemChangeCustomer(event: any[]) {\r\n        let me = this;\r\n\r\n        if(this.selectItemCustomer.length==this.dataSetCustomer.total){\r\n            this.customSelectAllCustomer = true\r\n        }else{\r\n            this.customSelectAllCustomer = false\r\n        }\r\n\r\n        const currentCustomerIds = new Set((event|| []).map(customer => customer.id));\r\n        const previousCustomerIds = new Set((this.accountInfo.customers|| []).map(customer => customer.id));\r\n\r\n        const addedCustomers = (event|| []).filter(customer => !previousCustomerIds.has(customer.id));\r\n\r\n        const removedCustomers = this.accountInfo.customers.filter(customer => !currentCustomerIds.has(customer.id));\r\n\r\n        this.fetchContractsByCustomerId((addedCustomers|| []).map(customer => customer.id))\r\n\r\n        removedCustomers.forEach(customer => {\r\n            this.selectItemContract = this.selectItemContract.filter(contract => contract.customerCode != customer.code);\r\n        });\r\n\r\n        this.accountInfo.customers = event;\r\n    }\r\n    fetchContractsByCustomerId(customerIds: number[]) {\r\n\r\n        let me = this;\r\n        this.messageCommonService.onload()\r\n        this.paginationContract.page = 0;\r\n        let dataParams = {\r\n            page: '0',\r\n            size: '10000',\r\n            sort: this.paginationContract.sortBy\r\n        }\r\n        this.contractService.quickSearchContract(dataParams,\r\n            {\r\n                keyword: null,\r\n                accountRootId: this.accountInfo.accountRootId,\r\n                provinceCode: this.accountInfo.province,\r\n                customerIds: customerIds,\r\n            }, (res) => {\r\n                if (res.totalElements > 0) {\r\n                    const newContracts = res.content.filter(contract => !this.deselectedContracts.has(contract.contractCode) &&\r\n                        !this.selectItemContract.some(existingContract => existingContract.contractCode === contract.contractCode));\r\n                    this.selectItemContract.push(...newContracts);\r\n                }\r\n            }, null, () => {\r\n                me.messageCommonService.offload();\r\n            })\r\n    }\r\n\r\n    checkSelectItemChangeContract(event) {\r\n        if(this.selectItemContract.length==this.dataSetContract.total){\r\n            this.customSelectAllContract = true\r\n        }else{\r\n            this.customSelectAllContract = false\r\n        }\r\n    }\r\n\r\n    onChangeSelectAllItemsCustomer(){\r\n        // console.log(this.selectItemCustomer);\r\n        let me = this;\r\n        let params = {\r\n            page: \"0\",\r\n            size: \"********\",\r\n            sort: \"name,asc;id,asc\",\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                params[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        this.loadingCustomer = true\r\n        this.customerService.quickSearchCustomer(params,this.paramQuickSearchCustomer,(response)=>{\r\n            if(this.selectItemCustomer.length == response.totalElements){\r\n                this.selectItemCustomer = [];\r\n                this.customSelectAllCustomer = false\r\n                return;\r\n            }\r\n            this.selectItemCustomer = response.content\r\n            this.customSelectAllCustomer = true;\r\n        },null,()=>{ this.loadingCustomer = false });\r\n    }\r\n\r\n    onChangeSelectAllItemsContract(){\r\n        // console.log(this.selectItemCustomer);\r\n        let me = this;\r\n        let params = {\r\n            page: \"0\",\r\n            size: \"********\",\r\n            sort: \"customerName,asc;id,asc\",\r\n        }\r\n        this.loadingContract = true\r\n        this.contractService.quickSearchContract(params, this.paramQuickSearchContract,(response)=>{\r\n            if(this.selectItemContract.length == response.totalElements){\r\n                this.selectItemContract = [];\r\n                this.customSelectAllContract = false\r\n                return;\r\n            }\r\n            this.selectItemContract = response.content\r\n            this.customSelectAllContract = true\r\n        }, null, ()=>{\r\n            this.loadingContract = false;\r\n        })\r\n    }\r\n\r\n    checkShowTabAddCustomerAndContract() {\r\n        let me = this\r\n\r\n        if (me.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER)  return false;\r\n        if (me.formAccount.invalid || me.isEmailExisted || me.isPhoneExisted || me.isUsernameExisted ) {\r\n            // Object.keys(this.formAccount.controls).forEach(key => {\r\n            //     const control = this.formAccount.get(key);\r\n            //     if (control.invalid) {\r\n            //         console.log('Field:', key, 'is invalid. Errors:', control.errors);\r\n            //     }\r\n            // });\r\n            // console.log(me.isEmailExisted)\r\n            // console.log(me.isPhoneExisted)\r\n            // console.log(me.isUsernameExisted)\r\n            return false;\r\n        }\r\n        if ((me.userType == CONSTANTS.USER_TYPE.ADMIN && this.accountInfo.province == null) || ((me.userType == CONSTANTS.USER_TYPE.ADMIN || me.userType == CONSTANTS.USER_TYPE.PROVINCE) && me.accountInfo.manager == null)){\r\n            return false\r\n        }\r\n        return  true\r\n    }\r\n\r\n    genToken(){\r\n        let me = this;\r\n        if(this.genGrantApi.secretKey) {\r\n            this.genGrantApi.secretKey = this.generateToken(20);\r\n        }else {\r\n            this.genGrantApi.secretKey = this.generateToken(20);\r\n            me.onSearchGrantApi()\r\n        }\r\n    }\r\n}\r\n", "<form [formGroup]=\"formAccount\" (keydown.enter)=\"$event.preventDefault()\" (ngSubmit)=\"onSubmitCreate()\" style=\"position: relative\">\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{this.tranService.translate(\"global.menu.listaccount\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <div class=\"flex flex-row justify-content-right align-items-center mr-6\">\r\n            <p-button [label]=\"tranService.translate('global.button.save')\" styleClass=\"p-button-info\" type=\"submit\"\r\n                      [disabled]=\"formAccount.invalid || isEmailExisted || isPhoneExisted || isUsernameExisted || ((userType == CONSTANTS.USER_TYPE.PROVINCE || userType == CONSTANTS.USER_TYPE.ADMIN) &&  accountInfo.userType === CONSTANTS.USER_TYPE.CUSTOMER && !accountInfo.manager)\"></p-button>\r\n            <p-button [label]=\"tranService.translate('global.button.cancel')\" styleClass=\"p-button-secondary p-button-outlined ml-3\" (click)=\"closeForm()\"></p-button>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<p-card styleClass=\"mt-3 responsive-pcard\">\r\n    <p-tabView (onChange)=\"onTabChange($event)\">\r\n        <p-tabPanel header=\"{{tranService.translate('account.label.generalInfo')}}\">\r\n                <div class=\"flex flex-row justify-content-between\">\r\n                    <div style=\"width: 49%;\">\r\n                        <!-- username -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.username\")}}<span class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"accountName\"\r\n                                       [(ngModel)]=\"accountInfo.accountName\"\r\n                                       formControlName=\"accountName\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"50\"\r\n                                       pattern=\"^[a-zA-Z0-9\\-_]*$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputUsername')\"\r\n                                       (ngModelChange)=\"checkExistAccount('accountName')\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- error username -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.accountName.dirty && formAccount.controls.accountName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.accountName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:50})}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.accountName.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"isUsernameExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.username\").toLowerCase()})}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- fullname -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.fullname\")}}<span class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"fullName\"\r\n                                       [(ngModel)]=\"accountInfo.fullName\"\r\n                                       formControlName=\"fullName\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"255\"\r\n                                       pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- error fullname -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.dirty && formAccount.controls.fullName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- phone -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.phone\")}}</label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"phone\"\r\n                                       [(ngModel)]=\"accountInfo.phone\"\r\n                                       formControlName=\"phone\"\r\n                                       pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- error phone -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.phone.errors?.pattern\">{{tranService.translate(\"global.message.invalidPhone\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"isPhoneExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.phone\").toLowerCase()})}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- email -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.email\")}}<span class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"email\"\r\n                                       [(ngModel)]=\"accountInfo.email\"\r\n                                       formControlName=\"email\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"255\"\r\n                                       pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                                       (ngModelChange)=\"checkExistAccount('email')\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- error email -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.dirty && formAccount.controls.email.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.errors?.pattern\">{{tranService.translate(\"global.message.invalidEmail\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- description -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.description\")}}</label>\r\n                            <div class=\"col\">\r\n                            <textarea  class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"description\"\r\n                                       [(ngModel)]=\"accountInfo.description\"\r\n                                       formControlName=\"description\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                            ></textarea>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error description -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.description.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div style=\"width: 49%;\">\r\n\r\n                        <!-- loai tai khoan -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.userType\")}}<span class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <p-dropdown styleClass=\"w-full\"\r\n                                            [showClear]=\"true\"\r\n                                            id=\"userType\" [autoDisplayFirst]=\"false\"\r\n                                            [(ngModel)]=\"accountInfo.userType\"\r\n                                            [required]=\"true\"\r\n                                            formControlName=\"userType\"\r\n                                            [options]=\"statusAccounts\"\r\n                                            optionLabel=\"name\"\r\n                                            optionValue=\"value\"\r\n                                            [placeholder]=\"tranService.translate('account.text.selectUserType')\"\r\n                                            (ngModelChange)=\"getListRole(true)\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error loai tai khoan -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.userType.dirty && formAccount.controls.userType.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- Tinh thanh pho -->\r\n                        <div class=\"w-full field grid\" [class]=\" (accountInfo.userType == optionUserType.ADMIN) ? 'hidden' : ''\">\r\n                            <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.province\")}}<span class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <p-dropdown styleClass=\"w-full\"\r\n                                            [showClear]=\"userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\"\r\n                                            [readonly]=\"userType != optionUserType.ADMIN\"\r\n                                            [disabled]=\"userType != optionUserType.ADMIN\"\r\n                                            id=\"province\" [autoDisplayFirst]=\"false\"\r\n                                            [(ngModel)]=\"accountInfo.province\"\r\n                                            [required]=\"userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\"\r\n                                            formControlName=\"province\"\r\n                                            [options]=\"listProvince\"\r\n                                            optionLabel=\"name\"\r\n                                            [filter]=\"true\" filterBy=\"name\"\r\n                                            optionValue=\"id\"\r\n                                            [placeholder]=\"tranService.translate('account.text.selectProvince')\"\r\n                                            (ngModelChange)=\"getListCustomer(true)\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error tinh thanh pho -->\r\n                        <div class=\"w-full field grid text-error-field\" [class]=\"userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY ? '' : 'hidden'\">\r\n                            <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"formAccount.controls.province.dirty && formAccount.controls.province.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- GDV quan ly-->\r\n                        <div class=\"w-full field grid\" [class]=\"accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.managerName\")}}<span class=\"text-red-500\">*</span></label>\r\n<!--                            <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n                            <div class=\"col\">\r\n                                <vnpt-select\r\n                                    [control]=\"controlComboSelectManager\"\r\n                                    class=\"w-full\"\r\n                                    [(value)]=\"accountInfo.manager\"\r\n                                    [placeholder]=\"tranService.translate('account.text.selectGDV')\"\r\n                                    objectKey=\"account\"\r\n                                    paramKey=\"name\"\r\n                                    keyReturn=\"id\"\r\n                                    displayPattern=\"${fullName} - ${username}\"\r\n                                    typeValue=\"primitive\"\r\n                                    [isMultiChoice]=\"false\"\r\n                                    [required] = \"accountInfo.userType == optionUserType.CUSTOMER ? true :false\"\r\n                                    [paramDefault]=\"paramSearchManager\"\r\n                                    (onchange)=\"onChangeTeller()\"\r\n                                ></vnpt-select>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error GDV quan ly -->\r\n                        <div class=\"w-full field grid text-error-field\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">\r\n                            <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"controlComboSelectManager.dirty && controlComboSelectManager.error.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!--                    &lt;!&ndash; ten khach hang &ndash;&gt;-->\r\n                        <!--                    <div class=\"w-full field grid\" [class]=\"accountInfo.userType == optionUserType.CUSTOMER ? '' : 'hidden'\">-->\r\n                        <!--                        <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerName\")}}<span class=\"text-red-500\">*</span></label>-->\r\n                        <!--                        <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n                        <!--                            <vnpt-select-->\r\n                        <!--                                [control]=\"controlComboSelect\"-->\r\n                        <!--                                class=\"w-full\"-->\r\n                        <!--                                [(value)]=\"accountInfo.customers\"-->\r\n                        <!--                                [placeholder]=\"tranService.translate('account.text.selectCustomers')\"-->\r\n                        <!--                                objectKey=\"customer\"-->\r\n                        <!--                                paramKey=\"customerName\"-->\r\n                        <!--                                keyReturn=\"id\"-->\r\n                        <!--                                displayPattern=\"${customerName} - ${customerCode}\"-->\r\n                        <!--                                typeValue=\"primitive\"-->\r\n                        <!--                                [paramDefault]=\"paramSearchCustomerProvince\"-->\r\n                        <!--                                [required]=\"accountInfo.userType == optionUserType.CUSTOMER\"-->\r\n                        <!--                            ></vnpt-select>-->\r\n                        <!--                        </div>-->\r\n                        <!--                    </div>-->\r\n                        <!--                    &lt;!&ndash; error khach hang &ndash;&gt;-->\r\n                        <!--                    <div class=\"w-full field grid text-error-field\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n                        <!--                        <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>-->\r\n                        <!--                        <div class=\"col\">-->\r\n                        <!--                            <small class=\"text-red-500\" *ngIf=\"controlComboSelect.dirty && controlComboSelect.error.required\">{{tranService.translate(\"global.message.required\")}}</small>-->\r\n                        <!--                        </div>-->\r\n                        <!--                    </div>-->\r\n\r\n                        <!-- Danh sach tai khoan khach hang -->\r\n                        <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.DISTRICT\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px);\">\r\n<!--                            <div class=\"col\">-->\r\n                                <vnpt-select\r\n                                    [control]=\"controlComboSelectCustomerAccount\"\r\n                                    class=\"w-full\"\r\n                                    [(value)]=\"accountInfo.customerAccounts\"\r\n                                    [placeholder]=\"tranService.translate('account.text.selectCustomerAccount')\"\r\n                                    objectKey=\"account\"\r\n                                    paramKey=\"username\"\r\n                                    keyReturn=\"id\"\r\n                                    displayPattern=\"${fullName} - ${username}\"\r\n                                    typeValue=\"primitive\"\r\n                                    [paramDefault]=\"accountInfo.manager != null ? paramSearchCustomerAccount : paramSearchCustomerAccount\"\r\n                                    [loadData]=\"loadCustomerAccount.bind(this)\"\r\n                                ></vnpt-select>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error Danh sach tai khoan khach hang-->\r\n                        <div class=\"w-full field grid text-error-field\" *ngIf=\"accountInfo.userType == optionUserType.DISTRICT\">\r\n                            <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\" *ngIf=\"controlComboSelectCustomerAccount.dirty && controlComboSelectCustomerAccount.error.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- Danh sách tài khoản khách hàng để chọn tài khoản khách hàng root -->\r\n                        <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER &&\r\n                        (userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.PROVINCE || userType == CONSTANTS.USER_TYPE.DISTRICT)\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n<!--                            <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n                            <div class=\"col\">\r\n                                <vnpt-select\r\n                                    [control]=\"controlComboSelectCustomerAccount\"\r\n                                    class=\"w-full\"\r\n                                    [(value)]=\"accountInfo.accountRootId\"\r\n                                    [placeholder]=\"tranService.translate('account.text.selectCustomerAccount')\"\r\n                                    objectKey=\"account\"\r\n                                    paramKey=\"username\"\r\n                                    keyReturn=\"id\"\r\n                                    displayPattern=\"${fullName} - ${username}\"\r\n                                    typeValue=\"primitive\"\r\n                                    [paramDefault]=\"paramSearchCustomerAccount\"\r\n                                    [loadData]=\"loadCustomerAccount.bind(this)\"\r\n                                    (onchange)=\"getListCustomer(false)\"\r\n                                    [isMultiChoice]=\"false\"\r\n                                ></vnpt-select>\r\n                            </div>\r\n                        </div>\r\n                        <!--                    <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n                        <!--                        <i htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px; text-decoration: underline\" [ngStyle]=\"{color: 'var(&#45;&#45;mainColorText)', cursor: 'pointer'}\" (click)=\"showDialogCustomer()\">{{tranService.translate(\"account.text.addCustomer\")}}<span>*</span></i>-->\r\n                        <!--                    </div>-->\r\n                        <!--                    <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n                        <!--                        <i htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px; text-decoration: underline\" [ngStyle]=\"{color: 'var(&#45;&#45;mainColorText)', cursor: 'pointer'}\" >{{tranService.translate(\"account.text.addContract\")}}</i>-->\r\n                        <!--                    </div>-->\r\n                        <!-- nhom quyen -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"account.label.role\") }}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px)\">\r\n                                <p-multiSelect styleClass=\"w-full\"\r\n                                               id=\"roles\"\r\n                                               [(ngModel)]=\"accountInfo.roles\"\r\n                                               [options]=\"listRole\"\r\n                                               [defaultLabel]=\"tranService.translate('account.text.selectRoles')\"\r\n                                               optionLabel=\"name\"\r\n                                               display=\"chip\"\r\n                                               formControlName=\"roles\"\r\n                                               [resetFilterOnHide]=true\r\n                                ></p-multiSelect>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n<!--                <div class=\"flex flex-row justify-content-between align-items-center\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n<!--                </div>-->\r\n\r\n        </p-tabPanel>\r\n        <p-tabPanel header=\"{{tranService.translate('account.text.addCustomer')}}*\" *ngIf=\"checkShowTabAddCustomerAndContract()\">\r\n            <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"$event.preventDefault(); onSearchCustomer(true)\" [(ngModel)]=\"paramQuickSearchCustomer.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"button\"\r\n                          (click)=\"onSearchCustomer(true)\"\r\n                ></p-button>\r\n            </div>\r\n            <div class=\"flex relative justify-content-center align-items-center w-full h-full\">\r\n                <div class=\"absolute flex justify-content-center align-items-center w-full h-full\" [ngStyle]=\"{\r\n                           'top': '50%',\r\n                           'left': '50%',\r\n                           'transform': 'translate(-50%, -50%)',\r\n                           'z-index': '10',\r\n                           'background': 'rgba(0, 0, 0, 0.1)'\r\n                         }\"\r\n                     *ngIf=\"loadingCustomer\">\r\n                    <p-progressSpinner></p-progressSpinner>\r\n                </div>\r\n                <div class=\"w-full h-full\">\r\n                    <table-vnpt\r\n                        [fieldId]=\"'id'\"\r\n                        [pageNumber]=\"paginationCustomer.page\"\r\n                        [pageSize]=\"paginationCustomer.size\"\r\n                        [(selectItems)]=\"selectItemCustomer\"\r\n                        [columns]=\"columnInfoCustomer\"\r\n                        [dataSet]=\"dataSetCustomer\"\r\n                        [options]=\"optionTableCustomer\"\r\n                        [loadData]=\"searchCustomer.bind(this)\"\r\n                        [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                        [scrollHeight]=\"'400px'\"\r\n                        [sort]=\"paginationCustomer.sortBy\"\r\n                        [params]=\"paramQuickSearchCustomer\"\r\n                        (selectItemsChange)=\"checkSelectItemChangeCustomer($event)\"\r\n                        (onChangeCustomSelectAllEmmiter)=\"onChangeSelectAllItemsCustomer()\"\r\n                        isUseCustomSelectAll=\"true\"\r\n                        [(customSelectAll)]=\"customSelectAllCustomer\"\r\n                    ></table-vnpt>\r\n                </div>\r\n            </div>\r\n        </p-tabPanel>\r\n        <p-tabPanel header=\"{{tranService.translate('account.text.addContract')}}\" *ngIf=\"checkShowTabAddCustomerAndContract() && selectItemCustomer && selectItemCustomer.length > 0\">\r\n            <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"$event.preventDefault(); onSearchContract(true)\" [(ngModel)]=\"paramQuickSearchContract.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"button\"\r\n                          (click)=\"onSearchContract(true)\"\r\n                ></p-button>\r\n            </div>\r\n            <div class=\"flex relative justify-content-center align-items-center w-full h-full\">\r\n                <div class=\"absolute flex justify-content-center align-items-center w-full h-full\" [ngStyle]=\"{\r\n                           'top': '50%',\r\n                           'left': '50%',\r\n                           'transform': 'translate(-50%, -50%)',\r\n                           'z-index': '10',\r\n                           'background': 'rgba(0, 0, 0, 0.1)'\r\n                         }\"\r\n                     *ngIf=\"loadingContract\">\r\n                    <p-progressSpinner></p-progressSpinner>\r\n                </div>\r\n               <div class=\"w-full h-full\">\r\n                    <table-vnpt\r\n                        [fieldId]=\"'id'\"\r\n                        [pageNumber]=\"paginationContract.page\"\r\n                        [pageSize]=\"paginationContract.size\"\r\n                        [(selectItems)]=\"selectItemContract\"\r\n                        [columns]=\"columnInfoContract\"\r\n                        [dataSet]=\"dataSetContract\"\r\n                        [options]=\"optionTableContract\"\r\n                        [loadData]=\"searchContract.bind(this)\"\r\n                        [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                        [scrollHeight]=\"'400px'\"\r\n                        [sort]=\"paginationContract.sortBy\"\r\n                        [params]=\"paramQuickSearchContract\"\r\n                        (selectItemsChange)=\"checkSelectItemChangeContract($event)\"\r\n                        (onChangeCustomSelectAllEmmiter)=\"onChangeSelectAllItemsContract()\"\r\n                        isUseCustomSelectAll=\"true\"\r\n                        [(customSelectAll)]=\"customSelectAllContract\"\r\n                    ></table-vnpt>\r\n               </div>\r\n            </div>\r\n        </p-tabPanel>\r\n\r\n        <p-tabPanel header=\"{{tranService.translate('account.text.grantApi')}}\" *ngIf=\"(checkAuthen([CONSTANTS.PERMISSIONS.THIRD_PARTY_API.GRANT_PERMISSION_3RD_API])) && accountInfo.userType == optionUserType.CUSTOMER\" [pt]=\"'ProfileTab'\">\r\n            <div class=\"mb-3\">\r\n                <p-panel [showHeader]=\"false\">\r\n                    <div class=\"flex gap-2\">\r\n                        <p-radioButton\r\n                                [label]=\"tranService.translate('account.text.working')\"\r\n                                value=\"1\"\r\n                                class=\"p-3\"\r\n                                [(ngModel)]=\"statusGrantApi\"\r\n                                inputId=\"1\"\r\n                                [ngModelOptions]=\"{standalone: true}\"\r\n                        >\r\n                        </p-radioButton>\r\n                        <p-radioButton\r\n                                [label]=\"tranService.translate('account.text.notWorking')\"\r\n                                value=\"0\"\r\n                                class=\"p-3\"\r\n                                [(ngModel)]=\"statusGrantApi\"\r\n                                [ngModelOptions]=\"{standalone: true}\"\r\n                        >\r\n                        </p-radioButton>\r\n                    </div>\r\n                    <div class=\"flex gap-3 align-items-center\">\r\n                        <div class=\"col-5\">\r\n                            <div class=\"flex align-items-center\">\r\n                                <label style=\"min-width: 100px\" class=\"mr-3\">Client ID</label>\r\n                                <input [(ngModel)]=\"genGrantApi.clientId\"  [disabled]=\"true\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full\" type=\"text\" pInputText>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-5\">\r\n                            <div class=\"flex align-items-center\">\r\n                                <label style=\"min-width: 100px\" class=\"mr-3\">Secret Key</label>\r\n                                <div class=\"w-full flex align-items-center\">\r\n                                    <input class=\"w-full mr-2\" style=\"padding-right: 30px;\"\r\n                                           [(ngModel)]=\"genGrantApi.secretKey\"\r\n                                           [ngModelOptions]=\"{standalone: true}\"\r\n                                           [type]=\"isShowSecretKey ? 'text': 'password'\"\r\n                                           pInputText\r\n                                           [disabled]=\"true\"\r\n                                    />\r\n                                    <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == false\" class=\"pi pi-eye toggle-password\" (click)=\"isShowSecretKey = true\"></label>\r\n                                    <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == true\" class=\"pi pi-eye-slash toggle-password\" (click)=\"isShowSecretKey = false\"></label>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-2\">\r\n                            <p-button (click)=\"genToken()\" [label]=\"tranService.translate('account.text.gen')\" styleClass=\"p-button-primary mr-2\"></p-button>\r\n                        </div>\r\n                    </div>\r\n                </p-panel>\r\n            </div>\r\n            <div>\r\n                <p-panel [showHeader]=\"false\" class=\"  \" *ngIf=\"genGrantApi.secretKey\">\r\n                    <div class=\"flex gap-3 align-items-center\">\r\n                        <div class=\"col-3\">\r\n                            <p-dropdown class=\"w-full\"\r\n                                        [showClear]=\"true\"\r\n                                        [(ngModel)]=\"paramsSearchGrantApi.module\"\r\n                                        [ngModelOptions]=\"{standalone: true}\"\r\n                                        [options]=\"listModule\"\r\n                                        optionLabel=\"name\"\r\n                                        optionValue=\"value\"\r\n                                        [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                        filter=\"true\"\r\n                                        [placeholder]=\"tranService.translate('account.text.module')\"\r\n                            ></p-dropdown>\r\n                        </div>\r\n                        <div class=\"col-3\">\r\n                            <input [(ngModel)]=\"paramsSearchGrantApi.api\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full mr-2\" type=\"text\" pInputText placeholder=\"API\"/>\r\n                        </div>\r\n                        <p-button icon=\"pi pi-search\"\r\n                                  styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                  type=\"button\"\r\n                                  (click)=\"onSearchGrantApi(true)\"\r\n                        ></p-button>\r\n                    </div>\r\n\r\n                    <table-vnpt\r\n                            [fieldId]=\"'id'\"\r\n                            [pageNumber]=\"paginationGrantApi.page\"\r\n                            [pageSize]=\"paginationGrantApi.size\"\r\n                            [(selectItems)]=\"selectItemGrantApi\"\r\n                            [columns]=\"columnInfoGrantApi\"\r\n                            [dataSet]=\"dataSetGrantApi\"\r\n                            [options]=\"optionTableGrantApi\"\r\n                            [loadData]=\"searchGrantApi.bind(this)\"\r\n                            [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                            [scrollHeight]=\"'400px'\"\r\n                            [sort]=\"paginationGrantApi.sortBy\"\r\n                            [params]=\"paramsSearchGrantApi\"\r\n                    ></table-vnpt>\r\n                </p-panel>\r\n            </div>\r\n        </p-tabPanel>\r\n    </p-tabView>\r\n<!--        <div class=\"flex flex-row justify-content-center align-items-center\">-->\r\n<!--            <p-button [label]=\"tranService.translate('global.button.cancel')\" styleClass=\"p-button-secondary p-button-outlined mr-2\" (click)=\"closeForm()\"></p-button>-->\r\n<!--            <p-button [label]=\"tranService.translate('global.button.save')\" styleClass=\"p-button-info\" type=\"submit\"-->\r\n<!--                      [disabled]=\"formAccount.invalid || isEmailExisted || isPhoneExisted || isUsernameExisted\"></p-button>-->\r\n<!--        </div>-->\r\n</p-card>\r\n</form>\r\n\r\n"], "mappings": "AAGA,SAASA,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAASC,gBAAgB,QAAQ,yDAAyD;;;;;;;;;;;;;;;;;;;;;;ICkC1DC,EAAA,CAAAC,cAAA,gBAAgI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACpLR,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAtEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA8D;;;;;IACrJX,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,8BAAsD;;;;;;;;;;IAC3IR,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAwH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhIH,EAAA,CAAAI,SAAA,GAAwH;IAAxHJ,EAAA,CAAAK,iBAAA,CAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAF,MAAA,CAAAN,WAAA,CAAAC,SAAA,2BAAAQ,WAAA,KAAwH;;;;;IAsB9KhB,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAY,MAAA,CAAAV,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IAC9KR,EAAA,CAAAC,cAAA,gBAAoF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAX,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAS,GAAA,GAA+D;;;;;IACnJnB,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,iBAAA,CAAAe,MAAA,CAAAb,WAAA,CAAAC,SAAA,mCAA2D;;;;;IAoB7IR,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhEH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAd,WAAA,CAAAC,SAAA,gCAAwD;;;;;IACvIR,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA7HH,EAAA,CAAAI,SAAA,GAAqH;IAArHJ,EAAA,CAAAK,iBAAA,CAAAiB,MAAA,CAAAf,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAO,MAAA,CAAAf,WAAA,CAAAC,SAAA,wBAAAQ,WAAA,KAAqH;;;;;IAuBxKhB,EAAA,CAAAC,cAAA,gBAAoH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAkB,MAAA,CAAAhB,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACxKR,EAAA,CAAAC,cAAA,gBAAiF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAmB,OAAA,CAAAjB,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAS,GAAA,GAA+D;;;;;IAChJnB,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhEH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAoB,OAAA,CAAAlB,WAAA,CAAAC,SAAA,gCAAwD;;;;;IACvIR,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA7HH,EAAA,CAAAI,SAAA,GAAqH;IAArHJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAnB,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAW,OAAA,CAAAnB,WAAA,CAAAC,SAAA,wBAAAQ,WAAA,KAAqH;;;;;IAsBxKhB,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAsB,OAAA,CAAApB,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAS,GAAA,GAA+D;;;;;IA4BtJnB,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAuB,OAAA,CAAArB,WAAA,CAAAC,SAAA,4BAAoD;;;;;IA4B9KR,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAwB,OAAA,CAAAtB,WAAA,CAAAC,SAAA,4BAAoD;;;;;IA6B9KR,EAAA,CAAAC,cAAA,gBAAgH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAyB,OAAA,CAAAvB,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAH5KR,EAAA,CAAAC,cAAA,cAAwG;IACpGD,EAAA,CAAA+B,SAAA,gBAAwE;IACxE/B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAgC,UAAA,IAAAC,kDAAA,oBAA4K;IAChLjC,EAAA,CAAAG,YAAA,EAAM;;;;IAD2BH,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAkC,UAAA,SAAAC,OAAA,CAAAC,yBAAA,CAAAC,KAAA,IAAAF,OAAA,CAAAC,yBAAA,CAAAE,KAAA,CAAAC,QAAA,CAAiF;;;;;;IA+BtHvC,EAAA,CAAAC,cAAA,cAAuF;IACtBD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/HH,EAAA,CAAAC,cAAA,cAAwD;IAKhDD,EAAA,CAAAwC,UAAA,yBAAAC,8EAAAC,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAA8C,aAAA;MAAA,OAAW9C,EAAA,CAAA+C,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAC,gBAAA,GAAAP,MAAA,CAC1C;IAAA,EADuE;IAS3C1C,EAAA,CAAAG,YAAA,EAAc;;;;IAf0CH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAA6C,OAAA,CAAA3C,WAAA,CAAAC,SAAA,kCAA0D;IAI/GR,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAkC,UAAA,YAAAgB,OAAA,CAAAC,iCAAA,CAA6C,UAAAD,OAAA,CAAAF,WAAA,CAAAC,gBAAA,iBAAAC,OAAA,CAAA3C,WAAA,CAAAC,SAAA,wDAAA0C,OAAA,CAAAF,WAAA,CAAAI,OAAA,WAAAF,OAAA,CAAAG,0BAAA,GAAAH,OAAA,CAAAG,0BAAA,cAAAH,OAAA,CAAAI,mBAAA,CAAAC,IAAA,CAAAL,OAAA;;;;;IAkBjDlD,EAAA,CAAAC,cAAA,gBAAgI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAmD,OAAA,CAAAjD,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAH5LR,EAAA,CAAAC,cAAA,cAAwG;IACpGD,EAAA,CAAA+B,SAAA,gBAAwE;IACxE/B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAgC,UAAA,IAAAyB,kDAAA,oBAA4L;IAChMzD,EAAA,CAAAG,YAAA,EAAM;;;;IAD2BH,EAAA,CAAAI,SAAA,GAAiG;IAAjGJ,EAAA,CAAAkC,UAAA,SAAAwB,OAAA,CAAAP,iCAAA,CAAAd,KAAA,IAAAqB,OAAA,CAAAP,iCAAA,CAAAb,KAAA,CAAAC,QAAA,CAAiG;;;;;;IAItIvC,EAAA,CAAAC,cAAA,cACiI;IAChED,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE/HH,EAAA,CAAAC,cAAA,cAAiB;IAITD,EAAA,CAAAwC,UAAA,yBAAAmB,8EAAAjB,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAA8C,aAAA;MAAA,OAAW9C,EAAA,CAAA+C,WAAA,CAAAc,OAAA,CAAAb,WAAA,CAAAc,aAAA,GAAApB,MAAA,CAC1C;IAAA,EADoE,sBAAAqB,2EAAA;MAAA/D,EAAA,CAAA2C,aAAA,CAAAiB,IAAA;MAAA,MAAAI,OAAA,GAAAhE,EAAA,CAAA8C,aAAA;MAAA,OASzB9C,EAAA,CAAA+C,WAAA,CAAAiB,OAAA,CAAAC,eAAA,CAAgB,KAAK,CAAC;IAAA,EATG;IAWxCjE,EAAA,CAAAG,YAAA,EAAc;;;;IAjB0CH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAA6D,OAAA,CAAA3D,WAAA,CAAAC,SAAA,kCAA0D;IAI/GR,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAkC,UAAA,YAAAgC,OAAA,CAAAf,iCAAA,CAA6C,UAAAe,OAAA,CAAAlB,WAAA,CAAAc,aAAA,iBAAAI,OAAA,CAAA3D,WAAA,CAAAC,SAAA,wDAAA0D,OAAA,CAAAb,0BAAA,cAAAa,OAAA,CAAAZ,mBAAA,CAAAC,IAAA,CAAAW,OAAA;;;;;;;;;;;;;;IAuDjElE,EAAA,CAAAC,cAAA,cAO6B;IACzBD,EAAA,CAAA+B,SAAA,wBAAuC;IAC3C/B,EAAA,CAAAG,YAAA,EAAM;;;IAT6EH,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAU,eAAA,IAAAyD,GAAA,EAMxE;;;;;;;;;;;;;;IAhBnBnE,EAAA,CAAAC,cAAA,qBAAyH;IAEKD,EAAA,CAAAwC,UAAA,2BAAA4B,iFAAA1B,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAA8C,aAAA;MAAiBJ,MAAA,CAAA6B,cAAA,EAAuB;MAAA,OAAEvE,EAAA,CAAA+C,WAAA,CAAAuB,OAAA,CAAAE,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,iFAAA/B,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAK,OAAA,GAAA1E,EAAA,CAAA8C,aAAA;MAAA,OAAc9C,EAAA,CAAA+C,WAAA,CAAA2B,OAAA,CAAAC,wBAAA,CAAAC,OAAA,GAAAlC,MAAA,CAAwC;IAAA,EAAtD;IAAvL1C,EAAA,CAAAG,YAAA,EAA6Q;IAC7QH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAAwC,UAAA,mBAAAqC,4EAAA;MAAA7E,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAS,OAAA,GAAA9E,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAA+B,OAAA,CAAAN,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCxE,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAC,cAAA,cAAmF;IAC/ED,EAAA,CAAAgC,UAAA,IAAA+C,uDAAA,kBASM;IACN/E,EAAA,CAAAC,cAAA,cAA2B;IAKnBD,EAAA,CAAAwC,UAAA,+BAAAwC,0FAAAtC,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAY,OAAA,GAAAjF,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAAkC,OAAA,CAAAC,kBAAA,GAAAxC,MAAA;IAAA,EAAoC,+BAAAsC,0FAAAtC,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAc,OAAA,GAAAnF,EAAA,CAAA8C,aAAA;MAAA,OASf9C,EAAA,CAAA+C,WAAA,CAAAoC,OAAA,CAAAC,6BAAA,CAAA1C,MAAA,CAAqC;IAAA,EATtB,4CAAA2C,uGAAA;MAAArF,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAiB,OAAA,GAAAtF,EAAA,CAAA8C,aAAA;MAAA,OAUF9C,EAAA,CAAA+C,WAAA,CAAAuC,OAAA,CAAAC,8BAAA,EAAgC;IAAA,EAV9B,mCAAAC,8FAAA9C,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA0B,IAAA;MAAA,MAAAoB,OAAA,GAAAzF,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAA0C,OAAA,CAAAC,uBAAA,GAAAhD,MAAA;IAAA;IAavC1C,EAAA,CAAAG,YAAA,EAAa;;;;IAtCdH,EAAA,CAAA2F,sBAAA,eAAAC,OAAA,CAAArF,WAAA,CAAAC,SAAA,kCAA+D;IAEZR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAkC,UAAA,gBAAA0D,OAAA,CAAArF,WAAA,CAAAC,SAAA,0BAA8D,YAAAoF,OAAA,CAAAjB,wBAAA,CAAAC,OAAA,oBAAA5E,EAAA,CAAAU,eAAA,KAAAmF,GAAA;IAe/G7F,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAkC,UAAA,SAAA0D,OAAA,CAAAE,eAAA,CAAqB;IAKnB9F,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAkC,UAAA,iBAAgB,eAAA0D,OAAA,CAAAG,kBAAA,CAAAC,IAAA,cAAAJ,OAAA,CAAAG,kBAAA,CAAAE,IAAA,iBAAAL,OAAA,CAAAV,kBAAA,aAAAU,OAAA,CAAAM,kBAAA,aAAAN,OAAA,CAAAO,eAAA,aAAAP,OAAA,CAAAQ,mBAAA,cAAAR,OAAA,CAAAS,cAAA,CAAA9C,IAAA,CAAAqC,OAAA,yBAAA5F,EAAA,CAAAU,eAAA,KAAA4F,GAAA,oCAAAV,OAAA,CAAAG,kBAAA,CAAAQ,MAAA,YAAAX,OAAA,CAAAjB,wBAAA,qBAAAiB,OAAA,CAAAF,uBAAA;;;;;IA8BxB1F,EAAA,CAAAC,cAAA,cAO6B;IACzBD,EAAA,CAAA+B,SAAA,wBAAuC;IAC3C/B,EAAA,CAAAG,YAAA,EAAM;;;IAT6EH,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAU,eAAA,IAAAyD,GAAA,EAMxE;;;;;;IAhBnBnE,EAAA,CAAAC,cAAA,qBAA+K;IAEjDD,EAAA,CAAAwC,UAAA,2BAAAgE,iFAAA9D,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAA8C,aAAA;MAAiBJ,MAAA,CAAA6B,cAAA,EAAuB;MAAA,OAAEvE,EAAA,CAAA+C,WAAA,CAAA2D,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,iFAAAlE,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAI,OAAA,GAAA7G,EAAA,CAAA8C,aAAA;MAAA,OAAc9C,EAAA,CAAA+C,WAAA,CAAA8D,OAAA,CAAAC,wBAAA,CAAAlC,OAAA,GAAAlC,MAAA,CAAwC;IAAA,EAAtD;IAAvL1C,EAAA,CAAAG,YAAA,EAA6Q;IAC7QH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAAwC,UAAA,mBAAAuE,4EAAA;MAAA/G,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAO,OAAA,GAAAhH,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAiE,OAAA,CAAAL,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzC3G,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAC,cAAA,cAAmF;IAC/ED,EAAA,CAAAgC,UAAA,IAAAiF,uDAAA,kBASM;IACPjH,EAAA,CAAAC,cAAA,cAA2B;IAKlBD,EAAA,CAAAwC,UAAA,+BAAA0E,0FAAAxE,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAU,OAAA,GAAAnH,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAAoE,OAAA,CAAAC,kBAAA,GAAA1E,MAAA;IAAA,EAAoC,+BAAAwE,0FAAAxE,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAY,OAAA,GAAArH,EAAA,CAAA8C,aAAA;MAAA,OASf9C,EAAA,CAAA+C,WAAA,CAAAsE,OAAA,CAAAC,6BAAA,CAAA5E,MAAA,CAAqC;IAAA,EATtB,4CAAA6E,uGAAA;MAAAvH,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAe,OAAA,GAAAxH,EAAA,CAAA8C,aAAA;MAAA,OAUF9C,EAAA,CAAA+C,WAAA,CAAAyE,OAAA,CAAAC,8BAAA,EAAgC;IAAA,EAV9B,mCAAAC,8FAAAhF,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAA8D,IAAA;MAAA,MAAAkB,OAAA,GAAA3H,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAA4E,OAAA,CAAAC,uBAAA,GAAAlF,MAAA;IAAA;IAavC1C,EAAA,CAAAG,YAAA,EAAa;;;;IAtCdH,EAAA,CAAA6H,qBAAA,WAAAC,OAAA,CAAAvH,WAAA,CAAAC,SAAA,6BAA8D;IAEXR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAkC,UAAA,gBAAA4F,OAAA,CAAAvH,WAAA,CAAAC,SAAA,0BAA8D,YAAAsH,OAAA,CAAAhB,wBAAA,CAAAlC,OAAA,oBAAA5E,EAAA,CAAAU,eAAA,KAAAmF,GAAA;IAe/G7F,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAkC,UAAA,SAAA4F,OAAA,CAAAC,eAAA,CAAqB;IAKnB/H,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAkC,UAAA,iBAAgB,eAAA4F,OAAA,CAAAE,kBAAA,CAAAhC,IAAA,cAAA8B,OAAA,CAAAE,kBAAA,CAAA/B,IAAA,iBAAA6B,OAAA,CAAAV,kBAAA,aAAAU,OAAA,CAAAG,kBAAA,aAAAH,OAAA,CAAAI,eAAA,aAAAJ,OAAA,CAAAK,mBAAA,cAAAL,OAAA,CAAAM,cAAA,CAAA7E,IAAA,CAAAuE,OAAA,yBAAA9H,EAAA,CAAAU,eAAA,KAAA4F,GAAA,oCAAAwB,OAAA,CAAAE,kBAAA,CAAAzB,MAAA,YAAAuB,OAAA,CAAAhB,wBAAA,qBAAAgB,OAAA,CAAAF,uBAAA;;;;;;IA6DJ5H,EAAA,CAAAC,cAAA,gBAAkJ;IAAjCD,EAAA,CAAAwC,UAAA,mBAAA6F,kFAAA;MAAArI,EAAA,CAAA2C,aAAA,CAAA2F,IAAA;MAAA,MAAAC,OAAA,GAAAvI,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAAwF,OAAA,CAAAC,eAAA,GAA2B,IAAI;IAAA,EAAC;IAACxI,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAC1JH,EAAA,CAAAC,cAAA,gBAAwJ;IAAlCD,EAAA,CAAAwC,UAAA,mBAAAiG,kFAAA;MAAAzI,EAAA,CAAA2C,aAAA,CAAA+F,IAAA;MAAA,MAAAC,OAAA,GAAA3I,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAA4F,OAAA,CAAAH,eAAA,GAA2B,KAAK;IAAA,EAAC;IAACxI,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAWpLH,EAAA,CAAAC,cAAA,kBAAuE;IAK/CD,EAAA,CAAAwC,UAAA,2BAAAoG,iGAAAlG,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAA9I,EAAA,CAAA8C,aAAA;MAAA,OAAa9C,EAAA,CAAA+C,WAAA,CAAA+F,OAAA,CAAAC,oBAAA,CAAAC,MAAA,GAAAtG,MAAA,CAChD;IAAA,EAD4E;IAQpD1C,EAAA,CAAAG,YAAA,EAAa;IAElBH,EAAA,CAAAC,cAAA,cAAmB;IACRD,EAAA,CAAAwC,UAAA,2BAAAyG,4FAAAvG,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAkG,IAAA;MAAA,MAAAK,OAAA,GAAAlJ,EAAA,CAAA8C,aAAA;MAAA,OAAa9C,EAAA,CAAA+C,WAAA,CAAAmG,OAAA,CAAAH,oBAAA,CAAAI,GAAA,GAAAzG,MAAA,CAAgC;IAAA,EAAP;IAA7C1C,EAAA,CAAAG,YAAA,EAAkJ;IAEtJH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAAwC,UAAA,mBAAA4G,uFAAA;MAAApJ,EAAA,CAAA2C,aAAA,CAAAkG,IAAA;MAAA,MAAAQ,OAAA,GAAArJ,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAsG,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCtJ,EAAA,CAAAG,YAAA,EAAW;IAGhBH,EAAA,CAAAC,cAAA,qBAaC;IATOD,EAAA,CAAAwC,UAAA,+BAAA+G,qGAAA7G,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAkG,IAAA;MAAA,MAAAW,OAAA,GAAAxJ,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAAyG,OAAA,CAAAC,kBAAA,GAAA/G,MAAA;IAAA,EAAoC;IAS3C1C,EAAA,CAAAG,YAAA,EAAa;;;;IAtCTH,EAAA,CAAAkC,UAAA,qBAAoB;IAILlC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAkC,UAAA,mBAAkB,YAAAwH,OAAA,CAAAX,oBAAA,CAAAC,MAAA,oBAAAhJ,EAAA,CAAAU,eAAA,KAAAmF,GAAA,cAAA6D,OAAA,CAAAC,UAAA,wBAAAD,OAAA,CAAAnJ,WAAA,CAAAC,SAAA,uCAAAkJ,OAAA,CAAAnJ,WAAA,CAAAC,SAAA;IAYvBR,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkC,UAAA,YAAAwH,OAAA,CAAAX,oBAAA,CAAAI,GAAA,CAAsC,mBAAAnJ,EAAA,CAAAU,eAAA,KAAAmF,GAAA;IAU7C7F,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAkC,UAAA,iBAAgB,eAAAwH,OAAA,CAAAE,kBAAA,CAAA5D,IAAA,cAAA0D,OAAA,CAAAE,kBAAA,CAAA3D,IAAA,iBAAAyD,OAAA,CAAAD,kBAAA,aAAAC,OAAA,CAAAG,kBAAA,aAAAH,OAAA,CAAAI,eAAA,aAAAJ,OAAA,CAAAK,mBAAA,cAAAL,OAAA,CAAAM,cAAA,CAAAzG,IAAA,CAAAmG,OAAA,yBAAA1J,EAAA,CAAAU,eAAA,KAAA4F,GAAA,oCAAAoD,OAAA,CAAAE,kBAAA,CAAArD,MAAA,YAAAmD,OAAA,CAAAX,oBAAA;;;;;;IA9EpC/I,EAAA,CAAAC,cAAA,qBAAuO;IAQ/MD,EAAA,CAAAwC,UAAA,2BAAAyH,yFAAAvH,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAuH,IAAA;MAAA,MAAAC,OAAA,GAAAnK,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAAoH,OAAA,CAAAC,cAAA,GAAA1H,MAAA;IAAA,EAA4B;IAIpC1C,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,wBAMC;IAFOD,EAAA,CAAAwC,UAAA,2BAAA6H,yFAAA3H,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAuH,IAAA;MAAA,MAAAI,OAAA,GAAAtK,EAAA,CAAA8C,aAAA;MAAA,OAAA9C,EAAA,CAAA+C,WAAA,CAAAuH,OAAA,CAAAF,cAAA,GAAA1H,MAAA;IAAA,EAA4B;IAGpC1C,EAAA,CAAAG,YAAA,EAAgB;IAEpBH,EAAA,CAAAC,cAAA,cAA2C;IAGcD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,iBAAyI;IAAlID,EAAA,CAAAwC,UAAA,2BAAA+H,kFAAA7H,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAuH,IAAA;MAAA,MAAAM,OAAA,GAAAxK,EAAA,CAAA8C,aAAA;MAAA,OAAa9C,EAAA,CAAA+C,WAAA,CAAAyH,OAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAhI,MAAA,CAA4B;IAAA,EAAP;IAAzC1C,EAAA,CAAAG,YAAA,EAAyI;IAGjJH,EAAA,CAAAC,cAAA,eAAmB;IAEkCD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,eAA4C;IAEjCD,EAAA,CAAAwC,UAAA,2BAAAmI,kFAAAjI,MAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAuH,IAAA;MAAA,MAAAU,OAAA,GAAA5K,EAAA,CAAA8C,aAAA;MAAA,OAAa9C,EAAA,CAAA+C,WAAA,CAAA6H,OAAA,CAAAH,WAAA,CAAAI,SAAA,GAAAnI,MAAA,CACnD;IAAA,EADyE;IAD1C1C,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAgC,UAAA,KAAA8I,0DAAA,oBAA0J;IAC1J9K,EAAA,CAAAgC,UAAA,KAAA+I,0DAAA,oBAAgK;IACpK/K,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAmB;IACLD,EAAA,CAAAwC,UAAA,mBAAAwI,6EAAA;MAAAhL,EAAA,CAAA2C,aAAA,CAAAuH,IAAA;MAAA,MAAAe,OAAA,GAAAjL,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAkI,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAwFlL,EAAA,CAAAG,YAAA,EAAW;IAKjJH,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAgC,UAAA,KAAAmJ,4DAAA,uBAuCU;IACdnL,EAAA,CAAAG,YAAA,EAAM;;;;IA5FEH,EAAA,CAAA6H,qBAAA,WAAAuD,OAAA,CAAA7K,WAAA,CAAAC,SAAA,0BAA2D;IAA4IR,EAAA,CAAAkC,UAAA,oBAAmB;IAErNlC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAkC,UAAA,qBAAoB;IAGblC,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAkC,UAAA,UAAAkJ,OAAA,CAAA7K,WAAA,CAAAC,SAAA,yBAAuD,YAAA4K,OAAA,CAAAhB,cAAA,oBAAApK,EAAA,CAAAU,eAAA,KAAAmF,GAAA;IASvD7F,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAkC,UAAA,UAAAkJ,OAAA,CAAA7K,WAAA,CAAAC,SAAA,4BAA0D,YAAA4K,OAAA,CAAAhB,cAAA,oBAAApK,EAAA,CAAAU,eAAA,KAAAmF,GAAA;IAYnD7F,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAkC,UAAA,YAAAkJ,OAAA,CAAAX,WAAA,CAAAC,QAAA,CAAkC,qCAAA1K,EAAA,CAAAU,eAAA,KAAAmF,GAAA;IAQ9B7F,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAkC,UAAA,YAAAkJ,OAAA,CAAAX,WAAA,CAAAI,SAAA,CAAmC,mBAAA7K,EAAA,CAAAU,eAAA,KAAAmF,GAAA,WAAAuF,OAAA,CAAA5C,eAAA;IAMKxI,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAkC,UAAA,SAAAkJ,OAAA,CAAA5C,eAAA,UAA8B;IAC9BxI,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAkC,UAAA,SAAAkJ,OAAA,CAAA5C,eAAA,SAA6B;IAKrDxI,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAkC,UAAA,UAAAkJ,OAAA,CAAA7K,WAAA,CAAAC,SAAA,qBAAmD;IAMpDR,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAkC,UAAA,SAAAkJ,OAAA,CAAAX,WAAA,CAAAI,SAAA,CAA2B;;;;;;ADncrF,OAAM,MAAOQ,yBAA0B,SAAQxL,aAAa;EACxDyL,YAAmBC,cAA8B,EAC9BC,eAAgC,EAChCC,eAAgC,EAC/BC,WAAwB,EACxBC,GAAsB,EAC9BC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IANA,KAAAL,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,GAAG,GAAHA,GAAG;IA4BvB,KAAAE,iBAAiB,GAAY,KAAK;IAClC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,WAAW,GAAkB,IAAI;IACjC,KAAAC,2BAA2B,GAA2B;MAACC,YAAY,EAAE;IAAE,CAAC;IACxE,KAAAC,kBAAkB,GAAyC;MAACC,IAAI,EAAE,CAAC;MAAEF,YAAY,EAAE;IAAE,CAAC;IACtF,KAAA7I,0BAA0B,GAA+C;MAAC6I,YAAY,EAAE,EAAE;MAAEG,SAAS,EAAE,CAAC;IAAC,CAAC;IAC1G,KAAAC,eAAe,GAA+C;MAACC,iBAAiB,EAAE,CAAC,CAAC;MAAEH,IAAI,EAAE,CAAC;IAAC,CAAC;IAC/F,KAAAI,kBAAkB,GAAqB,IAAIzM,gBAAgB,EAAE;IAC7D,KAAAqC,yBAAyB,GAAsB,IAAIrC,gBAAgB,EAAE;IACrE,KAAAoD,iCAAiC,GAAsB,IAAIpD,gBAAgB,EAAE;IAC7E,KAAA0M,+BAA+B,GAAsB,IAAI1M,gBAAgB,EAAE;IAQ3E,KAAA2F,uBAAuB,GAAG,KAAK;IAC/B,KAAAkC,uBAAuB,GAAG,KAAK;IAC/B,KAAA9B,eAAe,GAAY,KAAK;IAChC,KAAAiC,eAAe,GAAY,KAAK;IAoChC,KAAAS,eAAe,GAAG,IAAI;IACtB,KAAAmB,UAAU,GAAG,EAAE;IACf;IACA,KAAAF,kBAAkB,GAAe,EAAE;IAcnC,KAAAV,oBAAoB,GAAG;MAACI,GAAG,EAAG,IAAI;MAAEH,MAAM,EAAG;IAAI,CAAC;IAElD,KAAAyB,WAAW,GAAG;MAACC,QAAQ,EAAE,IAAI;MAAEG,SAAS,EAAE;IAAI,CAAC;IAE/C,KAAAT,cAAc,GAAS,CAAC,CAAC,CAAC;IAE1B,KAAAsC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACD,QAAQ;IAEvC,KAAAE,oBAAoB,GAAS,EAAE;IA4iBZ,KAAA9M,SAAS,GAAGA,SAAS;IACrB,KAAA+M,OAAO,GAAGA,OAAO;EAzpBpC;EA8GAC,QAAQA,CAAA;IACJ,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAACjN,SAAS,CAACkN,WAAW,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;MAACC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;;IAChG,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACX,cAAc,CAACD,QAAQ,CAACN,IAAI;IACjD,IAAI,CAACmB,SAAS,GAAG,IAAI,CAACZ,cAAc,CAACD,QAAQ,CAACc,EAAE;IAChD,IAAI,CAACC,cAAc,GAAG3N,SAAS,CAAC4N,SAAS;IACzC,IAAI,CAACC,KAAK,GAAG,CACT;MAAEC,KAAK,EAAE,IAAI,CAACrN,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,EAChE;MAAEoN,KAAK,EAAE,IAAI,CAACrN,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAAEqN,UAAU,EAAC;IAAW,CAAE,EACxF;MAAED,KAAK,EAAE,IAAI,CAACrN,WAAW,CAACC,SAAS,CAAC,sBAAsB;IAAC,CAAE,CAChE;IACD,IAAI,CAACsN,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAIG,eAAe,GAAG,CAClB;MAACC,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAAC0N,KAAK,EAACpO,SAAS,CAAC4N,SAAS,CAACS,KAAK;MAAEC,OAAO,EAAC,CAACtO,SAAS,CAAC4N,SAAS,CAACS,KAAK;IAAC,CAAC;IACjI;IACA;MAACF,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0N,KAAK,EAACpO,SAAS,CAAC4N,SAAS,CAACW,QAAQ;MAACD,OAAO,EAAC,CAACtO,SAAS,CAAC4N,SAAS,CAACS,KAAK,EAACrO,SAAS,CAAC4N,SAAS,CAACY,QAAQ,EAACxO,SAAS,CAAC4N,SAAS,CAACa,QAAQ,EAAEzO,SAAS,CAAC4N,SAAS,CAACW,QAAQ;IAAC,CAAC,EAC9N;MAACJ,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0N,KAAK,EAACpO,SAAS,CAAC4N,SAAS,CAACY,QAAQ;MAACF,OAAO,EAAC,CAACtO,SAAS,CAAC4N,SAAS,CAACS,KAAK;IAAC,CAAC,EACtI;MAACF,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0N,KAAK,EAACpO,SAAS,CAAC4N,SAAS,CAACa,QAAQ;MAACH,OAAO,EAAC,CAACtO,SAAS,CAAC4N,SAAS,CAACS,KAAK,EAACrO,SAAS,CAAC4N,SAAS,CAACY,QAAQ;IAAC;IAClK;IAAA,CACH;;IACD,IAAI,CAACE,cAAc,GAAGR,eAAe,CAACS,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACN,OAAO,CAACO,QAAQ,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC;IACtF,IAAI,CAACtK,WAAW,GAAG;MACf4L,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXzB,QAAQ,EAAE,IAAI,CAACkB,cAAc,CAAC,CAAC,CAAC,CAACN,KAAK;MACtCc,QAAQ,EAAE,IAAI,CAACrC,cAAc,CAACD,QAAQ,CAACR,YAAY;MACnD+C,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjB9L,OAAO,EAAE,IAAI;MACb+L,SAAS,EAAE,IAAI;MACflM,gBAAgB,EAAG,IAAI;MACvBa,aAAa,EAAE;KAClB;IACD,IAAI,CAACa,wBAAwB,GAAG;MAC5BC,OAAO,EAAE,IAAI;MACbd,aAAa,EAAE,IAAI;MACnBoI,YAAY,EAAE,IAAI,CAAClJ,WAAW,CAACgM,QAAQ;MACvC3C,SAAS,EAAE,CAAC;KACf;IACD,IAAI,IAAI,CAACiB,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,EAAE;MAC/C,IAAI,CAAC1J,wBAAwB,CAACb,aAAa,GAAG,IAAI,CAACyJ,SAAS;;IAEhE,IAAI,CAACrH,kBAAkB,GAAG,CACtB;MACI+H,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4O,GAAG,EAAE,MAAM;MACXnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACItB,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4O,GAAG,EAAE,MAAM;MACXnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACpJ,eAAe,GAAG;MACnBqJ,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC1J,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRM,MAAM,EAAE;KACX;IACD,IAAI,CAACqD,kBAAkB,GAAG;MACtB5D,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRM,MAAM,EAAE;KACX;IACD,IAAI,CAACH,mBAAmB,GAAG;MACvBsJ,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAAC3K,kBAAkB,GAAG,EAAE;IAE5B,IAAI,CAAC4B,wBAAwB,GAAG;MAC5BlC,OAAO,EAAE,IAAI;MACbkL,WAAW,EAAE;KAChB;IACD,IAAI,CAAC7H,kBAAkB,GAAG,CACtB;MACIgG,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4O,GAAG,EAAE,cAAc;MACnBnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACItB,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4O,GAAG,EAAE,cAAc;MACnBnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACItB,IAAI,EAAE,IAAI,CAAC1N,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4O,GAAG,EAAE,cAAc;MACnBnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAED,IAAI,CAAC1F,kBAAkB,GAAG,CACtB;MACIoE,IAAI,EAAE,KAAK;MACXmB,GAAG,EAAE,MAAM;MACXnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACItB,IAAI,EAAE,QAAQ;MACdmB,GAAG,EAAE,QAAQ;MACbnJ,IAAI,EAAE,KAAK;MACXoJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACrH,eAAe,GAAG;MACnBsH,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACzH,kBAAkB,GAAG;MACtBhC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRM,MAAM,EAAE;KACX;IACD,IAAI,CAACuD,eAAe,GAAG;MACnB0F,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACtH,mBAAmB,GAAG;MACvBuH,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAAC9F,mBAAmB,GAAG;MACvB2F,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACzI,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC2I,mBAAmB,GAAG,IAAIC,GAAG,EAAU;IAC5C,IAAI,CAACC,WAAW,GAAG,IAAI,CAACvE,WAAW,CAACwE,KAAK,CAAC,IAAI,CAAClN,WAAW,CAAC;IAC3D,IAAI,CAACmN,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAIC,EAAE,GAAG,IAAI;EACjB;EAEAF,oBAAoBA,CAAA;IAChB,IAAIE,EAAE,GAAG,IAAI;IACb,IAAI,CAAC9E,cAAc,CAAC+E,WAAW,CAAGC,QAAQ,IAAG;MACzCF,EAAE,CAACzD,oBAAoB,GAAG2D,QAAQ;IACtC,CAAC,CAAC;EACN;EAEAC,qBAAqBA,CAAA;IACjB,IAAG,IAAI,CAACxN,WAAW,CAACsK,QAAQ,IAAI,IAAI,CAACtB,WAAW,EAAC;MAC7C,IAAI,CAACA,WAAW,GAAG,IAAI,CAAChJ,WAAW,CAACsK,QAAQ;MAC5C;MACA,IAAI,CAAC2C,WAAW,CAACQ,GAAG,CAAC,WAAW,CAAC,CAACC,KAAK,EAAE;;EAEjD;EAEAC,iBAAiBA,CAACvE,IAAI;IAClB,IAAI0C,KAAK,GAAG,IAAI;IAChB,IAAI8B,QAAQ,GAAG,IAAI;IACnB,IAAGxE,IAAI,IAAI,aAAa,EAAC;MACrBwE,QAAQ,GAAG,IAAI,CAAC5N,WAAW,CAAC4L,WAAW;KAC1C,MAAK,IAAGxC,IAAI,IAAI,OAAO,EAAC;MACrB0C,KAAK,GAAG,IAAI,CAAC9L,WAAW,CAAC8L,KAAK;;IAGlC,IAAIuB,EAAE,GAAG,IAAI;IAEb,IAAI,CAACQ,eAAe,CAACC,GAAG,CAAC1E,IAAI,EAAE,IAAI,CAACb,cAAc,CAACwF,YAAY,CAACxN,IAAI,CAAC,IAAI,CAACgI,cAAc,CAAC,EAAEuD,KAAK,EAAE8B,QAAQ,EAAEL,QAAQ,IAAG;MACnH,IAAGA,QAAQ,IAAI,CAAC,EAAC;QACb,IAAGnE,IAAI,IAAI,aAAa,EAAC;UACrBiE,EAAE,CAACxE,iBAAiB,GAAG,IAAI;SAC9B,MAAI;UACDwE,EAAE,CAACvE,cAAc,GAAG,IAAI;;OAE/B,MAAI;QACD,IAAGM,IAAI,IAAI,aAAa,EAAC;UACrBiE,EAAE,CAACxE,iBAAiB,GAAG,KAAK;SAC/B,MAAI;UACDwE,EAAE,CAACvE,cAAc,GAAG,KAAK;;;IAGrC,CAAC,CAAC;EACN;EAEAkF,cAAcA,CAAA;IACV,IAAIX,EAAE,GAAG,IAAI;IACb,IAAGA,EAAE,CAACrN,WAAW,CAACsK,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,IAAIgC,EAAE,CAACnL,kBAAkB,CAAC+L,MAAM,IAAI,CAAC,EAAE;MAC7FZ,EAAE,CAACa,oBAAoB,CAACC,OAAO,CAACd,EAAE,CAAC9P,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC,CAAC;MAC7F;;IAEJ,IAAI,CAAC0Q,oBAAoB,CAACE,MAAM,EAAE;IAClCC,UAAU,CAAC;MACPhB,EAAE,CAACiB,YAAY,EAAE;IACrB,CAAC,CAAC;EACN;EAEAA,YAAYA,CAAA;IACR,IAAG,IAAI,CAACtO,WAAW,CAACsK,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,EAAE;MAC1D,IAAI,CAACjE,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACX,kBAAkB,GAAG,EAAE;;IAEhC,IAAI8H,QAAQ,GAAG;MACXX,QAAQ,EAAE,IAAI,CAAC5N,WAAW,CAAC4L,WAAW;MACtCC,QAAQ,EAAE,IAAI,CAAC7L,WAAW,CAAC6L,QAAQ;MACnCK,WAAW,EAAE,IAAI,CAAClM,WAAW,CAACkM,WAAW;MACzCJ,KAAK,EAAE,IAAI,CAAC9L,WAAW,CAAC8L,KAAK;MAC7BC,KAAK,EAAE,IAAI,CAAC/L,WAAW,CAAC+L,KAAK;MAC7B3C,IAAI,EAAE,IAAI,CAACpJ,WAAW,CAACsK,QAAQ;MAC/BpB,YAAY,EAAE,IAAI,CAAClJ,WAAW,CAACgM,QAAQ;MACvCwC,OAAO,EAAE,CAAC,IAAI,CAACxO,WAAW,CAACiM,KAAK,IAAI,EAAE,EAAEwC,GAAG,CAAC/C,EAAE,IAAIA,EAAE,CAAClB,EAAE,CAAC;MACxDkE,aAAa,EAAE,CAAC,IAAI,CAAC1O,WAAW,CAACmM,SAAS,IAAI,EAAE,EAAEsC,GAAG,CAACE,QAAQ,IAAIA,QAAQ,CAACnE,EAAE,CAAC;MAC9EoE,SAAS,EAAG,IAAI,CAAC5O,WAAW,CAACI,OAAO,IAAI,IAAK;MAC7CyO,gBAAgB,EAAG,IAAI,CAAC7O,WAAW,CAACC,gBAAgB,IAAI,EAAG;MAC3D6O,aAAa,EAAE,CAAC,IAAI,CAAC1K,kBAAkB,IAAI,EAAE,EAAEqK,GAAG,CAACM,QAAQ,IAAIA,QAAQ,CAACvE,EAAE,CAAC;MAC3E1J,aAAa,EAAE,IAAI,CAACd,WAAW,CAACc,aAAa;MAC7CkO,SAAS,EAAE,IAAI,CAAC5H,cAAc;MAC9B6H,SAAS,EAAE,CAAC,IAAI,CAACxI,kBAAkB,IAAI,EAAE,EAAEgI,GAAG,CAAC/C,EAAE,IAAEA,EAAE,CAAClB,EAAE;KAC3D;IACD,IAAG+D,QAAQ,CAACxC,KAAK,IAAI,IAAI,EAAC;MACtB,IAAGwC,QAAQ,CAACxC,KAAK,CAACmD,UAAU,CAAC,GAAG,CAAC,EAAC;QAC9BX,QAAQ,CAACxC,KAAK,GAAG,IAAI,GAACwC,QAAQ,CAACxC,KAAK,CAACoD,SAAS,CAAC,CAAC,EAAEZ,QAAQ,CAACxC,KAAK,CAACkC,MAAM,CAAC;OAC3E,MAAK,IAAGM,QAAQ,CAACxC,KAAK,CAACkC,MAAM,IAAI,CAAC,IAAIM,QAAQ,CAACxC,KAAK,CAACkC,MAAM,IAAI,EAAE,EAAC;QAC/DM,QAAQ,CAACxC,KAAK,GAAG,IAAI,GAACwC,QAAQ,CAACxC,KAAK;;;IAI5C,IAAIsB,EAAE,GAAG,IAAI;IACb,IAAI,CAAC9E,cAAc,CAAC6G,aAAa,CAACb,QAAQ,EAAGhB,QAAQ,IAAG;MACpD,IAAI8B,OAAO,GAAG;QACV3H,QAAQ,EAAE6F,QAAQ,CAACK,QAAQ;QAC3B0B,QAAQ,EAAG,IAAI,CAAC7H,WAAW,CAACI,SAAS;QACrCoD,IAAI,EAAGsC,QAAQ,CAACK,QAAQ;QACxB2B,MAAM,EAAG,IAAI,CAACnI,cAAc;QAC5BoI,MAAM,EAAGjC,QAAQ,CAAC/C;OACrB;MACD,IAAG,IAAI,CAACpD,cAAc,EAAE;QACpB,IAAI,CAACmB,cAAc,CAACkH,kBAAkB,CAACJ,OAAO,EAAC,MAAI;UAC/ChC,EAAE,CAACa,oBAAoB,CAACwB,OAAO,CAACrC,EAAE,CAAC9P,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;UACvF6P,EAAE,CAACsC,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC,EAAE,IAAI,EAAE,MAAI;UACTvC,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;QACrC,CAAC,CAAC;OACL,MAAK;QACFxC,EAAE,CAACa,oBAAoB,CAACwB,OAAO,CAACrC,EAAE,CAAC9P,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF6P,EAAE,CAACsC,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;IAE1C,CAAC,EAAE,IAAI,EAAE,MAAI;MACTvC,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,SAASA,CAAA;IACL,IAAI,CAACH,MAAM,CAACC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACvC;EAEAG,WAAWA,CAACC,OAAO;IACf,IAAGA,OAAO,EAAC;MACP,IAAI,CAAChQ,WAAW,CAACiM,KAAK,GAAG,IAAI;;IAEjC,IAAI7C,IAAI,GAAG,CAAC,CAAC;IACb,IAAG,IAAI,CAACpJ,WAAW,CAACsK,QAAQ,IAAI,IAAI,EAAC;MACjClB,IAAI,GAAG,IAAI,CAACpJ,WAAW,CAACsK,QAAQ;;IAEpC,IAAI,CAAC/B,cAAc,CAACwH,WAAW,CAAC;MAAC3G,IAAI,EAAEA,IAAI;MAAEtI,aAAa,EAAE,IAAI,CAACd,WAAW,CAACc,aAAa,GAAG,IAAI,CAACd,WAAW,CAACc,aAAa,GAAG,CAAC;IAAC,CAAC,EAAGyM,QAAQ,IAAG;MAC3I,IAAI,CAAC0C,QAAQ,GAAG1C,QAAQ,CAACkB,GAAG,CAAC/C,EAAE,IAAG;QAC9B,OAAO;UACHlB,EAAE,EAAEkB,EAAE,CAAClB,EAAE;UACTS,IAAI,EAAES,EAAE,CAACT;SACZ;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAhK,eAAeA,CAAC+O,OAAO,EAAE/E,IAAA,GAAY,EAAE;IACnC,IAAIoC,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACrN,WAAW,CAACsK,QAAQ,IAAI,IAAI,CAACG,cAAc,CAACY,QAAQ,EAAE;MAC1DgC,EAAE,CAACtK,kBAAkB,CAACC,IAAI,GAAG,CAAC;MAC9BqK,EAAE,CAACrI,kBAAkB,CAAChC,IAAI,GAAG,CAAC;MAC9B,IAAGgN,OAAO,EAAC;QACP,IAAI,CAAChQ,WAAW,CAACmM,SAAS,GAAG,EAAE;QAC/B,IAAI,CAACnM,WAAW,CAACI,OAAO,GAAG,IAAI;QAC/B,IAAI,CAACJ,WAAW,CAACc,aAAa,GAAG,IAAI;;MAEzC,IAAI,CAACoB,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACkC,kBAAkB,GAAG,EAAE;MAC5B,IAAG,IAAI,CAACpE,WAAW,CAACgM,QAAQ,IAAI,IAAI,EAAC;QACjC,IAAI,CAAC/C,2BAA2B,GAAG;UAACC,YAAY,EAAE,IAAI,CAAClJ,WAAW,CAACgM;QAAQ,CAAC;QAC5E,IAAI,CAAC7C,kBAAkB,GAAG;UAACC,IAAI,EAAE,CAAC;UAAEF,YAAY,EAAE,IAAI,CAAClJ,WAAW,CAACgM;QAAQ,CAAC;QAC5EqB,EAAE,CAAChN,0BAA0B,CAAC6I,YAAY,GAAGmE,EAAE,CAACrN,WAAW,CAACgM,QAAQ;;MAExE;MACA,IAAIqB,EAAE,CAAC/C,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACa,QAAQ,EAAC;QAC5C8B,EAAE,CAACrN,WAAW,CAACI,OAAO,GAAGiN,EAAE,CAAC9C,SAAS;;MAEzC,IAAI8C,EAAE,CAACrN,WAAW,CAACI,OAAO,EAAE;QACxBiN,EAAE,CAAChN,0BAA0B,CAACgJ,SAAS,GAAGgE,EAAE,CAACrN,WAAW,CAACI,OAAO;OACnE,MAAM;QACHiN,EAAE,CAAChN,0BAA0B,CAACgJ,SAAS,GAAG,CAAC,CAAC;;MAEhDgE,EAAE,CAAC0C,WAAW,CAAC,IAAI,CAAC;MACpB;MACA,IAAG,IAAI,CAACtI,WAAW,CAACI,SAAS,IAAI,IAAI,EAAE;QACnCwF,EAAE,CAACrG,cAAc,CAACqG,EAAE,CAACzG,kBAAkB,CAAC5D,IAAI,EAAEqK,EAAE,CAACzG,kBAAkB,CAAC3D,IAAI,EAAEoK,EAAE,CAACzG,kBAAkB,CAACrD,MAAM,EAAE8J,EAAE,CAACtH,oBAAoB,CAAC;QAChI,IAAI,CAACU,kBAAkB,GAAG,EAAE;;;IAGpC,IAAG,IAAI,CAACzG,WAAW,CAACsK,QAAQ,IAAI,IAAI,CAACG,cAAc,CAACc,QAAQ,EAAE;MAC1D,IAAGyE,OAAO,EAAC;QACP,IAAI,CAAChQ,WAAW,CAACC,gBAAgB,GAAG,IAAI;;MAE5C,IAAG,IAAI,CAACD,WAAW,CAACgM,QAAQ,IAAI,IAAI,EAAC;QACjCqB,EAAE,CAAChN,0BAA0B,CAAC6I,YAAY,GAAGmE,EAAE,CAACrN,WAAW,CAACgM,QAAQ;;MAExE,IAAIqB,EAAE,CAACrN,WAAW,CAACI,OAAO,EAAE;QACxBiN,EAAE,CAAChN,0BAA0B,CAACgJ,SAAS,GAAGgE,EAAE,CAACrN,WAAW,CAACI,OAAO;OACnE,MAAM;QACHiN,EAAE,CAAChN,0BAA0B,CAACgJ,SAAS,GAAG,CAAC,CAAC;;;EAGxD;EACA6G,cAAcA,CAAA;IACV,IAAI7C,EAAE,GAAG,IAAI;IACb,IAAI,CAACrN,WAAW,CAACmM,SAAS,GAAG,EAAE;IAC/B,IAAI,CAACjK,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACkC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACpE,WAAW,CAACc,aAAa,GAAG,IAAI;IAErC,IAAIuM,EAAE,CAACrN,WAAW,CAACI,OAAO,EAAE;MACxBiN,EAAE,CAAChN,0BAA0B,CAACgJ,SAAS,GAAGgE,EAAE,CAACrN,WAAW,CAACI,OAAO;MAChEiN,EAAE,CAAC1L,wBAAwB,CAAC0H,SAAS,GAAGgE,EAAE,CAACrN,WAAW,CAACI,OAAO;KACjE,MAAM;MACHiN,EAAE,CAAChN,0BAA0B,CAACgJ,SAAS,GAAG,CAAC,CAAC;MAC5CgE,EAAE,CAAC1L,wBAAwB,CAAC0H,SAAS,GAAGgE,EAAE,CAACrN,WAAW,CAACI,OAAO;;EAEtE;EAEA+P,oBAAoBA,CAACC,KAAK;IACtB,IAAI,CAACvC,eAAe,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC7M,eAAe,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE6P,KAAK,CAAC3E,MAAM,CAAC;EACpG;EAEA2B,eAAeA,CAAA;IACX,IAAI,CAAC7E,cAAc,CAAC6E,eAAe,CAAEG,QAAQ,IAAG;MAC5C,IAAI,CAAC8C,YAAY,GAAG9C,QAAQ,CAACkB,GAAG,CAAC/C,EAAE,IAAG;QAClC,OAAO;UACHlB,EAAE,EAAEkB,EAAE,CAAC4E,IAAI;UACXrF,IAAI,EAAE,GAAGS,EAAE,CAACT,IAAI,KAAKS,EAAE,CAAC4E,IAAI;SAC/B;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAhQ,mBAAmBA,CAACiQ,MAAM,EAAEC,QAAQ;IAChC,OAAO,IAAI,CAACjI,cAAc,CAACkI,kBAAkB,CAACF,MAAM,EAAEC,QAAQ,CAAC;EACnE;EACAhP,gBAAgBA,CAACkP,IAAK;IAClB,IAAIrD,EAAE,GAAG,IAAI;IACb,IAAGqD,IAAI,EAAE;MACLrD,EAAE,CAACtK,kBAAkB,CAACC,IAAI,GAAG,CAAC;;IAElCqK,EAAE,CAAC1L,wBAAwB,CAACuH,YAAY,GAAG,IAAI,CAAClJ,WAAW,CAACgM,QAAQ,EACpEqB,EAAE,CAAC1L,wBAAwB,CAACb,aAAa,GAAGuM,EAAE,CAAC/C,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,GAAGgC,EAAE,CAAC9C,SAAS,GAAG8C,EAAE,CAACrN,WAAW,CAACc,aAAa;IACrIuM,EAAE,CAAChK,cAAc,CAACgK,EAAE,CAACtK,kBAAkB,CAACC,IAAI,EAAEqK,EAAE,CAACtK,kBAAkB,CAACE,IAAI,EAAEoK,EAAE,CAACtK,kBAAkB,CAACQ,MAAM,EAAE8J,EAAE,CAAC1L,wBAAwB,CAAC;EACxI;EACA0B,cAAcA,CAACL,IAAI,EAAE2N,KAAK,EAAEC,IAAI,EAAEL,MAAM;IACpC,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAACtK,kBAAkB,CAACC,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACD,kBAAkB,CAACE,IAAI,GAAG0N,KAAK;IACpC,IAAI,CAAC5N,kBAAkB,CAACQ,MAAM,GAAGqN,IAAI;IACrC,IAAIC,UAAU,GAAG;MACb7N,IAAI;MACJC,IAAI,EAAE0N,KAAK;MACXC;KACH;IACDE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpP,wBAAwB,CAAC,CAACqP,OAAO,CAAC5E,GAAG,IAAG;MACrD,IAAG,IAAI,CAACzK,wBAAwB,CAACyK,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CyE,UAAU,CAACzE,GAAG,CAAC,GAAG,IAAI,CAACzK,wBAAwB,CAACyK,GAAG,CAAC;;IAE5D,CAAC,CAAC;IACFiB,EAAE,CAACa,oBAAoB,CAACE,MAAM,EAAE;IAChC,IAAI,CAAC5F,eAAe,CAACyI,mBAAmB,CAACJ,UAAU,EAAE,IAAI,CAAClP,wBAAwB,EAAE4L,QAAQ,IAAG;MAC3FF,EAAE,CAAClK,eAAe,GAAG;QACjBqJ,OAAO,EAAEe,QAAQ,CAACf,OAAO;QACzBC,KAAK,EAAEc,QAAQ,CAAC2D;OACnB;MACD,IAAG,IAAI,CAAChP,kBAAkB,CAAC+L,MAAM,IAAEV,QAAQ,CAAC2D,aAAa,IAAI3D,QAAQ,CAAC2D,aAAa,IAAI,CAAC,EAAC;QACrF,IAAI,CAACxO,uBAAuB,GAAG,IAAI;;IAE3C,CAAC,EAAE,IAAI,EAAE,MAAI;MACT2K,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;IACrC,CAAC,CAAC;IACF;EACJ;;EAEAlM,gBAAgBA,CAAC+M,IAAK;IAClB,IAAIrD,EAAE,GAAG,IAAI;IACb,IAAGqD,IAAI,EAAE;MACLrD,EAAE,CAACrI,kBAAkB,CAAChC,IAAI,GAAG,CAAC;;IAElCqK,EAAE,CAACvJ,wBAAwB,CAACgJ,WAAW,GAAG,CAACO,EAAE,CAACnL,kBAAkB,IAAG,EAAE,EAAEuM,GAAG,CAACE,QAAQ,IAAIA,QAAQ,CAACnE,EAAE,CAAC,EACnG6C,EAAE,CAACjI,cAAc,CAACiI,EAAE,CAACrI,kBAAkB,CAAChC,IAAI,EAAEqK,EAAE,CAACrI,kBAAkB,CAAC/B,IAAI,EAAEoK,EAAE,CAACrI,kBAAkB,CAACzB,MAAM,EAAE8J,EAAE,CAACvJ,wBAAwB,CAAC;EACxI;EAEAwC,gBAAgBA,CAACoK,IAAK;IAClB,IAAIrD,EAAE,GAAG,IAAI;IACb,IAAGqD,IAAI,EAAE;MACLrD,EAAE,CAACzG,kBAAkB,CAAC5D,IAAI,GAAG,CAAC;;IAElC;IACIqK,EAAE,CAACrG,cAAc,CAACqG,EAAE,CAACzG,kBAAkB,CAAC5D,IAAI,EAAEqK,EAAE,CAACzG,kBAAkB,CAAC3D,IAAI,EAAEoK,EAAE,CAACzG,kBAAkB,CAACrD,MAAM,EAAE8J,EAAE,CAACtH,oBAAoB,CAAC;EACxI;EACAX,cAAcA,CAACpC,IAAI,EAAE2N,KAAK,EAAEC,IAAI,EAAEL,MAAM;IACpC,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAACrI,kBAAkB,CAAChC,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACgC,kBAAkB,CAAC/B,IAAI,GAAG0N,KAAK;IACpC,IAAI,CAAC3L,kBAAkB,CAACzB,MAAM,GAAGqN,IAAI;IACrC,IAAIC,UAAU,GAAG;MACb7N,IAAI;MACJC,IAAI,EAAE0N,KAAK;MACXC;KACH;IACD;IACA;IACA;IACA;IACA;IACAvD,EAAE,CAACa,oBAAoB,CAACE,MAAM,EAAE;IAChC,IAAI,CAAC3F,eAAe,CAAC0I,mBAAmB,CAACN,UAAU,EAAE,IAAI,CAAC/M,wBAAwB,EAAEyJ,QAAQ,IAAG;MAC3FF,EAAE,CAACnI,eAAe,GAAG;QACjBsH,OAAO,EAAEe,QAAQ,CAACf,OAAO;QACzBC,KAAK,EAAEc,QAAQ,CAAC2D;OACnB;MACD,IAAG,IAAI,CAAC9M,kBAAkB,CAAC6J,MAAM,IAAEV,QAAQ,CAAC2D,aAAa,IAAI3D,QAAQ,CAAC2D,aAAa,IAAG,CAAC,EAAC;QACpF,IAAI,CAACtM,uBAAuB,GAAG,IAAI;;MAEvC;IACJ,CAAC,EAAE,IAAI,EAAE,MAAI;MACTyI,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA7I,cAAcA,CAAChE,IAAI,EAAE2N,KAAK,EAAEC,IAAI,EAAEL,MAAM;IACpC,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAACzG,kBAAkB,CAAC5D,IAAI,GAAGA,IAAI;IACnC,IAAI,CAAC4D,kBAAkB,CAAC3D,IAAI,GAAG0N,KAAK;IACpC,IAAI,CAAC/J,kBAAkB,CAACrD,MAAM,GAAGqN,IAAI;IACrC,IAAIC,UAAU,GAAG;MACb7N,IAAI;MACJC,IAAI,EAAE0N,KAAK;MACXC;KACH;IACDE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChL,oBAAoB,CAAC,CAACiL,OAAO,CAAC5E,GAAG,IAAG;MACjD,IAAG,IAAI,CAACrG,oBAAoB,CAACqG,GAAG,CAAC,IAAI,IAAI,EAAC;QACtCyE,UAAU,CAACzE,GAAG,CAAC,GAAG,IAAI,CAACrG,oBAAoB,CAACqG,GAAG,CAAC;;IAExD,CAAC,CAAC;IACFiB,EAAE,CAACa,oBAAoB,CAACE,MAAM,EAAE;IAChC;IACA,IAAG,CAAC,IAAI,CAAC9D,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACY,QAAQ,IAC7C,IAAI,CAAChB,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACS,KAAK,IAC1C,IAAI,CAACb,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACa,QAAQ,KAAK,CAAC,IAAI,CAACvL,WAAW,CAACc,aAAa,EAAE;MACnF,IAAI,CAACyH,cAAc,CAACvB,cAAc,CAAC6J,UAAU,EAAEtD,QAAQ,IAAG;QACtDF,EAAE,CAACvG,eAAe,GAAG;UACjB0F,OAAO,EAAEe,QAAQ,CAACf,OAAO;UACzBC,KAAK,EAAEc,QAAQ,CAAC2D;SACnB;MACL,CAAC,EAAE,IAAI,EAAE,MAAI;QACT7D,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;MACrC,CAAC,CAAC;MACF,IAAIuB,SAAS,GAAG;QAAC,GAAGP;MAAU,CAAC;MAC/BO,SAAS,CAACnO,IAAI,GAAG,SAAS;MAC1B,IAAI,CAACsF,cAAc,CAACvB,cAAc,CAACoK,SAAS,EAAE7D,QAAQ,IAAG;QACrDF,EAAE,CAAC1G,UAAU,GAAG,CAAC,GAAG,IAAIqG,GAAG,CAACO,QAAQ,CAACf,OAAO,CAACiC,GAAG,CAAC/C,EAAE,IAAEA,EAAE,CAAC1F,MAAM,CAAC,CAAC,CAAC;QACjEqH,EAAE,CAAC1G,UAAU,GAAG0G,EAAE,CAAC1G,UAAU,CAAC8H,GAAG,CAAC/C,EAAE,KAAG;UACnCT,IAAI,EAAGS,EAAE;UACTR,KAAK,EAAGQ;SACX,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,EAAE,MAAI;QACT2B,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;MACrC,CAAC,CAAC;MACF;MACA;KACH,MAAK,IAAG,CAAC,IAAI,CAACvF,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACY,QAAQ,IACnD,IAAI,CAAChB,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACS,KAAK,IAC1C,IAAI,CAACb,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACa,QAAQ,IAAI,IAAI,CAACjB,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,KAAK,IAAI,CAACrL,WAAW,CAACc,aAAa,EAAE;MACnI+P,UAAU,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC7Q,WAAW,CAACc,aAAa;MACjE,IAAI,CAACyH,cAAc,CAAC8I,WAAW,CAACR,UAAU,EAAEtD,QAAQ,IAAG;QACnDF,EAAE,CAACvG,eAAe,GAAG;UACjB0F,OAAO,EAAEe,QAAQ,CAACf,OAAO;UACzBC,KAAK,EAAEc,QAAQ,CAAC2D;SACnB;MACL,CAAC,EAAE,IAAI,EAAE,MAAI;QACT7D,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;MACrC,CAAC,CAAC;MACF,IAAIuB,SAAS,GAAG;QAAC,GAAGP;MAAU,CAAC;MAC/BO,SAAS,CAACnO,IAAI,GAAG,SAAS;MAC1B,IAAI,CAACsF,cAAc,CAAC8I,WAAW,CAACD,SAAS,EAAE7D,QAAQ,IAAG;QAClDF,EAAE,CAAC1G,UAAU,GAAG,CAAC,GAAG,IAAIqG,GAAG,CAACO,QAAQ,CAACf,OAAO,CAACiC,GAAG,CAAC/C,EAAE,IAAEA,EAAE,CAAC1F,MAAM,CAAC,CAAC,CAAC;QACjEqH,EAAE,CAAC1G,UAAU,GAAG0G,EAAE,CAAC1G,UAAU,CAAC8H,GAAG,CAAC/C,EAAE,KAAG;UACnCT,IAAI,EAAGS,EAAE;UACTR,KAAK,EAAGQ;SACX,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,EAAE,MAAI;QACT2B,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;MACrC,CAAC,CAAC;MACN;KACC,MAAK,IAAG,IAAI,CAACvF,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,IAAI,CAAC,IAAI,CAACrL,WAAW,CAACc,aAAa,EAAE;MACvF+P,UAAU,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAACjH,oBAAoB,CAACqF,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEqC,IAAI,CAAC,GAAG,CAAC;MACvF,IAAI,CAAC/I,cAAc,CAACvB,cAAc,CAAC6J,UAAU,EAAEtD,QAAQ,IAAG;QACtDF,EAAE,CAACvG,eAAe,GAAG;UACjB0F,OAAO,EAAEe,QAAQ,CAACf,OAAO;UACzBC,KAAK,EAAEc,QAAQ,CAAC2D;SACnB;MACL,CAAC,EAAE,IAAI,EAAE,MAAI;QACT7D,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;MACrC,CAAC,CAAC;MACF,IAAIuB,SAAS,GAAG;QAAC,GAAGP;MAAU,CAAC;MAC/BO,SAAS,CAACnO,IAAI,GAAG,SAAS;MAC1B,IAAI,CAACsF,cAAc,CAACvB,cAAc,CAACoK,SAAS,EAAE7D,QAAQ,IAAG;QACrDF,EAAE,CAAC1G,UAAU,GAAG,CAAC,GAAG,IAAIqG,GAAG,CAACO,QAAQ,CAACf,OAAO,CAACiC,GAAG,CAAC/C,EAAE,IAAEA,EAAE,CAAC1F,MAAM,CAAC,CAAC,CAAC;QACjEqH,EAAE,CAAC1G,UAAU,GAAG0G,EAAE,CAAC1G,UAAU,CAAC8H,GAAG,CAAC/C,EAAE,KAAG;UACnCT,IAAI,EAAGS,EAAE;UACTR,KAAK,EAAGQ;SACX,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,EAAE,MAAI;QACT2B,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;MACrC,CAAC,CAAC;;EAEV;EAIA0B,WAAWA,CAACnB,KAAK;IACb,MAAMoB,OAAO,GAAGpB,KAAK,CAACqB,aAAa,CAACC,MAAM,CAACC,SAAS;IACpD,IAAItE,EAAE,GAAG,IAAI;IACb,IAAI+C,KAAK,IAAIoB,OAAO,CAAC7F,QAAQ,CAAC,IAAI,CAACpO,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,CAAC,EAAE;MAChF,IAAI,CAACiK,WAAW,CAACC,QAAQ,GAAG,IAAI,CAAC1H,WAAW,CAAC4L,WAAW;KAC3D,MAAM,IAAIwE,KAAK,IAAIoB,OAAO,CAAC7F,QAAQ,CAAC,IAAI,CAACpO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1F6P,EAAE,CAAC1J,gBAAgB,EAAE;KACxB,MAAM,IAAIyM,KAAK,IAAIoB,OAAO,CAAC7F,QAAQ,CAAC,IAAI,CAACpO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1F6P,EAAE,CAAC7L,gBAAgB,EAAE;;EAE7B;EAEAoQ,aAAaA,CAACC,CAAC;IACX,IAAIC,KAAK,GAAG,gEAAgE;IAC5E,IAAIC,KAAK,GAAG,EAAE;IACd,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACvBD,KAAK,IAAID,KAAK,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGL,KAAK,CAAC7D,MAAM,CAAC,CAAC;;IAE5D,OAAO8D,KAAK;EAChB;EAEA3P,6BAA6BA,CAACgO,KAAY;IACtC,IAAI/C,EAAE,GAAG,IAAI;IAEb,IAAG,IAAI,CAACnL,kBAAkB,CAAC+L,MAAM,IAAE,IAAI,CAAC9K,eAAe,CAACsJ,KAAK,EAAC;MAC1D,IAAI,CAAC/J,uBAAuB,GAAG,IAAI;KACtC,MAAI;MACD,IAAI,CAACA,uBAAuB,GAAG,KAAK;;IAGxC,MAAM0P,kBAAkB,GAAG,IAAIpF,GAAG,CAAC,CAACoD,KAAK,IAAG,EAAE,EAAE3B,GAAG,CAACE,QAAQ,IAAIA,QAAQ,CAACnE,EAAE,CAAC,CAAC;IAC7E,MAAM6H,mBAAmB,GAAG,IAAIrF,GAAG,CAAC,CAAC,IAAI,CAAChN,WAAW,CAACmM,SAAS,IAAG,EAAE,EAAEsC,GAAG,CAACE,QAAQ,IAAIA,QAAQ,CAACnE,EAAE,CAAC,CAAC;IAEnG,MAAM8H,cAAc,GAAG,CAAClC,KAAK,IAAG,EAAE,EAAE3E,MAAM,CAACkD,QAAQ,IAAI,CAAC0D,mBAAmB,CAACE,GAAG,CAAC5D,QAAQ,CAACnE,EAAE,CAAC,CAAC;IAE7F,MAAMgI,gBAAgB,GAAG,IAAI,CAACxS,WAAW,CAACmM,SAAS,CAACV,MAAM,CAACkD,QAAQ,IAAI,CAACyD,kBAAkB,CAACG,GAAG,CAAC5D,QAAQ,CAACnE,EAAE,CAAC,CAAC;IAE5G,IAAI,CAACiI,0BAA0B,CAAC,CAACH,cAAc,IAAG,EAAE,EAAE7D,GAAG,CAACE,QAAQ,IAAIA,QAAQ,CAACnE,EAAE,CAAC,CAAC;IAEnFgI,gBAAgB,CAACxB,OAAO,CAACrC,QAAQ,IAAG;MAChC,IAAI,CAACvK,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqH,MAAM,CAACsD,QAAQ,IAAIA,QAAQ,CAAC2D,YAAY,IAAI/D,QAAQ,CAAC2B,IAAI,CAAC;IAChH,CAAC,CAAC;IAEF,IAAI,CAACtQ,WAAW,CAACmM,SAAS,GAAGiE,KAAK;EACtC;EACAqC,0BAA0BA,CAAC3F,WAAqB;IAE5C,IAAIO,EAAE,GAAG,IAAI;IACb,IAAI,CAACa,oBAAoB,CAACE,MAAM,EAAE;IAClC,IAAI,CAACpJ,kBAAkB,CAAChC,IAAI,GAAG,CAAC;IAChC,IAAI6N,UAAU,GAAG;MACb7N,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,OAAO;MACb2N,IAAI,EAAE,IAAI,CAAC5L,kBAAkB,CAACzB;KACjC;IACD,IAAI,CAACkF,eAAe,CAAC0I,mBAAmB,CAACN,UAAU,EAC/C;MACIjP,OAAO,EAAE,IAAI;MACbd,aAAa,EAAE,IAAI,CAACd,WAAW,CAACc,aAAa;MAC7CoI,YAAY,EAAE,IAAI,CAAClJ,WAAW,CAACgM,QAAQ;MACvCc,WAAW,EAAEA;KAChB,EAAG6F,GAAG,IAAI;MACP,IAAIA,GAAG,CAACzB,aAAa,GAAG,CAAC,EAAE;QACvB,MAAM0B,YAAY,GAAGD,GAAG,CAACnG,OAAO,CAACf,MAAM,CAACsD,QAAQ,IAAI,CAAC,IAAI,CAAChC,mBAAmB,CAACwF,GAAG,CAACxD,QAAQ,CAAC8D,YAAY,CAAC,IACpG,CAAC,IAAI,CAACzO,kBAAkB,CAAC0O,IAAI,CAACC,gBAAgB,IAAIA,gBAAgB,CAACF,YAAY,KAAK9D,QAAQ,CAAC8D,YAAY,CAAC,CAAC;QAC/G,IAAI,CAACzO,kBAAkB,CAAC4O,IAAI,CAAC,GAAGJ,YAAY,CAAC;;IAErD,CAAC,EAAE,IAAI,EAAE,MAAK;MACVvF,EAAE,CAACa,oBAAoB,CAAC2B,OAAO,EAAE;IACrC,CAAC,CAAC;EACV;EAEAvL,6BAA6BA,CAAC8L,KAAK;IAC/B,IAAG,IAAI,CAAChM,kBAAkB,CAAC6J,MAAM,IAAE,IAAI,CAAC/I,eAAe,CAACuH,KAAK,EAAC;MAC1D,IAAI,CAAC7H,uBAAuB,GAAG,IAAI;KACtC,MAAI;MACD,IAAI,CAACA,uBAAuB,GAAG,KAAK;;EAE5C;EAEArC,8BAA8BA,CAAA;IAC1B;IACA,IAAI8K,EAAE,GAAG,IAAI;IACb,IAAIkD,MAAM,GAAG;MACTvN,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,UAAU;MAChB2N,IAAI,EAAE;KACT;IACDE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpP,wBAAwB,CAAC,CAACqP,OAAO,CAAC5E,GAAG,IAAG;MACrD,IAAG,IAAI,CAACzK,wBAAwB,CAACyK,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CmE,MAAM,CAACnE,GAAG,CAAC,GAAG,IAAI,CAACzK,wBAAwB,CAACyK,GAAG,CAAC;;IAExD,CAAC,CAAC;IACF,IAAI,CAACtJ,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC0F,eAAe,CAACyI,mBAAmB,CAACV,MAAM,EAAC,IAAI,CAAC5O,wBAAwB,EAAE4L,QAAQ,IAAG;MACtF,IAAG,IAAI,CAACrL,kBAAkB,CAAC+L,MAAM,IAAIV,QAAQ,CAAC2D,aAAa,EAAC;QACxD,IAAI,CAAChP,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACQ,uBAAuB,GAAG,KAAK;QACpC;;MAEJ,IAAI,CAACR,kBAAkB,GAAGqL,QAAQ,CAACf,OAAO;MAC1C,IAAI,CAAC9J,uBAAuB,GAAG,IAAI;IACvC,CAAC,EAAC,IAAI,EAAC,MAAI;MAAE,IAAI,CAACI,eAAe,GAAG,KAAK;IAAC,CAAC,CAAC;EAChD;EAEA2B,8BAA8BA,CAAA;IAC1B;IACA,IAAI4I,EAAE,GAAG,IAAI;IACb,IAAIkD,MAAM,GAAG;MACTvN,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,UAAU;MAChB2N,IAAI,EAAE;KACT;IACD,IAAI,CAAC7L,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC0D,eAAe,CAAC0I,mBAAmB,CAACZ,MAAM,EAAE,IAAI,CAACzM,wBAAwB,EAAEyJ,QAAQ,IAAG;MACvF,IAAG,IAAI,CAACnJ,kBAAkB,CAAC6J,MAAM,IAAIV,QAAQ,CAAC2D,aAAa,EAAC;QACxD,IAAI,CAAC9M,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACQ,uBAAuB,GAAG,KAAK;QACpC;;MAEJ,IAAI,CAACR,kBAAkB,GAAGmJ,QAAQ,CAACf,OAAO;MAC1C,IAAI,CAAC5H,uBAAuB,GAAG,IAAI;IACvC,CAAC,EAAE,IAAI,EAAE,MAAI;MACT,IAAI,CAACG,eAAe,GAAG,KAAK;IAChC,CAAC,CAAC;EACN;EAEAkO,kCAAkCA,CAAA;IAC9B,IAAI5F,EAAE,GAAG,IAAI;IAEb,IAAIA,EAAE,CAACrN,WAAW,CAACsK,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACW,QAAQ,EAAG,OAAO,KAAK;IAC1E,IAAIgC,EAAE,CAACJ,WAAW,CAACiG,OAAO,IAAI7F,EAAE,CAACvE,cAAc,IAAIuE,EAAE,CAACtE,cAAc,IAAIsE,EAAE,CAACxE,iBAAiB,EAAG;MAC3F;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,KAAK;;IAEhB,IAAKwE,EAAE,CAAC/C,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACS,KAAK,IAAI,IAAI,CAACnL,WAAW,CAACgM,QAAQ,IAAI,IAAI,IAAM,CAACqB,EAAE,CAAC/C,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACS,KAAK,IAAIkC,EAAE,CAAC/C,QAAQ,IAAIxN,SAAS,CAAC4N,SAAS,CAACY,QAAQ,KAAK+B,EAAE,CAACrN,WAAW,CAACI,OAAO,IAAI,IAAK,EAAC;MACjN,OAAO,KAAK;;IAEhB,OAAQ,IAAI;EAChB;EAEA8H,QAAQA,CAAA;IACJ,IAAImF,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAAC5F,WAAW,CAACI,SAAS,EAAE;MAC3B,IAAI,CAACJ,WAAW,CAACI,SAAS,GAAG,IAAI,CAAC+J,aAAa,CAAC,EAAE,CAAC;KACtD,MAAK;MACF,IAAI,CAACnK,WAAW,CAACI,SAAS,GAAG,IAAI,CAAC+J,aAAa,CAAC,EAAE,CAAC;MACnDvE,EAAE,CAAC/G,gBAAgB,EAAE;;EAE7B;;;uBA/zBS+B,yBAAyB,EAAArL,EAAA,CAAAmW,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArW,EAAA,CAAAmW,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvW,EAAA,CAAAmW,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAzW,EAAA,CAAAmW,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA3W,EAAA,CAAAmW,iBAAA,CAAAnW,EAAA,CAAA4W,iBAAA,GAAA5W,EAAA,CAAAmW,iBAAA,CAAAnW,EAAA,CAAA6W,QAAA;IAAA;EAAA;;;YAAzBxL,yBAAyB;MAAAyL,SAAA;MAAAC,QAAA,GAAA/W,EAAA,CAAAgX,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBtCtX,EAAA,CAAAC,cAAA,cAAmI;UAAnGD,EAAA,CAAAwC,UAAA,2BAAAgV,iEAAA9U,MAAA;YAAA,OAAiBA,MAAA,CAAA6B,cAAA,EAAuB;UAAA,EAAC,sBAAAkT,4DAAA;YAAA,OAAaF,GAAA,CAAAvG,cAAA,EAAgB;UAAA,EAA7B;UACzEhR,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnGH,EAAA,CAAA+B,SAAA,sBAAoF;UACxF/B,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAwE;UAEhED,EAAA,CAAA+B,SAAA,kBAC0R;UAC1R/B,EAAA,CAAAC,cAAA,kBAA+I;UAAtBD,EAAA,CAAAwC,UAAA,mBAAAkV,6DAAA;YAAA,OAASH,GAAA,CAAAzE,SAAA,EAAW;UAAA,EAAC;UAAC9S,EAAA,CAAAG,YAAA,EAAW;UAKtKH,EAAA,CAAAC,cAAA,iBAA2C;UAC5BD,EAAA,CAAAwC,UAAA,sBAAAmV,kEAAAjV,MAAA;YAAA,OAAY6U,GAAA,CAAAhD,WAAA,CAAA7R,MAAA,CAAmB;UAAA,EAAC;UACvC1C,EAAA,CAAAC,cAAA,sBAA4E;UAKWD,EAAA,CAAAE,MAAA,IAAmD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzJH,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAwC,UAAA,2BAAAoV,mEAAAlV,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAA4L,WAAA,GAAAlM,MAAA;UAAA,EAAqC,2BAAAkV,mEAAA;YAAA,OAMpBL,GAAA,CAAA5G,iBAAA,CAAkB,aAAa,CAAC;UAAA,EANZ;UAF5C3Q,EAAA,CAAAG,YAAA,EASE;UAIVH,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+B,SAAA,iBAA2E;UAC3E/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAA6V,2CAAA,oBAA4L;UAC5L7X,EAAA,CAAAgC,UAAA,KAAA8V,2CAAA,oBAA6J;UAC7J9X,EAAA,CAAAgC,UAAA,KAAA+V,2CAAA,oBAAmJ;UACnJ/X,EAAA,CAAAgC,UAAA,KAAAgW,2CAAA,oBAAsL;UAC1LhY,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA+B;UACqCD,EAAA,CAAAE,MAAA,IAAmD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtJH,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAwC,UAAA,2BAAAyV,mEAAAvV,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAA6L,QAAA,GAAAnM,MAAA;UAAA,EAAkC;UAFzC1C,EAAA,CAAAG,YAAA,EAQE;UAIVH,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+B,SAAA,iBAAwE;UACxE/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAAkW,2CAAA,oBAAsL;UACtLlY,EAAA,CAAAgC,UAAA,KAAAmW,2CAAA,oBAA2J;UAC3JnY,EAAA,CAAAgC,UAAA,KAAAoW,2CAAA,oBAAqJ;UACzJpY,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA+B;UACkCD,EAAA,CAAAE,MAAA,IAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrHH,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAwC,UAAA,2BAAA6V,mEAAA3V,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAA+L,KAAA,GAAArM,MAAA;UAAA,EAA+B;UAFtC1C,EAAA,CAAAG,YAAA,EAME;UAIVH,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+B,SAAA,iBAAqE;UACrE/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAAsW,2CAAA,oBAA+I;UAC/ItY,EAAA,CAAAgC,UAAA,KAAAuW,2CAAA,oBAAgL;UACpLvY,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA+B;UACkCD,EAAA,CAAAE,MAAA,IAAgD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChJH,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAwC,UAAA,2BAAAgW,mEAAA9V,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAA8L,KAAA,GAAApM,MAAA;UAAA,EAA+B,2BAAA8V,mEAAA;YAAA,OAMdjB,GAAA,CAAA5G,iBAAA,CAAkB,OAAO,CAAC;UAAA,EANZ;UAFtC3Q,EAAA,CAAAG,YAAA,EASE;UAIVH,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+B,SAAA,iBAAqE;UACrE/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAAyW,2CAAA,oBAAgL;UAChLzY,EAAA,CAAAgC,UAAA,KAAA0W,2CAAA,oBAAwJ;UACxJ1Y,EAAA,CAAAgC,UAAA,KAAA2W,2CAAA,oBAA+I;UAC/I3Y,EAAA,CAAAgC,UAAA,KAAA4W,2CAAA,oBAAgL;UACpL5Y,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA+B;UAC6DD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtJH,EAAA,CAAAC,cAAA,eAAiB;UAKND,EAAA,CAAAwC,UAAA,2BAAAqW,sEAAAnW,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAAkM,WAAA,GAAAxM,MAAA;UAAA,EAAqC;UAI/C1C,EAAA,CAAAG,YAAA,EAAW;UAIhBH,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+B,SAAA,iBAA2E;UAC3E/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAA8W,2CAAA,oBAA8J;UAClK9Y,EAAA,CAAAG,YAAA,EAAM;UAGdH,EAAA,CAAAC,cAAA,eAAyB;UAI2CD,EAAA,CAAAE,MAAA,IAAmD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClJH,EAAA,CAAAC,cAAA,eAAiB;UAIDD,EAAA,CAAAwC,UAAA,2BAAAuW,wEAAArW,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,GAAA5K,MAAA;UAAA,EAAkC,2BAAAqW,wEAAA;YAAA,OAOjBxB,GAAA,CAAAxE,WAAA,CAAY,IAAI,CAAC;UAAA,EAPA;UAQ7C/S,EAAA,CAAAG,YAAA,EAAa;UAItBH,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+B,SAAA,iBAAwE;UACxE/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAAgX,2CAAA,oBAAsL;UAC1LhZ,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAAyG;UACrCD,EAAA,CAAAE,MAAA,IAAmD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtJH,EAAA,CAAAC,cAAA,eAAiB;UAMDD,EAAA,CAAAwC,UAAA,2BAAAyW,wEAAAvW,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAAgM,QAAA,GAAAtM,MAAA;UAAA,EAAkC,2BAAAuW,wEAAA;YAAA,OAQjB1B,GAAA,CAAAtT,eAAA,CAAgB,IAAI,CAAC;UAAA,EARJ;UAS7CjE,EAAA,CAAAG,YAAA,EAAa;UAItBH,EAAA,CAAAC,cAAA,eAA4M;UACxMD,EAAA,CAAA+B,SAAA,iBAAwE;UACxE/B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAgC,UAAA,KAAAkX,2CAAA,oBAAsL;UAC1LlZ,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAAsL;UACrHD,EAAA,CAAAE,MAAA,KAAsD;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtJH,EAAA,CAAAC,cAAA,gBAAiB;UAITD,EAAA,CAAAwC,UAAA,yBAAA2W,wEAAAzW,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAAI,OAAA,GAAAV,MAAA;UAAA,EAA+B,sBAAA0W,qEAAA;YAAA,OAUnB7B,GAAA,CAAArE,cAAA,EAAgB;UAAA,EAVG;UAWlClT,EAAA,CAAAG,YAAA,EAAc;UAIvBH,EAAA,CAAAgC,UAAA,MAAAqX,0CAAA,kBAKM;UA6BNrZ,EAAA,CAAAgC,UAAA,MAAAsX,0CAAA,kBAkBM;UAENtZ,EAAA,CAAAgC,UAAA,MAAAuX,0CAAA,kBAKM;UAENvZ,EAAA,CAAAgC,UAAA,MAAAwX,0CAAA,kBAqBM;UAQNxZ,EAAA,CAAAC,cAAA,gBAA+B;UAEAD,EAAA,CAAAE,MAAA,KAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpFH,EAAA,CAAAC,cAAA,gBAAuD;UAGpCD,EAAA,CAAAwC,UAAA,2BAAAiX,4EAAA/W,MAAA;YAAA,OAAA6U,GAAA,CAAAvU,WAAA,CAAAiM,KAAA,GAAAvM,MAAA;UAAA,EAA+B;UAO7C1C,EAAA,CAAAG,YAAA,EAAgB;UASzCH,EAAA,CAAAgC,UAAA,MAAA0X,iDAAA,0BAyCa;UACb1Z,EAAA,CAAAgC,UAAA,MAAA2X,iDAAA,0BAyCa;UAEb3Z,EAAA,CAAAgC,UAAA,MAAA4X,iDAAA,2BA6Fa;UACjB5Z,EAAA,CAAAG,YAAA,EAAY;;;UA7fVH,EAAA,CAAAkC,UAAA,cAAAqV,GAAA,CAAAtH,WAAA,CAAyB;UAGajQ,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,4BAAyD;UACtDR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAkC,UAAA,UAAAqV,GAAA,CAAA5J,KAAA,CAAe,SAAA4J,GAAA,CAAAzJ,IAAA;UAIxC9N,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAkC,UAAA,UAAAqV,GAAA,CAAAhX,WAAA,CAAAC,SAAA,uBAAqD,aAAA+W,GAAA,CAAAtH,WAAA,CAAAiG,OAAA,IAAAqB,GAAA,CAAAzL,cAAA,IAAAyL,GAAA,CAAAxL,cAAA,IAAAwL,GAAA,CAAA1L,iBAAA,KAAA0L,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAAzX,SAAA,CAAA4N,SAAA,CAAAY,QAAA,IAAAiJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAAzX,SAAA,CAAA4N,SAAA,CAAAS,KAAA,KAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,KAAAiK,GAAA,CAAAzX,SAAA,CAAA4N,SAAA,CAAAW,QAAA,KAAAkJ,GAAA,CAAAvU,WAAA,CAAAI,OAAA;UAErDpD,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAkC,UAAA,UAAAqV,GAAA,CAAAhX,WAAA,CAAAC,SAAA,yBAAuD;UAOzDR,EAAA,CAAAI,SAAA,GAA+D;UAA/DJ,EAAA,CAAA6H,qBAAA,WAAA0P,GAAA,CAAAhX,WAAA,CAAAC,SAAA,8BAA+D;UAKYR,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,2BAAmD;UAI3GR,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAkC,UAAA,YAAAqV,GAAA,CAAAvU,WAAA,CAAA4L,WAAA,CAAqC,mDAAA2I,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAcfR,EAAA,CAAAI,SAAA,GAAiG;UAAjGJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAvM,KAAA,KAAAkV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAkL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAkL,MAAA,CAAAvX,QAAA,EAAiG;UACjGvC,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAkL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAkL,MAAA,CAAAC,SAAA,CAAwD;UACxD/Z,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAkL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAjL,WAAA,CAAAkL,MAAA,CAAAE,OAAA,CAAsD;UACtDha,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAA1L,iBAAA,CAAuB;UAKQ7L,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,2BAAmD;UAIxGR,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAkC,UAAA,YAAAqV,GAAA,CAAAvU,WAAA,CAAA6L,QAAA,CAAkC,oDAAA0I,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAaZR,EAAA,CAAAI,SAAA,GAA2F;UAA3FJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAxM,KAAA,KAAAkV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAiL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAiL,MAAA,CAAAvX,QAAA,EAA2F;UAC3FvC,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAiL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAiL,MAAA,CAAAC,SAAA,CAAqD;UACrD/Z,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAiL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAhL,QAAA,CAAAiL,MAAA,CAAAE,OAAA,CAAmD;UAKvBha,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,wBAAgD;UAIlGR,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAkC,UAAA,YAAAqV,GAAA,CAAAvU,WAAA,CAAA+L,KAAA,CAA+B,gBAAAwI,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAWTR,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA9K,KAAA,CAAA+K,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA9K,KAAA,CAAA+K,MAAA,CAAAE,OAAA,CAAgD;UAChDha,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAxL,cAAA,CAAoB;UAKQ/L,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,wBAAgD;UAIlGR,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAkC,UAAA,YAAAqV,GAAA,CAAAvU,WAAA,CAAA8L,KAAA,CAA+B,oDAAAyI,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAcTR,EAAA,CAAAI,SAAA,GAAqF;UAArFJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAzM,KAAA,KAAAkV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAgL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAgL,MAAA,CAAAvX,QAAA,EAAqF;UACrFvC,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAgL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAgL,MAAA,CAAAC,SAAA,CAAkD;UAClD/Z,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAgL,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA/K,KAAA,CAAAgL,MAAA,CAAAE,OAAA,CAAgD;UAChDha,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAzL,cAAA,CAAoB;UAKmC9L,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,8BAAsD;UAInIR,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAkC,UAAA,qBAAoB,YAAAqV,GAAA,CAAAvU,WAAA,CAAAkM,WAAA,mCAAAqI,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAaER,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA3K,WAAA,CAAA4K,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA3K,WAAA,CAAA4K,MAAA,CAAAC,SAAA,CAAwD;UAQ7B/Z,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,2BAAmD;UAG/FR,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAkC,UAAA,mBAAkB,uCAAAqV,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,+BAAAiK,GAAA,CAAA/I,cAAA,iBAAA+I,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAiBDR,EAAA,CAAAI,SAAA,GAA2F;UAA3FJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAvM,QAAA,CAAAjL,KAAA,KAAAkV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAvM,QAAA,CAAAwM,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAAvM,QAAA,CAAAwM,MAAA,CAAAvX,QAAA,EAA2F;UAIjGvC,EAAA,CAAAI,SAAA,GAAyE;UAAzEJ,EAAA,CAAAia,UAAA,CAAA1C,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,iBAAyE;UACpCnO,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,2BAAmD;UAGnGR,EAAA,CAAAI,SAAA,GAA+I;UAA/IJ,EAAA,CAAAkC,UAAA,cAAAqV,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAyM,MAAA,CAA+I,aAAA3C,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,cAAAoJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,wCAAAoJ,GAAA,CAAAvU,WAAA,CAAAgM,QAAA,cAAAuI,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAyM,MAAA,aAAA3C,GAAA,CAAAlE,YAAA,iCAAAkE,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAiBnHR,EAAA,CAAAI,SAAA,GAA2J;UAA3JJ,EAAA,CAAAia,UAAA,CAAA1C,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAyM,MAAA,iBAA2J;UAGtKla,EAAA,CAAAI,SAAA,GAA2F;UAA3FJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA7K,QAAA,CAAA3M,KAAA,KAAAkV,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA7K,QAAA,CAAA8K,MAAA,kBAAAvC,GAAA,CAAAtH,WAAA,CAAA4J,QAAA,CAAA7K,QAAA,CAAA8K,MAAA,CAAAvX,QAAA,EAA2F;UAIjGvC,EAAA,CAAAI,SAAA,GAAsJ;UAAtJJ,EAAA,CAAAia,UAAA,CAAA1C,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAY,QAAA,KAAAkJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAU,KAAA,IAAAoJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAa,QAAA,kBAAsJ;UACpHtO,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,8BAAsD;UAI3GR,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAkC,UAAA,YAAAqV,GAAA,CAAAnV,yBAAA,CAAqC,UAAAmV,GAAA,CAAAvU,WAAA,CAAAI,OAAA,iBAAAmU,GAAA,CAAAhX,WAAA,CAAAC,SAAA,gEAAA+W,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAY,QAAA,iCAAAkJ,GAAA,CAAApL,kBAAA;UAiBAnM,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAY,QAAA,CAAqD;UAkCtErO,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAc,QAAA,CAAqD;UAoBpCvO,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAc,QAAA,CAAqD;UAOtEvO,EAAA,CAAAI,SAAA,GAC8F;UAD9FJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAY,QAAA,KAAAkJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAAzX,SAAA,CAAA4N,SAAA,CAAAS,KAAA,IAAAoJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAAzX,SAAA,CAAA4N,SAAA,CAAAY,QAAA,IAAAiJ,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAAzX,SAAA,CAAA4N,SAAA,CAAAa,QAAA,EAC8F;UA8B/FvO,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAK,iBAAA,CAAAkX,GAAA,CAAAhX,WAAA,CAAAC,SAAA,uBAAiD;UAIzDR,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAkC,UAAA,YAAAqV,GAAA,CAAAvU,WAAA,CAAAiM,KAAA,CAA+B,YAAAsI,GAAA,CAAAtE,QAAA,kBAAAsE,GAAA,CAAAhX,WAAA,CAAAC,SAAA;UAgBOR,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtB,kCAAA,GAA0C;UA0C3CjW,EAAA,CAAAI,SAAA,GAAiG;UAAjGJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAtB,kCAAA,MAAAsB,GAAA,CAAArS,kBAAA,IAAAqS,GAAA,CAAArS,kBAAA,CAAA+L,MAAA,KAAiG;UA2CpGjR,EAAA,CAAAI,SAAA,GAAwI;UAAxIJ,EAAA,CAAAkC,UAAA,SAAAqV,GAAA,CAAAxK,WAAA,CAAA/M,EAAA,CAAAc,eAAA,KAAAqZ,GAAA,EAAA5C,GAAA,CAAAzX,SAAA,CAAAkN,WAAA,CAAAoN,eAAA,CAAAC,wBAAA,MAAA9C,GAAA,CAAAvU,WAAA,CAAAsK,QAAA,IAAAiK,GAAA,CAAA9J,cAAA,CAAAY,QAAA,CAAwI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}