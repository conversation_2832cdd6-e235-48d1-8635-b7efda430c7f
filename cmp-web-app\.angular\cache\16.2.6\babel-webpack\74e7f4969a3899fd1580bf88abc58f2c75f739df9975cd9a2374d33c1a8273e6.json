{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { AppRatingPlanListComponent } from \"./list-plan/app.ratingplan.list.component\";\nimport { AppRegisterPlanListComponent } from \"./list-register-plan/app.registerplan.list.component\";\nimport { CreatePlanComponent } from \"./list-plan/create-plan/create-plan.component\";\nimport { UpdatePlanComponent } from \"./list-plan/update-plan/update-plan.component\";\nimport { AppHistoryRegisterplanListComponent } from \"./list-history-register-plan/app.history.registerplan.list.component\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport DataPage from \"../../service/data.page\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppRatingPlanRouting {\n  static {\n    this.ɵfac = function AppRatingPlanRouting_Factory(t) {\n      return new (t || AppRatingPlanRouting)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRatingPlanRouting\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: AppRatingPlanListComponent,\n        data: new DataPage(\"global.menu.listplan\", [CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST])\n      }, {\n        path: \"registers\",\n        component: AppRegisterPlanListComponent,\n        data: new DataPage(\"global.menu.registerplan\", [CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])\n      },\n      // {path: \"detail/:id\", component: AppRatingPlanDetailComponent, data: new DataPage(\"global.menu.detailplan\")},\n      {\n        path: \"registers/history\",\n        component: AppHistoryRegisterplanListComponent,\n        data: new DataPage(\"global.titlepage.historyRegisterPlan\", [CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_HISTORY])\n      }, {\n        path: \"create\",\n        component: CreatePlanComponent,\n        data: new DataPage(\"global.titlepage.createRatingPlan\", [CONSTANTS.PERMISSIONS.RATING_PLAN.CREATE])\n      }, {\n        path: \"update/:id\",\n        component: UpdatePlanComponent,\n        data: new DataPage(\"global.titlepage.editRatingPlan\", [CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRatingPlanRouting, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppRatingPlanListComponent", "AppRegisterPlanListComponent", "CreatePlanComponent", "UpdatePlanComponent", "AppHistoryRegisterplanListComponent", "CONSTANTS", "DataPage", "AppRatingPlanRouting", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "RATING_PLAN", "VIEW_LIST", "RATING_PLAN_SIM", "REGISTER_PLAN", "REGISTER_HISTORY", "CREATE", "UPDATE", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\app.ratingplan.routing.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { AppRatingPlanListComponent } from \"./list-plan/app.ratingplan.list.component\";\r\nimport { AppRegisterPlanListComponent } from \"./list-register-plan/app.registerplan.list.component\";\r\nimport { CreatePlanComponent } from \"./list-plan/create-plan/create-plan.component\";\r\nimport { UpdatePlanComponent } from \"./list-plan/update-plan/update-plan.component\";\r\nimport { AppRatingPlanDetailComponent } from \"./detail-plan/app.ratingplan.detail.component\";\r\nimport { AppHistoryRegisterplanListComponent } from \"./list-history-register-plan/app.history.registerplan.list.component\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport DataPage from \"../../service/data.page\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {path: \"\", component: AppRatingPlanListComponent, data: new DataPage(\"global.menu.listplan\", [CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST])},\r\n            {path: \"registers\", component: AppRegisterPlanListComponent, data: new DataPage(\"global.menu.registerplan\", [CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])},\r\n            // {path: \"detail/:id\", component: AppRatingPlanDetailComponent, data: new DataPage(\"global.menu.detailplan\")},\r\n            {path: \"registers/history\", component: AppHistoryRegisterplanListComponent, data: new DataPage(\"global.titlepage.historyRegisterPlan\", [CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_HISTORY])},\r\n            {path: \"create\", component: CreatePlanComponent, data: new DataPage(\"global.titlepage.createRatingPlan\", [CONSTANTS.PERMISSIONS.RATING_PLAN.CREATE])},\r\n            {path: \"update/:id\", component: UpdatePlanComponent, data: new DataPage(\"global.titlepage.editRatingPlan\", [CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])}\r\n        ])\r\n    ],\r\n    exports: [RouterModule]\r\n})\r\nexport class AppRatingPlanRouting{}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,4BAA4B,QAAQ,sDAAsD;AACnG,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,mBAAmB,QAAQ,+CAA+C;AAEnF,SAASC,mCAAmC,QAAQ,sEAAsE;AAC1H,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAOC,QAAQ,MAAM,yBAAyB;;;AAe9C,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAXzBR,YAAY,CAACS,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEV,0BAA0B;QAAEW,IAAI,EAAE,IAAIL,QAAQ,CAAC,sBAAsB,EAAE,CAACD,SAAS,CAACO,WAAW,CAACC,WAAW,CAACC,SAAS,CAAC;MAAC,CAAC,EAC5I;QAACL,IAAI,EAAE,WAAW;QAAEC,SAAS,EAAET,4BAA4B;QAAEU,IAAI,EAAE,IAAIL,QAAQ,CAAC,0BAA0B,EAAE,CAACD,SAAS,CAACO,WAAW,CAACG,eAAe,CAACC,aAAa,CAAC;MAAC,CAAC;MACnK;MACA;QAACP,IAAI,EAAE,mBAAmB;QAAEC,SAAS,EAAEN,mCAAmC;QAAEO,IAAI,EAAE,IAAIL,QAAQ,CAAC,sCAAsC,EAAE,CAACD,SAAS,CAACO,WAAW,CAACG,eAAe,CAACE,gBAAgB,CAAC;MAAC,CAAC,EACjM;QAACR,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAER,mBAAmB;QAAES,IAAI,EAAE,IAAIL,QAAQ,CAAC,mCAAmC,EAAE,CAACD,SAAS,CAACO,WAAW,CAACC,WAAW,CAACK,MAAM,CAAC;MAAC,CAAC,EACrJ;QAACT,IAAI,EAAE,YAAY;QAAEC,SAAS,EAAEP,mBAAmB;QAAEQ,IAAI,EAAE,IAAIL,QAAQ,CAAC,iCAAiC,EAAE,CAACD,SAAS,CAACO,WAAW,CAACC,WAAW,CAACM,MAAM,CAAC;MAAC,CAAC,CAC1J,CAAC,EAEIpB,YAAY;IAAA;EAAA;;;2EAEbQ,oBAAoB;IAAAa,OAAA,GAAAC,EAAA,CAAAtB,YAAA;IAAAuB,OAAA,GAFnBvB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}