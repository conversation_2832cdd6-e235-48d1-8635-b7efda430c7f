{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class ReportService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/report\";\n  }\n  createGeneralReportDynamic(data, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}`, {}, data, {}, callback, errorCallback, finallyCallback);\n  }\n  updateGeneralReportDynamic(id, data, callback, errorCallback, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/${id}`, {}, data, {}, callback, errorCallback, finallyCallback);\n  }\n  updateSummaryReportDynamic(id, data, callback) {\n    this.httpService.put(`${this.prefixApi}/schedule/${id}`, {}, data, {}, callback);\n  }\n  updateSendingReportDynamic(id, data, callback) {\n    this.httpService.put(`${this.prefixApi}/sending/${id}`, {}, data, {}, callback);\n  }\n  getDetailReportDynamic(id, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  checkExistNameReportDynamic(name, callback) {\n    this.httpService.get(`${this.prefixApi}/checkExits`, {}, {\n      name\n    }, callback);\n  }\n  getAllEmailGroup(callback) {\n    this.httpService.get(`${this.prefixApi}/email-group/all`, {}, {}, callback);\n  }\n  preview(data, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/preview`, {\n      timeout: 120000\n    }, data, {}, callback, errorCallback, finallyCallback);\n  }\n  exportFile(data, callback, errorCallback) {\n    this.httpService.downloadPostForReporting(`${this.prefixApi}/export`, {\n      timeout: 900000\n    }, data, {}, callback, errorCallback);\n  }\n  static {\n    this.ɵfac = function ReportService_Factory(t) {\n      return new (t || ReportService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ReportService,\n      factory: ReportService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "ReportService", "constructor", "httpService", "prefixApi", "createGeneralReportDynamic", "data", "callback", "<PERSON><PERSON><PERSON><PERSON>", "finally<PERSON><PERSON><PERSON>", "post", "updateGeneralReportDynamic", "id", "put", "updateSummaryReportDynamic", "updateSendingReportDynamic", "getDetailReportDynamic", "get", "checkExistNameReportDynamic", "name", "getAllEmailGroup", "preview", "timeout", "exportFile", "downloadPostForReporting", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\report\\ReportService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class ReportService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/report\";\r\n    }\r\n\r\n    public createGeneralReportDynamic(data, callback, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}`,{}, data, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public updateGeneralReportDynamic(id, data, callback, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/${id}`,{}, data, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public updateSummaryReportDynamic(id, data, callback){\r\n        this.httpService.put(`${this.prefixApi}/schedule/${id}`,{}, data, {}, callback);\r\n    }\r\n\r\n    public updateSendingReportDynamic(id, data, callback){\r\n        this.httpService.put(`${this.prefixApi}/sending/${id}`,{}, data, {}, callback);\r\n    }\r\n\r\n    public getDetailReportDynamic(id, callback, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public checkExistNameReportDynamic(name, callback){\r\n        this.httpService.get(`${this.prefixApi}/checkExits`, {}, {name}, callback);\r\n    }\r\n\r\n    public getAllEmailGroup(callback){\r\n        this.httpService.get(`${this.prefixApi}/email-group/all`, {}, {}, callback);\r\n    }\r\n\r\n    public preview(data, callback, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/preview`, {timeout: 120000}, data, {}, callback,errorCallback, finallyCallback);\r\n    }\r\n\r\n    public exportFile(data, callback?:Function, errorCallback?:Function){\r\n        this.httpService.downloadPostForReporting(`${this.prefixApi}/export`, {timeout: 900000}, data, {}, callback, errorCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,aAAa;EAEtBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,SAAS;EAC9B;EAEOC,0BAA0BA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,aAAuB,EAAEC,eAA0B;IACjG,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,EAAE,EAAC,EAAE,EAAEE,IAAI,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrG;EAEOE,0BAA0BA,CAACC,EAAE,EAAEN,IAAI,EAAEC,QAAQ,EAAEC,aAAuB,EAAEC,eAA0B;IACrG,IAAI,CAACN,WAAW,CAACU,GAAG,CAAC,GAAG,IAAI,CAACT,SAAS,IAAIQ,EAAE,EAAE,EAAC,EAAE,EAAEN,IAAI,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1G;EAEOK,0BAA0BA,CAACF,EAAE,EAAEN,IAAI,EAAEC,QAAQ;IAChD,IAAI,CAACJ,WAAW,CAACU,GAAG,CAAC,GAAG,IAAI,CAACT,SAAS,aAAaQ,EAAE,EAAE,EAAC,EAAE,EAAEN,IAAI,EAAE,EAAE,EAAEC,QAAQ,CAAC;EACnF;EAEOQ,0BAA0BA,CAACH,EAAE,EAAEN,IAAI,EAAEC,QAAQ;IAChD,IAAI,CAACJ,WAAW,CAACU,GAAG,CAAC,GAAG,IAAI,CAACT,SAAS,YAAYQ,EAAE,EAAE,EAAC,EAAE,EAAEN,IAAI,EAAE,EAAE,EAAEC,QAAQ,CAAC;EAClF;EAEOS,sBAAsBA,CAACJ,EAAE,EAAEL,QAAQ,EAAEC,aAAuB,EAAEC,eAA0B;IAC3F,IAAI,CAACN,WAAW,CAACc,GAAG,CAAC,GAAG,IAAI,CAACb,SAAS,IAAIQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEL,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrG;EAEOS,2BAA2BA,CAACC,IAAI,EAAEZ,QAAQ;IAC7C,IAAI,CAACJ,WAAW,CAACc,GAAG,CAAC,GAAG,IAAI,CAACb,SAAS,aAAa,EAAE,EAAE,EAAE;MAACe;IAAI,CAAC,EAAEZ,QAAQ,CAAC;EAC9E;EAEOa,gBAAgBA,CAACb,QAAQ;IAC5B,IAAI,CAACJ,WAAW,CAACc,GAAG,CAAC,GAAG,IAAI,CAACb,SAAS,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAEG,QAAQ,CAAC;EAC/E;EAEOc,OAAOA,CAACf,IAAI,EAAEC,QAAQ,EAAEC,aAAuB,EAAEC,eAA0B;IAC9E,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,UAAU,EAAE;MAACkB,OAAO,EAAE;IAAM,CAAC,EAAEhB,IAAI,EAAE,EAAE,EAAEC,QAAQ,EAACC,aAAa,EAAEC,eAAe,CAAC;EAC5H;EAEOc,UAAUA,CAACjB,IAAI,EAAEC,QAAkB,EAAEC,aAAuB;IAC/D,IAAI,CAACL,WAAW,CAACqB,wBAAwB,CAAC,GAAG,IAAI,CAACpB,SAAS,SAAS,EAAE;MAACkB,OAAO,EAAE;IAAM,CAAC,EAAEhB,IAAI,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,CAAC;EAC/H;;;uBAxCSP,aAAa,EAAAwB,EAAA,CAAAC,QAAA,CAEF1B,WAAW;IAAA;EAAA;;;aAFtBC,aAAa;MAAA0B,OAAA,EAAb1B,aAAa,CAAA2B;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}