{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class CommuneAddressService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/address\";\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/commune`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function CommuneAddressService_Factory(t) {\n      return new (t || CommuneAddressService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CommuneAddressService,\n      factory: CommuneAddressService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "CommuneAddressService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\address\\CommuneAddressService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class CommuneAddressService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/address\";\r\n    }\r\n    public search(params: any, callback: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/commune`, {}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,qBAAqB;EAE9BC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,UAAU;EAC/B;EACOC,MAAMA,CAACC,MAAW,EAAEC,QAAkB,EAAEC,aAAwB,EAAEC,eAA0B;IAC/F,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,UAAU,EAAE,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1G;;;uBAPSR,qBAAqB,EAAAU,EAAA,CAAAC,QAAA,CAEVZ,WAAW;IAAA;EAAA;;;aAFtBC,qBAAqB;MAAAY,OAAA,EAArBZ,qBAAqB,CAAAa;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}