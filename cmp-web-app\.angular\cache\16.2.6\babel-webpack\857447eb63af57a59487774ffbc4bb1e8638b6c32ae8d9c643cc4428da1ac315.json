{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\nfunction Sidebar_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_button_4_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sidebar-close-icon\");\n  }\n}\nfunction Sidebar_div_0_button_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Sidebar_div_0_button_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Sidebar_div_0_button_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Sidebar_div_0_button_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, Sidebar_div_0_button_4_span_2_1_Template, 1, 0, null, 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.closeIconTemplate);\n  }\n}\nfunction Sidebar_div_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function Sidebar_div_0_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.close($event));\n    })(\"keydown.enter\", function Sidebar_div_0_button_4_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.close($event));\n    });\n    i0.ɵɵtemplate(1, Sidebar_div_0_button_4_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 9);\n    i0.ɵɵtemplate(2, Sidebar_div_0_button_4_span_2_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.ariaCloseLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.closeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.closeIconTemplate);\n  }\n}\nfunction Sidebar_div_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c0 = function (a1, a2, a3, a4, a5, a6) {\n  return {\n    \"p-sidebar\": true,\n    \"p-sidebar-active\": a1,\n    \"p-sidebar-left\": a2,\n    \"p-sidebar-right\": a3,\n    \"p-sidebar-top\": a4,\n    \"p-sidebar-bottom\": a5,\n    \"p-sidebar-full\": a6\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    transform: a0,\n    transition: a1\n  };\n};\nconst _c2 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nfunction Sidebar_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"@panelState.start\", function Sidebar_div_0_Template_div_animation_panelState_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onAnimationStart($event));\n    })(\"@panelState.done\", function Sidebar_div_0_Template_div_animation_panelState_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵtemplate(3, Sidebar_div_0_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵtemplate(4, Sidebar_div_0_button_4_Template, 3, 3, \"button\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵprojection(6);\n    i0.ɵɵtemplate(7, Sidebar_div_0_ng_container_7_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 7);\n    i0.ɵɵtemplate(9, Sidebar_div_0_ng_container_9_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction6(10, _c0, ctx_r0.visible, ctx_r0.position === \"left\" && !ctx_r0.fullScreen, ctx_r0.position === \"right\" && !ctx_r0.fullScreen, ctx_r0.position === \"top\" && !ctx_r0.fullScreen, ctx_r0.position === \"bottom\" && !ctx_r0.fullScreen, ctx_r0.fullScreen))(\"@panelState\", i0.ɵɵpureFunction1(20, _c2, i0.ɵɵpureFunction2(17, _c1, ctx_r0.transformOptions, ctx_r0.transitionOptions)))(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵattribute(\"aria-modal\", ctx_r0.modal);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCloseIcon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate);\n  }\n}\nconst _c3 = [\"*\"];\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * Sidebar is a panel component displayed as an overlay at the edges of the screen.\n * @group Components\n */\nclass Sidebar {\n  document;\n  el;\n  renderer;\n  cd;\n  config;\n  /**\n   *  Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Whether to block scrolling of the document when sidebar is active.\n   * @group Props\n   */\n  blockScroll = false;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Aria label of the close icon.\n   * @group Props\n   */\n  ariaCloseLabel;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether an overlay mask is displayed behind the sidebar.\n   * @group Props\n   */\n  modal = true;\n  /**\n   * Whether to dismiss sidebar on click of the mask.\n   * @group Props\n   */\n  dismissible = true;\n  /**\n   * Whether to display the close icon.\n   * @group Props\n   */\n  showCloseIcon = true;\n  /**\n   * Specifies if pressing escape key should hide the sidebar.\n   * @group Props\n   */\n  closeOnEscape = true;\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Specifies the visibility of the dialog.\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(val) {\n    this._visible = val;\n  }\n  /**\n   * Specifies the position of the sidebar, valid values are \"left\", \"right\", \"bottom\" and \"top\".\n   * @group Props\n   */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    this._position = value;\n    switch (value) {\n      case 'left':\n        this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n        break;\n      case 'right':\n        this.transformOptions = 'translate3d(100%, 0px, 0px)';\n        break;\n      case 'bottom':\n        this.transformOptions = 'translate3d(0px, 100%, 0px)';\n        break;\n      case 'top':\n        this.transformOptions = 'translate3d(0px, -100%, 0px)';\n        break;\n    }\n  }\n  /**\n   * Adds a close icon to the header to hide the dialog.\n   * @group Props\n   */\n  get fullScreen() {\n    return this._fullScreen;\n  }\n  set fullScreen(value) {\n    this._fullScreen = value;\n    if (value) this.transformOptions = 'none';\n  }\n  templates;\n  /**\n   * Callback to invoke when dialog is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when dialog is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * Callback to invoke when dialog visibility is changed.\n   * @param {boolean} value - Visible value.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  initialized;\n  _visible;\n  _position = 'left';\n  _fullScreen = false;\n  container;\n  transformOptions = 'translate3d(-100%, 0px, 0px)';\n  mask;\n  maskClickListener;\n  documentEscapeListener;\n  animationEndListener;\n  contentTemplate;\n  headerTemplate;\n  footerTemplate;\n  closeIconTemplate;\n  constructor(document, el, renderer, cd, config) {\n    this.document = document;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n  }\n  ngAfterViewInit() {\n    this.initialized = true;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n        case 'closeicon':\n          this.closeIconTemplate = item.template;\n          break;\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  show() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n    }\n    if (this.modal) {\n      this.enableModality();\n    }\n    this.onShow.emit({});\n    this.visibleChange.emit(true);\n  }\n  hide(emit = true) {\n    if (emit) {\n      this.onHide.emit({});\n    }\n    if (this.modal) {\n      this.disableModality();\n    }\n  }\n  close(event) {\n    this.hide();\n    this.visibleChange.emit(false);\n    event.preventDefault();\n  }\n  enableModality() {\n    if (!this.mask) {\n      this.mask = this.renderer.createElement('div');\n      this.renderer.setStyle(this.mask, 'zIndex', String(parseInt(this.container.style.zIndex) - 1));\n      DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n      if (this.dismissible) {\n        this.maskClickListener = this.renderer.listen(this.mask, 'click', event => {\n          if (this.dismissible) {\n            this.close(event);\n          }\n        });\n      }\n      this.renderer.appendChild(this.document.body, this.mask);\n      if (this.blockScroll) {\n        DomHandler.addClass(document.body, 'p-overflow-hidden');\n      }\n    }\n  }\n  disableModality() {\n    if (this.mask) {\n      DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n      this.animationEndListener = this.renderer.listen(this.mask, 'animationend', this.destroyModal.bind(this));\n    }\n  }\n  destroyModal() {\n    this.unbindMaskClickListener();\n    if (this.mask) {\n      this.renderer.removeChild(this.document.body, this.mask);\n    }\n    if (this.blockScroll) {\n      DomHandler.removeClass(document.body, 'p-overflow-hidden');\n    }\n    this.unbindAnimationEndListener();\n    this.mask = null;\n  }\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.container = event.element;\n        this.appendContainer();\n        this.show();\n        if (this.closeOnEscape) {\n          this.bindDocumentEscapeListener();\n        }\n        break;\n    }\n  }\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.hide(false);\n        ZIndexUtils.clear(this.container);\n        this.unbindGlobalListeners();\n        break;\n    }\n  }\n  appendContainer() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n  bindDocumentEscapeListener() {\n    const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n    this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n      if (event.which == 27) {\n        if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container)) {\n          this.close(event);\n        }\n      }\n    });\n  }\n  unbindDocumentEscapeListener() {\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n      this.documentEscapeListener = null;\n    }\n  }\n  unbindMaskClickListener() {\n    if (this.maskClickListener) {\n      this.maskClickListener();\n      this.maskClickListener = null;\n    }\n  }\n  unbindGlobalListeners() {\n    this.unbindMaskClickListener();\n    this.unbindDocumentEscapeListener();\n  }\n  unbindAnimationEndListener() {\n    if (this.animationEndListener && this.mask) {\n      this.animationEndListener();\n      this.animationEndListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.initialized = false;\n    if (this.visible && this.modal) {\n      this.destroyModal();\n    }\n    if (this.appendTo && this.container) {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.container = null;\n    this.unbindGlobalListeners();\n    this.unbindAnimationEndListener();\n  }\n  static ɵfac = function Sidebar_Factory(t) {\n    return new (t || Sidebar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Sidebar,\n    selectors: [[\"p-sidebar\"]],\n    contentQueries: function Sidebar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      appendTo: \"appendTo\",\n      blockScroll: \"blockScroll\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      ariaCloseLabel: \"ariaCloseLabel\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      modal: \"modal\",\n      dismissible: \"dismissible\",\n      showCloseIcon: \"showCloseIcon\",\n      closeOnEscape: \"closeOnEscape\",\n      transitionOptions: \"transitionOptions\",\n      visible: \"visible\",\n      position: \"position\",\n      fullScreen: \"fullScreen\"\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\",\n      visibleChange: \"visibleChange\"\n    },\n    ngContentSelectors: _c3,\n    decls: 1,\n    vars: 1,\n    consts: [[\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [1, \"p-sidebar-header\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"class\", \"p-sidebar-close p-sidebar-icon p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-sidebar-content\"], [1, \"p-sidebar-footer\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-sidebar-close\", \"p-sidebar-icon\", \"p-link\", 3, \"click\", \"keydown.enter\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-sidebar-close-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-sidebar-close-icon\"]],\n    template: function Sidebar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Sidebar_div_0_Template, 10, 22, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.visible);\n      }\n    },\n    dependencies: function () {\n      return [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, TimesIcon];\n    },\n    styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n    },\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Sidebar, [{\n    type: Component,\n    args: [{\n      selector: 'p-sidebar',\n      template: `\n        <div\n            #container\n            [ngClass]=\"{\n                'p-sidebar': true,\n                'p-sidebar-active': visible,\n                'p-sidebar-left': position === 'left' && !fullScreen,\n                'p-sidebar-right': position === 'right' && !fullScreen,\n                'p-sidebar-top': position === 'top' && !fullScreen,\n                'p-sidebar-bottom': position === 'bottom' && !fullScreen,\n                'p-sidebar-full': fullScreen\n            }\"\n            *ngIf=\"visible\"\n            [@panelState]=\"{ value: 'visible', params: { transform: transformOptions, transition: transitionOptions } }\"\n            (@panelState.start)=\"onAnimationStart($event)\"\n            (@panelState.done)=\"onAnimationEnd($event)\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            role=\"complementary\"\n            [attr.aria-modal]=\"modal\"\n        >\n            <div class=\"p-sidebar-header\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <button type=\"button\" class=\"p-sidebar-close p-sidebar-icon p-link\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\" [attr.aria-label]=\"ariaCloseLabel\" *ngIf=\"showCloseIcon\" pRipple>\n                    <TimesIcon *ngIf=\"!closeIconTemplate\" [styleClass]=\"'p-sidebar-close-icon'\" />\n                    <span *ngIf=\"closeIconTemplate\" class=\"p-sidebar-close-icon\">\n                        <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                    </span>\n                </button>\n            </div>\n            <div class=\"p-sidebar-content\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <div class=\"p-sidebar-footer\">\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.PrimeNGConfig\n    }];\n  }, {\n    appendTo: [{\n      type: Input\n    }],\n    blockScroll: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    ariaCloseLabel: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    modal: [{\n      type: Input\n    }],\n    dismissible: [{\n      type: Input\n    }],\n    showCloseIcon: [{\n      type: Input\n    }],\n    closeOnEscape: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }]\n  });\n})();\nclass SidebarModule {\n  static ɵfac = function SidebarModule_Factory(t) {\n    return new (t || SidebarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SidebarModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RippleModule, SharedModule, TimesIcon, SharedModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SidebarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RippleModule, SharedModule, TimesIcon],\n      exports: [Sidebar, SharedModule],\n      declarations: [Sidebar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Sidebar, SidebarModule };", "map": {"version": 3, "names": ["animation", "style", "animate", "trigger", "transition", "useAnimation", "i2", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "ContentChildren", "Output", "NgModule", "i1", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "TimesIcon", "i3", "RippleModule", "ZIndexUtils", "Sidebar_div_0_ng_container_3_Template", "rf", "ctx", "ɵɵelementContainer", "Sidebar_div_0_button_4_TimesIcon_1_Template", "ɵɵelement", "ɵɵproperty", "Sidebar_div_0_button_4_span_2_1_ng_template_0_Template", "Sidebar_div_0_button_4_span_2_1_Template", "ɵɵtemplate", "Sidebar_div_0_button_4_span_2_Template", "ɵɵelementStart", "ɵɵelementEnd", "ctx_r7", "ɵɵnextContext", "ɵɵadvance", "closeIconTemplate", "Sidebar_div_0_button_4_Template", "_r11", "ɵɵgetCurrentView", "ɵɵlistener", "Sidebar_div_0_button_4_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "ctx_r10", "ɵɵresetView", "close", "Sidebar_div_0_button_4_Template_button_keydown_enter_0_listener", "ctx_r12", "ctx_r3", "ɵɵattribute", "ariaCloseLabel", "Sidebar_div_0_ng_container_7_Template", "Sidebar_div_0_ng_container_9_Template", "_c0", "a1", "a2", "a3", "a4", "a5", "a6", "_c1", "a0", "transform", "_c2", "value", "params", "Sidebar_div_0_Template", "_r14", "Sidebar_div_0_Template_div_animation_panelState_start_0_listener", "ctx_r13", "onAnimationStart", "Sidebar_div_0_Template_div_animation_panelState_done_0_listener", "ctx_r15", "onAnimationEnd", "ɵɵprojection", "ctx_r0", "ɵɵclassMap", "styleClass", "ɵɵpureFunction6", "visible", "position", "fullScreen", "ɵɵpureFunction1", "ɵɵpureFunction2", "transformOptions", "transitionOptions", "modal", "headerTemplate", "showCloseIcon", "contentTemplate", "footerTemplate", "_c3", "showAnimation", "opacity", "hideAnimation", "Sidebar", "document", "el", "renderer", "cd", "config", "appendTo", "blockScroll", "autoZIndex", "baseZIndex", "dismissible", "closeOnEscape", "_visible", "val", "_position", "_fullScreen", "templates", "onShow", "onHide", "visibleChange", "initialized", "container", "mask", "maskClickListener", "documentEscapeListener", "animationEndListener", "constructor", "ngAfterViewInit", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "show", "set", "zIndex", "enableModality", "emit", "hide", "disableModality", "event", "preventDefault", "createElement", "setStyle", "String", "parseInt", "addMultipleClasses", "listen", "append<PERSON><PERSON><PERSON>", "body", "addClass", "destroyModal", "bind", "unbindMaskClickListener", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "unbindAnimationEndListener", "toState", "element", "append<PERSON><PERSON><PERSON>", "bindDocumentEscapeListener", "clear", "unbindGlobalListeners", "documentTarget", "nativeElement", "ownerDocument", "which", "get", "unbindDocumentEscapeListener", "ngOnDestroy", "ɵfac", "Sidebar_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "ChangeDetectorRef", "PrimeNGConfig", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Sidebar_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "outputs", "ngContentSelectors", "decls", "vars", "consts", "Sidebar_Template", "ɵɵprojectionDef", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "styles", "encapsulation", "data", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "animations", "OnPush", "None", "host", "class", "Document", "decorators", "SidebarModule", "SidebarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/primeng/fesm2022/primeng-sidebar.mjs"], "sourcesContent": ["import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\n\nconst showAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n/**\n * Sidebar is a panel component displayed as an overlay at the edges of the screen.\n * @group Components\n */\nclass Sidebar {\n    document;\n    el;\n    renderer;\n    cd;\n    config;\n    /**\n     *  Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Whether to block scrolling of the document when sidebar is active.\n     * @group Props\n     */\n    blockScroll = false;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Aria label of the close icon.\n     * @group Props\n     */\n    ariaCloseLabel;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether an overlay mask is displayed behind the sidebar.\n     * @group Props\n     */\n    modal = true;\n    /**\n     * Whether to dismiss sidebar on click of the mask.\n     * @group Props\n     */\n    dismissible = true;\n    /**\n     * Whether to display the close icon.\n     * @group Props\n     */\n    showCloseIcon = true;\n    /**\n     * Specifies if pressing escape key should hide the sidebar.\n     * @group Props\n     */\n    closeOnEscape = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Specifies the visibility of the dialog.\n     * @group Props\n     */\n    get visible() {\n        return this._visible;\n    }\n    set visible(val) {\n        this._visible = val;\n    }\n    /**\n     * Specifies the position of the sidebar, valid values are \"left\", \"right\", \"bottom\" and \"top\".\n     * @group Props\n     */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        this._position = value;\n        switch (value) {\n            case 'left':\n                this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n                break;\n            case 'right':\n                this.transformOptions = 'translate3d(100%, 0px, 0px)';\n                break;\n            case 'bottom':\n                this.transformOptions = 'translate3d(0px, 100%, 0px)';\n                break;\n            case 'top':\n                this.transformOptions = 'translate3d(0px, -100%, 0px)';\n                break;\n        }\n    }\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    get fullScreen() {\n        return this._fullScreen;\n    }\n    set fullScreen(value) {\n        this._fullScreen = value;\n        if (value)\n            this.transformOptions = 'none';\n    }\n    templates;\n    /**\n     * Callback to invoke when dialog is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when dialog visibility is changed.\n     * @param {boolean} value - Visible value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    initialized;\n    _visible;\n    _position = 'left';\n    _fullScreen = false;\n    container;\n    transformOptions = 'translate3d(-100%, 0px, 0px)';\n    mask;\n    maskClickListener;\n    documentEscapeListener;\n    animationEndListener;\n    contentTemplate;\n    headerTemplate;\n    footerTemplate;\n    closeIconTemplate;\n    constructor(document, el, renderer, cd, config) {\n        this.document = document;\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n    }\n    ngAfterViewInit() {\n        this.initialized = true;\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    show() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n        }\n        if (this.modal) {\n            this.enableModality();\n        }\n        this.onShow.emit({});\n        this.visibleChange.emit(true);\n    }\n    hide(emit = true) {\n        if (emit) {\n            this.onHide.emit({});\n        }\n        if (this.modal) {\n            this.disableModality();\n        }\n    }\n    close(event) {\n        this.hide();\n        this.visibleChange.emit(false);\n        event.preventDefault();\n    }\n    enableModality() {\n        if (!this.mask) {\n            this.mask = this.renderer.createElement('div');\n            this.renderer.setStyle(this.mask, 'zIndex', String(parseInt(this.container.style.zIndex) - 1));\n            DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n            if (this.dismissible) {\n                this.maskClickListener = this.renderer.listen(this.mask, 'click', (event) => {\n                    if (this.dismissible) {\n                        this.close(event);\n                    }\n                });\n            }\n            this.renderer.appendChild(this.document.body, this.mask);\n            if (this.blockScroll) {\n                DomHandler.addClass(document.body, 'p-overflow-hidden');\n            }\n        }\n    }\n    disableModality() {\n        if (this.mask) {\n            DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n            this.animationEndListener = this.renderer.listen(this.mask, 'animationend', this.destroyModal.bind(this));\n        }\n    }\n    destroyModal() {\n        this.unbindMaskClickListener();\n        if (this.mask) {\n            this.renderer.removeChild(this.document.body, this.mask);\n        }\n        if (this.blockScroll) {\n            DomHandler.removeClass(document.body, 'p-overflow-hidden');\n        }\n        this.unbindAnimationEndListener();\n        this.mask = null;\n    }\n    onAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.appendContainer();\n                this.show();\n                if (this.closeOnEscape) {\n                    this.bindDocumentEscapeListener();\n                }\n                break;\n        }\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                this.hide(false);\n                ZIndexUtils.clear(this.container);\n                this.unbindGlobalListeners();\n                break;\n        }\n    }\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                this.renderer.appendChild(this.document.body, this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    bindDocumentEscapeListener() {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n        this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', (event) => {\n            if (event.which == 27) {\n                if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container)) {\n                    this.close(event);\n                }\n            }\n        });\n    }\n    unbindDocumentEscapeListener() {\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n            this.documentEscapeListener = null;\n        }\n    }\n    unbindMaskClickListener() {\n        if (this.maskClickListener) {\n            this.maskClickListener();\n            this.maskClickListener = null;\n        }\n    }\n    unbindGlobalListeners() {\n        this.unbindMaskClickListener();\n        this.unbindDocumentEscapeListener();\n    }\n    unbindAnimationEndListener() {\n        if (this.animationEndListener && this.mask) {\n            this.animationEndListener();\n            this.animationEndListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.initialized = false;\n        if (this.visible && this.modal) {\n            this.destroyModal();\n        }\n        if (this.appendTo && this.container) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.container = null;\n        this.unbindGlobalListeners();\n        this.unbindAnimationEndListener();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: Sidebar, deps: [{ token: DOCUMENT }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.2\", type: Sidebar, selector: \"p-sidebar\", inputs: { appendTo: \"appendTo\", blockScroll: \"blockScroll\", style: \"style\", styleClass: \"styleClass\", ariaCloseLabel: \"ariaCloseLabel\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", modal: \"modal\", dismissible: \"dismissible\", showCloseIcon: \"showCloseIcon\", closeOnEscape: \"closeOnEscape\", transitionOptions: \"transitionOptions\", visible: \"visible\", position: \"position\", fullScreen: \"fullScreen\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\", visibleChange: \"visibleChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div\n            #container\n            [ngClass]=\"{\n                'p-sidebar': true,\n                'p-sidebar-active': visible,\n                'p-sidebar-left': position === 'left' && !fullScreen,\n                'p-sidebar-right': position === 'right' && !fullScreen,\n                'p-sidebar-top': position === 'top' && !fullScreen,\n                'p-sidebar-bottom': position === 'bottom' && !fullScreen,\n                'p-sidebar-full': fullScreen\n            }\"\n            *ngIf=\"visible\"\n            [@panelState]=\"{ value: 'visible', params: { transform: transformOptions, transition: transitionOptions } }\"\n            (@panelState.start)=\"onAnimationStart($event)\"\n            (@panelState.done)=\"onAnimationEnd($event)\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            role=\"complementary\"\n            [attr.aria-modal]=\"modal\"\n        >\n            <div class=\"p-sidebar-header\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <button type=\"button\" class=\"p-sidebar-close p-sidebar-icon p-link\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\" [attr.aria-label]=\"ariaCloseLabel\" *ngIf=\"showCloseIcon\" pRipple>\n                    <TimesIcon *ngIf=\"!closeIconTemplate\" [styleClass]=\"'p-sidebar-close-icon'\" />\n                    <span *ngIf=\"closeIconTemplate\" class=\"p-sidebar-close-icon\">\n                        <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                    </span>\n                </button>\n            </div>\n            <div class=\"p-sidebar-content\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <div class=\"p-sidebar-footer\">\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i2.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgTemplateOutlet; }), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i3.Ripple; }), selector: \"[pRipple]\" }, { kind: \"component\", type: i0.forwardRef(function () { return TimesIcon; }), selector: \"TimesIcon\" }], animations: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: Sidebar, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-sidebar', template: `\n        <div\n            #container\n            [ngClass]=\"{\n                'p-sidebar': true,\n                'p-sidebar-active': visible,\n                'p-sidebar-left': position === 'left' && !fullScreen,\n                'p-sidebar-right': position === 'right' && !fullScreen,\n                'p-sidebar-top': position === 'top' && !fullScreen,\n                'p-sidebar-bottom': position === 'bottom' && !fullScreen,\n                'p-sidebar-full': fullScreen\n            }\"\n            *ngIf=\"visible\"\n            [@panelState]=\"{ value: 'visible', params: { transform: transformOptions, transition: transitionOptions } }\"\n            (@panelState.start)=\"onAnimationStart($event)\"\n            (@panelState.done)=\"onAnimationEnd($event)\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            role=\"complementary\"\n            [attr.aria-modal]=\"modal\"\n        >\n            <div class=\"p-sidebar-header\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <button type=\"button\" class=\"p-sidebar-close p-sidebar-icon p-link\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\" [attr.aria-label]=\"ariaCloseLabel\" *ngIf=\"showCloseIcon\" pRipple>\n                    <TimesIcon *ngIf=\"!closeIconTemplate\" [styleClass]=\"'p-sidebar-close-icon'\" />\n                    <span *ngIf=\"closeIconTemplate\" class=\"p-sidebar-close-icon\">\n                        <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                    </span>\n                </button>\n            </div>\n            <div class=\"p-sidebar-content\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <div class=\"p-sidebar-footer\">\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `, animations: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i1.PrimeNGConfig }]; }, propDecorators: { appendTo: [{\n                type: Input\n            }], blockScroll: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], ariaCloseLabel: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], modal: [{\n                type: Input\n            }], dismissible: [{\n                type: Input\n            }], showCloseIcon: [{\n                type: Input\n            }], closeOnEscape: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }], visible: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }], fullScreen: [{\n                type: Input\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], visibleChange: [{\n                type: Output\n            }] } });\nclass SidebarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: SidebarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.2\", ngImport: i0, type: SidebarModule, declarations: [Sidebar], imports: [CommonModule, RippleModule, SharedModule, TimesIcon], exports: [Sidebar, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: SidebarModule, imports: [CommonModule, RippleModule, SharedModule, TimesIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: SidebarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RippleModule, SharedModule, TimesIcon],\n                    exports: [Sidebar, SharedModule],\n                    declarations: [Sidebar]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Sidebar, SidebarModule };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,YAAY,QAAQ,qBAAqB;AAClG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,eAAe,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACrJ,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA4TiDnB,EAAE,CAAAqB,kBAAA,EAuBhB,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBanB,EAAE,CAAAuB,SAAA,mBAyBE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAzBLnB,EAAE,CAAAwB,UAAA,qCAyBD,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAAN,EAAA,EAAAC,GAAA;AAAA,SAAAM,yCAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzBFnB,EAAE,CAAA2B,UAAA,IAAAF,sDAAA,qBA2BP,CAAC;EAAA;AAAA;AAAA,SAAAG,uCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3BInB,EAAE,CAAA6B,cAAA,cA0Bf,CAAC;IA1BY7B,EAAE,CAAA2B,UAAA,IAAAD,wCAAA,eA2BP,CAAC;IA3BI1B,EAAE,CAAA8B,YAAA,CA4BrE,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAY,MAAA,GA5BkE/B,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EA2BvB,CAAC;IA3BoBjC,EAAE,CAAAwB,UAAA,qBAAAO,MAAA,CAAAG,iBA2BvB,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiB,IAAA,GA3BoBpC,EAAE,CAAAqC,gBAAA;IAAFrC,EAAE,CAAA6B,cAAA,eAwB6G,CAAC;IAxBhH7B,EAAE,CAAAsC,UAAA,mBAAAC,wDAAAC,MAAA;MAAFxC,EAAE,CAAAyC,aAAA,CAAAL,IAAA;MAAA,MAAAM,OAAA,GAAF1C,EAAE,CAAAgC,aAAA;MAAA,OAAFhC,EAAE,CAAA2C,WAAA,CAwBFD,OAAA,CAAAE,KAAA,CAAAJ,MAAY,EAAC;IAAA,EAAC,2BAAAK,gEAAAL,MAAA;MAxBdxC,EAAE,CAAAyC,aAAA,CAAAL,IAAA;MAAA,MAAAU,OAAA,GAAF9C,EAAE,CAAAgC,aAAA;MAAA,OAAFhC,EAAE,CAAA2C,WAAA,CAwB8BG,OAAA,CAAAF,KAAA,CAAAJ,MAAY,EAAC;IAAA,CAAhC,CAAC;IAxBdxC,EAAE,CAAA2B,UAAA,IAAAL,2CAAA,sBAyBE,CAAC;IAzBLtB,EAAE,CAAA2B,UAAA,IAAAC,sCAAA,kBA4BrE,CAAC;IA5BkE5B,EAAE,CAAA8B,YAAA,CA6BvE,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAA4B,MAAA,GA7BoE/C,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAgD,WAAA,eAAAD,MAAA,CAAAE,cAwB8E,CAAC;IAxBjFjD,EAAE,CAAAiC,SAAA,EAyBxC,CAAC;IAzBqCjC,EAAE,CAAAwB,UAAA,UAAAuB,MAAA,CAAAb,iBAyBxC,CAAC;IAzBqClC,EAAE,CAAAiC,SAAA,EA0B9C,CAAC;IA1B2CjC,EAAE,CAAAwB,UAAA,SAAAuB,MAAA,CAAAb,iBA0B9C,CAAC;EAAA;AAAA;AAAA,SAAAgB,sCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1B2CnB,EAAE,CAAAqB,kBAAA,EAiCf,CAAC;EAAA;AAAA;AAAA,SAAA8B,sCAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCYnB,EAAE,CAAAqB,kBAAA,EAoChB,CAAC;EAAA;AAAA;AAAA,MAAA+B,GAAA,YAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;IAAA;IAAA,oBAAAL,EAAA;IAAA,kBAAAC,EAAA;IAAA,mBAAAC,EAAA;IAAA,iBAAAC,EAAA;IAAA,oBAAAC,EAAA;IAAA,kBAAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA,EAAAP,EAAA;EAAA;IAAAQ,SAAA,EAAAD,EAAA;IAAAjE,UAAA,EAAA0D;EAAA;AAAA;AAAA,MAAAS,GAAA,YAAAA,CAAAT,EAAA;EAAA;IAAAU,KAAA;IAAAC,MAAA,EAAAX;EAAA;AAAA;AAAA,SAAAY,uBAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+C,IAAA,GApCalE,EAAE,CAAAqC,gBAAA;IAAFrC,EAAE,CAAA6B,cAAA,eAqBvF,CAAC;IArBoF7B,EAAE,CAAAsC,UAAA,+BAAA6B,iEAAA3B,MAAA;MAAFxC,EAAE,CAAAyC,aAAA,CAAAyB,IAAA;MAAA,MAAAE,OAAA,GAAFpE,EAAE,CAAAgC,aAAA;MAAA,OAAFhC,EAAE,CAAA2C,WAAA,CAe9DyB,OAAA,CAAAC,gBAAA,CAAA7B,MAAuB,EAAC;IAAA,EAAC,8BAAA8B,gEAAA9B,MAAA;MAfmCxC,EAAE,CAAAyC,aAAA,CAAAyB,IAAA;MAAA,MAAAK,OAAA,GAAFvE,EAAE,CAAAgC,aAAA;MAAA,OAAFhC,EAAE,CAAA2C,WAAA,CAgB/D4B,OAAA,CAAAC,cAAA,CAAAhC,MAAqB,EAAC;IAAA,CADG,CAAC;IAfmCxC,EAAE,CAAA6B,cAAA,YAsBtD,CAAC;IAtBmD7B,EAAE,CAAA2B,UAAA,IAAAT,qCAAA,yBAuBhB,CAAC;IAvBalB,EAAE,CAAA2B,UAAA,IAAAQ,+BAAA,mBA6BvE,CAAC;IA7BoEnC,EAAE,CAAA8B,YAAA,CA8B9E,CAAC;IA9B2E9B,EAAE,CAAA6B,cAAA,YA+BrD,CAAC;IA/BkD7B,EAAE,CAAAyE,YAAA,EAgCvD,CAAC;IAhCoDzE,EAAE,CAAA2B,UAAA,IAAAuB,qCAAA,yBAiCf,CAAC;IAjCYlD,EAAE,CAAA8B,YAAA,CAkC9E,CAAC;IAlC2E9B,EAAE,CAAA6B,cAAA,YAmCtD,CAAC;IAnCmD7B,EAAE,CAAA2B,UAAA,IAAAwB,qCAAA,yBAoChB,CAAC;IApCanD,EAAE,CAAA8B,YAAA,CAqC9E,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAuD,MAAA,GArC2E1E,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAA2E,UAAA,CAAAD,MAAA,CAAAE,UAkBhE,CAAC;IAlB6D5E,EAAE,CAAAwB,UAAA,YAAFxB,EAAE,CAAA6E,eAAA,KAAAzB,GAAA,EAAAsB,MAAA,CAAAI,OAAA,EAAAJ,MAAA,CAAAK,QAAA,gBAAAL,MAAA,CAAAM,UAAA,EAAAN,MAAA,CAAAK,QAAA,iBAAAL,MAAA,CAAAM,UAAA,EAAAN,MAAA,CAAAK,QAAA,eAAAL,MAAA,CAAAM,UAAA,EAAAN,MAAA,CAAAK,QAAA,kBAAAL,MAAA,CAAAM,UAAA,EAAAN,MAAA,CAAAM,UAAA,CAYlF,CAAC,gBAZ+EhF,EAAE,CAAAiF,eAAA,KAAAnB,GAAA,EAAF9D,EAAE,CAAAkF,eAAA,KAAAvB,GAAA,EAAAe,MAAA,CAAAS,gBAAA,EAAAT,MAAA,CAAAU,iBAAA,EAYlF,CAAC,YAAAV,MAAA,CAAAlF,KAAD,CAAC;IAZ+EQ,EAAE,CAAAgD,WAAA,eAAA0B,MAAA,CAAAW,KAoB3D,CAAC;IApBwDrF,EAAE,CAAAiC,SAAA,EAuBjC,CAAC;IAvB8BjC,EAAE,CAAAwB,UAAA,qBAAAkD,MAAA,CAAAY,cAuBjC,CAAC;IAvB8BtF,EAAE,CAAAiC,SAAA,EAwBmG,CAAC;IAxBtGjC,EAAE,CAAAwB,UAAA,SAAAkD,MAAA,CAAAa,aAwBmG,CAAC;IAxBtGvF,EAAE,CAAAiC,SAAA,EAiChC,CAAC;IAjC6BjC,EAAE,CAAAwB,UAAA,qBAAAkD,MAAA,CAAAc,eAiChC,CAAC;IAjC6BxF,EAAE,CAAAiC,SAAA,EAoCjC,CAAC;IApC8BjC,EAAE,CAAAwB,UAAA,qBAAAkD,MAAA,CAAAe,cAoCjC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AA9V/D,MAAMC,aAAa,GAAGpG,SAAS,CAAC,CAACC,KAAK,CAAC;EAAEqE,SAAS,EAAE,eAAe;EAAE+B,OAAO,EAAE;AAAE,CAAC,CAAC,EAAEnG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC/G,MAAMoG,aAAa,GAAGtG,SAAS,CAAC,CAACE,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;EAAEqE,SAAS,EAAE,eAAe;EAAE+B,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G;AACA;AACA;AACA;AACA,MAAME,OAAO,CAAC;EACVC,QAAQ;EACRC,EAAE;EACFC,QAAQ;EACRC,EAAE;EACFC,MAAM;EACN;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,WAAW,GAAG,KAAK;EACnB;AACJ;AACA;AACA;EACI7G,KAAK;EACL;AACJ;AACA;AACA;EACIoF,UAAU;EACV;AACJ;AACA;AACA;EACI3B,cAAc;EACd;AACJ;AACA;AACA;EACIqD,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;EACIlB,KAAK,GAAG,IAAI;EACZ;AACJ;AACA;AACA;EACImB,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIjB,aAAa,GAAG,IAAI;EACpB;AACJ;AACA;AACA;EACIkB,aAAa,GAAG,IAAI;EACpB;AACJ;AACA;AACA;EACIrB,iBAAiB,GAAG,kCAAkC;EACtD;AACJ;AACA;AACA;EACI,IAAIN,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4B,QAAQ;EACxB;EACA,IAAI5B,OAAOA,CAAC6B,GAAG,EAAE;IACb,IAAI,CAACD,QAAQ,GAAGC,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAI5B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC6B,SAAS;EACzB;EACA,IAAI7B,QAAQA,CAAChB,KAAK,EAAE;IAChB,IAAI,CAAC6C,SAAS,GAAG7C,KAAK;IACtB,QAAQA,KAAK;MACT,KAAK,MAAM;QACP,IAAI,CAACoB,gBAAgB,GAAG,8BAA8B;QACtD;MACJ,KAAK,OAAO;QACR,IAAI,CAACA,gBAAgB,GAAG,6BAA6B;QACrD;MACJ,KAAK,QAAQ;QACT,IAAI,CAACA,gBAAgB,GAAG,6BAA6B;QACrD;MACJ,KAAK,KAAK;QACN,IAAI,CAACA,gBAAgB,GAAG,8BAA8B;QACtD;IACR;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIH,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC6B,WAAW;EAC3B;EACA,IAAI7B,UAAUA,CAACjB,KAAK,EAAE;IAClB,IAAI,CAAC8C,WAAW,GAAG9C,KAAK;IACxB,IAAIA,KAAK,EACL,IAAI,CAACoB,gBAAgB,GAAG,MAAM;EACtC;EACA2B,SAAS;EACT;AACJ;AACA;AACA;EACIC,MAAM,GAAG,IAAI9G,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACI+G,MAAM,GAAG,IAAI/G,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACIgH,aAAa,GAAG,IAAIhH,YAAY,CAAC,CAAC;EAClCiH,WAAW;EACXR,QAAQ;EACRE,SAAS,GAAG,MAAM;EAClBC,WAAW,GAAG,KAAK;EACnBM,SAAS;EACThC,gBAAgB,GAAG,8BAA8B;EACjDiC,IAAI;EACJC,iBAAiB;EACjBC,sBAAsB;EACtBC,oBAAoB;EACpB/B,eAAe;EACfF,cAAc;EACdG,cAAc;EACdvD,iBAAiB;EACjBsF,WAAWA,CAACzB,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,MAAM,EAAE;IAC5C,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAsB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACP,WAAW,GAAG,IAAI;EAC3B;EACAQ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACZ,SAAS,EAAEa,OAAO,CAAEC,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAACrC,eAAe,GAAGoC,IAAI,CAACE,QAAQ;UACpC;QACJ,KAAK,QAAQ;UACT,IAAI,CAACxC,cAAc,GAAGsC,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,QAAQ;UACT,IAAI,CAACrC,cAAc,GAAGmC,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,WAAW;UACZ,IAAI,CAAC5F,iBAAiB,GAAG0F,IAAI,CAACE,QAAQ;UACtC;QACJ;UACI,IAAI,CAACtC,eAAe,GAAGoC,IAAI,CAACE,QAAQ;UACpC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACzB,UAAU,EAAE;MACjBrF,WAAW,CAAC+G,GAAG,CAAC,OAAO,EAAE,IAAI,CAACb,SAAS,EAAE,IAAI,CAACZ,UAAU,IAAI,IAAI,CAACJ,MAAM,CAAC8B,MAAM,CAAC5C,KAAK,CAAC;IACzF;IACA,IAAI,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,CAAC6C,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACnB,MAAM,CAACoB,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,CAAClB,aAAa,CAACkB,IAAI,CAAC,IAAI,CAAC;EACjC;EACAC,IAAIA,CAACD,IAAI,GAAG,IAAI,EAAE;IACd,IAAIA,IAAI,EAAE;MACN,IAAI,CAACnB,MAAM,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAAC9C,KAAK,EAAE;MACZ,IAAI,CAACgD,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAzF,KAAKA,CAAC0F,KAAK,EAAE;IACT,IAAI,CAACF,IAAI,CAAC,CAAC;IACX,IAAI,CAACnB,aAAa,CAACkB,IAAI,CAAC,KAAK,CAAC;IAC9BG,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B;EACAL,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACd,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG,IAAI,CAACnB,QAAQ,CAACuC,aAAa,CAAC,KAAK,CAAC;MAC9C,IAAI,CAACvC,QAAQ,CAACwC,QAAQ,CAAC,IAAI,CAACrB,IAAI,EAAE,QAAQ,EAAEsB,MAAM,CAACC,QAAQ,CAAC,IAAI,CAACxB,SAAS,CAAC3H,KAAK,CAACyI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9FpH,UAAU,CAAC+H,kBAAkB,CAAC,IAAI,CAACxB,IAAI,EAAE,kFAAkF,CAAC;MAC5H,IAAI,IAAI,CAACZ,WAAW,EAAE;QAClB,IAAI,CAACa,iBAAiB,GAAG,IAAI,CAACpB,QAAQ,CAAC4C,MAAM,CAAC,IAAI,CAACzB,IAAI,EAAE,OAAO,EAAGkB,KAAK,IAAK;UACzE,IAAI,IAAI,CAAC9B,WAAW,EAAE;YAClB,IAAI,CAAC5D,KAAK,CAAC0F,KAAK,CAAC;UACrB;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACrC,QAAQ,CAAC6C,WAAW,CAAC,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,EAAE,IAAI,CAAC3B,IAAI,CAAC;MACxD,IAAI,IAAI,CAACf,WAAW,EAAE;QAClBxF,UAAU,CAACmI,QAAQ,CAACjD,QAAQ,CAACgD,IAAI,EAAE,mBAAmB,CAAC;MAC3D;IACJ;EACJ;EACAV,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjB,IAAI,EAAE;MACXvG,UAAU,CAACmI,QAAQ,CAAC,IAAI,CAAC5B,IAAI,EAAE,2BAA2B,CAAC;MAC3D,IAAI,CAACG,oBAAoB,GAAG,IAAI,CAACtB,QAAQ,CAAC4C,MAAM,CAAC,IAAI,CAACzB,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC6B,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7G;EACJ;EACAD,YAAYA,CAAA,EAAG;IACX,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAC/B,IAAI,EAAE;MACX,IAAI,CAACnB,QAAQ,CAACmD,WAAW,CAAC,IAAI,CAACrD,QAAQ,CAACgD,IAAI,EAAE,IAAI,CAAC3B,IAAI,CAAC;IAC5D;IACA,IAAI,IAAI,CAACf,WAAW,EAAE;MAClBxF,UAAU,CAACwI,WAAW,CAACtD,QAAQ,CAACgD,IAAI,EAAE,mBAAmB,CAAC;IAC9D;IACA,IAAI,CAACO,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAAClC,IAAI,GAAG,IAAI;EACpB;EACA/C,gBAAgBA,CAACiE,KAAK,EAAE;IACpB,QAAQA,KAAK,CAACiB,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACpC,SAAS,GAAGmB,KAAK,CAACkB,OAAO;QAC9B,IAAI,CAACC,eAAe,CAAC,CAAC;QACtB,IAAI,CAAC1B,IAAI,CAAC,CAAC;QACX,IAAI,IAAI,CAACtB,aAAa,EAAE;UACpB,IAAI,CAACiD,0BAA0B,CAAC,CAAC;QACrC;QACA;IACR;EACJ;EACAlF,cAAcA,CAAC8D,KAAK,EAAE;IAClB,QAAQA,KAAK,CAACiB,OAAO;MACjB,KAAK,MAAM;QACP,IAAI,CAACnB,IAAI,CAAC,KAAK,CAAC;QAChBnH,WAAW,CAAC0I,KAAK,CAAC,IAAI,CAACxC,SAAS,CAAC;QACjC,IAAI,CAACyC,qBAAqB,CAAC,CAAC;QAC5B;IACR;EACJ;EACAH,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACrD,QAAQ,EAAE;MACf,IAAI,IAAI,CAACA,QAAQ,KAAK,MAAM,EACxB,IAAI,CAACH,QAAQ,CAAC6C,WAAW,CAAC,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,EAAE,IAAI,CAAC5B,SAAS,CAAC,CAAC,KAE9DtG,UAAU,CAACiI,WAAW,CAAC,IAAI,CAAC3B,SAAS,EAAE,IAAI,CAACf,QAAQ,CAAC;IAC7D;EACJ;EACAsD,0BAA0BA,CAAA,EAAG;IACzB,MAAMG,cAAc,GAAG,IAAI,CAAC7D,EAAE,GAAG,IAAI,CAACA,EAAE,CAAC8D,aAAa,CAACC,aAAa,GAAG,IAAI,CAAChE,QAAQ;IACpF,IAAI,CAACuB,sBAAsB,GAAG,IAAI,CAACrB,QAAQ,CAAC4C,MAAM,CAACgB,cAAc,EAAE,SAAS,EAAGvB,KAAK,IAAK;MACrF,IAAIA,KAAK,CAAC0B,KAAK,IAAI,EAAE,EAAE;QACnB,IAAIrB,QAAQ,CAAC,IAAI,CAACxB,SAAS,CAAC3H,KAAK,CAACyI,MAAM,CAAC,KAAKhH,WAAW,CAACgJ,GAAG,CAAC,IAAI,CAAC9C,SAAS,CAAC,EAAE;UAC3E,IAAI,CAACvE,KAAK,CAAC0F,KAAK,CAAC;QACrB;MACJ;IACJ,CAAC,CAAC;EACN;EACA4B,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAAC5C,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACA6B,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC9B,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACA,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACAuC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACT,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACe,4BAA4B,CAAC,CAAC;EACvC;EACAZ,0BAA0BA,CAAA,EAAG;IACzB,IAAI,IAAI,CAAC/B,oBAAoB,IAAI,IAAI,CAACH,IAAI,EAAE;MACxC,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACA4C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjD,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACpC,OAAO,IAAI,IAAI,CAACO,KAAK,EAAE;MAC5B,IAAI,CAAC4D,YAAY,CAAC,CAAC;IACvB;IACA,IAAI,IAAI,CAAC7C,QAAQ,IAAI,IAAI,CAACe,SAAS,EAAE;MACjC,IAAI,CAAClB,QAAQ,CAAC6C,WAAW,CAAC,IAAI,CAAC9C,EAAE,CAAC8D,aAAa,EAAE,IAAI,CAAC3C,SAAS,CAAC;IACpE;IACA,IAAI,IAAI,CAACA,SAAS,IAAI,IAAI,CAACb,UAAU,EAAE;MACnCrF,WAAW,CAAC0I,KAAK,CAAC,IAAI,CAACxC,SAAS,CAAC;IACrC;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACyC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACN,0BAA0B,CAAC,CAAC;EACrC;EACA,OAAOc,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFxE,OAAO,EAAjB9F,EAAE,CAAAuK,iBAAA,CAAiCzK,QAAQ,GAA3CE,EAAE,CAAAuK,iBAAA,CAAsDvK,EAAE,CAACwK,UAAU,GAArExK,EAAE,CAAAuK,iBAAA,CAAgFvK,EAAE,CAACyK,SAAS,GAA9FzK,EAAE,CAAAuK,iBAAA,CAAyGvK,EAAE,CAAC0K,iBAAiB,GAA/H1K,EAAE,CAAAuK,iBAAA,CAA0I7J,EAAE,CAACiK,aAAa;EAAA;EACrP,OAAOC,IAAI,kBAD8E5K,EAAE,CAAA6K,iBAAA;IAAAC,IAAA,EACJhF,OAAO;IAAAiF,SAAA;IAAAC,cAAA,WAAAC,uBAAA9J,EAAA,EAAAC,GAAA,EAAA8J,QAAA;MAAA,IAAA/J,EAAA;QADLnB,EAAE,CAAAmL,cAAA,CAAAD,QAAA,EAC4lBvK,aAAa;MAAA;MAAA,IAAAQ,EAAA;QAAA,IAAAiK,EAAA;QAD3mBpL,EAAE,CAAAqL,cAAA,CAAAD,EAAA,GAAFpL,EAAE,CAAAsL,WAAA,QAAAlK,GAAA,CAAA0F,SAAA,GAAAsE,EAAA;MAAA;IAAA;IAAAG,SAAA;IAAAC,MAAA;MAAApF,QAAA;MAAAC,WAAA;MAAA7G,KAAA;MAAAoF,UAAA;MAAA3B,cAAA;MAAAqD,UAAA;MAAAC,UAAA;MAAAlB,KAAA;MAAAmB,WAAA;MAAAjB,aAAA;MAAAkB,aAAA;MAAArB,iBAAA;MAAAN,OAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA;IAAAyG,OAAA;MAAA1E,MAAA;MAAAC,MAAA;MAAAC,aAAA;IAAA;IAAAyE,kBAAA,EAAAhG,GAAA;IAAAiG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA/D,QAAA,WAAAgE,iBAAA3K,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnB,EAAE,CAAA+L,eAAA;QAAF/L,EAAE,CAAA2B,UAAA,IAAAsC,sBAAA,kBAsClF,CAAC;MAAA;MAAA,IAAA9C,EAAA;QAtC+EnB,EAAE,CAAAwB,UAAA,SAAAJ,GAAA,CAAA0D,OAatE,CAAC;MAAA;IAAA;IAAAkH,YAAA,WAAAA,CAAA;MAAA,QA0B+wCnM,EAAE,CAACoM,OAAO,EAA2HpM,EAAE,CAACqM,IAAI,EAAoIrM,EAAE,CAACsM,gBAAgB,EAA2LtM,EAAE,CAACuM,OAAO,EAAkHrL,EAAE,CAACsL,MAAM,EAA6FvL,SAAS;IAAA;IAAAwL,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAjN,SAAA,EAA4C,CAACG,OAAO,CAAC,YAAY,EAAE,CAACC,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAAC+F,aAAa,CAAC,CAAC,CAAC,EAAEhG,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAACiG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;IAAA4G,eAAA;EAAA;AACnrE;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzC6F1M,EAAE,CAAA2M,iBAAA,CAyCJ7G,OAAO,EAAc,CAAC;IACrGgF,IAAI,EAAE5K,SAAS;IACf0M,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAE/E,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEgF,UAAU,EAAE,CAACpN,OAAO,CAAC,YAAY,EAAE,CAACC,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAAC+F,aAAa,CAAC,CAAC,CAAC,EAAEhG,UAAU,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAACiG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAE4G,eAAe,EAAEtM,uBAAuB,CAAC4M,MAAM;MAAER,aAAa,EAAEnM,iBAAiB,CAAC4M,IAAI;MAAEC,IAAI,EAAE;QAChPC,KAAK,EAAE;MACX,CAAC;MAAEZ,MAAM,EAAE,CAAC,yrCAAyrC;IAAE,CAAC;EACptC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExB,IAAI,EAAEqC,QAAQ;MAAEC,UAAU,EAAE,CAAC;QAC7DtC,IAAI,EAAEzK,MAAM;QACZuM,IAAI,EAAE,CAAC9M,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgL,IAAI,EAAE9K,EAAE,CAACwK;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAE9K,EAAE,CAACyK;IAAU,CAAC,EAAE;MAAEK,IAAI,EAAE9K,EAAE,CAAC0K;IAAkB,CAAC,EAAE;MAAEI,IAAI,EAAEpK,EAAE,CAACiK;IAAc,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEvE,QAAQ,EAAE,CAAC;MACpJ0E,IAAI,EAAExK;IACV,CAAC,CAAC;IAAE+F,WAAW,EAAE,CAAC;MACdyE,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEd,KAAK,EAAE,CAAC;MACRsL,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEsE,UAAU,EAAE,CAAC;MACbkG,IAAI,EAAExK;IACV,CAAC,CAAC;IAAE2C,cAAc,EAAE,CAAC;MACjB6H,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEgG,UAAU,EAAE,CAAC;MACbwE,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEiG,UAAU,EAAE,CAAC;MACbuE,IAAI,EAAExK;IACV,CAAC,CAAC;IAAE+E,KAAK,EAAE,CAAC;MACRyF,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEkG,WAAW,EAAE,CAAC;MACdsE,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEiF,aAAa,EAAE,CAAC;MAChBuF,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEmG,aAAa,EAAE,CAAC;MAChBqE,IAAI,EAAExK;IACV,CAAC,CAAC;IAAE8E,iBAAiB,EAAE,CAAC;MACpB0F,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEwE,OAAO,EAAE,CAAC;MACVgG,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEyE,QAAQ,EAAE,CAAC;MACX+F,IAAI,EAAExK;IACV,CAAC,CAAC;IAAE0E,UAAU,EAAE,CAAC;MACb8F,IAAI,EAAExK;IACV,CAAC,CAAC;IAAEwG,SAAS,EAAE,CAAC;MACZgE,IAAI,EAAEvK,eAAe;MACrBqM,IAAI,EAAE,CAACjM,aAAa;IACxB,CAAC,CAAC;IAAEoG,MAAM,EAAE,CAAC;MACT+D,IAAI,EAAEtK;IACV,CAAC,CAAC;IAAEwG,MAAM,EAAE,CAAC;MACT8D,IAAI,EAAEtK;IACV,CAAC,CAAC;IAAEyG,aAAa,EAAE,CAAC;MAChB6D,IAAI,EAAEtK;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM6M,aAAa,CAAC;EAChB,OAAOjD,IAAI,YAAAkD,sBAAAhD,CAAA;IAAA,YAAAA,CAAA,IAAwF+C,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAjI8EvN,EAAE,CAAAwN,gBAAA;IAAA1C,IAAA,EAiISuC;EAAa;EACjH,OAAOI,IAAI,kBAlI8EzN,EAAE,CAAA0N,gBAAA;IAAAC,OAAA,GAkIkC5N,YAAY,EAAEiB,YAAY,EAAEJ,YAAY,EAAEE,SAAS,EAAEF,YAAY;EAAA;AAClM;AACA;EAAA,QAAA8L,SAAA,oBAAAA,SAAA,KApI6F1M,EAAE,CAAA2M,iBAAA,CAoIJU,aAAa,EAAc,CAAC;IAC3GvC,IAAI,EAAErK,QAAQ;IACdmM,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAAC5N,YAAY,EAAEiB,YAAY,EAAEJ,YAAY,EAAEE,SAAS,CAAC;MAC9D8M,OAAO,EAAE,CAAC9H,OAAO,EAAElF,YAAY,CAAC;MAChCiN,YAAY,EAAE,CAAC/H,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,OAAO,EAAEuH,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}