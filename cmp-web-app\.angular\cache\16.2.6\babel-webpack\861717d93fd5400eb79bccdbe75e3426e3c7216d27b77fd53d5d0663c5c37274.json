{"ast": null, "code": "export default {\n  text: {\n    templateTextPagination: \"Showing {first} to {last} of {totalRecords} entries\",\n    stt: \"Index\",\n    page: \"Page\",\n    action: \"Action\",\n    nodata: \"There are no items to display\",\n    itemselected: \"Selected\",\n    filter: \"Filter\",\n    advanceSearch: \"Advance Search\",\n    createGroupSim: \"Create Group Subcriber\",\n    createGroupSimAndPushSimToGroup: \"Create Group Subcriber And Push Subscriber To Group\",\n    all: \"All\",\n    resultRegister: \"Result Register\",\n    inputText: \"Input Text\",\n    inputNumber: \"Input Number\",\n    selectValue: \"Select Value\",\n    inputDate: \"Pick Date\",\n    inputTimestamp: \"Pick Time\",\n    homepage: \"Homepage\",\n    selectOption: \"Select Option\",\n    selectMoreItem: \"+${maxDisplay} items selected\",\n    clearSelected: \"Clear Selected\",\n    textCaptcha: \"Slide to complete the puzzle\",\n    readandagree: \"I have read and agree with\",\n    changeManageData: \"Change Manage Data Customer For Teller\"\n  },\n  field: {},\n  lang: {\n    vi: \"Vietnamese\",\n    en: \"English\"\n  },\n  menu: {\n    accountmgmt: \"Account Management\",\n    listaccount: \"Account List\",\n    listpermission: \"Permissions List\",\n    billmgmt: \"Billing Management\",\n    listbill: \"Bill List\",\n    configuration: \"Configuration\",\n    customermgmt: \"Customer Management\",\n    listcustomer: \"Customer List\",\n    dashboard: \"Dashboard\",\n    devicemgmt: \"Device Management\",\n    listdevice: \"Device List\",\n    extraservice: \"Extra Services\",\n    guide: \"Guide\",\n    manual: \"Manual\",\n    log: \"Logs\",\n    ordermgmt: \"Order Management\",\n    listorder: \"Order List\",\n    report: \"Report\",\n    dynamicreport: \"Report Dynamic Config\",\n    dynamicreportgroup: \"Group Dynamic Report\",\n    simmgmt: \"Subcriber Management\",\n    listsim: \"Subcriber List\",\n    subscriptionmgmt: \"Subscription Management\",\n    listsubscription: \"Subscription List\",\n    troubleshoot: \"Troubleshoot\",\n    listroles: \"Role List\",\n    listpermissions: \"Permission List\",\n    detailroles: \"Detail Role\",\n    editroles: \"Edit Roles\",\n    groupSim: \"Group Subcriber\",\n    contract: \"Contract List\",\n    ratingplanmgmt: \"Subscription Management\",\n    listplan: \"Plan List\",\n    registerplan: \"Register Plan\",\n    detailplan: \"Detail Plan\",\n    historyRegister: \"History\",\n    apnsim: \"APN Subcriber\",\n    apnsimlist: \"APN Subcriber List\",\n    apnsimdetail: \"Detail APN Subcriber\",\n    account: \"Account\",\n    detailAccount: \"Detail\",\n    editAccount: \"Edit\",\n    alerts: \"Alert\",\n    rule: \"Alert management\",\n    alertreceivinggroup: \"Alert receiving group\",\n    alerthistory: \"Alert history\",\n    devicedetail: \"Device detail\",\n    deviceupdate: \"Device update\",\n    devicecreate: \"Device create\",\n    changePass: \"Change password\",\n    alert: \"Alerts\",\n    alertSettings: \"Set up rules\",\n    alertReceivingGroup: \"Alert receiving group\",\n    alertHistory: \"Alert history\",\n    alertList: \"Rule List\",\n    groupReceiving: \"Receiving Group Alert\",\n    groupReceivingList: \"Alert Receiving Group List\",\n    reportGroupReceivingList: \"Dynamic Report Group List\",\n    termpolicy: \"Terms and policies\",\n    termpolicyhistory: \"Policies confirm history\",\n    cmpManagement: \"M2M subscription management system\",\n    charts: \"Config Chart\",\n    chartList: \"List Config Chart\",\n    trafficManagement: \"Share Traffic Management\",\n    subTrafficManagement: \"Wallet Traffic Management\",\n    walletList: \"Wallet List\",\n    shareManagement: \"Share Management\",\n    shareList: \"Share List\",\n    walletConfig: \"Wallet Config\",\n    historyWallet: \"History Wallet\",\n    listGroupSub: \"Group List Share\",\n    autoShareGroup: \"Auto Share Group\",\n    apiLogs: \"Api Usage Log\",\n    userGuide: \"User Guide\",\n    integrationGuide: \"System Integration Guide\"\n  },\n  button: {\n    export: \"Export\",\n    exportSelect: \"CSV export of selected items\",\n    exportFilter: \"CSV export of entire list\",\n    pushGroupSim: \"Push Subcriber To Group\",\n    exportExelSelect: \"Excel export of selected items\",\n    exportExelFilter: \"Excel export the entire list\",\n    pushToGroupAvailable: \"Push Group Available\",\n    pushToNewGroup: \"Push Group New Group\",\n    cancel: \"Cancel\",\n    registerRatingPlan: \"Register Plan\",\n    changeRatingPlan: \"Change Plan\",\n    cancelRatingPlan: \"Cancel Plan\",\n    assignPlan: \"Assign Plan\",\n    historyRegisterPlan: \"History Register Plan\",\n    registerPlanForGroup: \"Register Plan For Subcriber Group\",\n    registerPlanByFile: \"Register Plan By File Importing\",\n    create: \"Create\",\n    edit: \"Edit\",\n    yes: \"Yes\",\n    agree: \"Agree\",\n    no: \"No\",\n    save: \"Save\",\n    changeStatus: \"Change Status\",\n    delete: \"Delete\",\n    active: \"Active\",\n    approve: \"Approve\",\n    suspend: \"Suspend\",\n    uploadFile: \"Drap/Drop file or Click to choose file\",\n    upFile: \"Upload File\",\n    downloadTemp: \"Download Template\",\n    back: \"Back\",\n    add: \"Create New\",\n    add2: \"Add\",\n    addDefault: \"Add Default\",\n    view: \"View Detail\",\n    import: \"Import\",\n    changePass: \"Change Password\",\n    update: \"Update\",\n    copy: \"Copy\",\n    reset: \"Reset\",\n    clear: \"Remove All\",\n    preview: \"Preview\",\n    pushUp: \"Push Up\",\n    pushDown: \"Push Down\",\n    confirm: \"Confirm\",\n    changeManageData: \"Change Manage Data\",\n    addSubToGroup: \"Add each subscription\",\n    deleteSubInGroup: \"Delete subscription\"\n  },\n  message: {\n    copied: \"Copied\",\n    required: \"This field is required\",\n    requiredField: \"${field} is required\",\n    maxLength: \"This field should have ${len} characters or fewer\",\n    minLength: \"This field should have ${len} characters or more\",\n    max: \"This field has a maximum value of ${value}\",\n    min: \"This field has a minimum value of ${value}\",\n    numbericMin: \"This field should have ${length} numeric characters or more\",\n    numbericMax: \"This field should have ${length} numeric characters or fewer\",\n    duplicated: \"Data is duplicated\",\n    invalidValue: \"Value is invalid\",\n    formatContainVN: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, . -_, space, Vietnamese)\",\n    formatCode: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, - _)\",\n    formatCodeNotSub: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, _)\",\n    invalidEmail: \"Email is invalid format\",\n    formatEmail: \"Email is invalid format. <NAME_EMAIL>\",\n    invalidPhone: \"Phone is invalid format\",\n    formatPhone: \"Phone number must be a number starting with 0 (10-11 characters) or 84 (11-12 characters)\",\n    invalidSubsciption: \"Subcriber is invalid format\",\n    exists: \"Existed ${type}\",\n    success: \"Action successfully\",\n    error: \"Action failed\",\n    saveSuccess: \"Save successfully\",\n    addGroupSuccess: \"Add Subcriber to group successfully\",\n    saveError: \"Save failed\",\n    timeout: \"Timeout Expired\",\n    errorMatchCaptcha: \"Position the piece in its slot\",\n    confirmDeleteAccount: \"Are you sure you want to delete this account?\",\n    titleConfirmDeleteAccount: \"Delete Account\",\n    confirmDeletePlan: \"Are you sure you want to delete this plan?\",\n    titleConfirmDeletePlan: \"Delete Plan\",\n    deleteSuccess: \"Delete successfully\",\n    deleteFail: \"Delete failure\",\n    confirmChangeStatusAccount: \"Are you sure you want to change status for this account?\",\n    confirmChangeStatusAlert: \"Are you sure you want to change status for this alert?\",\n    titleConfirmChangeStatusAccount: \"Change Status Account\",\n    titleConfirmChangeStatusAlert: \"Change Status Alert\",\n    changeStatusSuccess: \"Change status successfully\",\n    changeStatusFail: \"Change status failure\",\n    titleConfirmDeleteRoles: \"Delete Roles\",\n    titleConfirmDeleteAlert: \"Delete Rule\",\n    confirmDeleteRoles: \"Are you sure you want to delete this role?\",\n    confirmDeleteAlert: \"Are you sure you want to delete this rule?\",\n    titleConfirmChangeStatusRole: \"Change Status Role\",\n    confirmChangeStatusRole: \"Are you sure you want to change status for this role?\",\n    conditionExportChoose: \"Limit 1 milion rows\",\n    conditionExportFilter: \"The maximum number of subcribers cannot exceed 1 million\",\n    conditionExportExelFilter: \"The export file list exceeds 100 thousand records\",\n    conditionExportFilterEmpty: \"No subscribers have been selected yet\",\n    titleConfirmActivePlan: \"Are you sure you want to activate this plan?\",\n    confirmActivePlan: \"Active rating plan\",\n    titleConfirmApprovePlan: \"Are you sure you want to approve this plan?\",\n    confirmApprovePlan: \"Approve rating plan\",\n    titleConfirmSuspendPlan: \"Are you sure you want to suspend this plan?\",\n    confirmSuspendPlan: \"Suspend rating plan\",\n    titleConfirmDeleteDevice: \"Delete device\",\n    confirmDeleteDevice: \"Are you sure you want to delete this device?\",\n    activeSuccess: \"Activate successfully\",\n    approveSuccess: \"Approve successfully\",\n    suspendSuccess: \"Suspend successfully\",\n    activeError: \"Activate unsuccessfully\",\n    approveError: \"Approve unsuccessfully\",\n    maxsizeupload: \"Max size uploaded\",\n    invalidtypeupload: \"Invalid type uploaded\",\n    maxSizeRecordRow: \"The number of records exceeds the limit of ${row} items\",\n    wrongFileExcel: \"Wrong file format, please import excel file\",\n    invalidFile: \"Invalid File Format\",\n    planNotExists: \"Plan does not exist\",\n    planNoPermit: \"No permission with this plan\",\n    titleConfirmDeleteAlertReceivingGroup: \"Delete alert receiving group\",\n    titleConfirmDeleteReportReceivingGroup: \"Delete report receiving group\",\n    titleConfirmDeleteShareGroup: \"Delete sharing group\",\n    confirmDeleteAlertReceivingGroup: \"Are you sure you want to delete this alert receiving group?\",\n    confirmDeleteReportReceivingGroup: \"Are you sure you want to delete this report receiving group?\",\n    confirmDeleteShareGroup: \"Are you sure you want to delete this sharing group?\",\n    invalidinformation64: \"Invalid information. Please enter between 2 and 64 characters excluding special characters\",\n    invalidinformation32: \"Invalid information. Please enter between 2 and 32 characters excluding special characters\",\n    invalidPasswordFomat: \"Password must be from 6 to 20 characters, include at least 1 letter, 1 number and 1 special character\",\n    passwordNotMatch: \"Password does not match\",\n    wrongCurrentPassword: \"Wrong current password\",\n    forgotPassSendMailSuccess: \"A password recovery has been sent to your email. Please check it.\",\n    notPermissionMisidn: \"The subscription number has been assigned to another device or There are no permissions on the subscription, please re-enter\",\n    titleConfirmDeleteReport: \"Delete report\",\n    confirmDeleteReport: \"Are you sure you want to delete this report?\",\n    titleConfirmChangeStatusReport: \"Change status report\",\n    confirmChangeStatusReport: \"Are you sure you want change status this report?\",\n    confirmCancelPlan: \"Are you sure you want cancel plan \\\"${planName}\\\" for subscriber ${msisdn}?\",\n    accuracySuccess: \"Wallet authentication successful\",\n    accuracyFail: \"Wallet authentication failed\",\n    twentydigitlength: \"This field should have 10 numeric characters or fewer\",\n    oneHundredLength: \"This field cannot exceed the value 100\",\n    onlySelectGroupOrSub: \"Only a maximum of 50 emails are allowed\",\n    max50Emails: \"Only a maximum of 50 emails are allowed\",\n    max50Sms: \"Only a maximum of 50 phone numbers are allowed\",\n    emailExist: \"Email already exists\",\n    phoneExist: \"Phone number already exists\",\n    urlNotValid: \"The url is not in the correct format\",\n    onlyPositiveInteger: \"Only positive integers are allowed\",\n    titleRejectPolicy: \"ACKNOWLEDGMENT OF OBJECTION - RESTRICTION - WITHDRAWAL OF CONSENT TO PROCESSING OF PERSONAL DATA\",\n    messageRejectPolicy1: \"Dear Customer,\",\n    messageRejectPolicy2: \"The Customer has the right to object, restrict or withdraw consent to the processing of the Customer's Personal Data. However, objecting, restricting or withdrawing consent to process Customer's Personal Data may result in VNPT/VNPT Subsidiaries being unable to provide Products and services to Customers, which is This means that VNPT/VNPT's Subsidiary can unilaterally terminate the contract without having to compensate the Customer because the conditions for performing the contract have changed. Therefore, VNPT/VNPT Subsidiary recommends that Customers consider carefully before objecting, restricting or withdrawing consent to process Customer's Personal Data.\",\n    messageRejectPolicy3: \"I have read and agree to the Objection, restriction, withdrawal of consent to processing of personal data\",\n    confirmationHistory: \"Confirmation history of Personal Data Protection Policy\",\n    confirmationUserInfo: \"Confirmation account information\",\n    confirmationDevice: \"Confirmation device information\",\n    wrongFormatName: \"Wrong format. Only spaces and Vietnamese letters (a-z, A-Z, 0-9, - _) are allowed\",\n    notChartData: \"No data\",\n    isErrorQuery: \"Error Query\",\n    errorLoading: \"There was an error displaying data, please try again\"\n  },\n  searchSeperate: {\n    button: {\n      add: \"Add Filter\",\n      reset: \"Reset Filter\"\n    },\n    placeholder: {\n      dropdownFlter: \"Choose Filter\",\n      input: \"Search\",\n      dropdown: \"Select value\",\n      calendar: \"Choose date\",\n      rangeCalendar: \"Choose range date\"\n    }\n  },\n  titlepage: {\n    createAlertReceivingGroup: \"Create Alert Receiving Group\",\n    listAlertReceivingGroup: \"Alert receiving group\",\n    detailAlertReceivingGroup: \"Alert receiving group details\",\n    editAlertReceivingGroup: \"Alert receiving group edit\",\n    deleteAlertReceivingGroup: \"Delete alert receiving group\",\n    listApnSim: \"Subscriber APN\",\n    detailApnSim: \"Subscriber APN details\",\n    listAlertHistory: \"Alert history\",\n    listDevice: \"Device\",\n    createDevice: \"Create new Device\",\n    detailDevice: \"Device details\",\n    editDevice: \"Edit device\",\n    deleteDevice: \"Delete device\",\n    createaccount: \"Create account\",\n    editaccount: \"Edit account\",\n    detailaccount: \"Detail account\",\n    createRole: \"Create role\",\n    detailCustomer: \"Detail customer\",\n    editCustomer: \"Edit customer\",\n    detailsim: \"Detail SIM\",\n    listGroupSim: \"Group SIM List\",\n    createGroupSim: \"Create group SIM\",\n    detailGroupSim: \"Detail group SIM\",\n    editGroupSim: \"Edit group SIM\",\n    listContract: \"Contract List\",\n    createRatingPlan: \"Create rating plan\",\n    editRatingPlan: \"Edit rating plan\",\n    historyRegisterPlan: \"Register plan history list\",\n    createAlarm: \"Create alarm\",\n    detailAlarm: \"Detail Alarm\",\n    editAlarm: \"Edit alarm\",\n    reportDynamic: \"Report Dynamic\",\n    listGroupReportDynamic: \"Group report dynamic list\",\n    editGroupReportDynamic: \"Edit group report dynamic\",\n    detailGroupReportDynamic: \"Detail group report dynamic\",\n    createGroupReportDynamic: \"Create group report dynamic\",\n    m2SubscriptionManagementSystem: \"M2M subscription management system\",\n    apiLogs: \"API Logs\"\n  }\n};", "map": {"version": 3, "names": ["text", "templateTextPagination", "stt", "page", "action", "nodata", "itemselected", "filter", "advanceSearch", "createGroupSim", "createGroupSimAndPushSimToGroup", "all", "resultRegister", "inputText", "inputNumber", "selectValue", "inputDate", "inputTimestamp", "homepage", "selectOption", "selectMoreItem", "clearSelected", "textCaptcha", "read<PERSON><PERSON><PERSON>", "changeManageData", "field", "lang", "vi", "en", "menu", "accountmgmt", "listaccount", "listpermission", "billmgmt", "listbill", "configuration", "customermgmt", "list<PERSON><PERSON><PERSON>", "dashboard", "devicemgmt", "listdevice", "extraservice", "guide", "manual", "log", "ordermgmt", "listorder", "report", "dynamicreport", "dynamicreportgroup", "simmgmt", "listsim", "subscriptionmgmt", "listsubscription", "troubleshoot", "listroles", "listpermissions", "detailroles", "editroles", "groupSim", "contract", "ratingplanmgmt", "listplan", "registerplan", "detailplan", "historyRegister", "apnsim", "apnsimlist", "apnsimdetail", "account", "detailAccount", "editAccount", "alerts", "rule", "alertreceivinggroup", "alerthistory", "devicedetail", "deviceupdate", "devicecreate", "changePass", "alert", "alertSettings", "alertReceivingGroup", "alertHistory", "alertList", "groupReceiving", "groupReceivingList", "reportGroupReceivingList", "termpolicy", "termpolicyhistory", "cmpManagement", "charts", "chartList", "trafficManagement", "subTrafficManagement", "walletList", "shareManagement", "shareList", "walletConfig", "historyWallet", "listGroupSub", "autoShareGroup", "apiLogs", "userGuide", "integrationGuide", "button", "export", "exportSelect", "exportFilter", "pushGroupSim", "exportExelSelect", "exportExelFilter", "pushToGroupAvailable", "pushToNewGroup", "cancel", "registerRatingPlan", "changeRatingPlan", "cancelRatingPlan", "assignPlan", "historyRegisterPlan", "registerPlanForGroup", "registerPlanByFile", "create", "edit", "yes", "agree", "no", "save", "changeStatus", "delete", "active", "approve", "suspend", "uploadFile", "upFile", "downloadTemp", "back", "add", "add2", "addDefault", "view", "import", "update", "copy", "reset", "clear", "preview", "pushUp", "pushDown", "confirm", "addSubToGroup", "deleteSubInGroup", "message", "copied", "required", "requiredField", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "max", "min", "numbericMin", "numbericMax", "duplicated", "invalidV<PERSON>ue", "formatContainVN", "formatCode", "formatCodeNotSub", "invalidEmail", "formatEmail", "invalidPhone", "formatPhone", "invalidSubsciption", "exists", "success", "error", "saveSuccess", "addGroupSuccess", "saveError", "timeout", "errorMatchCaptcha", "confirmDeleteAccount", "titleConfirmDeleteAccount", "confirmDeletePlan", "titleConfirmDeletePlan", "deleteSuccess", "deleteFail", "confirm<PERSON><PERSON>eStatusAccount", "confirmChangeStatusAlert", "titleConfirmChangeStatusAccount", "titleConfirmChangeStatusAlert", "changeStatusSuccess", "changeStatusFail", "titleConfirmDeleteRoles", "titleConfirmDeleteAlert", "confirmDeleteRoles", "confirmDeleteAlert", "titleConfirmChangeStatusRole", "confirmChangeStatusRole", "conditionExportChoose", "conditionExportFilter", "conditionExportExelFilter", "conditionExportFilterEmpty", "titleConfirmActivePlan", "confirmActivePlan", "titleConfirmApprovePlan", "confirmApprovePlan", "titleConfirmSuspendPlan", "confirmSuspendPlan", "titleConfirmDeleteDevice", "confirmDeleteDevice", "activeSuccess", "approveSuccess", "suspendSuccess", "activeError", "approveError", "maxsizeupload", "invalidtypeupload", "maxSizeRecordRow", "wrongFileExcel", "invalidFile", "planNotExists", "planNoPermit", "titleConfirmDeleteAlertReceivingGroup", "titleConfirmDeleteReportReceivingGroup", "titleConfirmDeleteShareGroup", "confirmDeleteAlertReceivingGroup", "confirmDeleteReportReceivingGroup", "confirmDeleteShareGroup", "invalidinformation64", "invalidinformation32", "invalidPasswordFomat", "passwordNotMatch", "wrongCurrentPassword", "forgotPassSendMailSuccess", "notPermissionMisidn", "titleConfirmDeleteReport", "confirmDeleteReport", "titleConfirmChangeStatusReport", "confirmChangeStatusReport", "confirmCancelPlan", "accuracySuccess", "accuracyFail", "twentydigitlength", "oneHundredLength", "onlySelectGroupOrSub", "max50Emails", "max50Sms", "emailExist", "phoneExist", "urlNotValid", "onlyPositiveInteger", "titleRejectPolicy", "messageRejectPolicy1", "messageRejectPolicy2", "messageRejectPolicy3", "confirmationHistory", "confirmationUserInfo", "confirmationDevice", "wrongFormatName", "notChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorLoading", "searchSeperate", "placeholder", "dropdownFlter", "input", "dropdown", "calendar", "rangeCalendar", "titlepage", "createAlertReceivingGroup", "listAlertReceivingGroup", "detailAlertReceivingGroup", "editAlertReceivingGroup", "deleteAlertReceivingGroup", "listApnSim", "detailApnSim", "listAlertHistory", "listDevice", "createDevice", "detailDevice", "editDevice", "deleteDevice", "createaccount", "editaccount", "detailaccount", "createRole", "detailCustomer", "editCustomer", "detailsim", "listGroupSim", "detailGroupSim", "editGroupSim", "listContract", "createRatingPlan", "editRatingPlan", "createAlarm", "detailAlarm", "editAlarm", "reportDynamic", "listGroupReportDynamic", "editGroupReportDynamic", "detailGroupReportDynamic", "createGroupReportDynamic", "m2SubscriptionManagementSystem"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\global.ts"], "sourcesContent": ["export default {\r\n    text:{\r\n        templateTextPagination: \"Showing {first} to {last} of {totalRecords} entries\",\r\n        stt: \"Index\",\r\n        page: \"Page\",\r\n        action: \"Action\",\r\n        nodata: \"There are no items to display\",\r\n        itemselected: \"Selected\",\r\n        filter: \"Filter\",\r\n        advanceSearch: \"Advance Search\",\r\n        createGroupSim: \"Create Group Subcriber\",\r\n        createGroupSimAndPushSimToGroup: \"Create Group Subcriber And Push Subscriber To Group\",\r\n        all: \"All\",\r\n        resultRegister: \"Result Register\",\r\n        inputText: \"Input Text\",\r\n        inputNumber: \"Input Number\",\r\n        selectValue: \"Select Value\",\r\n        inputDate: \"Pick Date\",\r\n        inputTimestamp: \"Pick Time\",\r\n        homepage: \"Homepage\",\r\n        selectOption: \"Select Option\",\r\n        selectMoreItem: \"+${maxDisplay} items selected\",\r\n        clearSelected:\"Clear Selected\",\r\n        textCaptcha: \"Slide to complete the puzzle\",\r\n        readandagree: \"I have read and agree with\",\r\n        changeManageData: \"Change Manage Data Customer For Teller\",\r\n    },\r\n    field: {\r\n\r\n    },\r\n    lang: {\r\n        vi: \"Vietnamese\",\r\n        en: \"English\",\r\n    },\r\n    menu:{\r\n        accountmgmt: \"Account Management\",\r\n        listaccount: \"Account List\",\r\n        listpermission: \"Permissions List\",\r\n        billmgmt: \"Billing Management\",\r\n        listbill: \"Bill List\",\r\n        configuration: \"Configuration\",\r\n        customermgmt: \"Customer Management\",\r\n        listcustomer: \"Customer List\",\r\n        dashboard: \"Dashboard\",\r\n        devicemgmt: \"Device Management\",\r\n        listdevice: \"Device List\",\r\n        extraservice: \"Extra Services\",\r\n        guide: \"Guide\",\r\n        manual: \"Manual\",\r\n        log: \"Logs\",\r\n        ordermgmt: \"Order Management\",\r\n        listorder: \"Order List\",\r\n        report: \"Report\",\r\n        dynamicreport: \"Report Dynamic Config\",\r\n        dynamicreportgroup: \"Group Dynamic Report\",\r\n        simmgmt: \"Subcriber Management\",\r\n        listsim: \"Subcriber List\",\r\n        subscriptionmgmt: \"Subscription Management\",\r\n        listsubscription: \"Subscription List\",\r\n        troubleshoot: \"Troubleshoot\",\r\n        listroles: \"Role List\",\r\n        listpermissions: \"Permission List\",\r\n        detailroles: \"Detail Role\",\r\n        editroles: \"Edit Roles\",\r\n        groupSim: \"Group Subcriber\",\r\n        contract:\"Contract List\",\r\n        ratingplanmgmt: \"Subscription Management\",\r\n        listplan: \"Plan List\",\r\n        registerplan: \"Register Plan\",\r\n        detailplan: \"Detail Plan\",\r\n        historyRegister: \"History\",\r\n        apnsim: \"APN Subcriber\",\r\n        apnsimlist: \"APN Subcriber List\",\r\n        apnsimdetail: \"Detail APN Subcriber\",\r\n        account : \"Account\",\r\n        detailAccount : \"Detail\",\r\n        editAccount : \"Edit\",\r\n        alerts: \"Alert\",\r\n        rule: \"Alert management\",\r\n        alertreceivinggroup: \"Alert receiving group\",\r\n        alerthistory: \"Alert history\",\r\n        devicedetail: \"Device detail\",\r\n        deviceupdate: \"Device update\",\r\n        devicecreate: \"Device create\",\r\n        changePass: \"Change password\",\r\n        alert: \"Alerts\",\r\n        alertSettings: \"Set up rules\",\r\n        alertReceivingGroup: \"Alert receiving group\",\r\n        alertHistory: \"Alert history\",\r\n        alertList: \"Rule List\",\r\n        groupReceiving: \"Receiving Group Alert\",\r\n        groupReceivingList: \"Alert Receiving Group List\",\r\n        reportGroupReceivingList: \"Dynamic Report Group List\",\r\n        termpolicy: \"Terms and policies\",\r\n        termpolicyhistory: \"Policies confirm history\",\r\n        cmpManagement: \"M2M subscription management system\",\r\n        charts: \"Config Chart\",\r\n        chartList: \"List Config Chart\",\r\n        trafficManagement: \"Share Traffic Management\",\r\n        subTrafficManagement: \"Wallet Traffic Management\",\r\n        walletList: \"Wallet List\",\r\n        shareManagement:\"Share Management\",\r\n        shareList:\"Share List\",\r\n        walletConfig: \"Wallet Config\",\r\n        historyWallet: \"History Wallet\",\r\n        listGroupSub: \"Group List Share\",\r\n        autoShareGroup: \"Auto Share Group\",\r\n        apiLogs: \"Api Usage Log\",\r\n        userGuide: \"User Guide\",\r\n        integrationGuide: \"System Integration Guide\",\r\n    },\r\n    button: {\r\n        export: \"Export\",\r\n        exportSelect: \"CSV export of selected items\",\r\n        exportFilter: \"CSV export of entire list\",\r\n        pushGroupSim: \"Push Subcriber To Group\",\r\n        exportExelSelect: \"Excel export of selected items\",\r\n        exportExelFilter: \"Excel export the entire list\",\r\n        pushToGroupAvailable: \"Push Group Available\",\r\n        pushToNewGroup: \"Push Group New Group\",\r\n        cancel: \"Cancel\",\r\n        registerRatingPlan: \"Register Plan\",\r\n        changeRatingPlan: \"Change Plan\",\r\n        cancelRatingPlan: \"Cancel Plan\",\r\n        assignPlan: \"Assign Plan\",\r\n        historyRegisterPlan: \"History Register Plan\",\r\n        registerPlanForGroup: \"Register Plan For Subcriber Group\",\r\n        registerPlanByFile: \"Register Plan By File Importing\",\r\n        create: \"Create\",\r\n        edit: \"Edit\",\r\n        yes: \"Yes\",\r\n        agree: \"Agree\",\r\n        no: \"No\",\r\n        save: \"Save\",\r\n        changeStatus: \"Change Status\",\r\n        delete: \"Delete\",\r\n        active: \"Active\",\r\n        approve: \"Approve\",\r\n        suspend: \"Suspend\",\r\n        uploadFile: \"Drap/Drop file or Click to choose file\",\r\n        upFile: \"Upload File\",\r\n        downloadTemp: \"Download Template\",\r\n        back: \"Back\",\r\n        add: \"Create New\",\r\n        add2: \"Add\",\r\n        addDefault: \"Add Default\",\r\n        view: \"View Detail\",\r\n        import: \"Import\",\r\n        changePass : \"Change Password\",\r\n        update: \"Update\",\r\n        copy: \"Copy\",\r\n        reset: \"Reset\",\r\n        clear: \"Remove All\",\r\n        preview: \"Preview\",\r\n        pushUp: \"Push Up\",\r\n        pushDown: \"Push Down\",\r\n        confirm: \"Confirm\",\r\n        changeManageData: \"Change Manage Data\",\r\n        addSubToGroup: \"Add each subscription\",\r\n        deleteSubInGroup: \"Delete subscription\"\r\n    },\r\n    message:{\r\n        copied: \"Copied\",\r\n        required: \"This field is required\",\r\n        requiredField: \"${field} is required\",\r\n        maxLength: \"This field should have ${len} characters or fewer\",\r\n        minLength: \"This field should have ${len} characters or more\",\r\n        max: \"This field has a maximum value of ${value}\",\r\n        min: \"This field has a minimum value of ${value}\",\r\n        numbericMin: \"This field should have ${length} numeric characters or more\",\r\n        numbericMax: \"This field should have ${length} numeric characters or fewer\",\r\n        duplicated: \"Data is duplicated\",\r\n        invalidValue: \"Value is invalid\",\r\n        formatContainVN: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, . -_, space, Vietnamese)\",\r\n        formatCode: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, - _)\",\r\n        formatCodeNotSub: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, _)\",\r\n        invalidEmail: \"Email is invalid format\",\r\n        formatEmail: \"Email is invalid format. <NAME_EMAIL>\",\r\n        invalidPhone: \"Phone is invalid format\",\r\n        formatPhone: \"Phone number must be a number starting with 0 (10-11 characters) or 84 (11-12 characters)\",\r\n        invalidSubsciption: \"Subcriber is invalid format\",\r\n        exists: \"Existed ${type}\",\r\n        success: \"Action successfully\",\r\n        error: \"Action failed\",\r\n        saveSuccess: \"Save successfully\",\r\n        addGroupSuccess: \"Add Subcriber to group successfully\",\r\n        saveError: \"Save failed\",\r\n        timeout: \"Timeout Expired\",\r\n        errorMatchCaptcha: \"Position the piece in its slot\",\r\n        confirmDeleteAccount: \"Are you sure you want to delete this account?\",\r\n        titleConfirmDeleteAccount: \"Delete Account\",\r\n        confirmDeletePlan: \"Are you sure you want to delete this plan?\",\r\n        titleConfirmDeletePlan: \"Delete Plan\",\r\n        deleteSuccess: \"Delete successfully\",\r\n        deleteFail: \"Delete failure\",\r\n        confirmChangeStatusAccount: \"Are you sure you want to change status for this account?\",\r\n        confirmChangeStatusAlert: \"Are you sure you want to change status for this alert?\",\r\n        titleConfirmChangeStatusAccount: \"Change Status Account\",\r\n        titleConfirmChangeStatusAlert: \"Change Status Alert\",\r\n        changeStatusSuccess: \"Change status successfully\",\r\n        changeStatusFail: \"Change status failure\",\r\n        titleConfirmDeleteRoles: \"Delete Roles\",\r\n        titleConfirmDeleteAlert: \"Delete Rule\",\r\n        confirmDeleteRoles: \"Are you sure you want to delete this role?\",\r\n        confirmDeleteAlert: \"Are you sure you want to delete this rule?\",\r\n        titleConfirmChangeStatusRole: \"Change Status Role\",\r\n        confirmChangeStatusRole: \"Are you sure you want to change status for this role?\",\r\n        conditionExportChoose: \"Limit 1 milion rows\",\r\n        conditionExportFilter: \"The maximum number of subcribers cannot exceed 1 million\",\r\n        conditionExportExelFilter: \"The export file list exceeds 100 thousand records\",\r\n        conditionExportFilterEmpty: \"No subscribers have been selected yet\",\r\n        titleConfirmActivePlan: \"Are you sure you want to activate this plan?\",\r\n        confirmActivePlan: \"Active rating plan\",\r\n        titleConfirmApprovePlan: \"Are you sure you want to approve this plan?\",\r\n        confirmApprovePlan: \"Approve rating plan\",\r\n        titleConfirmSuspendPlan: \"Are you sure you want to suspend this plan?\",\r\n        confirmSuspendPlan: \"Suspend rating plan\",\r\n        titleConfirmDeleteDevice: \"Delete device\",\r\n        confirmDeleteDevice: \"Are you sure you want to delete this device?\",\r\n        activeSuccess:  \"Activate successfully\",\r\n        approveSuccess: \"Approve successfully\",\r\n        suspendSuccess: \"Suspend successfully\",\r\n        activeError:  \"Activate unsuccessfully\",\r\n        approveError: \"Approve unsuccessfully\",\r\n        maxsizeupload: \"Max size uploaded\",\r\n        invalidtypeupload: \"Invalid type uploaded\",\r\n        maxSizeRecordRow: \"The number of records exceeds the limit of ${row} items\",\r\n        wrongFileExcel: \"Wrong file format, please import excel file\",\r\n        invalidFile: \"Invalid File Format\",\r\n        planNotExists: \"Plan does not exist\",\r\n        planNoPermit: \"No permission with this plan\",\r\n        titleConfirmDeleteAlertReceivingGroup: \"Delete alert receiving group\",\r\n        titleConfirmDeleteReportReceivingGroup: \"Delete report receiving group\",\r\n        titleConfirmDeleteShareGroup: \"Delete sharing group\",\r\n        confirmDeleteAlertReceivingGroup: \"Are you sure you want to delete this alert receiving group?\",\r\n        confirmDeleteReportReceivingGroup: \"Are you sure you want to delete this report receiving group?\",\r\n        confirmDeleteShareGroup: \"Are you sure you want to delete this sharing group?\",\r\n        invalidinformation64: \"Invalid information. Please enter between 2 and 64 characters excluding special characters\",\r\n        invalidinformation32: \"Invalid information. Please enter between 2 and 32 characters excluding special characters\",\r\n        invalidPasswordFomat : \"Password must be from 6 to 20 characters, include at least 1 letter, 1 number and 1 special character\",\r\n        passwordNotMatch: \"Password does not match\",\r\n        wrongCurrentPassword : \"Wrong current password\",\r\n        forgotPassSendMailSuccess : \"A password recovery has been sent to your email. Please check it.\",\r\n        notPermissionMisidn: \"The subscription number has been assigned to another device or There are no permissions on the subscription, please re-enter\",\r\n        titleConfirmDeleteReport: \"Delete report\",\r\n        confirmDeleteReport: \"Are you sure you want to delete this report?\",\r\n        titleConfirmChangeStatusReport: \"Change status report\",\r\n        confirmChangeStatusReport: \"Are you sure you want change status this report?\",\r\n        confirmCancelPlan: \"Are you sure you want cancel plan \\\"${planName}\\\" for subscriber ${msisdn}?\",\r\n        accuracySuccess:\"Wallet authentication successful\",\r\n        accuracyFail: \"Wallet authentication failed\",\r\n        twentydigitlength:\"This field should have 10 numeric characters or fewer\",\r\n        oneHundredLength : \"This field cannot exceed the value 100\",\r\n        onlySelectGroupOrSub : \"Only a maximum of 50 emails are allowed\",\r\n        max50Emails: \"Only a maximum of 50 emails are allowed\",\r\n        max50Sms : \"Only a maximum of 50 phone numbers are allowed\",\r\n        emailExist : \"Email already exists\",\r\n        phoneExist : \"Phone number already exists\",\r\n        urlNotValid : \"The url is not in the correct format\",\r\n        onlyPositiveInteger : \"Only positive integers are allowed\",\r\n        titleRejectPolicy: \"ACKNOWLEDGMENT OF OBJECTION - RESTRICTION - WITHDRAWAL OF CONSENT TO PROCESSING OF PERSONAL DATA\",\r\n        messageRejectPolicy1: \"Dear Customer,\",\r\n        messageRejectPolicy2: \"The Customer has the right to object, restrict or withdraw consent to the processing of the Customer's Personal Data. However, objecting, restricting or withdrawing consent to process Customer's Personal Data may result in VNPT/VNPT Subsidiaries being unable to provide Products and services to Customers, which is This means that VNPT/VNPT's Subsidiary can unilaterally terminate the contract without having to compensate the Customer because the conditions for performing the contract have changed. Therefore, VNPT/VNPT Subsidiary recommends that Customers consider carefully before objecting, restricting or withdrawing consent to process Customer's Personal Data.\",\r\n        messageRejectPolicy3: \"I have read and agree to the Objection, restriction, withdrawal of consent to processing of personal data\",\r\n        confirmationHistory: \"Confirmation history of Personal Data Protection Policy\",\r\n        confirmationUserInfo: \"Confirmation account information\",\r\n        confirmationDevice: \"Confirmation device information\",\r\n        wrongFormatName: \"Wrong format. Only spaces and Vietnamese letters (a-z, A-Z, 0-9, - _) are allowed\",\r\n        notChartData: \"No data\",\r\n        isErrorQuery: \"Error Query\",\r\n        errorLoading: \"There was an error displaying data, please try again\"\r\n    },\r\n    searchSeperate:{\r\n        button:{\r\n            add:\"Add Filter\",\r\n            reset:\"Reset Filter\"\r\n        },\r\n        placeholder:{\r\n            dropdownFlter:\"Choose Filter\",\r\n            input:\"Search\",\r\n            dropdown:\"Select value\",\r\n            calendar:\"Choose date\",\r\n            rangeCalendar:\"Choose range date\"\r\n        }\r\n    },\r\n    titlepage: {\r\n        createAlertReceivingGroup: \"Create Alert Receiving Group\",\r\n        listAlertReceivingGroup: \"Alert receiving group\",\r\n        detailAlertReceivingGroup: \"Alert receiving group details\",\r\n        editAlertReceivingGroup: \"Alert receiving group edit\",\r\n        deleteAlertReceivingGroup: \"Delete alert receiving group\",\r\n        listApnSim: \"Subscriber APN\",\r\n        detailApnSim: \"Subscriber APN details\",\r\n        listAlertHistory: \"Alert history\",\r\n        listDevice: \"Device\",\r\n        createDevice: \"Create new Device\",\r\n        detailDevice: \"Device details\",\r\n        editDevice: \"Edit device\",\r\n        deleteDevice: \"Delete device\",\r\n        createaccount: \"Create account\",\r\n        editaccount: \"Edit account\",\r\n        detailaccount: \"Detail account\",\r\n        createRole: \"Create role\",\r\n        detailCustomer: \"Detail customer\",\r\n        editCustomer: \"Edit customer\",\r\n        detailsim: \"Detail SIM\",\r\n        listGroupSim: \"Group SIM List\",\r\n        createGroupSim: \"Create group SIM\",\r\n        detailGroupSim: \"Detail group SIM\",\r\n        editGroupSim: \"Edit group SIM\",\r\n        listContract: \"Contract List\",\r\n        createRatingPlan: \"Create rating plan\",\r\n        editRatingPlan: \"Edit rating plan\",\r\n        historyRegisterPlan: \"Register plan history list\",\r\n        createAlarm: \"Create alarm\",\r\n        detailAlarm: \"Detail Alarm\",\r\n        editAlarm: \"Edit alarm\",\r\n        reportDynamic: \"Report Dynamic\",\r\n        listGroupReportDynamic: \"Group report dynamic list\",\r\n        editGroupReportDynamic: \"Edit group report dynamic\",\r\n        detailGroupReportDynamic: \"Detail group report dynamic\",\r\n        createGroupReportDynamic: \"Create group report dynamic\",\r\n        m2SubscriptionManagementSystem: \"M2M subscription management system\",\r\n        apiLogs: \"API Logs\"\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAC;IACDC,sBAAsB,EAAE,qDAAqD;IAC7EC,GAAG,EAAE,OAAO;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,+BAA+B;IACvCC,YAAY,EAAE,UAAU;IACxBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,gBAAgB;IAC/BC,cAAc,EAAE,wBAAwB;IACxCC,+BAA+B,EAAE,qDAAqD;IACtFC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,WAAW;IACtBC,cAAc,EAAE,WAAW;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,eAAe;IAC7BC,cAAc,EAAE,+BAA+B;IAC/CC,aAAa,EAAC,gBAAgB;IAC9BC,WAAW,EAAE,8BAA8B;IAC3CC,YAAY,EAAE,4BAA4B;IAC1CC,gBAAgB,EAAE;GACrB;EACDC,KAAK,EAAE,EAEN;EACDC,IAAI,EAAE;IACFC,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE;GACP;EACDC,IAAI,EAAC;IACDC,WAAW,EAAE,oBAAoB;IACjCC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,kBAAkB;IAClCC,QAAQ,EAAE,oBAAoB;IAC9BC,QAAQ,EAAE,WAAW;IACrBC,aAAa,EAAE,eAAe;IAC9BC,YAAY,EAAE,qBAAqB;IACnCC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,mBAAmB;IAC/BC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,gBAAgB;IAC9BC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE,YAAY;IACvBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,uBAAuB;IACtCC,kBAAkB,EAAE,sBAAsB;IAC1CC,OAAO,EAAE,sBAAsB;IAC/BC,OAAO,EAAE,gBAAgB;IACzBC,gBAAgB,EAAE,yBAAyB;IAC3CC,gBAAgB,EAAE,mBAAmB;IACrCC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,WAAW;IACtBC,eAAe,EAAE,iBAAiB;IAClCC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAC,eAAe;IACxBC,cAAc,EAAE,yBAAyB;IACzCC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,eAAe;IAC7BC,UAAU,EAAE,aAAa;IACzBC,eAAe,EAAE,SAAS;IAC1BC,MAAM,EAAE,eAAe;IACvBC,UAAU,EAAE,oBAAoB;IAChCC,YAAY,EAAE,sBAAsB;IACpCC,OAAO,EAAG,SAAS;IACnBC,aAAa,EAAG,QAAQ;IACxBC,WAAW,EAAG,MAAM;IACpBC,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,kBAAkB;IACxBC,mBAAmB,EAAE,uBAAuB;IAC5CC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,eAAe;IAC7BC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,cAAc;IAC7BC,mBAAmB,EAAE,uBAAuB;IAC5CC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,WAAW;IACtBC,cAAc,EAAE,uBAAuB;IACvCC,kBAAkB,EAAE,4BAA4B;IAChDC,wBAAwB,EAAE,2BAA2B;IACrDC,UAAU,EAAE,oBAAoB;IAChCC,iBAAiB,EAAE,0BAA0B;IAC7CC,aAAa,EAAE,oCAAoC;IACnDC,MAAM,EAAE,cAAc;IACtBC,SAAS,EAAE,mBAAmB;IAC9BC,iBAAiB,EAAE,0BAA0B;IAC7CC,oBAAoB,EAAE,2BAA2B;IACjDC,UAAU,EAAE,aAAa;IACzBC,eAAe,EAAC,kBAAkB;IAClCC,SAAS,EAAC,YAAY;IACtBC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,gBAAgB;IAC/BC,YAAY,EAAE,kBAAkB;IAChCC,cAAc,EAAE,kBAAkB;IAClCC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,YAAY;IACvBC,gBAAgB,EAAE;GACrB;EACDC,MAAM,EAAE;IACJC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,8BAA8B;IAC5CC,YAAY,EAAE,2BAA2B;IACzCC,YAAY,EAAE,yBAAyB;IACvCC,gBAAgB,EAAE,gCAAgC;IAClDC,gBAAgB,EAAE,8BAA8B;IAChDC,oBAAoB,EAAE,sBAAsB;IAC5CC,cAAc,EAAE,sBAAsB;IACtCC,MAAM,EAAE,QAAQ;IAChBC,kBAAkB,EAAE,eAAe;IACnCC,gBAAgB,EAAE,aAAa;IAC/BC,gBAAgB,EAAE,aAAa;IAC/BC,UAAU,EAAE,aAAa;IACzBC,mBAAmB,EAAE,uBAAuB;IAC5CC,oBAAoB,EAAE,mCAAmC;IACzDC,kBAAkB,EAAE,iCAAiC;IACrDC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,OAAO;IACdC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,eAAe;IAC7BC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,wCAAwC;IACpDC,MAAM,EAAE,aAAa;IACrBC,YAAY,EAAE,mBAAmB;IACjCC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,YAAY;IACjBC,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,QAAQ;IAChB9D,UAAU,EAAG,iBAAiB;IAC9B+D,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,YAAY;IACnBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,WAAW;IACrBC,OAAO,EAAE,SAAS;IAClB7H,gBAAgB,EAAE,oBAAoB;IACtC8H,aAAa,EAAE,uBAAuB;IACtCC,gBAAgB,EAAE;GACrB;EACDC,OAAO,EAAC;IACJC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,wBAAwB;IAClCC,aAAa,EAAE,sBAAsB;IACrCC,SAAS,EAAE,mDAAmD;IAC9DC,SAAS,EAAE,kDAAkD;IAC7DC,GAAG,EAAE,4CAA4C;IACjDC,GAAG,EAAE,4CAA4C;IACjDC,WAAW,EAAE,6DAA6D;IAC1EC,WAAW,EAAE,8DAA8D;IAC3EC,UAAU,EAAE,oBAAoB;IAChCC,YAAY,EAAE,kBAAkB;IAChCC,eAAe,EAAE,oEAAoE;IACrFC,UAAU,EAAE,gDAAgD;IAC5DC,gBAAgB,EAAE,8CAA8C;IAChEC,YAAY,EAAE,yBAAyB;IACvCC,WAAW,EAAE,kDAAkD;IAC/DC,YAAY,EAAE,yBAAyB;IACvCC,WAAW,EAAE,2FAA2F;IACxGC,kBAAkB,EAAE,6BAA6B;IACjDC,MAAM,EAAE,iBAAiB;IACzBC,OAAO,EAAE,qBAAqB;IAC9BC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,mBAAmB;IAChCC,eAAe,EAAE,qCAAqC;IACtDC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,iBAAiB;IAC1BC,iBAAiB,EAAE,gCAAgC;IACnDC,oBAAoB,EAAE,+CAA+C;IACrEC,yBAAyB,EAAE,gBAAgB;IAC3CC,iBAAiB,EAAE,4CAA4C;IAC/DC,sBAAsB,EAAE,aAAa;IACrCC,aAAa,EAAE,qBAAqB;IACpCC,UAAU,EAAE,gBAAgB;IAC5BC,0BAA0B,EAAE,0DAA0D;IACtFC,wBAAwB,EAAE,wDAAwD;IAClFC,+BAA+B,EAAE,uBAAuB;IACxDC,6BAA6B,EAAE,qBAAqB;IACpDC,mBAAmB,EAAE,4BAA4B;IACjDC,gBAAgB,EAAE,uBAAuB;IACzCC,uBAAuB,EAAE,cAAc;IACvCC,uBAAuB,EAAE,aAAa;IACtCC,kBAAkB,EAAE,4CAA4C;IAChEC,kBAAkB,EAAE,4CAA4C;IAChEC,4BAA4B,EAAE,oBAAoB;IAClDC,uBAAuB,EAAE,uDAAuD;IAChFC,qBAAqB,EAAE,qBAAqB;IAC5CC,qBAAqB,EAAE,0DAA0D;IACjFC,yBAAyB,EAAE,mDAAmD;IAC9EC,0BAA0B,EAAE,uCAAuC;IACnEC,sBAAsB,EAAE,8CAA8C;IACtEC,iBAAiB,EAAE,oBAAoB;IACvCC,uBAAuB,EAAE,6CAA6C;IACtEC,kBAAkB,EAAE,qBAAqB;IACzCC,uBAAuB,EAAE,6CAA6C;IACtEC,kBAAkB,EAAE,qBAAqB;IACzCC,wBAAwB,EAAE,eAAe;IACzCC,mBAAmB,EAAE,8CAA8C;IACnEC,aAAa,EAAG,uBAAuB;IACvCC,cAAc,EAAE,sBAAsB;IACtCC,cAAc,EAAE,sBAAsB;IACtCC,WAAW,EAAG,yBAAyB;IACvCC,YAAY,EAAE,wBAAwB;IACtCC,aAAa,EAAE,mBAAmB;IAClCC,iBAAiB,EAAE,uBAAuB;IAC1CC,gBAAgB,EAAE,yDAAyD;IAC3EC,cAAc,EAAE,6CAA6C;IAC7DC,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,qBAAqB;IACpCC,YAAY,EAAE,8BAA8B;IAC5CC,qCAAqC,EAAE,8BAA8B;IACrEC,sCAAsC,EAAE,+BAA+B;IACvEC,4BAA4B,EAAE,sBAAsB;IACpDC,gCAAgC,EAAE,6DAA6D;IAC/FC,iCAAiC,EAAE,8DAA8D;IACjGC,uBAAuB,EAAE,qDAAqD;IAC9EC,oBAAoB,EAAE,4FAA4F;IAClHC,oBAAoB,EAAE,4FAA4F;IAClHC,oBAAoB,EAAG,uGAAuG;IAC9HC,gBAAgB,EAAE,yBAAyB;IAC3CC,oBAAoB,EAAG,wBAAwB;IAC/CC,yBAAyB,EAAG,mEAAmE;IAC/FC,mBAAmB,EAAE,8HAA8H;IACnJC,wBAAwB,EAAE,eAAe;IACzCC,mBAAmB,EAAE,8CAA8C;IACnEC,8BAA8B,EAAE,sBAAsB;IACtDC,yBAAyB,EAAE,kDAAkD;IAC7EC,iBAAiB,EAAE,6EAA6E;IAChGC,eAAe,EAAC,kCAAkC;IAClDC,YAAY,EAAE,8BAA8B;IAC5CC,iBAAiB,EAAC,uDAAuD;IACzEC,gBAAgB,EAAG,wCAAwC;IAC3DC,oBAAoB,EAAG,yCAAyC;IAChEC,WAAW,EAAE,yCAAyC;IACtDC,QAAQ,EAAG,gDAAgD;IAC3DC,UAAU,EAAG,sBAAsB;IACnCC,UAAU,EAAG,6BAA6B;IAC1CC,WAAW,EAAG,sCAAsC;IACpDC,mBAAmB,EAAG,oCAAoC;IAC1DC,iBAAiB,EAAE,kGAAkG;IACrHC,oBAAoB,EAAE,gBAAgB;IACtCC,oBAAoB,EAAE,6pBAA6pB;IACnrBC,oBAAoB,EAAE,2GAA2G;IACjIC,mBAAmB,EAAE,yDAAyD;IAC9EC,oBAAoB,EAAE,kCAAkC;IACxDC,kBAAkB,EAAE,iCAAiC;IACrDC,eAAe,EAAE,mFAAmF;IACpGC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,aAAa;IAC3BC,YAAY,EAAE;GACjB;EACDC,cAAc,EAAC;IACX7J,MAAM,EAAC;MACHgC,GAAG,EAAC,YAAY;MAChBO,KAAK,EAAC;KACT;IACDuH,WAAW,EAAC;MACRC,aAAa,EAAC,eAAe;MAC7BC,KAAK,EAAC,QAAQ;MACdC,QAAQ,EAAC,cAAc;MACvBC,QAAQ,EAAC,aAAa;MACtBC,aAAa,EAAC;;GAErB;EACDC,SAAS,EAAE;IACPC,yBAAyB,EAAE,8BAA8B;IACzDC,uBAAuB,EAAE,uBAAuB;IAChDC,yBAAyB,EAAE,+BAA+B;IAC1DC,uBAAuB,EAAE,4BAA4B;IACrDC,yBAAyB,EAAE,8BAA8B;IACzDC,UAAU,EAAE,gBAAgB;IAC5BC,YAAY,EAAE,wBAAwB;IACtCC,gBAAgB,EAAE,eAAe;IACjCC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE,mBAAmB;IACjCC,YAAY,EAAE,gBAAgB;IAC9BC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,gBAAgB;IAC/BC,WAAW,EAAE,cAAc;IAC3BC,aAAa,EAAE,gBAAgB;IAC/BC,UAAU,EAAE,aAAa;IACzBC,cAAc,EAAE,iBAAiB;IACjCC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,gBAAgB;IAC9BzR,cAAc,EAAE,kBAAkB;IAClC0R,cAAc,EAAE,kBAAkB;IAClCC,YAAY,EAAE,gBAAgB;IAC9BC,YAAY,EAAE,eAAe;IAC7BC,gBAAgB,EAAE,oBAAoB;IACtCC,cAAc,EAAE,kBAAkB;IAClChL,mBAAmB,EAAE,4BAA4B;IACjDiL,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAE,gBAAgB;IAC/BC,sBAAsB,EAAE,2BAA2B;IACnDC,sBAAsB,EAAE,2BAA2B;IACnDC,wBAAwB,EAAE,6BAA6B;IACvDC,wBAAwB,EAAE,6BAA6B;IACvDC,8BAA8B,EAAE,oCAAoC;IACpE1M,OAAO,EAAE;;CAEhB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}