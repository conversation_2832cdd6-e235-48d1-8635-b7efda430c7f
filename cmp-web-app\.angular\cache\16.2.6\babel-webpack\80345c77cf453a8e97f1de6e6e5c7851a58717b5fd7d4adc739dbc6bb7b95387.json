{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport DataPage from \"src/app/service/data.page\";\nimport { ReportReceivingGroupComponent } from \"./report-receiving-group/report.receiving.group.component\";\nimport { ReportGroupReceivingEditComponent } from \"./report-receiving-group/edit/app.group-receiving.edit.component\";\nimport { ReportGroupReceivingDetailComponent } from \"./report-receiving-group/detail/app.group-receiving.detail.component\";\nimport { ReportGroupReceivingCreateComponent } from \"./report-receiving-group/create/app.group-receiving.create.component\";\nimport { ReportDynamicListComponent } from \"./report-dynamic/list/report.dynamic.list.component\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ReportDynamicContentComponent } from \"./report-dynamic/content/report.dynamic.content.component\";\nimport { ReportDynamicListContentComponent } from \"./report-dynamic/list-content/report.dynamic.list.content\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ReportRoutingModule {\n  static {\n    this.ɵfac = function ReportRoutingModule_Factory(t) {\n      return new (t || ReportRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReportRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"report-dynamic\",\n        component: ReportDynamicListComponent,\n        data: new DataPage(\"global.titlepage.reportDynamic\", [CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])\n      }, {\n        path: \"report-dynamic/report-content/:id\",\n        component: ReportDynamicContentComponent,\n        data: new DataPage(\"permission.RptContent.RptContent\", [\"getReport\"])\n      }, {\n        path: \"report-dynamic/report-content\",\n        component: ReportDynamicListContentComponent,\n        data: new DataPage(\"permission.RptContent.RptContent\")\n      }, {\n        path: \"group-report-dynamic\",\n        component: ReportReceivingGroupComponent,\n        data: new DataPage(\"global.titlepage.listGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])\n      }, {\n        path: \"group-report-dynamic/edit/:id\",\n        component: ReportGroupReceivingEditComponent,\n        data: new DataPage(\"global.titlepage.editGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.UPDATE])\n      }, {\n        path: \"group-report-dynamic/detail/:id\",\n        component: ReportGroupReceivingDetailComponent,\n        data: new DataPage(\"global.titlepage.detailGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_DETAIL])\n      }, {\n        path: \"group-report-dynamic/create\",\n        component: ReportGroupReceivingCreateComponent,\n        data: new DataPage(\"global.titlepage.createGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.CREATE])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReportRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DataPage", "ReportReceivingGroupComponent", "ReportGroupReceivingEditComponent", "ReportGroupReceivingDetailComponent", "ReportGroupReceivingCreateComponent", "ReportDynamicListComponent", "CONSTANTS", "ReportDynamicContentComponent", "ReportDynamicListContentComponent", "ReportRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "REPORT_DYNAMIC", "VIEW_LIST", "GROUP_REPORT_DYNAMIC", "UPDATE", "VIEW_DETAIL", "CREATE", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\app.report.routing.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule } from \"@angular/router\";\r\nimport DataPage from \"src/app/service/data.page\";\r\nimport { ReportReceivingGroupComponent } from \"./report-receiving-group/report.receiving.group.component\";\r\nimport { ReportGroupReceivingEditComponent } from \"./report-receiving-group/edit/app.group-receiving.edit.component\";\r\nimport { ReportGroupReceivingDetailComponent } from \"./report-receiving-group/detail/app.group-receiving.detail.component\";\r\nimport { ReportGroupReceivingCreateComponent } from \"./report-receiving-group/create/app.group-receiving.create.component\";\r\nimport {ReportDynamicListComponent} from \"./report-dynamic/list/report.dynamic.list.component\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { ReportDynamicContentComponent } from \"./report-dynamic/content/report.dynamic.content.component\";\r\nimport { ReportDynamicListContentComponent } from \"./report-dynamic/list-content/report.dynamic.list.content\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {path: \"report-dynamic\", component: ReportDynamicListComponent, data: new DataPage(\"global.titlepage.reportDynamic\", [CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])},\r\n            {path: \"report-dynamic/report-content/:id\", component: ReportDynamicContentComponent, data: new DataPage(\"permission.RptContent.RptContent\", [\"getReport\"])},\r\n            {path: \"report-dynamic/report-content\", component: ReportDynamicListContentComponent, data: new DataPage(\"permission.RptContent.RptContent\")},\r\n            {path: \"group-report-dynamic\", component: ReportReceivingGroupComponent, data: new DataPage(\"global.titlepage.listGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])},\r\n            {path: \"group-report-dynamic/edit/:id\", component: ReportGroupReceivingEditComponent, data: new DataPage(\"global.titlepage.editGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.UPDATE])},\r\n            {path: \"group-report-dynamic/detail/:id\", component: ReportGroupReceivingDetailComponent, data: new DataPage(\"global.titlepage.detailGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_DETAIL])},\r\n            {path: \"group-report-dynamic/create\", component: ReportGroupReceivingCreateComponent, data: new DataPage(\"global.titlepage.createGroupReportDynamic\", [CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.CREATE])}\r\n        ])\r\n    ],\r\n    exports: [\r\n        RouterModule\r\n    ]\r\n})\r\nexport class ReportRoutingModule{}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,iCAAiC,QAAQ,kEAAkE;AACpH,SAASC,mCAAmC,QAAQ,sEAAsE;AAC1H,SAASC,mCAAmC,QAAQ,sEAAsE;AAC1H,SAAQC,0BAA0B,QAAO,qDAAqD;AAC9F,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,iCAAiC,QAAQ,2DAA2D;;;AAkB7G,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAdxBV,YAAY,CAACW,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,gBAAgB;QAAEC,SAAS,EAAEP,0BAA0B;QAAEQ,IAAI,EAAE,IAAIb,QAAQ,CAAC,gCAAgC,EAAE,CAACM,SAAS,CAACQ,WAAW,CAACC,cAAc,CAACC,SAAS,CAAC;MAAC,CAAC,EACvK;QAACL,IAAI,EAAE,mCAAmC;QAAEC,SAAS,EAAEL,6BAA6B;QAAEM,IAAI,EAAE,IAAIb,QAAQ,CAAC,kCAAkC,EAAE,CAAC,WAAW,CAAC;MAAC,CAAC,EAC5J;QAACW,IAAI,EAAE,+BAA+B;QAAEC,SAAS,EAAEJ,iCAAiC;QAAEK,IAAI,EAAE,IAAIb,QAAQ,CAAC,kCAAkC;MAAC,CAAC,EAC7I;QAACW,IAAI,EAAE,sBAAsB;QAAEC,SAAS,EAAEX,6BAA6B;QAAEY,IAAI,EAAE,IAAIb,QAAQ,CAAC,yCAAyC,EAAE,CAACM,SAAS,CAACQ,WAAW,CAACG,oBAAoB,CAACD,SAAS,CAAC;MAAC,CAAC,EAC/L;QAACL,IAAI,EAAE,+BAA+B;QAAEC,SAAS,EAAEV,iCAAiC;QAAEW,IAAI,EAAE,IAAIb,QAAQ,CAAC,yCAAyC,EAAE,CAACM,SAAS,CAACQ,WAAW,CAACG,oBAAoB,CAACC,MAAM,CAAC;MAAC,CAAC,EACzM;QAACP,IAAI,EAAE,iCAAiC;QAAEC,SAAS,EAAET,mCAAmC;QAAEU,IAAI,EAAE,IAAIb,QAAQ,CAAC,2CAA2C,EAAE,CAACM,SAAS,CAACQ,WAAW,CAACG,oBAAoB,CAACE,WAAW,CAAC;MAAC,CAAC,EACpN;QAACR,IAAI,EAAE,6BAA6B;QAAEC,SAAS,EAAER,mCAAmC;QAAES,IAAI,EAAE,IAAIb,QAAQ,CAAC,2CAA2C,EAAE,CAACM,SAAS,CAACQ,WAAW,CAACG,oBAAoB,CAACG,MAAM,CAAC;MAAC,CAAC,CAC9M,CAAC,EAGFrB,YAAY;IAAA;EAAA;;;2EAGPU,mBAAmB;IAAAY,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAHxBxB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}