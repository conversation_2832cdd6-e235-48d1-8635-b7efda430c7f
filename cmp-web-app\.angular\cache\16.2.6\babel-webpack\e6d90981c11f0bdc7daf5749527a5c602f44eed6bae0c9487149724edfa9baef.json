{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class RolesService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/role\";\n  }\n  demo(url, params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(url, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  getTreeRoles(callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/get-all-permission`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  getById(id, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  deleteRole(id, callback, errorCallBack, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  editRole(id, role, callback, errorCallBack, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/${id}`, {}, role, {}, callback, errorCallBack, finallyCallback);\n  }\n  createRole(role, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}`, {}, role, {}, callback, errorCallBack, finallyCallback);\n  }\n  checkName(query, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/checkExist`, {}, query, callback, errorCallBack, finallyCallback);\n  }\n  changeStatusRole(id, callback, errorCallBack, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/update-status/${id}`, {}, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function RolesService_Factory(t) {\n      return new (t || RolesService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RolesService,\n      factory: RolesService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "RolesService", "constructor", "httpService", "prefixApi", "demo", "url", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "search", "getTreeRoles", "getById", "id", "deleteRole", "delete", "editRole", "role", "put", "createRole", "post", "checkName", "query", "changeStatusRole", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\account\\RolesService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class RolesService{\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/role\";\r\n    }\r\n\r\n    public demo(url:string, params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(url,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public search(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/search`,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n    public getTreeRoles( callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/get-all-permission`,{}, {},callback, errorCallBack, finallyCallback);\r\n    }\r\n    public getById(id:number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/${id}`,{}, {},callback, errorCallBack, finallyCallback);\r\n    }\r\n    public deleteRole(id:string, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.delete(`${this.prefixApi}/${id}`,{}, {},callback, errorCallBack, finallyCallback);\r\n    }\r\n    public editRole(id:number, role:{name?: string, description?:string, type?:number, status?:number, permissionIds?:Array<number>}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.put(`${this.prefixApi}/${id}`,{}, role,{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public createRole(role:{name?: string, description?:string, type?:number, status?:number, permissionIds?:Array<number>}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}`,{}, role, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n    public checkName(query:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/checkExist`,{}, query,callback, errorCallBack, finallyCallback);\r\n    }\r\n    public changeStatusRole(id:number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.put(`${this.prefixApi}/update-status/${id}`,{}, {}, {},callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,YAAY;EAErBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,OAAO;EAC5B;EAEOC,IAAIA,CAACC,GAAU,EAAEC,MAA8B,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC1H,IAAI,CAACP,WAAW,CAACQ,GAAG,CAACL,GAAG,EAAC,EAAE,EAAEC,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACjF;EAEOE,MAAMA,CAACL,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC3G,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,SAAS,EAAC,EAAE,EAAEG,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;EACOG,YAAYA,CAAEL,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACvF,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,qBAAqB,EAAC,EAAE,EAAE,EAAE,EAACI,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAChH;EACOI,OAAOA,CAACC,EAAS,EAAEP,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC5F,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,IAAIW,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAACP,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACnG;EACOM,UAAUA,CAACD,EAAS,EAAEP,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC/F,IAAI,CAACP,WAAW,CAACc,MAAM,CAAC,GAAG,IAAI,CAACb,SAAS,IAAIW,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAACP,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACtG;EACOQ,QAAQA,CAACH,EAAS,EAAEI,IAAqG,EAAEX,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACpM,IAAI,CAACP,WAAW,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,SAAS,IAAIW,EAAE,EAAE,EAAC,EAAE,EAAEI,IAAI,EAAC,EAAE,EAAEX,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzG;EACOW,UAAUA,CAACF,IAAqG,EAAEX,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC3L,IAAI,CAACP,WAAW,CAACmB,IAAI,CAAC,GAAG,IAAI,CAAClB,SAAS,EAAE,EAAC,EAAE,EAAEe,IAAI,EAAE,EAAE,EAAEX,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrG;EACOa,SAASA,CAACC,KAAwB,EAAEhB,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC7G,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,aAAa,EAAC,EAAE,EAAEoB,KAAK,EAAChB,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC3G;EACOe,gBAAgBA,CAACV,EAAS,EAAEP,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACrG,IAAI,CAACP,WAAW,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,SAAS,kBAAkBW,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAACP,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACrH;;;uBAjCST,YAAY,EAAAyB,EAAA,CAAAC,QAAA,CAED3B,WAAW;IAAA;EAAA;;;aAFtBC,YAAY;MAAA2B,OAAA,EAAZ3B,YAAY,CAAA4B;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}