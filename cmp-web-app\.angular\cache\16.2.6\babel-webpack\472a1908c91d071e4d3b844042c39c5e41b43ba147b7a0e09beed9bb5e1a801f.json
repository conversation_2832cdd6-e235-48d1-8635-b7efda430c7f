{"ast": null, "code": "export default {\n  label: {\n    createdDate: \"Created date\",\n    username: \"<PERSON>rname\",\n    ip: \"IP\",\n    actionType: \"Action Type\",\n    module: \"Module\",\n    affectedField: \"The data field is affected\",\n    viewAccount: \"View account\",\n    detail: \"Detail\"\n  },\n  menu: {\n    log: 'View activity log'\n  },\n  actionType: {\n    search: \"Search\",\n    create: \"Create\",\n    update: \"Update\",\n    delete: \"Delete\"\n  },\n  objectKey: {\n    user: \"User\",\n    customer: \"Customer\",\n    contract: \"Contract\",\n    sim: \"Subscription\",\n    report: \"Report\"\n  }\n};", "map": {"version": 3, "names": ["label", "createdDate", "username", "ip", "actionType", "module", "affectedField", "viewAccount", "detail", "menu", "log", "search", "create", "update", "delete", "object<PERSON>ey", "user", "customer", "contract", "sim", "report"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\logs.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        createdDate : \"Created date\",\r\n        username : \"<PERSON>rname\",\r\n        ip : \"IP\",\r\n        actionType : \"Action Type\",\r\n        module : \"Module\",\r\n        affectedField : \"The data field is affected\",\r\n        viewAccount : \"View account\",\r\n        detail: \"Detail\",\r\n    },\r\n    menu : {\r\n        log : 'View activity log'\r\n    },\r\n    actionType: {\r\n        search : \"Search\",\r\n        create : \"Create\",\r\n        update : \"Update\",\r\n        delete : \"Delete\"\r\n    },\r\n    objectKey: {\r\n        user : \"User\",\r\n        customer : \"Customer\",\r\n        contract : \"Contract\",\r\n        sim : \"Subscription\",\r\n        report : \"Report\"\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,WAAW,EAAG,cAAc;IAC5BC,QAAQ,EAAG,UAAU;IACrBC,EAAE,EAAG,IAAI;IACTC,UAAU,EAAG,aAAa;IAC1BC,MAAM,EAAG,QAAQ;IACjBC,aAAa,EAAG,4BAA4B;IAC5CC,WAAW,EAAG,cAAc;IAC5BC,MAAM,EAAE;GACX;EACDC,IAAI,EAAG;IACHC,GAAG,EAAG;GACT;EACDN,UAAU,EAAE;IACRO,MAAM,EAAG,QAAQ;IACjBC,MAAM,EAAG,QAAQ;IACjBC,MAAM,EAAG,QAAQ;IACjBC,MAAM,EAAG;GACZ;EACDC,SAAS,EAAE;IACPC,IAAI,EAAG,MAAM;IACbC,QAAQ,EAAG,UAAU;IACrBC,QAAQ,EAAG,UAAU;IACrBC,GAAG,EAAG,cAAc;IACpBC,MAAM,EAAG;;CAEhB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}