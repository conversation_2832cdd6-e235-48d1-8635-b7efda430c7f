{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/observable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../comon/http.service\";\nexport class SessionService {\n  constructor(httpService, observableService, router) {\n    this.httpService = httpService;\n    this.observableService = observableService;\n    this.router = router;\n    this.authPrefixApi = \"/auth\";\n    this.userPrefixApi = \"/user-mgmt\";\n    if (localStorage.getItem(\"userInfo\")) {\n      this.userInfo = JSON.parse(localStorage.getItem(\"userInfo\"));\n    } else {\n      this.userInfo = {};\n    }\n    if (localStorage.getItem(\"confirmPolicyHistory\")) {\n      this.confirmPolicyHistory = JSON.parse(localStorage.getItem(\"confirmPolicyHistory\"));\n    } else {\n      this.confirmPolicyHistory = [];\n    }\n  }\n  getData(key) {\n    return localStorage.getItem(key);\n  }\n  setData(key, data) {\n    localStorage.setItem(key, data);\n  }\n  updateToken(token) {\n    this.httpService.updateHeader(token);\n  }\n  login(body, callback, errorCallBack, finallyCallback) {\n    this.httpService.postNoHeader(`${this.authPrefixApi}/token`, {}, body, {}, callback, errorCallBack, finallyCallback);\n  }\n  logout(callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.authPrefixApi}/logout`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  current(callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.userPrefixApi}/current`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function SessionService_Factory(t) {\n      return new (t || SessionService)(i0.ɵɵinject(HttpService), i0.ɵɵinject(i1.ObservableService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SessionService,\n      factory: SessionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "SessionService", "constructor", "httpService", "observableService", "router", "authPrefixApi", "userPrefixApi", "localStorage", "getItem", "userInfo", "JSON", "parse", "confirmPolicyHistory", "getData", "key", "setData", "data", "setItem", "updateToken", "token", "updateHeader", "login", "body", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "postNoHeader", "logout", "<PERSON><PERSON><PERSON><PERSON>", "post", "current", "get", "i0", "ɵɵinject", "i1", "ObservableService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\session\\SessionService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\nimport {Router} from \"@angular/router\";\r\nimport { ObservableService } from \"../comon/observable.service\";\r\nimport { CONSTANTS } from \"../comon/constants\";\r\n\r\n@Injectable({ providedIn: 'root'})\r\nexport class SessionService{\r\n    private authPrefixApi: string;\r\n    private userPrefixApi: string;\r\n    public userInfo:any;\r\n    public token:string;\r\n    public confirmPolicyHistory: Array<any>;\r\n    constructor(@Inject(HttpService) private httpService:HttpService, private observableService: ObservableService,\r\n                private router : Router) {\r\n        this.authPrefixApi = \"/auth\";\r\n        this.userPrefixApi = \"/user-mgmt\";\r\n        if(localStorage.getItem(\"userInfo\")) {\r\n            this.userInfo = JSON.parse(localStorage.getItem(\"userInfo\"));\r\n        }else{\r\n            this.userInfo = {};\r\n        }\r\n        if(localStorage.getItem(\"confirmPolicyHistory\")) {\r\n            this.confirmPolicyHistory = JSON.parse(localStorage.getItem(\"confirmPolicyHistory\"));\r\n        }else{\r\n            this.confirmPolicyHistory = [];\r\n        }\r\n    }\r\n\r\n    getData(key: string): string | undefined {\r\n        return localStorage.getItem(key);\r\n    }\r\n\r\n    setData(key: string, data: string): void {\r\n        localStorage.setItem(key, data);\r\n    }\r\n\r\n    updateToken(token) {\r\n        this.httpService.updateHeader(token);\r\n    }\r\n\r\n    public login(body, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.postNoHeader(`${this.authPrefixApi}/token`, {}, body,{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public logout(callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.authPrefixApi}/logout`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public current(callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.userPrefixApi}/current`,{},{}, callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;;;AAMnD,OAAM,MAAOC,cAAc;EAMvBC,YAAyCC,WAAuB,EAAUC,iBAAoC,EAC1FC,MAAe;IADM,KAAAF,WAAW,GAAXA,WAAW;IAAsB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACvE,KAAAC,MAAM,GAANA,MAAM;IACtB,IAAI,CAACC,aAAa,GAAG,OAAO;IAC5B,IAAI,CAACC,aAAa,GAAG,YAAY;IACjC,IAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAE;MACjC,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;KAC/D,MAAI;MACD,IAAI,CAACC,QAAQ,GAAG,EAAE;;IAEtB,IAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAC7C,IAAI,CAACI,oBAAoB,GAAGF,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;KACvF,MAAI;MACD,IAAI,CAACI,oBAAoB,GAAG,EAAE;;EAEtC;EAEAC,OAAOA,CAACC,GAAW;IACf,OAAOP,YAAY,CAACC,OAAO,CAACM,GAAG,CAAC;EACpC;EAEAC,OAAOA,CAACD,GAAW,EAAEE,IAAY;IAC7BT,YAAY,CAACU,OAAO,CAACH,GAAG,EAAEE,IAAI,CAAC;EACnC;EAEAE,WAAWA,CAACC,KAAK;IACb,IAAI,CAACjB,WAAW,CAACkB,YAAY,CAACD,KAAK,CAAC;EACxC;EAEOE,KAAKA,CAACC,IAAI,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACrF,IAAI,CAACvB,WAAW,CAACwB,YAAY,CAAC,GAAG,IAAI,CAACrB,aAAa,QAAQ,EAAE,EAAE,EAAEiB,IAAI,EAAC,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACvH;EAEOE,MAAMA,CAACJ,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IAClF,IAAI,CAACvB,WAAW,CAAC2B,IAAI,CAAC,GAAG,IAAI,CAACxB,aAAa,SAAS,EAAC,EAAE,EAAE,EAAE,EAAEkB,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EAC1G;EAEOK,OAAOA,CAACP,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACjF,IAAI,CAACvB,WAAW,CAAC6B,GAAG,CAAC,GAAG,IAAI,CAACzB,aAAa,UAAU,EAAC,EAAE,EAAC,EAAE,EAAEiB,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACzG;;;uBA5CSzB,cAAc,EAAAgC,EAAA,CAAAC,QAAA,CAMHlC,WAAW,GAAAiC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aANtBrC,cAAc;MAAAsC,OAAA,EAAdtC,cAAc,CAAAuC,IAAA;MAAAC,UAAA,EADD;IAAM;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}