{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class ContractService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/contract\";\n  }\n  searchContract(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(this.prefixApi + \"/search\", {\n      timeout: 180000\n    }, params, callback, errorCallBack, finallyCallback);\n  }\n  search(params, callback) {\n    this.httpService.get(this.prefixApi + \"/search\", {\n      timeout: 180000\n    }, params, callback);\n  }\n  getById(id, callback) {\n    this.httpService.get(this.prefixApi + \"/\" + id, {}, {}, callback);\n  }\n  getByKey(key, value, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/getByKey`, {}, {\n      key,\n      value\n    }, callback, errorCallback, finallyCallback);\n  }\n  // public getAllSumaryCustomer(callback: Function, errorCallBack?: Function, finallyCallback?: Function){\n  //     this.httpService.get(`${this.prefixApi}/get-list-customer`,{},{},callback,errorCallBack,finallyCallback);\n  // }\n  quickSearchContract(params, body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/quick-search`, {\n      timeout: 180000\n    }, body, params, callback, errorCallback, finallyCallback);\n  }\n  static {\n    this.ɵfac = function ContractService_Factory(t) {\n      return new (t || ContractService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ContractService,\n      factory: ContractService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "ContractService", "constructor", "httpService", "prefixApi", "searchContract", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "timeout", "search", "getById", "id", "get<PERSON><PERSON><PERSON><PERSON>", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "quickSearchContract", "body", "post", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\contract\\ContractService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class ContractService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/contract\";\r\n    }\r\n\r\n    public searchContract(params:{[key: string]: string},callback:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(this.prefixApi+\"/search\",{timeout: 180000},params, callback,errorCallBack,finallyCallback);\r\n    }\r\n\r\n    public search(params:{[key: string]: string},callback:Function){\r\n        this.httpService.get(this.prefixApi+\"/search\",{timeout: 180000},params, callback);\r\n    }\r\n\r\n    public getById(id,callback:Function){\r\n        this.httpService.get(this.prefixApi+\"/\"+id,{},{}, callback);\r\n    }\r\n\r\n    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    // public getAllSumaryCustomer(callback: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n    //     this.httpService.get(`${this.prefixApi}/get-list-customer`,{},{},callback,errorCallBack,finallyCallback);\r\n    // }\r\n    public quickSearchContract(params:{[key: string]:string}, body ,callback?:Function, errorCallback?:Function, finallyCallback?: Function ){\r\n        this.httpService.post(`${this.prefixApi}/quick-search`, {timeout: 180000}, body, params, callback, errorCallback, finallyCallback)\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,eAAe;EAExBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,WAAW;EAChC;EAEOC,cAAcA,CAACC,MAA8B,EAACC,QAAiB,EAAEC,aAAuB,EAAEC,eAAyB;IACtH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,SAAS,EAAC;MAACO,OAAO,EAAE;IAAM,CAAC,EAACL,MAAM,EAAEC,QAAQ,EAACC,aAAa,EAACC,eAAe,CAAC;EACnH;EAEOG,MAAMA,CAACN,MAA8B,EAACC,QAAiB;IAC1D,IAAI,CAACJ,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,SAAS,EAAC;MAACO,OAAO,EAAE;IAAM,CAAC,EAACL,MAAM,EAAEC,QAAQ,CAAC;EACrF;EAEOM,OAAOA,CAACC,EAAE,EAACP,QAAiB;IAC/B,IAAI,CAACJ,WAAW,CAACO,GAAG,CAAC,IAAI,CAACN,SAAS,GAAC,GAAG,GAACU,EAAE,EAAC,EAAE,EAAC,EAAE,EAAEP,QAAQ,CAAC;EAC/D;EAEOQ,QAAQA,CAACC,GAAW,EAAEC,KAAa,EAAEV,QAAmB,EAAEW,aAAuB,EAAET,eAA0B;IAChH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,WAAW,EAAC,EAAE,EAAE;MAACY,GAAG;MAAEC;IAAK,CAAC,EAAEV,QAAQ,EAAEW,aAAa,EAAET,eAAe,CAAC;EACjH;EAEA;EACA;EACA;EACOU,mBAAmBA,CAACb,MAA6B,EAAEc,IAAI,EAAEb,QAAkB,EAAEW,aAAuB,EAAET,eAA0B;IACnI,IAAI,CAACN,WAAW,CAACkB,IAAI,CAAC,GAAG,IAAI,CAACjB,SAAS,eAAe,EAAE;MAACO,OAAO,EAAE;IAAM,CAAC,EAAES,IAAI,EAAEd,MAAM,EAAEC,QAAQ,EAAEW,aAAa,EAAET,eAAe,CAAC;EACtI;;;uBA3BSR,eAAe,EAAAqB,EAAA,CAAAC,QAAA,CAEJvB,WAAW;IAAA;EAAA;;;aAFtBC,eAAe;MAAAuB,OAAA,EAAfvB,eAAe,CAAAwB;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}