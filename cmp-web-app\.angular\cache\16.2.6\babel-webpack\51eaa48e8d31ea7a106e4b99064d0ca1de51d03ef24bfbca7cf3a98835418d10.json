{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ButtonModule } from 'primeng/button';\nimport { GuideRoutingModule } from './app.guide.routing.module';\nimport { AppGuideComponent } from './app.guide.component';\nimport { CommonVnptModule } from '../../common-module/common.module';\nimport { RippleModule } from 'primeng/ripple';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { LayoutService } from 'src/app/service/app.layout.service';\nimport { GuideService } from 'src/app/service/guide/GuideService';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { DividerModule } from 'primeng/divider';\nimport { TieredMenuModule } from 'primeng/tieredmenu';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { AppGuideIntegrationComponent } from \"./integration/app.guide.integration.component\";\nimport * as i0 from \"@angular/core\";\nexport class GuideModule {\n  static {\n    this.ɵfac = function GuideModule_Factory(t) {\n      return new (t || GuideModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GuideModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [LayoutService, GuideService],\n      imports: [CommonModule, GuideRoutingModule, ButtonModule, CommonVnptModule, RippleModule, CommonVnptModule, OverlayPanelModule, TooltipModule, PanelMenuModule, DividerModule, TieredMenuModule, BreadcrumbModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GuideModule, {\n    declarations: [AppGuideComponent, AppGuideIntegrationComponent],\n    imports: [CommonModule, GuideRoutingModule, ButtonModule, CommonVnptModule, RippleModule, CommonVnptModule, OverlayPanelModule, TooltipModule, PanelMenuModule, DividerModule, TieredMenuModule, BreadcrumbModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ButtonModule", "GuideRoutingModule", "AppGuideComponent", "CommonVnptModule", "RippleModule", "OverlayPanelModule", "TooltipModule", "LayoutService", "GuideService", "PanelMenuModule", "DividerModule", "TieredMenuModule", "BreadcrumbModule", "AppGuideIntegrationComponent", "GuideModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\guide\\app.guide.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { GuideRoutingModule } from './app.guide.routing.module';\r\nimport { AppGuideComponent } from './app.guide.component';\r\nimport { CommonVnptModule } from '../../common-module/common.module';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { LayoutService } from 'src/app/service/app.layout.service';\r\nimport { GuideService } from 'src/app/service/guide/GuideService';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { DividerModule } from 'primeng/divider';\r\nimport { TieredMenuModule } from 'primeng/tieredmenu';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport {AppGuideIntegrationComponent} from \"./integration/app.guide.integration.component\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        GuideRoutingModule,\r\n        ButtonModule,\r\n        CommonVnptModule,\r\n        RippleModule,\r\n        CommonVnptModule,\r\n        OverlayPanelModule,\r\n        TooltipModule,\r\n        PanelMenuModule,\r\n        DividerModule,\r\n        TieredMenuModule,\r\n        BreadcrumbModule,\r\n    ],\r\n    declarations: [\r\n        AppGuideComponent,\r\n        AppGuideIntegrationComponent,\r\n    ],\r\n    providers: [LayoutService, GuideService]\r\n})\r\nexport class GuideModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAAQC,4BAA4B,QAAO,+CAA+C;;AAuB1F,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;iBAFT,CAACP,aAAa,EAAEC,YAAY,CAAC;MAAAO,OAAA,GAjBpChB,YAAY,EACZE,kBAAkB,EAClBD,YAAY,EACZG,gBAAgB,EAChBC,YAAY,EACZD,gBAAgB,EAChBE,kBAAkB,EAClBC,aAAa,EACbG,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB;IAAA;EAAA;;;2EAQXE,WAAW;IAAAE,YAAA,GALhBd,iBAAiB,EACjBW,4BAA4B;IAAAE,OAAA,GAf5BhB,YAAY,EACZE,kBAAkB,EAClBD,YAAY,EACZG,gBAAgB,EAChBC,YAAY,EACZD,gBAAgB,EAChBE,kBAAkB,EAClBC,aAAa,EACbG,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}