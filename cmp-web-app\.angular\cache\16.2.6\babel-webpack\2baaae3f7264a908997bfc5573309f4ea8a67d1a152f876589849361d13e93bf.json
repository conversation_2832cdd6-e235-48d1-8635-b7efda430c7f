{"ast": null, "code": "export default {\n  label: {\n    email: \"<PERSON><PERSON><PERSON> điện tử\",\n    password: \"<PERSON><PERSON><PERSON> khẩu\",\n    signIn: \"<PERSON><PERSON><PERSON> nhập\",\n    forgotPass: \"<PERSON>u<PERSON><PERSON> mật khẩu\",\n    m2mTitle: \"<PERSON><PERSON> thống Quản lý <PERSON>huê bao M2M\",\n    resetPass: \"<PERSON><PERSON><PERSON><PERSON> phục mật khẩu\",\n    editProfile: \"Thông tin tài khoản\",\n    getProfileUser: \"Xem chi tiết thông tin cá nhân\",\n    updateProfile: \"Cập nhật thông tin cá nhân\",\n    changePassword: \"Đ<PERSON>i mật khẩu\",\n    logout: \"<PERSON><PERSON>ng xuất\"\n  },\n  text: {}\n};", "map": {"version": 3, "names": ["label", "email", "password", "signIn", "forgot<PERSON>ass", "m2mTitle", "resetPass", "editProfile", "getProfileUser", "updateProfile", "changePassword", "logout", "text"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\login.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        email: \"<PERSON><PERSON><PERSON> điện tử\",\r\n        password: \"<PERSON><PERSON><PERSON> khẩu\",\r\n        signIn : \"<PERSON><PERSON><PERSON> nhập\",\r\n        forgotPass : \"<PERSON>u<PERSON><PERSON> mật khẩu\",\r\n        m2mTitle : \"<PERSON><PERSON> thống Quản lý <PERSON>huê bao M2M\",\r\n        resetPass : \"<PERSON><PERSON><PERSON><PERSON> phục mật khẩu\",\r\n        editProfile : \"Thông tin tài khoản\",\r\n        getProfileUser: \"Xem chi tiết thông tin cá nhân\",\r\n        updateProfile: \"Cập nhật thông tin cá nhân\",\r\n        changePassword : \"Đ<PERSON>i mật khẩu\",\r\n        logout : \"<PERSON><PERSON>ng xuất\",\r\n    },\r\n    text: {\r\n\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAG,WAAW;IACpBC,UAAU,EAAG,eAAe;IAC5BC,QAAQ,EAAG,+BAA+B;IAC1CC,SAAS,EAAG,oBAAoB;IAChCC,WAAW,EAAG,qBAAqB;IACnCC,cAAc,EAAE,gCAAgC;IAChDC,aAAa,EAAE,4BAA4B;IAC3CC,cAAc,EAAG,cAAc;IAC/BC,MAAM,EAAG;GACZ;EACDC,IAAI,EAAE;CAGT"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}