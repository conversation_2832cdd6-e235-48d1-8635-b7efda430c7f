{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport DataPage from \"src/app/service/data.page\";\nimport { TermPolicyListComponent } from \"./app.term.policy.list.component\";\nimport { TermPolicyHistoryComponent } from \"./app.term.policy.history.component\";\nimport { CONSTANTS } from \"../../service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class TermPolicyRoutingModule {\n  static {\n    this.ɵfac = function TermPolicyRoutingModule_Factory(t) {\n      return new (t || TermPolicyRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TermPolicyRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: TermPolicyListComponent,\n        data: new DataPage(\"global.menu.termpolicy\", [CONSTANTS.PERMISSIONS.POLICY.PERSONAL])\n      }, {\n        path: \"history\",\n        component: TermPolicyHistoryComponent,\n        data: new DataPage(\"global.menu.termpolicyhistory\", [CONSTANTS.PERMISSIONS.POLICY.PERSONAL])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TermPolicyRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();\n;", "map": {"version": 3, "names": ["RouterModule", "DataPage", "TermPolicyListComponent", "TermPolicyHistoryComponent", "CONSTANTS", "TermPolicyRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "POLICY", "PERSONAL", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\term-policy\\app.term.policy.routing.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule } from \"@angular/router\";\r\nimport DataPage from \"src/app/service/data.page\";\r\nimport { TermPolicyListComponent } from \"./app.term.policy.list.component\";\r\nimport { TermPolicyHistoryComponent } from \"./app.term.policy.history.component\";\r\nimport {CONSTANTS} from \"../../service/comon/constants\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {path: \"\", component: TermPolicyListComponent, data: new DataPage(\"global.menu.termpolicy\", [CONSTANTS.PERMISSIONS.POLICY.PERSONAL])},\r\n            {path: \"history\", component: TermPolicyHistoryComponent, data: new DataPage(\"global.menu.termpolicyhistory\", [CONSTANTS.PERMISSIONS.POLICY.PERSONAL])},\r\n        ])\r\n    ],\r\n    exports: [RouterModule]\r\n})\r\nexport class TermPolicyRoutingModule {};\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,0BAA0B,QAAQ,qCAAqC;AAChF,SAAQC,SAAS,QAAO,+BAA+B;;;AAWvD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAP5BL,YAAY,CAACM,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEN,uBAAuB;QAAEO,IAAI,EAAE,IAAIR,QAAQ,CAAC,wBAAwB,EAAE,CAACG,SAAS,CAACM,WAAW,CAACC,MAAM,CAACC,QAAQ,CAAC;MAAC,CAAC,EACrI;QAACL,IAAI,EAAE,SAAS;QAAEC,SAAS,EAAEL,0BAA0B;QAAEM,IAAI,EAAE,IAAIR,QAAQ,CAAC,+BAA+B,EAAE,CAACG,SAAS,CAACM,WAAW,CAACC,MAAM,CAACC,QAAQ,CAAC;MAAC,CAAC,CACzJ,CAAC,EAEIZ,YAAY;IAAA;EAAA;;;2EAEbK,uBAAuB;IAAAQ,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFtBf,YAAY;EAAA;AAAA;AAEa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}