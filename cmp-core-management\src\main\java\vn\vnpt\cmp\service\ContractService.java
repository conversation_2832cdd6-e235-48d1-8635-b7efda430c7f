package vn.vnpt.cmp.service;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PostConstruct;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.cmp.base.constants.Constants;
import vn.vnpt.cmp.base.constants.Constants;
import vn.vnpt.cmp.base.constants.MessageKeyConstant;
import vn.vnpt.cmp.base.constants.ResponseCode;
import vn.vnpt.cmp.base.event.Event;
import vn.vnpt.cmp.base.event.constants.AMQPConstants;
import vn.vnpt.cmp.base.jpa.constants.JpaConstants;
import vn.vnpt.cmp.base.jpa.constants.JpaConstants.Method;
import vn.vnpt.cmp.base.jpa.dto.PageInfo;
import vn.vnpt.cmp.base.jpa.dto.req.QuickSearchCustomerContractDTO;
import vn.vnpt.cmp.base.jpa.dto.req.SearchContractRequest;
import vn.vnpt.cmp.base.jpa.dto.req.SearchReferNameReq;
import vn.vnpt.cmp.base.jpa.dto.resp.*;
import vn.vnpt.cmp.base.jpa.entity.Contract;
import vn.vnpt.cmp.base.jpa.entity.Customer;
import vn.vnpt.cmp.base.jpa.entity.User;
import vn.vnpt.cmp.base.jpa.services.CrudService;
import vn.vnpt.cmp.base.jpa.sync.CmpInitInfo;
import vn.vnpt.cmp.base.utils.ObjectMapperUtil;
import vn.vnpt.cmp.model.dto.ISearchContractDTO;
import vn.vnpt.cmp.model.dto.ISearchCustomerDTO;
import vn.vnpt.cmp.repository.ContractRepository;
import vn.vnpt.cmp.repository.CustomerRepository;
import vn.vnpt.cmp.repository.UserRepository;
import vn.vnpt.cmp.util.BaseController;
import vn.vnpt.cmp.util.RequestUtils;

import javax.sql.DataSource;

@Service
@Transactional
public class ContractService extends CrudService<Contract, Long> {

    private ContractRepository contractRepository;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    ApplicationContext applicationContext;

    public ContractService(ContractRepository contractRepository) {
        super(Contract.class);
        this.repository = this.contractRepository = contractRepository;
    }


    public Event process(Event event) {
        switch (event.method) {
            case Method.SEARCH:
                return processSearch(event);
            case Constants.Method.GET_CONTRACT_SYNC:
                return processGetContractSync(event);
            case JpaConstants.Method.CREATE:
                return processCreate(event);
            case Constants.Method.GET_CONTRACT_BY_CUSTOMER_ID:
                return processGetByCustomerId(event);
            case Constants.Method.GET_CONTRACT_BY_CONTRACT_CODE:
                return processGetByContractCode(event);
            case Constants.Method.GET_ALL_CONTRACT_BY_USER:
                return processGetContractByUserId(event);
            case JpaConstants.Method.GET_ONE:
                return processGetOne(event);
            case Constants.Method.QUICK_SEARCH_CUSTOMERS_AND_CONTRACTS:
                return processQuickSearchCustomersAndContracts(event);
            case Constants.Method.SIM_LIST_CONTRACT_DROPDOWN:
                return processDropdownContract(event);
            default:
                return super.process(event);
        }
    }


    private Event processGetContractSync(Event event) {
        CmpInitInfo cmpInitInfo = (CmpInitInfo) event.payload;
        Contract contract = contractRepository.findByContractCode(cmpInitInfo.getContractCode());
        return event.createResponse(contract, ResponseCode.OK, "");
    }

    private Event processDropdownContract(Event event) {
        SearchReferNameReq searchDTO = (SearchReferNameReq) event.payload;
        String provinceCode = event.provinceCode;
        int userType = event.userType;
        Long userId = event.userId;

        SearchContractRequest searchContractRequest = new SearchContractRequest(" ",searchDTO.getName() == null ? " " : searchDTO.getName()," "," ",List.of("-1")," "," "," ",searchDTO.getPage(),searchDTO.getSize(),"contract_code,desc");

        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchContractRequest.getSize(), searchContractRequest.getPage(),
                searchContractRequest.getSortBy());
        Page<ISearchContractDTO> pageContract = null;

        if (userType == Constants.UserType.ADMIN) {
            pageContract = contractRepository.getPageAdmin(searchContractRequest, listRequest.getPageable());
        } else if (userType == Constants.UserType.SUPERVISOR) {
            // cấp tỉnh
            pageContract = contractRepository.getPageSupervisor(searchContractRequest, provinceCode, listRequest.getPageable());
        } else if (userType == Constants.UserType.MANAGER) {
            // cấp gdv
            pageContract = contractRepository.getPageManager(searchContractRequest, provinceCode, userId, listRequest.getPageable());
        } else {
            pageContract = contractRepository.getPageCustomer(searchContractRequest, userId, listRequest.getPageable());
        }
        List<ContractSumaryResponse> userResponseDTOList = pageContract.getContent().stream().map(contractDTO -> {
            ContractSumaryResponse contractRespDTO = new ContractSumaryResponse();
            contractRespDTO.setContractCode(contractDTO.getContractCode());
            return contractRespDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageContract.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event processSearch(Event event) {

        Integer typeUser = event.userType;
        String provinceCode = event.provinceCode;
        Long userId = event.userId;

        SearchContractRequest searchContractRequest = (SearchContractRequest) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchContractRequest.getSize(), searchContractRequest.getPage(),
                searchContractRequest.getSortBy());
        Page<ISearchContractDTO> pageContract = null;

        if (typeUser == Constants.UserType.ADMIN) {
            pageContract = contractRepository.getPageAdmin(searchContractRequest, listRequest.getPageable());
        } else if (typeUser == Constants.UserType.SUPERVISOR) {
            // cấp tỉnh
            pageContract = contractRepository.getPageSupervisor(searchContractRequest, provinceCode, listRequest.getPageable());
        } else if (typeUser == Constants.UserType.MANAGER) {
            // cấp gdv
            pageContract = contractRepository.getPageManager(searchContractRequest, provinceCode, userId, listRequest.getPageable());
        } else {
            pageContract = contractRepository.getPageCustomer(searchContractRequest, userId, listRequest.getPageable());
        }
        List<SearchContractResponseDTO> userResponseDTOList = pageContract.getContent().stream().map(contractDTO -> {
            SearchContractResponseDTO contractRespDTO = new SearchContractResponseDTO();
            BeanUtils.copyProperties(contractDTO, contractRespDTO);
            return contractRespDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageContract.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }


    private Event processGetByCustomerId(Event event) {
        Long customerId = (Long) event.payload;
        List<Long> listValidCustomerId = customerRepository.getAllByUser(event.userType, event.provinceCode, event.userId, customerId).stream().map(e -> e.getId()).collect(
                Collectors.toList());
        if (!listValidCustomerId.contains(customerId)) {
            return event.createResponse(Arrays.asList("customerId"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
        }
//        if (Objects.equals(event.userType, Constants.UserType.CUSTOMER)) {
//            List<String> contractCodes = contractRepository.getAllByUser(event.userType, event.provinceCode, event.userId).stream().map(ISearchContractDTO::getContractCode).toList();
//            List<Contract> contract = contractRepository.findByCustomerIdAndContractCodeIn(customerId, contractCodes);
//            return event.createResponse(contract, ResponseCode.OK, null);
//        }

        List<Contract> contract = contractRepository.findByCustomerId(customerId);

        return event.createResponse(contract, 200, null);
    }

    private Event processGetByContractCode(Event event) {
        String contractCode = (String) event.payload;
        Contract contract = contractRepository.findByContractCode(contractCode);
//        List<Long> listValidCustomerId = customerRepository.getAllByUser(event.userType, event.provinceCode, event.userId).stream().map(e -> e.getId()).collect(
//                Collectors.toList());
//        if (!listValidCustomerId.contains(contract.getCustomerId())) {
//            return event.createResponse(Arrays.asList("code"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
//        }

        Integer typeUser = event.userType;
        String provinceCode = event.provinceCode;
        Long userId = event.userId;
        Integer page = 0;
        Integer size = 10000000;
        String sortBy = "id,desc";
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sortBy);
        Page<ISearchContractDTO> pageContract = null;
        SearchContractRequest searchContractRequest = new SearchContractRequest(" ", contractCode == null ? " " : contractCode, " ", " ", List.of("-1"), " ", " ", " ", page, size, sortBy);
        if (typeUser == Constants.UserType.ADMIN) {
            pageContract = contractRepository.getPageAdmin(searchContractRequest, listRequest.getPageable());
        } else if (typeUser == Constants.UserType.SUPERVISOR) {
            // cấp tỉnh
            pageContract = contractRepository.getPageSupervisor(searchContractRequest, provinceCode, listRequest.getPageable());
        } else if (typeUser == Constants.UserType.MANAGER) {
            // cấp gdv
            pageContract = contractRepository.getPageManager(searchContractRequest, provinceCode, userId, listRequest.getPageable());
        } else {
            pageContract = contractRepository.getPageCustomer(searchContractRequest, userId, listRequest.getPageable());
        }
        List<Long> listContractId = pageContract.getContent().stream().map(e -> e.getId()).collect(Collectors.toList());

        if (listContractId.size() == 0 || !listContractId.contains(contract.getId())) {
            return event.createResponse(Arrays.asList("code"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
        }
        return event.createResponse(contract, ResponseCode.OK, "");

    }

    private Event processGetContractByUserId(Event event) {
        Long userIdOfSearchSim = (Long) event.payload;
//        Optional<User> userSearch = null;
        List<ISearchContractDTO> page = new ArrayList<>();
        Integer typeUser = event.userType;
        String provinceCode = event.provinceCode;
        Long userId = event.userId;
        if (userIdOfSearchSim != null) { // Nếu payload có chưa userId lấy theo payload
            Optional<User> userSearch = userRepository.findById(userIdOfSearchSim);
            if (userSearch.isPresent()) {
                User user = userSearch.get();
                typeUser = user.getType();
                provinceCode = user.getProvinceCode();
                userId = user.getId();
            }
        }

        if (typeUser == Constants.UserType.ADMIN) {
            page = contractRepository.getAllByUserAdmin();
        } else if (typeUser == Constants.UserType.SUPERVISOR) {
            // cấp tỉnh
            page = contractRepository.getAllByUserSupervisor(provinceCode);
        } else if (typeUser == Constants.UserType.MANAGER) {
            // cấp gdv
            page = contractRepository.getAllByUserManager(provinceCode, userId);
        } else {
            page = contractRepository.getAllByUserCustomer(userId);
        }
//        page = contractRepository.getAllByUser(userSearch.get().getType(),userSearch.get().getProvinceCode(), userSearch.get().getId());
        List<IdNameResponse> contractList = page.stream().map(contract -> {
            IdNameResponse idNameResponse = new IdNameResponse();
            idNameResponse.setCode(contract.getContractCode());
            idNameResponse.setId(contract.getId());
            return idNameResponse;
        }).collect(Collectors.toList());

        return event.createResponse(contractList, ResponseCode.OK, null);
    }

    private Event processQuickSearchCustomersAndContracts(Event event) {
        QuickSearchCustomerContractDTO search = (QuickSearchCustomerContractDTO) event.payload;
        // Nếu là tài khoản khách hàng thực hiện thì load danh sách hợp đồng theo tài khoản khách hàng thực hiện
        if (event.userType == Constants.UserType.CUSTOMER) {
            search.setAccountRootId(event.userId);
        }
        search.setAccountRootId(search.getAccountRootId() != null ? search.getAccountRootId() : -1);
        BaseController.MultiSortListRequest listRequest = new BaseController.MultiSortListRequest(search.getSize(), search.getPage(), search.getSortBy());
        //nếu chọn tài khoản khách hàng root thì type lấy theo acc root
        insertSerialTemporary(search.getCustomerIds());
        Page<ICustomerContractSumaryDTO> page = contractRepository.getContractsByCustomerIds(
                search.getAccountRootId(),
                search.getKeyword(),
                listRequest.getPageable()
        );

        List<CustomerContractSumaryResponse> contractList = page.getContent().stream().map(ICustomerContractSumaryDTO -> {
            CustomerContractSumaryResponse contract = new CustomerContractSumaryResponse();
            contract.setId(ICustomerContractSumaryDTO.getId());
            contract.setCustomerCode(ICustomerContractSumaryDTO.getCustomerCode());
            contract.setCustomerName(ICustomerContractSumaryDTO.getCustomerName());
            contract.setContractCode(ICustomerContractSumaryDTO.getContractCode());
            return contract;
        }).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(contractList));
        return event.createResponse(pageInfo, ResponseCode.OK, null);
    }

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void batchInsert(List<Long> entities, String sql) {
//        String sql = "INSERT INTO CUSTOMER_CODE_TEMP_TABLE (customer_code) VALUES (?)";
        jdbcTemplate.batchUpdate(sql, entities, 1000, (ps, entity) -> {
            ps.setLong(1, entity);
        });
    }
    public void insertSerialTemporary(List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            batchInsert(ids, "INSERT INTO CUSTOMER_ID_TEMP (ID) VALUES (?)");
        }


    }

}
