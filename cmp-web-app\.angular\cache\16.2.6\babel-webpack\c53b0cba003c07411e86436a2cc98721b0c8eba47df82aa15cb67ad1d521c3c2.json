{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/comon/translate.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../service/comon/message-common.service\";\nimport * as i4 from \"primeng/button\";\nexport class AccessComponent {\n  constructor(tranService, router, messageCommonService) {\n    this.tranService = tranService;\n    this.router = router;\n    this.messageCommonService = messageCommonService;\n  }\n  goToHome() {\n    // setTimeout(function(){\n    //     window.location.hash = \"/\"\n    // })\n    let me = this;\n    this.messageCommonService.onload();\n    setTimeout(() => {\n      localStorage.clear();\n      me.messageCommonService.offload();\n      window.location.href = \"/#/login\";\n    }, 500);\n  }\n  static {\n    this.ɵfac = function AccessComponent_Factory(t) {\n      return new (t || AccessComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageCommonService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccessComponent,\n      selectors: [[\"app-access\"]],\n      decls: 14,\n      vars: 1,\n      consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [\"src\", \"assets/images/m2m.png\", \"alt\", \"ONEIOT Platform logo\", 1, \"mb-5\", \"w-20rem\", \"flex-shrink-0\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\", \"background\", \"linear-gradient(180deg, rgba(247, 149, 48, 0.4) 10%, rgba(247, 149, 48, 0) 30%)\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", \"flex\", \"flex-column\", \"align-items-center\", 2, \"border-radius\", \"53px\"], [1, \"grid\", \"flex\", \"flex-column\", \"align-items-center\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-orange-500\", \"border-circle\", 2, \"width\", \"3.2rem\", \"height\", \"3.2rem\"], [1, \"text-50\", \"pi\", \"pi-fw\", \"pi-lock\", \"text-2xl\"], [1, \"text-900\", \"font-bold\", \"text-4xl\", \"lg:text-5xl\", \"mb-2\"], [1, \"text-600\", \"mb-5\"], [\"src\", \"assets/images/access/asset-access.svg\", \"alt\", \"Access denied\", \"width\", \"80%\", 1, \"mb-5\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-arrow-left\", 1, \"p-button-text\", 3, \"label\", \"click\"]],\n      template: function AccessComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"h1\", 8);\n          i0.ɵɵtext(9, \"Access Denied\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 9);\n          i0.ɵɵtext(11, \"You do not have the necessary permisions. Please contact admins.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"img\", 10);\n          i0.ɵɵelementStart(13, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AccessComponent_Template_button_click_13_listener() {\n            return ctx.goToHome();\n          });\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.text.homepage\"));\n        }\n      },\n      dependencies: [i4.ButtonDirective],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AccessComponent", "constructor", "tranService", "router", "messageCommonService", "goToHome", "me", "onload", "setTimeout", "localStorage", "clear", "offload", "window", "location", "href", "i0", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "Router", "i3", "MessageCommonService", "selectors", "decls", "vars", "consts", "template", "AccessComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "AccessComponent_Template_button_click_13_listener", "ɵɵadvance", "ɵɵproperty", "translate"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\access\\access.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\access\\access.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { TranslateService } from 'src/app/service/comon/translate.service';\r\nimport {MessageCommonService} from \"../../../service/comon/message-common.service\";\r\n\r\n@Component({\r\n    selector: 'app-access',\r\n    templateUrl: './access.component.html',\r\n})\r\nexport class AccessComponent {\r\n    constructor(public tranService: TranslateService, private router: Router,\r\n    protected messageCommonService: MessageCommonService\r\n    ) {\r\n\r\n    }\r\n\r\n    goToHome(){\r\n        // setTimeout(function(){\r\n        //     window.location.hash = \"/\"\r\n        // })\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        setTimeout(()=>{\r\n            localStorage.clear();\r\n            me.messageCommonService.offload();\r\n            window.location.href = \"/#/login\";\r\n        }, 500)\r\n    }\r\n}\r\n", "<div class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\r\n    <div class=\"flex flex-column align-items-center justify-content-center\">\r\n        <img src=\"assets/images/m2m.png\" alt=\"ONEIOT Platform logo\" class=\"mb-5 w-20rem flex-shrink-0\">\r\n        <div style=\"border-radius:56px; padding:0.3rem; background: linear-gradient(180deg, rgba(247, 149, 48, 0.4) 10%, rgba(247, 149, 48, 0) 30%);\">\r\n            <div class=\"w-full surface-card py-8 px-5 sm:px-8 flex flex-column align-items-center\" style=\"border-radius:53px\">\r\n                <div class=\"grid flex flex-column align-items-center\">\r\n                    <div class=\"flex justify-content-center align-items-center bg-orange-500 border-circle\" style=\"width:3.2rem; height:3.2rem;\">\r\n                        <i class=\"text-50 pi pi-fw pi-lock text-2xl\"></i>\r\n                    </div>\r\n                    <h1 class=\"text-900 font-bold text-4xl lg:text-5xl mb-2\">Access Denied</h1>\r\n                    <span class=\"text-600 mb-5\">You do not have the necessary permisions. Please contact admins.</span>\r\n                    <img src=\"assets/images/access/asset-access.svg\" alt=\"Access denied\" class=\"mb-5\" width=\"80%\">\r\n                    <button pButton pRipple icon=\"pi pi-arrow-left\" [label]=\"tranService.translate('global.text.homepage')\" class=\"p-button-text\" (click)=\"goToHome()\"></button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;AASA,OAAM,MAAOA,eAAe;EACxBC,YAAmBC,WAA6B,EAAUC,MAAc,EAC9DC,oBAA0C;IADjC,KAAAF,WAAW,GAAXA,WAAW;IAA4B,KAAAC,MAAM,GAANA,MAAM;IACtD,KAAAC,oBAAoB,GAApBA,oBAAoB;EAG9B;EAEAC,QAAQA,CAAA;IACJ;IACA;IACA;IACA,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACF,oBAAoB,CAACG,MAAM,EAAE;IAClCC,UAAU,CAAC,MAAI;MACXC,YAAY,CAACC,KAAK,EAAE;MACpBJ,EAAE,CAACF,oBAAoB,CAACO,OAAO,EAAE;MACjCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU;IACrC,CAAC,EAAE,GAAG,CAAC;EACX;;;uBAlBSd,eAAe,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAftB,eAAe;MAAAuB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5Bd,EAAA,CAAAgB,cAAA,aAAqH;UAE7GhB,EAAA,CAAAiB,SAAA,aAA+F;UAC/FjB,EAAA,CAAAgB,cAAA,aAA8I;UAI9HhB,EAAA,CAAAiB,SAAA,WAAiD;UACrDjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAgB,cAAA,YAAyD;UAAAhB,EAAA,CAAAmB,MAAA,oBAAa;UAAAnB,EAAA,CAAAkB,YAAA,EAAK;UAC3ElB,EAAA,CAAAgB,cAAA,eAA4B;UAAAhB,EAAA,CAAAmB,MAAA,wEAAgE;UAAAnB,EAAA,CAAAkB,YAAA,EAAO;UACnGlB,EAAA,CAAAiB,SAAA,eAA8F;UAC9FjB,EAAA,CAAAgB,cAAA,kBAAmJ;UAArBhB,EAAA,CAAAoB,UAAA,mBAAAC,kDAAA;YAAA,OAASN,GAAA,CAAAzB,QAAA,EAAU;UAAA,EAAC;UAACU,EAAA,CAAAkB,YAAA,EAAS;;;UAA5GlB,EAAA,CAAAsB,SAAA,IAAuD;UAAvDtB,EAAA,CAAAuB,UAAA,UAAAR,GAAA,CAAA5B,WAAA,CAAAqC,SAAA,yBAAuD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}