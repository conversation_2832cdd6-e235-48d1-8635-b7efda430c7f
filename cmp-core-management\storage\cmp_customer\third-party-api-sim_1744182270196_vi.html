<p><span style="font-size: 16px"><strong>Nhóm Public API về thuê bao</strong></span></p><p><span style="font-size: 14px"><strong>1. GetListSimByAccount</strong></span></p><p><span style="font-size: 14px">- <strong><PERSON><PERSON><PERSON> đích sử dụng</strong>: Là API được đối tác sử dụng để lấy danh sách simcard theo tài khoản xác thực</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListSimByAccount?page=1&amp;pageSize=10</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Brearer token<br>   Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>contractCode</p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>page</p></td><td><p>String</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>pageSize</p></td><td><p>String</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr></tbody></table><p><span style="font-size: 14px">​</span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>listSim</p></td><td><p>Json array</p></td><td><p>Danh sách thiết bị. Trong mỗi bản ghi chứa các trường từ 5   à   12</p></td><td><p>Y</p></td></tr><tr><td><p>5</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>ratePlanName</p></td><td><p>String</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr><tr><td><p style="text-align:left">7</p></td><td><p style="text-align:left">status</p></td><td><p style="text-align:left">Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>imsi</p></td><td><p>String</p></td><td><p>imsi</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>simGroupName</p></td><td><p>String</p></td><td><p>Nhóm sim</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>customerName</p></td><td><p>String</p></td><td><p>Tên khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>11</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>12</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr></tbody></table><p><!--[if !supportLists]--><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table><tbody><tr><td><p><span style="font-size: 16px;background-color: transparent">&nbsp;</span><span style="color: black;font-family: Courier New;font-size: 12px;background-color: transparent">{</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">0</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorDesc"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"SUCCESS"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"total"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">22</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"listSim"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;[</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"msisdn"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"8482*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"ratePlanName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"status"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">1</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"imsi"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"45*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"simGroupName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;]</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">}</span></p>​</td></tr></tbody></table><p style="margin-left:.25in;text-align:left"><br><br></p><p><span style="font-size: 14px"><strong>2. GetSimInfo</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là api cho phép lấy thông tin chi tiết của sim</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getSimInfo?msisdn=84823384832</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Brearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong><br></strong></span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><p><br></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>dataUsed</p></td><td><p>Numbẻ</p></td><td><p>Dung lượng sử dụng</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>chargesIncurred</p></td><td><p>String</p></td><td><p>Cước phát sinh</p></td><td><p>N</p></td></tr><tr><td><p style="text-align:left">6</p></td><td><p style="text-align:left">planName</p></td><td><p style="text-align:left">Number</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>apn</p></td><td><p>String</p></td><td><p>apn</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>status</p></td><td><p>Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>imsi</p></td><td><p>String</p></td><td><p>imsi</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>simGroupName</p></td><td><p>String</p></td><td><p>Nhóm sim</p></td><td><p>N</p></td></tr><tr><td><p>11</p></td><td><p style="text-align:left"><span style="color:black">customerName</span></p></td><td><p>String</p></td><td><p>Tên khách hang</p></td><td><p>N</p></td></tr><tr><td><p>12</p></td><td><p style="text-align:left"><span style="color:black">customerCode</span></p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>13</p></td><td><p style="text-align:left"><span style="color:black">contractCode</span></p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>14</p></td><td><p style="text-align:left"><span style="color:black">contractDate</span></p></td><td><p>Date</p></td><td><p>Ngày làm hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>15</p></td><td><p style="text-align:left"><span style="color:black">contractorInfo</span></p></td><td><p>String</p></td><td><p>Người làm hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>16</p></td><td><p style="text-align:left"><span style="color:black">centerCode</span></p></td><td><p>String</p></td><td><p>Mã trung tâm</p></td><td><p>N</p></td></tr><tr><td><p>17</p></td><td><p style="text-align:left"><span style="color:black">contactPhone</span></p></td><td><p>String</p></td><td><p>Số điện thoại liên hệ</p></td><td><p>N</p></td></tr><tr><td><p>18</p></td><td><p style="text-align:left"><span style="color:black">contactAddress</span></p></td><td><p>String</p></td><td><p>Địa chỉ liên hệ</p></td><td><p>N</p></td></tr><tr><td><p>19</p></td><td><p style="text-align:left"><span style="color:black">paymentName</span></p></td><td><p>String</p></td><td><p>Tên thanh toán</p></td><td><p>N</p></td></tr><tr><td><p>20</p></td><td><p style="text-align:left"><span style="color:black">paymentAddress</span></p></td><td><p>String</p></td><td><p>Địa chỉ thanh toán</p></td><td><p>N</p></td></tr><tr><td><p>21</p></td><td><p style="text-align:left"><span style="color:black">routeCode</span></p></td><td><p>String</p></td><td><p>Mã tuyến đường</p></td><td><p>N</p></td></tr><tr><td><p>22</p></td><td><p style="text-align:left"><span style="color:black">birthday</span></p></td><td><p>Date</p></td><td><p>Sinh nhật khách hàng</p></td><td><p>N</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table class="se-table-size-100 se-table-layout-auto"><tbody><tr><td><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">{</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">0</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorDesc"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"SUCCESS"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"msisdn"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"84*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"dataUsed"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"chargesIncurred"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"planName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"apn"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"status"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"imsi"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"simGroupName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractDate"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractorInfo"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"centerCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"3"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contactPhone"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"84125*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contactAddress"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"Số&nbsp;82&nbsp;Chùa&nbsp;Láng&nbsp;(số&nbsp;38&nbsp;cũ)&nbsp;phố&nbsp;Chùa&nbsp;Láng&nbsp;&nbsp;-&nbsp;Láng&nbsp;Thượng&nbsp;-&nbsp;Đống&nbsp;Đa&nbsp;-&nbsp;Hà&nbsp;Nội"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"paymentName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"Công&nbsp;ty&nbsp;cổ&nbsp;phần&nbsp;tư&nbsp;vấn&nbsp;đầu&nbsp;tư&nbsp;và&nbsp;xây&nbsp;dựng&nbsp;bưu&nbsp;điện"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"paymentAddress"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"Số&nbsp;82&nbsp;Chùa&nbsp;Láng&nbsp;(số&nbsp;38&nbsp;cũ)&nbsp;phố&nbsp;Chùa&nbsp;Láng&nbsp;&nbsp;-&nbsp;Láng&nbsp;Thượng&nbsp;-&nbsp;Đống&nbsp;Đa&nbsp;-&nbsp;Hà&nbsp;Nội"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"routeCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"PNT000-99"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"birthday"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"1998-08-14"</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">}</span></p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>3. GetSimInfoToAssign</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là API lấy danh sách số msisdn ứng với imsi đã gán</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getSimInfoToAssign?msisdn=84815147332&amp;imsi=452021163974144</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Brearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color: black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>​<span style="font-size: 14px">msisdn</span>​</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>​<span style="font-size: 14px">imsi</span>​</p></td><td><p>String</p></td><td><p>Số sim</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px">​</span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color: black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin sim</p></td><td><p>Y</p></td></tr><tr><td><p>5</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>​<span style="font-size: 14px">imsi</span>​</p></td><td><p>String</p></td><td><p>Số sim</p></td><td><p>N</p></td></tr><tr><td><p style="text-align: left">7</p></td><td><p style="text-align: left">status</p></td><td><p style="text-align: left">Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>planName</p></td><td><p>String</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table><tbody><tr><td><p>{<br>    &nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>    &nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>    &nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"msisdn": "841388100419",<br>        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"imsi": "452021146247242",<br>        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"planName": "EZ35NEW",<br>        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"status": 2<br>    &nbsp;&nbsp;&nbsp;&nbsp;}<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>4. CountSimByProvinceCode</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là API lấy thông tin số lượng sim hoạt động trong 1 tỉnh</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/countSimByProvinceCode?provinceCode=HNI</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Brearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color: black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>​<span style="font-size: 14px">provinceCode</span>​</p></td><td><p>String</p></td><td><p>Mã tỉnh</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px">​</span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color: black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>status</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>message</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>currentDate</p></td><td><p>Date</p></td><td><p>Ngày request</p></td><td><p>Y</p></td></tr><tr><td><p>5</p></td><td><p>name</p></td><td><p>String</p></td><td><p>M2M SIM</p></td><td><p>Y</p></td></tr><tr><td><p>6</p></td><td><p>​<span style="font-size: 14px">unit</span>​</p></td><td><p>String</p></td><td><p>Thue bao</p></td><td><p>Y<br></p></td></tr><tr><td><p style="text-align: left">7</p></td><td><p style="text-align: left">data</p></td><td><p style="text-align: left">Json</p></td><td><p><br></p></td><td><p>Y<br></p></td></tr><tr><td><div>8</div></td><td><div>MaTinh<br></div></td><td><div>String<br></div></td><td><div>Mã tỉnh</div></td><td><div>Y<br></div></td></tr><tr><td><p>9</p></td><td><p>SoLuong</p></td><td><p>String</p></td><td><p>Số lượng thuê bao</p></td><td><p>Y<br></p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table><tbody><tr><td><p>{<br>    &nbsp;&nbsp;&nbsp;&nbsp;"status": 1,<br>    &nbsp;&nbsp;&nbsp;&nbsp;"message": "SUCCESS",<br>    &nbsp;&nbsp;&nbsp;&nbsp;"currentDate": "2025-04-09",<br>    &nbsp;&nbsp;&nbsp;&nbsp;"name": "M2M SIM",<br>    &nbsp;&nbsp;&nbsp;&nbsp;"unit": "Thue bao",<br>    &nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"MaTinh": "HNI",<br>            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"SoLuong": 442877<br>        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>    &nbsp;&nbsp;&nbsp;&nbsp;]<br>}<br></p></td></tr></tbody></table><p><br></p>