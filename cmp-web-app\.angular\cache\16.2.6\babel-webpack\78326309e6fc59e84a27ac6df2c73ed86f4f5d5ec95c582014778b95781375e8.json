{"ast": null, "code": "export default {\n  label: {\n    username: \"<PERSON><PERSON><PERSON> đăng nhập\",\n    fullname: \"<PERSON><PERSON> và tên\",\n    userType: \"<PERSON>ại tài khoản\",\n    email: \"Email\",\n    provinceCode: \"<PERSON><PERSON> tỉnh\",\n    time: \"<PERSON>h<PERSON><PERSON> gian\",\n    status: \"Tr<PERSON><PERSON> thái\",\n    phone: \"<PERSON><PERSON> điện thoại\",\n    description: \"<PERSON><PERSON> tả\",\n    manager: \"<PERSON><PERSON>p quản lý\",\n    province: \"Tỉnh/Thành phố\",\n    role: \"Nhóm quyền\",\n    permission: {\n      name: \"<PERSON>ê<PERSON> quyền\",\n      object: \"Đối tượng tác động\"\n    },\n    customerName: \"Tên khách hàng\",\n    oldPass: \"Mật khẩu cũ\",\n    newPass: \"Mật khẩu mới\",\n    confirmPass: \"Nhắc lại mật khẩu\",\n    submitChangePass: \"Đồng ý & Đăng nhập\",\n    linkExpired: \"<PERSON>i<PERSON><PERSON> khôi phục mật khẩu này đã hết hạn\",\n    cmpForgotPass: \"M2M SIM - Quên mật khẩu\",\n    deviceType: \"<PERSON><PERSON><PERSON> thiết bị\",\n    os: \"<PERSON><PERSON> điều hành\",\n    ip: \"Địa chỉ IP\",\n    managerName: \"GDV quản lý\",\n    customerAccount: \"Tài khoản khách hàng cấp trên\",\n    generalInfo: \"Thông tin chung\",\n    addCustomerAccount: \"Thêm tài khoản khách hàng\",\n    showCustomerAccount: \"Xem tài khoản khách hàng\",\n    notiChangePass: \"Mật khẩu đã hết hạn sử dụng. Vui lòng đổi lại mật khẩu để sử dụng tiếp. Thời hạn hiệu lực của mật khẩu mới là 6 tháng tính từ ngày thay đổi mật khẩu gần nhất.\"\n  },\n  text: {\n    detailaccount: \"Chi tiết tài khoản\",\n    infoAccount: \"Thông tin tài khoản\",\n    active: \"Đang hoạt động\",\n    inactive: \"Chưa hoạt động\",\n    account: \"Tài khoản\",\n    titleChangeManageLevel: \"Chuyển cấp quản lý\",\n    selectAccount: \"Chọn tài khoản\",\n    inputUsername: \"Nhập tên đăng nhập\",\n    inputFullname: \"Nhập tên đầy đủ\",\n    inputEmail: \"Nhập email\",\n    inputPhone: \"Nhập số điện thoại\",\n    selectUserType: \"Chọn loại tài khoản\",\n    selectRoles: \"Chọn nhóm quyền\",\n    selectManager: \"Chọn cấp quản lý\",\n    selectProvince: \"Chọn tỉnh/thành phố\",\n    selectCustomers: \"Chọn khách hàng\",\n    disagreePolicy: \"Bạn chưa đồng ý với điều khoản chính sách này.\",\n    typeSelectAll: \"Chuyển toàn bộ\",\n    typeSelectList: \"Chuyển theo danh sách\",\n    selectGDV: \"Chọn GDV\",\n    selectCustomerAccount: \"Chọn tài khoản khách hàng cấp trên\",\n    addCustomer: \"Thêm Khách hàng\",\n    addContract: \"Thêm Mã hợp đồng\",\n    grantApi: \"Cấp quyền API\",\n    module: \"Module\",\n    gen: \"Gen\",\n    working: \"Hoạt động\",\n    notWorking: \"Không hoạt động\"\n  },\n  usertype: {\n    admin: \"Admin\",\n    customer: \"Khách hàng\",\n    district: \"Giao dịch viên\",\n    province: \"Tỉnh/Thành phố\",\n    agency: \"Đại lý\"\n  },\n  userstatus: {\n    active: \"Đang hoạt động\",\n    inactive: \"Không hoạt động\"\n  },\n  button: {\n    disagreePolicy: \"Phản đối, hạn chế, rút lại đồng ý chính sách\",\n    viewPolicyProtectPersonalData: \"Xem chính sách bảo vệ dữ liệu cá nhân\"\n  },\n  message: {\n    customerRequired: \"Phải chọn ít nhất một khách hàng\",\n    managerRequired: '\"GDV quản lý\" không được để trống'\n  }\n};", "map": {"version": 3, "names": ["label", "username", "fullname", "userType", "email", "provinceCode", "time", "status", "phone", "description", "manager", "province", "role", "permission", "name", "object", "customerName", "old<PERSON><PERSON>", "newPass", "confirmPass", "submitChangePass", "linkExpired", "cmpForgotPass", "deviceType", "os", "ip", "<PERSON><PERSON><PERSON>", "customerAccount", "generalInfo", "addCustomerAccount", "showCustomerAccount", "notiChangePass", "text", "detailaccount", "infoAccount", "active", "inactive", "account", "titleChangeManageLevel", "selectAccount", "inputUsername", "inputFullname", "inputEmail", "inputPhone", "selectUserType", "selectRoles", "selectManager", "selectProvince", "selectCustomers", "disagreePolicy", "typeSelectAll", "typeSelectList", "selectGDV", "selectCustomerAccount", "addCustomer", "addContract", "grantApi", "module", "gen", "working", "notWorking", "usertype", "admin", "customer", "district", "agency", "userstatus", "button", "viewPolicyProtectPersonalData", "message", "customerRequired", "manager<PERSON><PERSON><PERSON>d"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\account.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        username: \"<PERSON><PERSON><PERSON> đăng nhập\",\r\n        fullname: \"<PERSON><PERSON> và tên\",\r\n        userType: \"<PERSON>ại tài khoản\",\r\n        email: \"Email\",\r\n        provinceCode: \"<PERSON><PERSON> tỉnh\",\r\n        time: \"<PERSON>h<PERSON><PERSON> gian\",\r\n        status: \"Tr<PERSON><PERSON> thái\",\r\n        phone: \"<PERSON><PERSON> điện thoại\",\r\n        description: \"<PERSON><PERSON> tả\",\r\n        manager: \"<PERSON><PERSON>p quản lý\",\r\n        province: \"Tỉnh/Thành phố\",\r\n        role: \"Nhóm quyền\",\r\n        permission:{\r\n            name: \"<PERSON>ê<PERSON> quyền\",\r\n            object: \"Đối tượng tác động\"\r\n        },\r\n        customerName: \"Tên khách hàng\",\r\n        oldPass : \"Mật khẩu cũ\",\r\n        newPass : \"Mật khẩu mới\",\r\n        confirmPass: \"Nhắc lại mật khẩu\",\r\n        submitChangePass : \"Đồng ý & Đăng nhập\",\r\n        linkExpired : \"<PERSON>i<PERSON><PERSON> khôi phục mật khẩu này đã hết hạn\",\r\n        cmpForgotPass : \"M2M SIM - Quên mật khẩu\",\r\n        deviceType: \"<PERSON><PERSON><PERSON> thiết bị\",\r\n        os: \"<PERSON><PERSON> điều hành\",\r\n        ip: \"Địa chỉ IP\",\r\n        managerName : \"GDV quản lý\",\r\n        customerAccount : \"Tài khoản khách hàng cấp trên\",\r\n        generalInfo: \"Thông tin chung\",\r\n        addCustomerAccount : \"Thêm tài khoản khách hàng\",\r\n        showCustomerAccount : \"Xem tài khoản khách hàng\",\r\n        notiChangePass: \"Mật khẩu đã hết hạn sử dụng. Vui lòng đổi lại mật khẩu để sử dụng tiếp. Thời hạn hiệu lực của mật khẩu mới là 6 tháng tính từ ngày thay đổi mật khẩu gần nhất.\"\r\n    },\r\n    text: {\r\n        detailaccount: \"Chi tiết tài khoản\",\r\n        infoAccount: \"Thông tin tài khoản\",\r\n        active: \"Đang hoạt động\",\r\n        inactive: \"Chưa hoạt động\",\r\n        account: \"Tài khoản\",\r\n        titleChangeManageLevel: \"Chuyển cấp quản lý\",\r\n        selectAccount: \"Chọn tài khoản\",\r\n        inputUsername: \"Nhập tên đăng nhập\",\r\n        inputFullname: \"Nhập tên đầy đủ\",\r\n        inputEmail: \"Nhập email\",\r\n        inputPhone: \"Nhập số điện thoại\",\r\n        selectUserType: \"Chọn loại tài khoản\",\r\n        selectRoles: \"Chọn nhóm quyền\",\r\n        selectManager: \"Chọn cấp quản lý\",\r\n        selectProvince: \"Chọn tỉnh/thành phố\",\r\n        selectCustomers: \"Chọn khách hàng\",\r\n        disagreePolicy: \"Bạn chưa đồng ý với điều khoản chính sách này.\",\r\n        typeSelectAll: \"Chuyển toàn bộ\",\r\n        typeSelectList: \"Chuyển theo danh sách\",\r\n        selectGDV : \"Chọn GDV\",\r\n        selectCustomerAccount : \"Chọn tài khoản khách hàng cấp trên\",\r\n        addCustomer: \"Thêm Khách hàng\",\r\n        addContract: \"Thêm Mã hợp đồng\",\r\n        grantApi : \"Cấp quyền API\",\r\n        module : \"Module\",\r\n        gen : \"Gen\",\r\n        working: \"Hoạt động\",\r\n        notWorking : \"Không hoạt động\"\r\n    },\r\n    usertype: {\r\n        admin: \"Admin\",\r\n        customer: \"Khách hàng\",\r\n        district: \"Giao dịch viên\",\r\n        province: \"Tỉnh/Thành phố\",\r\n        agency: \"Đại lý\"\r\n    },\r\n    userstatus: {\r\n        active: \"Đang hoạt động\",\r\n        inactive: \"Không hoạt động\"\r\n    },\r\n    button: {\r\n        disagreePolicy: \"Phản đối, hạn chế, rút lại đồng ý chính sách\",\r\n        viewPolicyProtectPersonalData: \"Xem chính sách bảo vệ dữ liệu cá nhân\"\r\n    },\r\n    message: {\r\n        customerRequired: \"Phải chọn ít nhất một khách hàng\",\r\n        managerRequired: '\"GDV quản lý\" không được để trống'\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,gBAAgB;IAC1BC,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,SAAS;IACvBC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,OAAO;IACpBC,OAAO,EAAE,aAAa;IACtBC,QAAQ,EAAE,gBAAgB;IAC1BC,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,MAAM,EAAE;KACX;IACDC,YAAY,EAAE,gBAAgB;IAC9BC,OAAO,EAAG,aAAa;IACvBC,OAAO,EAAG,cAAc;IACxBC,WAAW,EAAE,mBAAmB;IAChCC,gBAAgB,EAAG,oBAAoB;IACvCC,WAAW,EAAG,wCAAwC;IACtDC,aAAa,EAAG,yBAAyB;IACzCC,UAAU,EAAE,eAAe;IAC3BC,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,YAAY;IAChBC,WAAW,EAAG,aAAa;IAC3BC,eAAe,EAAG,+BAA+B;IACjDC,WAAW,EAAE,iBAAiB;IAC9BC,kBAAkB,EAAG,2BAA2B;IAChDC,mBAAmB,EAAG,0BAA0B;IAChDC,cAAc,EAAE;GACnB;EACDC,IAAI,EAAE;IACFC,aAAa,EAAE,oBAAoB;IACnCC,WAAW,EAAE,qBAAqB;IAClCC,MAAM,EAAE,gBAAgB;IACxBC,QAAQ,EAAE,gBAAgB;IAC1BC,OAAO,EAAE,WAAW;IACpBC,sBAAsB,EAAE,oBAAoB;IAC5CC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAE,oBAAoB;IACnCC,aAAa,EAAE,iBAAiB;IAChCC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,oBAAoB;IAChCC,cAAc,EAAE,qBAAqB;IACrCC,WAAW,EAAE,iBAAiB;IAC9BC,aAAa,EAAE,kBAAkB;IACjCC,cAAc,EAAE,qBAAqB;IACrCC,eAAe,EAAE,iBAAiB;IAClCC,cAAc,EAAE,gDAAgD;IAChEC,aAAa,EAAE,gBAAgB;IAC/BC,cAAc,EAAE,uBAAuB;IACvCC,SAAS,EAAG,UAAU;IACtBC,qBAAqB,EAAG,oCAAoC;IAC5DC,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE,kBAAkB;IAC/BC,QAAQ,EAAG,eAAe;IAC1BC,MAAM,EAAG,QAAQ;IACjBC,GAAG,EAAG,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAG;GAChB;EACDC,QAAQ,EAAE;IACNC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE,gBAAgB;IAC1BrD,QAAQ,EAAE,gBAAgB;IAC1BsD,MAAM,EAAE;GACX;EACDC,UAAU,EAAE;IACR/B,MAAM,EAAE,gBAAgB;IACxBC,QAAQ,EAAE;GACb;EACD+B,MAAM,EAAE;IACJlB,cAAc,EAAE,8CAA8C;IAC9DmB,6BAA6B,EAAE;GAClC;EACDC,OAAO,EAAE;IACLC,gBAAgB,EAAE,kCAAkC;IACpDC,eAAe,EAAE;;CAExB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}