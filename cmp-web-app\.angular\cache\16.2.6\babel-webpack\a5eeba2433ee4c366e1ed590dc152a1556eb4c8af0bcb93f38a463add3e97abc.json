{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ErrorComponent } from './error.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ErrorRoutingModule {\n  static {\n    this.ɵfac = function ErrorRoutingModule_Factory(t) {\n      return new (t || ErrorRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ErrorRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: '',\n        component: ErrorComponent\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ErrorRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ErrorComponent", "ErrorRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\error\\error-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { ErrorComponent } from './error.component';\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild([\r\n        { path: '', component: ErrorComponent }\r\n    ])],\r\n    exports: [RouterModule]\r\n})\r\nexport class ErrorRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,mBAAmB;;;AAQlD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBALjBF,YAAY,CAACG,QAAQ,CAAC,CAC5B;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEJ;MAAc,CAAE,CAC1C,CAAC,EACQD,YAAY;IAAA;EAAA;;;2EAEbE,kBAAkB;IAAAI,OAAA,GAAAC,EAAA,CAAAP,YAAA;IAAAQ,OAAA,GAFjBR,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}