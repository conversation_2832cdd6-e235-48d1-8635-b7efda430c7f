{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { CommonVnptModule } from \"../common-module/common.module\";\nimport { TestComponent } from \"./test.component\";\nimport { TestRoutingModule } from \"./test.routing.module\";\nimport { DragDropModule } from \"primeng/dragdrop\";\nimport { DialogModule } from \"primeng/dialog\";\nimport * as i0 from \"@angular/core\";\nexport class TestModule {\n  static {\n    this.ɵfac = function TestModule_Factory(t) {\n      return new (t || TestModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TestModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TestRoutingModule, FormsModule, ReactiveFormsModule, CommonVnptModule, DragDropModule, DialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TestModule, {\n    declarations: [TestComponent],\n    imports: [CommonModule, TestRoutingModule, FormsModule, ReactiveFormsModule, CommonVnptModule, DragDropModule, DialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "CommonVnptModule", "TestComponent", "TestRoutingModule", "DragDropModule", "DialogModule", "TestModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\test\\test.module.ts"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\r\nimport { NgModule } from \"@angular/core\";\r\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\r\nimport { CommonVnptModule } from \"../common-module/common.module\";\r\nimport { TestComponent } from \"./test.component\";\r\nimport { TestRoutingModule } from \"./test.routing.module\";\r\nimport { DragDropModule } from \"primeng/dragdrop\";\r\nimport { DialogModule } from \"primeng/dialog\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        TestRoutingModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        CommonVnptModule,\r\n        DragDropModule,\r\n        DialogModule\r\n    ],\r\n    declarations:[\r\n        TestComponent\r\n    ],\r\n    providers:[]\r\n})\r\nexport class TestModule{\r\n\r\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;;AAiB7C,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAbfR,YAAY,EACZK,iBAAiB,EACjBJ,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBG,cAAc,EACdC,YAAY;IAAA;EAAA;;;2EAOPC,UAAU;IAAAC,YAAA,GAJfL,aAAa;IAAAM,OAAA,GATbV,YAAY,EACZK,iBAAiB,EACjBJ,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBG,cAAc,EACdC,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}