{"ast": null, "code": "import { DeviceService } from \"src/app/service/device/DeviceService\";\nimport { CONSTANTS } from \"../../../service/comon/constants\";\nimport { ComponentBase } from \"../../../component.base\";\nimport * as XLSX from 'xlsx';\nimport { SimService } from \"../../../service/sim/SimService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"../../common-module/table/table.component\";\nimport * as i9 from \"../../common-module/input-file/input.file.component\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/dialog\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/panel\";\nimport * as i14 from \"primeng/checkbox\";\nimport * as i15 from \"primeng/togglebutton\";\nimport * as i16 from \"src/app/service/device/DeviceService\";\nimport * as i17 from \"../../../service/sim/SimService\";\nfunction DeviceListComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 59);\n    i0.ɵɵlistener(\"click\", function DeviceListComponent_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.navigateToCreateDevice());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.add\"));\n  }\n}\nfunction DeviceListComponent_p_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 60);\n    i0.ɵɵlistener(\"click\", function DeviceListComponent_p_button_7_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.importByFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r1.tranService.translate(\"global.button.import\"));\n  }\n}\nfunction DeviceListComponent_small_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.messageErrorUpload);\n  }\n}\nfunction DeviceListComponent_span_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1, \"ON\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeviceListComponent_span_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1, \"OFF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeviceListComponent_span_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1, \"NOT FOUND\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function () {\n  return {\n    \"margin\": \"6px\"\n  };\n};\nfunction DeviceListComponent_form_204_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 64)(1, \"div\", 65)(2, \"div\", 10)(3, \"span\", 11)(4, \"input\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_form_204_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.deviceInfo.imei = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"span\", 11)(9, \"input\", 67);\n    i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_form_204_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.deviceInfo.deviceType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"label\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 10)(13, \"span\", 11)(14, \"input\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_form_204_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.deviceInfo.msisdn = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 10)(18, \"span\", 11)(19, \"input\", 69);\n    i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_form_204_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.deviceInfo.country = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"label\", 17);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 10)(23, \"span\", 70)(24, \"p-calendar\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_form_204_Template_p_calendar_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.deviceInfo.expiredDate = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"label\", 72);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 10)(28, \"span\", 11)(29, \"input\", 73);\n    i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_form_204_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.deviceInfo.location = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"label\", 74);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 10)(33, \"label\", 75);\n    i0.ɵɵelement(34, \"p-checkbox\", 76);\n    i0.ɵɵelementStart(35, \"label\", 77);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 78);\n    i0.ɵɵelement(38, \"iframe\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r6.formDetailDevice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.deviceInfo.imei);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.imei\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.deviceInfo.deviceType)(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.deviceType\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.deviceInfo.msisdn);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.msisdn\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.deviceInfo.country)(\"readonly\", true)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.country\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.deviceInfo.expiredDate)(\"disabled\", true)(\"readonlyInput\", true)(\"minDate\", ctx_r6.minDateTo)(\"maxDate\", ctx_r6.maxDateTo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.expireDate\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.deviceInfo.location);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.location\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", true)(\"readonly\", true)(\"binary\", true)(\"ngStyle\", i0.ɵɵpureFunction0(26, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"device.label.iotLink\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r6.safeUrl, i0.ɵɵsanitizeResourceUrl);\n  }\n}\nconst _c1 = function (a0) {\n  return [a0];\n};\nconst _c2 = function () {\n  return {\n    width: \"700px\"\n  };\n};\nconst _c3 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nexport class DeviceListComponent extends ComponentBase {\n  constructor(deviceService, simService, sanitizer, formBuilder, injector) {\n    super(injector);\n    this.deviceService = deviceService;\n    this.simService = simService;\n    this.sanitizer = sanitizer;\n    this.formBuilder = formBuilder;\n    this.isShowPopupDetailSim = false;\n    this.isShowPopupDetailDevice = false;\n    this.detailSim = {};\n    this.detailStatusSim = {};\n    this.detailCustomer = {};\n    this.detailRatingPlan = {};\n    this.detailContract = {};\n    this.detailAPN = {};\n    this.maxDateFrom = null;\n    this.minDateTo = null;\n    this.maxDateTo = null;\n    this.isShowDialogImportByFile = false;\n    this.isShowErrorUpload = false;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.devicemgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listdevice\")\n    }];\n    this.searchInfo = {\n      imei: null,\n      imsi: null,\n      msisdn: null,\n      // location: null,\n      deviceType: null,\n      country: null,\n      contractDateFrom: null,\n      contractDateTo: null\n    };\n    this.detailSim = {};\n    this.deviceInfo = {\n      imei: null,\n      location: null,\n      msisdn: null,\n      country: null,\n      category: null,\n      expiredDate: null,\n      deviceType: null,\n      iotLink: null\n    };\n    this.detailStatusSim = {\n      statusData: null,\n      statusReceiveCall: null,\n      statusSendCall: null,\n      statusWorldCall: null,\n      statusReceiveSms: null,\n      statusSendSms: null\n    };\n    const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;\n    this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n    this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n    this.formSearch = this.formBuilder.group(this.searchInfo);\n    this.columns = [{\n      name: this.tranService.translate(\"device.label.imei\"),\n      key: \"imei\",\n      size: \"250px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"device.label.deviceType\"),\n      key: \"deviceType\",\n      size: \"250px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"device.label.subcriber\"),\n      key: \"msisdn\",\n      size: \"200px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcClick(id, item) {\n        me.isShowPopupDetailSim = true;\n        me.simId = id.toString();\n        me.simService.getById(me.simId, response => {\n          me.detailSim = {\n            ...response\n          };\n          me.simService.getDetailStatus(me.simId, response => {\n            me.detailStatusSim = {\n              statusData: response.gprsStatus == 1,\n              statusReceiveCall: response.icStatus == 1,\n              statusSendCall: response.ocStatus == 1,\n              statusWorldCall: response.iddStatus == 1,\n              statusReceiveSms: response.smtStatus == 1,\n              statusSendSms: response.smoStatus == 1\n            };\n          }, () => {});\n          me.detailCustomer = {\n            name: me.detailSim.customerName,\n            code: me.detailSim.customerCode\n          };\n          me.detailCustomer = {\n            name: me.detailSim.customerName,\n            code: me.detailSim.customerCode\n          };\n          me.simService.getDetailPlanSim(me.simId, response => {\n            me.detailRatingPlan = {\n              ...response\n            };\n          }, () => {});\n          me.simService.getDetailContract(me.utilService.stringToStrBase64(me.detailSim.contractCode), response => {\n            me.detailContract = response;\n          }, () => {});\n          me.detailAPN = {\n            code: me.detailSim.apnCode,\n            type: \"Kết nối bằng 3G\",\n            ip: 0,\n            rangeIp: me.detailSim.ip\n          };\n          me.simService.getConnectionStatus([me.simId], resp => {\n            me.detailSim.connectionStatus = resp[0].userstate;\n          }, () => {});\n        }, null, () => {\n          this.messageCommonService.offload();\n        });\n      }\n    }, {\n      name: this.tranService.translate(\"device.label.country\"),\n      key: \"country\",\n      size: \"175px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"device.label.expireDate\"),\n      key: \"expiredDate\",\n      size: \"175px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      funcConvertText(value) {\n        if (value == null) return \"\";\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }];\n    this.selectItems = [];\n    this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-eye\",\n        tooltip: this.tranService.translate(\"global.button.view\"),\n        func: function (id, item) {\n          me.msisdn = id;\n          me.messageCommonService.onload();\n          me.deviceService.detailDevice(Number(me.msisdn), response => {\n            me.deviceInfo = {\n              ...response\n            };\n            if (response.expiredDate != null && response.expiredDate != \"\") {\n              me.deviceInfo.expiredDate = new Date(response.expiredDate);\n              me.minDateTo = me.deviceInfo.expiredDate;\n              me.maxDateTo = me.deviceInfo.expiredDate;\n            } else {\n              me.deviceInfo.expiredDate = null;\n            }\n            me.initForm();\n          }, null, () => {\n            me.messageCommonService.offload();\n          });\n          me.deviceService.getLocation(Number(me.msisdn), response => {\n            if (response != null) {\n              me.findCellId(response.mediaDtoResp.cell_lac);\n            } else {\n              me.deviceInfo.location = \" \";\n              const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;\n              me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);\n            }\n          });\n          me.isShowPopupDetailDevice = true;\n        },\n        funcAppear: function (id, item) {\n          return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]);\n        }\n      }, {\n        icon: \"pi pi-pencil\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.router.navigate([`/devices/edit/${item.msisdn}`]);\n        },\n        funcAppear: function (id, item) {\n          return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE]);\n        }\n      }, {\n        icon: \"pi pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmDeleteDevice\"), me.tranService.translate(\"global.message.confirmDeleteDevice\"), {\n            ok: () => {\n              me.messageCommonService.onload();\n              me.deviceService.deleleDevice(item.msisdn, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              }, null, () => {\n                me.messageCommonService.offload();\n              });\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.DELETE]);\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"imei,asc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n    this.optionInputFile = {\n      type: ['xls', 'xlsx'],\n      messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\n      maxSize: 10,\n      unit: \"MB\",\n      required: true,\n      isShowButtonUpload: true,\n      actionUpload: this.uploadFile.bind(this),\n      disabled: false\n    };\n  }\n  onSubmitSearch() {\n    let me = this;\n    me.pageNumber = 0;\n    me.search(0, this.pageSize, this.sort, this.searchInfo);\n  }\n  updateParams(dataParams) {\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        if (key == \"contractDateFrom\") {\n          dataParams[\"contractDateFrom\"] = this.searchInfo.contractDateFrom.getTime();\n        } else if (key == \"contractDateTo\") {\n          dataParams[\"contractDateTo\"] = this.searchInfo.contractDateTo.getTime();\n        } else {\n          dataParams[key] = this.searchInfo[key];\n        }\n      }\n    });\n  }\n  search(page, limit, sort, params) {\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let me = this;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    this.updateParams(dataParams);\n    me.messageCommonService.onload();\n    this.deviceService.search(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  onChangeDateFrom(value) {\n    if (value) {\n      this.minDateTo = value;\n    } else {\n      this.minDateTo = null;\n    }\n  }\n  onChangeDateTo(value) {\n    if (value) {\n      this.maxDateFrom = value;\n    } else {\n      this.maxDateFrom = null;\n    }\n  }\n  navigateToCreateDevice() {\n    this.router.navigate(['/devices/create']);\n  }\n  clearFileCallback() {\n    this.isShowErrorUpload = false;\n  }\n  uploadFile(objectFile) {\n    let me = this;\n    me.messageCommonService.onload();\n    this.deviceService.uploadRegisterByFile(objectFile, response => {\n      me.messageCommonService.offload();\n      console.log(response);\n      me.excuteResponseImportFile(response);\n    });\n  }\n  excuteResponseImportFile(response) {\n    let me = this;\n    me.simImportsOrigin = undefined;\n    me.isShowErrorUpload = false;\n    this.optionInputFile.disabled = true;\n    if (response.errorCode == CONSTANTS.DEVICE.SUCCESS) {\n      me.messageCommonService.success(me.tranService.translate(\"device.text.messageSuccess\"));\n      me.isShowDialogImportByFile = false;\n      me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n      return;\n    } else if (response.errorCode == CONSTANTS.DEVICE.WRONG_FOMAT) {\n      me.messageCommonService.error(me.tranService.translate(\"device.text.wrongFormat\"));\n      me.isShowDialogImportByFile = false;\n      return;\n    } else if (response.errorCode == CONSTANTS.DEVICE.FILE_TO_BIG) {\n      me.messageCommonService.error(me.tranService.translate(\"device.text.tooBig\"));\n      me.isShowDialogImportByFile = false;\n      return;\n    } else if (response.errorCode == CONSTANTS.DEVICE.COLUMN_INVALID) {\n      me.messageCommonService.error(me.tranService.translate(\"device.text.columnInvalid\"));\n      me.isShowDialogImportByFile = false;\n      return;\n    }\n    if (response.errorCode == CONSTANTS.DEVICE.MAX_ROW_FILE_IMPORT) {\n      me.messageCommonService.error(me.tranService.translate(\"device.text.maxRowImport\"));\n      me.isShowDialogImportByFile = false;\n      return;\n    } else {\n      let data = [];\n      response.errorList.forEach(item => {\n        data.push({\n          imei: item.imei,\n          msisdn: item.msisdn,\n          // location: item.location,\n          deviceType: item.deviceType,\n          expiredDate: item.expiredDate,\n          country: item.country,\n          error: this.getErrorContent(item.errorCode)\n        });\n      });\n      if (data.length === 0) {\n        me.messageCommonService.success(me.tranService.translate(\"device.text.messageSuccess\"));\n        me.isShowDialogImportByFile = false;\n        return;\n      }\n      me.isShowDialogImportByFile = false;\n      const header = [me.tranService.translate(\"device.label.imei\"), me.tranService.translate(\"device.label.msisdn\"),\n      // me.tranService.translate(\"device.label.location\"),\n      me.tranService.translate(\"device.label.deviceType\"), me.tranService.translate(\"device.label.expireDate\"), me.tranService.translate(\"device.label.country\"), me.tranService.translate(\"device.label.note\")];\n      const ws = XLSX.utils.json_to_sheet(data);\n      XLSX.utils.sheet_add_aoa(ws, [header], {\n        origin: 'A1'\n      });\n      const columnWidths = {\n        A: {\n          wch: 20\n        },\n        B: {\n          wch: 20\n        },\n        C: {\n          wch: 15\n        },\n        D: {\n          wch: 15\n        },\n        E: {\n          wch: 15\n        },\n        F: {\n          wch: 15\n        },\n        G: {\n          wch: 50\n        }\n      };\n      ws['!cols'] = Object.keys(columnWidths).map(col => ({\n        ...{\n          width: columnWidths[col].wch\n        },\n        ...columnWidths[col]\n      }));\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');\n      XLSX.writeFile(wb, 'error.xlsx');\n      me.messageCommonService.warning(me.tranService.translate(\"device.text.textResultImportByFile\"));\n    }\n  }\n  downloadTemplate() {\n    this.deviceService.downloadTemplate();\n  }\n  importByFile() {\n    let me = this;\n    me.isShowDialogImportByFile = true;\n    me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});\n    me.simImportsOrigin = undefined;\n    me.isShowErrorUpload = false;\n  }\n  getErrorContent(code) {\n    let me = this;\n    if (code == CONSTANTS.DEVICE.MSISDN_IS_EMPTY) {\n      return me.tranService.translate('device.text.msisdnEmpty');\n    } else if (code == CONSTANTS.DEVICE.MSISDN_NOTEXITS) {\n      return me.tranService.translate('device.text.msisdnNotExists');\n    } else if (code == CONSTANTS.DEVICE.MSISDN_ASSIGN) {\n      return me.tranService.translate('device.text.msisdnAssign');\n    } else if (code == CONSTANTS.DEVICE.MSISDN_INVALD) {\n      return me.tranService.translate('device.text.msisdnInvalid');\n    } else if (code == CONSTANTS.DEVICE.MSISDN_IS_EMPTY) {\n      return me.tranService.translate('device.text.msisdnIsEmptly');\n    } else if (code == CONSTANTS.DEVICE.MSISDN_IS_DUPLICATE) {\n      return me.tranService.translate('device.text.msisdnIsDuplicate');\n    } else if (code == CONSTANTS.DEVICE.EXPRIRED_DATE_INVALID) {\n      return me.tranService.translate('device.text.expriredDateInvalid');\n    } else if (code == CONSTANTS.DEVICE.MSISDN_NOT_PERMISSION) {\n      return me.tranService.translate('device.text.msisdnNotPermission');\n    } else if (code == CONSTANTS.DEVICE.IMEI_IS_EXSIT) {\n      return me.tranService.translate('device.text.imeiIsExist');\n    } else if (code == CONSTANTS.DEVICE.IMEI_IS_DUPLITE) {\n      return me.tranService.translate('device.text.imeiIsDuplicate');\n    } else if (code == CONSTANTS.DEVICE.IMEI_LEN) {\n      return me.tranService.translate('device.text.imeiLen');\n    } else if (code == CONSTANTS.DEVICE.DEVICE_TYPE_LEN) {\n      return me.tranService.translate('device.text.deviceTypeLen');\n    } else if (code == CONSTANTS.DEVICE.COUNTRY_LEN) {\n      return me.tranService.translate('device.text.countryLen');\n    }\n    return \"\";\n  }\n  convertDateString(dateString) {\n    // Create a new Date object from the string\n    const date = new Date(dateString);\n    // Extract day, month, and year\n    const day = date.getDate();\n    const month = date.getMonth() + 1; // Months are zero-based\n    const year = date.getFullYear();\n    // Format day and month to two digits\n    const formattedDay = day < 10 ? `0${day}` : `${day}`;\n    const formattedMonth = month < 10 ? `0${month}` : `${month}`;\n    // Return the formatted date string\n    return `${formattedDay}/${formattedMonth}/${year}`;\n  }\n  findCellId(cell_lac) {\n    let me = this;\n    this.deviceService.findCellId({\n      loc: cell_lac.split(\":\")[1],\n      cell: cell_lac.split(\":\")[0]\n    }, response => {\n      me.findCellIDDto = {\n        lat: response.lat,\n        lng: response.lng\n      };\n      me.findAddress(response.lat, response.lng);\n    }, null, null);\n  }\n  findAddress(lat, lon) {\n    let me = this;\n    me.deviceService.findAddress(lat, lon, response => {\n      me.deviceInfo.location = response.display_name;\n      const url = `https://www.google.com/maps?q=${me.findCellIDDto.lat},${me.findCellIDDto.lng}&output=embed`;\n      me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);\n    }, null, null);\n  }\n  initForm() {\n    // debugger\n    let me = this;\n    me.formDetailDevice = me.formBuilder.group({\n      imei: [me.deviceInfo.imei],\n      location: [me.deviceInfo.location],\n      msisdn: [me.deviceInfo.msisdn],\n      country: [me.deviceInfo.country],\n      expiredDate: [me.utilService.convertDateToString(new Date(me.deviceInfo.expiredDate))],\n      deviceType: [me.deviceInfo.deviceType],\n      iotLink: [me.deviceInfo.iotLink]\n    });\n    if (me.deviceInfo.iotLink === 1) {\n      me.formDetailDevice.controls[\"iotLink\"].setValue(true);\n    }\n  }\n  goUpdate() {\n    this.router.navigate([`/devices/edit/${this.msisdn}`]);\n  }\n  getNameStatus(value) {\n    if (value == 0) {\n      return this.tranService.translate(\"sim.status.inventory\");\n    } else if (value == CONSTANTS.SIM_STATUS.READY) {\n      // return this.tranService.translate(\"sim.status.ready\");\n      return this.tranService.translate(\"sim.status.activated\");\n    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n      return this.tranService.translate(\"sim.status.activated\");\n    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n      return this.tranService.translate(\"sim.status.deactivated\");\n    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n      return this.tranService.translate(\"sim.status.purged\");\n    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n      return this.tranService.translate(\"sim.status.inactivated\");\n    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.processingChangePlan\");\n    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n    }\n    return \"\";\n  }\n  getClassStatus(value) {\n    if (value == 0) {\n      return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.READY) {\n      // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n      return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n      return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n      return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n      return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n      return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n    }\n    return [];\n  }\n  getServiceType(value) {\n    if (value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate(\"sim.serviceType.prepaid\");else if (value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate(\"sim.serviceType.postpaid\");else return \"\";\n  }\n  static {\n    this.ɵfac = function DeviceListComponent_Factory(t) {\n      return new (t || DeviceListComponent)(i0.ɵɵdirectiveInject(DeviceService), i0.ɵɵdirectiveInject(SimService), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeviceListComponent,\n      selectors: [[\"app-device-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 206,\n      vars: 150,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info mr-2\", 3, \"label\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"p-button-success\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"grid-4\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"imei\", \"formControlName\", \"imei\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"imei\"], [\"pInputText\", \"\", \"id\", \"msisdn\", \"formControlName\", \"msisdn\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"msisdn\"], [\"pInputText\", \"\", \"id\", \"country\", \"formControlName\", \"country\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"country\"], [\"pInputText\", \"\", \"id\", \"deviceType\", \"formControlName\", \"deviceType\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"deviceType\"], [1, \"col-3\", \"pb-0\"], [1, \"p-float-label\", \"date-filter-1\"], [\"styleClass\", \"w-full\", \"id\", \"contractDateFrom\", \"formControlName\", \"contractDateFrom\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"contractDateFrom\"], [\"styleClass\", \"w-full\", \"id\", \"contractDateTo\", \"formControlName\", \"contractDateTo\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"minDate\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"contractDateTo\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\"], [1, \"col-10\", \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\"], [1, \"w-full\", 3, \"fileObject\", \"clearFileCallback\", \"options\", \"fileObjectChange\"], [1, \"col-2\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"icon\", \"pi pi-download\", \"styleClass\", \"p-button-outlined p-button-secondary\", 3, \"pTooltip\", \"click\"], [1, \"grid\"], [1, \"col\", \"pt-0\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [1, \"grid\", \"grid-1\", \"mt-1\", \"h-auto\", 2, \"width\", \"calc(100% + 16px)\"], [1, \"col\", \"sim-detail\", \"pr-0\"], [3, \"header\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"custom-card\"], [1, \"w-6\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"150px\", \"max-width\", \"200px\"], [1, \"col\"], [1, \"mt-1\", \"grid\"], [1, \"w-auto\", \"ml-3\"], [\"class\", \"ml-3 p-2 text-green-800 bg-green-100 border-round inline-block\", 4, \"ngIf\"], [\"class\", \"ml-3 p-2 text-50 surface-500 border-round inline-block\", 4, \"ngIf\"], [\"styleClass\", \"mt-3 sim-status\", 3, \"header\"], [1, \"col-4\", \"text-center\"], [\"onLabel\", \"ON\", \"offLabel\", \"OFF\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"styleClass\", \"mt-3\", 3, \"header\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\"], [1, \"grid\", \"mt-0\"], [1, \"col\", \"uppercase\"], [\"styleClass\", \"mt-3\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [\"styleClass\", \"p-button-info mr-2\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-success\", 3, \"label\", \"click\"], [1, \"text-red-500\"], [1, \"ml-3\", \"p-2\", \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"], [1, \"ml-3\", \"p-2\", \"text-50\", \"surface-500\", \"border-round\", \"inline-block\"], [3, \"formGroup\"], [1, \"grid\", \"mx-4\", \"my-3\"], [\"pInputText\", \"\", \"id\", \"imei\", \"formControlName\", \"imei\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]{2,255}$\", \"readonly\", \"\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"deviceType\", \"formControlName\", \"deviceType\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]{2,255}$\", \"autofocus\", \"\", 1, \"w-full\", 3, \"ngModel\", \"readonly\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"msisdn\", \"formControlName\", \"msisdn\", \"readonly\", \"\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"country\", \"formControlName\", \"country\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]{2,255}$\", 1, \"w-full\", 3, \"ngModel\", \"readonly\", \"disabled\", \"ngModelChange\"], [\"disabled\", \"true\", 1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"id\", \"expiredDate\", \"formControlName\", \"expiredDate\", \"disabledDays\", \"1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31\", 3, \"ngModel\", \"disabled\", \"readonlyInput\", \"minDate\", \"maxDate\", \"ngModelChange\"], [\"htmlFor\", \"expiredDate\"], [\"pInputText\", \"\", \"id\", \"location\", \"formControlName\", \"location\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]{2,255}$\", \"readonly\", \"\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"location\"], [1, \"flex\", \"align-items-center\"], [\"formControlName\", \"iotLink\", \"id\", \"iotLink\", \"inputId\", \"iotLink\", 3, \"disabled\", \"readonly\", \"binary\", \"ngStyle\"], [\"for\", \"iotLink\"], [1, \"col-offset-2\", \"col-8\"], [\"allowfullscreen\", \"true\", \"loading\", \"lazy\", \"height\", \"500\", \"referrerpolicy\", \"no-referrer-when-downgrade\", 1, \"w-full\", 2, \"border\", \"0\", 3, \"src\"]],\n      template: function DeviceListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, DeviceListComponent_p_button_6_Template, 1, 1, \"p-button\", 5);\n          i0.ɵɵtemplate(7, DeviceListComponent_p_button_7_Template, 1, 1, \"p-button\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function DeviceListComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(9, \"p-panel\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"span\", 11)(13, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.imei = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.msisdn = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"span\", 11)(23, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.searchInfo.country = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"span\", 11)(28, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_input_ngModelChange_28_listener($event) {\n            return ctx.searchInfo.deviceType = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"label\", 19);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 20)(32, \"span\", 21)(33, \"p-calendar\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_calendar_ngModelChange_33_listener($event) {\n            return ctx.searchInfo.contractDateFrom = $event;\n          })(\"onSelect\", function DeviceListComponent_Template_p_calendar_onSelect_33_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.contractDateFrom);\n          })(\"onInput\", function DeviceListComponent_Template_p_calendar_onInput_33_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.contractDateFrom);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"label\", 23);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 20)(37, \"span\", 21)(38, \"p-calendar\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_calendar_ngModelChange_38_listener($event) {\n            return ctx.searchInfo.contractDateTo = $event;\n          })(\"onSelect\", function DeviceListComponent_Template_p_calendar_onSelect_38_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.contractDateTo);\n          })(\"onInput\", function DeviceListComponent_Template_p_calendar_onInput_38_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.contractDateTo);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"label\", 25);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 20);\n          i0.ɵɵelement(42, \"p-button\", 26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 27)(44, \"p-dialog\", 28);\n          i0.ɵɵlistener(\"visibleChange\", function DeviceListComponent_Template_p_dialog_visibleChange_44_listener($event) {\n            return ctx.isShowDialogImportByFile = $event;\n          });\n          i0.ɵɵelementStart(45, \"div\", 29)(46, \"div\", 30)(47, \"input-file-vnpt\", 31);\n          i0.ɵɵlistener(\"fileObjectChange\", function DeviceListComponent_Template_input_file_vnpt_fileObjectChange_47_listener($event) {\n            return ctx.fileObject = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 32)(49, \"p-button\", 33);\n          i0.ɵɵlistener(\"click\", function DeviceListComponent_Template_p_button_click_49_listener() {\n            return ctx.downloadTemplate();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 34)(51, \"div\", 35);\n          i0.ɵɵtemplate(52, DeviceListComponent_small_52_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(53, \"div\", 37)(54, \"p-dialog\", 28);\n          i0.ɵɵlistener(\"visibleChange\", function DeviceListComponent_Template_p_dialog_visibleChange_54_listener($event) {\n            return ctx.isShowPopupDetailSim = $event;\n          });\n          i0.ɵɵelementStart(55, \"div\", 38)(56, \"div\", 39)(57, \"p-card\", 40)(58, \"div\", 41)(59, \"div\", 42)(60, \"div\", 34)(61, \"span\", 43);\n          i0.ɵɵtext(62);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 44);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 45)(66, \"span\", 43);\n          i0.ɵɵtext(67);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\", 46);\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 45)(71, \"span\", 43);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\", 44);\n          i0.ɵɵtext(74);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 45)(76, \"span\", 43);\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\", 44);\n          i0.ɵɵtext(79);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 45)(81, \"span\", 43);\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"span\", 44);\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 45)(86, \"span\", 43);\n          i0.ɵɵtext(87);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(88, DeviceListComponent_span_88_Template, 2, 0, \"span\", 47);\n          i0.ɵɵtemplate(89, DeviceListComponent_span_89_Template, 2, 0, \"span\", 48);\n          i0.ɵɵtemplate(90, DeviceListComponent_span_90_Template, 2, 0, \"span\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 42)(92, \"div\", 34)(93, \"span\", 43);\n          i0.ɵɵtext(94);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"span\", 44);\n          i0.ɵɵtext(96);\n          i0.ɵɵpipe(97, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 45)(99, \"span\", 43);\n          i0.ɵɵtext(100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"span\", 46);\n          i0.ɵɵtext(102);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(103, \"p-card\", 49)(104, \"div\", 34)(105, \"div\", 50)(106, \"p-toggleButton\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_toggleButton_ngModelChange_106_listener($event) {\n            return ctx.detailStatusSim.statusData = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"div\");\n          i0.ɵɵtext(108);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 50)(110, \"p-toggleButton\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_toggleButton_ngModelChange_110_listener($event) {\n            return ctx.detailStatusSim.statusReceiveCall = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"div\");\n          i0.ɵɵtext(112);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 50)(114, \"p-toggleButton\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_toggleButton_ngModelChange_114_listener($event) {\n            return ctx.detailStatusSim.statusSendCall = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\");\n          i0.ɵɵtext(116);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(117, \"div\", 34)(118, \"div\", 50)(119, \"p-toggleButton\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_toggleButton_ngModelChange_119_listener($event) {\n            return ctx.detailStatusSim.statusWorldCall = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"div\");\n          i0.ɵɵtext(121);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(122, \"div\", 50)(123, \"p-toggleButton\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_toggleButton_ngModelChange_123_listener($event) {\n            return ctx.detailStatusSim.statusReceiveSms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"div\");\n          i0.ɵɵtext(125);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"div\", 50)(127, \"p-toggleButton\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function DeviceListComponent_Template_p_toggleButton_ngModelChange_127_listener($event) {\n            return ctx.detailStatusSim.statusSendSms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"div\");\n          i0.ɵɵtext(129);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(130, \"p-card\", 52)(131, \"div\", 34)(132, \"span\", 53);\n          i0.ɵɵtext(133);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"span\", 44);\n          i0.ɵɵtext(135);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(136, \"div\", 45)(137, \"span\", 53);\n          i0.ɵɵtext(138);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"span\", 44);\n          i0.ɵɵtext(140);\n          i0.ɵɵpipe(141, \"number\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(142, \"div\", 39)(143, \"p-card\", 40)(144, \"div\", 54)(145, \"span\", 53);\n          i0.ɵɵtext(146);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"span\", 44);\n          i0.ɵɵtext(148);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(149, \"div\", 45)(150, \"span\", 53);\n          i0.ɵɵtext(151);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"span\", 44);\n          i0.ɵɵtext(153);\n          i0.ɵɵpipe(154, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(155, \"div\", 45)(156, \"span\", 53);\n          i0.ɵɵtext(157);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"span\", 55);\n          i0.ɵɵtext(159);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(160, \"div\", 45)(161, \"span\", 53);\n          i0.ɵɵtext(162);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(163, \"span\", 44);\n          i0.ɵɵtext(164);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(165, \"div\", 45)(166, \"span\", 53);\n          i0.ɵɵtext(167);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(168, \"span\", 44);\n          i0.ɵɵtext(169);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(170, \"div\", 45)(171, \"span\", 53);\n          i0.ɵɵtext(172);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(173, \"span\", 44);\n          i0.ɵɵtext(174);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(175, \"div\", 45)(176, \"span\", 53);\n          i0.ɵɵtext(177);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(178, \"span\", 55);\n          i0.ɵɵtext(179);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(180, \"div\", 45)(181, \"span\", 53);\n          i0.ɵɵtext(182);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(183, \"span\", 44);\n          i0.ɵɵtext(184);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(185, \"div\", 45)(186, \"span\", 53);\n          i0.ɵɵtext(187);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(188, \"span\", 44);\n          i0.ɵɵtext(189);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(190, \"p-card\", 52)(191, \"div\", 34)(192, \"span\", 53);\n          i0.ɵɵtext(193);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(194, \"span\", 44);\n          i0.ɵɵtext(195);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"div\", 45)(197, \"span\", 53);\n          i0.ɵɵtext(198);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(199, \"span\", 44);\n          i0.ɵɵtext(200);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(201, \"div\", 37)(202, \"p-dialog\", 28);\n          i0.ɵɵlistener(\"visibleChange\", function DeviceListComponent_Template_p_dialog_visibleChange_202_listener($event) {\n            return ctx.isShowPopupDetailDevice = $event;\n          });\n          i0.ɵɵelementStart(203, \"p-card\", 56);\n          i0.ɵɵtemplate(204, DeviceListComponent_form_204_Template, 39, 27, \"form\", 57);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(205, \"table-vnpt\", 58);\n          i0.ɵɵlistener(\"selectItemsChange\", function DeviceListComponent_Template_table_vnpt_selectItemsChange_205_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listdevice\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(143, _c1, ctx.CONSTANTS.PERMISSIONS.DEVICE.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(145, _c1, ctx.CONSTANTS.PERMISSIONS.DEVICE.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearch);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.imei);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"device.label.imei\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.msisdn);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"device.label.subcriber\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.country);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"device.label.country\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.deviceType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"device.label.deviceType\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contractDateFrom)(\"showIcon\", true)(\"showClear\", true)(\"maxDate\", ctx.maxDateFrom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"device.label.expireFrom\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contractDateTo)(\"showIcon\", true)(\"showClear\", true)(\"minDate\", ctx.minDateTo)(\"maxDate\", ctx.maxDateTo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"device.label.expireTo\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(147, _c2));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"device.label.importByFile\"))(\"visible\", ctx.isShowDialogImportByFile)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fileObject\", ctx.fileObject)(\"clearFileCallback\", ctx.clearFileCallback.bind(ctx))(\"options\", ctx.optionInputFile);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"pTooltip\", ctx.tranService.translate(\"global.button.downloadTemp\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowErrorUpload);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(148, _c3));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.text.detailSim\"))(\"visible\", ctx.isShowPopupDetailSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.text.simInfo\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.sothuebao\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailSim.msisdn);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.trangthaisim\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.getClassStatus(ctx.detailSim.status));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.getNameStatus(ctx.detailSim.status));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.imsi\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailSim.imsi);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.imeiDevice\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailSim.imei);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.maapn\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailSim.apnId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.trangthaiketnoi\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.detailSim.connectionStatus !== undefined && ctx.detailSim.connectionStatus !== null && ctx.detailSim.connectionStatus !== \"0\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.detailSim.connectionStatus === \"0\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.detailSim.connectionStatus === undefined || ctx.detailSim.connectionStatus === null);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.startDate\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(97, 135, ctx.detailSim.startDate, \"dd/MM/yyyy\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.serviceType\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getServiceType(ctx.detailSim.serviceType));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.text.simStatusInfo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.detailStatusSim.statusData)(\"disabled\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.status.service.data\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.detailStatusSim.statusReceiveCall)(\"disabled\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.status.service.callReceived\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.detailStatusSim.statusSendCall)(\"disabled\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.status.service.callSent\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.detailStatusSim.statusWorldCall)(\"disabled\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.status.service.callWorld\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.detailStatusSim.statusReceiveSms)(\"disabled\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.status.service.smsReceived\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.detailStatusSim.statusSendSms)(\"disabled\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.status.service.smsSent\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.text.ratingPlanInfo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.tengoicuoc\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailSim.ratingPlanName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.dataUseInMonth\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(141, 138, ctx.utilService.bytesToMegabytes(ctx.detailRatingPlan.dataUseInMonth)), \" \", ctx.detailRatingPlan.unit ? ctx.detailRatingPlan.unit : \"MB\", \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.text.contractInfo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.mahopdong\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.contractCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.ngaylamhopdong\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(154, 140, ctx.detailContract.contractDate, \"dd/MM/yyyy\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.nguoilamhopdong\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.contractorInfo);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.matrungtam\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.centerCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.dienthoailienhe\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.contactPhone);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.diachilienhe\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.contactAddress);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.paymentName\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.paymentName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.paymentAddress\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.paymentAddress);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.routeCode\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailContract.routeCode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"sim.text.customerInfo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.khachhang\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailCustomer.name);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.customerCode\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.detailCustomer.code);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(149, _c3));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.menu.devicedetail\"))(\"visible\", ctx.isShowPopupDetailDevice)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowPopupDetailDevice);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"fieldId\", \"msisdn\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.listdevice\"));\n        }\n      },\n      dependencies: [i3.NgIf, i3.NgStyle, i4.Breadcrumb, i5.Tooltip, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.PatternValidator, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i6.InputText, i7.Button, i8.TableVnptComponent, i9.InputFileVnptComponent, i10.Calendar, i11.Dialog, i12.Card, i13.Panel, i14.Checkbox, i15.ToggleButton, i3.DecimalPipe, i3.DatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["DeviceService", "CONSTANTS", "ComponentBase", "XLSX", "SimService", "i0", "ɵɵelementStart", "ɵɵlistener", "DeviceListComponent_p_button_6_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "navigateToCreateDevice", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "tranService", "translate", "DeviceListComponent_p_button_7_Template_p_button_click_0_listener", "_r10", "ctx_r9", "importByFile", "ctx_r1", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r2", "messageErrorUpload", "DeviceListComponent_form_204_Template_input_ngModelChange_4_listener", "$event", "_r12", "ctx_r11", "deviceInfo", "imei", "DeviceListComponent_form_204_Template_input_ngModelChange_9_listener", "ctx_r13", "deviceType", "DeviceListComponent_form_204_Template_input_ngModelChange_14_listener", "ctx_r14", "msisdn", "DeviceListComponent_form_204_Template_input_ngModelChange_19_listener", "ctx_r15", "country", "DeviceListComponent_form_204_Template_p_calendar_ngModelChange_24_listener", "ctx_r16", "expiredDate", "DeviceListComponent_form_204_Template_input_ngModelChange_29_listener", "ctx_r17", "location", "ɵɵelement", "ctx_r6", "formDetailDevice", "minDateTo", "maxDateTo", "ɵɵpureFunction0", "_c0", "safeUrl", "ɵɵsanitizeResourceUrl", "DeviceListComponent", "constructor", "deviceService", "simService", "sanitizer", "formBuilder", "injector", "isShowPopupDetailSim", "isShowPopupDetailDevice", "detailSim", "detailStatusSim", "detailCustomer", "detailRatingPlan", "detailContract", "detailAPN", "maxDateFrom", "isShowDialogImportByFile", "isShowErrorUpload", "ngOnInit", "me", "home", "icon", "routerLink", "items", "label", "searchInfo", "imsi", "contractDateFrom", "contractDateTo", "category", "iotLink", "statusData", "statusReceiveCall", "statusSendCall", "statusWorldCall", "statusReceiveSms", "statusSendSms", "url", "bypassSecurityTrustResourceUrl", "formSearch", "group", "columns", "name", "key", "size", "align", "isShow", "isSort", "style", "cursor", "color", "funcClick", "id", "item", "simId", "toString", "getById", "response", "getDetailStatus", "gprsStatus", "icStatus", "ocStatus", "iddStatus", "smtStatus", "smoStatus", "customerName", "code", "customerCode", "getDetailPlanSim", "getDetailContract", "utilService", "stringToStrBase64", "contractCode", "apnCode", "type", "ip", "rangeIp", "getConnectionStatus", "resp", "connectionStatus", "userstate", "messageCommonService", "offload", "funcConvertText", "value", "convertDateToString", "Date", "selectItems", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "tooltip", "func", "onload", "detailDevice", "Number", "initForm", "getLocation", "findCellId", "mediaDtoResp", "cell_lac", "funcAppear", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "DEVICE", "VIEW_DETAIL", "router", "navigate", "UPDATE", "confirm", "ok", "deleleDevice", "success", "search", "pageNumber", "pageSize", "sort", "cancel", "DELETE", "dataSet", "content", "total", "optionInputFile", "messageErrorType", "maxSize", "unit", "required", "isShowButtonUpload", "actionUpload", "uploadFile", "bind", "disabled", "onSubmitSearch", "updateParams", "dataParams", "Object", "keys", "for<PERSON>ach", "getTime", "page", "limit", "params", "totalElements", "onChangeDateFrom", "onChangeDateTo", "clearFileCallback", "objectFile", "uploadRegisterByFile", "console", "log", "excuteResponseImportFile", "simImportsOrigin", "undefined", "errorCode", "SUCCESS", "WRONG_FOMAT", "error", "FILE_TO_BIG", "COLUMN_INVALID", "MAX_ROW_FILE_IMPORT", "data", "errorList", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "header", "ws", "utils", "json_to_sheet", "sheet_add_aoa", "origin", "columnWidths", "A", "wch", "B", "C", "D", "E", "F", "G", "map", "col", "width", "wb", "book_new", "book_append_sheet", "writeFile", "warning", "downloadTemplate", "observableService", "next", "OBSERVABLE", "KEY_INPUT_FILE_VNPT", "MSISDN_IS_EMPTY", "MSISDN_NOTEXITS", "MSISDN_ASSIGN", "MSISDN_INVALD", "MSISDN_IS_DUPLICATE", "EXPRIRED_DATE_INVALID", "MSISDN_NOT_PERMISSION", "IMEI_IS_EXSIT", "IMEI_IS_DUPLITE", "IMEI_LEN", "DEVICE_TYPE_LEN", "COUNTRY_LEN", "convertDateString", "dateString", "date", "day", "getDate", "month", "getMonth", "year", "getFullYear", "formattedDay", "formattedMonth", "loc", "split", "cell", "findCellIDDto", "lat", "lng", "<PERSON><PERSON><PERSON><PERSON>", "lon", "display_name", "controls", "setValue", "goUpdate", "getNameStatus", "SIM_STATUS", "READY", "ACTIVATED", "DEACTIVATED", "PURGED", "INACTIVED", "getClassStatus", "getServiceType", "SERVICE_TYPE", "PREPAID", "POSTPAID", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "i2", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "DeviceListComponent_Template", "rf", "ctx", "ɵɵtemplate", "DeviceListComponent_p_button_6_Template", "DeviceListComponent_p_button_7_Template", "DeviceListComponent_Template_form_ngSubmit_8_listener", "DeviceListComponent_Template_input_ngModelChange_13_listener", "DeviceListComponent_Template_input_ngModelChange_18_listener", "DeviceListComponent_Template_input_ngModelChange_23_listener", "DeviceListComponent_Template_input_ngModelChange_28_listener", "DeviceListComponent_Template_p_calendar_ngModelChange_33_listener", "DeviceListComponent_Template_p_calendar_onSelect_33_listener", "DeviceListComponent_Template_p_calendar_onInput_33_listener", "DeviceListComponent_Template_p_calendar_ngModelChange_38_listener", "DeviceListComponent_Template_p_calendar_onSelect_38_listener", "DeviceListComponent_Template_p_calendar_onInput_38_listener", "DeviceListComponent_Template_p_dialog_visibleChange_44_listener", "DeviceListComponent_Template_input_file_vnpt_fileObjectChange_47_listener", "fileObject", "DeviceListComponent_Template_p_button_click_49_listener", "DeviceListComponent_small_52_Template", "DeviceListComponent_Template_p_dialog_visibleChange_54_listener", "DeviceListComponent_span_88_Template", "DeviceListComponent_span_89_Template", "DeviceListComponent_span_90_Template", "DeviceListComponent_Template_p_toggleButton_ngModelChange_106_listener", "DeviceListComponent_Template_p_toggleButton_ngModelChange_110_listener", "DeviceListComponent_Template_p_toggleButton_ngModelChange_114_listener", "DeviceListComponent_Template_p_toggleButton_ngModelChange_119_listener", "DeviceListComponent_Template_p_toggleButton_ngModelChange_123_listener", "DeviceListComponent_Template_p_toggleButton_ngModelChange_127_listener", "DeviceListComponent_Template_p_dialog_visibleChange_202_listener", "DeviceListComponent_form_204_Template", "DeviceListComponent_Template_table_vnpt_selectItemsChange_205_listener", "ɵɵpureFunction1", "_c1", "CREATE", "ɵɵstyleMap", "_c2", "_c3", "ɵɵclassMap", "status", "apnId", "ɵɵpipeBind2", "startDate", "serviceType", "ratingPlanName", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "bytesToMegabytes", "dataUseInMonth", "contractDate", "contractorInfo", "centerCode", "contactPhone", "contactAddress", "paymentName", "paymentAddress", "routeCode"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\device-management\\list\\app.device.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\device-management\\list\\app.device.list.component.html"], "sourcesContent": ["import {Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport {FormBuilder, FormControl, FormGroup} from \"@angular/forms\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {DeviceService} from \"src/app/service/device/DeviceService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {CONSTANTS} from \"../../../service/comon/constants\";\r\nimport {OptionInputFile} from \"../../common-module/input-file/input.file.component\";\r\nimport {ComponentBase} from \"../../../component.base\";\r\nimport * as XLSX from 'xlsx';\r\nimport {DomSanitizer, SafeResourceUrl} from \"@angular/platform-browser\";\r\nimport {SimService} from \"../../../service/sim/SimService\";\r\n\r\n@Component({\r\n    selector: \"app-device-list\",\r\n    templateUrl: \"./app.device.list.component.html\"\r\n})\r\nexport class DeviceListComponent extends ComponentBase implements OnInit{\r\n    constructor(\r\n        @Inject(DeviceService) private deviceService: DeviceService,\r\n        @Inject(SimService) private simService: SimService,\r\n        private sanitizer: DomSanitizer,\r\n        private formBuilder: FormBuilder,\r\n        injector: Injector) {\r\n        super(injector);\r\n    }\r\n\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        imei: string|null,\r\n        msisdn: string|null,//so thue bao\r\n        imsi: string|null,//imsi\r\n        // location:string|null,//vi tri\r\n        country: string|null,//xuat su\r\n        deviceType: string|null,//chung loai\r\n        contractDateFrom: Date|null,\r\n        contractDateTo: Date|null,\r\n    };\r\n    safeUrl: SafeResourceUrl;\r\n    deviceInfo: {\r\n        imei: string | null,\r\n        location: string | null,\r\n        msisdn: number | null,\r\n        country: string | null,\r\n        category: string | null,\r\n        expiredDate: Date | string | null,\r\n        deviceType: string | null,\r\n        iotLink: number | null,\r\n    }\r\n    findCellIDDto: any;\r\n    msisdn: number;\r\n    formDetailDevice: any;\r\n    isShowPopupDetailSim: boolean = false;\r\n    isShowPopupDetailDevice: boolean = false;\r\n    simId: string;\r\n    detailSim:any = {};\r\n    detailStatusSim: any={};\r\n    detailCustomer:any={};\r\n    detailRatingPlan: any={};\r\n    detailContract: any={};\r\n    detailAPN: any={};\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    dataStore: Array<any>;\r\n    selectItems: Array<{id:number,[key:string]:any}>;\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearch: any;\r\n    maxDateFrom: Date|number|string|null = null;\r\n    minDateTo: Date|number|string|null = null;\r\n    maxDateTo: Date|number|string|null = null;\r\n    isShowDialogImportByFile: boolean = false;\r\n    simImportsOrigin: Array<any>;\r\n    isShowErrorUpload: boolean = false;\r\n    fileObject: any;\r\n    optionInputFile: OptionInputFile;\r\n    messageErrorUpload: string| null;\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.devicemgmt\") }, { label: this.tranService.translate(\"global.menu.listdevice\") },];\r\n        this.searchInfo = {\r\n            imei: null,\r\n            imsi: null,\r\n            msisdn: null,\r\n            // location: null,\r\n            deviceType: null,\r\n            country: null,\r\n            contractDateFrom: null,\r\n            contractDateTo: null\r\n        }\r\n        this.detailSim = {};\r\n        this.deviceInfo = {\r\n            imei: null,\r\n            location: null,\r\n            msisdn: null,\r\n            country: null,\r\n            category: null,\r\n            expiredDate: null,\r\n            deviceType: null,\r\n            iotLink: null,\r\n        }\r\n        this.detailStatusSim = {\r\n            statusData: null,\r\n            statusReceiveCall: null,\r\n            statusSendCall: null,\r\n            statusWorldCall: null,\r\n            statusReceiveSms: null,\r\n            statusSendSms: null\r\n        }\r\n        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;\r\n        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\r\n        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\r\n        this.formSearch = this.formBuilder.group(this.searchInfo);\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"device.label.imei\"),\r\n                key: \"imei\",\r\n                size: \"250px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"device.label.deviceType\"),\r\n                key: \"deviceType\",\r\n                size: \"250px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"device.label.subcriber\"),\r\n                key: \"msisdn\",\r\n                size: \"200px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                style:{\r\n                    cursor: \"pointer\",\r\n             color: \"var(--mainColorText)\"\r\n                },\r\n                funcClick(id, item) {\r\n                    me.isShowPopupDetailSim = true;\r\n                    me.simId = id.toString();\r\n                    me.simService.getById(me.simId, (response)=>{\r\n                        me.detailSim = {\r\n                            ...response\r\n                        }\r\n                        me.simService.getDetailStatus(me.simId, (response)=>{\r\n                            me.detailStatusSim =  {\r\n                                statusData: response.gprsStatus == 1,\r\n                                statusReceiveCall: response.icStatus == 1,\r\n                                statusSendCall: response.ocStatus == 1,\r\n                                statusWorldCall: response.iddStatus == 1,\r\n                                statusReceiveSms: response.smtStatus == 1,\r\n                                statusSendSms: response.smoStatus == 1\r\n                            };\r\n                        },()=>{})\r\n                        me.detailCustomer = {\r\n                            name: me.detailSim.customerName,\r\n                            code: me.detailSim.customerCode\r\n                        };\r\n                        me.detailCustomer = {\r\n                            name: me.detailSim.customerName,\r\n                            code: me.detailSim.customerCode\r\n                        };\r\n                        me.simService.getDetailPlanSim(me.simId, (response)=>{\r\n                            me.detailRatingPlan = {\r\n                                ...response\r\n                            }\r\n                        }, ()=>{})\r\n                        me.simService.getDetailContract(me.utilService.stringToStrBase64(me.detailSim.contractCode), (response)=>{\r\n                            me.detailContract = response;\r\n                        }, ()=>{});\r\n                        me.detailAPN = {\r\n                            code: me.detailSim.apnCode,\r\n                            type: \"Kết nối bằng 3G\",\r\n                            ip: 0,\r\n                            rangeIp: me.detailSim.ip\r\n                        };\r\n                        me.simService.getConnectionStatus([me.simId], (resp)=>{\r\n                            me.detailSim.connectionStatus = resp[0].userstate\r\n                        }, ()=>{})\r\n                    }, null,()=>{\r\n                        this.messageCommonService.offload();\r\n                    })\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"device.label.country\"),\r\n                key: \"country\",\r\n                size: \"175px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"device.label.expireDate\"),\r\n                key: \"expiredDate\",\r\n                size: \"175px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                funcConvertText(value){\r\n                    if(value == null) return \"\";\r\n                    return me.utilService.convertDateToString(new Date(value));\r\n                }\r\n            }\r\n        ]\r\n        this.selectItems = [];\r\n        this.optionTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-eye\",\r\n                    tooltip: this.tranService.translate(\"global.button.view\"),\r\n                    func: function(id, item){\r\n                        me.msisdn = id;\r\n                        me.messageCommonService.onload()\r\n                        me.deviceService.detailDevice(Number(me.msisdn), (response) => {\r\n                            me.deviceInfo = {\r\n                                ...response\r\n                            }\r\n                            if (response.expiredDate != null && response.expiredDate != \"\") {\r\n                                me.deviceInfo.expiredDate = new Date(response.expiredDate);\r\n                                me.minDateTo = me.deviceInfo.expiredDate;\r\n                                me.maxDateTo = me.deviceInfo.expiredDate;\r\n                            } else {\r\n                                me.deviceInfo.expiredDate = null;\r\n                            }\r\n                            me.initForm();\r\n                        }, null, () => {\r\n                            me.messageCommonService.offload();\r\n                        })\r\n\r\n                        me.deviceService.getLocation(Number(me.msisdn), (response) => {\r\n                            if (response != null) {\r\n                                me.findCellId(response.mediaDtoResp.cell_lac);\r\n                            } else {\r\n                                me.deviceInfo.location = \" \"\r\n                                const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;\r\n                                me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);\r\n                            }\r\n                        })\r\n                        me.isShowPopupDetailDevice = true;\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-pencil\",\r\n                    tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                    func: function(id, item){\r\n                        me.router.navigate([`/devices/edit/${item.msisdn}`]);\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-trash\",\r\n                    tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                    func: function(id, item){\r\n                        me.messageCommonService.confirm(\r\n                            me.tranService.translate(\"global.message.titleConfirmDeleteDevice\"),\r\n                            me.tranService.translate(\"global.message.confirmDeleteDevice\"),\r\n                            {\r\n                                ok:()=>{\r\n                                    me.messageCommonService.onload();\r\n                                    me.deviceService.deleleDevice(item.msisdn, (response) => {\r\n                                        me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\r\n                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                        }, null, ()=>{\r\n                                        me.messageCommonService.offload();\r\n                                    })\r\n                                },\r\n                                cancel: ()=>{\r\n                                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                                }\r\n                            }\r\n                        )\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.DELETE]);\r\n                    }\r\n                }\r\n            ]\r\n        }\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"imei,asc\";\r\n\r\n        this.dataSet = {\r\n            content: [\r\n            ],\r\n            total: 0\r\n        }\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n        this.optionInputFile = {\r\n            type: ['xls','xlsx'],\r\n            messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\r\n            maxSize: 10,\r\n            unit: \"MB\",\r\n            required: true,\r\n            isShowButtonUpload: true,\r\n            actionUpload: this.uploadFile.bind(this),\r\n            disabled: false\r\n        }\r\n    }\r\n\r\n    onSubmitSearch(){\r\n        let me = this;\r\n        me.pageNumber = 0;\r\n        me.search(0, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    updateParams(dataParams){\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if(this.searchInfo[key] != null){\r\n                if(key == \"contractDateFrom\"){\r\n                    dataParams[\"contractDateFrom\"] = this.searchInfo.contractDateFrom.getTime();\r\n                }else if(key == \"contractDateTo\"){\r\n                    dataParams[\"contractDateTo\"] = this.searchInfo.contractDateTo.getTime();\r\n                }else{\r\n                    dataParams[key] = this.searchInfo[key];\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n    search(page, limit, sort, params){\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let me = this;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        this.updateParams(dataParams);\r\n        me.messageCommonService.onload();\r\n        this.deviceService.search(dataParams, (response)=>{\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    onChangeDateFrom(value){\r\n        if(value){\r\n            this.minDateTo = value;\r\n        }else{\r\n            this.minDateTo = null\r\n        }\r\n    }\r\n\r\n    onChangeDateTo(value){\r\n        if(value){\r\n            this.maxDateFrom = value;\r\n        }else{\r\n            this.maxDateFrom = null;\r\n        }\r\n    }\r\n    navigateToCreateDevice() {\r\n        this.router.navigate(['/devices/create']);\r\n    }\r\n    clearFileCallback(){\r\n        this.isShowErrorUpload = false;\r\n    }\r\n    uploadFile(objectFile: any) {\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.deviceService.uploadRegisterByFile(objectFile, (response) => {\r\n            me.messageCommonService.offload();\r\n            console.log(response)\r\n            me.excuteResponseImportFile(response);\r\n        })\r\n    }\r\n    excuteResponseImportFile(response) {\r\n        let me = this;\r\n        me.simImportsOrigin = undefined;\r\n        me.isShowErrorUpload = false;\r\n        this.optionInputFile.disabled = true;\r\n        if (response.errorCode == CONSTANTS.DEVICE.SUCCESS) {\r\n            me.messageCommonService.success(me.tranService.translate(\"device.text.messageSuccess\"));\r\n            me.isShowDialogImportByFile = false;\r\n            me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n            return;\r\n        } else if (response.errorCode == CONSTANTS.DEVICE.WRONG_FOMAT){\r\n            me.messageCommonService.error(me.tranService.translate(\"device.text.wrongFormat\"));\r\n            me.isShowDialogImportByFile = false;\r\n            return;\r\n        }\r\n        else if (response.errorCode == CONSTANTS.DEVICE.FILE_TO_BIG){\r\n            me.messageCommonService.error(me.tranService.translate(\"device.text.tooBig\"));\r\n            me.isShowDialogImportByFile = false;\r\n            return;\r\n        }else if (response.errorCode == CONSTANTS.DEVICE.COLUMN_INVALID) {\r\n            me.messageCommonService.error(me.tranService.translate(\"device.text.columnInvalid\"));\r\n            me.isShowDialogImportByFile = false;\r\n            return;\r\n        } if (response.errorCode == CONSTANTS.DEVICE.MAX_ROW_FILE_IMPORT) {\r\n            me.messageCommonService.error(me.tranService.translate(\"device.text.maxRowImport\"));\r\n            me.isShowDialogImportByFile = false;\r\n            return;\r\n        }else {\r\n            let data = [];\r\n            response.errorList.forEach(item => {\r\n                data.push({\r\n                    imei: item.imei,\r\n                    msisdn: item.msisdn,\r\n                    // location: item.location,\r\n                    deviceType: item.deviceType,\r\n                    expiredDate: item.expiredDate,\r\n                    country: item.country,\r\n                    error: this.getErrorContent(item.errorCode),\r\n                })\r\n            })\r\n            if (data.length === 0) {\r\n                me.messageCommonService.success(me.tranService.translate(\"device.text.messageSuccess\"));\r\n                me.isShowDialogImportByFile = false;\r\n                return;\r\n            }\r\n            me.isShowDialogImportByFile = false;\r\n            const header = [me.tranService.translate(\"device.label.imei\"),\r\n                me.tranService.translate(\"device.label.msisdn\"),\r\n                // me.tranService.translate(\"device.label.location\"),\r\n                me.tranService.translate(\"device.label.deviceType\"),\r\n                me.tranService.translate(\"device.label.expireDate\"),\r\n                me.tranService.translate(\"device.label.country\"),\r\n                me.tranService.translate(\"device.label.note\")]\r\n            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);\r\n            XLSX.utils.sheet_add_aoa(ws, [header], { origin: 'A1' });\r\n            const columnWidths = { A: { wch: 20 }, B: { wch: 20 }, C: { wch: 15 }, D: { wch: 15 }, E: { wch: 15 }, F: { wch: 15 }, G: { wch: 50 } };\r\n            ws['!cols'] = Object.keys(columnWidths).map(col => ({ ...{ width: columnWidths[col].wch }, ...columnWidths[col] }));\r\n            const wb: XLSX.WorkBook = XLSX.utils.book_new();\r\n            XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');\r\n            XLSX.writeFile(wb, 'error.xlsx');\r\n            me.messageCommonService.warning(me.tranService.translate(\"device.text.textResultImportByFile\"))\r\n        }\r\n    }\r\n    downloadTemplate(){\r\n        this.deviceService.downloadTemplate();\r\n    }\r\n    importByFile() {\r\n        let me = this;\r\n        me.isShowDialogImportByFile = true;\r\n        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});\r\n        me.simImportsOrigin = undefined;\r\n        me.isShowErrorUpload = false;\r\n    }\r\n    getErrorContent(code) {\r\n        let me = this;\r\n        if (code == CONSTANTS.DEVICE.MSISDN_IS_EMPTY) {\r\n            return me.tranService.translate('device.text.msisdnEmpty')\r\n        } else if (code == CONSTANTS.DEVICE.MSISDN_NOTEXITS) {\r\n            return me.tranService.translate('device.text.msisdnNotExists')\r\n        } else if (code == CONSTANTS.DEVICE.MSISDN_ASSIGN) {\r\n            return me.tranService.translate('device.text.msisdnAssign')\r\n        } else if (code == CONSTANTS.DEVICE.MSISDN_INVALD) {\r\n            return me.tranService.translate('device.text.msisdnInvalid')\r\n        } else if (code == CONSTANTS.DEVICE.MSISDN_IS_EMPTY) {\r\n            return me.tranService.translate('device.text.msisdnIsEmptly')\r\n        } else if (code == CONSTANTS.DEVICE.MSISDN_IS_DUPLICATE) {\r\n            return me.tranService.translate('device.text.msisdnIsDuplicate')\r\n        } else if (code == CONSTANTS.DEVICE.EXPRIRED_DATE_INVALID) {\r\n            return me.tranService.translate('device.text.expriredDateInvalid')\r\n        } else if (code == CONSTANTS.DEVICE.MSISDN_NOT_PERMISSION) {\r\n            return me.tranService.translate('device.text.msisdnNotPermission')\r\n        } else if (code == CONSTANTS.DEVICE.IMEI_IS_EXSIT) {\r\n            return me.tranService.translate('device.text.imeiIsExist')\r\n        } else if (code == CONSTANTS.DEVICE.IMEI_IS_DUPLITE) {\r\n            return me.tranService.translate('device.text.imeiIsDuplicate')\r\n        } else if (code == CONSTANTS.DEVICE.IMEI_LEN) {\r\n            return me.tranService.translate('device.text.imeiLen')\r\n        } else if (code == CONSTANTS.DEVICE.DEVICE_TYPE_LEN) {\r\n            return me.tranService.translate('device.text.deviceTypeLen')\r\n        } else if (code == CONSTANTS.DEVICE.COUNTRY_LEN) {\r\n            return me.tranService.translate('device.text.countryLen');\r\n        }\r\n        return \"\"\r\n    }\r\n    convertDateString(dateString: string): string {\r\n        // Create a new Date object from the string\r\n        const date = new Date(dateString);\r\n\r\n        // Extract day, month, and year\r\n        const day = date.getDate();\r\n        const month = date.getMonth() + 1; // Months are zero-based\r\n        const year = date.getFullYear();\r\n\r\n        // Format day and month to two digits\r\n        const formattedDay = day < 10 ? `0${day}` : `${day}`;\r\n        const formattedMonth = month < 10 ? `0${month}` : `${month}`;\r\n\r\n        // Return the formatted date string\r\n        return `${formattedDay}/${formattedMonth}/${year}`;\r\n    };\r\n\r\n    findCellId(cell_lac){\r\n        let me = this;\r\n        this.deviceService.findCellId({loc: cell_lac.split(\":\")[1], cell: cell_lac.split(\":\")[0]}, (response)=>{\r\n                me.findCellIDDto = {\r\n                    lat: response.lat,\r\n                    lng: response.lng\r\n                }\r\n                me.findAddress(response.lat, response.lng)\r\n        }, null, null)\r\n    };\r\n\r\n    findAddress(lat, lon){\r\n        let me = this;\r\n        me.deviceService.findAddress(lat, lon, (response)=>{\r\n            me.deviceInfo.location = response.display_name\r\n            const url = `https://www.google.com/maps?q=${me.findCellIDDto.lat},${me.findCellIDDto.lng}&output=embed`;\r\n            me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);\r\n        }, null, null)\r\n    }\r\n\r\n    initForm() {\r\n        // debugger\r\n        let me = this;\r\n        me.formDetailDevice = me.formBuilder.group({\r\n            imei: [me.deviceInfo.imei],\r\n            location: [me.deviceInfo.location],\r\n            msisdn: [me.deviceInfo.msisdn],\r\n            country: [me.deviceInfo.country],\r\n            expiredDate: [me.utilService.convertDateToString(new Date(me.deviceInfo.expiredDate))],\r\n            deviceType: [me.deviceInfo.deviceType],\r\n            iotLink: [me.deviceInfo.iotLink],\r\n        });\r\n        if (me.deviceInfo.iotLink === 1) {\r\n            me.formDetailDevice.controls[\"iotLink\"].setValue(true);\r\n        }\r\n    };\r\n\r\n    goUpdate() {\r\n        this.router.navigate([`/devices/edit/${this.msisdn}`]);\r\n    };\r\n\r\n    getNameStatus(value){\r\n        if(value == 0){\r\n            return this.tranService.translate(\"sim.status.inventory\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n            // return this.tranService.translate(\"sim.status.ready\");\r\n            return this.tranService.translate(\"sim.status.activated\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n            return this.tranService.translate(\"sim.status.activated\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n            return this.tranService.translate(\"sim.status.deactivated\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n            return this.tranService.translate(\"sim.status.purged\");\r\n        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n            return this.tranService.translate(\"sim.status.inactivated\");\r\n        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n            return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n            return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n            return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getClassStatus(value){\r\n        if(value == 0){\r\n            return ['p-1' , \"border-round\", \"border-400\", \"text-color\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.READY){\r\n            // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n            return ['p-2', \"text-green-800\", \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){\r\n            return ['p-2', 'text-green-800', \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){\r\n            return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){\r\n            return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.SIM_STATUS.PURGED){\r\n            return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){\r\n            return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){\r\n            return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){\r\n            return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\",\"inline-block\"];\r\n        }\r\n        return [];\r\n    }\r\n\r\n    getServiceType(value) {\r\n        if(value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate(\"sim.serviceType.prepaid\")\r\n        else if(value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate(\"sim.serviceType.postpaid\")\r\n        else return \"\"\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.listdevice\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button [label]=\"tranService.translate('global.button.add')\" (click)=\"navigateToCreateDevice()\" styleClass=\"p-button-info mr-2\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])\"></p-button>\r\n        <p-button [label]=\"tranService.translate('global.button.import')\" (click)=\"importByFile()\" styleClass=\"p-button-success\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])\"></p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearch\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid grid-4\">\r\n            <!-- imei -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                           class=\"w-full\"\r\n                           pInputText id=\"imei\"\r\n                           [(ngModel)]=\"searchInfo.imei\"\r\n                           formControlName=\"imei\"\r\n                    />\r\n                    <label htmlFor=\"imei\">{{tranService.translate(\"device.label.imei\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"msisdn\"\r\n                           [(ngModel)]=\"searchInfo.msisdn\"\r\n                           formControlName=\"msisdn\"\r\n                    />\r\n                    <label htmlFor=\"msisdn\">{{tranService.translate(\"device.label.subcriber\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- xuat xu -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"country\"\r\n                           [(ngModel)]=\"searchInfo.country\"\r\n                           formControlName=\"country\"\r\n                    />\r\n                    <label htmlFor=\"country\">{{tranService.translate(\"device.label.country\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- chung loai -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"deviceType\"\r\n                           [(ngModel)]=\"searchInfo.deviceType\"\r\n                           formControlName=\"deviceType\"\r\n                    />\r\n                    <label htmlFor=\"deviceType\">{{tranService.translate(\"device.label.deviceType\")}}</label>\r\n                </span>\r\n            </div>\r\n\r\n            <div class=\"col-3 pb-0\">\r\n                <span class=\"p-float-label date-filter-1\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                            id=\"contractDateFrom\"\r\n                            [(ngModel)]=\"searchInfo.contractDateFrom\"\r\n                            formControlName=\"contractDateFrom\"\r\n                            [showIcon]=\"true\"\r\n                            [showClear]=\"true\"\r\n                            dateFormat=\"dd/mm/yy\"\r\n                            [maxDate]=\"maxDateFrom\"\r\n                            (onSelect)=\"onChangeDateFrom(searchInfo.contractDateFrom)\"\r\n                            (onInput)=\"onChangeDateFrom(searchInfo.contractDateFrom)\"\r\n                    ></p-calendar>\r\n                    <label htmlFor=\"contractDateFrom\">{{tranService.translate(\"device.label.expireFrom\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <span class=\"p-float-label date-filter-1\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                            id=\"contractDateTo\"\r\n                            [(ngModel)]=\"searchInfo.contractDateTo\"\r\n                            formControlName=\"contractDateTo\"\r\n                            [showIcon]=\"true\"\r\n                            [showClear]=\"true\"\r\n                            dateFormat=\"dd/mm/yy\"\r\n                            [minDate]=\"minDateTo\"\r\n                            [maxDate]=\"maxDateTo\"\r\n                            (onSelect)=\"onChangeDateTo(searchInfo.contractDateTo)\"\r\n                            (onInput)=\"onChangeDateTo(searchInfo.contractDateTo)\"\r\n                    />\r\n                    <label htmlFor=\"contractDateTo\">{{tranService.translate(\"device.label.expireTo\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('device.label.importByFile')\" [(visible)]=\"isShowDialogImportByFile\" [modal]=\"true\" [style]=\"{ width: '700px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"w-full field grid\">\r\n            <div class=\"col-10 flex flex-row justify-content-start align-items-center\">\r\n                <input-file-vnpt class=\"w-full\" [(fileObject)]=\"fileObject\" [clearFileCallback]=\"clearFileCallback.bind(this)\"\r\n                                 [options]=\"optionInputFile\"\r\n                ></input-file-vnpt>\r\n            </div>\r\n            <div class=\"col-2 flex flex-row justify-content-end align-items-center\">\r\n                <p-button icon=\"pi pi-download\" [pTooltip]=\"tranService.translate('global.button.downloadTemp')\" styleClass=\"p-button-outlined p-button-secondary\" (click)=\"downloadTemplate()\"></p-button>\r\n            </div>\r\n        </div>\r\n        <div class=\"grid\"><div class=\"col pt-0\"><small class=\"text-red-500\" *ngIf=\"isShowErrorUpload\">{{messageErrorUpload}}</small></div></div>\r\n    </p-dialog>\r\n</div>\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [header]=\"tranService.translate('sim.text.detailSim')\" [(visible)]=\"isShowPopupDetailSim\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"grid grid-1 mt-1 h-auto\" style=\"width: calc(100% + 16px);\">\r\n            <div class=\"col sim-detail pr-0\">\r\n                <p-card [header]=\"tranService.translate('sim.text.simInfo')\">\r\n                    <div class=\"flex flex-row justify-content-between custom-card\">\r\n                        <div class=\"w-6\">\r\n                            <div class=\"grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.sothuebao\")}}</span>\r\n                                <span class=\"col\">{{detailSim.msisdn}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.trangthaisim\")}}</span>\r\n                                <span class=\"w-auto ml-3\" [class]=\"getClassStatus(detailSim.status)\">{{getNameStatus(detailSim.status)}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.imsi\")}}</span>\r\n                                <span class=\"col\">{{detailSim.imsi}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.imeiDevice\")}}</span>\r\n                                <span class=\"col\">{{detailSim.imei}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.maapn\")}}</span>\r\n                                <span class=\"col\">{{detailSim.apnId}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.trangthaiketnoi\")}}</span>\r\n                                <span *ngIf=\"detailSim.connectionStatus!==undefined && detailSim.connectionStatus!==null && detailSim.connectionStatus!=='0' \" class=\"ml-3 p-2 text-green-800 bg-green-100 border-round inline-block\">ON</span>\r\n                                <span *ngIf=\"detailSim.connectionStatus==='0'\" class=\"ml-3 p-2 text-50 surface-500 border-round inline-block\">OFF</span>\r\n                                <span *ngIf=\"detailSim.connectionStatus===undefined || detailSim.connectionStatus=== null \" class=\"ml-3 p-2 text-50 surface-500 border-round inline-block\">NOT FOUND</span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-6\">\r\n                            <div class=\"grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.startDate\")}}</span>\r\n                                <span class=\"col\">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.serviceType\")}}</span>\r\n                                <span class=\"w-auto ml-3\">{{getServiceType(detailSim.serviceType)}}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.simStatusInfo')\" styleClass=\"mt-3 sim-status\">\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusData\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.data\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusReceiveCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callReceived\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusSendCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callSent\")}}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusWorldCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callWorld\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusReceiveSms\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.smsReceived\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusSendSms\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.smsSent\")}}</div>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <!-- goi cuoc -->\r\n                <p-card [header]=\"tranService.translate('sim.text.ratingPlanInfo')\" styleClass=\"mt-3\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.tengoicuoc\")}}</span>\r\n                        <span class=\"col\">{{detailSim.ratingPlanName}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.dataUseInMonth\")}}</span>\r\n                        <span class=\"col\">{{this.utilService.bytesToMegabytes(detailRatingPlan.dataUseInMonth) | number }} {{detailRatingPlan.unit?detailRatingPlan.unit:\"MB\"}}</span>\r\n                    </div>\r\n                </p-card>\r\n            </div>\r\n            <div class=\"col sim-detail pr-0\">\r\n                <!-- hop dong -->\r\n                <p-card [header]=\"tranService.translate('sim.text.contractInfo')\">\r\n                    <div class=\"grid mt-0\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.mahopdong\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contractCode}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.ngaylamhopdong\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.nguoilamhopdong\")}}</span>\r\n                        <span class=\"col uppercase\">{{detailContract.contractorInfo}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.matrungtam\")}}</span>\r\n                        <span class=\"col\">{{detailContract.centerCode}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.dienthoailienhe\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contactPhone}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.diachilienhe\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contactAddress}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.paymentName\")}}</span>\r\n                        <span class=\"col uppercase\">{{detailContract.paymentName}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.paymentAddress\")}}</span>\r\n                        <span class=\"col\">{{detailContract.paymentAddress}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.routeCode\")}}</span>\r\n                        <span class=\"col\">{{detailContract.routeCode}}</span>\r\n                    </div>\r\n                </p-card>\r\n                <!-- customer -->\r\n                <p-card [header]=\"tranService.translate('sim.text.customerInfo')\" styleClass=\"mt-3\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.khachhang\")}}</span>\r\n                        <span class=\"col\">{{detailCustomer.name}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.customerCode\")}}</span>\r\n                        <span class=\"col\">{{detailCustomer.code}}</span>\r\n                    </div>\r\n                </p-card>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n<!-- detail device -->\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [header]=\"tranService.translate('global.menu.devicedetail')\" [(visible)]=\"isShowPopupDetailDevice\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <p-card styleClass=\"mt-3\">\r\n            <form [formGroup]=\"formDetailDevice\" *ngIf=\"isShowPopupDetailDevice\">\r\n                <div class=\"grid mx-4 my-3\">\r\n                    <!--            imei-->\r\n                    <div class=\"col-3 \">\r\n                        <span class=\"p-float-label\">\r\n                            <input class=\"w-full\"\r\n                                   pInputText id=\"imei\"\r\n                                   [(ngModel)]=\"deviceInfo.imei\"\r\n                                   formControlName=\"imei\"\r\n                                   pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]{2,255}$\" readonly\r\n                            >\r\n                            <label htmlFor=\"imei\">{{ tranService.translate(\"device.label.imei\") }}</label>\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"col-3\">\r\n                        <span class=\"p-float-label\">\r\n                            <input class=\"w-full\"\r\n                                   pInputText id=\"deviceType\"\r\n                                   [(ngModel)]=\"deviceInfo.deviceType\"\r\n                                   formControlName=\"deviceType\"\r\n                                   pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]{2,255}$\" [readonly]=\"true\"\r\n                                   autofocus\r\n                            >\r\n                            <label htmlFor=\"deviceType\">{{ tranService.translate(\"device.label.deviceType\") }}</label>\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"col-3\">\r\n                        <span class=\"p-float-label\">\r\n                                <input class=\"w-full\"\r\n                                   pInputText id=\"msisdn\"\r\n                                   [(ngModel)]=\"deviceInfo.msisdn\"\r\n                                   formControlName=\"msisdn\"\r\n                                   readonly\r\n                                >\r\n                            <label htmlFor=\"msisdn\">{{ tranService.translate(\"device.label.msisdn\") }}</label>\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"col-3\">\r\n                        <span class=\"p-float-label\">\r\n                            <input class=\"w-full\"\r\n                                   pInputText id=\"country\"\r\n                                   [(ngModel)]=\"deviceInfo.country\"\r\n                                   formControlName=\"country\"\r\n                                   pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]{2,255}$\" [readonly]=\"true\"\r\n                                   [disabled]=\"true\">\r\n                            <label htmlFor=\"country\">{{ tranService.translate(\"device.label.country\") }}</label>\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"col-3 \">\r\n                        <span class=\"p-float-label\" disabled=\"true\">\r\n                            <p-calendar styleClass=\"w-full\"\r\n                                        id=\"expiredDate\"\r\n                                        [(ngModel)]=\"deviceInfo.expiredDate\"\r\n                                        formControlName=\"expiredDate\"\r\n                                        [disabled]=\"true\"\r\n                                        disabledDays=\"1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31\"\r\n                                        [readonlyInput]=\"true\"\r\n                                        [minDate]=\"minDateTo\"\r\n                                        [maxDate]=\"maxDateTo\"\r\n                            ></p-calendar>\r\n                            <label htmlFor=\"expiredDate\">{{ tranService.translate(\"device.label.expireDate\") }}</label>\r\n                        </span>\r\n                    </div>\r\n\r\n                    <!--            vị trí-->\r\n                    <div class=\"col-3\">\r\n                        <span class=\"p-float-label\">\r\n                            <input class=\"w-full\"\r\n                                   pInputText id=\"location\"\r\n                                   [(ngModel)]=\"deviceInfo.location\"\r\n                                   formControlName=\"location\"\r\n                                   pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]{2,255}$\" readonly>\r\n\r\n                            <label htmlFor=\"location\">{{ tranService.translate(\"device.label.location\") }}</label>\r\n                        </span>\r\n                    </div>\r\n\r\n                    <div class=\"col-3\">\r\n                        <label class=\"flex align-items-center\">\r\n                            <p-checkbox [disabled]=\"true\" [readonly]=\"true\" formControlName=\"iotLink\" id=\"iotLink\"\r\n                                        inputId=\"iotLink\" [binary]=\"true\" [ngStyle]=\"{'margin': '6px'}\"></p-checkbox>\r\n                            <label for=\"iotLink\">{{ tranService.translate(\"device.label.iotLink\") }}</label>\r\n                        </label>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-offset-2 col-8\">\r\n                    <iframe [src]=\"safeUrl\" class=\"w-full\" style=\"border:0;\" allowfullscreen=\"true\" loading=\"lazy\" height=\"500\"\r\n                            referrerpolicy=\"no-referrer-when-downgrade\"></iframe>\r\n                </div>\r\n\r\n<!--                <div class=\"flex flex-row justify-content-center align-items-center mt-3\">-->\r\n<!--                    <p-button styleClass=\"p-button-info \"-->\r\n<!--                              (click)=\"goUpdate()\">{{ tranService.translate(\"global.button.update\") }}-->\r\n<!--                    </p-button>-->\r\n<!--                </div>-->\r\n            </form>\r\n        </p-card>\r\n    </p-dialog>\r\n</div>\r\n<table-vnpt\r\n    [fieldId]=\"'msisdn'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"this.tranService.translate('global.menu.listdevice')\"\r\n></table-vnpt>\r\n"], "mappings": "AAGA,SAAQA,aAAa,QAAO,sCAAsC;AAElE,SAAQC,SAAS,QAAO,kCAAkC;AAE1D,SAAQC,aAAa,QAAO,yBAAyB;AACrD,OAAO,KAAKC,IAAI,MAAM,MAAM;AAE5B,SAAQC,UAAU,QAAO,iCAAiC;;;;;;;;;;;;;;;;;;;;;;ICJlDC,EAAA,CAAAC,cAAA,mBAA6L;IAA9HD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAA4FT,EAAA,CAAAU,YAAA,EAAW;;;;IAA9LV,EAAA,CAAAW,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,sBAAoD;;;;;;IAC9Dd,EAAA,CAAAC,cAAA,mBAAoL;IAAlHD,EAAA,CAAAE,UAAA,mBAAAa,kEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,IAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,MAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAA0FlB,EAAA,CAAAU,YAAA,EAAW;;;;IAArLV,EAAA,CAAAW,UAAA,UAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,yBAAuD;;;;;IA0GzBd,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAoB,MAAA,GAAsB;IAAApB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9BV,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,kBAAA,CAAsB;;;;;IAgC5FxB,EAAA,CAAAC,cAAA,eAAsM;IAAAD,EAAA,CAAAoB,MAAA,SAAE;IAAApB,EAAA,CAAAU,YAAA,EAAO;;;;;IAC/MV,EAAA,CAAAC,cAAA,eAA8G;IAAAD,EAAA,CAAAoB,MAAA,UAAG;IAAApB,EAAA,CAAAU,YAAA,EAAO;;;;;IACxHV,EAAA,CAAAC,cAAA,eAA2J;IAAAD,EAAA,CAAAoB,MAAA,gBAAS;IAAApB,EAAA,CAAAU,YAAA,EAAO;;;;;;;;;;;IAoH/LV,EAAA,CAAAC,cAAA,eAAqE;IAO9CD,EAAA,CAAAE,UAAA,2BAAAuB,qEAAAC,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAoB,OAAA,CAAAC,UAAA,CAAAC,IAAA,GAAAJ,MAAA,CAC3C;IAAA,EAD2D;IAFpC1B,EAAA,CAAAU,YAAA,EAKC;IACDV,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAoB,MAAA,GAAgD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAGtFV,EAAA,CAAAC,cAAA,cAAmB;IAIJD,EAAA,CAAAE,UAAA,2BAAA6B,qEAAAL,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAK,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAH,UAAA,CAAAI,UAAA,GAAAP,MAAA,CAC3C;IAAA,EADiE;IAF1C1B,EAAA,CAAAU,YAAA,EAMC;IACDV,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAoB,MAAA,IAAsD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAGlGV,EAAA,CAAAC,cAAA,eAAmB;IAIJD,EAAA,CAAAE,UAAA,2BAAAgC,sEAAAR,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAQ,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAN,UAAA,CAAAO,MAAA,GAAAV,MAAA,CAC3C;IAAA,EAD6D;IAFlC1B,EAAA,CAAAU,YAAA,EAKC;IACLV,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAoB,MAAA,IAAkD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAG1FV,EAAA,CAAAC,cAAA,eAAmB;IAIJD,EAAA,CAAAE,UAAA,2BAAAmC,sEAAAX,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAW,OAAA,GAAAtC,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA8B,OAAA,CAAAT,UAAA,CAAAU,OAAA,GAAAb,MAAA,CAC3C;IAAA,EAD8D;IAFvC1B,EAAA,CAAAU,YAAA,EAKyB;IACzBV,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAAmD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAG5FV,EAAA,CAAAC,cAAA,eAAoB;IAIAD,EAAA,CAAAE,UAAA,2BAAAsC,2EAAAd,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAc,OAAA,GAAAzC,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAiC,OAAA,CAAAZ,UAAA,CAAAa,WAAA,GAAAhB,MAAA,CAChD;IAAA,EADuE;IAO/C1B,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAoB,MAAA,IAAsD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAKnGV,EAAA,CAAAC,cAAA,eAAmB;IAIJD,EAAA,CAAAE,UAAA,2BAAAyC,sEAAAjB,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAiB,OAAA,GAAA5C,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAoC,OAAA,CAAAf,UAAA,CAAAgB,QAAA,GAAAnB,MAAA,CAC3C;IAAA,EAD+D;IAFxC1B,EAAA,CAAAU,YAAA,EAI4E;IAE5EV,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAoB,MAAA,IAAoD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAI9FV,EAAA,CAAAC,cAAA,eAAmB;IAEXD,EAAA,CAAA8C,SAAA,sBACyF;IACzF9C,EAAA,CAAAC,cAAA,iBAAqB;IAAAD,EAAA,CAAAoB,MAAA,IAAmD;IAAApB,EAAA,CAAAU,YAAA,EAAQ;IAI5FV,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAA8C,SAAA,kBAC6D;IACjE9C,EAAA,CAAAU,YAAA,EAAM;;;;IAxFJV,EAAA,CAAAW,UAAA,cAAAoC,MAAA,CAAAC,gBAAA,CAA8B;IAObhD,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAAW,UAAA,YAAAoC,MAAA,CAAAlB,UAAA,CAAAC,IAAA,CAA6B;IAId9B,EAAA,CAAAqB,SAAA,GAAgD;IAAhDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,sBAAgD;IAO/Dd,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAAW,UAAA,YAAAoC,MAAA,CAAAlB,UAAA,CAAAI,UAAA,CAAmC;IAKdjC,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAsD;IAO3Ed,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAW,UAAA,YAAAoC,MAAA,CAAAlB,UAAA,CAAAO,MAAA,CAA+B;IAIdpC,EAAA,CAAAqB,SAAA,GAAkD;IAAlDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,wBAAkD;IAOnEd,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAW,UAAA,YAAAoC,MAAA,CAAAlB,UAAA,CAAAU,OAAA,CAAgC;IAIdvC,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,yBAAmD;IAOhEd,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAW,UAAA,YAAAoC,MAAA,CAAAlB,UAAA,CAAAa,WAAA,CAAoC,qDAAAK,MAAA,CAAAE,SAAA,aAAAF,MAAA,CAAAG,SAAA;IAQnBlD,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAsD;IAS5Ed,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAW,UAAA,YAAAoC,MAAA,CAAAlB,UAAA,CAAAgB,QAAA,CAAiC;IAId7C,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,0BAAoD;IAMlEd,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAW,UAAA,kBAAiB,8CAAAX,EAAA,CAAAmD,eAAA,KAAAC,GAAA;IAERpD,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAsB,iBAAA,CAAAyB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,yBAAmD;IAKxEd,EAAA,CAAAqB,SAAA,GAAe;IAAfrB,EAAA,CAAAW,UAAA,QAAAoC,MAAA,CAAAM,OAAA,EAAArD,EAAA,CAAAsD,qBAAA,CAAe;;;;;;;;;;;;;;;;AD7U3C,OAAM,MAAOC,mBAAoB,SAAQ1D,aAAa;EAClD2D,YACmCC,aAA4B,EAC/BC,UAAsB,EAC1CC,SAAuB,EACvBC,WAAwB,EAChCC,QAAkB;IAClB,KAAK,CAACA,QAAQ,CAAC;IALgB,KAAAJ,aAAa,GAAbA,aAAa;IAChB,KAAAC,UAAU,GAAVA,UAAU;IAC9B,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IA+BvB,KAAAE,oBAAoB,GAAY,KAAK;IACrC,KAAAC,uBAAuB,GAAY,KAAK;IAExC,KAAAC,SAAS,GAAO,EAAE;IAClB,KAAAC,eAAe,GAAM,EAAE;IACvB,KAAAC,cAAc,GAAK,EAAE;IACrB,KAAAC,gBAAgB,GAAM,EAAE;IACxB,KAAAC,cAAc,GAAM,EAAE;IACtB,KAAAC,SAAS,GAAM,EAAE;IAajB,KAAAC,WAAW,GAA4B,IAAI;IAC3C,KAAArB,SAAS,GAA4B,IAAI;IACzC,KAAAC,SAAS,GAA4B,IAAI;IACzC,KAAAqB,wBAAwB,GAAY,KAAK;IAEzC,KAAAC,iBAAiB,GAAY,KAAK;IAkhBf,KAAA5E,SAAS,GAAGA,SAAS;EAxkBxC;EA0DA6E,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACC,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAAClE,WAAW,CAACC,SAAS,CAAC,wBAAwB;IAAC,CAAE,EAAE;MAAEiE,KAAK,EAAE,IAAI,CAAClE,WAAW,CAACC,SAAS,CAAC,wBAAwB;IAAC,CAAE,CAAE;IAChJ,IAAI,CAACkE,UAAU,GAAG;MACdlD,IAAI,EAAE,IAAI;MACVmD,IAAI,EAAE,IAAI;MACV7C,MAAM,EAAE,IAAI;MACZ;MACAH,UAAU,EAAE,IAAI;MAChBM,OAAO,EAAE,IAAI;MACb2C,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE;KACnB;IACD,IAAI,CAACnB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACnC,UAAU,GAAG;MACdC,IAAI,EAAE,IAAI;MACVe,QAAQ,EAAE,IAAI;MACdT,MAAM,EAAE,IAAI;MACZG,OAAO,EAAE,IAAI;MACb6C,QAAQ,EAAE,IAAI;MACd1C,WAAW,EAAE,IAAI;MACjBT,UAAU,EAAE,IAAI;MAChBoD,OAAO,EAAE;KACZ;IACD,IAAI,CAACpB,eAAe,GAAG;MACnBqB,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE;KAClB;IACD,MAAMC,GAAG,GAAG,8QAA8Q;IAC1R,IAAI,CAACvC,OAAO,GAAG,IAAI,CAACM,SAAS,CAACkC,8BAA8B,CAACD,GAAG,CAAC;IACjE,IAAI,CAACvC,OAAO,GAAG,IAAI,CAACM,SAAS,CAACkC,8BAA8B,CAACD,GAAG,CAAC;IACjE,IAAI,CAACE,UAAU,GAAG,IAAI,CAAClC,WAAW,CAACmC,KAAK,CAAC,IAAI,CAACf,UAAU,CAAC;IACzD,IAAI,CAACgB,OAAO,GAAG,CACX;MACIC,IAAI,EAAE,IAAI,CAACpF,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACrDoF,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIL,IAAI,EAAE,IAAI,CAACpF,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3DoF,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIL,IAAI,EAAE,IAAI,CAACpF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DoF,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAC;QACFC,MAAM,EAAE,SAAS;QACxBC,KAAK,EAAE;OACH;MACDC,SAASA,CAACC,EAAE,EAAEC,IAAI;QACdlC,EAAE,CAACZ,oBAAoB,GAAG,IAAI;QAC9BY,EAAE,CAACmC,KAAK,GAAGF,EAAE,CAACG,QAAQ,EAAE;QACxBpC,EAAE,CAAChB,UAAU,CAACqD,OAAO,CAACrC,EAAE,CAACmC,KAAK,EAAGG,QAAQ,IAAG;UACxCtC,EAAE,CAACV,SAAS,GAAG;YACX,GAAGgD;WACN;UACDtC,EAAE,CAAChB,UAAU,CAACuD,eAAe,CAACvC,EAAE,CAACmC,KAAK,EAAGG,QAAQ,IAAG;YAChDtC,EAAE,CAACT,eAAe,GAAI;cAClBqB,UAAU,EAAE0B,QAAQ,CAACE,UAAU,IAAI,CAAC;cACpC3B,iBAAiB,EAAEyB,QAAQ,CAACG,QAAQ,IAAI,CAAC;cACzC3B,cAAc,EAAEwB,QAAQ,CAACI,QAAQ,IAAI,CAAC;cACtC3B,eAAe,EAAEuB,QAAQ,CAACK,SAAS,IAAI,CAAC;cACxC3B,gBAAgB,EAAEsB,QAAQ,CAACM,SAAS,IAAI,CAAC;cACzC3B,aAAa,EAAEqB,QAAQ,CAACO,SAAS,IAAI;aACxC;UACL,CAAC,EAAC,MAAI,CAAC,CAAC,CAAC;UACT7C,EAAE,CAACR,cAAc,GAAG;YAChB+B,IAAI,EAAEvB,EAAE,CAACV,SAAS,CAACwD,YAAY;YAC/BC,IAAI,EAAE/C,EAAE,CAACV,SAAS,CAAC0D;WACtB;UACDhD,EAAE,CAACR,cAAc,GAAG;YAChB+B,IAAI,EAAEvB,EAAE,CAACV,SAAS,CAACwD,YAAY;YAC/BC,IAAI,EAAE/C,EAAE,CAACV,SAAS,CAAC0D;WACtB;UACDhD,EAAE,CAAChB,UAAU,CAACiE,gBAAgB,CAACjD,EAAE,CAACmC,KAAK,EAAGG,QAAQ,IAAG;YACjDtC,EAAE,CAACP,gBAAgB,GAAG;cAClB,GAAG6C;aACN;UACL,CAAC,EAAE,MAAI,CAAC,CAAC,CAAC;UACVtC,EAAE,CAAChB,UAAU,CAACkE,iBAAiB,CAAClD,EAAE,CAACmD,WAAW,CAACC,iBAAiB,CAACpD,EAAE,CAACV,SAAS,CAAC+D,YAAY,CAAC,EAAGf,QAAQ,IAAG;YACrGtC,EAAE,CAACN,cAAc,GAAG4C,QAAQ;UAChC,CAAC,EAAE,MAAI,CAAC,CAAC,CAAC;UACVtC,EAAE,CAACL,SAAS,GAAG;YACXoD,IAAI,EAAE/C,EAAE,CAACV,SAAS,CAACgE,OAAO;YAC1BC,IAAI,EAAE,iBAAiB;YACvBC,EAAE,EAAE,CAAC;YACLC,OAAO,EAAEzD,EAAE,CAACV,SAAS,CAACkE;WACzB;UACDxD,EAAE,CAAChB,UAAU,CAAC0E,mBAAmB,CAAC,CAAC1D,EAAE,CAACmC,KAAK,CAAC,EAAGwB,IAAI,IAAG;YAClD3D,EAAE,CAACV,SAAS,CAACsE,gBAAgB,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACE,SAAS;UACrD,CAAC,EAAE,MAAI,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,IAAI,EAAC,MAAI;UACR,IAAI,CAACC,oBAAoB,CAACC,OAAO,EAAE;QACvC,CAAC,CAAC;MACN;KACH,EACD;MACIxC,IAAI,EAAE,IAAI,CAACpF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDoF,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIL,IAAI,EAAE,IAAI,CAACpF,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3DoF,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACboC,eAAeA,CAACC,KAAK;QACjB,IAAGA,KAAK,IAAI,IAAI,EAAE,OAAO,EAAE;QAC3B,OAAOjE,EAAE,CAACmD,WAAW,CAACe,mBAAmB,CAAC,IAAIC,IAAI,CAACF,KAAK,CAAC,CAAC;MAC9D;KACH,CACJ;IACD,IAAI,CAACG,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG;MACfC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIxE,IAAI,EAAE,WAAW;QACjByE,OAAO,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDwI,IAAI,EAAE,SAAAA,CAAS3C,EAAE,EAAEC,IAAI;UACnBlC,EAAE,CAACtC,MAAM,GAAGuE,EAAE;UACdjC,EAAE,CAAC8D,oBAAoB,CAACe,MAAM,EAAE;UAChC7E,EAAE,CAACjB,aAAa,CAAC+F,YAAY,CAACC,MAAM,CAAC/E,EAAE,CAACtC,MAAM,CAAC,EAAG4E,QAAQ,IAAI;YAC1DtC,EAAE,CAAC7C,UAAU,GAAG;cACZ,GAAGmF;aACN;YACD,IAAIA,QAAQ,CAACtE,WAAW,IAAI,IAAI,IAAIsE,QAAQ,CAACtE,WAAW,IAAI,EAAE,EAAE;cAC5DgC,EAAE,CAAC7C,UAAU,CAACa,WAAW,GAAG,IAAImG,IAAI,CAAC7B,QAAQ,CAACtE,WAAW,CAAC;cAC1DgC,EAAE,CAACzB,SAAS,GAAGyB,EAAE,CAAC7C,UAAU,CAACa,WAAW;cACxCgC,EAAE,CAACxB,SAAS,GAAGwB,EAAE,CAAC7C,UAAU,CAACa,WAAW;aAC3C,MAAM;cACHgC,EAAE,CAAC7C,UAAU,CAACa,WAAW,GAAG,IAAI;;YAEpCgC,EAAE,CAACgF,QAAQ,EAAE;UACjB,CAAC,EAAE,IAAI,EAAE,MAAK;YACVhF,EAAE,CAAC8D,oBAAoB,CAACC,OAAO,EAAE;UACrC,CAAC,CAAC;UAEF/D,EAAE,CAACjB,aAAa,CAACkG,WAAW,CAACF,MAAM,CAAC/E,EAAE,CAACtC,MAAM,CAAC,EAAG4E,QAAQ,IAAI;YACzD,IAAIA,QAAQ,IAAI,IAAI,EAAE;cAClBtC,EAAE,CAACkF,UAAU,CAAC5C,QAAQ,CAAC6C,YAAY,CAACC,QAAQ,CAAC;aAChD,MAAM;cACHpF,EAAE,CAAC7C,UAAU,CAACgB,QAAQ,GAAG,GAAG;cAC5B,MAAM+C,GAAG,GAAG,8QAA8Q;cAC1RlB,EAAE,CAACrB,OAAO,GAAGqB,EAAE,CAACf,SAAS,CAACkC,8BAA8B,CAACD,GAAG,CAAC;;UAErE,CAAC,CAAC;UACFlB,EAAE,CAACX,uBAAuB,GAAG,IAAI;QACrC,CAAC;QACDgG,UAAU,EAAE,SAAAA,CAAUpD,EAAE,EAAEC,IAAI;UAC1B,OAAOlC,EAAE,CAACsF,WAAW,CAAC,CAACpK,SAAS,CAACqK,WAAW,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC;QACrE;OACH,EACD;QACIvF,IAAI,EAAE,cAAc;QACpByE,OAAO,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDwI,IAAI,EAAE,SAAAA,CAAS3C,EAAE,EAAEC,IAAI;UACnBlC,EAAE,CAAC0F,MAAM,CAACC,QAAQ,CAAC,CAAC,iBAAiBzD,IAAI,CAACxE,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QACD2H,UAAU,EAAE,SAAAA,CAAUpD,EAAE,EAAEC,IAAI;UAC1B,OAAOlC,EAAE,CAACsF,WAAW,CAAC,CAACpK,SAAS,CAACqK,WAAW,CAACC,MAAM,CAACI,MAAM,CAAC,CAAC;QAChE;OACH,EACD;QACI1F,IAAI,EAAE,aAAa;QACnByE,OAAO,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3DwI,IAAI,EAAE,SAAAA,CAAS3C,EAAE,EAAEC,IAAI;UACnBlC,EAAE,CAAC8D,oBAAoB,CAAC+B,OAAO,CAC3B7F,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,yCAAyC,CAAC,EACnE4D,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,EAC9D;YACI0J,EAAE,EAACA,CAAA,KAAI;cACH9F,EAAE,CAAC8D,oBAAoB,CAACe,MAAM,EAAE;cAChC7E,EAAE,CAACjB,aAAa,CAACgH,YAAY,CAAC7D,IAAI,CAACxE,MAAM,EAAG4E,QAAQ,IAAI;gBACpDtC,EAAE,CAAC8D,oBAAoB,CAACkC,OAAO,CAAChG,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;gBACzF4D,EAAE,CAACiG,MAAM,CAACjG,EAAE,CAACkG,UAAU,EAAElG,EAAE,CAACmG,QAAQ,EAAEnG,EAAE,CAACoG,IAAI,EAAEpG,EAAE,CAACM,UAAU,CAAC;cAC7D,CAAC,EAAE,IAAI,EAAE,MAAI;gBACbN,EAAE,CAAC8D,oBAAoB,CAACC,OAAO,EAAE;cACrC,CAAC,CAAC;YACN,CAAC;YACDsC,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDhB,UAAU,EAAE,SAAAA,CAAUpD,EAAE,EAAEC,IAAI;UAC1B,OAAOlC,EAAE,CAACsF,WAAW,CAAC,CAACpK,SAAS,CAACqK,WAAW,CAACC,MAAM,CAACc,MAAM,CAAC,CAAC;QAChE;OACH;KAER;IACD,IAAI,CAACJ,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,UAAU;IAEtB,IAAI,CAACG,OAAO,GAAG;MACXC,OAAO,EAAE,EACR;MACDC,KAAK,EAAE;KACV;IACD,IAAI,CAACR,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC9F,UAAU,CAAC;IACvE,IAAI,CAACoG,eAAe,GAAG;MACnBnD,IAAI,EAAE,CAAC,KAAK,EAAC,MAAM,CAAC;MACpBoD,gBAAgB,EAAE,IAAI,CAACxK,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MAC7EwK,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;MACxCC,QAAQ,EAAE;KACb;EACL;EAEAC,cAAcA,CAAA;IACV,IAAIpH,EAAE,GAAG,IAAI;IACbA,EAAE,CAACkG,UAAU,GAAG,CAAC;IACjBlG,EAAE,CAACiG,MAAM,CAAC,CAAC,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC9F,UAAU,CAAC;EAC3D;EAEA+G,YAAYA,CAACC,UAAU;IACnBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClH,UAAU,CAAC,CAACmH,OAAO,CAACjG,GAAG,IAAG;MACvC,IAAG,IAAI,CAAClB,UAAU,CAACkB,GAAG,CAAC,IAAI,IAAI,EAAC;QAC5B,IAAGA,GAAG,IAAI,kBAAkB,EAAC;UACzB8F,UAAU,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAChH,UAAU,CAACE,gBAAgB,CAACkH,OAAO,EAAE;SAC9E,MAAK,IAAGlG,GAAG,IAAI,gBAAgB,EAAC;UAC7B8F,UAAU,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAChH,UAAU,CAACG,cAAc,CAACiH,OAAO,EAAE;SAC1E,MAAI;UACDJ,UAAU,CAAC9F,GAAG,CAAC,GAAG,IAAI,CAAClB,UAAU,CAACkB,GAAG,CAAC;;;IAGlD,CAAC,CAAC;EACN;EAEAyE,MAAMA,CAAC0B,IAAI,EAAEC,KAAK,EAAExB,IAAI,EAAEyB,MAAM;IAC5B,IAAI,CAAC3B,UAAU,GAAGyB,IAAI;IACtB,IAAI,CAACxB,QAAQ,GAAGyB,KAAK;IACrB,IAAI,CAACxB,IAAI,GAAGA,IAAI;IAChB,IAAIpG,EAAE,GAAG,IAAI;IACb,IAAIsH,UAAU,GAAG;MACbK,IAAI;MACJlG,IAAI,EAAEmG,KAAK;MACXxB;KACH;IACD,IAAI,CAACiB,YAAY,CAACC,UAAU,CAAC;IAC7BtH,EAAE,CAAC8D,oBAAoB,CAACe,MAAM,EAAE;IAChC,IAAI,CAAC9F,aAAa,CAACkH,MAAM,CAACqB,UAAU,EAAGhF,QAAQ,IAAG;MAC9CtC,EAAE,CAACuG,OAAO,GAAG;QACTC,OAAO,EAAElE,QAAQ,CAACkE,OAAO;QACzBC,KAAK,EAAEnE,QAAQ,CAACwF;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACT9H,EAAE,CAAC8D,oBAAoB,CAACC,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAgE,gBAAgBA,CAAC9D,KAAK;IAClB,IAAGA,KAAK,EAAC;MACL,IAAI,CAAC1F,SAAS,GAAG0F,KAAK;KACzB,MAAI;MACD,IAAI,CAAC1F,SAAS,GAAG,IAAI;;EAE7B;EAEAyJ,cAAcA,CAAC/D,KAAK;IAChB,IAAGA,KAAK,EAAC;MACL,IAAI,CAACrE,WAAW,GAAGqE,KAAK;KAC3B,MAAI;MACD,IAAI,CAACrE,WAAW,GAAG,IAAI;;EAE/B;EACA7D,sBAAsBA,CAAA;IAClB,IAAI,CAAC2J,MAAM,CAACC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC7C;EACAsC,iBAAiBA,CAAA;IACb,IAAI,CAACnI,iBAAiB,GAAG,KAAK;EAClC;EACAmH,UAAUA,CAACiB,UAAe;IACtB,IAAIlI,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC8D,oBAAoB,CAACe,MAAM,EAAE;IAChC,IAAI,CAAC9F,aAAa,CAACoJ,oBAAoB,CAACD,UAAU,EAAG5F,QAAQ,IAAI;MAC7DtC,EAAE,CAAC8D,oBAAoB,CAACC,OAAO,EAAE;MACjCqE,OAAO,CAACC,GAAG,CAAC/F,QAAQ,CAAC;MACrBtC,EAAE,CAACsI,wBAAwB,CAAChG,QAAQ,CAAC;IACzC,CAAC,CAAC;EACN;EACAgG,wBAAwBA,CAAChG,QAAQ;IAC7B,IAAItC,EAAE,GAAG,IAAI;IACbA,EAAE,CAACuI,gBAAgB,GAAGC,SAAS;IAC/BxI,EAAE,CAACF,iBAAiB,GAAG,KAAK;IAC5B,IAAI,CAAC4G,eAAe,CAACS,QAAQ,GAAG,IAAI;IACpC,IAAI7E,QAAQ,CAACmG,SAAS,IAAIvN,SAAS,CAACsK,MAAM,CAACkD,OAAO,EAAE;MAChD1I,EAAE,CAAC8D,oBAAoB,CAACkC,OAAO,CAAChG,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;MACvF4D,EAAE,CAACH,wBAAwB,GAAG,KAAK;MACnCG,EAAE,CAACiG,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC9F,UAAU,CAAC;MACrE;KACH,MAAM,IAAIgC,QAAQ,CAACmG,SAAS,IAAIvN,SAAS,CAACsK,MAAM,CAACmD,WAAW,EAAC;MAC1D3I,EAAE,CAAC8D,oBAAoB,CAAC8E,KAAK,CAAC5I,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC;MAClF4D,EAAE,CAACH,wBAAwB,GAAG,KAAK;MACnC;KACH,MACI,IAAIyC,QAAQ,CAACmG,SAAS,IAAIvN,SAAS,CAACsK,MAAM,CAACqD,WAAW,EAAC;MACxD7I,EAAE,CAAC8D,oBAAoB,CAAC8E,KAAK,CAAC5I,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC,CAAC;MAC7E4D,EAAE,CAACH,wBAAwB,GAAG,KAAK;MACnC;KACH,MAAK,IAAIyC,QAAQ,CAACmG,SAAS,IAAIvN,SAAS,CAACsK,MAAM,CAACsD,cAAc,EAAE;MAC7D9I,EAAE,CAAC8D,oBAAoB,CAAC8E,KAAK,CAAC5I,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC,CAAC;MACpF4D,EAAE,CAACH,wBAAwB,GAAG,KAAK;MACnC;;IACF,IAAIyC,QAAQ,CAACmG,SAAS,IAAIvN,SAAS,CAACsK,MAAM,CAACuD,mBAAmB,EAAE;MAC9D/I,EAAE,CAAC8D,oBAAoB,CAAC8E,KAAK,CAAC5I,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC;MACnF4D,EAAE,CAACH,wBAAwB,GAAG,KAAK;MACnC;KACH,MAAK;MACF,IAAImJ,IAAI,GAAG,EAAE;MACb1G,QAAQ,CAAC2G,SAAS,CAACxB,OAAO,CAACvF,IAAI,IAAG;QAC9B8G,IAAI,CAACE,IAAI,CAAC;UACN9L,IAAI,EAAE8E,IAAI,CAAC9E,IAAI;UACfM,MAAM,EAAEwE,IAAI,CAACxE,MAAM;UACnB;UACAH,UAAU,EAAE2E,IAAI,CAAC3E,UAAU;UAC3BS,WAAW,EAAEkE,IAAI,CAAClE,WAAW;UAC7BH,OAAO,EAAEqE,IAAI,CAACrE,OAAO;UACrB+K,KAAK,EAAE,IAAI,CAACO,eAAe,CAACjH,IAAI,CAACuG,SAAS;SAC7C,CAAC;MACN,CAAC,CAAC;MACF,IAAIO,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE;QACnBpJ,EAAE,CAAC8D,oBAAoB,CAACkC,OAAO,CAAChG,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF4D,EAAE,CAACH,wBAAwB,GAAG,KAAK;QACnC;;MAEJG,EAAE,CAACH,wBAAwB,GAAG,KAAK;MACnC,MAAMwJ,MAAM,GAAG,CAACrJ,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC,EACzD4D,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MAC/C;MACA4D,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,EACnD4D,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,EACnD4D,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,EAChD4D,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC,CAAC;MAClD,MAAMkN,EAAE,GAAmBlO,IAAI,CAACmO,KAAK,CAACC,aAAa,CAACR,IAAI,CAAC;MACzD5N,IAAI,CAACmO,KAAK,CAACE,aAAa,CAACH,EAAE,EAAE,CAACD,MAAM,CAAC,EAAE;QAAEK,MAAM,EAAE;MAAI,CAAE,CAAC;MACxD,MAAMC,YAAY,GAAG;QAAEC,CAAC,EAAE;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAEC,CAAC,EAAE;UAAED,GAAG,EAAE;QAAE,CAAE;QAAEE,CAAC,EAAE;UAAEF,GAAG,EAAE;QAAE,CAAE;QAAEG,CAAC,EAAE;UAAEH,GAAG,EAAE;QAAE,CAAE;QAAEI,CAAC,EAAE;UAAEJ,GAAG,EAAE;QAAE,CAAE;QAAEK,CAAC,EAAE;UAAEL,GAAG,EAAE;QAAE,CAAE;QAAEM,CAAC,EAAE;UAAEN,GAAG,EAAE;QAAE;MAAE,CAAE;MACvIP,EAAE,CAAC,OAAO,CAAC,GAAG/B,MAAM,CAACC,IAAI,CAACmC,YAAY,CAAC,CAACS,GAAG,CAACC,GAAG,KAAK;QAAE,GAAG;UAAEC,KAAK,EAAEX,YAAY,CAACU,GAAG,CAAC,CAACR;QAAG,CAAE;QAAE,GAAGF,YAAY,CAACU,GAAG;MAAC,CAAE,CAAC,CAAC;MACnH,MAAME,EAAE,GAAkBnP,IAAI,CAACmO,KAAK,CAACiB,QAAQ,EAAE;MAC/CpP,IAAI,CAACmO,KAAK,CAACkB,iBAAiB,CAACF,EAAE,EAAEjB,EAAE,EAAE,SAAS,CAAC;MAC/ClO,IAAI,CAACsP,SAAS,CAACH,EAAE,EAAE,YAAY,CAAC;MAChCvK,EAAE,CAAC8D,oBAAoB,CAAC6G,OAAO,CAAC3K,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;;EAEvG;EACAwO,gBAAgBA,CAAA;IACZ,IAAI,CAAC7L,aAAa,CAAC6L,gBAAgB,EAAE;EACzC;EACApO,YAAYA,CAAA;IACR,IAAIwD,EAAE,GAAG,IAAI;IACbA,EAAE,CAACH,wBAAwB,GAAG,IAAI;IAClCG,EAAE,CAAC6K,iBAAiB,CAACC,IAAI,CAAC5P,SAAS,CAAC6P,UAAU,CAACC,mBAAmB,EAAE,EAAE,CAAC;IACvEhL,EAAE,CAACuI,gBAAgB,GAAGC,SAAS;IAC/BxI,EAAE,CAACF,iBAAiB,GAAG,KAAK;EAChC;EACAqJ,eAAeA,CAACpG,IAAI;IAChB,IAAI/C,EAAE,GAAG,IAAI;IACb,IAAI+C,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACyF,eAAe,EAAE;MAC1C,OAAOjL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC7D,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAAC0F,eAAe,EAAE;MACjD,OAAOlL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACjE,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAAC2F,aAAa,EAAE;MAC/C,OAAOnL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;KAC9D,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAAC4F,aAAa,EAAE;MAC/C,OAAOpL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KAC/D,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACyF,eAAe,EAAE;MACjD,OAAOjL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;KAChE,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAAC6F,mBAAmB,EAAE;MACrD,OAAOrL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;KACnE,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAAC8F,qBAAqB,EAAE;MACvD,OAAOtL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;KACrE,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAAC+F,qBAAqB,EAAE;MACvD,OAAOvL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;KACrE,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACgG,aAAa,EAAE;MAC/C,OAAOxL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC7D,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACiG,eAAe,EAAE;MACjD,OAAOzL,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACjE,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACkG,QAAQ,EAAE;MAC1C,OAAO1L,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;KACzD,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACmG,eAAe,EAAE;MACjD,OAAO3L,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KAC/D,MAAM,IAAI2G,IAAI,IAAI7H,SAAS,CAACsK,MAAM,CAACoG,WAAW,EAAE;MAC7C,OAAO5L,EAAE,CAAC7D,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;;IAE7D,OAAO,EAAE;EACb;EACAyP,iBAAiBA,CAACC,UAAkB;IAChC;IACA,MAAMC,IAAI,GAAG,IAAI5H,IAAI,CAAC2H,UAAU,CAAC;IAEjC;IACA,MAAME,GAAG,GAAGD,IAAI,CAACE,OAAO,EAAE;IAC1B,MAAMC,KAAK,GAAGH,IAAI,CAACI,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGL,IAAI,CAACM,WAAW,EAAE;IAE/B;IACA,MAAMC,YAAY,GAAGN,GAAG,GAAG,EAAE,GAAG,IAAIA,GAAG,EAAE,GAAG,GAAGA,GAAG,EAAE;IACpD,MAAMO,cAAc,GAAGL,KAAK,GAAG,EAAE,GAAG,IAAIA,KAAK,EAAE,GAAG,GAAGA,KAAK,EAAE;IAE5D;IACA,OAAO,GAAGI,YAAY,IAAIC,cAAc,IAAIH,IAAI,EAAE;EACtD;EAEAlH,UAAUA,CAACE,QAAQ;IACf,IAAIpF,EAAE,GAAG,IAAI;IACb,IAAI,CAACjB,aAAa,CAACmG,UAAU,CAAC;MAACsH,GAAG,EAAEpH,QAAQ,CAACqH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAEC,IAAI,EAAEtH,QAAQ,CAACqH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAAC,CAAC,EAAGnK,QAAQ,IAAG;MAC/FtC,EAAE,CAAC2M,aAAa,GAAG;QACfC,GAAG,EAAEtK,QAAQ,CAACsK,GAAG;QACjBC,GAAG,EAAEvK,QAAQ,CAACuK;OACjB;MACD7M,EAAE,CAAC8M,WAAW,CAACxK,QAAQ,CAACsK,GAAG,EAAEtK,QAAQ,CAACuK,GAAG,CAAC;IAClD,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;EAClB;EAEAC,WAAWA,CAACF,GAAG,EAAEG,GAAG;IAChB,IAAI/M,EAAE,GAAG,IAAI;IACbA,EAAE,CAACjB,aAAa,CAAC+N,WAAW,CAACF,GAAG,EAAEG,GAAG,EAAGzK,QAAQ,IAAG;MAC/CtC,EAAE,CAAC7C,UAAU,CAACgB,QAAQ,GAAGmE,QAAQ,CAAC0K,YAAY;MAC9C,MAAM9L,GAAG,GAAG,iCAAiClB,EAAE,CAAC2M,aAAa,CAACC,GAAG,IAAI5M,EAAE,CAAC2M,aAAa,CAACE,GAAG,eAAe;MACxG7M,EAAE,CAACrB,OAAO,GAAGqB,EAAE,CAACf,SAAS,CAACkC,8BAA8B,CAACD,GAAG,CAAC;IACjE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;EAClB;EAEA8D,QAAQA,CAAA;IACJ;IACA,IAAIhF,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC1B,gBAAgB,GAAG0B,EAAE,CAACd,WAAW,CAACmC,KAAK,CAAC;MACvCjE,IAAI,EAAE,CAAC4C,EAAE,CAAC7C,UAAU,CAACC,IAAI,CAAC;MAC1Be,QAAQ,EAAE,CAAC6B,EAAE,CAAC7C,UAAU,CAACgB,QAAQ,CAAC;MAClCT,MAAM,EAAE,CAACsC,EAAE,CAAC7C,UAAU,CAACO,MAAM,CAAC;MAC9BG,OAAO,EAAE,CAACmC,EAAE,CAAC7C,UAAU,CAACU,OAAO,CAAC;MAChCG,WAAW,EAAE,CAACgC,EAAE,CAACmD,WAAW,CAACe,mBAAmB,CAAC,IAAIC,IAAI,CAACnE,EAAE,CAAC7C,UAAU,CAACa,WAAW,CAAC,CAAC,CAAC;MACtFT,UAAU,EAAE,CAACyC,EAAE,CAAC7C,UAAU,CAACI,UAAU,CAAC;MACtCoD,OAAO,EAAE,CAACX,EAAE,CAAC7C,UAAU,CAACwD,OAAO;KAClC,CAAC;IACF,IAAIX,EAAE,CAAC7C,UAAU,CAACwD,OAAO,KAAK,CAAC,EAAE;MAC7BX,EAAE,CAAC1B,gBAAgB,CAAC2O,QAAQ,CAAC,SAAS,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACzH,MAAM,CAACC,QAAQ,CAAC,CAAC,iBAAiB,IAAI,CAACjI,MAAM,EAAE,CAAC,CAAC;EAC1D;EAEA0P,aAAaA,CAACnJ,KAAK;IACf,IAAGA,KAAK,IAAI,CAAC,EAAC;MACV,OAAO,IAAI,CAAC9H,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAG6H,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MACzC;MACA,OAAO,IAAI,CAACnR,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAG6H,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACE,SAAS,EAAC;MAC7C,OAAO,IAAI,CAACpR,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAG6H,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACG,WAAW,EAAC;MAC/C,OAAO,IAAI,CAACrR,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAG6H,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACI,MAAM,EAAC;MAC1C,OAAO,IAAI,CAACtR,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;KACzD,MAAK,IAAG6H,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACK,SAAS,EAAC;MAC7C,OAAO,IAAI,CAACvR,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAG6H,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACE,SAAS,IAAItJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MAC9F,OAAO,IAAI,CAACnR,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;KACvE,MAAK,IAAG6H,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACE,SAAS,IAAItJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MAC9F,OAAO,IAAI,CAACnR,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;KACzE,MAAK,IAAG6H,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACE,SAAS,IAAItJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MAC9F,OAAO,IAAI,CAACnR,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;IAErE,OAAO,EAAE;EACb;EAEAuR,cAAcA,CAAC1J,KAAK;IAChB,IAAGA,KAAK,IAAI,CAAC,EAAC;MACV,OAAO,CAAC,KAAK,EAAG,cAAc,EAAE,YAAY,EAAE,YAAY,EAAC,cAAc,CAAC;KAC7E,MAAK,IAAGA,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MACzC;MACA,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;KACjF,MAAK,IAAGrJ,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACE,SAAS,EAAC;MAC7C,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;KACjF,MAAK,IAAGtJ,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACK,SAAS,EAAC;MAC7C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;KACpF,MAAK,IAAGzJ,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACG,WAAW,EAAC;MAC/C,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;KACpF,MAAK,IAAGvJ,KAAK,IAAI/I,SAAS,CAACmS,UAAU,CAACI,MAAM,EAAC;MAC1C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAC,cAAc,CAAC;KAC9E,MAAK,IAAGxJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACE,SAAS,IAAItJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;KAChF,MAAK,IAAGrJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACE,SAAS,IAAItJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MAC9F,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;KAChF,MAAK,IAAGrJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACE,SAAS,IAAItJ,KAAK,IAAI,EAAE,GAAG/I,SAAS,CAACmS,UAAU,CAACC,KAAK,EAAC;MAC9F,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;;IAErF,OAAO,EAAE;EACb;EAEAM,cAAcA,CAAC3J,KAAK;IAChB,IAAGA,KAAK,IAAI/I,SAAS,CAAC2S,YAAY,CAACC,OAAO,EAAE,OAAO,IAAI,CAAC3R,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,MACnG,IAAG6H,KAAK,IAAI/I,SAAS,CAAC2S,YAAY,CAACE,QAAQ,EAAE,OAAO,IAAI,CAAC5R,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,MAC1G,OAAO,EAAE;EAClB;;;uBA9kBSyC,mBAAmB,EAAAvD,EAAA,CAAA0S,iBAAA,CAEhB/S,aAAa,GAAAK,EAAA,CAAA0S,iBAAA,CACb3S,UAAU,GAAAC,EAAA,CAAA0S,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA5S,EAAA,CAAA0S,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9S,EAAA,CAAA0S,iBAAA,CAAA1S,EAAA,CAAA+S,QAAA;IAAA;EAAA;;;YAHbxP,mBAAmB;MAAAyP,SAAA;MAAAC,QAAA,GAAAjT,EAAA,CAAAkT,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBhCxT,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAoB,MAAA,GAAmD;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAC7FV,EAAA,CAAA8C,SAAA,sBAAoF;UACxF9C,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAwE;UACpED,EAAA,CAAA0T,UAAA,IAAAC,uCAAA,sBAAwM;UACxM3T,EAAA,CAAA0T,UAAA,IAAAE,uCAAA,sBAA+L;UACnM5T,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,cAA8F;UAA/DD,EAAA,CAAAE,UAAA,sBAAA2T,sDAAA;YAAA,OAAYJ,GAAA,CAAA3H,cAAA,EAAgB;UAAA,EAAC;UACxD9L,EAAA,CAAAC,cAAA,iBAAoF;UAQ7DD,EAAA,CAAAE,UAAA,2BAAA4T,6DAAApS,MAAA;YAAA,OAAA+R,GAAA,CAAAzO,UAAA,CAAAlD,IAAA,GAAAJ,MAAA;UAAA,EAA6B;UAHpC1B,EAAA,CAAAU,YAAA,EAKE;UACFV,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAoB,MAAA,IAA8C;UAAApB,EAAA,CAAAU,YAAA,EAAQ;UAGpFV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA6T,6DAAArS,MAAA;YAAA,OAAA+R,GAAA,CAAAzO,UAAA,CAAA5C,MAAA,GAAAV,MAAA;UAAA,EAA+B;UAFtC1B,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAAwB;UAAAD,EAAA,CAAAoB,MAAA,IAAmD;UAAApB,EAAA,CAAAU,YAAA,EAAQ;UAI3FV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA8T,6DAAAtS,MAAA;YAAA,OAAA+R,GAAA,CAAAzO,UAAA,CAAAzC,OAAA,GAAAb,MAAA;UAAA,EAAgC;UAFvC1B,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAoB,MAAA,IAAiD;UAAApB,EAAA,CAAAU,YAAA,EAAQ;UAI1FV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA+T,6DAAAvS,MAAA;YAAA,OAAA+R,GAAA,CAAAzO,UAAA,CAAA/C,UAAA,GAAAP,MAAA;UAAA,EAAmC;UAF1C1B,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAoB,MAAA,IAAoD;UAAApB,EAAA,CAAAU,YAAA,EAAQ;UAIhGV,EAAA,CAAAC,cAAA,eAAwB;UAIRD,EAAA,CAAAE,UAAA,2BAAAgU,kEAAAxS,MAAA;YAAA,OAAA+R,GAAA,CAAAzO,UAAA,CAAAE,gBAAA,GAAAxD,MAAA;UAAA,EAAyC,sBAAAyS,6DAAA;YAAA,OAM7BV,GAAA,CAAAhH,gBAAA,CAAAgH,GAAA,CAAAzO,UAAA,CAAAE,gBAAA,CAA6C;UAAA,EANhB,qBAAAkP,4DAAA;YAAA,OAO9BX,GAAA,CAAAhH,gBAAA,CAAAgH,GAAA,CAAAzO,UAAA,CAAAE,gBAAA,CAA6C;UAAA,EAPf;UAQhDlF,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAoB,MAAA,IAAoD;UAAApB,EAAA,CAAAU,YAAA,EAAQ;UAGtGV,EAAA,CAAAC,cAAA,eAAwB;UAIRD,EAAA,CAAAE,UAAA,2BAAAmU,kEAAA3S,MAAA;YAAA,OAAA+R,GAAA,CAAAzO,UAAA,CAAAG,cAAA,GAAAzD,MAAA;UAAA,EAAuC,sBAAA4S,6DAAA;YAAA,OAO3Bb,GAAA,CAAA/G,cAAA,CAAA+G,GAAA,CAAAzO,UAAA,CAAAG,cAAA,CAAyC;UAAA,EAPd,qBAAAoP,4DAAA;YAAA,OAQ5Bd,GAAA,CAAA/G,cAAA,CAAA+G,GAAA,CAAAzO,UAAA,CAAAG,cAAA,CAAyC;UAAA,EARb;UAF/CnF,EAAA,CAAAU,YAAA,EAWE;UACFV,EAAA,CAAAC,cAAA,iBAAgC;UAAAD,EAAA,CAAAoB,MAAA,IAAkD;UAAApB,EAAA,CAAAU,YAAA,EAAQ;UAGlGV,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA8C,SAAA,oBAGY;UAChB9C,EAAA,CAAAU,YAAA,EAAM;UAIlBV,EAAA,CAAAC,cAAA,eAA2D;UACiBD,EAAA,CAAAE,UAAA,2BAAAsU,gEAAA9S,MAAA;YAAA,OAAA+R,GAAA,CAAAlP,wBAAA,GAAA7C,MAAA;UAAA,EAAsC;UAC1G1B,EAAA,CAAAC,cAAA,eAA+B;UAESD,EAAA,CAAAE,UAAA,8BAAAuU,0EAAA/S,MAAA;YAAA,OAAA+R,GAAA,CAAAiB,UAAA,GAAAhT,MAAA;UAAA,EAA2B;UAE1D1B,EAAA,CAAAU,YAAA,EAAkB;UAEvBV,EAAA,CAAAC,cAAA,eAAwE;UAC+ED,EAAA,CAAAE,UAAA,mBAAAyU,wDAAA;YAAA,OAASlB,GAAA,CAAAnE,gBAAA,EAAkB;UAAA,EAAC;UAACtP,EAAA,CAAAU,YAAA,EAAW;UAGnMV,EAAA,CAAAC,cAAA,eAAkB;UAAsBD,EAAA,CAAA0T,UAAA,KAAAkB,qCAAA,oBAAoF;UAAA5U,EAAA,CAAAU,YAAA,EAAM;UAG1IV,EAAA,CAAAC,cAAA,eAAqD;UACgBD,EAAA,CAAAE,UAAA,2BAAA2U,gEAAAnT,MAAA;YAAA,OAAA+R,GAAA,CAAA3P,oBAAA,GAAApC,MAAA;UAAA,EAAkC;UAC/F1B,EAAA,CAAAC,cAAA,eAAuE;UAMiCD,EAAA,CAAAoB,MAAA,IAAgD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACvIV,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAoB,MAAA,IAAoB;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAEjDV,EAAA,CAAAC,cAAA,eAAuB;UAC6DD,EAAA,CAAAoB,MAAA,IAAmD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC1IV,EAAA,CAAAC,cAAA,gBAAqE;UAAAD,EAAA,CAAAoB,MAAA,IAAmC;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAEnHV,EAAA,CAAAC,cAAA,eAAuB;UAC6DD,EAAA,CAAAoB,MAAA,IAA2C;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAClIV,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAoB,MAAA,IAAkB;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE/CV,EAAA,CAAAC,cAAA,eAAuB;UAC6DD,EAAA,CAAAoB,MAAA,IAAiD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACxIV,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAoB,MAAA,IAAkB;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE/CV,EAAA,CAAAC,cAAA,eAAuB;UAC6DD,EAAA,CAAAoB,MAAA,IAA4C;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACnIV,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAoB,MAAA,IAAmB;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAEhDV,EAAA,CAAAC,cAAA,eAAuB;UAC6DD,EAAA,CAAAoB,MAAA,IAAsD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC7IV,EAAA,CAAA0T,UAAA,KAAAoB,oCAAA,mBAA+M;UAC/M9U,EAAA,CAAA0T,UAAA,KAAAqB,oCAAA,mBAAwH;UACxH/U,EAAA,CAAA0T,UAAA,KAAAsB,oCAAA,mBAA2K;UAC/KhV,EAAA,CAAAU,YAAA,EAAM;UAEVV,EAAA,CAAAC,cAAA,eAAiB;UAEuED,EAAA,CAAAoB,MAAA,IAAgD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACvIV,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAoB,MAAA,IAA2C;;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAExEV,EAAA,CAAAC,cAAA,eAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAkD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACzIV,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAoB,MAAA,KAAyC;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAK1FV,EAAA,CAAAC,cAAA,mBAAgG;UAGpED,EAAA,CAAAE,UAAA,2BAAA+U,uEAAAvT,MAAA;YAAA,OAAA+R,GAAA,CAAAxP,eAAA,CAAAqB,UAAA,GAAA5D,MAAA;UAAA,EAAwC;UAA+C1B,EAAA,CAAAU,YAAA,EAAiB;UACxHV,EAAA,CAAAC,cAAA,YAAK;UAAAD,EAAA,CAAAoB,MAAA,KAAoD;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAEnEV,EAAA,CAAAC,cAAA,gBAA+B;UACXD,EAAA,CAAAE,UAAA,2BAAAgV,uEAAAxT,MAAA;YAAA,OAAA+R,GAAA,CAAAxP,eAAA,CAAAsB,iBAAA,GAAA7D,MAAA;UAAA,EAA+C;UAA+C1B,EAAA,CAAAU,YAAA,EAAiB;UAC/HV,EAAA,CAAAC,cAAA,YAAK;UAAAD,EAAA,CAAAoB,MAAA,KAA4D;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAE3EV,EAAA,CAAAC,cAAA,gBAA+B;UACXD,EAAA,CAAAE,UAAA,2BAAAiV,uEAAAzT,MAAA;YAAA,OAAA+R,GAAA,CAAAxP,eAAA,CAAAuB,cAAA,GAAA9D,MAAA;UAAA,EAA4C;UAA+C1B,EAAA,CAAAU,YAAA,EAAiB;UAC5HV,EAAA,CAAAC,cAAA,YAAK;UAAAD,EAAA,CAAAoB,MAAA,KAAwD;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAG3EV,EAAA,CAAAC,cAAA,gBAAkB;UAEMD,EAAA,CAAAE,UAAA,2BAAAkV,uEAAA1T,MAAA;YAAA,OAAA+R,GAAA,CAAAxP,eAAA,CAAAwB,eAAA,GAAA/D,MAAA;UAAA,EAA6C;UAA+C1B,EAAA,CAAAU,YAAA,EAAiB;UAC7HV,EAAA,CAAAC,cAAA,YAAK;UAAAD,EAAA,CAAAoB,MAAA,KAAyD;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAExEV,EAAA,CAAAC,cAAA,gBAA+B;UACXD,EAAA,CAAAE,UAAA,2BAAAmV,uEAAA3T,MAAA;YAAA,OAAA+R,GAAA,CAAAxP,eAAA,CAAAyB,gBAAA,GAAAhE,MAAA;UAAA,EAA8C;UAA+C1B,EAAA,CAAAU,YAAA,EAAiB;UAC9HV,EAAA,CAAAC,cAAA,YAAK;UAAAD,EAAA,CAAAoB,MAAA,KAA2D;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAE1EV,EAAA,CAAAC,cAAA,gBAA+B;UACXD,EAAA,CAAAE,UAAA,2BAAAoV,uEAAA5T,MAAA;YAAA,OAAA+R,GAAA,CAAAxP,eAAA,CAAA0B,aAAA,GAAAjE,MAAA;UAAA,EAA2C;UAA+C1B,EAAA,CAAAU,YAAA,EAAiB;UAC3HV,EAAA,CAAAC,cAAA,YAAK;UAAAD,EAAA,CAAAoB,MAAA,KAAuD;UAAApB,EAAA,CAAAU,YAAA,EAAM;UAK9EV,EAAA,CAAAC,cAAA,mBAAsF;UAEED,EAAA,CAAAoB,MAAA,KAAiD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACxIV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAA4B;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAEzDV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAqD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC5IV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAAqI;;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAI1KV,EAAA,CAAAC,cAAA,gBAAiC;UAI2DD,EAAA,CAAAoB,MAAA,KAAgD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACvIV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAA+B;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE5DV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAqD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC5IV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAAmD;;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAEhFV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAsD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC7IV,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAoB,MAAA,KAAiC;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAExEV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAiD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACxIV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAA6B;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE1DV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAsD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC7IV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAA+B;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE5DV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAmD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC1IV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAAiC;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE9DV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAkD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACzIV,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAoB,MAAA,KAA8B;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAErEV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAqD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC5IV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAAiC;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAE9DV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAgD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACvIV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAA4B;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAI7DV,EAAA,CAAAC,cAAA,mBAAoF;UAEID,EAAA,CAAAoB,MAAA,KAAgD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UACvIV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAAuB;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAEpDV,EAAA,CAAAC,cAAA,gBAAuB;UAC6DD,EAAA,CAAAoB,MAAA,KAAmD;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAC1IV,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAoB,MAAA,KAAuB;UAAApB,EAAA,CAAAU,YAAA,EAAO;UAQxEV,EAAA,CAAAC,cAAA,gBAAqD;UACsBD,EAAA,CAAAE,UAAA,2BAAAqV,iEAAA7T,MAAA;YAAA,OAAA+R,GAAA,CAAA1P,uBAAA,GAAArC,MAAA;UAAA,EAAqC;UACxG1B,EAAA,CAAAC,cAAA,mBAA0B;UACtBD,EAAA,CAAA0T,UAAA,MAAA8B,qCAAA,qBA+FO;UACXxV,EAAA,CAAAU,YAAA,EAAS;UAGjBV,EAAA,CAAAC,cAAA,uBAYC;UAVGD,EAAA,CAAAE,UAAA,+BAAAuV,uEAAA/T,MAAA;YAAA,OAAA+R,GAAA,CAAA3K,WAAA,GAAApH,MAAA;UAAA,EAA6B;UAUhC1B,EAAA,CAAAU,YAAA,EAAa;;;UApX8BV,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,2BAAmD;UAChDd,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAW,UAAA,UAAA8S,GAAA,CAAA3O,KAAA,CAAe,SAAA2O,GAAA,CAAA9O,IAAA;UAG6E3E,EAAA,CAAAqB,SAAA,GAAwD;UAAxDrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAAzJ,WAAA,CAAAhK,EAAA,CAAA0V,eAAA,MAAAC,GAAA,EAAAlC,GAAA,CAAA7T,SAAA,CAAAqK,WAAA,CAAAC,MAAA,CAAA0L,MAAA,GAAwD;UACjE5V,EAAA,CAAAqB,SAAA,GAAwD;UAAxDrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAAzJ,WAAA,CAAAhK,EAAA,CAAA0V,eAAA,MAAAC,GAAA,EAAAlC,GAAA,CAAA7T,SAAA,CAAAqK,WAAA,CAAAC,MAAA,CAAA0L,MAAA,GAAwD;UAIpL5V,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAAW,UAAA,cAAA8S,GAAA,CAAA3N,UAAA,CAAwB;UACjB9F,EAAA,CAAAqB,SAAA,GAAmB;UAAnBrB,EAAA,CAAAW,UAAA,oBAAmB,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA;UAQLd,EAAA,CAAAqB,SAAA,GAA6B;UAA7BrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAzO,UAAA,CAAAlD,IAAA,CAA6B;UAGd9B,EAAA,CAAAqB,SAAA,GAA8C;UAA9CrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,sBAA8C;UAO7Dd,EAAA,CAAAqB,SAAA,GAA+B;UAA/BrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAzO,UAAA,CAAA5C,MAAA,CAA+B;UAGdpC,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,2BAAmD;UAQpEd,EAAA,CAAAqB,SAAA,GAAgC;UAAhCrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAzO,UAAA,CAAAzC,OAAA,CAAgC;UAGdvC,EAAA,CAAAqB,SAAA,GAAiD;UAAjDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,yBAAiD;UAQnEd,EAAA,CAAAqB,SAAA,GAAmC;UAAnCrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAzO,UAAA,CAAA/C,UAAA,CAAmC;UAGdjC,EAAA,CAAAqB,SAAA,GAAoD;UAApDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,4BAAoD;UAQxEd,EAAA,CAAAqB,SAAA,GAAyC;UAAzCrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAzO,UAAA,CAAAE,gBAAA,CAAyC,iDAAAuO,GAAA,CAAAnP,WAAA;UASftE,EAAA,CAAAqB,SAAA,GAAoD;UAApDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,4BAAoD;UAO9Ed,EAAA,CAAAqB,SAAA,GAAuC;UAAvCrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAzO,UAAA,CAAAG,cAAA,CAAuC,iDAAAsO,GAAA,CAAAxQ,SAAA,aAAAwQ,GAAA,CAAAvQ,SAAA;UAUflD,EAAA,CAAAqB,SAAA,GAAkD;UAAlDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,0BAAkD;UAa4Bd,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAA6V,UAAA,CAAA7V,EAAA,CAAAmD,eAAA,MAAA2S,GAAA,EAA4B;UAAhJ9V,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,8BAA6D,YAAA2S,GAAA,CAAAlP,wBAAA;UAG3BvE,EAAA,CAAAqB,SAAA,GAA2B;UAA3BrB,EAAA,CAAAW,UAAA,eAAA8S,GAAA,CAAAiB,UAAA,CAA2B,sBAAAjB,GAAA,CAAA9G,iBAAA,CAAAf,IAAA,CAAA6H,GAAA,cAAAA,GAAA,CAAArI,eAAA;UAK3BpL,EAAA,CAAAqB,SAAA,GAAgE;UAAhErB,EAAA,CAAAW,UAAA,aAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,+BAAgE;UAGnCd,EAAA,CAAAqB,SAAA,GAAuB;UAAvBrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAAjP,iBAAA,CAAuB;UAImBxE,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAA6V,UAAA,CAAA7V,EAAA,CAAAmD,eAAA,MAAA4S,GAAA,EAA4B;UAArI/V,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,uBAAsD,YAAA2S,GAAA,CAAA3P,oBAAA;UAG5C9D,EAAA,CAAAqB,SAAA,GAAoD;UAApDrB,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,qBAAoD;UAIoCd,EAAA,CAAAqB,SAAA,GAAgD;UAAhDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,wBAAgD;UAC9Gd,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAzP,SAAA,CAAA5B,MAAA,CAAoB;UAG0CpC,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,2BAAmD;UACzGd,EAAA,CAAAqB,SAAA,GAA0C;UAA1CrB,EAAA,CAAAgW,UAAA,CAAAvC,GAAA,CAAApB,cAAA,CAAAoB,GAAA,CAAAzP,SAAA,CAAAiS,MAAA,EAA0C;UAACjW,EAAA,CAAAqB,SAAA,GAAmC;UAAnCrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA3B,aAAA,CAAA2B,GAAA,CAAAzP,SAAA,CAAAiS,MAAA,EAAmC;UAGxBjW,EAAA,CAAAqB,SAAA,GAA2C;UAA3CrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,mBAA2C;UACzGd,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAzP,SAAA,CAAAiB,IAAA,CAAkB;UAG4CjF,EAAA,CAAAqB,SAAA,GAAiD;UAAjDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,yBAAiD;UAC/Gd,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAzP,SAAA,CAAAlC,IAAA,CAAkB;UAG4C9B,EAAA,CAAAqB,SAAA,GAA4C;UAA5CrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,oBAA4C;UAC1Gd,EAAA,CAAAqB,SAAA,GAAmB;UAAnBrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAzP,SAAA,CAAAkS,KAAA,CAAmB;UAG2ClW,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,8BAAsD;UAC/Hd,EAAA,CAAAqB,SAAA,GAAqH;UAArHrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAAzP,SAAA,CAAAsE,gBAAA,KAAA4E,SAAA,IAAAuG,GAAA,CAAAzP,SAAA,CAAAsE,gBAAA,aAAAmL,GAAA,CAAAzP,SAAA,CAAAsE,gBAAA,SAAqH;UACrHtI,EAAA,CAAAqB,SAAA,GAAsC;UAAtCrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAAzP,SAAA,CAAAsE,gBAAA,SAAsC;UACtCtI,EAAA,CAAAqB,SAAA,GAAkF;UAAlFrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAAzP,SAAA,CAAAsE,gBAAA,KAAA4E,SAAA,IAAAuG,GAAA,CAAAzP,SAAA,CAAAsE,gBAAA,UAAkF;UAKTtI,EAAA,CAAAqB,SAAA,GAAgD;UAAhDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,wBAAgD;UAC9Gd,EAAA,CAAAqB,SAAA,GAA2C;UAA3CrB,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAAmW,WAAA,UAAA1C,GAAA,CAAAzP,SAAA,CAAAoS,SAAA,gBAA2C;UAGmBpW,EAAA,CAAAqB,SAAA,GAAkD;UAAlDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,0BAAkD;UACxGd,EAAA,CAAAqB,SAAA,GAAyC;UAAzCrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAnB,cAAA,CAAAmB,GAAA,CAAAzP,SAAA,CAAAqS,WAAA,EAAyC;UAK3ErW,EAAA,CAAAqB,SAAA,GAA0D;UAA1DrB,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,2BAA0D;UAGtCd,EAAA,CAAAqB,SAAA,GAAwC;UAAxCrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAxP,eAAA,CAAAqB,UAAA,CAAwC;UACnDtF,EAAA,CAAAqB,SAAA,GAAoD;UAApDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,4BAAoD;UAGzCd,EAAA,CAAAqB,SAAA,GAA+C;UAA/CrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAxP,eAAA,CAAAsB,iBAAA,CAA+C;UAC1DvF,EAAA,CAAAqB,SAAA,GAA4D;UAA5DrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,oCAA4D;UAGjDd,EAAA,CAAAqB,SAAA,GAA4C;UAA5CrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAxP,eAAA,CAAAuB,cAAA,CAA4C;UACvDxF,EAAA,CAAAqB,SAAA,GAAwD;UAAxDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,gCAAwD;UAK7Cd,EAAA,CAAAqB,SAAA,GAA6C;UAA7CrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAxP,eAAA,CAAAwB,eAAA,CAA6C;UACxDzF,EAAA,CAAAqB,SAAA,GAAyD;UAAzDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,iCAAyD;UAG9Cd,EAAA,CAAAqB,SAAA,GAA8C;UAA9CrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAxP,eAAA,CAAAyB,gBAAA,CAA8C;UACzD1F,EAAA,CAAAqB,SAAA,GAA2D;UAA3DrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,mCAA2D;UAGhDd,EAAA,CAAAqB,SAAA,GAA2C;UAA3CrB,EAAA,CAAAW,UAAA,YAAA8S,GAAA,CAAAxP,eAAA,CAAA0B,aAAA,CAA2C;UACtD3F,EAAA,CAAAqB,SAAA,GAAuD;UAAvDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,+BAAuD;UAKhEd,EAAA,CAAAqB,SAAA,GAA2D;UAA3DrB,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,4BAA2D;UAEqBd,EAAA,CAAAqB,SAAA,GAAiD;UAAjDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,yBAAiD;UAC/Gd,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAzP,SAAA,CAAAsS,cAAA,CAA4B;UAGkCtW,EAAA,CAAAqB,SAAA,GAAqD;UAArDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,6BAAqD;UACnHd,EAAA,CAAAqB,SAAA,GAAqI;UAArIrB,EAAA,CAAAuW,kBAAA,KAAAvW,EAAA,CAAAwW,WAAA,WAAA/C,GAAA,CAAA5L,WAAA,CAAA4O,gBAAA,CAAAhD,GAAA,CAAAtP,gBAAA,CAAAuS,cAAA,SAAAjD,GAAA,CAAAtP,gBAAA,CAAAoH,IAAA,GAAAkI,GAAA,CAAAtP,gBAAA,CAAAoH,IAAA,YAAqI;UAMvJvL,EAAA,CAAAqB,SAAA,GAAyD;UAAzDrB,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,0BAAyD;UAEuBd,EAAA,CAAAqB,SAAA,GAAgD;UAAhDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,wBAAgD;UAC9Gd,EAAA,CAAAqB,SAAA,GAA+B;UAA/BrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAA2D,YAAA,CAA+B;UAG+B/H,EAAA,CAAAqB,SAAA,GAAqD;UAArDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,6BAAqD;UACnHd,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAAmW,WAAA,WAAA1C,GAAA,CAAArP,cAAA,CAAAuS,YAAA,gBAAmD;UAGW3W,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,8BAAsD;UAC1Gd,EAAA,CAAAqB,SAAA,GAAiC;UAAjCrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAAwS,cAAA,CAAiC;UAGmB5W,EAAA,CAAAqB,SAAA,GAAiD;UAAjDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,yBAAiD;UAC/Gd,EAAA,CAAAqB,SAAA,GAA6B;UAA7BrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAAyS,UAAA,CAA6B;UAGiC7W,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,8BAAsD;UACpHd,EAAA,CAAAqB,SAAA,GAA+B;UAA/BrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAA0S,YAAA,CAA+B;UAG+B9W,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,2BAAmD;UACjHd,EAAA,CAAAqB,SAAA,GAAiC;UAAjCrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAA2S,cAAA,CAAiC;UAG6B/W,EAAA,CAAAqB,SAAA,GAAkD;UAAlDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,0BAAkD;UACtGd,EAAA,CAAAqB,SAAA,GAA8B;UAA9BrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAA4S,WAAA,CAA8B;UAGsBhX,EAAA,CAAAqB,SAAA,GAAqD;UAArDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,6BAAqD;UACnHd,EAAA,CAAAqB,SAAA,GAAiC;UAAjCrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAA6S,cAAA,CAAiC;UAG6BjX,EAAA,CAAAqB,SAAA,GAAgD;UAAhDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,wBAAgD;UAC9Gd,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAArP,cAAA,CAAA8S,SAAA,CAA4B;UAI9ClX,EAAA,CAAAqB,SAAA,GAAyD;UAAzDrB,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,0BAAyD;UAEuBd,EAAA,CAAAqB,SAAA,GAAgD;UAAhDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,wBAAgD;UAC9Gd,EAAA,CAAAqB,SAAA,GAAuB;UAAvBrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAvP,cAAA,CAAA+B,IAAA,CAAuB;UAGuCjG,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAA5S,WAAA,CAAAC,SAAA,2BAAmD;UACjHd,EAAA,CAAAqB,SAAA,GAAuB;UAAvBrB,EAAA,CAAAsB,iBAAA,CAAAmS,GAAA,CAAAvP,cAAA,CAAAuD,IAAA,CAAuB;UAS+DzH,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAA6V,UAAA,CAAA7V,EAAA,CAAAmD,eAAA,MAAA4S,GAAA,EAA4B;UAA9I/V,EAAA,CAAAW,UAAA,WAAA8S,GAAA,CAAA5S,WAAA,CAAAC,SAAA,6BAA4D,YAAA2S,GAAA,CAAA1P,uBAAA;UAExB/D,EAAA,CAAAqB,SAAA,GAA6B;UAA7BrB,EAAA,CAAAW,UAAA,SAAA8S,GAAA,CAAA1P,uBAAA,CAA6B;UAoG3E/D,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAAW,UAAA,qBAAoB,gBAAA8S,GAAA,CAAA3K,WAAA,aAAA2K,GAAA,CAAAzN,OAAA,aAAAyN,GAAA,CAAAxI,OAAA,aAAAwI,GAAA,CAAA1K,WAAA,cAAA0K,GAAA,CAAA9I,MAAA,CAAAiB,IAAA,CAAA6H,GAAA,iBAAAA,GAAA,CAAA7I,UAAA,cAAA6I,GAAA,CAAA5I,QAAA,UAAA4I,GAAA,CAAA3I,IAAA,YAAA2I,GAAA,CAAAzO,UAAA,gBAAAyO,GAAA,CAAA5S,WAAA,CAAAC,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}