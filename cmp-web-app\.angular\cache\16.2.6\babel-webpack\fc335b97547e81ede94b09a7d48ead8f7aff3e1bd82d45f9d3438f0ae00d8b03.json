{"ast": null, "code": "import { AccountService } from \"src/app/service/account/AccountService\";\nimport { CommonModule } from \"@angular/common\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { FieldsetModule } from \"primeng/fieldset\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { InputTextModule } from \"primeng/inputtext\";\nimport { ButtonModule } from \"primeng/button\";\nimport { CommonVnptModule } from \"../common-module/common.module\";\nimport { SplitButtonModule } from \"primeng/splitbutton\";\nimport { AutoCompleteModule } from \"primeng/autocomplete\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { CardModule } from \"primeng/card\";\nimport { PanelModule } from \"primeng/panel\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { ListTicketConfigComponent } from \"./list-config/app.config.list.component\";\nimport { AppTicketRouting } from \"./app.ticket-routing\";\nimport { TicketService } from \"../../service/ticket/TicketService\";\nimport { UpdateTicketConfigComponent } from \"./update-config/app.config.update.component\";\nimport { ListReplaceSimTicketComponent } from \"./list/app.list.replace-sim.component\";\nimport { ListTestSimTicketComponent } from \"./list/app.list.test-sim.component\";\nimport { ChipsModule } from 'primeng/chips';\nimport { ListOrderSimTicketComponent } from \"./list/order-sim/app.list.order-sim.component\";\nimport { InputNumberModule } from \"primeng/inputnumber\";\nimport { ListActiveSimTicketComponent } from \"./list/active-sim/app.list.active-sim.component\";\nimport { ListSimIssuedComponent } from \"./list/order-sim/sim-ticket/app.list.sim-issued.component\";\nimport { SimTicketService } from \"../../service/ticket/SimTicketService\";\nimport { TableModule } from \"primeng/table\";\nimport { LogHandleTicketService } from \"../../service/ticket/LogHandleTicketService\";\nimport { ListDiagnoseTicketComponent } from \"./list/diagnose/app.list.diagnose.component\";\nimport * as i0 from \"@angular/core\";\nexport class AppTicketModule {\n  static {\n    this.ɵfac = function AppTicketModule_Factory(t) {\n      return new (t || AppTicketModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppTicketModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [TicketService, AccountService, SimTicketService, LogHandleTicketService],\n      imports: [AppTicketRouting, CommonModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, AutoCompleteModule, CalendarModule, DropdownModule, CardModule, DialogModule, InputTextareaModule, MultiSelectModule, PanelModule, ChipsModule, InputNumberModule, TableModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppTicketModule, {\n    declarations: [ListTicketConfigComponent, UpdateTicketConfigComponent, ListReplaceSimTicketComponent, ListTestSimTicketComponent, ListOrderSimTicketComponent, ListActiveSimTicketComponent, ListSimIssuedComponent, ListDiagnoseTicketComponent],\n    imports: [AppTicketRouting, CommonModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, AutoCompleteModule, CalendarModule, DropdownModule, CardModule, DialogModule, InputTextareaModule, MultiSelectModule, PanelModule, ChipsModule, InputNumberModule, TableModule]\n  });\n})();", "map": {"version": 3, "names": ["AccountService", "CommonModule", "BreadcrumbModule", "FieldsetModule", "FormsModule", "ReactiveFormsModule", "InputTextModule", "ButtonModule", "CommonVnptModule", "SplitButtonModule", "AutoCompleteModule", "CalendarModule", "DropdownModule", "CardModule", "PanelModule", "DialogModule", "InputTextareaModule", "MultiSelectModule", "ListTicketConfigComponent", "AppTicketRouting", "TicketService", "UpdateTicketConfigComponent", "ListReplaceSimTicketComponent", "ListTestSimTicketComponent", "ChipsModule", "ListOrderSimTicketComponent", "InputNumberModule", "ListActiveSimTicketComponent", "ListSimIssuedComponent", "SimTicketService", "TableModule", "LogHandleTicketService", "ListDiagnoseTicketComponent", "AppTicketModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\app.ticket.module.ts"], "sourcesContent": ["import {NgModule} from \"@angular/core\";\r\nimport {AccountService} from \"src/app/service/account/AccountService\";\r\nimport {CommonModule} from \"@angular/common\";\r\nimport {BreadcrumbModule} from \"primeng/breadcrumb\";\r\nimport {FieldsetModule} from \"primeng/fieldset\";\r\nimport {FormsModule, ReactiveFormsModule} from \"@angular/forms\";\r\nimport {InputTextModule} from \"primeng/inputtext\";\r\nimport {ButtonModule} from \"primeng/button\";\r\nimport {CommonVnptModule} from \"../common-module/common.module\";\r\nimport {SplitButtonModule} from \"primeng/splitbutton\";\r\nimport {AutoCompleteModule} from \"primeng/autocomplete\";\r\nimport {CalendarModule} from \"primeng/calendar\";\r\nimport {DropdownModule} from \"primeng/dropdown\";\r\nimport {CardModule} from \"primeng/card\";\r\nimport {PanelModule} from \"primeng/panel\";\r\nimport {DialogModule} from \"primeng/dialog\";\r\nimport {InputTextareaModule} from 'primeng/inputtextarea';\r\nimport {MultiSelectModule} from 'primeng/multiselect';\r\nimport {ListTicketConfigComponent} from \"./list-config/app.config.list.component\";\r\nimport {AppTicketRouting} from \"./app.ticket-routing\";\r\nimport {TicketService} from \"../../service/ticket/TicketService\";\r\nimport {UpdateTicketConfigComponent} from \"./update-config/app.config.update.component\";\r\nimport {ListReplaceSimTicketComponent} from \"./list/app.list.replace-sim.component\";\r\nimport {ListTestSimTicketComponent} from \"./list/app.list.test-sim.component\";\r\nimport { ChipsModule } from 'primeng/chips';\r\nimport {ListOrderSimTicketComponent} from \"./list/order-sim/app.list.order-sim.component\";\r\nimport {InputNumberModule} from \"primeng/inputnumber\";\r\nimport {ListActiveSimTicketComponent} from \"./list/active-sim/app.list.active-sim.component\";\r\nimport {ListSimIssuedComponent} from \"./list/order-sim/sim-ticket/app.list.sim-issued.component\";\r\nimport {SimTicketService} from \"../../service/ticket/SimTicketService\";\r\nimport {TableModule} from \"primeng/table\";\r\nimport {LogHandleTicketService} from \"../../service/ticket/LogHandleTicketService\";\r\nimport {ListDiagnoseTicketComponent} from \"./list/diagnose/app.list.diagnose.component\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        AppTicketRouting,\r\n        CommonModule,\r\n        BreadcrumbModule,\r\n        FieldsetModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        InputTextModule,\r\n        ButtonModule,\r\n        CommonVnptModule,\r\n        SplitButtonModule,\r\n        AutoCompleteModule,\r\n        CalendarModule,\r\n        DropdownModule,\r\n        CardModule,\r\n        DialogModule,\r\n        InputTextareaModule,\r\n        MultiSelectModule,\r\n        PanelModule,\r\n        ChipsModule,\r\n        InputNumberModule,\r\n        TableModule,\r\n    ],\r\n  declarations: [\r\n    ListTicketConfigComponent,\r\n    UpdateTicketConfigComponent,\r\n    ListReplaceSimTicketComponent,\r\n    ListTestSimTicketComponent,\r\n    ListOrderSimTicketComponent,\r\n    ListActiveSimTicketComponent,\r\n    ListSimIssuedComponent,\r\n    ListDiagnoseTicketComponent,\r\n  ],\r\n  exports: [],\r\n  providers: [\r\n    TicketService,\r\n    AccountService,\r\n    SimTicketService,\r\n    LogHandleTicketService,\r\n  ]\r\n})\r\nexport class AppTicketModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,cAAc,QAAO,wCAAwC;AACrE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,gBAAgB,QAAO,oBAAoB;AACnD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAC/D,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,gBAAgB,QAAO,gCAAgC;AAC/D,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,kBAAkB,QAAO,sBAAsB;AACvD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,UAAU,QAAO,cAAc;AACvC,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,mBAAmB,QAAO,uBAAuB;AACzD,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,yBAAyB,QAAO,yCAAyC;AACjF,SAAQC,gBAAgB,QAAO,sBAAsB;AACrD,SAAQC,aAAa,QAAO,oCAAoC;AAChE,SAAQC,2BAA2B,QAAO,6CAA6C;AACvF,SAAQC,6BAA6B,QAAO,uCAAuC;AACnF,SAAQC,0BAA0B,QAAO,oCAAoC;AAC7E,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAAQC,2BAA2B,QAAO,+CAA+C;AACzF,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,4BAA4B,QAAO,iDAAiD;AAC5F,SAAQC,sBAAsB,QAAO,2DAA2D;AAChG,SAAQC,gBAAgB,QAAO,uCAAuC;AACtE,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,sBAAsB,QAAO,6CAA6C;AAClF,SAAQC,2BAA2B,QAAO,6CAA6C;;AA4CvF,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBAPf,CACTb,aAAa,EACbpB,cAAc,EACd6B,gBAAgB,EAChBE,sBAAsB,CACvB;MAAAG,OAAA,GAtCKf,gBAAgB,EAChBlB,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVE,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBH,WAAW,EACXU,WAAW,EACXE,iBAAiB,EACjBI,WAAW;IAAA;EAAA;;;2EAoBNG,eAAe;IAAAE,YAAA,GAjBxBjB,yBAAyB,EACzBG,2BAA2B,EAC3BC,6BAA6B,EAC7BC,0BAA0B,EAC1BE,2BAA2B,EAC3BE,4BAA4B,EAC5BC,sBAAsB,EACtBI,2BAA2B;IAAAE,OAAA,GA9BvBf,gBAAgB,EAChBlB,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,UAAU,EACVE,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBH,WAAW,EACXU,WAAW,EACXE,iBAAiB,EACjBI,WAAW;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}