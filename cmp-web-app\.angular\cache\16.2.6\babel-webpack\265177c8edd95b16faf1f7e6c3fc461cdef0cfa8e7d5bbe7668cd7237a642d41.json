{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Chinese (Taiwan) [zh-tw]\n//! author : Ben : https://github.com/ben-lin\n//! author : <PERSON> : https://github.com/hehachris\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var zhTw = moment.defineLocale('zh-tw', {\n    months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),\n    monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n    weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),\n    weekdaysShort: '週日_週一_週二_週三_週四_週五_週六'.split('_'),\n    weekdaysMin: '日_一_二_三_四_五_六'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY/MM/DD',\n      LL: 'YYYY年M月D日',\n      LLL: 'YYYY年M月D日 HH:mm',\n      LLLL: 'YYYY年M月D日dddd HH:mm',\n      l: 'YYYY/M/D',\n      ll: 'YYYY年M月D日',\n      lll: 'YYYY年M月D日 HH:mm',\n      llll: 'YYYY年M月D日dddd HH:mm'\n    },\n    meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === '凌晨' || meridiem === '早上' || meridiem === '上午') {\n        return hour;\n      } else if (meridiem === '中午') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === '下午' || meridiem === '晚上') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      var hm = hour * 100 + minute;\n      if (hm < 600) {\n        return '凌晨';\n      } else if (hm < 900) {\n        return '早上';\n      } else if (hm < 1130) {\n        return '上午';\n      } else if (hm < 1230) {\n        return '中午';\n      } else if (hm < 1800) {\n        return '下午';\n      } else {\n        return '晚上';\n      }\n    },\n    calendar: {\n      sameDay: '[今天] LT',\n      nextDay: '[明天] LT',\n      nextWeek: '[下]dddd LT',\n      lastDay: '[昨天] LT',\n      lastWeek: '[上]dddd LT',\n      sameElse: 'L'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(日|月|週)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '日';\n        case 'M':\n          return number + '月';\n        case 'w':\n        case 'W':\n          return number + '週';\n        default:\n          return number;\n      }\n    },\n    relativeTime: {\n      future: '%s後',\n      past: '%s前',\n      s: '幾秒',\n      ss: '%d 秒',\n      m: '1 分鐘',\n      mm: '%d 分鐘',\n      h: '1 小時',\n      hh: '%d 小時',\n      d: '1 天',\n      dd: '%d 天',\n      M: '1 個月',\n      MM: '%d 個月',\n      y: '1 年',\n      yy: '%d 年'\n    }\n  });\n  return zhTw;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "zhTw", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "hm", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/moment/locale/zh-tw.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Chinese (Taiwan) [zh-tw]\n//! author : Ben : https://github.com/ben-lin\n//! author : <PERSON> : https://github.com/hehachris\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var zhTw = moment.defineLocale('zh-tw', {\n        months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split(\n            '_'\n        ),\n        monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split(\n            '_'\n        ),\n        weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),\n        weekdaysShort: '週日_週一_週二_週三_週四_週五_週六'.split('_'),\n        weekdaysMin: '日_一_二_三_四_五_六'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY/MM/DD',\n            LL: 'YYYY年M月D日',\n            LLL: 'YYYY年M月D日 HH:mm',\n            LLLL: 'YYYY年M月D日dddd HH:mm',\n            l: 'YYYY/M/D',\n            ll: 'YYYY年M月D日',\n            lll: 'YYYY年M月D日 HH:mm',\n            llll: 'YYYY年M月D日dddd HH:mm',\n        },\n        meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === '凌晨' || meridiem === '早上' || meridiem === '上午') {\n                return hour;\n            } else if (meridiem === '中午') {\n                return hour >= 11 ? hour : hour + 12;\n            } else if (meridiem === '下午' || meridiem === '晚上') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            var hm = hour * 100 + minute;\n            if (hm < 600) {\n                return '凌晨';\n            } else if (hm < 900) {\n                return '早上';\n            } else if (hm < 1130) {\n                return '上午';\n            } else if (hm < 1230) {\n                return '中午';\n            } else if (hm < 1800) {\n                return '下午';\n            } else {\n                return '晚上';\n            }\n        },\n        calendar: {\n            sameDay: '[今天] LT',\n            nextDay: '[明天] LT',\n            nextWeek: '[下]dddd LT',\n            lastDay: '[昨天] LT',\n            lastWeek: '[上]dddd LT',\n            sameElse: 'L',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(日|月|週)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'd':\n                case 'D':\n                case 'DDD':\n                    return number + '日';\n                case 'M':\n                    return number + '月';\n                case 'w':\n                case 'W':\n                    return number + '週';\n                default:\n                    return number;\n            }\n        },\n        relativeTime: {\n            future: '%s後',\n            past: '%s前',\n            s: '幾秒',\n            ss: '%d 秒',\n            m: '1 分鐘',\n            mm: '%d 分鐘',\n            h: '1 小時',\n            hh: '%d 小時',\n            d: '1 天',\n            dd: '%d 天',\n            M: '1 個月',\n            MM: '%d 個月',\n            y: '1 年',\n            yy: '%d 年',\n        },\n    });\n\n    return zhTw;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,uCAAuC,CAACC,KAAK,CACjD,GACJ,CAAC;IACDC,WAAW,EAAE,wCAAwC,CAACD,KAAK,CACvD,GACJ,CAAC;IACDE,QAAQ,EAAE,6BAA6B,CAACF,KAAK,CAAC,GAAG,CAAC;IAClDG,aAAa,EAAE,sBAAsB,CAACH,KAAK,CAAC,GAAG,CAAC;IAChDI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,qBAAqB;MAC3BC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,mBAAmB;IAClCC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,EAAE;QAC7D,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,IAAI,EAAE;QAC1B,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,EAAE;QAC/C,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIC,EAAE,GAAGJ,IAAI,GAAG,GAAG,GAAGE,MAAM;MAC5B,IAAIE,EAAE,GAAG,GAAG,EAAE;QACV,OAAO,IAAI;MACf,CAAC,MAAM,IAAIA,EAAE,GAAG,GAAG,EAAE;QACjB,OAAO,IAAI;MACf,CAAC,MAAM,IAAIA,EAAE,GAAG,IAAI,EAAE;QAClB,OAAO,IAAI;MACf,CAAC,MAAM,IAAIA,EAAE,GAAG,IAAI,EAAE;QAClB,OAAO,IAAI;MACf,CAAC,MAAM,IAAIA,EAAE,GAAG,IAAI,EAAE;QAClB,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE;IACd,CAAC;IACDC,sBAAsB,EAAE,gBAAgB;IACxCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;UACN,OAAOD,MAAM,GAAG,GAAG;QACvB,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,GAAG;QACvB,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,GAAG;QACvB;UACI,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDE,YAAY,EAAE;MACVC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,CAAC,EAAE,IAAI;MACPC,EAAE,EAAE,MAAM;MACVC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,MAAM;MACVC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE;IACR;EACJ,CAAC,CAAC;EAEF,OAAOnD,IAAI;AAEf,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}