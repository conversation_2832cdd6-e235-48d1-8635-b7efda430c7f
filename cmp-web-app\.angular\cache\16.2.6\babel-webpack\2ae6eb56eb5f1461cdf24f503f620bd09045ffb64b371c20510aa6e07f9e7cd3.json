{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ResetPasswordRoutingModule } from './reset-password-routing.module';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CommonVnptModule } from '../../common-module/common.module';\nimport { DialogModule } from \"primeng/dialog\";\nimport { ResetPasswordComponent } from \"../reset-password/reset-password.component\";\nimport * as i0 from \"@angular/core\";\nexport class ResetPasswordModule {\n  static {\n    this.ɵfac = function ResetPasswordModule_Factory(t) {\n      return new (t || ResetPasswordModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResetPasswordModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ResetPasswordRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule, CommonVnptModule, DialogModule, CommonModule, FormsModule, ReactiveFormsModule, CommonVnptModule, DialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResetPasswordModule, {\n    declarations: [ResetPasswordComponent],\n    imports: [CommonModule, ResetPasswordRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule, CommonVnptModule, DialogModule, CommonModule, FormsModule, ReactiveFormsModule, CommonVnptModule, DialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ResetPasswordRoutingModule", "ButtonModule", "CheckboxModule", "FormsModule", "ReactiveFormsModule", "PasswordModule", "InputTextModule", "CommonVnptModule", "DialogModule", "ResetPasswordComponent", "ResetPasswordModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\reset-password\\reset-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ResetPasswordRoutingModule } from './reset-password-routing.module';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\r\nimport { PasswordModule } from 'primeng/password';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { CommonVnptModule } from '../../common-module/common.module';\r\nimport { DialogModule } from \"primeng/dialog\";\r\nimport {ResetPasswordComponent} from \"../reset-password/reset-password.component\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        ResetPasswordRoutingModule,\r\n        ButtonModule,\r\n        CheckboxModule,\r\n        InputTextModule,\r\n        FormsModule,\r\n        PasswordModule,\r\n        CommonVnptModule,\r\n        DialogModule,\r\n        CommonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        CommonVnptModule,\r\n        DialogModule\r\n    ],\r\n    declarations: [ResetPasswordComponent]\r\n})\r\nexport class ResetPasswordModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAQC,sBAAsB,QAAO,4CAA4C;;AAqBjF,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAjBxBX,YAAY,EACZC,0BAA0B,EAC1BC,YAAY,EACZC,cAAc,EACdI,eAAe,EACfH,WAAW,EACXE,cAAc,EACdE,gBAAgB,EAChBC,YAAY,EACZT,YAAY,EACZI,WAAW,EACXC,mBAAmB,EACnBG,gBAAgB,EAChBC,YAAY;IAAA;EAAA;;;2EAIPE,mBAAmB;IAAAC,YAAA,GAFbF,sBAAsB;IAAAG,OAAA,GAfjCb,YAAY,EACZC,0BAA0B,EAC1BC,YAAY,EACZC,cAAc,EACdI,eAAe,EACfH,WAAW,EACXE,cAAc,EACdE,gBAAgB,EAChBC,YAAY,EACZT,YAAY,EACZI,WAAW,EACXC,mBAAmB,EACnBG,gBAAgB,EAChBC,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}