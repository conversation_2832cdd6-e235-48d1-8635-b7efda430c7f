{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class LogsService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/user-mgmt/logs\";\n  }\n  searchLogs(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function LogsService_Factory(t) {\n      return new (t || LogsService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LogsService,\n      factory: LogsService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "LogsService", "constructor", "httpService", "prefixApi", "searchLogs", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\activity-history\\LogsService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class LogsService{\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/user-mgmt/logs\";\r\n    }\r\n\r\n    public searchLogs(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/search`,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,WAAW;EAEpBC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,iBAAiB;EACtC;EAEOC,UAAUA,CAACC,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC/G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;;;uBARSR,WAAW,EAAAU,EAAA,CAAAC,QAAA,CAEAZ,WAAW;IAAA;EAAA;;;aAFtBC,WAAW;MAAAY,OAAA,EAAXZ,WAAW,CAAAa;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}