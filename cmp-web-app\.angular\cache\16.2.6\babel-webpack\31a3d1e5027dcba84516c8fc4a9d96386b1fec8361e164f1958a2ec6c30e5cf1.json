{"ast": null, "code": "export function calculateTableHeight(elTable, elFooter) {\n  const windowHeight = window.innerHeight;\n  const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;\n  const footerHeight = elFooter.nativeElement.offsetHeight;\n  const height = windowHeight - tableStyleTop - footerHeight - 11;\n  elTable.nativeElement.style.height = `${height}px`;\n}\nexport function calculateTableHeightWithoutFooter(elTable) {\n  const windowHeight = window.innerHeight;\n  const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;\n  const height = windowHeight - tableStyleTop - 9;\n  elTable.nativeElement.style.height = `${height}px`;\n}\nexport function calculateHeightContentBody(elTable) {\n  const windowHeight = window.innerHeight;\n  const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;\n  const height = windowHeight - tableStyleTop;\n  elTable.nativeElement.style.height = `${height}px`;\n}\nexport function convertToTreeData(flatData, hasNullParent = true) {\n  const nodeMap = new Map();\n  let treeData = [];\n  const rootNodes = [];\n  const sortedFlatData = flatData.sort((a, b) => a.parentId - b.parentId);\n  // Tạo map chứa node theo id để sau này dễ dàng truy cập khi tạo mối quan hệ cha - con\n  sortedFlatData.forEach(item => {\n    const node = {\n      data: {\n        ...item\n      },\n      children: []\n    };\n    nodeMap.set(item.id, node);\n    if (hasNullParent) {\n      if (item.parentId === null) {\n        rootNodes.push(node); // Lưu trữ tất cả các nút gốc vào mảng rootNodes\n      }\n    } else {\n      const parentId = item.parentId;\n      if (!nodeMap.has(parentId)) {\n        rootNodes.push(node); // Lưu trữ tất cả các nút gốc vào mảng rootNodes\n      }\n    }\n  });\n  // Tạo cây dữ liệu\n  sortedFlatData.forEach(item => {\n    const node = nodeMap.get(item.id);\n    const parentId = item.parentId;\n    if (parentId !== null) {\n      // Nếu không phải node gốc, thì tìm node cha tương ứng và thêm node vào mảng children của node cha\n      const parentNode = nodeMap.get(parentId);\n      if (parentNode) {\n        parentNode.children.push(node);\n      }\n    }\n  });\n  // Nếu không có nút gốc nào (có parentId là null), giữ lại tất cả các nút trong flatData làm nút gốc\n  if (rootNodes.length === 0) {\n    const nonRootNodes = new Set(rootNodes.map(node => node.data.id));\n    treeData = rootNodes.filter(node => !nonRootNodes.has(node.data.parentId));\n  } else {\n    // Nếu có nút gốc thì đưa các nút gốc vào mảng treeData\n    treeData.push(...rootNodes);\n  }\n  treeData.forEach(node => {\n    node.expanded = true;\n  });\n  return treeData;\n}\nexport function transformDataTreeNode(data, isDisable) {\n  return data.map(node => transformNode(node, null, isDisable));\n}\nfunction transformNode(node, parentKey, isDisable) {\n  const treeNode = {\n    label: node.title,\n    key: node.key,\n    selectable: !isDisable,\n    children: node.children ? node.children.map(child => transformNode(child, node.key, isDisable)) : []\n  };\n  return treeNode;\n}\nexport function getItemsSelected(arrayA, arrayB) {\n  const resultArray = [];\n  for (const item of arrayA) {\n    const hasMatchingChild = (item.children || []).some(child => arrayB.includes(parseInt(child.key)));\n    if (arrayB.includes(parseInt(item.key)) || hasMatchingChild) {\n      resultArray.push(item);\n      if (hasMatchingChild) {\n        const matchingChildren = (item.children || []).filter(child => arrayB.includes(parseInt(child.key)));\n        resultArray.push(...matchingChildren);\n      }\n    }\n  }\n  return resultArray;\n}\nexport function validate(form, fieldName, error) {\n  return form.controls[fieldName].dirty && form.controls[fieldName].errors && form.controls[fieldName].errors[error];\n}\nexport function isInvalid(form, fieldName) {\n  return form.controls[fieldName].dirty && form.controls[fieldName].invalid;\n}\nexport function validateNestedForm(form, fieldName, error) {\n  return form.get(fieldName).dirty && form.get(fieldName).errors && form.get(fieldName).errors[error];\n}\n// kiểm tra custom validation ở cả form\nexport function validateWholeForm(form, errorName) {\n  return form.dirty && form.errors && form.errors[errorName];\n}\nexport function convertStringToDate(dateString) {\n  if (!dateString) {\n    return null;\n  }\n  const arrDateTime = dateString.split(' - ');\n  if (!arrDateTime[0]) {\n    return null;\n  }\n  const arrDate = arrDateTime[0].split('/');\n  if (arrDate.length !== 3) {\n    return null;\n  }\n  const result = new Date(parseInt(arrDate[0]), parseInt(arrDate[1]) - 1, parseInt(arrDate[2]));\n  if (arrDateTime[1]) {\n    const arrTime = arrDateTime[1].split(':');\n    if (arrTime.length === 3) {\n      result.setHours(parseInt(arrTime[0]), parseInt(arrTime[1]), parseInt(arrTime[2]));\n    }\n  }\n  return result;\n}\nexport function convertStringToDateEnd(dateString) {\n  if (!dateString) {\n    return null;\n  }\n  const arrDateTime = dateString.split(' - ');\n  if (!arrDateTime[0]) {\n    return null;\n  }\n  const arrDate = arrDateTime[0].split('/');\n  if (arrDate.length !== 3) {\n    return null;\n  }\n  const result = new Date(parseInt(arrDate[0]), parseInt(arrDate[1]) - 1, parseInt(arrDate[2]));\n  if (arrDateTime[1]) {\n    const arrTime = arrDateTime[1].split(':');\n    if (arrTime.length === 3) {\n      result.setHours(parseInt(arrTime[0]), parseInt(arrTime[1]), parseInt(arrTime[2]));\n    } else {\n      result.setHours(23, 59, 59);\n    }\n  } else {\n    result.setHours(23, 59, 59);\n  }\n  return result;\n}\nexport function convertDateToStringDate(date) {\n  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;\n}\nexport function titleCaseWord(word) {\n  if (!word) {\n    return word;\n  }\n  return word[0].toUpperCase() + word.substr(1).toLowerCase();\n}\nexport function onInput(event) {\n  if (event.key === '.') {\n    event.preventDefault();\n  }\n}\nexport function checkNull(newData, oldData) {\n  oldData = oldData === undefined ? null : oldData;\n  newData = newData === null ? '' : newData;\n  oldData = oldData === null ? '' : oldData;\n  return newData !== oldData;\n}\nexport function checkNullBoolean(newData, oldData) {\n  if (oldData === undefined) {\n    return true;\n  }\n  newData = newData === null ? false : newData;\n  oldData = oldData === null ? false : oldData;\n  return newData !== oldData;\n}\nfunction compareObjects(obj1, obj2) {\n  return Object.keys(obj1).every(key => obj1[key] === obj2[key]);\n}\nexport function checkNullArray(newData, oldData) {\n  oldData = oldData ?? [];\n  newData = newData ?? [];\n  const allElementsIncluded = newData.every(newItem => oldData.some(oldItem => compareObjects(newItem, oldItem)));\n  if (allElementsIncluded && newData.length === oldData.length) {\n    return false;\n  }\n  return true;\n}\nexport var Type;\n(function (Type) {\n  Type[Type[\"string\"] = 0] = \"string\";\n  Type[Type[\"boolean\"] = 1] = \"boolean\";\n  Type[Type[\"array\"] = 2] = \"array\";\n})(Type || (Type = {}));\nexport function checkDataChanged(type, newData, oldData) {\n  switch (type) {\n    case Type.string:\n      {\n        return checkNull(newData, oldData);\n      }\n    case Type.boolean:\n      {\n        return checkNullBoolean(newData, oldData);\n      }\n    case Type.array:\n      {\n        return checkNullArray(newData, oldData);\n      }\n    default:\n      {\n        return true;\n      }\n  }\n}\nexport function isAdd(oldData) {\n  return oldData !== undefined && oldData === null;\n}\nexport function isEdit(oldData) {\n  return oldData !== undefined && oldData !== null;\n}\nexport function trimString(stringToTrim) {\n  return stringToTrim.trim();\n}\nexport function convertLabelIdsToNames(labelIds, labelList) {\n  const label = labelList.find(label => label.id === labelIds);\n  return label ? label.name.replaceAll('-', '').trim() : '';\n}\nexport function getFieldDataTable(value) {\n  return value = value !== undefined ? value : null;\n}\nexport function isEmptyList(dataList) {\n  return !dataList || dataList.length === 0;\n}\nexport function preventSpecialCharacter(keyboardEvent) {\n  if (keyboardEvent.key === 'e' || keyboardEvent.key === 'E') {\n    keyboardEvent.preventDefault();\n  }\n}\nexport function isValidPhoneNumber(phoneNumber) {\n  const phonePattern = /^0\\d{9,10}$/;\n  if (phonePattern.test(phoneNumber)) {\n    return true;\n  }\n  return false;\n}\nexport function convert84to0PhoneNumber(phoneNumber) {\n  if (phoneNumber.startsWith(\"84\")) {\n    phoneNumber = \"0\" + phoneNumber.substring(2);\n  }\n  return phoneNumber;\n}", "map": {"version": 3, "names": ["calculateTableHeight", "elTable", "<PERSON><PERSON><PERSON><PERSON>", "windowHeight", "window", "innerHeight", "tableStyleTop", "nativeElement", "getBoundingClientRect", "top", "footerHeight", "offsetHeight", "height", "style", "calculateTableHeightWithoutFooter", "calculateHeightContentBody", "convertToTreeData", "flatData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeMap", "Map", "treeData", "rootNodes", "sortedFlatData", "sort", "a", "b", "parentId", "for<PERSON>ach", "item", "node", "data", "children", "set", "id", "push", "has", "get", "parentNode", "length", "nonRootNodes", "Set", "map", "filter", "expanded", "transformDataTreeNode", "isDisable", "transformNode", "parent<PERSON><PERSON>", "treeNode", "label", "title", "key", "selectable", "child", "getItemsSelected", "arrayA", "arrayB", "resultArray", "hasMatchingChild", "some", "includes", "parseInt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validate", "form", "fieldName", "error", "controls", "dirty", "errors", "isInvalid", "invalid", "validateNestedForm", "validateWholeForm", "errorName", "convertStringToDate", "dateString", "arrDateTime", "split", "arrDate", "result", "Date", "arrTime", "setHours", "convertStringToDateEnd", "convertDateToStringDate", "date", "getFullYear", "getMonth", "getDate", "titleCaseWord", "word", "toUpperCase", "substr", "toLowerCase", "onInput", "event", "preventDefault", "checkNull", "newData", "oldData", "undefined", "checkNullBoolean", "compareObjects", "obj1", "obj2", "Object", "keys", "every", "checkNullArray", "allElementsIncluded", "newItem", "oldItem", "Type", "checkDataChanged", "type", "string", "boolean", "array", "isAdd", "isEdit", "trimString", "stringToTrim", "trim", "convertLabelIdsToNames", "labelIds", "labelList", "find", "name", "replaceAll", "getFieldDataTable", "value", "isEmptyList", "dataList", "preventSpecialCharacter", "keyboardEvent", "isValidPhoneNumber", "phoneNumber", "phonePattern", "test", "convert84to0PhoneNumber", "startsWith", "substring"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\utils\\util.ts"], "sourcesContent": ["import {ElementRef} from '@angular/core';\r\nimport {FormGroup} from '@angular/forms';\r\nimport {TreeNode} from 'primeng/api';\r\n\r\nexport function calculateTableHeight(elTable: ElementRef, elFooter: ElementRef) {\r\n    const windowHeight = window.innerHeight;\r\n    const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;\r\n    const footerHeight = elFooter.nativeElement.offsetHeight;\r\n    const height = windowHeight - tableStyleTop - footerHeight - 11;\r\n    elTable.nativeElement.style.height = `${height}px`;\r\n}\r\n\r\nexport function calculateTableHeightWithoutFooter(elTable: ElementRef) {\r\n    const windowHeight = window.innerHeight;\r\n    const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;\r\n    const height = windowHeight - tableStyleTop - 9;\r\n    elTable.nativeElement.style.height = `${height}px`;\r\n}\r\n\r\nexport function calculateHeightContentBody(elTable: ElementRef) {\r\n    const windowHeight = window.innerHeight;\r\n    const tableStyleTop = elTable.nativeElement.getBoundingClientRect().top;\r\n    const height = windowHeight - tableStyleTop;\r\n    elTable.nativeElement.style.height = `${height}px`;\r\n}\r\n\r\nexport function convertToTreeData(flatData: any[], hasNullParent: boolean = true): TreeNode[] {\r\n    const nodeMap = new Map<number, TreeNode>();\r\n    let treeData: TreeNode[] = [];\r\n    const rootNodes: TreeNode[] = [];\r\n    const sortedFlatData = flatData.sort((a, b) => (a.parentId - b.parentId));\r\n    // Tạo map chứa node theo id để sau này dễ dàng truy cập khi tạo mối quan hệ cha - con\r\n    sortedFlatData.forEach(item => {\r\n        const node: TreeNode = {\r\n            data: {...item},\r\n            children: []\r\n        };\r\n        nodeMap.set(item.id, node);\r\n        if (hasNullParent) {\r\n            if (item.parentId === null) {\r\n                rootNodes.push(node); // Lưu trữ tất cả các nút gốc vào mảng rootNodes\r\n            }\r\n        } else {\r\n            const parentId = item.parentId;\r\n            if (!nodeMap.has(parentId)) {\r\n                rootNodes.push(node); // Lưu trữ tất cả các nút gốc vào mảng rootNodes\r\n            }\r\n        }\r\n    });\r\n\r\n    // Tạo cây dữ liệu\r\n    sortedFlatData.forEach(item => {\r\n        const node = nodeMap.get(item.id);\r\n        const parentId = item.parentId;\r\n\r\n        if (parentId !== null) {\r\n            // Nếu không phải node gốc, thì tìm node cha tương ứng và thêm node vào mảng children của node cha\r\n            const parentNode = nodeMap.get(parentId);\r\n            if (parentNode) {\r\n                parentNode.children.push(node);\r\n            }\r\n        }\r\n    });\r\n\r\n    // Nếu không có nút gốc nào (có parentId là null), giữ lại tất cả các nút trong flatData làm nút gốc\r\n    if (rootNodes.length === 0) {\r\n        const nonRootNodes = new Set(rootNodes.map(node => node.data.id));\r\n        treeData = rootNodes.filter(node => !nonRootNodes.has(node.data.parentId));\r\n    } else {\r\n        // Nếu có nút gốc thì đưa các nút gốc vào mảng treeData\r\n        treeData.push(...rootNodes);\r\n    }\r\n    treeData.forEach((node: TreeNode) => {\r\n        node.expanded = true;\r\n    });\r\n\r\n    return treeData;\r\n}\r\n\r\nexport function transformDataTreeNode(data: [], isDisable: boolean) {\r\n    return data.map((node: any) => transformNode(node, null, isDisable));\r\n}\r\n\r\nfunction transformNode(node: any, parentKey: string | null, isDisable: boolean): TreeNode {\r\n    const treeNode: TreeNode = {\r\n        label: node.title,\r\n        key: node.key, // Thêm key của nút hiện tại vào TreeNode\r\n        selectable: !isDisable,\r\n        children: node.children ? node.children.map((child: any) => transformNode(child, node.key, isDisable)) : []\r\n    };\r\n    return treeNode;\r\n}\r\n\r\nexport function getItemsSelected(arrayA: TreeNode[], arrayB: number[]): TreeNode[] {\r\n    const resultArray: TreeNode[] = [];\r\n\r\n    for (const item of arrayA) {\r\n        const hasMatchingChild = (item.children || []).some(child => arrayB.includes(parseInt(child.key)));\r\n        if (arrayB.includes(parseInt(item.key)) || hasMatchingChild) {\r\n            resultArray.push(item);\r\n            if (hasMatchingChild) {\r\n                const matchingChildren = (item.children || []).filter(child => arrayB.includes(parseInt(child.key)));\r\n                resultArray.push(...matchingChildren);\r\n            }\r\n        }\r\n    }\r\n    return resultArray;\r\n}\r\n\r\nexport function validate(form: FormGroup, fieldName: string, error: string) {\r\n    return form.controls[fieldName].dirty && form.controls[fieldName].errors && form.controls[fieldName].errors[error];\r\n}\r\n\r\nexport function isInvalid(form: FormGroup, fieldName: string) {\r\n    return form.controls[fieldName].dirty && form.controls[fieldName].invalid;\r\n}\r\n\r\nexport function validateNestedForm(form: FormGroup, fieldName: string, error: string) {\r\n    return form.get(fieldName).dirty && form.get(fieldName).errors && form.get(fieldName).errors[error];\r\n}\r\n\r\n// kiểm tra custom validation ở cả form\r\nexport function validateWholeForm(form: FormGroup, errorName: string) {\r\n    return form.dirty && form.errors && form.errors[errorName];\r\n}\r\n\r\nexport function convertStringToDate(dateString: string) {\r\n    if (!dateString) {\r\n        return null;\r\n    }\r\n    const arrDateTime = dateString.split(' - ');\r\n    if (!arrDateTime[0]) {\r\n        return null;\r\n    }\r\n    const arrDate = arrDateTime[0].split('/');\r\n    if (arrDate.length !== 3) {\r\n        return null;\r\n    }\r\n    const result = new Date(parseInt(arrDate[0]), parseInt(arrDate[1]) - 1, parseInt(arrDate[2]));\r\n    if (arrDateTime[1]) {\r\n        const arrTime = arrDateTime[1].split(':');\r\n        if (arrTime.length === 3) {\r\n            result.setHours(parseInt(arrTime[0]), parseInt(arrTime[1]), parseInt(arrTime[2]));\r\n        }\r\n    }\r\n    return result;\r\n}\r\n\r\nexport function convertStringToDateEnd(dateString: string) {\r\n    if (!dateString) {\r\n        return null;\r\n    }\r\n    const arrDateTime = dateString.split(' - ');\r\n    if (!arrDateTime[0]) {\r\n        return null;\r\n    }\r\n    const arrDate = arrDateTime[0].split('/');\r\n    if (arrDate.length !== 3) {\r\n        return null;\r\n    }\r\n    const result = new Date(parseInt(arrDate[0]), parseInt(arrDate[1]) - 1, parseInt(arrDate[2]));\r\n    if (arrDateTime[1]) {\r\n        const arrTime = arrDateTime[1].split(':');\r\n        if (arrTime.length === 3) {\r\n            result.setHours(parseInt(arrTime[0]), parseInt(arrTime[1]), parseInt(arrTime[2]));\r\n        } else {\r\n            result.setHours(23, 59, 59);\r\n        }\r\n    } else {\r\n        result.setHours(23, 59, 59);\r\n    }\r\n    return result;\r\n}\r\n\r\nexport function convertDateToStringDate(date: Date) {\r\n    return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;\r\n}\r\n\r\nexport function titleCaseWord(word: string) {\r\n    if (!word) {\r\n        return word;\r\n    }\r\n    return word[0].toUpperCase() + word.substr(1).toLowerCase();\r\n}\r\n\r\nexport function onInput(event: KeyboardEvent) {\r\n    if (event.key === '.') {\r\n        event.preventDefault();\r\n    }\r\n}\r\n\r\n\r\nexport function checkNull(newData: any, oldData: any): boolean {\r\n    oldData = oldData === undefined ? null : oldData;\r\n    newData = newData === null ? '' : newData;\r\n    oldData = oldData === null ? '' : oldData;\r\n    return newData !== oldData;\r\n}\r\n\r\nexport function checkNullBoolean(newData: any, oldData: any): boolean {\r\n    if (oldData === undefined) {\r\n        return true;\r\n    }\r\n    newData = newData === null ? false : newData;\r\n    oldData = oldData === null ? false : oldData;\r\n    return newData !== oldData;\r\n}\r\n\r\nfunction compareObjects(obj1: any, obj2: any): boolean {\r\n    return Object.keys(obj1).every(key => obj1[key] === obj2[key]);\r\n}\r\n\r\nexport function checkNullArray(newData: any[] | null | undefined, oldData: any[] | null | undefined): boolean {\r\n    oldData = oldData ?? [];\r\n    newData = newData ?? [];\r\n    const allElementsIncluded = newData.every(newItem => oldData.some(oldItem => compareObjects(newItem, oldItem)));\r\n    if (allElementsIncluded && newData.length === oldData.length) {\r\n        return false;\r\n    }\r\n    return true;\r\n}\r\n\r\n\r\nexport enum Type {\r\n    string, boolean, array\r\n}\r\n\r\nexport function checkDataChanged(type, newData, oldData) {\r\n    switch (type) {\r\n        case Type.string: {\r\n            return checkNull(newData, oldData);\r\n        }\r\n        case Type.boolean: {\r\n            return checkNullBoolean(newData, oldData);\r\n        }\r\n        case Type.array: {\r\n            return checkNullArray(newData, oldData);\r\n        }\r\n        default : {\r\n            return true;\r\n        }\r\n    }\r\n}\r\n\r\nexport function isAdd(oldData) {\r\n    return oldData !== undefined && oldData === null;\r\n}\r\n\r\nexport function isEdit(oldData) {\r\n    return oldData !== undefined && oldData !== null;\r\n}\r\n\r\nexport function trimString(stringToTrim: string): string {\r\n    return stringToTrim.trim();\r\n}\r\n\r\nexport function convertLabelIdsToNames(labelIds, labelList) {\r\n    const label = labelList.find(label => label.id === labelIds);\r\n    return label ? label.name.replaceAll('-', '').trim() : '';\r\n}\r\n\r\nexport function getFieldDataTable(value) {\r\n    return value = value !== undefined ? value : null;\r\n}\r\n\r\nexport function isEmptyList(dataList: any): boolean {\r\n    return !dataList || dataList.length === 0;\r\n}\r\n\r\nexport function preventSpecialCharacter(keyboardEvent: KeyboardEvent): void {\r\n    if (keyboardEvent.key === 'e' || keyboardEvent.key === 'E') {\r\n        keyboardEvent.preventDefault();\r\n    }\r\n}\r\n\r\nexport function isValidPhoneNumber(phoneNumber) {\r\n    const phonePattern = /^0\\d{9,10}$/;\r\n\r\n    if (phonePattern.test(phoneNumber)) {\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\n\r\nexport function convert84to0PhoneNumber(phoneNumber :string) {\r\n    if(phoneNumber.startsWith(\"84\")){\r\n        phoneNumber = \"0\" + phoneNumber.substring(2);\r\n    }\r\n    return phoneNumber;\r\n}\r\n"], "mappings": "AAIA,OAAM,SAAUA,oBAAoBA,CAACC,OAAmB,EAAEC,QAAoB;EAC1E,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;EACvC,MAAMC,aAAa,GAAGL,OAAO,CAACM,aAAa,CAACC,qBAAqB,EAAE,CAACC,GAAG;EACvE,MAAMC,YAAY,GAAGR,QAAQ,CAACK,aAAa,CAACI,YAAY;EACxD,MAAMC,MAAM,GAAGT,YAAY,GAAGG,aAAa,GAAGI,YAAY,GAAG,EAAE;EAC/DT,OAAO,CAACM,aAAa,CAACM,KAAK,CAACD,MAAM,GAAG,GAAGA,MAAM,IAAI;AACtD;AAEA,OAAM,SAAUE,iCAAiCA,CAACb,OAAmB;EACjE,MAAME,YAAY,GAAGC,MAAM,CAACC,WAAW;EACvC,MAAMC,aAAa,GAAGL,OAAO,CAACM,aAAa,CAACC,qBAAqB,EAAE,CAACC,GAAG;EACvE,MAAMG,MAAM,GAAGT,YAAY,GAAGG,aAAa,GAAG,CAAC;EAC/CL,OAAO,CAACM,aAAa,CAACM,KAAK,CAACD,MAAM,GAAG,GAAGA,MAAM,IAAI;AACtD;AAEA,OAAM,SAAUG,0BAA0BA,CAACd,OAAmB;EAC1D,MAAME,YAAY,GAAGC,MAAM,CAACC,WAAW;EACvC,MAAMC,aAAa,GAAGL,OAAO,CAACM,aAAa,CAACC,qBAAqB,EAAE,CAACC,GAAG;EACvE,MAAMG,MAAM,GAAGT,YAAY,GAAGG,aAAa;EAC3CL,OAAO,CAACM,aAAa,CAACM,KAAK,CAACD,MAAM,GAAG,GAAGA,MAAM,IAAI;AACtD;AAEA,OAAM,SAAUI,iBAAiBA,CAACC,QAAe,EAAEC,aAAA,GAAyB,IAAI;EAC5E,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAoB;EAC3C,IAAIC,QAAQ,GAAe,EAAE;EAC7B,MAAMC,SAAS,GAAe,EAAE;EAChC,MAAMC,cAAc,GAAGN,QAAQ,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAS,CAAC;EACzE;EACAJ,cAAc,CAACK,OAAO,CAACC,IAAI,IAAG;IAC1B,MAAMC,IAAI,GAAa;MACnBC,IAAI,EAAE;QAAC,GAAGF;MAAI,CAAC;MACfG,QAAQ,EAAE;KACb;IACDb,OAAO,CAACc,GAAG,CAACJ,IAAI,CAACK,EAAE,EAAEJ,IAAI,CAAC;IAC1B,IAAIZ,aAAa,EAAE;MACf,IAAIW,IAAI,CAACF,QAAQ,KAAK,IAAI,EAAE;QACxBL,SAAS,CAACa,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC;;KAE7B,MAAM;MACH,MAAMH,QAAQ,GAAGE,IAAI,CAACF,QAAQ;MAC9B,IAAI,CAACR,OAAO,CAACiB,GAAG,CAACT,QAAQ,CAAC,EAAE;QACxBL,SAAS,CAACa,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC;;;EAGlC,CAAC,CAAC;EAEF;EACAP,cAAc,CAACK,OAAO,CAACC,IAAI,IAAG;IAC1B,MAAMC,IAAI,GAAGX,OAAO,CAACkB,GAAG,CAACR,IAAI,CAACK,EAAE,CAAC;IACjC,MAAMP,QAAQ,GAAGE,IAAI,CAACF,QAAQ;IAE9B,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACnB;MACA,MAAMW,UAAU,GAAGnB,OAAO,CAACkB,GAAG,CAACV,QAAQ,CAAC;MACxC,IAAIW,UAAU,EAAE;QACZA,UAAU,CAACN,QAAQ,CAACG,IAAI,CAACL,IAAI,CAAC;;;EAG1C,CAAC,CAAC;EAEF;EACA,IAAIR,SAAS,CAACiB,MAAM,KAAK,CAAC,EAAE;IACxB,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAACnB,SAAS,CAACoB,GAAG,CAACZ,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACG,EAAE,CAAC,CAAC;IACjEb,QAAQ,GAAGC,SAAS,CAACqB,MAAM,CAACb,IAAI,IAAI,CAACU,YAAY,CAACJ,GAAG,CAACN,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;GAC7E,MAAM;IACH;IACAN,QAAQ,CAACc,IAAI,CAAC,GAAGb,SAAS,CAAC;;EAE/BD,QAAQ,CAACO,OAAO,CAAEE,IAAc,IAAI;IAChCA,IAAI,CAACc,QAAQ,GAAG,IAAI;EACxB,CAAC,CAAC;EAEF,OAAOvB,QAAQ;AACnB;AAEA,OAAM,SAAUwB,qBAAqBA,CAACd,IAAQ,EAAEe,SAAkB;EAC9D,OAAOf,IAAI,CAACW,GAAG,CAAEZ,IAAS,IAAKiB,aAAa,CAACjB,IAAI,EAAE,IAAI,EAAEgB,SAAS,CAAC,CAAC;AACxE;AAEA,SAASC,aAAaA,CAACjB,IAAS,EAAEkB,SAAwB,EAAEF,SAAkB;EAC1E,MAAMG,QAAQ,GAAa;IACvBC,KAAK,EAAEpB,IAAI,CAACqB,KAAK;IACjBC,GAAG,EAAEtB,IAAI,CAACsB,GAAG;IACbC,UAAU,EAAE,CAACP,SAAS;IACtBd,QAAQ,EAAEF,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACE,QAAQ,CAACU,GAAG,CAAEY,KAAU,IAAKP,aAAa,CAACO,KAAK,EAAExB,IAAI,CAACsB,GAAG,EAAEN,SAAS,CAAC,CAAC,GAAG;GAC5G;EACD,OAAOG,QAAQ;AACnB;AAEA,OAAM,SAAUM,gBAAgBA,CAACC,MAAkB,EAAEC,MAAgB;EACjE,MAAMC,WAAW,GAAe,EAAE;EAElC,KAAK,MAAM7B,IAAI,IAAI2B,MAAM,EAAE;IACvB,MAAMG,gBAAgB,GAAG,CAAC9B,IAAI,CAACG,QAAQ,IAAI,EAAE,EAAE4B,IAAI,CAACN,KAAK,IAAIG,MAAM,CAACI,QAAQ,CAACC,QAAQ,CAACR,KAAK,CAACF,GAAG,CAAC,CAAC,CAAC;IAClG,IAAIK,MAAM,CAACI,QAAQ,CAACC,QAAQ,CAACjC,IAAI,CAACuB,GAAG,CAAC,CAAC,IAAIO,gBAAgB,EAAE;MACzDD,WAAW,CAACvB,IAAI,CAACN,IAAI,CAAC;MACtB,IAAI8B,gBAAgB,EAAE;QAClB,MAAMI,gBAAgB,GAAG,CAAClC,IAAI,CAACG,QAAQ,IAAI,EAAE,EAAEW,MAAM,CAACW,KAAK,IAAIG,MAAM,CAACI,QAAQ,CAACC,QAAQ,CAACR,KAAK,CAACF,GAAG,CAAC,CAAC,CAAC;QACpGM,WAAW,CAACvB,IAAI,CAAC,GAAG4B,gBAAgB,CAAC;;;;EAIjD,OAAOL,WAAW;AACtB;AAEA,OAAM,SAAUM,QAAQA,CAACC,IAAe,EAAEC,SAAiB,EAAEC,KAAa;EACtE,OAAOF,IAAI,CAACG,QAAQ,CAACF,SAAS,CAAC,CAACG,KAAK,IAAIJ,IAAI,CAACG,QAAQ,CAACF,SAAS,CAAC,CAACI,MAAM,IAAIL,IAAI,CAACG,QAAQ,CAACF,SAAS,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;AACtH;AAEA,OAAM,SAAUI,SAASA,CAACN,IAAe,EAAEC,SAAiB;EACxD,OAAOD,IAAI,CAACG,QAAQ,CAACF,SAAS,CAAC,CAACG,KAAK,IAAIJ,IAAI,CAACG,QAAQ,CAACF,SAAS,CAAC,CAACM,OAAO;AAC7E;AAEA,OAAM,SAAUC,kBAAkBA,CAACR,IAAe,EAAEC,SAAiB,EAAEC,KAAa;EAChF,OAAOF,IAAI,CAAC5B,GAAG,CAAC6B,SAAS,CAAC,CAACG,KAAK,IAAIJ,IAAI,CAAC5B,GAAG,CAAC6B,SAAS,CAAC,CAACI,MAAM,IAAIL,IAAI,CAAC5B,GAAG,CAAC6B,SAAS,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;AACvG;AAEA;AACA,OAAM,SAAUO,iBAAiBA,CAACT,IAAe,EAAEU,SAAiB;EAChE,OAAOV,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACK,MAAM,IAAIL,IAAI,CAACK,MAAM,CAACK,SAAS,CAAC;AAC9D;AAEA,OAAM,SAAUC,mBAAmBA,CAACC,UAAkB;EAClD,IAAI,CAACA,UAAU,EAAE;IACb,OAAO,IAAI;;EAEf,MAAMC,WAAW,GAAGD,UAAU,CAACE,KAAK,CAAC,KAAK,CAAC;EAC3C,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC,EAAE;IACjB,OAAO,IAAI;;EAEf,MAAME,OAAO,GAAGF,WAAW,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACzC,IAAIC,OAAO,CAACzC,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;;EAEf,MAAM0C,MAAM,GAAG,IAAIC,IAAI,CAACpB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAElB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAElB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F,IAAIF,WAAW,CAAC,CAAC,CAAC,EAAE;IAChB,MAAMK,OAAO,GAAGL,WAAW,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACzC,IAAII,OAAO,CAAC5C,MAAM,KAAK,CAAC,EAAE;MACtB0C,MAAM,CAACG,QAAQ,CAACtB,QAAQ,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAErB,QAAQ,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAErB,QAAQ,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGzF,OAAOF,MAAM;AACjB;AAEA,OAAM,SAAUI,sBAAsBA,CAACR,UAAkB;EACrD,IAAI,CAACA,UAAU,EAAE;IACb,OAAO,IAAI;;EAEf,MAAMC,WAAW,GAAGD,UAAU,CAACE,KAAK,CAAC,KAAK,CAAC;EAC3C,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC,EAAE;IACjB,OAAO,IAAI;;EAEf,MAAME,OAAO,GAAGF,WAAW,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACzC,IAAIC,OAAO,CAACzC,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;;EAEf,MAAM0C,MAAM,GAAG,IAAIC,IAAI,CAACpB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAElB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAElB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F,IAAIF,WAAW,CAAC,CAAC,CAAC,EAAE;IAChB,MAAMK,OAAO,GAAGL,WAAW,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACzC,IAAII,OAAO,CAAC5C,MAAM,KAAK,CAAC,EAAE;MACtB0C,MAAM,CAACG,QAAQ,CAACtB,QAAQ,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAErB,QAAQ,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAErB,QAAQ,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KACpF,MAAM;MACHF,MAAM,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;GAElC,MAAM;IACHH,MAAM,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAE/B,OAAOH,MAAM;AACjB;AAEA,OAAM,SAAUK,uBAAuBA,CAACC,IAAU;EAC9C,OAAO,GAAGA,IAAI,CAACC,WAAW,EAAE,IAAID,IAAI,CAACE,QAAQ,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACG,OAAO,EAAE,EAAE;AAC3E;AAEA,OAAM,SAAUC,aAAaA,CAACC,IAAY;EACtC,IAAI,CAACA,IAAI,EAAE;IACP,OAAOA,IAAI;;EAEf,OAAOA,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;AAC/D;AAEA,OAAM,SAAUC,OAAOA,CAACC,KAAoB;EACxC,IAAIA,KAAK,CAAC7C,GAAG,KAAK,GAAG,EAAE;IACnB6C,KAAK,CAACC,cAAc,EAAE;;AAE9B;AAGA,OAAM,SAAUC,SAASA,CAACC,OAAY,EAAEC,OAAY;EAChDA,OAAO,GAAGA,OAAO,KAAKC,SAAS,GAAG,IAAI,GAAGD,OAAO;EAChDD,OAAO,GAAGA,OAAO,KAAK,IAAI,GAAG,EAAE,GAAGA,OAAO;EACzCC,OAAO,GAAGA,OAAO,KAAK,IAAI,GAAG,EAAE,GAAGA,OAAO;EACzC,OAAOD,OAAO,KAAKC,OAAO;AAC9B;AAEA,OAAM,SAAUE,gBAAgBA,CAACH,OAAY,EAAEC,OAAY;EACvD,IAAIA,OAAO,KAAKC,SAAS,EAAE;IACvB,OAAO,IAAI;;EAEfF,OAAO,GAAGA,OAAO,KAAK,IAAI,GAAG,KAAK,GAAGA,OAAO;EAC5CC,OAAO,GAAGA,OAAO,KAAK,IAAI,GAAG,KAAK,GAAGA,OAAO;EAC5C,OAAOD,OAAO,KAAKC,OAAO;AAC9B;AAEA,SAASG,cAAcA,CAACC,IAAS,EAAEC,IAAS;EACxC,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,KAAK,CAACzD,GAAG,IAAIqD,IAAI,CAACrD,GAAG,CAAC,KAAKsD,IAAI,CAACtD,GAAG,CAAC,CAAC;AAClE;AAEA,OAAM,SAAU0D,cAAcA,CAACV,OAAiC,EAAEC,OAAiC;EAC/FA,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvBD,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB,MAAMW,mBAAmB,GAAGX,OAAO,CAACS,KAAK,CAACG,OAAO,IAAIX,OAAO,CAACzC,IAAI,CAACqD,OAAO,IAAIT,cAAc,CAACQ,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC;EAC/G,IAAIF,mBAAmB,IAAIX,OAAO,CAAC7D,MAAM,KAAK8D,OAAO,CAAC9D,MAAM,EAAE;IAC1D,OAAO,KAAK;;EAEhB,OAAO,IAAI;AACf;AAGA,WAAY2E,IAEX;AAFD,WAAYA,IAAI;EACZA,IAAA,CAAAA,IAAA,0BAAM;EAAEA,IAAA,CAAAA,IAAA,4BAAO;EAAEA,IAAA,CAAAA,IAAA,wBAAK;AAC1B,CAAC,EAFWA,IAAI,KAAJA,IAAI;AAIhB,OAAM,SAAUC,gBAAgBA,CAACC,IAAI,EAAEhB,OAAO,EAAEC,OAAO;EACnD,QAAQe,IAAI;IACR,KAAKF,IAAI,CAACG,MAAM;MAAE;QACd,OAAOlB,SAAS,CAACC,OAAO,EAAEC,OAAO,CAAC;;IAEtC,KAAKa,IAAI,CAACI,OAAO;MAAE;QACf,OAAOf,gBAAgB,CAACH,OAAO,EAAEC,OAAO,CAAC;;IAE7C,KAAKa,IAAI,CAACK,KAAK;MAAE;QACb,OAAOT,cAAc,CAACV,OAAO,EAAEC,OAAO,CAAC;;IAE3C;MAAU;QACN,OAAO,IAAI;;;AAGvB;AAEA,OAAM,SAAUmB,KAAKA,CAACnB,OAAO;EACzB,OAAOA,OAAO,KAAKC,SAAS,IAAID,OAAO,KAAK,IAAI;AACpD;AAEA,OAAM,SAAUoB,MAAMA,CAACpB,OAAO;EAC1B,OAAOA,OAAO,KAAKC,SAAS,IAAID,OAAO,KAAK,IAAI;AACpD;AAEA,OAAM,SAAUqB,UAAUA,CAACC,YAAoB;EAC3C,OAAOA,YAAY,CAACC,IAAI,EAAE;AAC9B;AAEA,OAAM,SAAUC,sBAAsBA,CAACC,QAAQ,EAAEC,SAAS;EACtD,MAAM7E,KAAK,GAAG6E,SAAS,CAACC,IAAI,CAAC9E,KAAK,IAAIA,KAAK,CAAChB,EAAE,KAAK4F,QAAQ,CAAC;EAC5D,OAAO5E,KAAK,GAAGA,KAAK,CAAC+E,IAAI,CAACC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAACN,IAAI,EAAE,GAAG,EAAE;AAC7D;AAEA,OAAM,SAAUO,iBAAiBA,CAACC,KAAK;EACnC,OAAOA,KAAK,GAAGA,KAAK,KAAK9B,SAAS,GAAG8B,KAAK,GAAG,IAAI;AACrD;AAEA,OAAM,SAAUC,WAAWA,CAACC,QAAa;EACrC,OAAO,CAACA,QAAQ,IAAIA,QAAQ,CAAC/F,MAAM,KAAK,CAAC;AAC7C;AAEA,OAAM,SAAUgG,uBAAuBA,CAACC,aAA4B;EAChE,IAAIA,aAAa,CAACpF,GAAG,KAAK,GAAG,IAAIoF,aAAa,CAACpF,GAAG,KAAK,GAAG,EAAE;IACxDoF,aAAa,CAACtC,cAAc,EAAE;;AAEtC;AAEA,OAAM,SAAUuC,kBAAkBA,CAACC,WAAW;EAC1C,MAAMC,YAAY,GAAG,aAAa;EAElC,IAAIA,YAAY,CAACC,IAAI,CAACF,WAAW,CAAC,EAAE;IAChC,OAAO,IAAI;;EAEf,OAAO,KAAK;AAChB;AAEA,OAAM,SAAUG,uBAAuBA,CAACH,WAAmB;EACvD,IAAGA,WAAW,CAACI,UAAU,CAAC,IAAI,CAAC,EAAC;IAC5BJ,WAAW,GAAG,GAAG,GAAGA,WAAW,CAACK,SAAS,CAAC,CAAC,CAAC;;EAEhD,OAAOL,WAAW;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}