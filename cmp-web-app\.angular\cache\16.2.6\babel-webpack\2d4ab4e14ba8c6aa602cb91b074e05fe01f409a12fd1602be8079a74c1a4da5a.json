{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ARRAY_SERVICE } from \"src/app/template/common-module/combobox-lazyload/combobox.lazyload\";\nimport { TableInputControl } from \"src/app/template/common-module/table/table.input.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/service/report/ReportService\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../../common-module/table/table.component\";\nimport * as i8 from \"../../../common-module/table/table.input.component\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/inputtextarea\";\nimport * as i12 from \"primeng/checkbox\";\nfunction TabReportDynamicGeneral_div_0_small_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction TabReportDynamicGeneral_div_0_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    type: a0\n  };\n};\nfunction TabReportDynamicGeneral_div_0_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r4.tranService.translate(\"report.label.reportName\").toLowerCase())));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_div_40_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.openCreateTable());\n    });\n    i0.ɵɵelementStart(1, \"u\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"report.button.addTable\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_div_47_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.openCreateParameter());\n    });\n    i0.ɵɵelementStart(1, \"u\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"report.button.addParam\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_p_button_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 73);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_p_button_51_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.onSubmit());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r8.tranService.translate(\"global.button.save\"))(\"disabled\", ctx_r8.formGeneralInfo.invalid || ctx_r8.isReportNameExisted);\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r12.tranService.translate(\"report.label.tableName\").toLowerCase())));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_p_button_97_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 73);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_p_button_97_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.saveTable());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r15.tranService.translate(\"global.button.save\"))(\"disabled\", ctx_r15.formTable.invalid || ctx_r15.tableInfo.columns == null || ctx_r15.tableInfo.columns == undefined || ctx_r15.tableInfo.columns.length == 0 || ctx_r15.tableInputControl.isUpdating == true || ctx_r15.isTableNameExisted);\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.tranService.translate(\"global.message.formatCodeNotSub\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r19.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r19.tranService.translate(\"report.label.paramKey\").toLowerCase())));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r21.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r22.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r23.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r23.tranService.translate(\"report.label.paramDisplay\").toLowerCase())));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_178_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r26.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c2 = function () {\n  return {\n    len: 16\n  };\n};\nfunction TabReportDynamicGeneral_div_0_small_179_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_180_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r28.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_191_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_192_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r30.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_193_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_204_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_205_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r33.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_206_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r34.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_217_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r35.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_218_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r36.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_219_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r37.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_div_220_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 4);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00A0\\u00A0 \");\n    i0.ɵɵelement(6, \"i\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29)(8, \"input\", 76);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_div_220_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.parameterInfo.queryParam = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r38.tranService.translate(\"report.label.queryParams\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r38.tranService.translate(\"report.label.sampleQueryParam\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r38.parameterInfo.queryParam)(\"maxLength\", 255)(\"placeholder\", ctx_r38.tranService.translate(\"report.text.inputQueryParam\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_224_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r39.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_small_225_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r40.tranService.translate(\"report.message.wrongQueryParamFormat\"));\n  }\n}\nfunction TabReportDynamicGeneral_div_0_p_button_230_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 73);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_p_button_230_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r53);\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.saveParameter());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r41.tranService.translate(\"global.button.save\"))(\"disabled\", ctx_r41.checkInvalidFormParameter());\n  }\n}\nconst _c3 = function () {\n  return {\n    width: \"700px\",\n    top: 0\n  };\n};\nfunction TabReportDynamicGeneral_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 1)(2, \"div\", 2)(3, \"label\", 3);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"span\", 4);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 5)(8, \"input\", 6);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.generalInfo.name = $event);\n    })(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.checkExistReportName());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 7);\n    i0.ɵɵelement(10, \"label\", 3);\n    i0.ɵɵelementStart(11, \"div\", 8);\n    i0.ɵɵtemplate(12, TabReportDynamicGeneral_div_0_small_12_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(13, TabReportDynamicGeneral_div_0_small_13_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(14, TabReportDynamicGeneral_div_0_small_14_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(15, TabReportDynamicGeneral_div_0_small_15_Template, 2, 3, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 10);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 5)(20, \"p-dropdown\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.generalInfo.status = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 2)(22, \"label\", 10);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 5)(25, \"p-checkbox\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.generalInfo.enablePreview = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 2)(27, \"label\", 13);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 5)(30, \"textarea\", 14);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.generalInfo.description = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 7);\n    i0.ɵɵelement(32, \"label\", 15);\n    i0.ɵɵelementStart(33, \"div\", 8);\n    i0.ɵɵtemplate(34, TabReportDynamicGeneral_div_0_small_34_Template, 2, 2, \"small\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\")(36, \"div\", 16)(37, \"div\")(38, \"b\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, TabReportDynamicGeneral_div_0_div_40_Template, 3, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"table-vnpt\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 19)(43, \"div\", 16)(44, \"div\")(45, \"b\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(47, TabReportDynamicGeneral_div_0_div_47_Template, 3, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"table-vnpt\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 21)(50, \"p-button\", 22);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_Template_p_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancel());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, TabReportDynamicGeneral_div_0_p_button_51_Template, 1, 2, \"p-button\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 24)(53, \"p-dialog\", 25);\n    i0.ɵɵlistener(\"visibleChange\", function TabReportDynamicGeneral_div_0_Template_p_dialog_visibleChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.isShowDialogTable = $event);\n    });\n    i0.ɵɵelementStart(54, \"div\", 26)(55, \"form\", 27)(56, \"div\", 2)(57, \"label\", 28);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementStart(59, \"span\", 4);\n    i0.ɵɵtext(60, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 29)(62, \"input\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_62_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.tableInfo.tableName = $event);\n    })(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_62_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.checkExistTableName());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"div\", 7);\n    i0.ɵɵelement(64, \"label\", 3);\n    i0.ɵɵelementStart(65, \"div\", 8);\n    i0.ɵɵtemplate(66, TabReportDynamicGeneral_div_0_small_66_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(67, TabReportDynamicGeneral_div_0_small_67_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(68, TabReportDynamicGeneral_div_0_small_68_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(69, TabReportDynamicGeneral_div_0_small_69_Template, 2, 3, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 2)(71, \"label\", 31);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementStart(73, \"span\", 4);\n    i0.ɵɵtext(74, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 29)(76, \"p-dropdown\", 32);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_76_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.tableInfo.schema = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(77, \"div\", 7);\n    i0.ɵɵelement(78, \"label\", 3);\n    i0.ɵɵelementStart(79, \"div\", 8);\n    i0.ɵɵtemplate(80, TabReportDynamicGeneral_div_0_small_80_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 33)(82, \"label\", 34);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementStart(84, \"span\", 4);\n    i0.ɵɵtext(85, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 35)(87, \"textarea\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_textarea_ngModelChange_87_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.tableInfo.query = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"span\", 37);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_Template_span_click_88_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.copyText($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 7);\n    i0.ɵɵelement(90, \"label\", 3);\n    i0.ɵɵelementStart(91, \"div\", 8);\n    i0.ɵɵtemplate(92, TabReportDynamicGeneral_div_0_small_92_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(93, \"div\", 38)(94, \"table-input-vnpt\", 39);\n    i0.ɵɵlistener(\"valueChange\", function TabReportDynamicGeneral_div_0_Template_table_input_vnpt_valueChange_94_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.tableInfo.columns = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"div\", 21)(96, \"p-button\", 22);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_Template_p_button_click_96_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.isShowDialogTable = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(97, TabReportDynamicGeneral_div_0_p_button_97_Template, 1, 2, \"p-button\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(98, \"div\", 40)(99, \"p-dialog\", 25);\n    i0.ɵɵlistener(\"visibleChange\", function TabReportDynamicGeneral_div_0_Template_p_dialog_visibleChange_99_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.isShowDialogParameter = $event);\n    });\n    i0.ɵɵelementStart(100, \"div\", 41)(101, \"form\", 27)(102, \"div\", 2)(103, \"label\", 42);\n    i0.ɵɵtext(104);\n    i0.ɵɵelementStart(105, \"span\", 4);\n    i0.ɵɵtext(106, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"div\", 29)(108, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_108_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.parameterInfo.prKey = $event);\n    })(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_108_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.checkExistParamKey());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 7);\n    i0.ɵɵelement(110, \"label\", 42);\n    i0.ɵɵelementStart(111, \"div\", 8);\n    i0.ɵɵtemplate(112, TabReportDynamicGeneral_div_0_small_112_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(113, TabReportDynamicGeneral_div_0_small_113_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(114, TabReportDynamicGeneral_div_0_small_114_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(115, TabReportDynamicGeneral_div_0_small_115_Template, 2, 3, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 2)(117, \"label\", 44);\n    i0.ɵɵtext(118);\n    i0.ɵɵelementStart(119, \"span\", 4);\n    i0.ɵɵtext(120, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(121, \"div\", 29)(122, \"input\", 45);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_122_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.parameterInfo.prDisplayName = $event);\n    })(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_122_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.checkExistParamDisplay());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(123, \"div\", 7);\n    i0.ɵɵelement(124, \"label\", 3);\n    i0.ɵɵelementStart(125, \"div\", 8);\n    i0.ɵɵtemplate(126, TabReportDynamicGeneral_div_0_small_126_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(127, TabReportDynamicGeneral_div_0_small_127_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(128, TabReportDynamicGeneral_div_0_small_128_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(129, TabReportDynamicGeneral_div_0_small_129_Template, 2, 3, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(130, \"div\", 2)(131, \"label\", 46);\n    i0.ɵɵtext(132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(133, \"div\", 29)(134, \"p-checkbox\", 47);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_134_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.parameterInfo.required = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(135, \"div\", 2)(136, \"label\", 48);\n    i0.ɵɵtext(137);\n    i0.ɵɵelementStart(138, \"span\", 4);\n    i0.ɵɵtext(139, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(140, \"div\", 29)(141, \"p-dropdown\", 49);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_141_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.parameterInfo.prType = $event);\n    })(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_141_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.changeParamType());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(142, \"div\", 7);\n    i0.ɵɵelement(143, \"label\", 3);\n    i0.ɵɵelementStart(144, \"div\", 8);\n    i0.ɵɵtemplate(145, TabReportDynamicGeneral_div_0_small_145_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(146, \"div\", 2)(147, \"label\", 50);\n    i0.ɵɵtext(148);\n    i0.ɵɵelementStart(149, \"span\", 4);\n    i0.ɵɵtext(150, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(151, \"div\", 29)(152, \"p-dropdown\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_152_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.parameterInfo.dateType = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(153, \"div\", 7);\n    i0.ɵɵelement(154, \"label\", 52);\n    i0.ɵɵelementStart(155, \"div\", 8);\n    i0.ɵɵtemplate(156, TabReportDynamicGeneral_div_0_small_156_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(157, \"div\", 53)(158, \"div\", 54)(159, \"label\", 55);\n    i0.ɵɵtext(160);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(161, \"div\", 29)(162, \"p-checkbox\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_162_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.parameterInfo.isAutoComplete = $event);\n    })(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_162_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.changeIsAutoComplete());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(163, \"div\", 54)(164, \"label\", 57);\n    i0.ɵɵtext(165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(166, \"div\", 29)(167, \"p-checkbox\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_167_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.parameterInfo.isMultiChoice = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(168, \"div\", 59)(169, \"label\", 60);\n    i0.ɵɵtext(170);\n    i0.ɵɵelementStart(171, \"span\", 4);\n    i0.ɵɵtext(172, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(173, \"div\", 29)(174, \"p-dropdown\", 61);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_174_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.parameterInfo.objectKey = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(175, \"div\", 62);\n    i0.ɵɵelement(176, \"label\", 42);\n    i0.ɵɵelementStart(177, \"div\", 8);\n    i0.ɵɵtemplate(178, TabReportDynamicGeneral_div_0_small_178_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(179, TabReportDynamicGeneral_div_0_small_179_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(180, TabReportDynamicGeneral_div_0_small_180_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(181, \"div\", 59)(182, \"label\", 63);\n    i0.ɵɵtext(183);\n    i0.ɵɵelementStart(184, \"span\", 4);\n    i0.ɵɵtext(185, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(186, \"div\", 29)(187, \"input\", 64);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_187_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.parameterInfo.input = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(188, \"div\", 62);\n    i0.ɵɵelement(189, \"label\", 42);\n    i0.ɵɵelementStart(190, \"div\", 8);\n    i0.ɵɵtemplate(191, TabReportDynamicGeneral_div_0_small_191_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(192, TabReportDynamicGeneral_div_0_small_192_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(193, TabReportDynamicGeneral_div_0_small_193_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(194, \"div\", 59)(195, \"label\", 65);\n    i0.ɵɵtext(196);\n    i0.ɵɵelementStart(197, \"span\", 4);\n    i0.ɵɵtext(198, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(199, \"div\", 29)(200, \"input\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_200_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.parameterInfo.output = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(201, \"div\", 62);\n    i0.ɵɵelement(202, \"label\", 42);\n    i0.ɵɵelementStart(203, \"div\", 8);\n    i0.ɵɵtemplate(204, TabReportDynamicGeneral_div_0_small_204_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(205, TabReportDynamicGeneral_div_0_small_205_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(206, TabReportDynamicGeneral_div_0_small_206_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(207, \"div\", 59)(208, \"label\", 67);\n    i0.ɵɵtext(209);\n    i0.ɵɵelementStart(210, \"span\", 4);\n    i0.ɵɵtext(211, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(212, \"div\", 29)(213, \"input\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function TabReportDynamicGeneral_div_0_Template_input_ngModelChange_213_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.parameterInfo.displayPattern = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(214, \"div\", 62);\n    i0.ɵɵelement(215, \"label\", 42);\n    i0.ɵɵelementStart(216, \"div\", 8);\n    i0.ɵɵtemplate(217, TabReportDynamicGeneral_div_0_small_217_Template, 2, 1, \"small\", 9);\n    i0.ɵɵtemplate(218, TabReportDynamicGeneral_div_0_small_218_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(219, TabReportDynamicGeneral_div_0_small_219_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(220, TabReportDynamicGeneral_div_0_div_220_Template, 9, 5, \"div\", 69);\n    i0.ɵɵelementStart(221, \"div\", 62);\n    i0.ɵɵelement(222, \"label\", 42);\n    i0.ɵɵelementStart(223, \"div\", 8);\n    i0.ɵɵtemplate(224, TabReportDynamicGeneral_div_0_small_224_Template, 2, 2, \"small\", 9);\n    i0.ɵɵtemplate(225, TabReportDynamicGeneral_div_0_small_225_Template, 2, 1, \"small\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(226, \"div\", 70)(227, \"table-input-vnpt\", 71);\n    i0.ɵɵlistener(\"valueChange\", function TabReportDynamicGeneral_div_0_Template_table_input_vnpt_valueChange_227_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.parameterInfo.valueList = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(228, \"div\", 21)(229, \"p-button\", 22);\n    i0.ɵɵlistener(\"click\", function TabReportDynamicGeneral_div_0_Template_p_button_click_229_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.isShowDialogParameter = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(230, TabReportDynamicGeneral_div_0_p_button_230_Template, 1, 2, \"p-button\", 23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.formGeneralInfo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.reportName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.generalInfo.name)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputReportName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formGeneralInfo.controls.name.dirty && (ctx_r0.formGeneralInfo.controls.name.errors == null ? null : ctx_r0.formGeneralInfo.controls.name.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formGeneralInfo.controls.name.errors == null ? null : ctx_r0.formGeneralInfo.controls.name.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formGeneralInfo.controls.name.errors == null ? null : ctx_r0.formGeneralInfo.controls.name.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isReportNameExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.reportStatus\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r0.generalInfo.status)(\"options\", ctx_r0.statusReports);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.reportEnablePreview\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.generalInfo.enablePreview)(\"trueValue\", ctx_r0.reportPreview.ENABLE)(\"falseValue\", ctx_r0.reportPreview.DISABLE);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r0.generalInfo.description)(\"maxlength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"sim.text.inputDescription\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formGeneralInfo.controls.description.errors == null ? null : ctx_r0.formGeneralInfo.controls.description.errors.maxLength);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.tableList\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.modeView != ctx_r0.objectMode.DETAIL);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"columns\", ctx_r0.tableColumns)(\"dataSet\", ctx_r0.dataTables)(\"options\", ctx_r0.optionTableListTable);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.paramList\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.modeView != ctx_r0.objectMode.DETAIL);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"columns\", ctx_r0.paramColumns)(\"dataSet\", ctx_r0.dataParams)(\"options\", ctx_r0.optionTableListParam)(\"isRowDraggable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.modeView != ctx_r0.objectMode.DETAIL);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(188, _c3));\n    i0.ɵɵproperty(\"header\", ctx_r0.getHeaderTable())(\"visible\", ctx_r0.isShowDialogTable)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.formTable);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.tableName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.tableInfo.tableName)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputTableName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formTable.controls.tableName.dirty && (ctx_r0.formTable.controls.tableName.errors == null ? null : ctx_r0.formTable.controls.tableName.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formTable.controls.tableName.errors == null ? null : ctx_r0.formTable.controls.tableName.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formTable.controls.tableName.errors == null ? null : ctx_r0.formTable.controls.tableName.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTableNameExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.schema\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r0.tableInfo.schema)(\"options\", ctx_r0.schemas)(\"required\", true)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.selectSchema\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formTable.controls.schema.dirty && (ctx_r0.formTable.controls.schema.errors == null ? null : ctx_r0.formTable.controls.schema.errors.required));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.query\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r0.tableInfo.query)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputQuery\"))(\"required\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r0.tranService.translate(\"global.button.copy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formTable.controls.query.dirty && (ctx_r0.formTable.controls.query.errors == null ? null : ctx_r0.formTable.controls.query.errors.required));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r0.tableInfo.columns)(\"columns\", ctx_r0.columnTableInput)(\"options\", ctx_r0.optionTableInput)(\"control\", ctx_r0.tableInputControl)(\"showMove\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.modeTable != ctx_r0.objectMode.DETAIL);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(189, _c3));\n    i0.ɵɵproperty(\"header\", ctx_r0.getHeaderParameter())(\"visible\", ctx_r0.isShowDialogParameter)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.formParameter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.paramKey\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.parameterInfo.prKey)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputParamKey\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prKey.dirty && (ctx_r0.formParameter.controls.prKey.errors == null ? null : ctx_r0.formParameter.controls.prKey.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prKey.errors == null ? null : ctx_r0.formParameter.controls.prKey.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prKey.errors == null ? null : ctx_r0.formParameter.controls.prKey.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isParamKeyExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.paramDisplay\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.parameterInfo.prDisplayName)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputDisplayName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prDisplayName.dirty && (ctx_r0.formParameter.controls.prDisplayName.errors == null ? null : ctx_r0.formParameter.controls.prDisplayName.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prDisplayName.errors == null ? null : ctx_r0.formParameter.controls.prDisplayName.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prDisplayName.errors == null ? null : ctx_r0.formParameter.controls.prDisplayName.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isParamDisplayExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.required\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"trueValue\", true)(\"falseValue\", false)(\"ngModel\", ctx_r0.parameterInfo.required);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.paramType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r0.parameterInfo.prType)(\"options\", ctx_r0.parameterTypes)(\"required\", true)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.selectParamType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.prType.dirty && (ctx_r0.formParameter.controls.prType.errors == null ? null : ctx_r0.formParameter.controls.prType.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.prType == ctx_r0.objectType.DATE || ctx_r0.parameterInfo.prType == ctx_r0.objectType.TIMESTAMP ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.dateType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r0.parameterInfo.dateType)(\"options\", ctx_r0.dateTypes)(\"required\", ctx_r0.parameterInfo.prType == ctx_r0.objectType.DATE || ctx_r0.parameterInfo.prType == ctx_r0.objectType.TIMESTAMP)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.selectDateType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.dateType.dirty && (ctx_r0.formParameter.controls.dateType.errors == null ? null : ctx_r0.formParameter.controls.dateType.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_NUMBER || ctx_r0.parameterInfo.prType == ctx_r0.objectType.STRING || ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_STRING ? \"\" : \"hidden\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.prType == ctx_r0.objectType.STRING || ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_STRING ? \"\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.isAutoComplete\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"trueValue\", true)(\"falseValue\", false)(\"ngModel\", ctx_r0.parameterInfo.isAutoComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_NUMBER || ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_STRING ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.isMultiChoice\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"trueValue\", true)(\"falseValue\", false)(\"ngModel\", ctx_r0.parameterInfo.isMultiChoice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.isAutoComplete == true ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.objectKey\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r0.parameterInfo.objectKey)(\"options\", ctx_r0.listObjectKey)(\"required\", ctx_r0.parameterInfo.isAutoComplete == true)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputObjectKey\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.objectKey.dirty && (ctx_r0.formParameter.controls.objectKey.errors == null ? null : ctx_r0.formParameter.controls.objectKey.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.objectKey.errors == null ? null : ctx_r0.formParameter.controls.objectKey.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.objectKey.errors == null ? null : ctx_r0.formParameter.controls.objectKey.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.isAutoComplete == true ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.input\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.parameterInfo.input)(\"required\", ctx_r0.parameterInfo.isAutoComplete == true)(\"maxLength\", 16)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputInput\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.input.dirty && (ctx_r0.formParameter.controls.input.errors == null ? null : ctx_r0.formParameter.controls.input.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.input.errors == null ? null : ctx_r0.formParameter.controls.input.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.input.errors == null ? null : ctx_r0.formParameter.controls.input.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.isAutoComplete == true ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.output\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.parameterInfo.output)(\"required\", ctx_r0.parameterInfo.isAutoComplete == true)(\"maxLength\", 16)(\"placeholder\", ctx_r0.tranService.translate(\"report.text.inputOutput\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.output.dirty && (ctx_r0.formParameter.controls.output.errors == null ? null : ctx_r0.formParameter.controls.output.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.output.errors == null ? null : ctx_r0.formParameter.controls.output.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.output.errors == null ? null : ctx_r0.formParameter.controls.output.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.parameterInfo.isAutoComplete == true ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"report.label.displayPattern\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.parameterInfo.displayPattern)(\"required\", ctx_r0.parameterInfo.isAutoComplete == true)(\"maxLength\", 255);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.displayPattern.dirty && (ctx_r0.formParameter.controls.displayPattern.errors == null ? null : ctx_r0.formParameter.controls.displayPattern.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.displayPattern.errors == null ? null : ctx_r0.formParameter.controls.displayPattern.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.displayPattern.errors == null ? null : ctx_r0.formParameter.controls.displayPattern.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.parameterInfo.isAutoComplete && (ctx_r0.parameterInfo.prType == ctx_r0.CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || ctx_r0.parameterInfo.prType == ctx_r0.CONSTANTS.PARAMETER_TYPE.LIST_STRING));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.queryParam.errors == null ? null : ctx_r0.formParameter.controls.queryParam.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formParameter.controls.queryParam.errors == null ? null : ctx_r0.formParameter.controls.queryParam.errors.pattern);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap((ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_NUMBER || ctx_r0.parameterInfo.prType == ctx_r0.objectType.LIST_STRING) && ctx_r0.parameterInfo.isAutoComplete == false ? \"\" : \"hidden\");\n    i0.ɵɵproperty(\"value\", ctx_r0.parameterInfo.valueList)(\"columns\", ctx_r0.columnParamInput)(\"options\", ctx_r0.optionParamInput)(\"control\", ctx_r0.paramInputControl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.modeParameter != ctx_r0.objectMode.DETAIL);\n  }\n}\nexport class TabGeneralDynamicReportControl {}\nexport class TabReportDynamicGeneral extends ComponentBase {\n  constructor(injector, formBuilder, reportService) {\n    super(injector);\n    this.formBuilder = formBuilder;\n    this.reportService = reportService;\n    this.oldReportName = null;\n    this.isReportNameExisted = false;\n    this.objectMode = CONSTANTS.MODE_VIEW;\n    this.objectType = CONSTANTS.PARAMETER_TYPE;\n    this.modeTable = CONSTANTS.MODE_VIEW.CREATE;\n    this.modeParameter = CONSTANTS.MODE_VIEW.CREATE;\n    this.isShowDialogTable = false;\n    this.isShowDialogParameter = false;\n    this.isTableNameExisted = false;\n    this.isParamKeyExisted = false;\n    this.isParamDisplayExisted = false;\n    this.indexFakeTable = -1;\n    this.indexFakeParameter = -1;\n    this.reportPreview = CONSTANTS.REPORT_PREVIEW;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.oldReportName = this.generalInfo ? this.generalInfo.name : null;\n    this.statusReports = [{\n      value: CONSTANTS.REPORT_STATUS.ACTIVE,\n      name: this.tranService.translate(\"report.status.active\")\n    }, {\n      value: CONSTANTS.REPORT_STATUS.INACTIVE,\n      name: this.tranService.translate(\"report.status.inactive\")\n    }];\n    //table of table content\n    this.tableColumns = [{\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      key: \"tableName\",\n      name: this.tranService.translate(\"report.label.tableName\"),\n      size: \"750px\",\n      className: \"text-cyan-500 cursor-pointer\",\n      funcClick: (id, item) => {\n        me.tableInfo = {\n          ...item\n        };\n        me.formTable = this.formBuilder.group(this.tableInfo);\n        me.formTable.get(\"tableName\").disable({\n          onlySelf: true\n        });\n        me.formTable.get(\"schema\").disable({\n          onlySelf: true\n        });\n        me.formTable.get(\"query\").disable({\n          onlySelf: true\n        });\n        me.isTableNameExisted = false;\n        me.tableInfo.columns = [];\n        let listDisplayName = me.tableInfo.columnDisplay.split(\",\");\n        let listQueryName = me.tableInfo.columnQueryResult.split(\",\");\n        listDisplayName.forEach((el, index) => {\n          me.tableInfo.columns.push({\n            id: null,\n            display: el,\n            key: listQueryName[index]\n          });\n        });\n        me.optionTableInput.mode = CONSTANTS.MODE_VIEW.DETAIL;\n        me.tableInputControl.reset();\n        me.modeTable = CONSTANTS.MODE_VIEW.DETAIL;\n        me.isShowDialogTable = true;\n      }\n    }];\n    this.optionTableListTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: false,\n      hasShowJumpPage: false,\n      paginator: false,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-external-link\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: (id, item) => {\n          me.tableInfo = {\n            ...item\n          };\n          me.formTable = me.formBuilder.group(me.tableInfo);\n          me.isTableNameExisted = false;\n          me.tableInfo.columns = [];\n          let listDisplayName = me.tableInfo.columnDisplay.split(\",\");\n          let listQueryName = me.tableInfo.columnQueryResult.split(\",\");\n          listDisplayName.forEach((el, index) => {\n            me.tableInfo.columns.push({\n              id: index + 1,\n              display: el,\n              key: listQueryName[index]\n            });\n          });\n          me.optionTableInput.mode = CONSTANTS.MODE_VIEW.UPDATE;\n          me.tableInputControl.reset();\n          me.modeTable = CONSTANTS.MODE_VIEW.UPDATE;\n          me.isShowDialogTable = true;\n        },\n        funcAppear(id, item) {\n          return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\n        }\n      }, {\n        icon: \"pi pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: (id, item) => {\n          for (let i = 0; i < me.dataTables.content.length; i++) {\n            if (me.dataTables.content[i].id == id) {\n              me.dataTables.content.splice(i, 1);\n              me.generalInfo.reportContents = me.dataTables.content;\n              break;\n            }\n          }\n        },\n        funcAppear(id, item) {\n          return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\n        }\n      }]\n    };\n    this.schemas = [{\n      value: CONSTANTS.SCHEMA.BILL,\n      name: this.tranService.translate(\"report.schema.bill\")\n    }, {\n      value: CONSTANTS.SCHEMA.CORE,\n      name: this.tranService.translate(\"report.schema.core\")\n    }, {\n      value: CONSTANTS.SCHEMA.LOG,\n      name: this.tranService.translate(\"report.schema.log\")\n    }, {\n      value: CONSTANTS.SCHEMA.MONITOR,\n      name: this.tranService.translate(\"report.schema.monitor\")\n    }, {\n      value: CONSTANTS.SCHEMA.REPORT,\n      name: this.tranService.translate(\"report.schema.report\")\n    }, {\n      value: CONSTANTS.SCHEMA.RULE,\n      name: this.tranService.translate(\"report.schema.rule\")\n    }, {\n      value: CONSTANTS.SCHEMA.SIM,\n      name: this.tranService.translate(\"report.schema.sim\")\n    }, {\n      value: CONSTANTS.SCHEMA.ELASTICSEARCH,\n      name: this.tranService.translate(\"report.schema.elasticsearch\")\n    }];\n    this.parameterTypes = [{\n      value: CONSTANTS.PARAMETER_TYPE.NUMBER,\n      name: this.tranService.translate(\"report.paramType.number\")\n    }, {\n      value: CONSTANTS.PARAMETER_TYPE.STRING,\n      name: this.tranService.translate(\"report.paramType.string\")\n    }, {\n      value: CONSTANTS.PARAMETER_TYPE.DATE,\n      name: this.tranService.translate(\"report.paramType.date\")\n    }, {\n      value: CONSTANTS.PARAMETER_TYPE.LIST_NUMBER,\n      name: this.tranService.translate(\"report.paramType.listNumber\")\n    }, {\n      value: CONSTANTS.PARAMETER_TYPE.LIST_STRING,\n      name: this.tranService.translate(\"report.paramType.listString\")\n    }, {\n      value: CONSTANTS.PARAMETER_TYPE.TIMESTAMP,\n      name: this.tranService.translate(\"report.paramType.timestamp\")\n    }];\n    this.dateTypes = [{\n      value: CONSTANTS.DATE_TYPE.MONTH,\n      name: this.tranService.translate(\"report.datetype.month\")\n    }, {\n      value: CONSTANTS.DATE_TYPE.DATE,\n      name: this.tranService.translate(\"report.datetype.date\")\n    }, {\n      value: CONSTANTS.DATE_TYPE.DATETIME,\n      name: this.tranService.translate(\"report.datetype.datetime\")\n    }];\n    //valueList tabe of table parameters\n    this.paramColumns = [{\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      key: \"prKey\",\n      name: this.tranService.translate(\"report.label.paramKey\"),\n      size: \"250px\",\n      className: \"text-cyan-500 cursor-pointer\",\n      funcClick: (id, item) => {\n        me.isParamKeyExisted = false;\n        me.isParamDisplayExisted = false;\n        me.parameterInfo = {\n          ...item\n        };\n        if (me.parameterInfo.required == undefined || me.parameterInfo.required == null) {\n          me.parameterInfo.required = false;\n        }\n        setTimeout(function () {\n          me.parameterInfo.valueList = item.valueList || [];\n          me.optionParamInput.mode = CONSTANTS.MODE_VIEW.DETAIL;\n          me.paramInputControl.reset();\n        });\n        me.formParameter = me.formBuilder.group(me.parameterInfo);\n        Object.keys(me.parameterInfo).forEach(key => {\n          me.formParameter.get(key).disable();\n        });\n        me.modeParameter = CONSTANTS.MODE_VIEW.DETAIL;\n        me.isShowDialogParameter = true;\n      }\n    }, {\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      key: \"prType\",\n      name: this.tranService.translate(\"report.label.paramType\"),\n      size: \"150px\",\n      funcConvertText(value) {\n        if (value == CONSTANTS.PARAMETER_TYPE.NUMBER) {\n          return me.tranService.translate(\"report.paramType.number\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.STRING) {\n          return me.tranService.translate(\"report.paramType.string\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.DATE) {\n          return me.tranService.translate(\"report.paramType.date\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER) {\n          return me.tranService.translate(\"report.paramType.listNumber\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n          return me.tranService.translate(\"report.paramType.listString\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.TIMESTAMP) {\n          return me.tranService.translate(\"report.paramType.timestamp\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {\n          return me.tranService.translate(\"report.paramType.recentlyDateFrom\");\n        } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {\n          return me.tranService.translate(\"report.paramType.recentlyDateTo\");\n        }\n        return \"\";\n      }\n    }, {\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      key: \"prDisplayName\",\n      name: this.tranService.translate(\"report.label.paramDisplay\"),\n      size: \"350px\"\n    }];\n    this.optionTableListParam = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: false,\n      hasShowJumpPage: false,\n      paginator: false,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-external-link\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: (id, item) => {\n          me.isParamKeyExisted = false;\n          me.isParamDisplayExisted = false;\n          me.parameterInfo = {\n            ...item\n          };\n          if (me.parameterInfo.required == undefined || me.parameterInfo.required == null) {\n            me.parameterInfo.required = false;\n          }\n          setTimeout(function () {\n            me.parameterInfo.valueList = item.valueList || [];\n            me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;\n            me.paramInputControl.reset();\n          });\n          me.formParameter = me.formBuilder.group(me.parameterInfo);\n          me.modeParameter = CONSTANTS.MODE_VIEW.UPDATE;\n          me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;\n          me.paramInputControl.reset();\n          me.isShowDialogParameter = true;\n        },\n        funcAppear(id, item) {\n          return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\n        }\n      }, {\n        icon: \"pi pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: (id, item) => {\n          for (let i = 0; i < me.dataParams.content.length; i++) {\n            if (me.dataParams.content[i].id == id) {\n              me.dataParams.content.splice(i, 1);\n              me.generalInfo.filterParams = JSON.stringify(me.dataParams.content);\n              break;\n            }\n          }\n        },\n        funcAppear(id, item) {\n          return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\n        }\n      }]\n    };\n    this.tableInfo = {\n      id: null,\n      query: null,\n      columnDisplay: null,\n      columnQueryResult: null,\n      reportConfigId: null,\n      schema: null,\n      tableName: null,\n      columns: null\n    };\n    this.formTable = this.formBuilder.group(this.tableInfo);\n    this.parameterInfo = {\n      id: null,\n      prDisplayName: null,\n      prKey: null,\n      prType: null,\n      valueList: null,\n      dateType: null,\n      displayPattern: null,\n      input: null,\n      isAutoComplete: false,\n      isMultiChoice: false,\n      objectKey: null,\n      output: null,\n      queryInfo: null,\n      required: false,\n      queryParam: null,\n      prValue: null\n    };\n    this.formParameter = this.formBuilder.group(this.parameterInfo);\n    //table of fields of table\n    this.columnTableInput = [{\n      align: 'left',\n      key: \"display\",\n      name: this.tranService.translate(\"report.label.fieldDisplay\"),\n      size: \"280px\",\n      type: CONSTANTS.PARAMETER_TYPE.STRING,\n      validate: {\n        required: true,\n        maxLength: 255,\n        pattern: /^[^~`!@#\\$%\\^&*=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$/,\n        messageErrorPattern: me.tranService.translate(\"global.message.formatContainVN\"),\n        exists: true\n      }\n    }, {\n      align: 'left',\n      key: \"key\",\n      name: this.tranService.translate(\"report.label.fieldData\"),\n      size: \"280px\",\n      type: CONSTANTS.PARAMETER_TYPE.STRING,\n      validate: {\n        required: true,\n        maxLength: 255,\n        pattern: /^[a-zA-Z0-9\\-_]*$/,\n        messageErrorPattern: this.tranService.translate(\"global.message.formatCode\"),\n        exists: true\n      }\n    }];\n    this.optionTableInput = {\n      mode: CONSTANTS.MODE_VIEW.CREATE\n    };\n    this.tableInputControl = new TableInputControl();\n    //table of value parameters\n    this.columnParamInput = [{\n      align: 'left',\n      key: \"display\",\n      name: this.tranService.translate(\"report.label.valueDisplay\"),\n      size: \"280px\",\n      type: CONSTANTS.PARAMETER_TYPE.STRING,\n      validate: {\n        required: true,\n        maxLength: 255,\n        exists: true,\n        pattern: /^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$/,\n        messageErrorPattern: me.tranService.translate(\"global.message.formatContainVN\")\n      }\n    }, {\n      align: 'left',\n      key: \"value\",\n      name: this.tranService.translate(\"report.label.valueDB\"),\n      size: \"280px\",\n      type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,\n      validate: {\n        required: true,\n        maxLength: 255,\n        pattern: /^[a-zA-Z0-9\\-_]+$/,\n        messageErrorPattern: me.tranService.translate(\"global.message.formatCode\"),\n        exists: true\n      }\n    }];\n    this.optionParamInput = {\n      mode: CONSTANTS.MODE_VIEW.CREATE\n    };\n    this.paramInputControl = new TableInputControl();\n    this.listObjectKey = ARRAY_SERVICE.map(el => {\n      return {\n        value: el.name,\n        display: this.tranService.translate(el.display)\n      };\n    });\n    this.control.reload = this.onload.bind(this);\n  }\n  onload() {\n    let me = this;\n    this.formGeneralInfo = undefined;\n    this.isReportNameExisted = false;\n    setTimeout(function () {\n      if (me.generalInfo.filterParams) {\n        me.generalInfo.filters = JSON.parse(me.generalInfo.filterParams);\n        let idParam = 1;\n        me.generalInfo.filters.forEach(el => {\n          el.id = idParam++;\n          if (el.dateType == undefined) {\n            el.dateType = null;\n          }\n          if (!el.isAutoComplete) {\n            el.isAutoComplete = false;\n          }\n          if (!el.isMultiChoice) {\n            el.isMultiChoice = false;\n          }\n          if (el.queryInfo != undefined && el.queryInfo != null) {\n            el.objectKey = el.queryInfo.objectKey;\n            el.input = el.queryInfo.input;\n            el.output = el.queryInfo.output;\n            el.displayPattern = el.queryInfo.displayPattern;\n            el.queryParam = el.queryInfo.queryParam;\n          } else {\n            el.objectKey = null;\n            el.input = null;\n            el.output = null;\n            el.displayPattern = null;\n            el.queryParam = null;\n          }\n        });\n      } else {\n        me.generalInfo.filters = [];\n      }\n      me.oldReportName = me.generalInfo ? me.generalInfo.name : null;\n      me.formGeneralInfo = me.formBuilder.group(me.generalInfo);\n      if (me.modeView == CONSTANTS.MODE_VIEW.DETAIL) {\n        me.formGeneralInfo.get(\"name\").disable();\n        me.formGeneralInfo.get(\"status\").disable();\n        me.formGeneralInfo.get(\"enablePreview\").disable();\n        me.formGeneralInfo.get(\"description\").disable();\n      }\n      me.dataTables = {\n        content: me.generalInfo.reportContents,\n        total: me.generalInfo.reportContents.length\n      };\n      me.dataParams = {\n        content: me.generalInfo.filters,\n        total: me.generalInfo.filters.length\n      };\n    });\n  }\n  ngAfterContentChecked() {}\n  checkExistReportName() {\n    if (this.generalInfo.name == this.oldReportName || this.generalInfo.name == null) return;\n    let me = this;\n    this.debounceService.set(\"reportDynamicName\", this.reportService.checkExistNameReportDynamic.bind(this.reportService), this.generalInfo.name, reponse => {\n      if (reponse == 0) {\n        me.isReportNameExisted = false;\n      } else {\n        me.isReportNameExisted = true;\n      }\n    });\n  }\n  enablePreviewChecked(event) {\n    if (event.checked.length > 0) {\n      this.generalInfo.enablePreview = CONSTANTS.REPORT_PREVIEW.ENABLE;\n    } else {\n      this.generalInfo.enablePreview = CONSTANTS.REPORT_PREVIEW.DISABLE;\n    }\n  }\n  getHeaderParameter() {\n    if (this.modeParameter == CONSTANTS.MODE_VIEW.CREATE) {\n      return this.tranService.translate(\"report.text.createParameter\");\n    } else if (this.modeParameter == CONSTANTS.MODE_VIEW.UPDATE) {\n      return this.tranService.translate(\"report.text.updateParameter\");\n    } else {\n      return this.tranService.translate(\"report.text.detailParameter\");\n    }\n  }\n  getHeaderTable() {\n    if (this.modeTable == CONSTANTS.MODE_VIEW.CREATE) {\n      return this.tranService.translate(\"report.text.createTable\");\n    } else if (this.modeTable == CONSTANTS.MODE_VIEW.UPDATE) {\n      return this.tranService.translate(\"report.text.updateTable\");\n    } else {\n      return this.tranService.translate(\"report.text.detailTable\");\n    }\n  }\n  openCreateTable() {\n    this.tableInfo = {\n      id: null,\n      columnDisplay: null,\n      columnQueryResult: null,\n      query: null,\n      reportConfigId: this.generalInfo.id,\n      schema: null,\n      tableName: null,\n      columns: []\n    };\n    this.formTable = this.formBuilder.group(this.tableInfo);\n    this.isTableNameExisted = false;\n    this.modeTable = CONSTANTS.MODE_VIEW.CREATE;\n    this.optionTableInput.mode = CONSTANTS.MODE_VIEW.CREATE;\n    this.isShowDialogTable = true;\n    this.tableInputControl.reset();\n  }\n  openCreateParameter() {\n    this.parameterInfo = {\n      prDisplayName: null,\n      prKey: null,\n      prType: null,\n      id: null,\n      valueList: [],\n      required: false,\n      dateType: null,\n      isAutoComplete: false,\n      isMultiChoice: false,\n      queryInfo: null,\n      displayPattern: null,\n      input: null,\n      objectKey: null,\n      output: null,\n      queryParam: null,\n      prValue: null\n    };\n    this.formParameter = this.formBuilder.group(this.parameterInfo);\n    this.isParamDisplayExisted = false;\n    this.isParamKeyExisted = false;\n    this.optionParamInput.mode = CONSTANTS.MODE_VIEW.CREATE;\n    this.modeParameter = CONSTANTS.MODE_VIEW.CREATE;\n    this.isShowDialogParameter = true;\n    this.paramInputControl.reset();\n  }\n  onSubmit() {\n    let me = this;\n    if (this.formGeneralInfo.invalid || this.isReportNameExisted) return;\n    let data = {\n      ...this.generalInfo\n    };\n    data.reportContents.forEach(el => {\n      if (el.id < 0) {\n        el.id = null;\n      }\n    });\n    let dataFilter = data.filters || [];\n    dataFilter.forEach(el => {\n      delete el.id;\n      if (el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n        el.valueList.forEach(ele => {\n          delete ele.id;\n          if (ele['mode']) {\n            delete ele['mode'];\n          }\n        });\n      }\n    });\n    data.filterParams = JSON.stringify(dataFilter);\n    delete data.filters;\n    if (data.enablePreview == null) {\n      data.enablePreview = CONSTANTS.REPORT_PREVIEW.DISABLE;\n    }\n    me.messageCommonService.onload();\n    if (me.generalInfo.id == null) {\n      this.reportService.createGeneralReportDynamic(data, response => {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.generalInfo = response;\n        me.saveSuccess(me.generalInfo.id);\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    } else {\n      this.reportService.updateGeneralReportDynamic(me.generalInfo.id, data, response => {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.generalInfo = response;\n        me.onload();\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  checkExistTableName() {\n    for (let i = 0; i < this.generalInfo.reportContents.length; i++) {\n      if (this.generalInfo.reportContents[i].tableName == this.tableInfo.tableName && this.generalInfo.reportContents[i].id != this.tableInfo.id) {\n        this.isTableNameExisted = true;\n        return;\n      }\n    }\n    this.isTableNameExisted = false;\n  }\n  checkExistParamKey() {\n    for (let i = 0; i < this.generalInfo.filters.length; i++) {\n      if (this.generalInfo.filters[i].prKey == this.parameterInfo.prKey && this.generalInfo.filters[i].id != this.parameterInfo.id) {\n        this.isParamKeyExisted = true;\n        return;\n      }\n    }\n    this.isParamKeyExisted = false;\n  }\n  checkExistParamDisplay() {\n    for (let i = 0; i < this.generalInfo.filters.length; i++) {\n      if (this.generalInfo.filters[i].prDisplayName == this.parameterInfo.prDisplayName && this.generalInfo.filters[i].id != this.parameterInfo.id) {\n        this.isParamDisplayExisted = true;\n        return;\n      }\n    }\n    this.isParamDisplayExisted = false;\n  }\n  getTextSchema(value) {\n    for (let i = 0; i < this.schemas.length; i++) {\n      if (value == this.schemas[i].value) {\n        return this.schemas[i].name;\n      }\n    }\n    return \"\";\n  }\n  getTextParamType(value) {\n    for (let i = 0; i < this.parameterTypes.length; i++) {\n      if (value == this.parameterTypes[i].value) {\n        return this.parameterTypes[i].name;\n      }\n    }\n    return \"\";\n  }\n  saveTable() {\n    if (this.formTable.invalid || this.tableInfo.columns == null || this.tableInfo.columns == undefined || this.tableInfo.columns.length == 0 || this.tableInputControl.isUpdating == true || this.isTableNameExisted) return;\n    let data = {\n      ...this.tableInfo\n    };\n    data.columnDisplay = data.columns.map(el => el.display).toLocaleString();\n    data.columnQueryResult = data.columns.map(el => el.key).toLocaleString();\n    this.isShowDialogTable = false;\n    delete data.columns;\n    if (data.id == null) {\n      data.id = this.indexFakeTable--;\n      this.generalInfo.reportContents.push(data);\n    } else {\n      for (let i = 0; i < this.generalInfo.reportContents.length; i++) {\n        if (this.generalInfo.reportContents[i].id == data.id) {\n          this.generalInfo.reportContents[i] = data;\n          break;\n        }\n      }\n    }\n    this.dataTables.content = this.generalInfo.reportContents;\n    this.dataTables.total = this.generalInfo.reportContents.length;\n  }\n  saveParameter() {\n    if (this.checkInvalidFormParameter() == true) return;\n    let data = {\n      ...this.parameterInfo\n    };\n    data.queryInfo = {\n      displayPattern: data.displayPattern,\n      queryParam: data.queryParam,\n      input: data.input,\n      output: data.output,\n      objectKey: data.objectKey\n    };\n    this.isShowDialogParameter = false;\n    if (data.id == null) {\n      data.id = this.indexFakeParameter--;\n      this.generalInfo.filters.push(data);\n    } else {\n      for (let i = 0; i < this.generalInfo.filters.length; i++) {\n        if (this.generalInfo.filters[i].id == data.id) {\n          this.generalInfo.filters[i] = data;\n          break;\n        }\n      }\n    }\n    this.generalInfo.filterParams = JSON.stringify(this.generalInfo.filters);\n  }\n  checkInvalidFormParameter() {\n    if (this.formParameter.invalid) return true;\n    if (this.isParamDisplayExisted || this.isParamKeyExisted) return true;\n    if (this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n      if (this.parameterInfo.isAutoComplete == false) {\n        if (this.parameterInfo.valueList == null || this.parameterInfo.valueList == undefined || this.parameterInfo.valueList.length == 0) return true;\n      }\n    }\n    return false;\n  }\n  changeParamType() {\n    let me = this;\n    this.columnParamInput = [{\n      align: 'left',\n      key: \"display\",\n      name: this.tranService.translate(\"report.label.valueDisplay\"),\n      size: \"280px\",\n      type: CONSTANTS.PARAMETER_TYPE.STRING,\n      validate: {\n        required: true,\n        maxLength: 255,\n        exists: true,\n        pattern: /^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$/,\n        messageErrorPattern: me.tranService.translate(\"global.message.formatContainVN\")\n      }\n    }, {\n      align: 'left',\n      key: \"value\",\n      name: this.tranService.translate(\"report.label.valueDB\"),\n      size: \"280px\",\n      type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,\n      validate: {\n        required: true,\n        maxLength: 255,\n        // pattern: /^[a-zA-Z0-9\\-_]+$/,\n        // messageErrorPattern: me.tranService.translate(\"global.message.formatCode\"),\n        exists: true\n      }\n    }];\n    if (this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_NUMBER && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n      this.parameterInfo.isMultiChoice = false;\n    }\n    if (this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.STRING && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n      this.parameterInfo.isAutoComplete = false;\n    }\n    this.parameterInfo.valueList = [];\n    if (this.paramInputControl.reset) {\n      this.paramInputControl.reset();\n    }\n  }\n  changeIsAutoComplete() {}\n  copyText(event) {\n    let me = this;\n    let value = document.querySelector(\"textarea#query\");\n    if (value) {\n      let text = value[\"value\"];\n      me.utilService.copyToClipboard(text, () => {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.copied\"));\n      });\n    } else {\n      this.messageCommonService.warning(this.tranService.translate(\"global.message.error\"));\n    }\n  }\n  static {\n    this.ɵfac = function TabReportDynamicGeneral_Factory(t) {\n      return new (t || TabReportDynamicGeneral)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReportService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TabReportDynamicGeneral,\n      selectors: [[\"tab-report-dynamic-general\"]],\n      inputs: {\n        generalInfo: \"generalInfo\",\n        control: \"control\",\n        modeView: \"modeView\",\n        cancel: \"cancel\",\n        saveSuccess: \"saveSuccess\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [3, \"formGroup\"], [1, \"w-full\", \"field\", \"grid\", \"tab-report-grid\"], [\"htmlFor\", \"name\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\", 2, \"max-width\", \"500px\"], [\"pInputText\", \"\", \"id\", \"name\", \"formControlName\", \"name\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\", \"tab-report-grid\"], [1, \"col\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"for\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"name\", \"enablePreview\", \"binary\", \"true\", \"formControlName\", \"enablePreview\", 3, \"ngModel\", \"trueValue\", \"falseValue\", \"ngModelChange\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"rows\", \"3\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"pl-4\"], [\"class\", \"text-cyan-500 cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"scrollHeight\", \"300px\", 3, \"fieldId\", \"columns\", \"dataSet\", \"options\"], [1, \"mt-3\"], [\"scrollHeight\", \"300px\", 3, \"fieldId\", \"columns\", \"dataSet\", \"options\", \"isRowDraggable\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary p-button-outlined\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\", 2, \"top\", \"0\"], [\"styleClass\", \"dialog-report\", 3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\", \"grid-1\", \"p-0\", \"m-0\"], [1, \"w-full\", 3, \"formGroup\"], [\"htmlFor\", \"tableName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"400px\"], [\"pInputText\", \"\", \"id\", \"tableName\", \"formControlName\", \"tableName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"for\", \"schema\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"showClear\", \"true\", \"id\", \"schema\", \"formControlName\", \"schema\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"autoDisplayFirst\", \"ngModel\", \"options\", \"required\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"tab-report-grid-query\"], [\"htmlFor\", \"query\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"col\", 2, \"width\", \"calc(100% - 210px)\"], [\"rows\", \"3\", \"pInputTextarea\", \"\", \"id\", \"query\", \"formControlName\", \"query\", 1, \"w-full\", 2, \"resize\", \"vertical\", 3, \"autoResize\", \"ngModel\", \"placeholder\", \"required\", \"ngModelChange\"], [1, \"col-fixed\", \"pi\", \"pi-clone\", \"text-xl\", \"cursor-pointer\", 2, \"width\", \"30px\", \"height\", \"fit-content\", 3, \"pTooltip\", \"click\"], [1, \"w-full\", \"table-input-div\"], [\"fieldId\", \"id\", 3, \"value\", \"columns\", \"options\", \"control\", \"showMove\", \"valueChange\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [1, \"w-full\", \"field\", \"grid\", \"p-0\", \"m-0\"], [\"htmlFor\", \"prKey\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"prKey\", \"formControlName\", \"prKey\", \"pattern\", \"^[a-zA-Z0-9_]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"prDisplayName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"prDisplayName\", \"formControlName\", \"prDisplayName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"for\", \"required\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"binary\", \"true\", \"name\", \"required\", \"formControlName\", \"required\", 3, \"trueValue\", \"falseValue\", \"ngModel\", \"ngModelChange\"], [\"for\", \"prType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"showClear\", \"true\", \"id\", \"prType\", \"formControlName\", \"prType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"autoDisplayFirst\", \"ngModel\", \"options\", \"required\", \"placeholder\", \"ngModelChange\"], [\"for\", \"dateType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"showClear\", \"true\", \"id\", \"dateType\", \"formControlName\", \"dateType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"autoDisplayFirst\", \"ngModel\", \"options\", \"required\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"dateType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"w-full\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\"], [1, \"w-6\", \"field\", \"grid\"], [\"for\", \"isAutoComplete\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"binary\", \"true\", \"name\", \"isAutoComplete\", \"formControlName\", \"isAutoComplete\", 3, \"trueValue\", \"falseValue\", \"ngModel\", \"ngModelChange\"], [\"for\", \"isMultiChoice\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"name\", \"isMultiChoice\", \"binary\", \"true\", \"formControlName\", \"isMultiChoice\", 3, \"trueValue\", \"falseValue\", \"ngModel\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"objectKey\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"objectKey\", \"formControlName\", \"objectKey\", \"optionLabel\", \"display\", \"optionValue\", \"value\", 3, \"autoDisplayFirst\", \"ngModel\", \"options\", \"required\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"input\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"input\", \"formControlName\", \"input\", \"pattern\", \"^[a-zA-Z0-9\\\\-_]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"output\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"output\", \"formControlName\", \"output\", \"pattern\", \"^[a-zA-Z0-9\\\\-_]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"displayPattern\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"displayPattern\", \"formControlName\", \"displayPattern\", \"placeholder\", \"${var1} - ${var2}\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [1, \"w-full\"], [\"fieldId\", \"id\", 3, \"value\", \"columns\", \"options\", \"control\", \"valueChange\"], [1, \"text-cyan-500\", \"cursor-pointer\", 3, \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"disabled\", \"click\"], [\"htmlFor\", \"queryParam\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"pi\", \"pi-info-circle\", 3, \"pTooltip\"], [\"pInputText\", \"\", \"id\", \"queryParam\", \"formControlName\", \"queryParam\", \"pattern\", \"^(\\\\w+=(\\\\$\\\\w+|\\\"[^\\\"]*\\\")|\\\\w+=\\\\d+)(?:&(\\\\w+=(\\\\$\\\\w+|\\\"[^\\\"]*\\\")|\\\\w+=\\\\d+))*$\", 1, \"w-full\", 3, \"ngModel\", \"maxLength\", \"placeholder\", \"ngModelChange\"]],\n      template: function TabReportDynamicGeneral_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TabReportDynamicGeneral_div_0_Template, 231, 190, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.formGeneralInfo);\n        }\n      },\n      dependencies: [i3.NgIf, i4.Tooltip, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.FormGroupDirective, i1.FormControlName, i5.InputText, i6.Button, i7.TableVnptComponent, i8.TableInputComponent, i9.Dropdown, i10.Dialog, i11.InputTextarea, i12.Checkbox],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "ARRAY_SERVICE", "TableInputControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "tranService", "translate", "ctx_r2", "ɵɵpureFunction0", "_c0", "ctx_r3", "ctx_r4", "ɵɵpureFunction1", "_c1", "toLowerCase", "ctx_r5", "ɵɵlistener", "TabReportDynamicGeneral_div_0_div_40_Template_div_click_0_listener", "ɵɵrestoreView", "_r43", "ctx_r42", "ɵɵnextContext", "ɵɵresetView", "openCreateTable", "ctx_r6", "TabReportDynamicGeneral_div_0_div_47_Template_div_click_0_listener", "_r45", "ctx_r44", "openCreateParameter", "ctx_r7", "TabReportDynamicGeneral_div_0_p_button_51_Template_p_button_click_0_listener", "_r47", "ctx_r46", "onSubmit", "ɵɵproperty", "ctx_r8", "formGeneralInfo", "invalid", "isReportNameExisted", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "TabReportDynamicGeneral_div_0_p_button_97_Template_p_button_click_0_listener", "_r49", "ctx_r48", "saveTable", "ctx_r15", "formTable", "tableInfo", "columns", "undefined", "length", "tableInputControl", "isUpdating", "isTableNameExisted", "ctx_r16", "ctx_r17", "ctx_r18", "ctx_r19", "ctx_r20", "ctx_r21", "ctx_r22", "ctx_r23", "ctx_r24", "ctx_r25", "ctx_r26", "ctx_r27", "_c2", "ctx_r28", "ctx_r29", "ctx_r30", "ctx_r31", "ctx_r32", "ctx_r33", "ctx_r34", "ctx_r35", "ctx_r36", "ctx_r37", "ɵɵelement", "TabReportDynamicGeneral_div_0_div_220_Template_input_ngModelChange_8_listener", "$event", "_r51", "ctx_r50", "parameterInfo", "queryParam", "ɵɵtextInterpolate1", "ctx_r38", "ctx_r39", "ctx_r40", "TabReportDynamicGeneral_div_0_p_button_230_Template_p_button_click_0_listener", "_r53", "ctx_r52", "saveParameter", "ctx_r41", "checkInvalidFormParameter", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_8_listener", "_r55", "ctx_r54", "generalInfo", "name", "ctx_r56", "checkExistReportName", "ɵɵtemplate", "TabReportDynamicGeneral_div_0_small_12_Template", "TabReportDynamicGeneral_div_0_small_13_Template", "TabReportDynamicGeneral_div_0_small_14_Template", "TabReportDynamicGeneral_div_0_small_15_Template", "TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_20_listener", "ctx_r57", "status", "TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_25_listener", "ctx_r58", "enablePreview", "TabReportDynamicGeneral_div_0_Template_textarea_ngModelChange_30_listener", "ctx_r59", "description", "TabReportDynamicGeneral_div_0_small_34_Template", "TabReportDynamicGeneral_div_0_div_40_Template", "TabReportDynamicGeneral_div_0_div_47_Template", "TabReportDynamicGeneral_div_0_Template_p_button_click_50_listener", "ctx_r60", "cancel", "TabReportDynamicGeneral_div_0_p_button_51_Template", "TabReportDynamicGeneral_div_0_Template_p_dialog_visibleChange_53_listener", "ctx_r61", "isShowDialogTable", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_62_listener", "ctx_r62", "tableName", "ctx_r63", "checkExistTableName", "TabReportDynamicGeneral_div_0_small_66_Template", "TabReportDynamicGeneral_div_0_small_67_Template", "TabReportDynamicGeneral_div_0_small_68_Template", "TabReportDynamicGeneral_div_0_small_69_Template", "TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_76_listener", "ctx_r64", "schema", "TabReportDynamicGeneral_div_0_small_80_Template", "TabReportDynamicGeneral_div_0_Template_textarea_ngModelChange_87_listener", "ctx_r65", "query", "TabReportDynamicGeneral_div_0_Template_span_click_88_listener", "ctx_r66", "copyText", "TabReportDynamicGeneral_div_0_small_92_Template", "TabReportDynamicGeneral_div_0_Template_table_input_vnpt_valueChange_94_listener", "ctx_r67", "TabReportDynamicGeneral_div_0_Template_p_button_click_96_listener", "ctx_r68", "TabReportDynamicGeneral_div_0_p_button_97_Template", "TabReportDynamicGeneral_div_0_Template_p_dialog_visibleChange_99_listener", "ctx_r69", "isShowDialogParameter", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_108_listener", "ctx_r70", "pr<PERSON><PERSON>", "ctx_r71", "checkExistParamKey", "TabReportDynamicGeneral_div_0_small_112_Template", "TabReportDynamicGeneral_div_0_small_113_Template", "TabReportDynamicGeneral_div_0_small_114_Template", "TabReportDynamicGeneral_div_0_small_115_Template", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_122_listener", "ctx_r72", "prDisplayName", "ctx_r73", "checkExistParamDisplay", "TabReportDynamicGeneral_div_0_small_126_Template", "TabReportDynamicGeneral_div_0_small_127_Template", "TabReportDynamicGeneral_div_0_small_128_Template", "TabReportDynamicGeneral_div_0_small_129_Template", "TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_134_listener", "ctx_r74", "required", "TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_141_listener", "ctx_r75", "prType", "ctx_r76", "changeParamType", "TabReportDynamicGeneral_div_0_small_145_Template", "TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_152_listener", "ctx_r77", "dateType", "TabReportDynamicGeneral_div_0_small_156_Template", "TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_162_listener", "ctx_r78", "isAutoComplete", "ctx_r79", "changeIsAutoComplete", "TabReportDynamicGeneral_div_0_Template_p_checkbox_ngModelChange_167_listener", "ctx_r80", "isMultiChoice", "TabReportDynamicGeneral_div_0_Template_p_dropdown_ngModelChange_174_listener", "ctx_r81", "object<PERSON>ey", "TabReportDynamicGeneral_div_0_small_178_Template", "TabReportDynamicGeneral_div_0_small_179_Template", "TabReportDynamicGeneral_div_0_small_180_Template", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_187_listener", "ctx_r82", "input", "TabReportDynamicGeneral_div_0_small_191_Template", "TabReportDynamicGeneral_div_0_small_192_Template", "TabReportDynamicGeneral_div_0_small_193_Template", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_200_listener", "ctx_r83", "output", "TabReportDynamicGeneral_div_0_small_204_Template", "TabReportDynamicGeneral_div_0_small_205_Template", "TabReportDynamicGeneral_div_0_small_206_Template", "TabReportDynamicGeneral_div_0_Template_input_ngModelChange_213_listener", "ctx_r84", "displayPattern", "TabReportDynamicGeneral_div_0_small_217_Template", "TabReportDynamicGeneral_div_0_small_218_Template", "TabReportDynamicGeneral_div_0_small_219_Template", "TabReportDynamicGeneral_div_0_div_220_Template", "TabReportDynamicGeneral_div_0_small_224_Template", "TabReportDynamicGeneral_div_0_small_225_Template", "TabReportDynamicGeneral_div_0_Template_table_input_vnpt_valueChange_227_listener", "ctx_r85", "valueList", "TabReportDynamicGeneral_div_0_Template_p_button_click_229_listener", "ctx_r86", "TabReportDynamicGeneral_div_0_p_button_230_Template", "ctx_r0", "controls", "dirty", "errors", "max<PERSON><PERSON><PERSON>", "pattern", "statusReports", "reportPreview", "ENABLE", "DISABLE", "modeView", "objectMode", "DETAIL", "tableColumns", "dataTables", "optionTableListTable", "paramColumns", "dataParams", "optionTableListParam", "ɵɵstyleMap", "_c3", "getHeaderTable", "schemas", "columnTableInput", "optionTableInput", "modeTable", "getHeaderParameter", "formParameter", "isParamKeyExisted", "isParamDisplayExisted", "parameterTypes", "ɵɵclassMap", "objectType", "DATE", "TIMESTAMP", "dateTypes", "LIST_NUMBER", "STRING", "LIST_STRING", "listObjectKey", "PARAMETER_TYPE", "columnParamInput", "optionParamInput", "paramInputControl", "modeParameter", "TabGeneralDynamicReportControl", "TabReportDynamicGeneral", "constructor", "injector", "formBuilder", "reportService", "oldReportName", "MODE_VIEW", "CREATE", "indexFakeTable", "indexFakeParameter", "REPORT_PREVIEW", "ngOnInit", "me", "value", "REPORT_STATUS", "ACTIVE", "INACTIVE", "align", "isShow", "isSort", "key", "size", "className", "funcClick", "id", "item", "group", "get", "disable", "onlySelf", "listDisplayName", "columnDisplay", "split", "listQueryName", "columnQueryResult", "for<PERSON>ach", "el", "index", "push", "display", "mode", "reset", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowJumpPage", "paginator", "hasShowToggleColumn", "action", "icon", "tooltip", "func", "UPDATE", "funcAppear", "i", "content", "splice", "reportContents", "SCHEMA", "BILL", "CORE", "LOG", "MONITOR", "REPORT", "RULE", "SIM", "ELASTICSEARCH", "NUMBER", "DATE_TYPE", "MONTH", "DATETIME", "setTimeout", "Object", "keys", "funcConvertText", "RECENTLY_DATE_FROM", "RECENTLY_DATE_TO", "filterParams", "JSON", "stringify", "reportConfigId", "queryInfo", "prValue", "type", "validate", "messageErrorPattern", "exists", "map", "control", "reload", "onload", "bind", "filters", "parse", "idParam", "total", "ngAfterContentChecked", "debounceService", "set", "checkExistNameReportDynamic", "reponse", "enablePreviewChecked", "event", "checked", "data", "dataFilter", "ele", "messageCommonService", "createGeneralReportDynamic", "response", "success", "saveSuccess", "offload", "updateGeneralReportDynamic", "getTextSchema", "getTextParamType", "toLocaleString", "document", "querySelector", "text", "utilService", "copyToClipboard", "warning", "ɵɵdirectiveInject", "Injector", "i1", "FormBuilder", "i2", "ReportService", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "TabReportDynamicGeneral_Template", "rf", "ctx", "TabReportDynamicGeneral_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\report-dynamic\\components\\tab.report.dynamic.general.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\report-dynamic\\components\\tab.report.dynamic.general.html"], "sourcesContent": ["import { AfterContentChecked, Component, Injector, Input, OnInit } from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { ReportService } from \"src/app/service/report/ReportService\";\r\nimport { ARRAY_SERVICE } from \"src/app/template/common-module/combobox-lazyload/combobox.lazyload\";\r\nimport { ColumnInfo, OptionTable } from \"src/app/template/common-module/table/table.component\";\r\nimport { ColumnInputInfo, OptionTableInput, TableInputControl } from \"src/app/template/common-module/table/table.input.component\";\r\nexport interface TableReportInfo {\r\n    id: number|null;\r\n    reportConfigId: number | null;\r\n    tableName: string;\r\n    schema: string;\r\n    query: string;\r\n    columnDisplay: string;\r\n    columnQueryResult: string;\r\n    columns?: {id: number, display: string, key: string}[] | null;\r\n}\r\nexport interface GeneralInfo {\r\n    id: number | null;\r\n    name: string | null;\r\n    status: number | null;\r\n    enablePreview: number | number[] | null;\r\n    description: string | null;\r\n    reportContents: TableReportInfo[] | null,\r\n    filterParams: string | null;\r\n    filters?: ParameterInfo[];\r\n}\r\n\r\nexport interface ParameterInfo{\r\n    id?: number | null;\r\n    prKey: string|null;\r\n    prType: number|null;\r\n    prDisplayName: string|null;\r\n    valueList?: {\r\n        id?: number | null;\r\n        display: string | null;\r\n        value: string | number | null;\r\n    }[],\r\n    required: boolean,\r\n    isAutoComplete: boolean,\r\n    isMultiChoice: boolean,\r\n    objectKey?: string,\r\n    input?: string,\r\n    output?: string,\r\n    displayPattern?: string,\r\n    queryParam?: string,\r\n    queryInfo: {\r\n        objectKey: string,\r\n        input: string,\r\n        output: string,\r\n        displayPattern: string,\r\n        queryParam?: string,\r\n    },\r\n    dateType: number;\r\n    paramDefault?: any\r\n    prValue: any;\r\n}\r\nexport class TabGeneralDynamicReportControl{\r\n    reload: Function;\r\n}\r\n@Component({\r\n    selector: \"tab-report-dynamic-general\",\r\n    templateUrl: \"./tab.report.dynamic.general.html\"\r\n})\r\nexport class TabReportDynamicGeneral extends ComponentBase implements OnInit, AfterContentChecked{\r\n    constructor(injector: Injector,\r\n                private formBuilder: FormBuilder,\r\n                private reportService: ReportService) {\r\n        super(injector);\r\n    }\r\n\r\n    @Input() generalInfo!: GeneralInfo;\r\n    @Input() control!: TabGeneralDynamicReportControl;\r\n    @Input() modeView!: number;\r\n    @Input() cancel!: Function;\r\n    @Input() saveSuccess!: Function;\r\n    formGeneralInfo: any;\r\n    oldReportName: string = null;\r\n    isReportNameExisted: boolean = false;\r\n    statusReports: any[];\r\n    tableColumns: ColumnInfo[];\r\n    optionTableListTable: OptionTable;\r\n    dataTables: {\r\n        content: any[],\r\n        total: number;\r\n    }\r\n    paramColumns: ColumnInfo[];\r\n    optionTableListParam: OptionTable;\r\n    dataParams: {\r\n        content: any[],\r\n        total: number;\r\n    }\r\n    objectMode = CONSTANTS.MODE_VIEW;\r\n    objectType = CONSTANTS.PARAMETER_TYPE;\r\n    modeTable = CONSTANTS.MODE_VIEW.CREATE;\r\n    modeParameter = CONSTANTS.MODE_VIEW.CREATE;\r\n    isShowDialogTable: boolean = false;\r\n    isShowDialogParameter: boolean = false;\r\n    tableInfo: TableReportInfo;\r\n    parameterInfo: ParameterInfo;\r\n    formTable: any;\r\n    formParameter: any;\r\n    isTableNameExisted: boolean = false;\r\n    isParamKeyExisted: boolean = false;\r\n    isParamDisplayExisted: boolean = false;\r\n    schemas: any[];\r\n    parameterTypes: any[];\r\n    dateTypes: any[];\r\n\r\n    columnTableInput: ColumnInputInfo[];\r\n    optionTableInput: OptionTableInput;\r\n    tableInputControl: TableInputControl;\r\n\r\n    columnParamInput: ColumnInputInfo[];\r\n    optionParamInput: OptionTableInput;\r\n    paramInputControl: TableInputControl;\r\n\r\n    indexFakeTable = -1;\r\n    indexFakeParameter = -1;\r\n\r\n    listObjectKey: Array<any>;\r\n    reportPreview = CONSTANTS.REPORT_PREVIEW;\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.oldReportName = this.generalInfo ? this.generalInfo.name : null;\r\n        this.statusReports = [\r\n            {value: CONSTANTS.REPORT_STATUS.ACTIVE, name: this.tranService.translate(\"report.status.active\")},\r\n            {value: CONSTANTS.REPORT_STATUS.INACTIVE, name: this.tranService.translate(\"report.status.inactive\")}\r\n        ]\r\n\r\n        //table of table content\r\n        this.tableColumns =[{\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            key: \"tableName\",\r\n            name: this.tranService.translate(\"report.label.tableName\"),\r\n            size: \"750px\",\r\n            className: \"text-cyan-500 cursor-pointer\",\r\n            funcClick: (id, item)=>{\r\n                me.tableInfo = {...item};\r\n                me.formTable = this.formBuilder.group(this.tableInfo);\r\n                me.formTable.get(\"tableName\").disable({onlySelf: true});\r\n                me.formTable.get(\"schema\").disable({onlySelf: true});\r\n                me.formTable.get(\"query\").disable({onlySelf: true});\r\n                me.isTableNameExisted = false;\r\n                me.tableInfo.columns = [];\r\n                let listDisplayName = me.tableInfo.columnDisplay.split(\",\");\r\n                let listQueryName = me.tableInfo.columnQueryResult.split(\",\");\r\n                listDisplayName.forEach( (el, index)=>{\r\n                    me.tableInfo.columns.push({\r\n                        id: null,\r\n                        display: el,\r\n                        key: listQueryName[index]\r\n                    })\r\n                })\r\n                me.optionTableInput.mode = CONSTANTS.MODE_VIEW.DETAIL;\r\n                me.tableInputControl.reset();\r\n                me.modeTable = CONSTANTS.MODE_VIEW.DETAIL;\r\n                me.isShowDialogTable = true;\r\n            }\r\n        }];\r\n        this.optionTableListTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: false,\r\n            hasShowJumpPage: false,\r\n            paginator: false,\r\n            hasShowToggleColumn: false,\r\n            action: [{\r\n                icon: \"pi pi-external-link\",\r\n                tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                func: (id, item) => {\r\n                    me.tableInfo = {...item};\r\n                    me.formTable = me.formBuilder.group(me.tableInfo);\r\n                    me.isTableNameExisted = false;\r\n                    me.tableInfo.columns = [];\r\n                    let listDisplayName = me.tableInfo.columnDisplay.split(\",\");\r\n                    let listQueryName = me.tableInfo.columnQueryResult.split(\",\");\r\n                    listDisplayName.forEach( (el, index)=>{\r\n                        me.tableInfo.columns.push({\r\n                            id: index + 1,\r\n                            display: el,\r\n                            key: listQueryName[index]\r\n                        })\r\n                    })\r\n                    me.optionTableInput.mode = CONSTANTS.MODE_VIEW.UPDATE;\r\n                    me.tableInputControl.reset();\r\n                    me.modeTable = CONSTANTS.MODE_VIEW.UPDATE;\r\n                    me.isShowDialogTable = true;\r\n                },\r\n                funcAppear(id, item) {\r\n                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\r\n                },\r\n            },{\r\n                icon: \"pi pi-trash\",\r\n                tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                func: (id, item) => {\r\n                    for(let i = 0; i < me.dataTables.content.length;i++){\r\n                        if(me.dataTables.content[i].id == id){\r\n                            me.dataTables.content.splice(i, 1);\r\n                            me.generalInfo.reportContents = me.dataTables.content;\r\n                            break;\r\n                        }\r\n                    }\r\n                },\r\n                funcAppear(id, item) {\r\n                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\r\n                },\r\n            }]\r\n        }\r\n\r\n        this.schemas = [\r\n            {value: CONSTANTS.SCHEMA.BILL, name: this.tranService.translate(\"report.schema.bill\")},\r\n            {value: CONSTANTS.SCHEMA.CORE, name: this.tranService.translate(\"report.schema.core\")},\r\n            {value: CONSTANTS.SCHEMA.LOG, name: this.tranService.translate(\"report.schema.log\")},\r\n            {value: CONSTANTS.SCHEMA.MONITOR, name: this.tranService.translate(\"report.schema.monitor\")},\r\n            {value: CONSTANTS.SCHEMA.REPORT, name: this.tranService.translate(\"report.schema.report\")},\r\n            {value: CONSTANTS.SCHEMA.RULE, name: this.tranService.translate(\"report.schema.rule\")},\r\n            {value: CONSTANTS.SCHEMA.SIM, name: this.tranService.translate(\"report.schema.sim\")},\r\n            {value: CONSTANTS.SCHEMA.ELASTICSEARCH, name: this.tranService.translate(\"report.schema.elasticsearch\")},\r\n        ];\r\n        this.parameterTypes = [\r\n            {value: CONSTANTS.PARAMETER_TYPE.NUMBER, name: this.tranService.translate(\"report.paramType.number\")},\r\n            {value: CONSTANTS.PARAMETER_TYPE.STRING, name: this.tranService.translate(\"report.paramType.string\")},\r\n            {value: CONSTANTS.PARAMETER_TYPE.DATE, name: this.tranService.translate(\"report.paramType.date\")},\r\n            {value: CONSTANTS.PARAMETER_TYPE.LIST_NUMBER, name: this.tranService.translate(\"report.paramType.listNumber\")},\r\n            {value: CONSTANTS.PARAMETER_TYPE.LIST_STRING, name: this.tranService.translate(\"report.paramType.listString\")},\r\n            {value: CONSTANTS.PARAMETER_TYPE.TIMESTAMP, name: this.tranService.translate(\"report.paramType.timestamp\")},\r\n        ]\r\n        this.dateTypes = [\r\n            {value: CONSTANTS.DATE_TYPE.MONTH, name: this.tranService.translate(\"report.datetype.month\")},\r\n            {value: CONSTANTS.DATE_TYPE.DATE, name: this.tranService.translate(\"report.datetype.date\")},\r\n            {value: CONSTANTS.DATE_TYPE.DATETIME, name: this.tranService.translate(\"report.datetype.datetime\")},\r\n        ]\r\n\r\n        //valueList tabe of table parameters\r\n        this.paramColumns =[{\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            key: \"prKey\",\r\n            name: this.tranService.translate(\"report.label.paramKey\"),\r\n            size: \"250px\",\r\n            className: \"text-cyan-500 cursor-pointer\",\r\n            funcClick: (id, item)=>{\r\n                me.isParamKeyExisted = false;\r\n                me.isParamDisplayExisted = false;\r\n                me.parameterInfo = {...item};\r\n                if(me.parameterInfo.required == undefined || me.parameterInfo.required == null){\r\n                    me.parameterInfo.required = false;\r\n                }\r\n                setTimeout(function(){\r\n                    me.parameterInfo.valueList = item.valueList || [];\r\n                    me.optionParamInput.mode = CONSTANTS.MODE_VIEW.DETAIL;\r\n                    me.paramInputControl.reset();\r\n                })\r\n                me.formParameter = me.formBuilder.group(me.parameterInfo);\r\n                Object.keys(me.parameterInfo).forEach(key => {\r\n                    me.formParameter.get(key).disable();\r\n                })\r\n                me.modeParameter = CONSTANTS.MODE_VIEW.DETAIL;\r\n                me.isShowDialogParameter = true;\r\n            }\r\n        },{\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            key: \"prType\",\r\n            name: this.tranService.translate(\"report.label.paramType\"),\r\n            size: \"150px\",\r\n            funcConvertText(value) {\r\n                if(value == CONSTANTS.PARAMETER_TYPE.NUMBER){\r\n                    return me.tranService.translate(\"report.paramType.number\");\r\n                }else if(value == CONSTANTS.PARAMETER_TYPE.STRING){\r\n                    return me.tranService.translate(\"report.paramType.string\");\r\n                }else if(value == CONSTANTS.PARAMETER_TYPE.DATE){\r\n                    return me.tranService.translate(\"report.paramType.date\");\r\n                }else if(value == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER){\r\n                    return me.tranService.translate(\"report.paramType.listNumber\");\r\n                }else if(value == CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n                    return me.tranService.translate(\"report.paramType.listString\");\r\n                }else if(value == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){\r\n                    return me.tranService.translate(\"report.paramType.timestamp\");\r\n                } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {\r\n                    return me.tranService.translate(\"report.paramType.recentlyDateFrom\");\r\n                } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {\r\n                    return me.tranService.translate(\"report.paramType.recentlyDateTo\");\r\n                }\r\n                return \"\";\r\n            },\r\n        },{\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            key: \"prDisplayName\",\r\n            name: this.tranService.translate(\"report.label.paramDisplay\"),\r\n            size: \"350px\"\r\n        }];\r\n\r\n        this.optionTableListParam = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: false,\r\n            hasShowJumpPage: false,\r\n            paginator: false,\r\n            hasShowToggleColumn: false,\r\n            action: [{\r\n                icon: \"pi pi-external-link\",\r\n                tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                func: (id, item) => {\r\n                    me.isParamKeyExisted = false;\r\n                    me.isParamDisplayExisted = false;\r\n                    me.parameterInfo = {...item};\r\n                    if(me.parameterInfo.required == undefined || me.parameterInfo.required == null){\r\n                        me.parameterInfo.required = false;\r\n                    }\r\n                    setTimeout(function(){\r\n                        me.parameterInfo.valueList = item.valueList || [];\r\n                        me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;\r\n                        me.paramInputControl.reset();\r\n                    })\r\n                    me.formParameter = me.formBuilder.group(me.parameterInfo);\r\n                    me.modeParameter = CONSTANTS.MODE_VIEW.UPDATE;\r\n                    me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;\r\n                    me.paramInputControl.reset();\r\n                    me.isShowDialogParameter = true;\r\n                },\r\n                funcAppear(id, item) {\r\n                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\r\n                },\r\n            },{\r\n                icon: \"pi pi-trash\",\r\n                tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                func: (id, item) => {\r\n                    for(let i = 0;i < me.dataParams.content.length;i++){\r\n                        if(me.dataParams.content[i].id == id){\r\n                            me.dataParams.content.splice(i, 1);\r\n                            me.generalInfo.filterParams = JSON.stringify(me.dataParams.content);\r\n                            break;\r\n                        }\r\n                    }\r\n                },\r\n                funcAppear(id, item) {\r\n                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;\r\n                },\r\n            }]\r\n        }\r\n\r\n        this.tableInfo = {\r\n            id: null,\r\n            query: null,\r\n            columnDisplay: null,\r\n            columnQueryResult: null,\r\n            reportConfigId: null,\r\n            schema: null,\r\n            tableName: null,\r\n            columns: null\r\n        }\r\n        this.formTable = this.formBuilder.group(this.tableInfo);\r\n        this.parameterInfo = {\r\n            id: null,\r\n            prDisplayName: null,\r\n            prKey: null,\r\n            prType: null,\r\n            valueList: null,\r\n            dateType: null,\r\n            displayPattern: null,\r\n            input: null,\r\n            isAutoComplete: false,\r\n            isMultiChoice: false,\r\n            objectKey: null,\r\n            output: null,\r\n            queryInfo: null,\r\n            required: false,\r\n            queryParam: null,\r\n            prValue: null,\r\n        }\r\n        this.formParameter = this.formBuilder.group(this.parameterInfo);\r\n\r\n        //table of fields of table\r\n        this.columnTableInput = [\r\n            {\r\n                align: 'left',\r\n                key: \"display\",\r\n                name: this.tranService.translate(\"report.label.fieldDisplay\"),\r\n                size: \"280px\",\r\n                type: CONSTANTS.PARAMETER_TYPE.STRING,\r\n                validate: {\r\n                    required: true,\r\n                    maxLength: 255,\r\n                    pattern: /^[^~`!@#\\$%\\^&*=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$/,\r\n                    messageErrorPattern: me.tranService.translate(\"global.message.formatContainVN\"),\r\n                    exists: true\r\n                }\r\n            },\r\n            {\r\n                align: 'left',\r\n                key: \"key\",\r\n                name: this.tranService.translate(\"report.label.fieldData\"),\r\n                size: \"280px\",\r\n                type: CONSTANTS.PARAMETER_TYPE.STRING,\r\n                validate: {\r\n                    required: true,\r\n                    maxLength: 255,\r\n                    pattern: /^[a-zA-Z0-9\\-_]*$/,\r\n                    messageErrorPattern: this.tranService.translate(\"global.message.formatCode\"),\r\n                    exists: true\r\n                }\r\n            }\r\n        ]\r\n        this.optionTableInput = {\r\n            mode: CONSTANTS.MODE_VIEW.CREATE\r\n        };\r\n        this.tableInputControl = new TableInputControl();\r\n\r\n        //table of value parameters\r\n        this.columnParamInput = [\r\n            {\r\n                align: 'left',\r\n                key: \"display\",\r\n                name: this.tranService.translate(\"report.label.valueDisplay\"),\r\n                size: \"280px\",\r\n                type: CONSTANTS.PARAMETER_TYPE.STRING,\r\n                validate: {\r\n                    required: true,\r\n                    maxLength: 255,\r\n                    exists: true,\r\n                    pattern: /^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$/,\r\n                    messageErrorPattern: me.tranService.translate(\"global.message.formatContainVN\")\r\n                }\r\n            },\r\n            {\r\n                align: 'left',\r\n                key: \"value\",\r\n                name: this.tranService.translate(\"report.label.valueDB\"),\r\n                size: \"280px\",\r\n                type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,\r\n                validate: {\r\n                    required: true,\r\n                    maxLength: 255,\r\n                    pattern: /^[a-zA-Z0-9\\-_]+$/,\r\n                    messageErrorPattern: me.tranService.translate(\"global.message.formatCode\"),\r\n                    exists: true\r\n                }\r\n            }\r\n        ]\r\n        this.optionParamInput = {\r\n            mode: CONSTANTS.MODE_VIEW.CREATE\r\n        };\r\n        this.paramInputControl = new TableInputControl();\r\n\r\n        this.listObjectKey = ARRAY_SERVICE.map(el => {\r\n            return {\r\n                value: el.name,\r\n                display: this.tranService.translate(el.display)\r\n            }\r\n        })\r\n\r\n        this.control.reload = this.onload.bind(this);\r\n    }\r\n\r\n    onload(){\r\n        let me = this;\r\n        this.formGeneralInfo = undefined;\r\n        this.isReportNameExisted = false;\r\n        setTimeout(function(){\r\n            if(me.generalInfo.filterParams){\r\n                me.generalInfo.filters = JSON.parse(me.generalInfo.filterParams);\r\n                let idParam = 1;\r\n                me.generalInfo.filters.forEach(el => {\r\n                    el.id = idParam ++;\r\n                    if(el.dateType == undefined){\r\n                        el.dateType = null\r\n                    }\r\n                    if(!el.isAutoComplete){\r\n                        el.isAutoComplete = false;\r\n                    }\r\n                    if(!el.isMultiChoice){\r\n                        el.isMultiChoice = false;\r\n                    }\r\n                    if(el.queryInfo != undefined && el.queryInfo != null){\r\n                        el.objectKey = el.queryInfo.objectKey;\r\n                        el.input = el.queryInfo.input;\r\n                        el.output = el.queryInfo.output;\r\n                        el.displayPattern = el.queryInfo.displayPattern;\r\n                        el.queryParam = el.queryInfo.queryParam;\r\n                    }else{\r\n                        el.objectKey = null;\r\n                        el.input = null;\r\n                        el.output = null;\r\n                        el.displayPattern = null;\r\n                        el.queryParam = null;\r\n                    }\r\n                })\r\n            }else{\r\n                me.generalInfo.filters = [];\r\n            }\r\n            me.oldReportName = me.generalInfo ? me.generalInfo.name : null;\r\n            me.formGeneralInfo = me.formBuilder.group(me.generalInfo);\r\n            if(me.modeView == CONSTANTS.MODE_VIEW.DETAIL){\r\n                me.formGeneralInfo.get(\"name\").disable();\r\n                me.formGeneralInfo.get(\"status\").disable();\r\n                me.formGeneralInfo.get(\"enablePreview\").disable();\r\n                me.formGeneralInfo.get(\"description\").disable();\r\n            }\r\n            me.dataTables = {\r\n                content: me.generalInfo.reportContents,\r\n                total: me.generalInfo.reportContents.length\r\n            }\r\n            me.dataParams = {\r\n                content: me.generalInfo.filters,\r\n                total: me.generalInfo.filters.length\r\n            }\r\n        })\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n\r\n    }\r\n\r\n    checkExistReportName(){\r\n        if(this.generalInfo.name == this.oldReportName || this.generalInfo.name == null) return;\r\n        let me = this;\r\n        this.debounceService.set(\"reportDynamicName\", this.reportService.checkExistNameReportDynamic.bind(this.reportService), this.generalInfo.name, (reponse)=>{\r\n            if(reponse == 0){\r\n                me.isReportNameExisted = false;\r\n            }else{\r\n                me.isReportNameExisted = true;\r\n            }\r\n        })\r\n    }\r\n\r\n    enablePreviewChecked(event){\r\n        if(event.checked.length > 0){\r\n            this.generalInfo.enablePreview = CONSTANTS.REPORT_PREVIEW.ENABLE;\r\n        }else{\r\n            this.generalInfo.enablePreview = CONSTANTS.REPORT_PREVIEW.DISABLE;\r\n        }\r\n    }\r\n\r\n    getHeaderParameter(){\r\n        if(this.modeParameter == CONSTANTS.MODE_VIEW.CREATE){\r\n            return this.tranService.translate(\"report.text.createParameter\");\r\n        }else if(this.modeParameter == CONSTANTS.MODE_VIEW.UPDATE){\r\n            return this.tranService.translate(\"report.text.updateParameter\");\r\n        }else{\r\n            return this.tranService.translate(\"report.text.detailParameter\");\r\n        }\r\n    }\r\n\r\n    getHeaderTable(){\r\n        if(this.modeTable == CONSTANTS.MODE_VIEW.CREATE){\r\n            return this.tranService.translate(\"report.text.createTable\");\r\n        }else if(this.modeTable == CONSTANTS.MODE_VIEW.UPDATE){\r\n            return this.tranService.translate(\"report.text.updateTable\");\r\n        }else{\r\n            return this.tranService.translate(\"report.text.detailTable\");\r\n        }\r\n    }\r\n\r\n    openCreateTable(){\r\n        this.tableInfo = {\r\n            id: null,\r\n            columnDisplay: null,\r\n            columnQueryResult: null,\r\n            query: null,\r\n            reportConfigId: this.generalInfo.id,\r\n            schema: null,\r\n            tableName: null,\r\n            columns: []\r\n        }\r\n        this.formTable = this.formBuilder.group(this.tableInfo);\r\n        this.isTableNameExisted = false;\r\n        this.modeTable = CONSTANTS.MODE_VIEW.CREATE;\r\n        this.optionTableInput.mode = CONSTANTS.MODE_VIEW.CREATE;\r\n        this.isShowDialogTable = true;\r\n        this.tableInputControl.reset();\r\n    }\r\n\r\n    openCreateParameter(){\r\n        this.parameterInfo = {\r\n            prDisplayName: null,\r\n            prKey: null,\r\n            prType: null,\r\n            id: null,\r\n            valueList: [],\r\n            required: false,\r\n            dateType: null,\r\n            isAutoComplete: false,\r\n            isMultiChoice: false,\r\n            queryInfo: null,\r\n            displayPattern: null,\r\n            input: null,\r\n            objectKey: null,\r\n            output: null,\r\n            queryParam: null,\r\n            prValue: null,\r\n        }\r\n        this.formParameter = this.formBuilder.group(this.parameterInfo);\r\n        this.isParamDisplayExisted = false;\r\n        this.isParamKeyExisted = false;\r\n        this.optionParamInput.mode = CONSTANTS.MODE_VIEW.CREATE;\r\n        this.modeParameter = CONSTANTS.MODE_VIEW.CREATE;\r\n        this.isShowDialogParameter = true;\r\n        this.paramInputControl.reset();\r\n\r\n\r\n    }\r\n\r\n    onSubmit(){\r\n        let me = this;\r\n        if(this.formGeneralInfo.invalid || this.isReportNameExisted) return;\r\n        let data = {\r\n            ...this.generalInfo\r\n        }\r\n        data.reportContents.forEach(el => {\r\n            if(el.id < 0){\r\n                el.id = null;\r\n            }\r\n        })\r\n        let dataFilter = data.filters || [];\r\n        dataFilter.forEach(el => {\r\n            delete el.id;\r\n            if(el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n                el.valueList.forEach(ele => {\r\n                    delete ele.id;\r\n                    if(ele['mode']){\r\n                        delete ele['mode']\r\n                    }\r\n                })\r\n            }\r\n        })\r\n        data.filterParams = JSON.stringify(dataFilter);\r\n        delete data.filters;\r\n        if(data.enablePreview == null){\r\n            data.enablePreview = CONSTANTS.REPORT_PREVIEW.DISABLE;\r\n        }\r\n        me.messageCommonService.onload();\r\n        if(me.generalInfo.id == null){\r\n            this.reportService.createGeneralReportDynamic(data, (response)=>{\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.generalInfo = response;\r\n                me.saveSuccess(me.generalInfo.id);\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n        }else{\r\n            this.reportService.updateGeneralReportDynamic(me.generalInfo.id, data, (response)=>{\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.generalInfo = response;\r\n                me.onload();\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n        }\r\n    }\r\n\r\n    checkExistTableName(){\r\n        for(let i = 0;i< this.generalInfo.reportContents.length;i++){\r\n            if(this.generalInfo.reportContents[i].tableName == this.tableInfo.tableName && this.generalInfo.reportContents[i].id != this.tableInfo.id){\r\n                this.isTableNameExisted = true;\r\n                return;\r\n            }\r\n        }\r\n        this.isTableNameExisted = false;\r\n    }\r\n\r\n    checkExistParamKey(){\r\n        for(let i = 0;i< this.generalInfo.filters.length;i++){\r\n            if(this.generalInfo.filters[i].prKey == this.parameterInfo.prKey && this.generalInfo.filters[i].id != this.parameterInfo.id){\r\n                this.isParamKeyExisted = true;\r\n                return;\r\n            }\r\n        }\r\n        this.isParamKeyExisted = false;\r\n    }\r\n\r\n    checkExistParamDisplay(){\r\n        for(let i = 0;i< this.generalInfo.filters.length;i++){\r\n            if(this.generalInfo.filters[i].prDisplayName == this.parameterInfo.prDisplayName && this.generalInfo.filters[i].id != this.parameterInfo.id){\r\n                this.isParamDisplayExisted = true;\r\n                return;\r\n            }\r\n        }\r\n        this.isParamDisplayExisted = false;\r\n    }\r\n\r\n    getTextSchema(value):string{\r\n        for(let i = 0;i<this.schemas.length;i++){\r\n            if(value == this.schemas[i].value){\r\n                return this.schemas[i].name;\r\n            }\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getTextParamType(value): string{\r\n        for(let i = 0;i<this.parameterTypes.length;i++){\r\n            if(value == this.parameterTypes[i].value){\r\n                return this.parameterTypes[i].name;\r\n            }\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    saveTable(){\r\n        if(this.formTable.invalid || this.tableInfo.columns == null || this.tableInfo.columns == undefined || this.tableInfo.columns.length == 0 || this.tableInputControl.isUpdating == true || this.isTableNameExisted) return;\r\n        let data = {...this.tableInfo};\r\n        data.columnDisplay = data.columns.map(el => el.display).toLocaleString();\r\n        data.columnQueryResult = data.columns.map(el => el.key).toLocaleString();\r\n        this.isShowDialogTable = false;\r\n        delete data.columns;\r\n        if(data.id == null){\r\n            data.id = this.indexFakeTable --;\r\n            this.generalInfo.reportContents.push(data);\r\n        }else{\r\n            for(let i = 0;i < this.generalInfo.reportContents.length; i++){\r\n                if(this.generalInfo.reportContents[i].id == data.id){\r\n                    this.generalInfo.reportContents[i] = data;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        this.dataTables.content = this.generalInfo.reportContents;\r\n        this.dataTables.total = this.generalInfo.reportContents.length;\r\n    }\r\n\r\n    saveParameter(){\r\n        if(this.checkInvalidFormParameter() == true) return;\r\n        let data = {...this.parameterInfo};\r\n        data.queryInfo = {\r\n            displayPattern: data.displayPattern,\r\n            queryParam: data.queryParam,\r\n            input: data.input,\r\n            output: data.output,\r\n            objectKey: data.objectKey\r\n        }\r\n        this.isShowDialogParameter = false;\r\n        if(data.id == null){\r\n            data.id = this.indexFakeParameter --;\r\n            this.generalInfo.filters.push(data);\r\n        }else{\r\n            for(let i = 0;i < this.generalInfo.filters.length; i++){\r\n                if(this.generalInfo.filters[i].id == data.id){\r\n                    this.generalInfo.filters[i] = data;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        this.generalInfo.filterParams = JSON.stringify(this.generalInfo.filters);\r\n    }\r\n\r\n    checkInvalidFormParameter():boolean{\r\n        if(this.formParameter.invalid) return true;\r\n        if(this.isParamDisplayExisted || this.isParamKeyExisted) return true;\r\n        if(this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n            if(this.parameterInfo.isAutoComplete == false){\r\n                if(this.parameterInfo.valueList == null || this.parameterInfo.valueList == undefined || this.parameterInfo.valueList.length == 0) return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    changeParamType(){\r\n        let me = this;\r\n        this.columnParamInput = [\r\n            {\r\n                align: 'left',\r\n                key: \"display\",\r\n                name: this.tranService.translate(\"report.label.valueDisplay\"),\r\n                size: \"280px\",\r\n                type: CONSTANTS.PARAMETER_TYPE.STRING,\r\n                validate: {\r\n                    required: true,\r\n                    maxLength: 255,\r\n                    exists: true,\r\n                    pattern: /^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$/,\r\n                    messageErrorPattern: me.tranService.translate(\"global.message.formatContainVN\")\r\n                }\r\n            },\r\n            {\r\n                align: 'left',\r\n                key: \"value\",\r\n                name: this.tranService.translate(\"report.label.valueDB\"),\r\n                size: \"280px\",\r\n                type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,\r\n                validate: {\r\n                    required: true,\r\n                    maxLength: 255,\r\n                    // pattern: /^[a-zA-Z0-9\\-_]+$/,\r\n                    // messageErrorPattern: me.tranService.translate(\"global.message.formatCode\"),\r\n                    exists: true\r\n                }\r\n            }\r\n        ]\r\n        if(this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_NUMBER && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n            this.parameterInfo.isMultiChoice = false;\r\n        }\r\n        if(this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.STRING && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n            this.parameterInfo.isAutoComplete = false;\r\n        }\r\n        this.parameterInfo.valueList = [];\r\n        if(this.paramInputControl.reset){\r\n            this.paramInputControl.reset();\r\n        }\r\n    }\r\n\r\n    changeIsAutoComplete(){\r\n\r\n    }\r\n\r\n    copyText(event){\r\n        let me = this;\r\n        let value = document.querySelector(\"textarea#query\");\r\n        if(value){\r\n            let text = value[\"value\"];\r\n            me.utilService.copyToClipboard(text,() => {\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.copied\"));\r\n            });\r\n        }else{\r\n            this.messageCommonService.warning(this.tranService.translate(\"global.message.error\"));\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS\r\n}\r\n", "<div *ngIf=\"formGeneralInfo\">\r\n    <form [formGroup]=\"formGeneralInfo\">\r\n        <!-- report name -->\r\n        <div class=\"w-full field grid tab-report-grid \">\r\n            <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.reportName\")}}<span class=\"text-red-500\">*</span></label>\r\n            <div class=\"col\" style=\"max-width: 500px;\">\r\n                <input class=\"w-full\"\r\n                        pInputText id=\"name\"\r\n                        [(ngModel)]=\"generalInfo.name\"\r\n                        formControlName=\"name\"\r\n                        [required]=\"true\"\r\n                        [maxLength]=\"255\"\r\n                        pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                        [placeholder]=\"tranService.translate('report.text.inputReportName')\"\r\n                        (ngModelChange)=\"checkExistReportName()\"\r\n                />\r\n            </div>\r\n        </div>\r\n         <!-- error report name -->\r\n         <div class=\"w-full field grid text-error-field tab-report-grid\">\r\n            <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n            <div class=\"col\">\r\n                <small class=\"text-red-500\" *ngIf=\"formGeneralInfo.controls.name.dirty && formGeneralInfo.controls.name.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                <small class=\"text-red-500\" *ngIf=\"formGeneralInfo.controls.name.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                <small class=\"text-red-500\" *ngIf=\"formGeneralInfo.controls.name.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                <small class=\"text-red-500\" *ngIf=\"isReportNameExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"report.label.reportName\").toLowerCase()})}}</small>\r\n            </div>\r\n        </div>\r\n        <!-- trang thai bao cao -->\r\n        <div class=\"w-full field grid tab-report-grid\">\r\n            <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.reportStatus\")}}</label>\r\n            <div class=\"col\" style=\"max-width: 500px;\">\r\n                <p-dropdown styleClass=\"w-full\"\r\n                        id=\"status\" [autoDisplayFirst]=\"false\"\r\n                        [(ngModel)]=\"generalInfo.status\"\r\n                        formControlName=\"status\"\r\n                        [options]=\"statusReports\"\r\n                        optionLabel=\"name\"\r\n                        optionValue=\"value\"\r\n                ></p-dropdown>\r\n            </div>\r\n        </div>\r\n        <!-- xem truoc bao cao -->\r\n        <div class=\"w-full field grid tab-report-grid\">\r\n            <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.reportEnablePreview\")}}</label>\r\n            <div class=\"col\" style=\"max-width: 500px;\">\r\n                <p-checkbox\r\n                        name=\"enablePreview\"\r\n                        binary=\"true\"\r\n                        [(ngModel)]=\"generalInfo.enablePreview\"\r\n                        formControlName=\"enablePreview\"\r\n                        [trueValue]=\"reportPreview.ENABLE\"\r\n                        [falseValue]=\"reportPreview.DISABLE\"></p-checkbox>\r\n            </div>\r\n        </div>\r\n        <!-- description -->\r\n        <div class=\"w-full field grid tab-report-grid\">\r\n            <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px; height: fit-content;\">{{tranService.translate(\"report.label.description\")}}</label>\r\n            <div class=\"col\" style=\"max-width: 500px;\">\r\n                <textarea  class=\"w-full\" style=\"resize: none;\"\r\n                    rows=\"3\"\r\n                    [autoResize]=\"false\"\r\n                    pInputTextarea id=\"description\"\r\n                    [(ngModel)]=\"generalInfo.description\"\r\n                    formControlName=\"description\"\r\n                    [maxlength]=\"255\"\r\n                    [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                ></textarea>\r\n            </div>\r\n        </div>\r\n        <!-- error description -->\r\n        <div class=\"w-full field grid text-error-field tab-report-grid\">\r\n            <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n            <div class=\"col\">\r\n                <small class=\"text-red-500\" *ngIf=\"formGeneralInfo.controls.description.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n            </div>\r\n        </div>\r\n    </form>\r\n\r\n    <div>\r\n        <div class=\"flex flex-row justify-content-between align-items-center pl-4\">\r\n            <div><b>{{tranService.translate(\"report.label.tableList\")}}</b></div>\r\n            <div *ngIf=\"modeView != objectMode.DETAIL\" class=\"text-cyan-500 cursor-pointer\" (click)=\"openCreateTable()\"><u>{{tranService.translate(\"report.button.addTable\")}}</u></div>\r\n        </div>\r\n        <table-vnpt\r\n            [fieldId]=\"'id'\"\r\n            [columns]=\"tableColumns\"\r\n            [dataSet]=\"dataTables\"\r\n            [options]=\"optionTableListTable\"\r\n            scrollHeight=\"300px\"\r\n        ></table-vnpt>\r\n    </div>\r\n\r\n    <div class=\"mt-3\">\r\n        <div class=\"flex flex-row justify-content-between align-items-center pl-4\">\r\n            <div><b>{{tranService.translate(\"report.label.paramList\")}}</b></div>\r\n            <div *ngIf=\"modeView != objectMode.DETAIL\" class=\"text-cyan-500 cursor-pointer\" (click)=\"openCreateParameter()\"><u>{{tranService.translate(\"report.button.addParam\")}}</u></div>\r\n        </div>\r\n        <table-vnpt\r\n            [fieldId]=\"'id'\"\r\n            [columns]=\"paramColumns\"\r\n            [dataSet]=\"dataParams\"\r\n            [options]=\"optionTableListParam\"\r\n            scrollHeight=\"300px\"\r\n            [isRowDraggable]=\"true\"\r\n        ></table-vnpt>\r\n    </div>\r\n\r\n    <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n        <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"cancel()\"></p-button>\r\n        <p-button *ngIf=\"modeView != objectMode.DETAIL\" (click)=\"onSubmit()\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" [disabled]=\"formGeneralInfo.invalid || isReportNameExisted\"></p-button>\r\n    </div>\r\n\r\n    <!-- table -->\r\n    <div class=\"flex justify-content-center dialog-push-group\" style=\"top: 0\">\r\n        <p-dialog [header]=\"getHeaderTable()\" [(visible)]=\"isShowDialogTable\" [modal]=\"true\" [style]=\"{ width: '700px', top: 0 }\" [draggable]=\"false\" [resizable]=\"false\" styleClass=\"dialog-report\">\r\n            <div class=\"w-full field grid grid-1 p-0 m-0\">\r\n                <form [formGroup]=\"formTable\" class=\"w-full\">\r\n                    <!-- table name -->\r\n                    <div class=\"w-full field grid tab-report-grid\">\r\n                        <label htmlFor=\"tableName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.tableName\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                    pInputText id=\"tableName\"\r\n                                    [(ngModel)]=\"tableInfo.tableName\"\r\n                                    formControlName=\"tableName\"\r\n                                    [required]=\"true\"\r\n                                    [maxLength]=\"255\"\r\n                                    pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                    [placeholder]=\"tranService.translate('report.text.inputTableName')\"\r\n                                    (ngModelChange)=\"checkExistTableName()\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <!-- error report name -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid\">\r\n                        <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formTable.controls.tableName.dirty && formTable.controls.tableName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formTable.controls.tableName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formTable.controls.tableName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"isTableNameExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"report.label.tableName\").toLowerCase()})}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- schema -->\r\n                    <div class=\"w-full field grid tab-report-grid\">\r\n                        <label for=\"schema\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.schema\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <p-dropdown styleClass=\"w-full\" showClear=\"true\"\r\n                                    id=\"schema\" [autoDisplayFirst]=\"false\"\r\n                                    [(ngModel)]=\"tableInfo.schema\"\r\n                                    formControlName=\"schema\"\r\n                                    [options]=\"schemas\"\r\n                                    optionLabel=\"name\"\r\n                                    optionValue=\"value\"\r\n                                    [required]=\"true\"\r\n                                    [placeholder]=\"tranService.translate('report.text.selectSchema')\"\r\n                            ></p-dropdown>\r\n                        </div>\r\n                    </div>\r\n                    <!-- error schema -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid\">\r\n                        <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formTable.controls.schema.dirty && formTable.controls.schema.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- query -->\r\n                    <div class=\"w-full field grid tab-report-grid-query\">\r\n                        <label htmlFor=\"query\" class=\"col-fixed\" style=\"width:180px; height: fit-content;\">{{tranService.translate(\"report.label.query\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"width: calc(100% - 210px)\">\r\n                            <textarea  class=\"w-full\" style=\"resize: vertical;\"\r\n                                rows=\"3\"\r\n                                [autoResize]=\"false\"\r\n                                pInputTextarea id=\"query\"\r\n                                [(ngModel)]=\"tableInfo.query\"\r\n                                formControlName=\"query\"\r\n                                [placeholder]=\"tranService.translate('report.text.inputQuery')\"\r\n                                [required]=\"true\"\r\n                            ></textarea>\r\n                        </div>\r\n                        <span class=\"col-fixed pi pi-clone text-xl cursor-pointer\" style=\"width:30px;height: fit-content\" [pTooltip]=\"tranService.translate('global.button.copy')\" (click)=\"copyText($event)\"></span>\r\n                    </div>\r\n                    <!-- error schema -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid\">\r\n                        <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formTable.controls.query.dirty && formTable.controls.query.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                </form>\r\n                <div class=\"w-full table-input-div\">\r\n                    <table-input-vnpt\r\n                        [(value)]=\"tableInfo.columns\"\r\n                        [columns]=\"columnTableInput\"\r\n                        [options]=\"optionTableInput\"\r\n                        [control]=\"tableInputControl\"\r\n                        fieldId=\"id\"\r\n                        [showMove]=\"true\"\r\n                    ></table-input-vnpt>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogTable = false\"></p-button>\r\n                <p-button (click)=\"saveTable()\" *ngIf=\"modeTable != objectMode.DETAIL\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" [disabled]=\"formTable.invalid || tableInfo.columns == null || tableInfo.columns == undefined || tableInfo.columns.length == 0 || tableInputControl.isUpdating == true || isTableNameExisted\"></p-button>\r\n            </div>\r\n        </p-dialog>\r\n    </div>\r\n\r\n    <!-- parameter -->\r\n    <div class=\"flex justify-content-center dialog-push-group\">\r\n        <p-dialog [header]=\"getHeaderParameter()\" [(visible)]=\"isShowDialogParameter\" [modal]=\"true\" [style]=\"{ width: '700px',top: 0 }\" [draggable]=\"false\" [resizable]=\"false\" styleClass=\"dialog-report\">\r\n            <div class=\"w-full field grid p-0 m-0\">\r\n                <form [formGroup]=\"formParameter\" class=\"w-full\">\r\n                    <!-- paramKey -->\r\n                    <div class=\"w-full field grid tab-report-grid \">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.paramKey\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                    pInputText id=\"prKey\"\r\n                                    [(ngModel)]=\"parameterInfo.prKey\"\r\n                                    formControlName=\"prKey\"\r\n                                    [required]=\"true\"\r\n                                    [maxLength]=\"255\"\r\n                                    pattern=\"^[a-zA-Z0-9_]*$\"\r\n                                    [placeholder]=\"tranService.translate('report.text.inputParamKey')\"\r\n                                    (ngModelChange)=\"checkExistParamKey()\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <!-- error prKey -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid \">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prKey.dirty && formParameter.controls.prKey.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prKey.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prKey.errors?.pattern\">{{tranService.translate(\"global.message.formatCodeNotSub\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"isParamKeyExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"report.label.paramKey\").toLowerCase()})}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- prDisplayName -->\r\n                    <div class=\"w-full field grid tab-report-grid \">\r\n                        <label htmlFor=\"prDisplayName\" class=\"col-fixed\" style=\"width:180px;\">{{tranService.translate(\"report.label.paramDisplay\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                    pInputText id=\"prDisplayName\"\r\n                                    [(ngModel)]=\"parameterInfo.prDisplayName\"\r\n                                    formControlName=\"prDisplayName\"\r\n                                    [required]=\"true\"\r\n                                    [maxLength]=\"255\"\r\n                                    pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                    [placeholder]=\"tranService.translate('report.text.inputDisplayName')\"\r\n                                    (ngModelChange)=\"checkExistParamDisplay()\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <!-- error prDisplayName -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid \">\r\n                        <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prDisplayName.dirty && formParameter.controls.prDisplayName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prDisplayName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prDisplayName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"isParamDisplayExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"report.label.paramDisplay\").toLowerCase()})}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- required -->\r\n                    <div class=\"w-full field grid tab-report-grid \">\r\n                        <label for=\"required\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.required\")}}</label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <p-checkbox\r\n                                binary=\"true\"\r\n                                [trueValue]=\"true\"\r\n                                [falseValue]=\"false\"\r\n                                name=\"required\"\r\n                                [(ngModel)]=\"parameterInfo.required\"\r\n                                formControlName=\"required\"\r\n                            ></p-checkbox>\r\n                        </div>\r\n                    </div>\r\n                    <!-- prType -->\r\n                    <div class=\"w-full field grid tab-report-grid \">\r\n                        <label for=\"prType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.paramType\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <p-dropdown styleClass=\"w-full\" showClear=\"true\"\r\n                                    id=\"prType\" [autoDisplayFirst]=\"false\"\r\n                                    [(ngModel)]=\"parameterInfo.prType\"\r\n                                    formControlName=\"prType\"\r\n                                    [options]=\"parameterTypes\"\r\n                                    optionLabel=\"name\"\r\n                                    optionValue=\"value\"\r\n                                    [required]=\"true\"\r\n                                    [placeholder]=\"tranService.translate('report.text.selectParamType')\"\r\n                                    (ngModelChange)=\"changeParamType()\"\r\n                            ></p-dropdown>\r\n                        </div>\r\n                    </div>\r\n                    <!-- error prType -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid \">\r\n                        <label htmlFor=\"name\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.prType.dirty && formParameter.controls.prType.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- date type -->\r\n                    <div class=\"w-full field grid tab-report-grid \" [class]=\"parameterInfo.prType == objectType.DATE || parameterInfo.prType == objectType.TIMESTAMP ? '' : 'hidden'\">\r\n                        <label for=\"dateType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.dateType\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <p-dropdown styleClass=\"w-full\" showClear=\"true\"\r\n                                    id=\"dateType\" [autoDisplayFirst]=\"false\"\r\n                                    [(ngModel)]=\"parameterInfo.dateType\"\r\n                                    formControlName=\"dateType\"\r\n                                    [options]=\"dateTypes\"\r\n                                    optionLabel=\"name\"\r\n                                    optionValue=\"value\"\r\n                                    [required]=\"parameterInfo.prType == objectType.DATE || parameterInfo.prType == objectType.TIMESTAMP\"\r\n                                    [placeholder]=\"tranService.translate('report.text.selectDateType')\"\r\n                            ></p-dropdown>\r\n                        </div>\r\n                    </div>\r\n                    <!-- error datetype -->\r\n                    <div class=\"w-full field grid text-error-field tab-report-grid \">\r\n                        <label htmlFor=\"dateType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.dateType.dirty && formParameter.controls.dateType.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- is autocomplete, is multichoice -->\r\n                    <div class=\"w-full flex flex-row justify-content-between align-items-center\" [class]=\"parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.STRING || parameterInfo.prType == objectType.LIST_STRING ? '' : 'hidden'\">\r\n                        <div class=\"w-6 field grid\" [class]=\"parameterInfo.prType == objectType.STRING || parameterInfo.prType == objectType.LIST_STRING ? '' : ''\">\r\n                            <label for=\"isAutoComplete\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.isAutoComplete\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: 400px;\">\r\n                                <p-checkbox\r\n                                    binary=\"true\"\r\n                                    [trueValue]=\"true\"\r\n                                    [falseValue]=\"false\"\r\n                                    name=\"isAutoComplete\"\r\n                                    [(ngModel)]=\"parameterInfo.isAutoComplete\"\r\n                                    formControlName=\"isAutoComplete\" (ngModelChange)=\"changeIsAutoComplete()\"\r\n                                    ></p-checkbox>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-6 field grid\" [class]=\"parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.LIST_STRING ? '' : 'hidden'\">\r\n                            <label for=\"isMultiChoice\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.isMultiChoice\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: 400px;\">\r\n                                <p-checkbox\r\n                                    name=\"isMultiChoice\"\r\n                                    binary=\"true\"\r\n                                    [trueValue]=\"true\"\r\n                                    [falseValue]=\"false\"\r\n                                    [(ngModel)]=\"parameterInfo.isMultiChoice\"\r\n                                    formControlName=\"isMultiChoice\"></p-checkbox>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <!-- objectkey -->\r\n                    <div class=\"w-full field grid\" [class]=\"parameterInfo.isAutoComplete == true ? '' : 'hidden'\">\r\n                        <label htmlFor=\"objectKey\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.objectKey\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <p-dropdown styleClass=\"w-full\"\r\n                                id=\"objectKey\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"parameterInfo.objectKey\"\r\n                                formControlName=\"objectKey\"\r\n                                [options]=\"listObjectKey\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"value\"\r\n                                [required]=\"parameterInfo.isAutoComplete == true\"\r\n                                [placeholder]=\"tranService.translate('report.text.inputObjectKey')\"\r\n                        ></p-dropdown>\r\n                        </div>\r\n                    </div>\r\n                    <!-- error object key -->\r\n                    <div class=\"w-full field grid text-error-field\">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.objectKey.dirty && formParameter.controls.objectKey.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.objectKey.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:16})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.objectKey.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- input -->\r\n                    <div class=\"w-full field grid\" [class]=\"parameterInfo.isAutoComplete == true ? '' : 'hidden'\">\r\n                        <label htmlFor=\"input\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.input\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                    pInputText id=\"input\"\r\n                                    [(ngModel)]=\"parameterInfo.input\"\r\n                                    formControlName=\"input\"\r\n                                    [required]=\"parameterInfo.isAutoComplete == true\"\r\n                                    [maxLength]=\"16\"\r\n                                    pattern=\"^[a-zA-Z0-9\\-_]*$\"\r\n                                    [placeholder]=\"tranService.translate('report.text.inputInput')\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <!-- error input -->\r\n                    <div class=\"w-full field grid text-error-field\">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.input.dirty && formParameter.controls.input.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.input.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:16})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.input.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- output -->\r\n                    <div class=\"w-full field grid\" [class]=\"parameterInfo.isAutoComplete == true ? '' : 'hidden'\">\r\n                        <label htmlFor=\"output\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.output\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                    pInputText id=\"output\"\r\n                                    [(ngModel)]=\"parameterInfo.output\"\r\n                                    formControlName=\"output\"\r\n                                    [required]=\"parameterInfo.isAutoComplete == true\"\r\n                                    [maxLength]=\"16\"\r\n                                    pattern=\"^[a-zA-Z0-9\\-_]*$\"\r\n                                    [placeholder]=\"tranService.translate('report.text.inputOutput')\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <!-- error output -->\r\n                    <div class=\"w-full field grid text-error-field\">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.output.dirty && formParameter.controls.output.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.output.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:16})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.output.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- displaypattern -->\r\n                    <div class=\"w-full field grid\" [class]=\"parameterInfo.isAutoComplete == true ? '' : 'hidden'\">\r\n                        <label htmlFor=\"displayPattern\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"report.label.displayPattern\")}}<span class=\"text-red-500\">*</span></label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                    pInputText id=\"displayPattern\"\r\n                                    [(ngModel)]=\"parameterInfo.displayPattern\"\r\n                                    formControlName=\"displayPattern\"\r\n                                    [required]=\"parameterInfo.isAutoComplete == true\"\r\n                                    [maxLength]=\"255\"\r\n                                    placeholder=\"${var1} - ${var2}\"\r\n                            />\r\n                            <!-- [placeholder]=\"tranService.translate('report.text.inputDisplayPattern')\" -->\r\n                        </div>\r\n                    </div>\r\n                    <!-- error displaypattern -->\r\n                    <div class=\"w-full field grid text-error-field\">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.displayPattern.dirty && formParameter.controls.displayPattern.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.displayPattern.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.displayPattern.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                    <!-- queryParams -->\r\n                    <div *ngIf=\"parameterInfo.isAutoComplete && (parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING)\" class=\"w-full field grid\">\r\n                        <label htmlFor=\"queryParam\" class=\"col-fixed\" style=\"width:180px\">\r\n                            {{tranService.translate(\"report.label.queryParams\")}}\r\n                            <span class=\"text-red-500\">*</span>\r\n                            &nbsp;&nbsp;\r\n                            <i class=\"pi pi-info-circle\" [pTooltip]=\"tranService.translate('report.label.sampleQueryParam')\"></i>\r\n                        </label>\r\n                        <div class=\"col\" style=\"max-width: 400px;\">\r\n                            <input class=\"w-full\"\r\n                                   pInputText id=\"queryParam\"\r\n                                   [(ngModel)]=\"parameterInfo.queryParam\"\r\n                                   formControlName=\"queryParam\"\r\n                                   [maxLength]=\"255\"\r\n                                   pattern=\"^(\\w+=(\\$\\w+|&quot;[^&quot;]*&quot;)|\\w+=\\d+)(?:&(\\w+=(\\$\\w+|&quot;[^&quot;]*&quot;)|\\w+=\\d+))*$\"\r\n                                   [placeholder]=\"tranService.translate('report.text.inputQueryParam')\"\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                    <!-- error displaypattern -->\r\n                    <div class=\"w-full field grid text-error-field\">\r\n                        <label htmlFor=\"prKey\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.queryParam.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                            <small class=\"text-red-500\" *ngIf=\"formParameter.controls.queryParam.errors?.pattern\">{{tranService.translate(\"report.message.wrongQueryParamFormat\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                </form>\r\n                <div class=\"w-full\">\r\n                    <table-input-vnpt [class]=\"(parameterInfo.prType == objectType.LIST_NUMBER || parameterInfo.prType == objectType.LIST_STRING) && parameterInfo.isAutoComplete == false ? '': 'hidden'\"\r\n                        [(value)]=\"parameterInfo.valueList\"\r\n                        [columns]=\"columnParamInput\"\r\n                        [options]=\"optionParamInput\"\r\n                        [control]=\"paramInputControl\"\r\n                        fieldId=\"id\"\r\n                    ></table-input-vnpt>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogParameter = false\"></p-button>\r\n                <p-button *ngIf=\"modeParameter != objectMode.DETAIL\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" [disabled]=\"checkInvalidFormParameter()\" (click)=\"saveParameter()\"></p-button>\r\n            </div>\r\n        </p-dialog>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,SAASC,aAAa,QAAQ,oEAAoE;AAElG,SAA4CC,iBAAiB,QAAQ,4DAA4D;;;;;;;;;;;;;;;;ICejHC,EAAA,CAAAC,cAAA,eAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IAC9KR,EAAA,CAAAC,cAAA,eAAoF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IACnJX,EAAA,CAAAC,cAAA,eAAkF;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,iBAAA,CAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,mCAA2D;;;;;;;;;;IAC7IR,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAyH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAjIH,EAAA,CAAAI,SAAA,GAAyH;IAAzHJ,EAAA,CAAAK,iBAAA,CAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAF,MAAA,CAAAN,WAAA,CAAAC,SAAA,4BAAAQ,WAAA,KAAyH;;;;;IAiDjLhB,EAAA,CAAAC,cAAA,eAA2F;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAY,MAAA,CAAAV,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;;IAQ9JX,EAAA,CAAAC,cAAA,cAA4G;IAA5BD,EAAA,CAAAkB,UAAA,mBAAAC,mEAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAACzB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAvDH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAqB,MAAA,CAAAnB,WAAA,CAAAC,SAAA,2BAAmD;;;;;;IAclKR,EAAA,CAAAC,cAAA,cAAgH;IAAhCD,EAAA,CAAAkB,UAAA,mBAAAS,mEAAA;MAAA3B,EAAA,CAAAoB,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAK,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAAC9B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAvDH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,2BAAmD;;;;;;IAc1KR,EAAA,CAAAC,cAAA,mBAAkN;IAAlKD,EAAA,CAAAkB,UAAA,mBAAAc,6EAAA;MAAAhC,EAAA,CAAAoB,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAU,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAA8InC,EAAA,CAAAG,YAAA,EAAW;;;;IAA7HH,EAAA,CAAAoC,UAAA,UAAAC,MAAA,CAAA9B,WAAA,CAAAC,SAAA,uBAAqD,aAAA6B,MAAA,CAAAC,eAAA,CAAAC,OAAA,IAAAF,MAAA,CAAAG,mBAAA;;;;;IA4BjIxC,EAAA,CAAAC,cAAA,eAAwH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAoC,MAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC5KR,EAAA,CAAAC,cAAA,eAAmF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAqC,OAAA,CAAAnC,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IAClJX,EAAA,CAAAC,cAAA,eAAiF;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,iBAAA,CAAAsC,OAAA,CAAApC,WAAA,CAAAC,SAAA,mCAA2D;;;;;IAC5IR,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAwH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhIH,EAAA,CAAAI,SAAA,GAAwH;IAAxHJ,EAAA,CAAAK,iBAAA,CAAAuC,OAAA,CAAArC,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAA6B,OAAA,CAAArC,WAAA,CAAAC,SAAA,2BAAAQ,WAAA,KAAwH;;;;;IAuB/KhB,EAAA,CAAAC,cAAA,eAAkH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAwC,OAAA,CAAAtC,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAuBtKR,EAAA,CAAAC,cAAA,eAAgH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAyC,OAAA,CAAAvC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;IAiBhLR,EAAA,CAAAC,cAAA,mBAAqV;IAA3UD,EAAA,CAAAkB,UAAA,mBAAA6B,6EAAA;MAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAyB,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAAsTlD,EAAA,CAAAG,YAAA,EAAW;;;;IAA9PH,EAAA,CAAAoC,UAAA,UAAAe,OAAA,CAAA5C,WAAA,CAAAC,SAAA,uBAAqD,aAAA2C,OAAA,CAAAC,SAAA,CAAAb,OAAA,IAAAY,OAAA,CAAAE,SAAA,CAAAC,OAAA,YAAAH,OAAA,CAAAE,SAAA,CAAAC,OAAA,IAAAC,SAAA,IAAAJ,OAAA,CAAAE,SAAA,CAAAC,OAAA,CAAAE,MAAA,SAAAL,OAAA,CAAAM,iBAAA,CAAAC,UAAA,YAAAP,OAAA,CAAAQ,kBAAA;;;;;IA8B3I3D,EAAA,CAAAC,cAAA,eAAwH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAuD,OAAA,CAAArD,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC5KR,EAAA,CAAAC,cAAA,eAAmF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAwD,OAAA,CAAAtD,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IAClJX,EAAA,CAAAC,cAAA,eAAiF;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAApEH,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAK,iBAAA,CAAAyD,OAAA,CAAAvD,WAAA,CAAAC,SAAA,oCAA4D;;;;;IAC7IR,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAuH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA/HH,EAAA,CAAAI,SAAA,GAAuH;IAAvHJ,EAAA,CAAAK,iBAAA,CAAA0D,OAAA,CAAAxD,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAgD,OAAA,CAAAxD,WAAA,CAAAC,SAAA,0BAAAQ,WAAA,KAAuH;;;;;IAuB7KhB,EAAA,CAAAC,cAAA,eAAwI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAA2D,OAAA,CAAAzD,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC5LR,EAAA,CAAAC,cAAA,eAA2F;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAA4D,OAAA,CAAA1D,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IAC1JX,EAAA,CAAAC,cAAA,eAAyF;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,iBAAA,CAAA6D,OAAA,CAAA3D,WAAA,CAAAC,SAAA,mCAA2D;;;;;IACpJR,EAAA,CAAAC,cAAA,eAA0D;IAAAD,EAAA,CAAAE,MAAA,GAA2H;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnIH,EAAA,CAAAI,SAAA,GAA2H;IAA3HJ,EAAA,CAAAK,iBAAA,CAAA8D,OAAA,CAAA5D,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAoD,OAAA,CAAA5D,WAAA,CAAAC,SAAA,8BAAAQ,WAAA,KAA2H;;;;;IAsCrLhB,EAAA,CAAAC,cAAA,eAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAA+D,OAAA,CAAA7D,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAuB9KR,EAAA,CAAAC,cAAA,eAA8H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAgE,OAAA,CAAA9D,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAmDlLR,EAAA,CAAAC,cAAA,eAAgI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAiE,OAAA,CAAA/D,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACpLR,EAAA,CAAAC,cAAA,eAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAtEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,iBAAA,CAAAkE,OAAA,CAAAhE,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAA8D,GAAA,GAA8D;;;;;IACrJxE,EAAA,CAAAC,cAAA,eAAqF;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAoE,OAAA,CAAAlE,WAAA,CAAAC,SAAA,8BAAsD;;;;;IAsB3IR,EAAA,CAAAC,cAAA,eAAwH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAqE,OAAA,CAAAnE,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC5KR,EAAA,CAAAC,cAAA,eAAmF;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAtEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,iBAAA,CAAAsE,OAAA,CAAApE,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAA8D,GAAA,GAA8D;;;;;IACjJxE,EAAA,CAAAC,cAAA,eAAiF;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAuE,OAAA,CAAArE,WAAA,CAAAC,SAAA,8BAAsD;;;;;IAsBvIR,EAAA,CAAAC,cAAA,eAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAwE,OAAA,CAAAtE,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC9KR,EAAA,CAAAC,cAAA,eAAoF;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAtEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,iBAAA,CAAAyE,OAAA,CAAAvE,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAA8D,GAAA,GAA8D;;;;;IAClJxE,EAAA,CAAAC,cAAA,eAAkF;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAA0E,OAAA,CAAAxE,WAAA,CAAAC,SAAA,8BAAsD;;;;;IAsBxIR,EAAA,CAAAC,cAAA,eAA0I;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAA2E,OAAA,CAAAzE,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC9LR,EAAA,CAAAC,cAAA,eAA4F;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAA4E,OAAA,CAAA1E,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IAC3JX,EAAA,CAAAC,cAAA,eAA0F;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAA6E,OAAA,CAAA3E,WAAA,CAAAC,SAAA,8BAAsD;;;;;;IAIxJR,EAAA,CAAAC,cAAA,cAAsM;IAE9LD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAE,MAAA,qBACA;IAAAF,EAAA,CAAAmF,SAAA,YAAqG;IACzGnF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAA2C;IAGhCD,EAAA,CAAAkB,UAAA,2BAAAkE,8EAAAC,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAkE,IAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA+D,OAAA,CAAAC,aAAA,CAAAC,UAAA,GAAAJ,MAAA,CAC3C;IAAA,EADoE;IAF7CrF,EAAA,CAAAG,YAAA,EAOE;;;;IAbFH,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAA0F,kBAAA,MAAAC,OAAA,CAAApF,WAAA,CAAAC,SAAA,kCACA;IAE6BR,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAoC,UAAA,aAAAuD,OAAA,CAAApF,WAAA,CAAAC,SAAA,kCAAmE;IAKzFR,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAoC,UAAA,YAAAuD,OAAA,CAAAH,aAAA,CAAAC,UAAA,CAAsC,kCAAAE,OAAA,CAAApF,WAAA,CAAAC,SAAA;;;;;IAY7CR,EAAA,CAAAC,cAAA,eAAwF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IACvJX,EAAA,CAAAC,cAAA,eAAsF;IAAAD,EAAA,CAAAE,MAAA,GAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAzEH,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAK,iBAAA,CAAAwF,OAAA,CAAAtF,WAAA,CAAAC,SAAA,yCAAiE;;;;;;IAgBnKR,EAAA,CAAAC,cAAA,mBAAyM;IAA1BD,EAAA,CAAAkB,UAAA,mBAAA4E,8EAAA;MAAA9F,EAAA,CAAAoB,aAAA,CAAA2E,IAAA;MAAA,MAAAC,OAAA,GAAAhG,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAwE,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAACjG,EAAA,CAAAG,YAAA,EAAW;;;;IAApIH,EAAA,CAAAoC,UAAA,UAAA8D,OAAA,CAAA3F,WAAA,CAAAC,SAAA,uBAAqD,aAAA0F,OAAA,CAAAC,yBAAA;;;;;;;;;;;;IA5erJnG,EAAA,CAAAC,cAAA,UAA6B;IAI2CD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnJH,EAAA,CAAAC,cAAA,aAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAkF,sEAAAf,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAC,OAAA,GAAAtG,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA8E,OAAA,CAAAC,WAAA,CAAAC,IAAA,GAAAnB,MAAA,CAChC;IAAA,EADiD,2BAAAe,sEAAA;MAAApG,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAI,OAAA,GAAAzG,EAAA,CAAAuB,aAAA;MAAA,OAMbvB,EAAA,CAAAwB,WAAA,CAAAiF,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EANT;IAFtC1G,EAAA,CAAAG,YAAA,EASE;IAITH,EAAA,CAAAC,cAAA,aAAgE;IAC7DD,EAAA,CAAAmF,SAAA,gBAAoE;IACpEnF,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAA2G,UAAA,KAAAC,+CAAA,mBAAsL;IACtL5G,EAAA,CAAA2G,UAAA,KAAAE,+CAAA,mBAA2J;IAC3J7G,EAAA,CAAA2G,UAAA,KAAAG,+CAAA,mBAAqJ;IACrJ9G,EAAA,CAAA2G,UAAA,KAAAI,+CAAA,mBAAyL;IAC7L/G,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,cAA+C;IACiBD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1HH,EAAA,CAAAC,cAAA,cAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAA8F,4EAAA3B,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAY,OAAA,GAAAjH,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAyF,OAAA,CAAAV,WAAA,CAAAW,MAAA,GAAA7B,MAAA,CAChC;IAAA,EADmD;IAKvCrF,EAAA,CAAAG,YAAA,EAAa;IAItBH,EAAA,CAAAC,cAAA,cAA+C;IACiBD,EAAA,CAAAE,MAAA,IAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjIH,EAAA,CAAAC,cAAA,cAA2C;IAI/BD,EAAA,CAAAkB,UAAA,2BAAAiG,4EAAA9B,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAe,OAAA,GAAApH,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA4F,OAAA,CAAAb,WAAA,CAAAc,aAAA,GAAAhC,MAAA,CAChC;IAAA,EAD0D;IAGFrF,EAAA,CAAAG,YAAA,EAAa;IAIlEH,EAAA,CAAAC,cAAA,cAA+C;IAC8CD,EAAA,CAAAE,MAAA,IAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtJH,EAAA,CAAAC,cAAA,cAA2C;IAKnCD,EAAA,CAAAkB,UAAA,2BAAAoG,0EAAAjC,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAkB,OAAA,GAAAvH,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA+F,OAAA,CAAAhB,WAAA,CAAAiB,WAAA,GAAAnC,MAAA,CAC5B;IAAA,EADoD;IAIxCrF,EAAA,CAAAG,YAAA,EAAW;IAIpBH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAmF,SAAA,iBAA2E;IAC3EnF,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAA2G,UAAA,KAAAc,+CAAA,mBAAkK;IACtKzH,EAAA,CAAAG,YAAA,EAAM;IAIdH,EAAA,CAAAC,cAAA,WAAK;IAEWD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAA2G,UAAA,KAAAe,6CAAA,kBAA4K;IAChL1H,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAmF,SAAA,sBAMc;IAClBnF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEFD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAA2G,UAAA,KAAAgB,6CAAA,kBAAgL;IACpL3H,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAmF,SAAA,sBAOc;IAClBnF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA0E;IACmDD,EAAA,CAAAkB,UAAA,mBAAA0G,kEAAA;MAAA5H,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAwB,OAAA,GAAA7H,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAqG,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAAC9H,EAAA,CAAAG,YAAA,EAAW;IACvJH,EAAA,CAAA2G,UAAA,KAAAoB,kDAAA,uBAA6N;IACjO/H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA0E;IAChCD,EAAA,CAAAkB,UAAA,2BAAA8G,0EAAA3C,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA4B,OAAA,GAAAjI,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAyG,OAAA,CAAAC,iBAAA,GAAA7C,MAAA;IAAA,EAA+B;IACjErF,EAAA,CAAAC,cAAA,eAA8C;IAI+BD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvJH,EAAA,CAAAC,cAAA,eAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAiH,uEAAA9C,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA+B,OAAA,GAAApI,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA4G,OAAA,CAAA/E,SAAA,CAAAgF,SAAA,GAAAhD,MAAA,CAC5C;IAAA,EADgE,2BAAA8C,uEAAA;MAAAnI,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAiC,OAAA,GAAAtI,EAAA,CAAAuB,aAAA;MAAA,OAMhBvB,EAAA,CAAAwB,WAAA,CAAA8G,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EANL;IAFzCvI,EAAA,CAAAG,YAAA,EASE;IAIVH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAmF,SAAA,gBAAoE;IACpEnF,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAA2G,UAAA,KAAA6B,+CAAA,mBAAoL;IACpLxI,EAAA,CAAA2G,UAAA,KAAA8B,+CAAA,mBAA0J;IAC1JzI,EAAA,CAAA2G,UAAA,KAAA+B,+CAAA,mBAAoJ;IACpJ1I,EAAA,CAAA2G,UAAA,KAAAgC,+CAAA,mBAAuL;IAC3L3I,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,cAA+C;IACeD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7IH,EAAA,CAAAC,cAAA,eAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAA0H,4EAAAvD,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAwC,OAAA,GAAA7I,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAqH,OAAA,CAAAxF,SAAA,CAAAyF,MAAA,GAAAzD,MAAA,CAC5C;IAAA,EAD6D;IAOrCrF,EAAA,CAAAG,YAAA,EAAa;IAItBH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAmF,SAAA,gBAAoE;IACpEnF,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAA2G,UAAA,KAAAoC,+CAAA,mBAA8K;IAClL/I,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAqD;IACkCD,EAAA,CAAAE,MAAA,IAA+C;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrKH,EAAA,CAAAC,cAAA,eAAmD;IAK3CD,EAAA,CAAAkB,UAAA,2BAAA8H,0EAAA3D,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA4C,OAAA,GAAAjJ,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAyH,OAAA,CAAA5F,SAAA,CAAA6F,KAAA,GAAA7D,MAAA,CACxC;IAAA,EADwD;IAIhCrF,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAC,cAAA,gBAAsL;IAA3BD,EAAA,CAAAkB,UAAA,mBAAAiI,8DAAA9D,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA+C,OAAA,GAAApJ,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA4H,OAAA,CAAAC,QAAA,CAAAhE,MAAA,CAAgB;IAAA,EAAC;IAACrF,EAAA,CAAAG,YAAA,EAAO;IAGjMH,EAAA,CAAAC,cAAA,cAAgE;IAC5DD,EAAA,CAAAmF,SAAA,gBAAoE;IACpEnF,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAA2G,UAAA,KAAA2C,+CAAA,mBAA4K;IAChLtJ,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAoC;IAE5BD,EAAA,CAAAkB,UAAA,yBAAAqI,gFAAAlE,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAmD,OAAA,GAAAxJ,EAAA,CAAAuB,aAAA;MAAA,OAAWvB,EAAA,CAAAwB,WAAA,CAAAgI,OAAA,CAAAnG,SAAA,CAAAC,OAAA,GAAA+B,MAAA,CAC9B;IAAA,EADgD;IAMhCrF,EAAA,CAAAG,YAAA,EAAmB;IAG5BH,EAAA,CAAAC,cAAA,eAA0E;IACmDD,EAAA,CAAAkB,UAAA,mBAAAuI,kEAAA;MAAAzJ,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAqD,OAAA,GAAA1J,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAkI,OAAA,CAAAxB,iBAAA,GAA6B,KAAK;IAAA,EAAC;IAAClI,EAAA,CAAAG,YAAA,EAAW;IACxKH,EAAA,CAAA2G,UAAA,KAAAgD,kDAAA,uBAAgW;IACpW3J,EAAA,CAAAG,YAAA,EAAM;IAKdH,EAAA,CAAAC,cAAA,eAA2D;IACbD,EAAA,CAAAkB,UAAA,2BAAA0I,0EAAAvE,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAwD,OAAA,GAAA7J,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAqI,OAAA,CAAAC,qBAAA,GAAAzE,MAAA;IAAA,EAAmC;IACzErF,EAAA,CAAAC,cAAA,gBAAuC;IAIkCD,EAAA,CAAAE,MAAA,KAAkD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClJH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAA6I,wEAAA1E,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA2D,OAAA,GAAAhK,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAwI,OAAA,CAAAxE,aAAA,CAAAyE,KAAA,GAAA5E,MAAA,CAC5C;IAAA,EADgE,2BAAA0E,wEAAA;MAAA/J,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA6D,OAAA,GAAAlK,EAAA,CAAAuB,aAAA;MAAA,OAMhBvB,EAAA,CAAAwB,WAAA,CAAA0I,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EANJ;IAFzCnK,EAAA,CAAAG,YAAA,EASE;IAIVH,EAAA,CAAAC,cAAA,eAAiE;IAC7DD,EAAA,CAAAmF,SAAA,kBAAqE;IACrEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAAyD,gDAAA,mBAAoL;IACpLpK,EAAA,CAAA2G,UAAA,MAAA0D,gDAAA,mBAA0J;IAC1JrK,EAAA,CAAA2G,UAAA,MAAA2D,gDAAA,mBAAqJ;IACrJtK,EAAA,CAAA2G,UAAA,MAAA4D,gDAAA,mBAAqL;IACzLvK,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAgD;IAC0BD,EAAA,CAAAE,MAAA,KAAsD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/JH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAsJ,wEAAAnF,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAoE,OAAA,GAAAzK,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAiJ,OAAA,CAAAjF,aAAA,CAAAkF,aAAA,GAAArF,MAAA,CAC5C;IAAA,EADwE,2BAAAmF,wEAAA;MAAAxK,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAsE,OAAA,GAAA3K,EAAA,CAAAuB,aAAA;MAAA,OAMxBvB,EAAA,CAAAwB,WAAA,CAAAmJ,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EANA;IAFjD5K,EAAA,CAAAG,YAAA,EASE;IAIVH,EAAA,CAAAC,cAAA,eAAiE;IAC7DD,EAAA,CAAAmF,SAAA,iBAAoE;IACpEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAAkE,gDAAA,mBAAoM;IACpM7K,EAAA,CAAA2G,UAAA,MAAAmE,gDAAA,mBAAkK;IAClK9K,EAAA,CAAA2G,UAAA,MAAAoE,gDAAA,mBAA4J;IAC5J/K,EAAA,CAAA2G,UAAA,MAAAqE,gDAAA,mBAA6L;IACjMhL,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAgD;IACgBD,EAAA,CAAAE,MAAA,KAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtHH,EAAA,CAAAC,cAAA,gBAA2C;IAMnCD,EAAA,CAAAkB,UAAA,2BAAA+J,6EAAA5F,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA6E,OAAA,GAAAlL,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA0J,OAAA,CAAA1F,aAAA,CAAA2F,QAAA,GAAA9F,MAAA,CACxC;IAAA,EAD+D;IAEvCrF,EAAA,CAAAG,YAAA,EAAa;IAItBH,EAAA,CAAAC,cAAA,eAAgD;IACcD,EAAA,CAAAE,MAAA,KAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChJH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAkK,6EAAA/F,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAgF,OAAA,GAAArL,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA6J,OAAA,CAAA7F,aAAA,CAAA8F,MAAA,GAAAjG,MAAA,CAC5C;IAAA,EADiE,2BAAA+F,6EAAA;MAAApL,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAkF,OAAA,GAAAvL,EAAA,CAAAuB,aAAA;MAAA,OAOjBvB,EAAA,CAAAwB,WAAA,CAAA+J,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAPA;IAQzCxL,EAAA,CAAAG,YAAA,EAAa;IAItBH,EAAA,CAAAC,cAAA,eAAiE;IAC7DD,EAAA,CAAAmF,SAAA,iBAAoE;IACpEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAA8E,gDAAA,mBAAsL;IAC1LzL,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAkK;IAClGD,EAAA,CAAAE,MAAA,KAAkD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjJH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAwK,6EAAArG,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAsF,OAAA,GAAA3L,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAmK,OAAA,CAAAnG,aAAA,CAAAoG,QAAA,GAAAvG,MAAA,CAC5C;IAAA,EADmE;IAO3CrF,EAAA,CAAAG,YAAA,EAAa;IAItBH,EAAA,CAAAC,cAAA,eAAiE;IAC7DD,EAAA,CAAAmF,SAAA,kBAAwE;IACxEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAAkF,gDAAA,mBAA0L;IAC9L7L,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAqP;IAE3KD,EAAA,CAAAE,MAAA,KAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClIH,EAAA,CAAAC,cAAA,gBAA2C;IAMnCD,EAAA,CAAAkB,UAAA,2BAAA4K,6EAAAzG,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA0F,OAAA,GAAA/L,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAuK,OAAA,CAAAvG,aAAA,CAAAwG,cAAA,GAAA3G,MAAA,CAC5C;IAAA,EADyE,2BAAAyG,6EAAA;MAAA9L,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA4F,OAAA,GAAAjM,EAAA,CAAAuB,aAAA;MAAA,OACQvB,EAAA,CAAAwB,WAAA,CAAAyK,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAD9B;IAEzClM,EAAA,CAAAG,YAAA,EAAa;IAG1BH,EAAA,CAAAC,cAAA,gBAAuJ;IAClFD,EAAA,CAAAE,MAAA,KAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChIH,EAAA,CAAAC,cAAA,gBAA2C;IAMnCD,EAAA,CAAAkB,UAAA,2BAAAiL,6EAAA9G,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA+F,OAAA,GAAApM,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA4K,OAAA,CAAA5G,aAAA,CAAA6G,aAAA,GAAAhH,MAAA,CAC5C;IAAA,EADwE;IACTrF,EAAA,CAAAG,YAAA,EAAa;IAK7DH,EAAA,CAAAC,cAAA,gBAA8F;IACzBD,EAAA,CAAAE,MAAA,KAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvJH,EAAA,CAAAC,cAAA,gBAA2C;IAGnCD,EAAA,CAAAkB,UAAA,2BAAAoL,6EAAAjH,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAkG,OAAA,GAAAvM,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA+K,OAAA,CAAA/G,aAAA,CAAAgH,SAAA,GAAAnH,MAAA,CACxC;IAAA,EADgE;IAO5CrF,EAAA,CAAAG,YAAA,EAAa;IAIlBH,EAAA,CAAAC,cAAA,gBAAgD;IAC5CD,EAAA,CAAAmF,SAAA,kBAAqE;IACrEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAA8F,gDAAA,mBAA4L;IAC5LzM,EAAA,CAAA2G,UAAA,MAAA+F,gDAAA,mBAA6J;IAC7J1M,EAAA,CAAA2G,UAAA,MAAAgG,gDAAA,mBAAmJ;IACvJ3M,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA8F;IAC7BD,EAAA,CAAAE,MAAA,KAA+C;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/IH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAA0L,wEAAAvH,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAwG,OAAA,GAAA7M,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAqL,OAAA,CAAArH,aAAA,CAAAsH,KAAA,GAAAzH,MAAA,CAC5C;IAAA,EADgE;IAFzCrF,EAAA,CAAAG,YAAA,EAQE;IAIVH,EAAA,CAAAC,cAAA,gBAAgD;IAC5CD,EAAA,CAAAmF,SAAA,kBAAqE;IACrEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAAoG,gDAAA,mBAAoL;IACpL/M,EAAA,CAAA2G,UAAA,MAAAqG,gDAAA,mBAAyJ;IACzJhN,EAAA,CAAA2G,UAAA,MAAAsG,gDAAA,mBAA+I;IACnJjN,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA8F;IAC5BD,EAAA,CAAAE,MAAA,KAAgD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjJH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAgM,wEAAA7H,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA8G,OAAA,GAAAnN,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAA2L,OAAA,CAAA3H,aAAA,CAAA4H,MAAA,GAAA/H,MAAA,CAC5C;IAAA,EADiE;IAF1CrF,EAAA,CAAAG,YAAA,EAQE;IAIVH,EAAA,CAAAC,cAAA,gBAAgD;IAC5CD,EAAA,CAAAmF,SAAA,kBAAqE;IACrEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAA0G,gDAAA,mBAAsL;IACtLrN,EAAA,CAAA2G,UAAA,MAAA2G,gDAAA,mBAA0J;IAC1JtN,EAAA,CAAA2G,UAAA,MAAA4G,gDAAA,mBAAgJ;IACpJvN,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA8F;IACpBD,EAAA,CAAAE,MAAA,KAAwD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjKH,EAAA,CAAAC,cAAA,gBAA2C;IAG/BD,EAAA,CAAAkB,UAAA,2BAAAsM,wEAAAnI,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAoH,OAAA,GAAAzN,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAiM,OAAA,CAAAjI,aAAA,CAAAkI,cAAA,GAAArI,MAAA,CAC5C;IAAA,EADyE;IAFlDrF,EAAA,CAAAG,YAAA,EAOE;IAKVH,EAAA,CAAAC,cAAA,gBAAgD;IAC5CD,EAAA,CAAAmF,SAAA,kBAAqE;IACrEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAAgH,gDAAA,mBAAsM;IACtM3N,EAAA,CAAA2G,UAAA,MAAAiH,gDAAA,mBAAmK;IACnK5N,EAAA,CAAA2G,UAAA,MAAAkH,gDAAA,mBAAwJ;IAC5J7N,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAA2G,UAAA,MAAAmH,8CAAA,kBAiBM;IAEN9N,EAAA,CAAAC,cAAA,gBAAgD;IAC5CD,EAAA,CAAAmF,SAAA,kBAAqE;IACrEnF,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAA2G,UAAA,MAAAoH,gDAAA,mBAA+J;IAC/J/N,EAAA,CAAA2G,UAAA,MAAAqH,gDAAA,mBAA+J;IACnKhO,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,gBAAoB;IAEZD,EAAA,CAAAkB,UAAA,yBAAA+M,iFAAA5I,MAAA;MAAArF,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAA6H,OAAA,GAAAlO,EAAA,CAAAuB,aAAA;MAAA,OAAWvB,EAAA,CAAAwB,WAAA,CAAA0M,OAAA,CAAA1I,aAAA,CAAA2I,SAAA,GAAA9I,MAAA,CAC9B;IAAA,EADsD;IAKtCrF,EAAA,CAAAG,YAAA,EAAmB;IAG5BH,EAAA,CAAAC,cAAA,gBAA0E;IACmDD,EAAA,CAAAkB,UAAA,mBAAAkN,mEAAA;MAAApO,EAAA,CAAAoB,aAAA,CAAAiF,IAAA;MAAA,MAAAgI,OAAA,GAAArO,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAA6M,OAAA,CAAAvE,qBAAA,GAAiC,KAAK;IAAA,EAAC;IAAC9J,EAAA,CAAAG,YAAA,EAAW;IAC5KH,EAAA,CAAA2G,UAAA,MAAA2H,mDAAA,uBAAoN;IACxNtO,EAAA,CAAAG,YAAA,EAAM;;;;IA5eRH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAoC,UAAA,cAAAmM,MAAA,CAAAjM,eAAA,CAA6B;IAGiCtC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,4BAAoD;IAIpGR,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAAhI,WAAA,CAAAC,IAAA,CAA8B,oDAAA+H,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAcTR,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAiI,KAAA,KAAAF,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAkI,MAAA,kBAAAH,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAkI,MAAA,CAAAvD,QAAA,EAA2F;IAC3FnL,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAkI,MAAA,kBAAAH,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAkI,MAAA,CAAAC,SAAA,CAAqD;IACrD3O,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAkI,MAAA,kBAAAH,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhI,IAAA,CAAAkI,MAAA,CAAAE,OAAA,CAAmD;IACnD5O,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA/L,mBAAA,CAAyB;IAKExC,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,8BAAsD;IAG1FR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoC,UAAA,2BAA0B,YAAAmM,MAAA,CAAAhI,WAAA,CAAAW,MAAA,aAAAqH,MAAA,CAAAM,aAAA;IAWU7O,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,qCAA6D;IAK7GR,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAAhI,WAAA,CAAAc,aAAA,CAAuC,cAAAkH,MAAA,CAAAO,aAAA,CAAAC,MAAA,gBAAAR,MAAA,CAAAO,aAAA,CAAAE,OAAA;IAQsChP,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,6BAAqD;IAItIR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoC,UAAA,qBAAoB,YAAAmM,MAAA,CAAAhI,WAAA,CAAAiB,WAAA,mCAAA+G,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAaKR,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhH,WAAA,CAAAkH,MAAA,kBAAAH,MAAA,CAAAjM,eAAA,CAAAkM,QAAA,CAAAhH,WAAA,CAAAkH,MAAA,CAAAC,SAAA,CAA4D;IAOrF3O,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,2BAAmD;IACrDR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,UAAA,CAAAC,MAAA,CAAmC;IAGzCnP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAoC,UAAA,iBAAgB,YAAAmM,MAAA,CAAAa,YAAA,aAAAb,MAAA,CAAAc,UAAA,aAAAd,MAAA,CAAAe,oBAAA;IAURtP,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,2BAAmD;IACrDR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,UAAA,CAAAC,MAAA,CAAmC;IAGzCnP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAoC,UAAA,iBAAgB,YAAAmM,MAAA,CAAAgB,YAAA,aAAAhB,MAAA,CAAAiB,UAAA,aAAAjB,MAAA,CAAAkB,oBAAA;IAU6CzP,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoC,UAAA,UAAAmM,MAAA,CAAAhO,WAAA,CAAAC,SAAA,yBAAuD;IAC7GR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,UAAA,CAAAC,MAAA,CAAmC;IAKuCnP,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAAU,eAAA,MAAAiP,GAAA,EAAoC;IAA/G3P,EAAA,CAAAoC,UAAA,WAAAmM,MAAA,CAAAqB,cAAA,GAA2B,YAAArB,MAAA,CAAArG,iBAAA;IAEvBlI,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAoC,UAAA,cAAAmM,MAAA,CAAAnL,SAAA,CAAuB;IAG4CpD,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,2BAAmD;IAIxGR,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAAlL,SAAA,CAAAgF,SAAA,CAAiC,oDAAAkG,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAcZR,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAoG,KAAA,KAAAF,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAqG,MAAA,kBAAAH,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAqG,MAAA,CAAAvD,QAAA,EAAyF;IACzFnL,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAqG,MAAA,kBAAAH,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAqG,MAAA,CAAAC,SAAA,CAAoD;IACpD3O,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAqG,MAAA,kBAAAH,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAnG,SAAA,CAAAqG,MAAA,CAAAE,OAAA,CAAkD;IAClD5O,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA5K,kBAAA,CAAwB;IAKC3D,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,wBAAgD;IAGlFR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoC,UAAA,2BAA0B,YAAAmM,MAAA,CAAAlL,SAAA,CAAAyF,MAAA,aAAAyF,MAAA,CAAAsB,OAAA,mCAAAtB,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAejBR,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAA1F,MAAA,CAAA2F,KAAA,KAAAF,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAA1F,MAAA,CAAA4F,MAAA,kBAAAH,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAA1F,MAAA,CAAA4F,MAAA,CAAAvD,QAAA,EAAmF;IAKjCnL,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,uBAA+C;IAI1HR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoC,UAAA,qBAAoB,YAAAmM,MAAA,CAAAlL,SAAA,CAAA6F,KAAA,iBAAAqF,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAQsER,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAoC,UAAA,aAAAmM,MAAA,CAAAhO,WAAA,CAAAC,SAAA,uBAAwD;IAMzHR,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAtF,KAAA,CAAAuF,KAAA,KAAAF,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAtF,KAAA,CAAAwF,MAAA,kBAAAH,MAAA,CAAAnL,SAAA,CAAAoL,QAAA,CAAAtF,KAAA,CAAAwF,MAAA,CAAAvD,QAAA,EAAiF;IAMlHnL,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAoC,UAAA,UAAAmM,MAAA,CAAAlL,SAAA,CAAAC,OAAA,CAA6B,YAAAiL,MAAA,CAAAuB,gBAAA,aAAAvB,MAAA,CAAAwB,gBAAA,aAAAxB,MAAA,CAAA9K,iBAAA;IAU4BzD,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoC,UAAA,UAAAmM,MAAA,CAAAhO,WAAA,CAAAC,SAAA,yBAAuD;IACvFR,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAAyB,SAAA,IAAAzB,MAAA,CAAAW,UAAA,CAAAC,MAAA,CAAoC;IAOgBnP,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAAU,eAAA,MAAAiP,GAAA,EAAmC;IAAtH3P,EAAA,CAAAoC,UAAA,WAAAmM,MAAA,CAAA0B,kBAAA,GAA+B,YAAA1B,MAAA,CAAAzE,qBAAA;IAE3B9J,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAoC,UAAA,cAAAmM,MAAA,CAAA2B,aAAA,CAA2B;IAGoClQ,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,0BAAkD;IAInGR,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAA/I,aAAA,CAAAyE,KAAA,CAAiC,oDAAAsE,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAcZR,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAwE,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAyE,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAyE,MAAA,CAAAvD,QAAA,EAAyF;IACzFnL,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAyE,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAyE,MAAA,CAAAC,SAAA,CAAoD;IACpD3O,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAyE,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAvE,KAAA,CAAAyE,MAAA,CAAAE,OAAA,CAAkD;IAClD5O,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA4B,iBAAA,CAAuB;IAKcnQ,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,8BAAsD;IAIhHR,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAA/I,aAAA,CAAAkF,aAAA,CAAyC,oDAAA6D,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAcpBR,EAAA,CAAAI,SAAA,GAAyG;IAAzGJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAA+D,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAAgE,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAAgE,MAAA,CAAAvD,QAAA,EAAyG;IACzGnL,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAAgE,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAAgE,MAAA,CAAAC,SAAA,CAA4D;IAC5D3O,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAAgE,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA9D,aAAA,CAAAgE,MAAA,CAAAE,OAAA,CAA0D;IAC1D5O,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA6B,qBAAA,CAA2B;IAKApQ,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,0BAAkD;IAItGR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAoC,UAAA,mBAAkB,iCAAAmM,MAAA,CAAA/I,aAAA,CAAA2F,QAAA;IAUgCnL,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,2BAAmD;IAGrFR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoC,UAAA,2BAA0B,YAAAmM,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,aAAAiD,MAAA,CAAA8B,cAAA,mCAAA9B,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAgBjBR,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAlD,MAAA,CAAAmD,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAlD,MAAA,CAAAoD,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAlD,MAAA,CAAAoD,MAAA,CAAAvD,QAAA,EAA2F;IAIhFnL,EAAA,CAAAI,SAAA,GAAiH;IAAjHJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAC,IAAA,IAAAjC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAE,SAAA,iBAAiH;IACjGzQ,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,0BAAkD;IAGpFR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoC,UAAA,2BAA0B,YAAAmM,MAAA,CAAA/I,aAAA,CAAAoG,QAAA,aAAA2C,MAAA,CAAAmC,SAAA,cAAAnC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAC,IAAA,IAAAjC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAE,SAAA,iBAAAlC,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAenBR,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA5C,QAAA,CAAA6C,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA5C,QAAA,CAAA8C,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA5C,QAAA,CAAA8C,MAAA,CAAAvD,QAAA,EAA+F;IAIvDnL,EAAA,CAAAI,SAAA,GAAuK;IAAvKJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAI,WAAA,IAAApC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAK,MAAA,IAAArC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAM,WAAA,iBAAuK;IACpN7Q,EAAA,CAAAI,SAAA,GAA+G;IAA/GJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAK,MAAA,IAAArC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAM,WAAA,WAA+G;IACrE7Q,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,gCAAwD;IAIlHR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAoC,UAAA,mBAAkB,iCAAAmM,MAAA,CAAA/I,aAAA,CAAAwG,cAAA;IAQFhM,EAAA,CAAAI,SAAA,GAA0H;IAA1HJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAI,WAAA,IAAApC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAM,WAAA,iBAA0H;IACjF7Q,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,+BAAuD;IAKhHR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAoC,UAAA,mBAAkB,iCAAAmM,MAAA,CAAA/I,aAAA,CAAA6G,aAAA;IAQHrM,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,yBAA8D;IACxBhM,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,2BAAmD;IAG7FR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoC,UAAA,2BAA0B,YAAAmM,MAAA,CAAA/I,aAAA,CAAAgH,SAAA,aAAA+B,MAAA,CAAAuC,aAAA,cAAAvC,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,yBAAAuC,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAehBR,EAAA,CAAAI,SAAA,GAAiG;IAAjGJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAiC,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAkC,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAkC,MAAA,CAAAvD,QAAA,EAAiG;IACjGnL,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAkC,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAkC,MAAA,CAAAC,SAAA,CAAwD;IACxD3O,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAkC,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAhC,SAAA,CAAAkC,MAAA,CAAAE,OAAA,CAAsD;IAI5D5O,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,yBAA8D;IAC5BhM,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,uBAA+C;IAIhGR,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAA/I,aAAA,CAAAsH,KAAA,CAAiC,aAAAyB,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,0CAAAuC,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAaZR,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA2B,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA4B,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA4B,MAAA,CAAAvD,QAAA,EAAyF;IACzFnL,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA4B,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA4B,MAAA,CAAAC,SAAA,CAAoD;IACpD3O,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA4B,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA1B,KAAA,CAAA4B,MAAA,CAAAE,OAAA,CAAkD;IAIxD5O,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,yBAA8D;IAC3BhM,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,wBAAgD;IAIlGR,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAA/I,aAAA,CAAA4H,MAAA,CAAkC,aAAAmB,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,0CAAAuC,MAAA,CAAAhO,WAAA,CAAAC,SAAA;IAabR,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAqB,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAsB,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAsB,MAAA,CAAAvD,QAAA,EAA2F;IAC3FnL,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAsB,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAsB,MAAA,CAAAC,SAAA,CAAqD;IACrD3O,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAsB,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAApB,MAAA,CAAAsB,MAAA,CAAAE,OAAA,CAAmD;IAIzD5O,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAsQ,UAAA,CAAA/B,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,yBAA8D;IACnBhM,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAkO,MAAA,CAAAhO,WAAA,CAAAC,SAAA,gCAAwD;IAIlHR,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAoC,UAAA,YAAAmM,MAAA,CAAA/I,aAAA,CAAAkI,cAAA,CAA0C,aAAAa,MAAA,CAAA/I,aAAA,CAAAwG,cAAA;IAarBhM,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAe,KAAA,KAAAF,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAgB,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAgB,MAAA,CAAAvD,QAAA,EAA2G;IAC3GnL,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAgB,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAgB,MAAA,CAAAC,SAAA,CAA6D;IAC7D3O,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAgB,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAAd,cAAA,CAAAgB,MAAA,CAAAE,OAAA,CAA2D;IAI1F5O,EAAA,CAAAI,SAAA,GAAoK;IAApKJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,KAAAuC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAA1O,SAAA,CAAAkR,cAAA,CAAAJ,WAAA,IAAApC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAA1O,SAAA,CAAAkR,cAAA,CAAAF,WAAA,EAAoK;IAsBrI7Q,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA/I,UAAA,CAAAiJ,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA/I,UAAA,CAAAiJ,MAAA,CAAAC,SAAA,CAAyD;IACzD3O,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA/I,UAAA,CAAAiJ,MAAA,kBAAAH,MAAA,CAAA2B,aAAA,CAAA1B,QAAA,CAAA/I,UAAA,CAAAiJ,MAAA,CAAAE,OAAA,CAAuD;IAK1E5O,EAAA,CAAAI,SAAA,GAAoK;IAApKJ,EAAA,CAAAsQ,UAAA,EAAA/B,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAI,WAAA,IAAApC,MAAA,CAAA/I,aAAA,CAAA8F,MAAA,IAAAiD,MAAA,CAAAgC,UAAA,CAAAM,WAAA,KAAAtC,MAAA,CAAA/I,aAAA,CAAAwG,cAAA,0BAAoK;IAClLhM,EAAA,CAAAoC,UAAA,UAAAmM,MAAA,CAAA/I,aAAA,CAAA2I,SAAA,CAAmC,YAAAI,MAAA,CAAAyC,gBAAA,aAAAzC,MAAA,CAAA0C,gBAAA,aAAA1C,MAAA,CAAA2C,iBAAA;IASsBlR,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoC,UAAA,UAAAmM,MAAA,CAAAhO,WAAA,CAAAC,SAAA,yBAAuD;IAC7GR,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAoC,UAAA,SAAAmM,MAAA,CAAA4C,aAAA,IAAA5C,MAAA,CAAAW,UAAA,CAAAC,MAAA,CAAwC;;;ADlbnE,OAAM,MAAOiC,8BAA8B;AAO3C,OAAM,MAAOC,uBAAwB,SAAQzR,aAAa;EACtD0R,YAAYC,QAAkB,EACVC,WAAwB,EACxBC,aAA4B;IAC5C,KAAK,CAACF,QAAQ,CAAC;IAFC,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IAUjC,KAAAC,aAAa,GAAW,IAAI;IAC5B,KAAAlP,mBAAmB,GAAY,KAAK;IAcpC,KAAA0M,UAAU,GAAGrP,SAAS,CAAC8R,SAAS;IAChC,KAAApB,UAAU,GAAG1Q,SAAS,CAACkR,cAAc;IACrC,KAAAf,SAAS,GAAGnQ,SAAS,CAAC8R,SAAS,CAACC,MAAM;IACtC,KAAAT,aAAa,GAAGtR,SAAS,CAAC8R,SAAS,CAACC,MAAM;IAC1C,KAAA1J,iBAAiB,GAAY,KAAK;IAClC,KAAA4B,qBAAqB,GAAY,KAAK;IAKtC,KAAAnG,kBAAkB,GAAY,KAAK;IACnC,KAAAwM,iBAAiB,GAAY,KAAK;IAClC,KAAAC,qBAAqB,GAAY,KAAK;IAatC,KAAAyB,cAAc,GAAG,CAAC,CAAC;IACnB,KAAAC,kBAAkB,GAAG,CAAC,CAAC;IAGvB,KAAAhD,aAAa,GAAGjP,SAAS,CAACkS,cAAc;IAgsBrB,KAAAlS,SAAS,GAAGA,SAAS;EApvBxC;EAqDAmS,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACP,aAAa,GAAG,IAAI,CAACnL,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,IAAI,GAAG,IAAI;IACpE,IAAI,CAACqI,aAAa,GAAG,CACjB;MAACqD,KAAK,EAAErS,SAAS,CAACsS,aAAa,CAACC,MAAM;MAAE5L,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,sBAAsB;IAAC,CAAC,EACjG;MAAC0R,KAAK,EAAErS,SAAS,CAACsS,aAAa,CAACE,QAAQ;MAAE7L,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,wBAAwB;IAAC,CAAC,CACxG;IAED;IACA,IAAI,CAAC4O,YAAY,GAAE,CAAC;MAChBkD,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,WAAW;MAChBjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DkS,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE,8BAA8B;MACzCC,SAAS,EAAEA,CAACC,EAAE,EAAEC,IAAI,KAAG;QACnBb,EAAE,CAAC5O,SAAS,GAAG;UAAC,GAAGyP;QAAI,CAAC;QACxBb,EAAE,CAAC7O,SAAS,GAAG,IAAI,CAACoO,WAAW,CAACuB,KAAK,CAAC,IAAI,CAAC1P,SAAS,CAAC;QACrD4O,EAAE,CAAC7O,SAAS,CAAC4P,GAAG,CAAC,WAAW,CAAC,CAACC,OAAO,CAAC;UAACC,QAAQ,EAAE;QAAI,CAAC,CAAC;QACvDjB,EAAE,CAAC7O,SAAS,CAAC4P,GAAG,CAAC,QAAQ,CAAC,CAACC,OAAO,CAAC;UAACC,QAAQ,EAAE;QAAI,CAAC,CAAC;QACpDjB,EAAE,CAAC7O,SAAS,CAAC4P,GAAG,CAAC,OAAO,CAAC,CAACC,OAAO,CAAC;UAACC,QAAQ,EAAE;QAAI,CAAC,CAAC;QACnDjB,EAAE,CAACtO,kBAAkB,GAAG,KAAK;QAC7BsO,EAAE,CAAC5O,SAAS,CAACC,OAAO,GAAG,EAAE;QACzB,IAAI6P,eAAe,GAAGlB,EAAE,CAAC5O,SAAS,CAAC+P,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC;QAC3D,IAAIC,aAAa,GAAGrB,EAAE,CAAC5O,SAAS,CAACkQ,iBAAiB,CAACF,KAAK,CAAC,GAAG,CAAC;QAC7DF,eAAe,CAACK,OAAO,CAAE,CAACC,EAAE,EAAEC,KAAK,KAAG;UAClCzB,EAAE,CAAC5O,SAAS,CAACC,OAAO,CAACqQ,IAAI,CAAC;YACtBd,EAAE,EAAE,IAAI;YACRe,OAAO,EAAEH,EAAE;YACXhB,GAAG,EAAEa,aAAa,CAACI,KAAK;WAC3B,CAAC;QACN,CAAC,CAAC;QACFzB,EAAE,CAAClC,gBAAgB,CAAC8D,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QACrD8C,EAAE,CAACxO,iBAAiB,CAACqQ,KAAK,EAAE;QAC5B7B,EAAE,CAACjC,SAAS,GAAGnQ,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QACzC8C,EAAE,CAAC/J,iBAAiB,GAAG,IAAI;MAC/B;KACH,CAAC;IACF,IAAI,CAACoH,oBAAoB,GAAG;MACxByE,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,KAAK;MACnBC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CAAC;QACLC,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,EAAE,IAAI,CAAChU,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDgU,IAAI,EAAEA,CAAC3B,EAAE,EAAEC,IAAI,KAAI;UACfb,EAAE,CAAC5O,SAAS,GAAG;YAAC,GAAGyP;UAAI,CAAC;UACxBb,EAAE,CAAC7O,SAAS,GAAG6O,EAAE,CAACT,WAAW,CAACuB,KAAK,CAACd,EAAE,CAAC5O,SAAS,CAAC;UACjD4O,EAAE,CAACtO,kBAAkB,GAAG,KAAK;UAC7BsO,EAAE,CAAC5O,SAAS,CAACC,OAAO,GAAG,EAAE;UACzB,IAAI6P,eAAe,GAAGlB,EAAE,CAAC5O,SAAS,CAAC+P,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC;UAC3D,IAAIC,aAAa,GAAGrB,EAAE,CAAC5O,SAAS,CAACkQ,iBAAiB,CAACF,KAAK,CAAC,GAAG,CAAC;UAC7DF,eAAe,CAACK,OAAO,CAAE,CAACC,EAAE,EAAEC,KAAK,KAAG;YAClCzB,EAAE,CAAC5O,SAAS,CAACC,OAAO,CAACqQ,IAAI,CAAC;cACtBd,EAAE,EAAEa,KAAK,GAAG,CAAC;cACbE,OAAO,EAAEH,EAAE;cACXhB,GAAG,EAAEa,aAAa,CAACI,KAAK;aAC3B,CAAC;UACN,CAAC,CAAC;UACFzB,EAAE,CAAClC,gBAAgB,CAAC8D,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAAC8C,MAAM;UACrDxC,EAAE,CAACxO,iBAAiB,CAACqQ,KAAK,EAAE;UAC5B7B,EAAE,CAACjC,SAAS,GAAGnQ,SAAS,CAAC8R,SAAS,CAAC8C,MAAM;UACzCxC,EAAE,CAAC/J,iBAAiB,GAAG,IAAI;QAC/B,CAAC;QACDwM,UAAUA,CAAC7B,EAAE,EAAEC,IAAI;UACf,OAAOb,EAAE,CAAChD,QAAQ,IAAIpP,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QACpD;OACH,EAAC;QACEmF,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE,IAAI,CAAChU,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3DgU,IAAI,EAAEA,CAAC3B,EAAE,EAAEC,IAAI,KAAI;UACf,KAAI,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,EAAE,CAAC5C,UAAU,CAACuF,OAAO,CAACpR,MAAM,EAACmR,CAAC,EAAE,EAAC;YAChD,IAAG1C,EAAE,CAAC5C,UAAU,CAACuF,OAAO,CAACD,CAAC,CAAC,CAAC9B,EAAE,IAAIA,EAAE,EAAC;cACjCZ,EAAE,CAAC5C,UAAU,CAACuF,OAAO,CAACC,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;cAClC1C,EAAE,CAAC1L,WAAW,CAACuO,cAAc,GAAG7C,EAAE,CAAC5C,UAAU,CAACuF,OAAO;cACrD;;;QAGZ,CAAC;QACDF,UAAUA,CAAC7B,EAAE,EAAEC,IAAI;UACf,OAAOb,EAAE,CAAChD,QAAQ,IAAIpP,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QACpD;OACH;KACJ;IAED,IAAI,CAACU,OAAO,GAAG,CACX;MAACqC,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACC,IAAI;MAAExO,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAC,EACtF;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACE,IAAI;MAAEzO,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAC,EACtF;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACG,GAAG;MAAE1O,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,mBAAmB;IAAC,CAAC,EACpF;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACI,OAAO;MAAE3O,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,uBAAuB;IAAC,CAAC,EAC5F;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACK,MAAM;MAAE5O,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,sBAAsB;IAAC,CAAC,EAC1F;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACM,IAAI;MAAE7O,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAC,EACtF;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACO,GAAG;MAAE9O,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,mBAAmB;IAAC,CAAC,EACpF;MAAC0R,KAAK,EAAErS,SAAS,CAACkV,MAAM,CAACQ,aAAa;MAAE/O,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,6BAA6B;IAAC,CAAC,CAC3G;IACD,IAAI,CAAC6P,cAAc,GAAG,CAClB;MAAC6B,KAAK,EAAErS,SAAS,CAACkR,cAAc,CAACyE,MAAM;MAAEhP,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAC,EACrG;MAAC0R,KAAK,EAAErS,SAAS,CAACkR,cAAc,CAACH,MAAM;MAAEpK,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAC,EACrG;MAAC0R,KAAK,EAAErS,SAAS,CAACkR,cAAc,CAACP,IAAI;MAAEhK,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,uBAAuB;IAAC,CAAC,EACjG;MAAC0R,KAAK,EAAErS,SAAS,CAACkR,cAAc,CAACJ,WAAW;MAAEnK,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,6BAA6B;IAAC,CAAC,EAC9G;MAAC0R,KAAK,EAAErS,SAAS,CAACkR,cAAc,CAACF,WAAW;MAAErK,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,6BAA6B;IAAC,CAAC,EAC9G;MAAC0R,KAAK,EAAErS,SAAS,CAACkR,cAAc,CAACN,SAAS;MAAEjK,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,4BAA4B;IAAC,CAAC,CAC9G;IACD,IAAI,CAACkQ,SAAS,GAAG,CACb;MAACwB,KAAK,EAAErS,SAAS,CAAC4V,SAAS,CAACC,KAAK;MAAElP,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,uBAAuB;IAAC,CAAC,EAC7F;MAAC0R,KAAK,EAAErS,SAAS,CAAC4V,SAAS,CAACjF,IAAI;MAAEhK,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,sBAAsB;IAAC,CAAC,EAC3F;MAAC0R,KAAK,EAAErS,SAAS,CAAC4V,SAAS,CAACE,QAAQ;MAAEnP,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,0BAA0B;IAAC,CAAC,CACtG;IAED;IACA,IAAI,CAAC+O,YAAY,GAAE,CAAC;MAChB+C,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,OAAO;MACZjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDkS,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE,8BAA8B;MACzCC,SAAS,EAAEA,CAACC,EAAE,EAAEC,IAAI,KAAG;QACnBb,EAAE,CAAC9B,iBAAiB,GAAG,KAAK;QAC5B8B,EAAE,CAAC7B,qBAAqB,GAAG,KAAK;QAChC6B,EAAE,CAACzM,aAAa,GAAG;UAAC,GAAGsN;QAAI,CAAC;QAC5B,IAAGb,EAAE,CAACzM,aAAa,CAAC2F,QAAQ,IAAI5H,SAAS,IAAI0O,EAAE,CAACzM,aAAa,CAAC2F,QAAQ,IAAI,IAAI,EAAC;UAC3E8G,EAAE,CAACzM,aAAa,CAAC2F,QAAQ,GAAG,KAAK;;QAErCyK,UAAU,CAAC;UACP3D,EAAE,CAACzM,aAAa,CAAC2I,SAAS,GAAG2E,IAAI,CAAC3E,SAAS,IAAI,EAAE;UACjD8D,EAAE,CAAChB,gBAAgB,CAAC4C,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAACxC,MAAM;UACrD8C,EAAE,CAACf,iBAAiB,CAAC4C,KAAK,EAAE;QAChC,CAAC,CAAC;QACF7B,EAAE,CAAC/B,aAAa,GAAG+B,EAAE,CAACT,WAAW,CAACuB,KAAK,CAACd,EAAE,CAACzM,aAAa,CAAC;QACzDqQ,MAAM,CAACC,IAAI,CAAC7D,EAAE,CAACzM,aAAa,CAAC,CAACgO,OAAO,CAACf,GAAG,IAAG;UACxCR,EAAE,CAAC/B,aAAa,CAAC8C,GAAG,CAACP,GAAG,CAAC,CAACQ,OAAO,EAAE;QACvC,CAAC,CAAC;QACFhB,EAAE,CAACd,aAAa,GAAGtR,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QAC7C8C,EAAE,CAACnI,qBAAqB,GAAG,IAAI;MACnC;KACH,EAAC;MACEwI,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,QAAQ;MACbjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DkS,IAAI,EAAE,OAAO;MACbqD,eAAeA,CAAC7D,KAAK;QACjB,IAAGA,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACyE,MAAM,EAAC;UACxC,OAAOvD,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;SAC7D,MAAK,IAAG0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACH,MAAM,EAAC;UAC9C,OAAOqB,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;SAC7D,MAAK,IAAG0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACP,IAAI,EAAC;UAC5C,OAAOyB,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;SAC3D,MAAK,IAAG0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACJ,WAAW,EAAC;UACnD,OAAOsB,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;SACjE,MAAK,IAAG0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACF,WAAW,EAAC;UACnD,OAAOoB,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;SACjE,MAAK,IAAG0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACN,SAAS,EAAC;UACjD,OAAOwB,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;SAChE,MAAM,IAAI0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACiF,kBAAkB,EAAE;UAC7D,OAAO/D,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;SACvE,MAAM,IAAI0R,KAAK,IAAIrS,SAAS,CAACkR,cAAc,CAACkF,gBAAgB,EAAE;UAC3D,OAAOhE,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;;QAEtE,OAAO,EAAE;MACb;KACH,EAAC;MACE8R,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,eAAe;MACpBjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DkS,IAAI,EAAE;KACT,CAAC;IAEF,IAAI,CAACjD,oBAAoB,GAAG;MACxBsE,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,KAAK;MACnBC,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAE,KAAK;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CAAC;QACLC,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,EAAE,IAAI,CAAChU,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDgU,IAAI,EAAEA,CAAC3B,EAAE,EAAEC,IAAI,KAAI;UACfb,EAAE,CAAC9B,iBAAiB,GAAG,KAAK;UAC5B8B,EAAE,CAAC7B,qBAAqB,GAAG,KAAK;UAChC6B,EAAE,CAACzM,aAAa,GAAG;YAAC,GAAGsN;UAAI,CAAC;UAC5B,IAAGb,EAAE,CAACzM,aAAa,CAAC2F,QAAQ,IAAI5H,SAAS,IAAI0O,EAAE,CAACzM,aAAa,CAAC2F,QAAQ,IAAI,IAAI,EAAC;YAC3E8G,EAAE,CAACzM,aAAa,CAAC2F,QAAQ,GAAG,KAAK;;UAErCyK,UAAU,CAAC;YACP3D,EAAE,CAACzM,aAAa,CAAC2I,SAAS,GAAG2E,IAAI,CAAC3E,SAAS,IAAI,EAAE;YACjD8D,EAAE,CAAChB,gBAAgB,CAAC4C,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAAC8C,MAAM;YACrDxC,EAAE,CAACf,iBAAiB,CAAC4C,KAAK,EAAE;UAChC,CAAC,CAAC;UACF7B,EAAE,CAAC/B,aAAa,GAAG+B,EAAE,CAACT,WAAW,CAACuB,KAAK,CAACd,EAAE,CAACzM,aAAa,CAAC;UACzDyM,EAAE,CAACd,aAAa,GAAGtR,SAAS,CAAC8R,SAAS,CAAC8C,MAAM;UAC7CxC,EAAE,CAAChB,gBAAgB,CAAC4C,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAAC8C,MAAM;UACrDxC,EAAE,CAACf,iBAAiB,CAAC4C,KAAK,EAAE;UAC5B7B,EAAE,CAACnI,qBAAqB,GAAG,IAAI;QACnC,CAAC;QACD4K,UAAUA,CAAC7B,EAAE,EAAEC,IAAI;UACf,OAAOb,EAAE,CAAChD,QAAQ,IAAIpP,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QACpD;OACH,EAAC;QACEmF,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE,IAAI,CAAChU,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3DgU,IAAI,EAAEA,CAAC3B,EAAE,EAAEC,IAAI,KAAI;UACf,KAAI,IAAI6B,CAAC,GAAG,CAAC,EAACA,CAAC,GAAG1C,EAAE,CAACzC,UAAU,CAACoF,OAAO,CAACpR,MAAM,EAACmR,CAAC,EAAE,EAAC;YAC/C,IAAG1C,EAAE,CAACzC,UAAU,CAACoF,OAAO,CAACD,CAAC,CAAC,CAAC9B,EAAE,IAAIA,EAAE,EAAC;cACjCZ,EAAE,CAACzC,UAAU,CAACoF,OAAO,CAACC,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;cAClC1C,EAAE,CAAC1L,WAAW,CAAC2P,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACnE,EAAE,CAACzC,UAAU,CAACoF,OAAO,CAAC;cACnE;;;QAGZ,CAAC;QACDF,UAAUA,CAAC7B,EAAE,EAAEC,IAAI;UACf,OAAOb,EAAE,CAAChD,QAAQ,IAAIpP,SAAS,CAAC8R,SAAS,CAACxC,MAAM;QACpD;OACH;KACJ;IAED,IAAI,CAAC9L,SAAS,GAAG;MACbwP,EAAE,EAAE,IAAI;MACR3J,KAAK,EAAE,IAAI;MACXkK,aAAa,EAAE,IAAI;MACnBG,iBAAiB,EAAE,IAAI;MACvB8C,cAAc,EAAE,IAAI;MACpBvN,MAAM,EAAE,IAAI;MACZT,SAAS,EAAE,IAAI;MACf/E,OAAO,EAAE;KACZ;IACD,IAAI,CAACF,SAAS,GAAG,IAAI,CAACoO,WAAW,CAACuB,KAAK,CAAC,IAAI,CAAC1P,SAAS,CAAC;IACvD,IAAI,CAACmC,aAAa,GAAG;MACjBqN,EAAE,EAAE,IAAI;MACRnI,aAAa,EAAE,IAAI;MACnBT,KAAK,EAAE,IAAI;MACXqB,MAAM,EAAE,IAAI;MACZ6C,SAAS,EAAE,IAAI;MACfvC,QAAQ,EAAE,IAAI;MACd8B,cAAc,EAAE,IAAI;MACpBZ,KAAK,EAAE,IAAI;MACXd,cAAc,EAAE,KAAK;MACrBK,aAAa,EAAE,KAAK;MACpBG,SAAS,EAAE,IAAI;MACfY,MAAM,EAAE,IAAI;MACZkJ,SAAS,EAAE,IAAI;MACfnL,QAAQ,EAAE,KAAK;MACf1F,UAAU,EAAE,IAAI;MAChB8Q,OAAO,EAAE;KACZ;IACD,IAAI,CAACrG,aAAa,GAAG,IAAI,CAACsB,WAAW,CAACuB,KAAK,CAAC,IAAI,CAACvN,aAAa,CAAC;IAE/D;IACA,IAAI,CAACsK,gBAAgB,GAAG,CACpB;MACIwC,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,SAAS;MACdjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DkS,IAAI,EAAE,OAAO;MACb8D,IAAI,EAAE3W,SAAS,CAACkR,cAAc,CAACH,MAAM;MACrC6F,QAAQ,EAAE;QACNtL,QAAQ,EAAE,IAAI;QACdwD,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,yCAAyC;QAClD8H,mBAAmB,EAAEzE,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;QAC/EmW,MAAM,EAAE;;KAEf,EACD;MACIrE,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,KAAK;MACVjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DkS,IAAI,EAAE,OAAO;MACb8D,IAAI,EAAE3W,SAAS,CAACkR,cAAc,CAACH,MAAM;MACrC6F,QAAQ,EAAE;QACNtL,QAAQ,EAAE,IAAI;QACdwD,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,mBAAmB;QAC5B8H,mBAAmB,EAAE,IAAI,CAACnW,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC5EmW,MAAM,EAAE;;KAEf,CACJ;IACD,IAAI,CAAC5G,gBAAgB,GAAG;MACpB8D,IAAI,EAAEhU,SAAS,CAAC8R,SAAS,CAACC;KAC7B;IACD,IAAI,CAACnO,iBAAiB,GAAG,IAAI1D,iBAAiB,EAAE;IAEhD;IACA,IAAI,CAACiR,gBAAgB,GAAG,CACpB;MACIsB,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,SAAS;MACdjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DkS,IAAI,EAAE,OAAO;MACb8D,IAAI,EAAE3W,SAAS,CAACkR,cAAc,CAACH,MAAM;MACrC6F,QAAQ,EAAE;QACNtL,QAAQ,EAAE,IAAI;QACdwD,SAAS,EAAE,GAAG;QACdgI,MAAM,EAAE,IAAI;QACZ/H,OAAO,EAAE,6CAA6C;QACtD8H,mBAAmB,EAAEzE,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,gCAAgC;;KAErF,EACD;MACI8R,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,OAAO;MACZjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDkS,IAAI,EAAE,OAAO;MACb8D,IAAI,EAAE,IAAI,CAAChR,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACF,WAAW,GAAGhR,SAAS,CAACkR,cAAc,CAACH,MAAM,GAAG/Q,SAAS,CAACkR,cAAc,CAACyE,MAAM;MAC3IiB,QAAQ,EAAE;QACNtL,QAAQ,EAAE,IAAI;QACdwD,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,mBAAmB;QAC5B8H,mBAAmB,EAAEzE,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC1EmW,MAAM,EAAE;;KAEf,CACJ;IACD,IAAI,CAAC1F,gBAAgB,GAAG;MACpB4C,IAAI,EAAEhU,SAAS,CAAC8R,SAAS,CAACC;KAC7B;IACD,IAAI,CAACV,iBAAiB,GAAG,IAAInR,iBAAiB,EAAE;IAEhD,IAAI,CAAC+Q,aAAa,GAAGhR,aAAa,CAAC8W,GAAG,CAACnD,EAAE,IAAG;MACxC,OAAO;QACHvB,KAAK,EAAEuB,EAAE,CAACjN,IAAI;QACdoN,OAAO,EAAE,IAAI,CAACrT,WAAW,CAACC,SAAS,CAACiT,EAAE,CAACG,OAAO;OACjD;IACL,CAAC,CAAC;IAEF,IAAI,CAACiD,OAAO,CAACC,MAAM,GAAG,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAChD;EAEAD,MAAMA,CAAA;IACF,IAAI9E,EAAE,GAAG,IAAI;IACb,IAAI,CAAC3P,eAAe,GAAGiB,SAAS;IAChC,IAAI,CAACf,mBAAmB,GAAG,KAAK;IAChCoT,UAAU,CAAC;MACP,IAAG3D,EAAE,CAAC1L,WAAW,CAAC2P,YAAY,EAAC;QAC3BjE,EAAE,CAAC1L,WAAW,CAAC0Q,OAAO,GAAGd,IAAI,CAACe,KAAK,CAACjF,EAAE,CAAC1L,WAAW,CAAC2P,YAAY,CAAC;QAChE,IAAIiB,OAAO,GAAG,CAAC;QACflF,EAAE,CAAC1L,WAAW,CAAC0Q,OAAO,CAACzD,OAAO,CAACC,EAAE,IAAG;UAChCA,EAAE,CAACZ,EAAE,GAAGsE,OAAO,EAAG;UAClB,IAAG1D,EAAE,CAAC7H,QAAQ,IAAIrI,SAAS,EAAC;YACxBkQ,EAAE,CAAC7H,QAAQ,GAAG,IAAI;;UAEtB,IAAG,CAAC6H,EAAE,CAACzH,cAAc,EAAC;YAClByH,EAAE,CAACzH,cAAc,GAAG,KAAK;;UAE7B,IAAG,CAACyH,EAAE,CAACpH,aAAa,EAAC;YACjBoH,EAAE,CAACpH,aAAa,GAAG,KAAK;;UAE5B,IAAGoH,EAAE,CAAC6C,SAAS,IAAI/S,SAAS,IAAIkQ,EAAE,CAAC6C,SAAS,IAAI,IAAI,EAAC;YACjD7C,EAAE,CAACjH,SAAS,GAAGiH,EAAE,CAAC6C,SAAS,CAAC9J,SAAS;YACrCiH,EAAE,CAAC3G,KAAK,GAAG2G,EAAE,CAAC6C,SAAS,CAACxJ,KAAK;YAC7B2G,EAAE,CAACrG,MAAM,GAAGqG,EAAE,CAAC6C,SAAS,CAAClJ,MAAM;YAC/BqG,EAAE,CAAC/F,cAAc,GAAG+F,EAAE,CAAC6C,SAAS,CAAC5I,cAAc;YAC/C+F,EAAE,CAAChO,UAAU,GAAGgO,EAAE,CAAC6C,SAAS,CAAC7Q,UAAU;WAC1C,MAAI;YACDgO,EAAE,CAACjH,SAAS,GAAG,IAAI;YACnBiH,EAAE,CAAC3G,KAAK,GAAG,IAAI;YACf2G,EAAE,CAACrG,MAAM,GAAG,IAAI;YAChBqG,EAAE,CAAC/F,cAAc,GAAG,IAAI;YACxB+F,EAAE,CAAChO,UAAU,GAAG,IAAI;;QAE5B,CAAC,CAAC;OACL,MAAI;QACDwM,EAAE,CAAC1L,WAAW,CAAC0Q,OAAO,GAAG,EAAE;;MAE/BhF,EAAE,CAACP,aAAa,GAAGO,EAAE,CAAC1L,WAAW,GAAG0L,EAAE,CAAC1L,WAAW,CAACC,IAAI,GAAG,IAAI;MAC9DyL,EAAE,CAAC3P,eAAe,GAAG2P,EAAE,CAACT,WAAW,CAACuB,KAAK,CAACd,EAAE,CAAC1L,WAAW,CAAC;MACzD,IAAG0L,EAAE,CAAChD,QAAQ,IAAIpP,SAAS,CAAC8R,SAAS,CAACxC,MAAM,EAAC;QACzC8C,EAAE,CAAC3P,eAAe,CAAC0Q,GAAG,CAAC,MAAM,CAAC,CAACC,OAAO,EAAE;QACxChB,EAAE,CAAC3P,eAAe,CAAC0Q,GAAG,CAAC,QAAQ,CAAC,CAACC,OAAO,EAAE;QAC1ChB,EAAE,CAAC3P,eAAe,CAAC0Q,GAAG,CAAC,eAAe,CAAC,CAACC,OAAO,EAAE;QACjDhB,EAAE,CAAC3P,eAAe,CAAC0Q,GAAG,CAAC,aAAa,CAAC,CAACC,OAAO,EAAE;;MAEnDhB,EAAE,CAAC5C,UAAU,GAAG;QACZuF,OAAO,EAAE3C,EAAE,CAAC1L,WAAW,CAACuO,cAAc;QACtCsC,KAAK,EAAEnF,EAAE,CAAC1L,WAAW,CAACuO,cAAc,CAACtR;OACxC;MACDyO,EAAE,CAACzC,UAAU,GAAG;QACZoF,OAAO,EAAE3C,EAAE,CAAC1L,WAAW,CAAC0Q,OAAO;QAC/BG,KAAK,EAAEnF,EAAE,CAAC1L,WAAW,CAAC0Q,OAAO,CAACzT;OACjC;IACL,CAAC,CAAC;EACN;EAEA6T,qBAAqBA,CAAA,GAErB;EAEA3Q,oBAAoBA,CAAA;IAChB,IAAG,IAAI,CAACH,WAAW,CAACC,IAAI,IAAI,IAAI,CAACkL,aAAa,IAAI,IAAI,CAACnL,WAAW,CAACC,IAAI,IAAI,IAAI,EAAE;IACjF,IAAIyL,EAAE,GAAG,IAAI;IACb,IAAI,CAACqF,eAAe,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC9F,aAAa,CAAC+F,2BAA2B,CAACR,IAAI,CAAC,IAAI,CAACvF,aAAa,CAAC,EAAE,IAAI,CAAClL,WAAW,CAACC,IAAI,EAAGiR,OAAO,IAAG;MACrJ,IAAGA,OAAO,IAAI,CAAC,EAAC;QACZxF,EAAE,CAACzP,mBAAmB,GAAG,KAAK;OACjC,MAAI;QACDyP,EAAE,CAACzP,mBAAmB,GAAG,IAAI;;IAErC,CAAC,CAAC;EACN;EAEAkV,oBAAoBA,CAACC,KAAK;IACtB,IAAGA,KAAK,CAACC,OAAO,CAACpU,MAAM,GAAG,CAAC,EAAC;MACxB,IAAI,CAAC+C,WAAW,CAACc,aAAa,GAAGxH,SAAS,CAACkS,cAAc,CAAChD,MAAM;KACnE,MAAI;MACD,IAAI,CAACxI,WAAW,CAACc,aAAa,GAAGxH,SAAS,CAACkS,cAAc,CAAC/C,OAAO;;EAEzE;EAEAiB,kBAAkBA,CAAA;IACd,IAAG,IAAI,CAACkB,aAAa,IAAItR,SAAS,CAAC8R,SAAS,CAACC,MAAM,EAAC;MAChD,OAAO,IAAI,CAACrR,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACnE,MAAK,IAAG,IAAI,CAAC2Q,aAAa,IAAItR,SAAS,CAAC8R,SAAS,CAAC8C,MAAM,EAAC;MACtD,OAAO,IAAI,CAAClU,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACnE,MAAI;MACD,OAAO,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;;EAExE;EAEAoP,cAAcA,CAAA;IACV,IAAG,IAAI,CAACI,SAAS,IAAInQ,SAAS,CAAC8R,SAAS,CAACC,MAAM,EAAC;MAC5C,OAAO,IAAI,CAACrR,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAK,IAAG,IAAI,CAACwP,SAAS,IAAInQ,SAAS,CAAC8R,SAAS,CAAC8C,MAAM,EAAC;MAClD,OAAO,IAAI,CAAClU,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAI;MACD,OAAO,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;;EAEpE;EAEAiB,eAAeA,CAAA;IACX,IAAI,CAAC4B,SAAS,GAAG;MACbwP,EAAE,EAAE,IAAI;MACRO,aAAa,EAAE,IAAI;MACnBG,iBAAiB,EAAE,IAAI;MACvBrK,KAAK,EAAE,IAAI;MACXmN,cAAc,EAAE,IAAI,CAAC9P,WAAW,CAACsM,EAAE;MACnC/J,MAAM,EAAE,IAAI;MACZT,SAAS,EAAE,IAAI;MACf/E,OAAO,EAAE;KACZ;IACD,IAAI,CAACF,SAAS,GAAG,IAAI,CAACoO,WAAW,CAACuB,KAAK,CAAC,IAAI,CAAC1P,SAAS,CAAC;IACvD,IAAI,CAACM,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACqM,SAAS,GAAGnQ,SAAS,CAAC8R,SAAS,CAACC,MAAM;IAC3C,IAAI,CAAC7B,gBAAgB,CAAC8D,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAACC,MAAM;IACvD,IAAI,CAAC1J,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACzE,iBAAiB,CAACqQ,KAAK,EAAE;EAClC;EAEAhS,mBAAmBA,CAAA;IACf,IAAI,CAAC0D,aAAa,GAAG;MACjBkF,aAAa,EAAE,IAAI;MACnBT,KAAK,EAAE,IAAI;MACXqB,MAAM,EAAE,IAAI;MACZuH,EAAE,EAAE,IAAI;MACR1E,SAAS,EAAE,EAAE;MACbhD,QAAQ,EAAE,KAAK;MACfS,QAAQ,EAAE,IAAI;MACdI,cAAc,EAAE,KAAK;MACrBK,aAAa,EAAE,KAAK;MACpBiK,SAAS,EAAE,IAAI;MACf5I,cAAc,EAAE,IAAI;MACpBZ,KAAK,EAAE,IAAI;MACXN,SAAS,EAAE,IAAI;MACfY,MAAM,EAAE,IAAI;MACZ3H,UAAU,EAAE,IAAI;MAChB8Q,OAAO,EAAE;KACZ;IACD,IAAI,CAACrG,aAAa,GAAG,IAAI,CAACsB,WAAW,CAACuB,KAAK,CAAC,IAAI,CAACvN,aAAa,CAAC;IAC/D,IAAI,CAAC4K,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACD,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACc,gBAAgB,CAAC4C,IAAI,GAAGhU,SAAS,CAAC8R,SAAS,CAACC,MAAM;IACvD,IAAI,CAACT,aAAa,GAAGtR,SAAS,CAAC8R,SAAS,CAACC,MAAM;IAC/C,IAAI,CAAC9H,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACoH,iBAAiB,CAAC4C,KAAK,EAAE;EAGlC;EAEA3R,QAAQA,CAAA;IACJ,IAAI8P,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAAC3P,eAAe,CAACC,OAAO,IAAI,IAAI,CAACC,mBAAmB,EAAE;IAC7D,IAAIqV,IAAI,GAAG;MACP,GAAG,IAAI,CAACtR;KACX;IACDsR,IAAI,CAAC/C,cAAc,CAACtB,OAAO,CAACC,EAAE,IAAG;MAC7B,IAAGA,EAAE,CAACZ,EAAE,GAAG,CAAC,EAAC;QACTY,EAAE,CAACZ,EAAE,GAAG,IAAI;;IAEpB,CAAC,CAAC;IACF,IAAIiF,UAAU,GAAGD,IAAI,CAACZ,OAAO,IAAI,EAAE;IACnCa,UAAU,CAACtE,OAAO,CAACC,EAAE,IAAG;MACpB,OAAOA,EAAE,CAACZ,EAAE;MACZ,IAAGY,EAAE,CAACnI,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACJ,WAAW,IAAI8C,EAAE,CAACnI,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACF,WAAW,EAAC;QACtG4C,EAAE,CAACtF,SAAS,CAACqF,OAAO,CAACuE,GAAG,IAAG;UACvB,OAAOA,GAAG,CAAClF,EAAE;UACb,IAAGkF,GAAG,CAAC,MAAM,CAAC,EAAC;YACX,OAAOA,GAAG,CAAC,MAAM,CAAC;;QAE1B,CAAC,CAAC;;IAEV,CAAC,CAAC;IACFF,IAAI,CAAC3B,YAAY,GAAGC,IAAI,CAACC,SAAS,CAAC0B,UAAU,CAAC;IAC9C,OAAOD,IAAI,CAACZ,OAAO;IACnB,IAAGY,IAAI,CAACxQ,aAAa,IAAI,IAAI,EAAC;MAC1BwQ,IAAI,CAACxQ,aAAa,GAAGxH,SAAS,CAACkS,cAAc,CAAC/C,OAAO;;IAEzDiD,EAAE,CAAC+F,oBAAoB,CAACjB,MAAM,EAAE;IAChC,IAAG9E,EAAE,CAAC1L,WAAW,CAACsM,EAAE,IAAI,IAAI,EAAC;MACzB,IAAI,CAACpB,aAAa,CAACwG,0BAA0B,CAACJ,IAAI,EAAGK,QAAQ,IAAG;QAC5DjG,EAAE,CAAC+F,oBAAoB,CAACG,OAAO,CAAClG,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvFyR,EAAE,CAAC1L,WAAW,GAAG2R,QAAQ;QACzBjG,EAAE,CAACmG,WAAW,CAACnG,EAAE,CAAC1L,WAAW,CAACsM,EAAE,CAAC;MACrC,CAAC,EAAE,IAAI,EAAE,MAAI;QACTZ,EAAE,CAAC+F,oBAAoB,CAACK,OAAO,EAAE;MACrC,CAAC,CAAC;KACL,MAAI;MACD,IAAI,CAAC5G,aAAa,CAAC6G,0BAA0B,CAACrG,EAAE,CAAC1L,WAAW,CAACsM,EAAE,EAAEgF,IAAI,EAAGK,QAAQ,IAAG;QAC/EjG,EAAE,CAAC+F,oBAAoB,CAACG,OAAO,CAAClG,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvFyR,EAAE,CAAC1L,WAAW,GAAG2R,QAAQ;QACzBjG,EAAE,CAAC8E,MAAM,EAAE;MACf,CAAC,EAAE,IAAI,EAAE,MAAI;QACT9E,EAAE,CAAC+F,oBAAoB,CAACK,OAAO,EAAE;MACrC,CAAC,CAAC;;EAEV;EAEA9P,mBAAmBA,CAAA;IACf,KAAI,IAAIoM,CAAC,GAAG,CAAC,EAACA,CAAC,GAAE,IAAI,CAACpO,WAAW,CAACuO,cAAc,CAACtR,MAAM,EAACmR,CAAC,EAAE,EAAC;MACxD,IAAG,IAAI,CAACpO,WAAW,CAACuO,cAAc,CAACH,CAAC,CAAC,CAACtM,SAAS,IAAI,IAAI,CAAChF,SAAS,CAACgF,SAAS,IAAI,IAAI,CAAC9B,WAAW,CAACuO,cAAc,CAACH,CAAC,CAAC,CAAC9B,EAAE,IAAI,IAAI,CAACxP,SAAS,CAACwP,EAAE,EAAC;QACtI,IAAI,CAAClP,kBAAkB,GAAG,IAAI;QAC9B;;;IAGR,IAAI,CAACA,kBAAkB,GAAG,KAAK;EACnC;EAEAwG,kBAAkBA,CAAA;IACd,KAAI,IAAIwK,CAAC,GAAG,CAAC,EAACA,CAAC,GAAE,IAAI,CAACpO,WAAW,CAAC0Q,OAAO,CAACzT,MAAM,EAACmR,CAAC,EAAE,EAAC;MACjD,IAAG,IAAI,CAACpO,WAAW,CAAC0Q,OAAO,CAACtC,CAAC,CAAC,CAAC1K,KAAK,IAAI,IAAI,CAACzE,aAAa,CAACyE,KAAK,IAAI,IAAI,CAAC1D,WAAW,CAAC0Q,OAAO,CAACtC,CAAC,CAAC,CAAC9B,EAAE,IAAI,IAAI,CAACrN,aAAa,CAACqN,EAAE,EAAC;QACxH,IAAI,CAAC1C,iBAAiB,GAAG,IAAI;QAC7B;;;IAGR,IAAI,CAACA,iBAAiB,GAAG,KAAK;EAClC;EAEAvF,sBAAsBA,CAAA;IAClB,KAAI,IAAI+J,CAAC,GAAG,CAAC,EAACA,CAAC,GAAE,IAAI,CAACpO,WAAW,CAAC0Q,OAAO,CAACzT,MAAM,EAACmR,CAAC,EAAE,EAAC;MACjD,IAAG,IAAI,CAACpO,WAAW,CAAC0Q,OAAO,CAACtC,CAAC,CAAC,CAACjK,aAAa,IAAI,IAAI,CAAClF,aAAa,CAACkF,aAAa,IAAI,IAAI,CAACnE,WAAW,CAAC0Q,OAAO,CAACtC,CAAC,CAAC,CAAC9B,EAAE,IAAI,IAAI,CAACrN,aAAa,CAACqN,EAAE,EAAC;QACxI,IAAI,CAACzC,qBAAqB,GAAG,IAAI;QACjC;;;IAGR,IAAI,CAACA,qBAAqB,GAAG,KAAK;EACtC;EAEAmI,aAAaA,CAACrG,KAAK;IACf,KAAI,IAAIyC,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAAC9E,OAAO,CAACrM,MAAM,EAACmR,CAAC,EAAE,EAAC;MACpC,IAAGzC,KAAK,IAAI,IAAI,CAACrC,OAAO,CAAC8E,CAAC,CAAC,CAACzC,KAAK,EAAC;QAC9B,OAAO,IAAI,CAACrC,OAAO,CAAC8E,CAAC,CAAC,CAACnO,IAAI;;;IAGnC,OAAO,EAAE;EACb;EAEAgS,gBAAgBA,CAACtG,KAAK;IAClB,KAAI,IAAIyC,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAACtE,cAAc,CAAC7M,MAAM,EAACmR,CAAC,EAAE,EAAC;MAC3C,IAAGzC,KAAK,IAAI,IAAI,CAAC7B,cAAc,CAACsE,CAAC,CAAC,CAACzC,KAAK,EAAC;QACrC,OAAO,IAAI,CAAC7B,cAAc,CAACsE,CAAC,CAAC,CAACnO,IAAI;;;IAG1C,OAAO,EAAE;EACb;EAEAtD,SAASA,CAAA;IACL,IAAG,IAAI,CAACE,SAAS,CAACb,OAAO,IAAI,IAAI,CAACc,SAAS,CAACC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACD,SAAS,CAACC,OAAO,IAAIC,SAAS,IAAI,IAAI,CAACF,SAAS,CAACC,OAAO,CAACE,MAAM,IAAI,CAAC,IAAI,IAAI,CAACC,iBAAiB,CAACC,UAAU,IAAI,IAAI,IAAI,IAAI,CAACC,kBAAkB,EAAE;IAClN,IAAIkU,IAAI,GAAG;MAAC,GAAG,IAAI,CAACxU;IAAS,CAAC;IAC9BwU,IAAI,CAACzE,aAAa,GAAGyE,IAAI,CAACvU,OAAO,CAACsT,GAAG,CAACnD,EAAE,IAAIA,EAAE,CAACG,OAAO,CAAC,CAAC6E,cAAc,EAAE;IACxEZ,IAAI,CAACtE,iBAAiB,GAAGsE,IAAI,CAACvU,OAAO,CAACsT,GAAG,CAACnD,EAAE,IAAIA,EAAE,CAAChB,GAAG,CAAC,CAACgG,cAAc,EAAE;IACxE,IAAI,CAACvQ,iBAAiB,GAAG,KAAK;IAC9B,OAAO2P,IAAI,CAACvU,OAAO;IACnB,IAAGuU,IAAI,CAAChF,EAAE,IAAI,IAAI,EAAC;MACfgF,IAAI,CAAChF,EAAE,GAAG,IAAI,CAAChB,cAAc,EAAG;MAChC,IAAI,CAACtL,WAAW,CAACuO,cAAc,CAACnB,IAAI,CAACkE,IAAI,CAAC;KAC7C,MAAI;MACD,KAAI,IAAIlD,CAAC,GAAG,CAAC,EAACA,CAAC,GAAG,IAAI,CAACpO,WAAW,CAACuO,cAAc,CAACtR,MAAM,EAAEmR,CAAC,EAAE,EAAC;QAC1D,IAAG,IAAI,CAACpO,WAAW,CAACuO,cAAc,CAACH,CAAC,CAAC,CAAC9B,EAAE,IAAIgF,IAAI,CAAChF,EAAE,EAAC;UAChD,IAAI,CAACtM,WAAW,CAACuO,cAAc,CAACH,CAAC,CAAC,GAAGkD,IAAI;UACzC;;;;IAIZ,IAAI,CAACxI,UAAU,CAACuF,OAAO,GAAG,IAAI,CAACrO,WAAW,CAACuO,cAAc;IACzD,IAAI,CAACzF,UAAU,CAAC+H,KAAK,GAAG,IAAI,CAAC7Q,WAAW,CAACuO,cAAc,CAACtR,MAAM;EAClE;EAEAyC,aAAaA,CAAA;IACT,IAAG,IAAI,CAACE,yBAAyB,EAAE,IAAI,IAAI,EAAE;IAC7C,IAAI0R,IAAI,GAAG;MAAC,GAAG,IAAI,CAACrS;IAAa,CAAC;IAClCqS,IAAI,CAACvB,SAAS,GAAG;MACb5I,cAAc,EAAEmK,IAAI,CAACnK,cAAc;MACnCjI,UAAU,EAAEoS,IAAI,CAACpS,UAAU;MAC3BqH,KAAK,EAAE+K,IAAI,CAAC/K,KAAK;MACjBM,MAAM,EAAEyK,IAAI,CAACzK,MAAM;MACnBZ,SAAS,EAAEqL,IAAI,CAACrL;KACnB;IACD,IAAI,CAAC1C,qBAAqB,GAAG,KAAK;IAClC,IAAG+N,IAAI,CAAChF,EAAE,IAAI,IAAI,EAAC;MACfgF,IAAI,CAAChF,EAAE,GAAG,IAAI,CAACf,kBAAkB,EAAG;MACpC,IAAI,CAACvL,WAAW,CAAC0Q,OAAO,CAACtD,IAAI,CAACkE,IAAI,CAAC;KACtC,MAAI;MACD,KAAI,IAAIlD,CAAC,GAAG,CAAC,EAACA,CAAC,GAAG,IAAI,CAACpO,WAAW,CAAC0Q,OAAO,CAACzT,MAAM,EAAEmR,CAAC,EAAE,EAAC;QACnD,IAAG,IAAI,CAACpO,WAAW,CAAC0Q,OAAO,CAACtC,CAAC,CAAC,CAAC9B,EAAE,IAAIgF,IAAI,CAAChF,EAAE,EAAC;UACzC,IAAI,CAACtM,WAAW,CAAC0Q,OAAO,CAACtC,CAAC,CAAC,GAAGkD,IAAI;UAClC;;;;IAIZ,IAAI,CAACtR,WAAW,CAAC2P,YAAY,GAAGC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7P,WAAW,CAAC0Q,OAAO,CAAC;EAC5E;EAEA9Q,yBAAyBA,CAAA;IACrB,IAAG,IAAI,CAAC+J,aAAa,CAAC3N,OAAO,EAAE,OAAO,IAAI;IAC1C,IAAG,IAAI,CAAC6N,qBAAqB,IAAI,IAAI,CAACD,iBAAiB,EAAE,OAAO,IAAI;IACpE,IAAG,IAAI,CAAC3K,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACJ,WAAW,IAAI,IAAI,CAACnL,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACF,WAAW,EAAC;MACtI,IAAG,IAAI,CAACrL,aAAa,CAACwG,cAAc,IAAI,KAAK,EAAC;QAC1C,IAAG,IAAI,CAACxG,aAAa,CAAC2I,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC3I,aAAa,CAAC2I,SAAS,IAAI5K,SAAS,IAAI,IAAI,CAACiC,aAAa,CAAC2I,SAAS,CAAC3K,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;;;IAGrJ,OAAO,KAAK;EAChB;EAEAgI,eAAeA,CAAA;IACX,IAAIyG,EAAE,GAAG,IAAI;IACb,IAAI,CAACjB,gBAAgB,GAAG,CACpB;MACIsB,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,SAAS;MACdjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DkS,IAAI,EAAE,OAAO;MACb8D,IAAI,EAAE3W,SAAS,CAACkR,cAAc,CAACH,MAAM;MACrC6F,QAAQ,EAAE;QACNtL,QAAQ,EAAE,IAAI;QACdwD,SAAS,EAAE,GAAG;QACdgI,MAAM,EAAE,IAAI;QACZ/H,OAAO,EAAE,6CAA6C;QACtD8H,mBAAmB,EAAEzE,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,gCAAgC;;KAErF,EACD;MACI8R,KAAK,EAAE,MAAM;MACbG,GAAG,EAAE,OAAO;MACZjM,IAAI,EAAE,IAAI,CAACjG,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDkS,IAAI,EAAE,OAAO;MACb8D,IAAI,EAAE,IAAI,CAAChR,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACF,WAAW,GAAGhR,SAAS,CAACkR,cAAc,CAACH,MAAM,GAAG/Q,SAAS,CAACkR,cAAc,CAACyE,MAAM;MAC3IiB,QAAQ,EAAE;QACNtL,QAAQ,EAAE,IAAI;QACdwD,SAAS,EAAE,GAAG;QACd;QACA;QACAgI,MAAM,EAAE;;KAEf,CACJ;IACD,IAAG,IAAI,CAACnR,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACJ,WAAW,IAAI,IAAI,CAACnL,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACF,WAAW,EAAC;MACtI,IAAI,CAACrL,aAAa,CAAC6G,aAAa,GAAG,KAAK;;IAE5C,IAAG,IAAI,CAAC7G,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACH,MAAM,IAAI,IAAI,CAACpL,aAAa,CAAC8F,MAAM,IAAIzL,SAAS,CAACkR,cAAc,CAACF,WAAW,EAAC;MACjI,IAAI,CAACrL,aAAa,CAACwG,cAAc,GAAG,KAAK;;IAE7C,IAAI,CAACxG,aAAa,CAAC2I,SAAS,GAAG,EAAE;IACjC,IAAG,IAAI,CAAC+C,iBAAiB,CAAC4C,KAAK,EAAC;MAC5B,IAAI,CAAC5C,iBAAiB,CAAC4C,KAAK,EAAE;;EAEtC;EAEA5H,oBAAoBA,CAAA,GAEpB;EAEA7C,QAAQA,CAACsO,KAAK;IACV,IAAI1F,EAAE,GAAG,IAAI;IACb,IAAIC,KAAK,GAAGwG,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACpD,IAAGzG,KAAK,EAAC;MACL,IAAI0G,IAAI,GAAG1G,KAAK,CAAC,OAAO,CAAC;MACzBD,EAAE,CAAC4G,WAAW,CAACC,eAAe,CAACF,IAAI,EAAC,MAAK;QACrC3G,EAAE,CAAC+F,oBAAoB,CAACG,OAAO,CAAClG,EAAE,CAAC1R,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,CAAC;MACtF,CAAC,CAAC;KACL,MAAI;MACD,IAAI,CAACwX,oBAAoB,CAACe,OAAO,CAAC,IAAI,CAACxY,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC;;EAE7F;;;uBAvvBS6Q,uBAAuB,EAAArR,EAAA,CAAAgZ,iBAAA,CAAAhZ,EAAA,CAAAiZ,QAAA,GAAAjZ,EAAA,CAAAgZ,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAAnZ,EAAA,CAAAgZ,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAvBhI,uBAAuB;MAAAiI,SAAA;MAAAC,MAAA;QAAAhT,WAAA;QAAAsQ,OAAA;QAAA5H,QAAA;QAAAnH,MAAA;QAAAsQ,WAAA;MAAA;MAAAoB,QAAA,GAAAxZ,EAAA,CAAAyZ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjEpC/Z,EAAA,CAAA2G,UAAA,IAAAsT,sCAAA,qBAgfM;;;UAhfAja,EAAA,CAAAoC,UAAA,SAAA4X,GAAA,CAAA1X,eAAA,CAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}