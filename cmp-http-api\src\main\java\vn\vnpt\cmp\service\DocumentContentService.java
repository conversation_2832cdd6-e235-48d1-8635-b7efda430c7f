package vn.vnpt.cmp.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.vnpt.cmp.base.constants.Constants;
import vn.vnpt.cmp.base.constants.ResponseCode;
import vn.vnpt.cmp.base.event.Event;
import vn.vnpt.cmp.base.event.constants.AMQPConstants;
import vn.vnpt.cmp.base.jpa.dto.docs.DocumentContent;
import vn.vnpt.cmp.base.jpa.dto.docs.Project;
import vn.vnpt.cmp.base.utils.ObjectMapperUtil;
import vn.vnpt.cmp.base.utils.TransactionLogger;
import vn.vnpt.cmp.util.RequestUtils;

import java.util.List;
import java.util.Map;

@Service
public class DocumentContentService {
    private Logger logger = LoggerFactory.getLogger(PermissionService.class);
    private String routingKey;
    private String category;

    private static final String objectKey = "DocumentGuide";

    public DocumentContentService(){
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.DOCS;
    }

    public Project getProjectInfo(Map<String, Object> params){
        Project response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_PROJECT_INFO, category, params, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (Project) event.payload;
            } else {
            }
            return null;
        } finally {
            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public List<DocumentContent> getPageForProject(Map<String, Object> params){
        List<DocumentContent> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_LIST_PAGE, category, params, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (List<DocumentContent>) event.payload;
            } else {
            }
            return null;
        } finally {
            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public DocumentContent getPageInfo(Map<String, Object> params){
        DocumentContent response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_PAGE_INFO, category, params, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (DocumentContent) event.payload;
            } else {
            }
            return null;
        } finally {
            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }
}
