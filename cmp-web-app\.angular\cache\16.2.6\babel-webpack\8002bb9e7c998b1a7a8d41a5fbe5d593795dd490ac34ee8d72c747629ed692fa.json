{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SidebarElementDirective {\n  constructor(el, renderer, router) {\n    this.el = el;\n    this.renderer = renderer;\n    this.router = router;\n    this.itemInfo = {};\n    this.displayElement = null;\n  }\n  onClick(event) {\n    event.stopPropagation(); // Prevent the click event from reaching the document click handler\n    if (this.itemInfo.url) {\n      if (this.itemInfo.target == \"_blank\") {\n        window.open(this.itemInfo.url, '_blank');\n      } else {\n        window.location.href = this.itemInfo.url;\n      }\n    } else if (this.itemInfo.routerLink) {\n      this.router.navigate(this.itemInfo.routerLink);\n      this.hideElement();\n    } else if (!this.itemInfo.url && this.itemInfo.items && this.itemInfo.items.length > 0) {\n      this.toggleElement(event);\n    }\n  }\n  onMouseEnter(event) {\n    let node = document.querySelector(\".display-subElement\");\n    if (node) {\n      event.stopPropagation();\n      if (!this.itemInfo.url && this.itemInfo.items && this.itemInfo.items.length > 0) {\n        this.toggleElement(event);\n      } else {\n        this.hideElement();\n      }\n    }\n  }\n  onDocumentClick(event) {\n    if (this.displayElement && !this.displayElement.contains(event.target)) {\n      this.hideElement();\n    }\n  }\n  toggleElement(event) {\n    let node = document.querySelector(\".display-subElement\");\n    if (node) {\n      this.hideElement();\n    }\n    this.showElement(event);\n  }\n  showElement(event) {\n    const rect = this.el.nativeElement.getBoundingClientRect();\n    // console.log(rect)\n    // const top = rect.top + window.scrollY;\n    // const left = rect.left + window.scrollX + rect.width + 30;\n    this.displayElement = document.createElement('div');\n    this.displayElement.className = 'display-subElement';\n    this.displayElement.style.position = 'fixed';\n    this.displayElement.style.top = `${rect.top}px`;\n    this.displayElement.style.left = `${rect.left + 55}px`;\n    // this.displayElement.style.backgroundColor = \"white\";\n    // this.displayElement.style.padding = \"10px\"\n    this.displayElement.style.zIndex = \"10\";\n    // this.renderer.addClass(this.el.nativeElement, 'active-route-small');\n    // this.displayElement\n    // Check if the itemInfo contains sub-items\n    if (this.itemInfo.items && this.itemInfo.items.length > 0) {\n      this.createSubMenu(this.itemInfo.items, this.displayElement);\n    }\n    document.body.appendChild(this.displayElement);\n  }\n  hideElement() {\n    let node = document.querySelector(\".display-subElement\");\n    if (node) {\n      // document.body.removeChild(this.displayElement);\n      node.remove();\n      // this.displayElement = null;\n    }\n  }\n\n  createSubMenu(items, parentElement) {\n    items.forEach(item => {\n      const subItemDiv = document.createElement('div');\n      subItemDiv.className = 'submenu-item';\n      subItemDiv.innerHTML = item.label;\n      // Check if the sub-item has routerLink\n      if (item.routerLink) {\n        const url = item.routerLink.join('/') || '';\n        // console.log(url)\n        // console.log(this.router.url)\n        if (this.router.url == url || this.router.url + \"/\" == url) {\n          this.renderer.addClass(subItemDiv, 'active-route-subitem');\n        }\n        this.renderer.listen(subItemDiv, 'click', () => {\n          this.router.navigate(item.routerLink);\n          this.hideElement();\n        });\n      } else {\n        // this.renderer.listen(subItemDiv, 'click', () => this.createSubMenu(item.items, this.displayElement!));\n      }\n      parentElement.appendChild(subItemDiv);\n    });\n  }\n  static {\n    this.ɵfac = function SidebarElementDirective_Factory(t) {\n      return new (t || SidebarElementDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: SidebarElementDirective,\n      selectors: [[\"\", \"appSmallSidebarElement\", \"\"]],\n      hostBindings: function SidebarElementDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SidebarElementDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"mouseenter\", function SidebarElementDirective_mouseenter_HostBindingHandler($event) {\n            return ctx.onMouseEnter($event);\n          })(\"click\", function SidebarElementDirective_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        itemInfo: \"itemInfo\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["SidebarElementDirective", "constructor", "el", "renderer", "router", "itemInfo", "displayElement", "onClick", "event", "stopPropagation", "url", "target", "window", "open", "location", "href", "routerLink", "navigate", "hideElement", "items", "length", "toggleElement", "onMouseEnter", "node", "document", "querySelector", "onDocumentClick", "contains", "showElement", "rect", "nativeElement", "getBoundingClientRect", "createElement", "className", "style", "position", "top", "left", "zIndex", "createSubMenu", "body", "append<PERSON><PERSON><PERSON>", "remove", "parentElement", "for<PERSON>ach", "item", "subItemDiv", "innerHTML", "label", "join", "addClass", "listen", "i0", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "i1", "Router", "selectors", "hostBindings", "SidebarElementDirective_HostBindings", "rf", "ctx", "$event", "SidebarElementDirective_mouseenter_HostBindingHandler", "SidebarElementDirective_click_HostBindingHandler", "ɵɵresolveDocument"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\layout\\small-side-bar\\sidebar.directive.ts"], "sourcesContent": ["import { Directive, ElementRef, HostListener, Input, Renderer2 } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\n\r\n@Directive({\r\n  selector: '[appSmallSidebarElement]'\r\n})\r\nexport class SidebarElementDirective {\r\n  @Input() itemInfo: any = {};\r\n  private displayElement: HTMLDivElement | null = null;\r\n\r\n  constructor(private el: ElementRef, private renderer: Renderer2, private router: Router) {}\r\n\r\n  @HostListener('click', ['$event']) onClick(event: MouseEvent) {\r\n    event.stopPropagation(); // Prevent the click event from reaching the document click handler\r\n\r\n    if (this.itemInfo.url) {\r\n      if (this.itemInfo.target == \"_blank\") {\r\n        window.open(this.itemInfo.url, '_blank');\r\n      } else {\r\n        window.location.href = this.itemInfo.url;\r\n      }\r\n    } else if (this.itemInfo.routerLink) {\r\n      this.router.navigate(this.itemInfo.routerLink);\r\n      this.hideElement()\r\n    } else if (!this.itemInfo.url && this.itemInfo.items && this.itemInfo.items.length > 0) {\r\n      this.toggleElement(event);\r\n    }\r\n  }\r\n\r\n  @HostListener('mouseenter', ['$event']) onMouseEnter(event: MouseEvent) {\r\n    let node = document.querySelector(\".display-subElement\");\r\n    if (node) {\r\n      event.stopPropagation();\r\n      if (!this.itemInfo.url && this.itemInfo.items && this.itemInfo.items.length > 0) {\r\n        this.toggleElement(event);\r\n      } else {\r\n        this.hideElement();\r\n      }\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event']) onDocumentClick(event: MouseEvent) {\r\n    if (this.displayElement && !this.displayElement.contains(event.target as Node)) {\r\n      this.hideElement();\r\n    }\r\n  }\r\n\r\n  private toggleElement(event: MouseEvent) {\r\n    let node = document.querySelector(\".display-subElement\");\r\n    if (node) {\r\n      this.hideElement();\r\n      \r\n    }\r\n    this.showElement(event);\r\n  }\r\n\r\n  private showElement(event: MouseEvent) {\r\n    const rect = this.el.nativeElement.getBoundingClientRect();\r\n    // console.log(rect)\r\n\r\n    // const top = rect.top + window.scrollY;\r\n    // const left = rect.left + window.scrollX + rect.width + 30;\r\n\r\n    this.displayElement = document.createElement('div');\r\n    this.displayElement.className = 'display-subElement';\r\n    this.displayElement.style.position = 'fixed';\r\n    this.displayElement.style.top = `${rect.top}px`;\r\n    this.displayElement.style.left = `${rect.left + 55}px`;\r\n    // this.displayElement.style.backgroundColor = \"white\";\r\n    // this.displayElement.style.padding = \"10px\"\r\n    this.displayElement.style.zIndex = \"10\"\r\n    // this.renderer.addClass(this.el.nativeElement, 'active-route-small');\r\n\r\n    // this.displayElement\r\n\r\n    // Check if the itemInfo contains sub-items\r\n    if (this.itemInfo.items && this.itemInfo.items.length > 0) {\r\n      this.createSubMenu(this.itemInfo.items, this.displayElement);\r\n    }\r\n\r\n    document.body.appendChild(this.displayElement);\r\n  }\r\n\r\n  private hideElement() {\r\n    let node = document.querySelector(\".display-subElement\");\r\n    if (node) {\r\n      // document.body.removeChild(this.displayElement);\r\n      node.remove()\r\n      // this.displayElement = null;\r\n    }\r\n  }\r\n\r\n  private createSubMenu(items: any[], parentElement: HTMLDivElement) {\r\n    items.forEach((item: any) => {\r\n      const subItemDiv = document.createElement('div');\r\n      subItemDiv.className = 'submenu-item';\r\n      subItemDiv.innerHTML = item.label\r\n\r\n      // Check if the sub-item has routerLink\r\n      if (item.routerLink) {\r\n        const url = item.routerLink.join('/') || '';\r\n        // console.log(url)\r\n        // console.log(this.router.url)\r\n        if ( this.router.url==url|| this.router.url+\"/\"==url ) {\r\n          this.renderer.addClass(subItemDiv, 'active-route-subitem');\r\n        }\r\n        this.renderer.listen(subItemDiv, 'click', () => {\r\n          this.router.navigate(item.routerLink);\r\n          this.hideElement();\r\n        });\r\n      } else {\r\n        // this.renderer.listen(subItemDiv, 'click', () => this.createSubMenu(item.items, this.displayElement!));\r\n      }\r\n\r\n      parentElement.appendChild(subItemDiv);\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;AAMA,OAAM,MAAOA,uBAAuB;EAIlCC,YAAoBC,EAAc,EAAUC,QAAmB,EAAUC,MAAc;IAAnE,KAAAF,EAAE,GAAFA,EAAE;IAAsB,KAAAC,QAAQ,GAARA,QAAQ;IAAqB,KAAAC,MAAM,GAANA,MAAM;IAHtE,KAAAC,QAAQ,GAAQ,EAAE;IACnB,KAAAC,cAAc,GAA0B,IAAI;EAEsC;EAEvDC,OAAOA,CAACC,KAAiB;IAC1DA,KAAK,CAACC,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,IAAI,CAACJ,QAAQ,CAACK,GAAG,EAAE;MACrB,IAAI,IAAI,CAACL,QAAQ,CAACM,MAAM,IAAI,QAAQ,EAAE;QACpCC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACR,QAAQ,CAACK,GAAG,EAAE,QAAQ,CAAC;OACzC,MAAM;QACLE,MAAM,CAACE,QAAQ,CAACC,IAAI,GAAG,IAAI,CAACV,QAAQ,CAACK,GAAG;;KAE3C,MAAM,IAAI,IAAI,CAACL,QAAQ,CAACW,UAAU,EAAE;MACnC,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,IAAI,CAACZ,QAAQ,CAACW,UAAU,CAAC;MAC9C,IAAI,CAACE,WAAW,EAAE;KACnB,MAAM,IAAI,CAAC,IAAI,CAACb,QAAQ,CAACK,GAAG,IAAI,IAAI,CAACL,QAAQ,CAACc,KAAK,IAAI,IAAI,CAACd,QAAQ,CAACc,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACtF,IAAI,CAACC,aAAa,CAACb,KAAK,CAAC;;EAE7B;EAEwCc,YAAYA,CAACd,KAAiB;IACpE,IAAIe,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACxD,IAAIF,IAAI,EAAE;MACRf,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACK,GAAG,IAAI,IAAI,CAACL,QAAQ,CAACc,KAAK,IAAI,IAAI,CAACd,QAAQ,CAACc,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/E,IAAI,CAACC,aAAa,CAACb,KAAK,CAAC;OAC1B,MAAM;QACL,IAAI,CAACU,WAAW,EAAE;;;EAGxB;EAE4CQ,eAAeA,CAAClB,KAAiB;IAC3E,IAAI,IAAI,CAACF,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACqB,QAAQ,CAACnB,KAAK,CAACG,MAAc,CAAC,EAAE;MAC9E,IAAI,CAACO,WAAW,EAAE;;EAEtB;EAEQG,aAAaA,CAACb,KAAiB;IACrC,IAAIe,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACxD,IAAIF,IAAI,EAAE;MACR,IAAI,CAACL,WAAW,EAAE;;IAGpB,IAAI,CAACU,WAAW,CAACpB,KAAK,CAAC;EACzB;EAEQoB,WAAWA,CAACpB,KAAiB;IACnC,MAAMqB,IAAI,GAAG,IAAI,CAAC3B,EAAE,CAAC4B,aAAa,CAACC,qBAAqB,EAAE;IAC1D;IAEA;IACA;IAEA,IAAI,CAACzB,cAAc,GAAGkB,QAAQ,CAACQ,aAAa,CAAC,KAAK,CAAC;IACnD,IAAI,CAAC1B,cAAc,CAAC2B,SAAS,GAAG,oBAAoB;IACpD,IAAI,CAAC3B,cAAc,CAAC4B,KAAK,CAACC,QAAQ,GAAG,OAAO;IAC5C,IAAI,CAAC7B,cAAc,CAAC4B,KAAK,CAACE,GAAG,GAAG,GAAGP,IAAI,CAACO,GAAG,IAAI;IAC/C,IAAI,CAAC9B,cAAc,CAAC4B,KAAK,CAACG,IAAI,GAAG,GAAGR,IAAI,CAACQ,IAAI,GAAG,EAAE,IAAI;IACtD;IACA;IACA,IAAI,CAAC/B,cAAc,CAAC4B,KAAK,CAACI,MAAM,GAAG,IAAI;IACvC;IAEA;IAEA;IACA,IAAI,IAAI,CAACjC,QAAQ,CAACc,KAAK,IAAI,IAAI,CAACd,QAAQ,CAACc,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAACmB,aAAa,CAAC,IAAI,CAAClC,QAAQ,CAACc,KAAK,EAAE,IAAI,CAACb,cAAc,CAAC;;IAG9DkB,QAAQ,CAACgB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACnC,cAAc,CAAC;EAChD;EAEQY,WAAWA,CAAA;IACjB,IAAIK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACxD,IAAIF,IAAI,EAAE;MACR;MACAA,IAAI,CAACmB,MAAM,EAAE;MACb;;EAEJ;;EAEQH,aAAaA,CAACpB,KAAY,EAAEwB,aAA6B;IAC/DxB,KAAK,CAACyB,OAAO,CAAEC,IAAS,IAAI;MAC1B,MAAMC,UAAU,GAAGtB,QAAQ,CAACQ,aAAa,CAAC,KAAK,CAAC;MAChDc,UAAU,CAACb,SAAS,GAAG,cAAc;MACrCa,UAAU,CAACC,SAAS,GAAGF,IAAI,CAACG,KAAK;MAEjC;MACA,IAAIH,IAAI,CAAC7B,UAAU,EAAE;QACnB,MAAMN,GAAG,GAAGmC,IAAI,CAAC7B,UAAU,CAACiC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QAC3C;QACA;QACA,IAAK,IAAI,CAAC7C,MAAM,CAACM,GAAG,IAAEA,GAAG,IAAG,IAAI,CAACN,MAAM,CAACM,GAAG,GAAC,GAAG,IAAEA,GAAG,EAAG;UACrD,IAAI,CAACP,QAAQ,CAAC+C,QAAQ,CAACJ,UAAU,EAAE,sBAAsB,CAAC;;QAE5D,IAAI,CAAC3C,QAAQ,CAACgD,MAAM,CAACL,UAAU,EAAE,OAAO,EAAE,MAAK;UAC7C,IAAI,CAAC1C,MAAM,CAACa,QAAQ,CAAC4B,IAAI,CAAC7B,UAAU,CAAC;UACrC,IAAI,CAACE,WAAW,EAAE;QACpB,CAAC,CAAC;OACH,MAAM;QACL;MAAA;MAGFyB,aAAa,CAACF,WAAW,CAACK,UAAU,CAAC;IACvC,CAAC,CAAC;EACJ;;;uBA9GW9C,uBAAuB,EAAAoD,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,SAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBzD,uBAAuB;MAAA0D,SAAA;MAAAC,YAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAvBC,GAAA,CAAAvD,OAAA,CAAAwD,MAAA,CAAe;UAAA,0BAAAC,sDAAAD,MAAA;YAAA,OAAfD,GAAA,CAAAxC,YAAA,CAAAyC,MAAA,CAAoB;UAAA,qBAAAE,iDAAAF,MAAA;YAAA,OAApBD,GAAA,CAAApC,eAAA,CAAAqC,MAAA,CAAuB;UAAA,UAAAX,EAAA,CAAAc,iBAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}