{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class ShareManagementService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/share\";\n  }\n  search(body, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/search`, {}, body, {}, callback, errorCallBack, finallyCallback);\n  }\n  shareTraffic(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}`, {\n      timeout: 180000\n    }, body, {}, callback, errorCallback, finallyCallback);\n  }\n  shareTrafficFromDate(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/auto-share-from-date`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  shareTrafficByGroup(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/by-group`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  create(body, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(this.prefixApi + \"/create\", {}, body, {}, callback, errorCallBack, finallyCallback);\n  }\n  getListShareInfoCbb(param, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/cbb/get-list-phone`, {}, param, callback, errorCallback, finallyCallback);\n  }\n  sendOTP(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/send-otp`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  downloadTemplate() {\n    this.httpService.downloadLocal(`/assets/data/share-info-import-file.xlsx`, \"Mẫu_file_thông_tin_người_chia_sẻ.xlsx\");\n  }\n  downloadTemplateReceiveInfo() {\n    this.httpService.downloadLocal(`/assets/data/phone-info-import-file.xlsx`, \"Mau_import_SDT_nguoi_duoc_chia_se.xlsx\");\n  }\n  uploadFileShareInfo(objectFile, callback, errorCallback, finallyCallback) {\n    this.httpService.upload(`${this.prefixApi}/import/share_info`, objectFile, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  getByMsisdn(msisdn, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/shareInfo/getOne`, {}, {\n      msisdn: msisdn\n    }, callback, errorCallback, finallyCallback);\n  }\n  updateShared(body, callback, errorCallback, finallyCallback) {\n    this.httpService.put(this.prefixApi + \"/update\", {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  deleteShared(id, callback, errorCallback, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/delete/${id}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  checkExisted(body, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(this.prefixApi + \"/check-phone\", {}, body, {}, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function ShareManagementService_Factory(t) {\n      return new (t || ShareManagementService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ShareManagementService,\n      factory: ShareManagementService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "ShareManagementService", "constructor", "httpService", "prefixApi", "search", "body", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "post", "shareTraffic", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "shareTrafficFromDate", "shareTrafficByGroup", "create", "getListShareInfoCbb", "param", "get", "sendOTP", "downloadTemplate", "downloadLocal", "downloadTemplateReceiveInfo", "uploadFileShareInfo", "objectFile", "upload", "getByMsisdn", "msisdn", "updateShared", "put", "deleteShared", "id", "delete", "checkExisted", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\datapool\\ShareManagementService.ts"], "sourcesContent": ["import { callback } from 'chart.js/types/helpers';\r\nimport { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class ShareManagementService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/share\";\r\n    }\r\n\r\n    public search(body:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}/search`,{}, body,{},callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public shareTraffic(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}`, {timeout : 180000},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public shareTrafficFromDate(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/auto-share-from-date`,{},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public shareTrafficByGroup(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/by-group`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public create(body,callback?:Function, errorCallBack?:Function, finallyCallback?: Function){\r\n        this.httpService.post(this.prefixApi+\"/create\", {}, body, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getListShareInfoCbb(param, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/cbb/get-list-phone`, {}, param, callback, errorCallback, finallyCallback);\r\n    }\r\n    public sendOTP(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/send-otp`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public downloadTemplate(){\r\n        this.httpService.downloadLocal(`/assets/data/share-info-import-file.xlsx`, \"Mẫu_file_thông_tin_người_chia_sẻ.xlsx\");\r\n    }\r\n\r\n    public downloadTemplateReceiveInfo(){\r\n        this.httpService.downloadLocal(`/assets/data/phone-info-import-file.xlsx`, \"Mau_import_SDT_nguoi_duoc_chia_se.xlsx\");\r\n    }\r\n\r\n    public uploadFileShareInfo(objectFile, callback:Function,errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.upload(`${this.prefixApi}/import/share_info`, objectFile,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public getByMsisdn(msisdn: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/shareInfo/getOne`,{}, {msisdn: msisdn}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public updateShared(body, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(this.prefixApi+\"/update\",{}, body,{}, callback, errorCallback, finallyCallback)\r\n    }\r\n\r\n    public deleteShared(id:number, callback?: Function, errorCallback?: Function, finallyCallback?: Function){\r\n        this.httpService.delete(`${this.prefixApi}/delete/${id}`,{},{},callback,errorCallback,finallyCallback)\r\n    }\r\n\r\n    public checkExisted(body,callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.post(this.prefixApi+\"/check-phone\", {}, body, {}, callback, errorCallBack, finallyCallback)\r\n    }\r\n\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,sBAAsB;EAE/BC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,QAAQ;EAC7B;EAEOC,MAAMA,CAACC,IAAuB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACzG,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAC,EAAE,EAAEE,IAAI,EAAC,EAAE,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1G;EAEOE,YAAYA,CAACL,IAAI,EAACC,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IAC7F,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,EAAE,EAAE;MAACS,OAAO,EAAG;IAAM,CAAC,EAACP,IAAI,EAAC,EAAE,EAAEC,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EACpH;EAEOK,oBAAoBA,CAACR,IAAI,EAACC,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IACrG,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,uBAAuB,EAAC,EAAE,EAACE,IAAI,EAAC,EAAE,EAAEC,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EACxH;EAEOM,mBAAmBA,CAACT,IAAI,EAACC,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IACpG,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,WAAW,EAAE,EAAE,EAACE,IAAI,EAAC,EAAE,EAAEC,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EAC7G;EAEOO,MAAMA,CAACV,IAAI,EAACC,QAAkB,EAAEC,aAAuB,EAAEC,eAA0B;IACtF,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,IAAI,CAACN,SAAS,GAAC,SAAS,EAAE,EAAE,EAAEE,IAAI,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC3G;EAEOQ,mBAAmBA,CAACC,KAAK,EAAEX,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IACtG,IAAI,CAACN,WAAW,CAACgB,GAAG,CAAC,GAAG,IAAI,CAACf,SAAS,qBAAqB,EAAE,EAAE,EAAEc,KAAK,EAAEX,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EACrH;EACOW,OAAOA,CAACd,IAAI,EAACC,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IACxF,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,SAAS,WAAW,EAAE,EAAE,EAACE,IAAI,EAAC,EAAE,EAAEC,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EAC7G;EACOY,gBAAgBA,CAAA;IACnB,IAAI,CAAClB,WAAW,CAACmB,aAAa,CAAC,0CAA0C,EAAE,uCAAuC,CAAC;EACvH;EAEOC,2BAA2BA,CAAA;IAC9B,IAAI,CAACpB,WAAW,CAACmB,aAAa,CAAC,0CAA0C,EAAE,wCAAwC,CAAC;EACxH;EAEOE,mBAAmBA,CAACC,UAAU,EAAElB,QAAiB,EAACK,aAAuB,EAAEH,eAA0B;IACxG,IAAI,CAACN,WAAW,CAACuB,MAAM,CAAC,GAAG,IAAI,CAACtB,SAAS,oBAAoB,EAAEqB,UAAU,EAAC,EAAE,EAAE,EAAE,EAAElB,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EAC/H;EACOkB,WAAWA,CAACC,MAAc,EAAErB,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IACvG,IAAI,CAACN,WAAW,CAACgB,GAAG,CAAC,GAAG,IAAI,CAACf,SAAS,mBAAmB,EAAC,EAAE,EAAE;MAACwB,MAAM,EAAEA;IAAM,CAAC,EAAErB,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EAC7H;EAEOoB,YAAYA,CAACvB,IAAI,EAAEC,QAAmB,EAAEK,aAAuB,EAAEH,eAA0B;IAC9F,IAAI,CAACN,WAAW,CAAC2B,GAAG,CAAC,IAAI,CAAC1B,SAAS,GAAC,SAAS,EAAC,EAAE,EAAEE,IAAI,EAAC,EAAE,EAAEC,QAAQ,EAAEK,aAAa,EAAEH,eAAe,CAAC;EACxG;EAEOsB,YAAYA,CAACC,EAAS,EAAEzB,QAAmB,EAAEK,aAAwB,EAAEH,eAA0B;IACpG,IAAI,CAACN,WAAW,CAAC8B,MAAM,CAAC,GAAG,IAAI,CAAC7B,SAAS,WAAW4B,EAAE,EAAE,EAAC,EAAE,EAAC,EAAE,EAACzB,QAAQ,EAACK,aAAa,EAACH,eAAe,CAAC;EAC1G;EAEOyB,YAAYA,CAAC5B,IAAI,EAACC,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IAC9F,IAAI,CAACN,WAAW,CAACO,IAAI,CAAC,IAAI,CAACN,SAAS,GAAC,cAAc,EAAE,EAAE,EAAEE,IAAI,EAAE,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAChH;;;uBAzDSR,sBAAsB,EAAAkC,EAAA,CAAAC,QAAA,CAEXpC,WAAW;IAAA;EAAA;;;aAFtBC,sBAAsB;MAAAoC,OAAA,EAAtBpC,sBAAsB,CAAAqC;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}