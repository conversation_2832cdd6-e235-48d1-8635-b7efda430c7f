{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class DistrictAddressService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/address\";\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/district`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function DistrictAddressService_Factory(t) {\n      return new (t || DistrictAddressService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DistrictAddressService,\n      factory: DistrictAddressService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "DistrictAddressService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\address\\DistrictAddressService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class DistrictAddressService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/address\";\r\n    }\r\n    public search(params: any, callback: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/district`, {}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,sBAAsB;EAE/BC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,UAAU;EAC/B;EACOC,MAAMA,CAACC,MAAW,EAAEC,QAAkB,EAAEC,aAAwB,EAAEC,eAA0B;IAC/F,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,WAAW,EAAE,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC3G;;;uBAPSR,sBAAsB,EAAAU,EAAA,CAAAC,QAAA,CAEXZ,WAAW;IAAA;EAAA;;;aAFtBC,sBAAsB;MAAAY,OAAA,EAAtBZ,sBAAsB,CAAAa;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}