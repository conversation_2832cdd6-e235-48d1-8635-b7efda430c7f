{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Nynorsk [nn]\n//! authors : https://github.com/mechuwind\n//!           <PERSON> : https://github.com/stephen<PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var nn = moment.defineLocale('nn', {\n    months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n    monthsShort: 'jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag'.split('_'),\n    weekdaysShort: 'su._må._ty._on._to._fr._lau.'.split('_'),\n    weekdaysMin: 'su_må_ty_on_to_fr_la'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY [kl.] H:mm',\n      LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[I dag klokka] LT',\n      nextDay: '[I morgon klokka] LT',\n      nextWeek: 'dddd [klokka] LT',\n      lastDay: '[I går klokka] LT',\n      lastWeek: '[Føregåande] dddd [klokka] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: '%s sidan',\n      s: 'nokre sekund',\n      ss: '%d sekund',\n      m: 'eit minutt',\n      mm: '%d minutt',\n      h: 'ein time',\n      hh: '%d timar',\n      d: 'ein dag',\n      dd: '%d dagar',\n      w: 'ei veke',\n      ww: '%d veker',\n      M: 'ein månad',\n      MM: '%d månader',\n      y: 'eit år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return nn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "nn", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/moment/locale/nn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Nynorsk [nn]\n//! authors : https://github.com/mechuwind\n//!           <PERSON> : https://github.com/stephenram<PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var nn = moment.defineLocale('nn', {\n        months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split(\n            '_'\n        ),\n        monthsShort:\n            'jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n        monthsParseExact: true,\n        weekdays: 'sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag'.split('_'),\n        weekdaysShort: 'su._må._ty._on._to._fr._lau.'.split('_'),\n        weekdaysMin: 'su_må_ty_on_to_fr_la'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY [kl.] H:mm',\n            LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm',\n        },\n        calendar: {\n            sameDay: '[I dag klokka] LT',\n            nextDay: '[I morgon klokka] LT',\n            nextWeek: 'dddd [klokka] LT',\n            lastDay: '[I går klokka] LT',\n            lastWeek: '[Føregåande] dddd [klokka] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'om %s',\n            past: '%s sidan',\n            s: 'nokre sekund',\n            ss: '%d sekund',\n            m: 'eit minutt',\n            mm: '%d minutt',\n            h: 'ein time',\n            hh: '%d timar',\n            d: 'ein dag',\n            dd: '%d dagar',\n            w: 'ei veke',\n            ww: '%d veker',\n            M: 'ein månad',\n            MM: '%d månader',\n            y: 'eit år',\n            yy: '%d år',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return nn;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;IACDC,WAAW,EACP,4DAA4D,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,oDAAoD,CAACH,KAAK,CAAC,GAAG,CAAC;IACzEI,aAAa,EAAE,8BAA8B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACxDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE,sBAAsB;MAC/BC,QAAQ,EAAE,kBAAkB;MAC5BC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,+BAA+B;MACzCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAO7C,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}