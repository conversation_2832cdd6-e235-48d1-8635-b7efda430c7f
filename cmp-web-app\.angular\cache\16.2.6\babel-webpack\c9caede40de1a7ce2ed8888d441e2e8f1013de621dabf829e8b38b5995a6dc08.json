{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class WalletConfigService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/wallet\";\n  }\n  static {\n    this.ɵfac = function WalletConfigService_Factory(t) {\n      return new (t || WalletConfigService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WalletConfigService,\n      factory: WalletConfigService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "WalletConfigService", "constructor", "httpService", "prefixApi", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\datapool\\WalletConfigService.ts"], "sourcesContent": ["import { Inject, Injectable } from \"@angular/core\";\r\nimport { HttpService } from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class WalletConfigService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/wallet\";\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,uBAAuB;;;AAGnD,OAAM,MAAOC,mBAAmB;EAE5BC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,SAAS;EAC9B;;;uBAJSH,mBAAmB,EAAAI,EAAA,CAAAC,QAAA,CAERN,WAAW;IAAA;EAAA;;;aAFtBC,mBAAmB;MAAAM,OAAA,EAAnBN,mBAAmB,CAAAO;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}