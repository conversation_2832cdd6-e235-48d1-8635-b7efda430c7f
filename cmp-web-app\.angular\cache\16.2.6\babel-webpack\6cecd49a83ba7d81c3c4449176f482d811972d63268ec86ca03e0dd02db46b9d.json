{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppApnSimListComponent } from \"./list/app.apnsim.list.component\";\nimport { AppApnSimDetailComponent } from \"./detail/app.apnsim.detail.component\";\nimport DataPage from \"../../service/data.page\";\nimport { CONSTANTS } from \"../../service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppApnSimRoutingModule {\n  static {\n    this.ɵfac = function AppApnSimRoutingModule_Factory(t) {\n      return new (t || AppApnSimRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppApnSimRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: AppApnSimListComponent,\n        data: new DataPage(\"global.titlepage.listApnSim\", [CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])\n      }, {\n        path: \"detail/:apnId\",\n        component: AppApnSimDetailComponent,\n        data: new DataPage(\"global.titlepage.detailApnSim\", [CONSTANTS.PERMISSIONS.APN_SIM.VIEW_DETAIL])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppApnSimRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppApnSimListComponent", "AppApnSimDetailComponent", "DataPage", "CONSTANTS", "AppApnSimRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "APN_SIM", "VIEW_LIST", "VIEW_DETAIL", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\apn-sim\\app.apnsim.routing.ts"], "sourcesContent": ["import { RouterModule } from '@angular/router';\r\nimport { NgModule } from '@angular/core';\r\nimport { AppApnSimListComponent} from \"./list/app.apnsim.list.component\";\r\nimport {AppApnSimDetailComponent} from \"./detail/app.apnsim.detail.component\";\r\nimport DataPage from \"../../service/data.page\";\r\nimport {CONSTANTS} from \"../../service/comon/constants\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {path: \"\", component: AppApnSimListComponent, data: new DataPage(\"global.titlepage.listApnSim\", [CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])},\r\n            {path: \"detail/:apnId\", component: AppApnSimDetailComponent, data: new DataPage(\"global.titlepage.detailApnSim\", [CONSTANTS.PERMISSIONS.APN_SIM.VIEW_DETAIL])}\r\n        ])\r\n    ],\r\n    exports: [RouterModule]\r\n})\r\nexport class AppApnSimRoutingModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAO,kCAAkC;AACxE,SAAQC,wBAAwB,QAAO,sCAAsC;AAC7E,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,SAAQC,SAAS,QAAO,+BAA+B;;;AAWvD,OAAM,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAP3BL,YAAY,CAACM,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEP,sBAAsB;QAAEQ,IAAI,EAAE,IAAIN,QAAQ,CAAC,6BAA6B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,OAAO,CAACC,SAAS,CAAC;MAAC,CAAC,EAC3I;QAACL,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAEN,wBAAwB;QAAEO,IAAI,EAAE,IAAIN,QAAQ,CAAC,+BAA+B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,OAAO,CAACE,WAAW,CAAC;MAAC,CAAC,CACjK,CAAC,EAEIb,YAAY;IAAA;EAAA;;;2EAEbK,sBAAsB;IAAAS,OAAA,GAAAC,EAAA,CAAAf,YAAA;IAAAgB,OAAA,GAFrBhB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}