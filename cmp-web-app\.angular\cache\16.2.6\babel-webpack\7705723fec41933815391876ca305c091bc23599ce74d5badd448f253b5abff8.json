{"ast": null, "code": "export default {\n  emailIncorect: \"Thông tin đăng nhập không chính xác. <PERSON>ui lòng thử lại.\",\n  passwordIncorect: \"Thông tin đăng nhập không chính xác. Vui lòng thử lại.\",\n  accountInactive: \"Thông tin đăng nhập không chính xác. Vui lòng thử lại.\",\n  length: \"less than ${0} and great than ${1}\",\n  object: {\n    not: {\n      found: \"Không tìm thấy trường ${0} với giá trị ${1}\"\n    }\n  },\n  field: {\n    must: {\n      be: {\n        not: {\n          null: \"${0} must be not null\"\n        }\n      }\n    }\n  },\n  duplicate: {\n    name: \"duplicate name\"\n  },\n  invalid: {\n    field: \"field ${0} invalid\",\n    email: \"Email không tồn tại!\",\n    phone: {\n      number: \"invalid phone number\"\n    },\n    password: \"Mật khẩu hiện tại sai\",\n    type: \"invalid type\",\n    isdn: {\n      empty: \"<PERSON>hu<PERSON> bao không được để trống\",\n      format: \"Thu<PERSON> bao không đúng định dạng\",\n      not: {\n        permission: \"Không có quyền trên thuê bao\",\n        inact: \"Thuê bao phải có trạng thái Khóa 1 chiều, Khóa 2 chiều\",\n        exist: \"Thuê bao không tồn tại\"\n      },\n      duplicated: \"Thuê bao bị trùng\",\n      status: {\n        purge: \"Sim đã hủy\"\n      }\n    },\n    msisdn: {\n      not: {\n        active: \"Thuê bao phải có trạng thái Hoạt động\"\n      },\n      register: {\n        rate: {\n          pending: \"Thuê bao phải có trạng thái Hoạt động\"\n        }\n      }\n    },\n    rating: {\n      empty: \"Gói cước không được để trống\",\n      format: \"Gói cước không đúng định dạng\",\n      not: {\n        permission: \"Không có quyền trên gói cước\",\n        inact: \"Gói cước phải có trạng thái hoạt động\",\n        exist: \"Gói cước không tồn tại\"\n      },\n      customer: {\n        not: {\n          permission: \"Gói cước không cho phép đăng ký theo nhóm và file. Vui lòng đăng ký đơn lẻ!\"\n        }\n      }\n    },\n    file: {\n      form: {\n        format: \"Định dạng file không hợp lệ\",\n        extension: \"Sai định dạng file excel\",\n        maxrow: \"Số lượng bản ghi quá giới hạn 1000\"\n      }\n    }\n  },\n  forbbiden: {\n    resource: \"Không có quyền thao tác\"\n  },\n  data: {\n    format: \"Invalid data format\"\n  },\n  user: {\n    not: {\n      found: \"${0} user not found\"\n    }\n  },\n  valid: {\n    assert: {\n      false: \"Must be false\",\n      true: \"Must be true\"\n    },\n    decimal: {\n      max: \"Must be less than ${0}\",\n      min: \"Must be greater than ${0}\"\n    },\n    digits: \"Numeric value out of bounds (<${0} digits>.<${1} digits> expected)\",\n    email: \"Must be a well-formed email address\",\n    max: \"Must be less than or equal to ${0}\",\n    min: \"Must be greater than or equal to ${0}\",\n    negative: {\n      \"\": \"Must be less than 0\",\n      or: {\n        zero: \"Must be less than or equal to 0\"\n      }\n    },\n    not: {\n      blank: \"Must not be blank\",\n      empty: \"Must not be empty\",\n      null: \"Field cannot NULL.\"\n    },\n    null: \"Must be null\",\n    range: \"Must be between ${0} and ${1}\",\n    past: {\n      \"\": \"Must be a past date\",\n      or: {\n        present: \"Must be a date in the past or in the present\"\n      }\n    },\n    pattern: 'Must match \"${0}\"',\n    phone: {\n      pattern: \"Invalid phone number\"\n    },\n    positive: {\n      \"\": \"Must be greater than 0\",\n      or: {\n        zero: \"Must be greater than or equal to 0\"\n      }\n    },\n    size: \"Size must be between ${0} and ${1}\",\n    length: \"Size must be between ${0} and ${1}\"\n  },\n  id: {\n    must: {\n      be: {\n        null: \"Id must be null\"\n      }\n    }\n  },\n  passwords: {\n    do: {\n      not: {\n        match: \"Mật khẩu không khớp\"\n      }\n    }\n  },\n  the: {\n    new: {\n      password: {\n        must: {\n          be: {\n            different: {\n              from: {\n                the: {\n                  old: {\n                    one: \"the new password must be different from the old one\"\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  },\n  exists: \"${0} exists\",\n  bad: {\n    request: \"Bad request\"\n  },\n  role: {\n    not: {\n      working: \"Bạn không có quyền truy cập hệ thống. Hãy liên hệ quản trị viên để được hỗ trợ\"\n    }\n  },\n  report: {\n    query: \"Lôi câu truy vấn\",\n    limit: {\n      row: \"Số lượng bản ghi báo cáo quá lớn\"\n    }\n  },\n  status: {\n    400: \"Bad request\",\n    401: \"Unauthorized\",\n    403: \"Forbidden\",\n    404: \"Not found\",\n    406: \"Not Acceptable\",\n    408: \"Request Timeout\",\n    409: \"Conflict\",\n    500: \"Internal Server Error\"\n  },\n  register: {\n    rate: {\n      groupsim: {\n        empty: \"Danh sách sim rỗng\"\n      },\n      in: {\n        other: {\n          process: \"Thuê bao phải có trạng thái Hoạt động\"\n        }\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["emailIncorect", "passwordIncorect", "accountInactive", "length", "object", "not", "found", "field", "must", "be", "null", "duplicate", "name", "invalid", "email", "phone", "number", "password", "type", "isdn", "empty", "format", "permission", "inact", "exist", "duplicated", "status", "purge", "msisdn", "active", "register", "rate", "pending", "rating", "customer", "file", "form", "extension", "maxrow", "for<PERSON><PERSON>", "resource", "data", "user", "valid", "assert", "false", "true", "decimal", "max", "min", "digits", "negative", "or", "zero", "blank", "range", "past", "present", "pattern", "positive", "size", "id", "passwords", "do", "match", "the", "new", "different", "from", "old", "one", "exists", "bad", "request", "role", "working", "report", "query", "limit", "row", "groupsim", "in", "other", "process"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\error.ts"], "sourcesContent": ["export default {\r\n    emailIncorect: \"Thông tin đăng nhập không chính xác. <PERSON>ui lòng thử lại.\",\r\n    passwordIncorect: \"Thông tin đăng nhập không chính xác. Vui lòng thử lại.\",\r\n    accountInactive: \"Thông tin đăng nhập không chính xác. Vui lòng thử lại.\",\r\n    length: \"less than ${0} and great than ${1}\",\r\n    object: {\r\n        not: {\r\n            found: \"Không tìm thấy trường ${0} với giá trị ${1}\"\r\n        }\r\n    },\r\n    field: {\r\n        must: {\r\n            be: {\r\n                not: {\r\n                    null: \"${0} must be not null\"\r\n                }\r\n            }\r\n        }\r\n    },\r\n    duplicate: {\r\n        name: \"duplicate name\"\r\n    },\r\n    invalid: {\r\n        field: \"field ${0} invalid\",\r\n        email: \"Email không tồn tại!\",\r\n        phone: {\r\n            number: \"invalid phone number\"\r\n        },\r\n        password: \"Mật khẩu hiện tại sai\",\r\n        type: \"invalid type\",\r\n        isdn: {\r\n            empty: \"<PERSON>hu<PERSON> bao không được để trống\",\r\n            format: \"Thu<PERSON> bao không đúng định dạng\",\r\n            not: {\r\n                permission: \"Không có quyền trên thuê bao\",\r\n                inact: \"Thuê bao phải có trạng thái Khóa 1 chiều, Khóa 2 chiều\",\r\n                exist: \"Thuê bao không tồn tại\"\r\n            },\r\n            duplicated: \"Thuê bao bị trùng\",\r\n            status: {\r\n                purge: \"Sim đã hủy\",\r\n            }\r\n        },\r\n        msisdn: {\r\n            not: {\r\n                active: \"Thuê bao phải có trạng thái Hoạt động\"\r\n            },\r\n            register: {\r\n                rate: {\r\n                    pending: \"Thuê bao phải có trạng thái Hoạt động\"\r\n                }\r\n            }\r\n        },\r\n        rating: {\r\n            empty: \"Gói cước không được để trống\",\r\n            format: \"Gói cước không đúng định dạng\",\r\n            not: {\r\n                permission: \"Không có quyền trên gói cước\",\r\n                inact: \"Gói cước phải có trạng thái hoạt động\",\r\n                exist: \"Gói cước không tồn tại\"\r\n            },\r\n            customer:{\r\n                not: {\r\n                    permission: \"Gói cước không cho phép đăng ký theo nhóm và file. Vui lòng đăng ký đơn lẻ!\",\r\n                }\r\n            }\r\n        },\r\n        file: {\r\n            form: {\r\n                format: \"Định dạng file không hợp lệ\",\r\n                extension: \"Sai định dạng file excel\",\r\n                maxrow: \"Số lượng bản ghi quá giới hạn 1000\"\r\n            }\r\n        }\r\n    },\r\n    forbbiden: {\r\n        resource: \"Không có quyền thao tác\",\r\n    },\r\n    data: {\r\n        format: \"Invalid data format\"\r\n    },\r\n    user: {\r\n        not: {\r\n            found: \"${0} user not found\"\r\n        }\r\n    },\r\n    valid: {\r\n        assert: {\r\n            false: \"Must be false\",\r\n            true: \"Must be true\"\r\n        },\r\n        decimal: {\r\n            max: \"Must be less than ${0}\",\r\n            min: \"Must be greater than ${0}\"\r\n        },\r\n        digits: \"Numeric value out of bounds (<${0} digits>.<${1} digits> expected)\",\r\n        email: \"Must be a well-formed email address\",\r\n        max: \"Must be less than or equal to ${0}\",\r\n        min: \"Must be greater than or equal to ${0}\",\r\n        negative: {\r\n            \"\": \"Must be less than 0\",\r\n            or: {\r\n                zero: \"Must be less than or equal to 0\"\r\n            }\r\n        },\r\n        not: {\r\n            blank: \"Must not be blank\",\r\n            empty: \"Must not be empty\",\r\n            null: \"Field cannot NULL.\"\r\n        },\r\n        null: \"Must be null\",\r\n        range: \"Must be between ${0} and ${1}\",\r\n        past: {\r\n            \"\": \"Must be a past date\",\r\n            or: {\r\n                present: \"Must be a date in the past or in the present\"\r\n            }\r\n        },\r\n        pattern: 'Must match \"${0}\"',\r\n        phone: {\r\n            pattern: \"Invalid phone number\"\r\n        },\r\n        positive: {\r\n            \"\": \"Must be greater than 0\",\r\n            or: {\r\n                zero: \"Must be greater than or equal to 0\"\r\n            }\r\n        },\r\n        size: \"Size must be between ${0} and ${1}\",\r\n        length: \"Size must be between ${0} and ${1}\"\r\n    },\r\n    id: {\r\n        must: {\r\n            be: {\r\n                null: \"Id must be null\"\r\n            }\r\n        }\r\n    },\r\n    passwords: {\r\n        do: {\r\n            not: {\r\n                match: \"Mật khẩu không khớp\"\r\n            }\r\n        }\r\n    },\r\n    the: {\r\n        new: {\r\n            password: {\r\n                must: {\r\n                    be: {\r\n                        different: {\r\n                            from: {\r\n                                the: {\r\n                                    old: {\r\n                                        one: \"the new password must be different from the old one\"\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    },\r\n    exists: \"${0} exists\",\r\n    bad: {\r\n        request: \"Bad request\"\r\n    },\r\n    role: {\r\n        not: {\r\n            working: \"Bạn không có quyền truy cập hệ thống. Hãy liên hệ quản trị viên để được hỗ trợ\"\r\n        }\r\n    },\r\n    report: {\r\n        query: \"Lôi câu truy vấn\",\r\n        limit: {\r\n            row: \"Số lượng bản ghi báo cáo quá lớn\"\r\n        }\r\n    },\r\n    status: {\r\n        400: \"Bad request\",\r\n        401: \"Unauthorized\",\r\n        403: \"Forbidden\",\r\n        404: \"Not found\",\r\n        406: \"Not Acceptable\",\r\n        408: \"Request Timeout\",\r\n        409: \"Conflict\",\r\n        500: \"Internal Server Error\"\r\n    },\r\n    register:{\r\n        rate:{\r\n            groupsim: {\r\n                empty: \"Danh sách sim rỗng\"\r\n            },\r\n            in: {\r\n                other: {\r\n                    process: \"Thuê bao phải có trạng thái Hoạt động\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,aAAa,EAAE,wDAAwD;EACvEC,gBAAgB,EAAE,wDAAwD;EAC1EC,eAAe,EAAE,wDAAwD;EACzEC,MAAM,EAAE,oCAAoC;EAC5CC,MAAM,EAAE;IACJC,GAAG,EAAE;MACDC,KAAK,EAAE;;GAEd;EACDC,KAAK,EAAE;IACHC,IAAI,EAAE;MACFC,EAAE,EAAE;QACAJ,GAAG,EAAE;UACDK,IAAI,EAAE;;;;GAIrB;EACDC,SAAS,EAAE;IACPC,IAAI,EAAE;GACT;EACDC,OAAO,EAAE;IACLN,KAAK,EAAE,oBAAoB;IAC3BO,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE;MACHC,MAAM,EAAE;KACX;IACDC,QAAQ,EAAE,uBAAuB;IACjCC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MACFC,KAAK,EAAE,8BAA8B;MACrCC,MAAM,EAAE,+BAA+B;MACvChB,GAAG,EAAE;QACDiB,UAAU,EAAE,8BAA8B;QAC1CC,KAAK,EAAE,wDAAwD;QAC/DC,KAAK,EAAE;OACV;MACDC,UAAU,EAAE,mBAAmB;MAC/BC,MAAM,EAAE;QACJC,KAAK,EAAE;;KAEd;IACDC,MAAM,EAAE;MACJvB,GAAG,EAAE;QACDwB,MAAM,EAAE;OACX;MACDC,QAAQ,EAAE;QACNC,IAAI,EAAE;UACFC,OAAO,EAAE;;;KAGpB;IACDC,MAAM,EAAE;MACJb,KAAK,EAAE,8BAA8B;MACrCC,MAAM,EAAE,+BAA+B;MACvChB,GAAG,EAAE;QACDiB,UAAU,EAAE,8BAA8B;QAC1CC,KAAK,EAAE,uCAAuC;QAC9CC,KAAK,EAAE;OACV;MACDU,QAAQ,EAAC;QACL7B,GAAG,EAAE;UACDiB,UAAU,EAAE;;;KAGvB;IACDa,IAAI,EAAE;MACFC,IAAI,EAAE;QACFf,MAAM,EAAE,6BAA6B;QACrCgB,SAAS,EAAE,0BAA0B;QACrCC,MAAM,EAAE;;;GAGnB;EACDC,SAAS,EAAE;IACPC,QAAQ,EAAE;GACb;EACDC,IAAI,EAAE;IACFpB,MAAM,EAAE;GACX;EACDqB,IAAI,EAAE;IACFrC,GAAG,EAAE;MACDC,KAAK,EAAE;;GAEd;EACDqC,KAAK,EAAE;IACHC,MAAM,EAAE;MACJC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE;KACT;IACDC,OAAO,EAAE;MACLC,GAAG,EAAE,wBAAwB;MAC7BC,GAAG,EAAE;KACR;IACDC,MAAM,EAAE,oEAAoE;IAC5EpC,KAAK,EAAE,qCAAqC;IAC5CkC,GAAG,EAAE,oCAAoC;IACzCC,GAAG,EAAE,uCAAuC;IAC5CE,QAAQ,EAAE;MACN,EAAE,EAAE,qBAAqB;MACzBC,EAAE,EAAE;QACAC,IAAI,EAAE;;KAEb;IACDhD,GAAG,EAAE;MACDiD,KAAK,EAAE,mBAAmB;MAC1BlC,KAAK,EAAE,mBAAmB;MAC1BV,IAAI,EAAE;KACT;IACDA,IAAI,EAAE,cAAc;IACpB6C,KAAK,EAAE,+BAA+B;IACtCC,IAAI,EAAE;MACF,EAAE,EAAE,qBAAqB;MACzBJ,EAAE,EAAE;QACAK,OAAO,EAAE;;KAEhB;IACDC,OAAO,EAAE,mBAAmB;IAC5B3C,KAAK,EAAE;MACH2C,OAAO,EAAE;KACZ;IACDC,QAAQ,EAAE;MACN,EAAE,EAAE,wBAAwB;MAC5BP,EAAE,EAAE;QACAC,IAAI,EAAE;;KAEb;IACDO,IAAI,EAAE,oCAAoC;IAC1CzD,MAAM,EAAE;GACX;EACD0D,EAAE,EAAE;IACArD,IAAI,EAAE;MACFC,EAAE,EAAE;QACAC,IAAI,EAAE;;;GAGjB;EACDoD,SAAS,EAAE;IACPC,EAAE,EAAE;MACA1D,GAAG,EAAE;QACD2D,KAAK,EAAE;;;GAGlB;EACDC,GAAG,EAAE;IACDC,GAAG,EAAE;MACDjD,QAAQ,EAAE;QACNT,IAAI,EAAE;UACFC,EAAE,EAAE;YACA0D,SAAS,EAAE;cACPC,IAAI,EAAE;gBACFH,GAAG,EAAE;kBACDI,GAAG,EAAE;oBACDC,GAAG,EAAE;;;;;;;;;GASxC;EACDC,MAAM,EAAE,aAAa;EACrBC,GAAG,EAAE;IACDC,OAAO,EAAE;GACZ;EACDC,IAAI,EAAE;IACFrE,GAAG,EAAE;MACDsE,OAAO,EAAE;;GAEhB;EACDC,MAAM,EAAE;IACJC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE;MACHC,GAAG,EAAE;;GAEZ;EACDrD,MAAM,EAAE;IACJ,GAAG,EAAE,aAAa;IAClB,GAAG,EAAE,cAAc;IACnB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,iBAAiB;IACtB,GAAG,EAAE,UAAU;IACf,GAAG,EAAE;GACR;EACDI,QAAQ,EAAC;IACLC,IAAI,EAAC;MACDiD,QAAQ,EAAE;QACN5D,KAAK,EAAE;OACV;MACD6D,EAAE,EAAE;QACAC,KAAK,EAAE;UACHC,OAAO,EAAE;;;;;CAK5B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}