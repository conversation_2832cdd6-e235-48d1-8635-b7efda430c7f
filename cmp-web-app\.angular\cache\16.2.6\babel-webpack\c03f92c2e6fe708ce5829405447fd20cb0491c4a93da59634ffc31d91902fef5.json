{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class LogHandleTicketService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/log-handle-ticket\";\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  static {\n    this.ɵfac = function LogHandleTicketService_Factory(t) {\n      return new (t || LogHandleTicketService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LogHandleTicketService,\n      factory: LogHandleTicketService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "LogHandleTicketService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\ticket\\LogHandleTicketService.ts"], "sourcesContent": ["import {Inject, Injectable} from \"@angular/core\";\r\nimport {HttpService} from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class LogHandleTicketService{\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/log-handle-ticket\";\r\n    }\r\n\r\n    public search(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/search`,{}, params,callback, errorCallBack, finallyCallback);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,uBAAuB;;;AAGjD,OAAM,MAAOC,sBAAsB;EAE/BC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,oBAAoB;EACzC;EAEOC,MAAMA,CAACC,MAAyB,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC3G,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;;;uBARSR,sBAAsB,EAAAU,EAAA,CAAAC,QAAA,CAEXZ,WAAW;IAAA;EAAA;;;aAFtBC,sBAAsB;MAAAY,OAAA,EAAtBZ,sBAAsB,CAAAa;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}