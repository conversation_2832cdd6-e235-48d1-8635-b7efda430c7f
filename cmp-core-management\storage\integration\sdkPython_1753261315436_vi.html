<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Hướng dẫn tích hợp SDK Python</title>
</head>
<body>

<p><br></p>
<h1 style="margin-left:21.6pt">
    <span style="font-size:17px">1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Giới thiệu</span>
</h1>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">1.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Mục đích tài liệu</span>
</h2>
<p><span style="font-size:17px">Tài liệu là User Guide cho các đối tượng liên quan đến hệ thống M2M được đưa ra với mục đích hướng dẫn người dùng sử dụng, vận hành và khai thác hệ thống M2M một cách hiệu quả và thiết thực thông qua Python SDK.</span></p>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">1.2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Phạm vi</span>
</h2>
<p><span style="font-size:17px">Tài liệu mô tả các chức năng của hệ thống cung cấp và các thao tác sử dụng, khai thác như bên dưới:</span></p>
<div>
    <table border="1">
        <tbody>
            <tr>
                <td><p><strong><span style="font-size:17px">Mục</span></strong></p></td>
                <td><p><strong><span style="font-size:17px">Nội dung</span></strong></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">2.1</span></p></td>
                <td><p><span style="font-size:17px">Môi trường yêu cầu, cài đặt thư viện</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">2.2</span></p></td>
                <td><p><span style="font-size:17px">Khởi tạo</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">2.3</span></p></td>
                <td><p><span style="font-size:17px">Danh sách các hàm</span></p></td>
            </tr>
        </tbody>
    </table>
</div>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">1.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Đối tượng</span>
</h2>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Người dùng cuối sử dụng M2M</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Người quản trị hệ thống, quản trị viên cấp cao</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lập trình viên Python</span>
</p>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">1.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Tài liệu tham khảo</span>
</h2>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Python Documentation: https://docs.python.org/</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Requests Library: https://requests.readthedocs.io/</span>
</p>

<p><strong><span style="font-size:17px"><br></span></strong></p>

<h1 style="margin-left:21.6pt">
    <span style="font-size:17px">2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Miêu tả chức năng</span>
</h1>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">2.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Môi trường yêu cầu, cài đặt thư viện</span>
</h2>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Môi trường yêu cầu Python 3.6+</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Tải Python SDK sau đó giải nén ta được thư mục ism-python-sdk</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Cài đặt các thư viện phụ thuộc:</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">pip install -r requirements.txt</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Cài đặt SDK vào project:</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">pip install -e /path/to/ism-python-sdk</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Hoặc build package và cài đặt:</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">python setup.py sdist bdist_wheel</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">pip install dist/ism-python-sdk-0.1.0-py3-none-any.whl</span>
</p>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">2.2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Khởi tạo</span>
</h2>
<p><span style="font-size:17px">Bước 1: Để sử dụng SDK bạn phải khởi tạo class ISMClient (dùng để thêm các cấu hình config hệ thống và ứng dụng). Thông tin client_id và secret_key được lấy từ website</span></p>

<table border="1">
    <tbody>
        <tr>
            <td><p><strong><span style="font-size:17px">STT</span></strong></p></td>
            <td><p><strong><span style="font-size:17px">Trường</span></strong></p></td>
            <td><p><strong><span style="font-size:17px">Kiểu</span></strong></p></td>
            <td><p><strong><span style="font-size:17px">Ý nghĩa</span></strong></p></td>
        </tr>
        <tr>
            <td><p><strong><span style="font-size:17px">1.</span></strong></p></td>
            <td><p><span style="font-size:17px">client_id</span></p></td>
            <td><p><span style="font-size:17px">str</span></p></td>
            <td><p><span style="font-size:17px">Client Id được cấp trên web</span></p></td>
        </tr>
        <tr>
            <td><p><strong><span style="font-size:17px">2.</span></strong></p></td>
            <td><p><span style="font-size:17px">secret_key</span></p></td>
            <td><p><span style="font-size:17px">str</span></p></td>
            <td><p><span style="font-size:17px">Secret Key được cấp trên web</span></p></td>
        </tr>
        <tr>
            <td><p><strong><span style="font-size:17px">3.</span></strong></p></td>
            <td><p><span style="font-size:17px">base_url</span></p></td>
            <td><p><span style="font-size:17px">str</span></p></td>
            <td><p><span style="font-size:17px">URL API endpoint</span></p></td>
        </tr>
    </tbody>
</table>

<p><span style="font-size:17px">Bước 2: Khởi tạo đối tượng ISMClient và thiết lập user_id cho rate limiting.</span></p>
<p><span style="font-size:17px">Ví dụ:</span></p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">from ism_sdk.client import ISMClient<br><br>
    client = ISMClient(<br>
    &nbsp;&nbsp;&nbsp;&nbsp;base_url="https://api-m2m.oneiot.com.vn/api",<br>
    &nbsp;&nbsp;&nbsp;&nbsp;client_id="your_client_id",<br>
    &nbsp;&nbsp;&nbsp;&nbsp;secret_key="your_secret_key"<br>
    )<br><br>
    client.set_user_id("your_user_id")</span>
</p>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">2.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Danh sách các hàm</span>
</h2>
<p>Response trả về luôn có trường errorCode<br>
<em>"errorCode": 0 thành công<br>
"errorCode": 15 không có quyền truy cập<br>
"errorCode": 99 lỗi hệ thống</em></p>

<h3 style="margin-left:36.0pt">
    <span style="font-size:17px">2.3.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Sim Service</span>
</h3>

<h4 style="margin-left:43.2pt">
    <span style="font-size:17px">2.3.1.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lấy danh sách sim theo mã khách hàng</span>
</h4>
<p><span style="font-size:17px">
response = client.sims.get_list_sim_by_account(<br>
&nbsp;&nbsp;&nbsp;&nbsp;customer_code="LDGCT00599332",<br>
&nbsp;&nbsp;&nbsp;&nbsp;page=0,<br>
&nbsp;&nbsp;&nbsp;&nbsp;page_size=10<br>
)<br><br>
<strong>Tham số:</strong></span></p>

<div>
    <table border="1">
        <tbody>
            <tr>
                <td><p><span style="font-size:17px">Field</span></p></td>
                <td><p><span style="font-size:17px">Type</span></p></td>
                <td><p><span style="font-size:17px">Description</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">customer_code</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Mã khách hàng</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">contract_code</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Mã hợp đồng</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">page</span></p></td>
                <td><p><span style="font-size:17px">int</span></p></td>
                <td><p><span style="font-size:17px">Số trang</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">page_size</span></p></td>
                <td><p><span style="font-size:17px">int</span></p></td>
                <td><p><span style="font-size:17px">Số lượng bản ghi mỗi trang</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">msisdn</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Số thuê bao</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">imsi</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Mã IMSI</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">status</span></p></td>
                <td><p><span style="font-size:17px">list</span></p></td>
                <td><p><span style="font-size:17px">Danh sách trạng thái</span></p></td>
            </tr>
        </tbody>
    </table>
</div>

<h4 style="margin-left:43.2pt">
    <span style="font-size:17px">*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lấy thông tin chi tiết sim theo msisdn</span>
</h4>
<p><span style="font-size:17px">
response = client.sims.get_sim_info("************")<br>
<strong>Tham số:</strong></span></p>

<div>
    <table border="1">
        <tbody>
            <tr>
                <td><p><span style="font-size:17px">Field</span></p></td>
                <td><p><span style="font-size:17px">Type</span></p></td>
                <td><p><span style="font-size:17px">Description</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">msisdn</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Số thuê bao (bắt buộc)</span></p></td>
            </tr>
        </tbody>
    </table>
</div>

<h4 style="margin-left:43.2pt">
    <span style="font-size:17px">*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lấy thông tin data đã sử dụng trong một khoảng</span>
</h4>
<p><span style="font-size:17px">
response = client.usage_data.get_terminal_usage_data_details(<br>
&nbsp;&nbsp;&nbsp;&nbsp;msisdn="841388100XXX",<br>
&nbsp;&nbsp;&nbsp;&nbsp;from_date="2024-04-11",<br>
&nbsp;&nbsp;&nbsp;&nbsp;to_date="2024-04-12"<br>
)<br>
<strong>Tham số:</strong></span></p>

<div>
    <table border="1">
        <tbody>
            <tr>
                <td><p><span style="font-size:17px">Field</span></p></td>
                <td><p><span style="font-size:17px">Type</span></p></td>
                <td><p><span style="font-size:17px">Description</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">msisdn</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Số thuê bao di động cần tra cứu thông tin sử dụng</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">from_date</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Ngày bắt đầu của khoảng thời gian tìm kiếm</span></p></td>
            </tr>
            <tr>
                <td><p><span style="font-size:17px">to_date</span></p></td>
                <td><p><span style="font-size:17px">str</span></p></td>
                <td><p><span style="font-size:17px">Ngày kết thúc của khoảng thời gian tìm kiếm</span></p></td>
            </tr>
        </tbody>
    </table>
</div>

<h3 style="margin-left:36.0pt">
    <span style="font-size:17px">2.3.2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Contract Service</span>
</h3>

<h4 style="margin-left:43.2pt">
    <span style="font-size:17px">2.3.2.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lấy danh sách hợp đồng</span>
</h4>
<p><span style="font-size:17px">
response = client.contracts.get_list_contract(<br>
&nbsp;&nbsp;&nbsp;&nbsp;center_code="1",<br>
&nbsp;&nbsp;&nbsp;&nbsp;payment_name="Lâm Đồng",<br>
&nbsp;&nbsp;&nbsp;&nbsp;contact_phone="841276423698",<br>
&nbsp;&nbsp;&nbsp;&nbsp;contract_code="LDG-LD/00048943",<br>
&nbsp;&nbsp;&nbsp;&nbsp;customer_code="LDG01142173",<br>
&nbsp;&nbsp;&nbsp;&nbsp;customer_name="Công An",<br>
&nbsp;&nbsp;&nbsp;&nbsp;page=0,<br>
&nbsp;&nbsp;&nbsp;&nbsp;size=10<br>
)</span></p>

<h3 style="margin-left:36.0pt">
    <span style="font-size:17px">2.3.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Customer Service</span>
</h3>

<h4 style="margin-left:43.2pt">
    <span style="font-size:17px">2.3.3.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lấy danh sách khách hàng</span>
</h4>
<p><span style="font-size:17px">
response = client.customers.get_list_customer(<br>
&nbsp;&nbsp;&nbsp;&nbsp;customer_code="LDGCT00599332",<br>
&nbsp;&nbsp;&nbsp;&nbsp;customer_name="NGUYỄN CÔNG",<br>
&nbsp;&nbsp;&nbsp;&nbsp;customer_type=2,<br>
&nbsp;&nbsp;&nbsp;&nbsp;phone="84911179900",<br>
&nbsp;&nbsp;&nbsp;&nbsp;page=0,<br>
&nbsp;&nbsp;&nbsp;&nbsp;size=10<br>
)</span></p>

<h3 style="margin-left:36.0pt">
    <span style="font-size:17px">2.3.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Device Service</span>
</h3>

<h4 style="margin-left:43.2pt">
    <span style="font-size:17px">2.3.4.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Lấy danh sách thiết bị</span>
</h4>
<p><span style="font-size:17px">
response = client.devices.get_list_device(<br>
&nbsp;&nbsp;&nbsp;&nbsp;msisdn="84836819721",<br>
&nbsp;&nbsp;&nbsp;&nbsp;page=0,<br>
&nbsp;&nbsp;&nbsp;&nbsp;size=10<br>
)</span></p>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">2.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Xử lý lỗi</span>
</h2>
<p><span style="font-size:17px">SDK tự động xử lý việc làm mới token khi hết hạn và cung cấp cơ chế xử lý lỗi:</span></p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">try:<br>
    &nbsp;&nbsp;&nbsp;&nbsp;response = client.sims.get_sim_info("************")<br>
    &nbsp;&nbsp;&nbsp;&nbsp;print(response)<br>
    except Exception as e:<br>
    &nbsp;&nbsp;&nbsp;&nbsp;print(f"Lỗi xảy ra: {e}")</span>
</p>

<h2 style="margin-left:28.8pt">
    <span style="font-size:17px">2.5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Ví dụ hoàn chỉnh</span>
</h2>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">import json<br>
    from ism_sdk.client import ISMClient<br><br>
    # Cấu hình<br>
    base_url = "https://api-m2m.oneiot.com.vn/api"<br>
    client_id = "your_client_id"<br>
    secret_key = "your_secret_key"<br>
    user_id = "your_user_id"<br><br>
    # Khởi tạo client<br>
    client = ISMClient(<br>
    &nbsp;&nbsp;&nbsp;&nbsp;base_url=base_url,<br>
    &nbsp;&nbsp;&nbsp;&nbsp;client_id=client_id,<br>
    &nbsp;&nbsp;&nbsp;&nbsp;secret_key=secret_key<br>
    )<br><br>
    # Thiết lập user ID cho rate limiting<br>
    client.set_user_id(user_id)<br><br>
    try:<br>
    &nbsp;&nbsp;&nbsp;&nbsp;# Lấy danh sách SIM<br>
    &nbsp;&nbsp;&nbsp;&nbsp;sim_list = client.sims.get_list_sim_by_account(<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;page=0,<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;page_size=10<br>
    &nbsp;&nbsp;&nbsp;&nbsp;)<br>
    &nbsp;&nbsp;&nbsp;&nbsp;print(json.dumps(sim_list, indent=4, ensure_ascii=False))<br><br>
    &nbsp;&nbsp;&nbsp;&nbsp;# Lấy thông tin chi tiết SIM<br>
    &nbsp;&nbsp;&nbsp;&nbsp;sim_info = client.sims.get_sim_info("************")<br>
    &nbsp;&nbsp;&nbsp;&nbsp;print(json.dumps(sim_info, indent=4, ensure_ascii=False))<br><br>
    except Exception as e:<br>
    &nbsp;&nbsp;&nbsp;&nbsp;print(f"Lỗi xảy ra: {e}")</span>
</p>

<p><strong><span style="font-size:17px">Lưu ý:</span></strong></p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Thay thế các giá trị placeholder bằng thông tin thực tế</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; SDK hỗ trợ OAuth2 client credentials flow</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Tất cả endpoint đều yêu cầu header userid cho rate limiting</span>
</p>
<p style="margin-left:36.0pt">
    <span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; SDK tự động xử lý làm mới token khi cần thiết</span>
</p>

</body>
</html>
