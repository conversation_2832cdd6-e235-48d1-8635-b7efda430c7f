<p><br></p><h1 style="margin-left:21.6pt"><a name="_Toc408591019"></a><a name="_Toc467835953"></a><a name="_Toc6920885"></a><a name="_Toc7196329"></a><a name="_Toc7196709"></a><a name="_Toc7197986"></a><a name="_Toc132239525"></a><a name="_Toc204170617"></a><a name="_Toc204176486"><!--[if !supportLists]--><span style="font-size:17px">1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px"><PERSON><PERSON><PERSON><PERSON> thi<PERSON>u</span></a></h1><h2 style="margin-left:28.8pt"><a name="_Toc408591020"></a><a name="_Toc467835954"></a><a name="_Toc6920886"></a><a name="_Toc7196330"></a><a name="_Toc7196710"></a><a name="_Toc7197987"></a><a name="_Toc132239526"></a><a name="_Toc204170618"></a><a name="_Toc204176487"><!--[if !supportLists]--><span style="font-size:17px">1.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Mục đích tài liệu</span></a></h2><p><span style="font-size:17px">Tài liệu là User Guide cho các đối tượng liên quan đến hệ thống M2M được đưa ra với mục đích hướng dẫn người dùng sử dụng, vận hành và khai thác hệ thống M2M một cách hiệu quả và thiết thực.</span></p><h2 style="margin-left:28.8pt"><a name="_Toc467835955"></a><a name="_Toc6920887"></a><a name="_Toc7196331"></a><a name="_Toc7196711"></a><a name="_Toc7197988"></a><a name="_Toc132239527"></a><a name="_Toc204170619"></a><a name="_Toc204176488"><!--[if !supportLists]--><span style="font-size:17px">1.2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Phạm vi</span></a></h2><p><span style="font-size:17px">Tài liệu mô tả các chức năng của hệ thống cung cấp và các thao tác sử dụng , khai thác như bên dưới:</span></p><div><table><tbody><tr><td><p><strong><span style="font-size:17px">Mục</span></strong></p></td><td><p><strong><span style="font-size:17px">Nội   dung</span></strong></p></td></tr><tr><td><p><span style="font-size:17px">2.1</span></p></td><td><p><span style="font-size:17px">Môi trường yêu cầu, import thư viện</span></p></td></tr><tr><td><p><span style="font-size:17px">2.2</span></p></td><td><p><span style="font-size:17px">Khởi tạo</span></p></td></tr><tr><td><p><span style="font-size:17px">2.3</span></p></td><td><p><span style="font-size:17px">Danh sách các hàm</span></p></td></tr></tbody></table></div><h2 style="margin-left:28.8pt"><a name="_Toc408591022"></a><a name="_Toc467835956"></a><a name="_Toc6920888"></a><a name="_Toc7196332"></a><a name="_Toc7196712"></a><a name="_Toc7197989"></a><a name="_Toc132239528"></a><a name="_Toc204170620"></a><a name="_Toc204176489"><!--[if !supportLists]--><span style="font-size:17px">1.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Đối tượng</span></a></h2><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Người dùng cuối sử dụng M2M</span></p><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Người quản trị hệ thống, quản trị viên cấp cao</span></p><h2 style="margin-left:28.8pt"><a name="_Toc408591023"></a><a name="_Toc467835957"></a><a name="_Toc6920889"></a><a name="_Toc7196333"></a><a name="_Toc7196713"></a><a name="_Toc7197990"></a><a name="_Toc132239529"></a><a name="_Toc204170621"></a><a name="_Toc204176490"><!--[if !supportLists]--><span style="font-size:17px">1.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Tài liệu tham khảo</span></a></h2><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">N/A</span></p><p><strong><span style="font-size:17px"><br></span></strong></p><h1 style="margin-left:21.6pt"><a name="_Toc408591024"></a><a name="_Toc467835958"></a><a name="_Toc6920890"></a><a name="_Toc7196334"></a><a name="_Toc7196714"></a><a name="_Toc7197991"></a><a name="_Toc132239530"></a><a name="_Toc204170622"></a><a name="_Toc204176491"><!--[if !supportLists]--><span style="font-size:17px">2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Miêu tả chức năng</span></a></h1><h2 style="margin-left:28.8pt"><a name="_Toc204170623"></a><a name="_Toc204176492"></a><a name="_Toc132239531"><!--[if !supportLists]--><span style="font-size:17px">2.1&nbsp; &nbsp; &nbsp; &nbsp; </span><span style="font-size:17px">M</span></a><span style="font-size:17px">ôi trường yêu cầu, import thư viện</span></h2><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Môi trường yêu cầu java 1.8+</span></p><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Tải SDK sau đó giải nén ta được file cmp-sdk-2.5.jar</span></p><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Build thư viện local repository: Tại folder chứa file sdk sử dụng câu lệnh</span></p><p style="margin-left:36.0pt"><span style="font-size:17px">mvn install:install-file \</span></p><p style="margin-left:36.0pt"><span style="font-size:17px">-Dfile=cmp-sdk-2.5.jar \</span></p><p style="margin-left:36.0pt"><span style="font-size:17px">-DgroupId=vn.vnpt.cmp \</span></p><p style="margin-left:36.0pt"><span style="font-size:17px">-DartifactId=cmp-sdk \</span></p><p style="margin-left:36.0pt"><span style="font-size:17px">-Dversion=2.5 \</span></p><p style="margin-left:36.0pt"><span style="font-size:17px">-Dpackaging=jar</span></p><p style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">·&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Vào project maven bất kỳ, thêm vào trong thẻ dependencies </span></p><h2 style="margin-left:28.8pt"><a name="_Toc132239532"></a><a name="_Toc204170624"></a><a name="_Toc204176493"><!--[if !supportLists]--><span style="font-size:17px">2.2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Khởi tạo</span></a></h2><p><a name="_Toc459908079"></a><a name="_Toc462164008"></a><a name="_Toc467691144"><span style="font-size:17px">Bước 1: Để sử dụng sdk bạn phải khởi tạo class SDKConfiguration ( dùng để thêm các cấu hình config hệ thống và ứng dụng)</span></a><span style="font-size:17px">. Thông tin clientId và secretKey được lấy từ website</span></p><table><tbody><tr><td><p><a name="_Toc459908080"></a><a name="_Toc462164009"></a><a name="_Toc467691145"><strong><span style="font-size:17px;color:white">STT</span></strong></a></p></td><td><p><strong><span style="font-size:17px;color:white">Trường</span></strong></p></td><td><p><strong><span style="font-size:17px;color:white">Kiểu</span></strong></p></td><td><p><strong><span style="font-size:17px;color:white">Ý nghĩa</span></strong></p></td></tr><tr><td><div><p style="margin-left:18.0pt"><!--[if !supportLists]--><strong><span style="font-size:17px">1.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   </span></strong><!--[endif]--></p></div></td><td><p><span style="font-size:17px;color:black">clientId</span></p></td><td><p><span style="font-size:17px;color:black">String</span></p></td><td><p><span style="font-size:17px;color:black">Client Id được cấp trên web</span></p></td></tr><tr><td><div><p style="margin-left:18.0pt"><!--[if !supportLists]--><strong><span style="font-size:17px">2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   </span></strong><!--[endif]--></p></div></td><td><p><span style="font-size:17px">secretKey</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Secret Key được cấp trên web</span></p></td></tr></tbody></table><p><span style="font-size:17px">Bước 2: Khởi tạo đối tượng CmpClient sử dụng config trên.</span></p><h2 style="margin-left:28.8pt"><a name="_Toc204170625"></a><a name="_Toc204176494"><!--[if !supportLists]--><span style="font-size:17px">2.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Danh sách các hàm</span></a></h2><p>Resonse trả về luôn có trườngErrorCode<br><em>"errorCode": 0 thành công<br> "errorCode": 15 không có quyền truy cập <br> "errorCode": 99 lỗi hệ thống</em></p><h3 style="margin-left:36.0pt"><a name="_Toc204170626"></a><a name="_Toc204176495"><!--[if !supportLists]--><span style="font-size:17px">2.3.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Sim service</span></a></h3><p style="margin-left:43.2pt"><!--[if !supportLists]--><span style="font-size:15px">2.3.1.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Lấy danh sách sim theo mã khách hàng</span></p><p><span style="font-size:17px"><br> SearchSim simRequest = new SearchSim();<br> simRequest.setCustomerCode("LDGCT00599332");<br> response = cmpClient.getSimService().getListSimByAccount(simRequest);<br><br><a name="_Hlk204174107"><strong>SearchSim:</strong></a></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">page</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số trang</span></p></td></tr><tr><td><p><span style="font-size:17px">size</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số lượng bản ghi mỗi trang</span></p></td></tr></tbody></table></div><p><a name="_Hlk204174141"><strong><span style="font-size:17px">GetListSim:</span></strong></a></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">total</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Tổng số bản ghi</span></p></td></tr><tr><td><p><span style="font-size:17px">listSim</span></p></td><td><p><span style="font-size:17px">List&lt;ListSim&gt;</span></p></td><td><p><span style="font-size:17px">Danh sách thông tin SIM</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">ListSim:</span></strong></p><div><table><tbody><tr><td><p><a name="_Hlk204174623"><span style="font-size:17px">Field</span></a></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">msisdn</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số thuê bao</span></p></td></tr><tr><td><p><span style="font-size:17px">ratePlanName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên gói cước</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Short</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">imsi</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã IMSI</span></p></td></tr><tr><td><p><span style="font-size:17px">simGroupName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên nhóm SIM</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy thông tin chi tiết sim theo msisdn</span></h4><p><span style="font-size:17px"><br> response = cmpClient.getSimService().getMsimInfo(“841388109596”);<br><strong>MsimDetail:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">msisdn</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số thuê bao</span></p></td></tr><tr><td><p><span style="font-size:17px">dataUsed</span></p></td><td><p><span style="font-size:17px">Double</span></p></td><td><p><span style="font-size:17px">Dung lượng đã sử dụng</span></p></td></tr><tr><td><p><span style="font-size:17px">chargesIncurred</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Cước phát sinh</span></p></td></tr><tr><td><p><span style="font-size:17px">planName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên gói cước</span></p></td></tr><tr><td><p><span style="font-size:17px">apn</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên điểm truy cập</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">imsi</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã IMSI</span></p></td></tr><tr><td><p><span style="font-size:17px">simGroupName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên nhóm SIM</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractorInfo</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thông tin người ký</span></p></td></tr><tr><td><p><span style="font-size:17px">centerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã trung tâm</span></p></td></tr><tr><td><p><span style="font-size:17px">contactPhone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">SĐT liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">contactAddress</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên đơn vị thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentAddress</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">routeCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã tuyến</span></p></td></tr><tr><td><p><span style="font-size:17px">birthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh</span></p></td></tr><tr><td><p><span style="font-size:17px">state</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái kết nối</span></p></td></tr><tr><td><p><span style="font-size:17px">location</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Vị trí thiết bị</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->2.3.1.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy thông tin msisdn - imsi đã gán</span></h4><p><span style="font-size:17px"><br> String imsi = "452022166736xxx";<br> response = cmpClient.getSimService().getSimInfoToAssign(msisdn.toString(), imsi);<br><strong>ImsiAndMsisdn:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">msisdn</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số thuê bao</span></p></td></tr><tr><td><p><span style="font-size:17px">imsi</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã IMSI</span></p></td></tr><tr><td><p><span style="font-size:17px">planName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên gói cước</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy số lượng sim đang hoạt động theo tỉnh</span></h4><p style="text-align:left"><span style="font-size:17px"><br> response = cmpClient.getSimService().getNumSimByProvinceCode("HNI");</span></p><p><strong><span style="font-size:17px">SimNumber:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">int</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">message</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thông báo</span></p></td></tr><tr><td><p><span style="font-size:17px">currentDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày hiện tại</span></p></td></tr><tr><td><p><span style="font-size:17px">name</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên</span></p></td></tr><tr><td><p><span style="font-size:17px">unit</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Đơn vị</span></p></td></tr><tr><td><p><span style="font-size:17px">data</span></p></td><td><p><span style="font-size:17px">List</span></p></td><td><p><span style="font-size:17px">Danh sách SIM theo tỉnh</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->2.3.1.5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy thông tin data đã sử dụng trong một khoảng</span></h4><p style="text-align:left"><span style="font-size:17px">SearchUsageDataDetail usageRequest = new SearchUsageDataDetail(<br>"841388100XXX", "2024-04-11", "2024-04-12"<br> );<br> response = cmpClient.getSimService().getTerminalUsageDataDetails(usageRequest);</span></p><p><strong><span style="font-size:17px">SearchUsageDataDetail:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">msisdn</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số thuê bao di động cần tra cứu   thông tin sử dụng</span></p></td></tr><tr><td><p><span style="font-size:17px">fromDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày bắt đầu của khoảng thời gian   tìm kiếm</span></p></td></tr><tr><td><p><span style="font-size:17px">toDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày kết thúc của khoảng thời gian   tìm kiếm</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px"><br> UsageDataDetail:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">msisdn</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số thuê bao</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">dataUsed</span></p></td><td><p><span style="font-size:17px">Double</span></p></td><td><p><span style="font-size:17px">Dung lượng đã sử dụng</span></p></td></tr></tbody></table></div><h3 style="margin-left:36.0pt;text-align:left"><a name="_Toc204170627"></a><a name="_Toc204176496"><!--[if !supportLists]--><span style="font-size: 17px">2.3.2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Contract Service</span></a></h3><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->2.3.2.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy danh sách hợp đồng</span></h4><p><span style="font-size:17px">SearchContract contractRequest = new SearchContract();<br> contractRequest.setCenterCode("1");<br> contractRequest.setPaymentName(" Lâm Đồng");<br> contractRequest.setContactPhone("841276423698");<br> contractRequest.setContractCode("LDG-LD/00048943");<br> contractRequest.setCustomerCode("LDG01142173");<br> contractRequest.setCustomerName("Công An");<br> response = cmpClient.getContractService().getListContract(contractRequest);<br><strong>SearchContract:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">centerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã trung   tâm</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên đơn vị   thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCodeArray</span></p></td><td><p><span style="font-size:17px">List</span></p></td><td><p><span style="font-size:17px">Danh sách   mã khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contactPhone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractor</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Người ký hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">page</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số trang</span></p></td></tr><tr><td><p><span style="font-size:17px">size</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số lượng bản   ghi mỗi trang</span></p></td></tr><tr><td><p><span style="font-size:17px">sortBy</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Trường sắp   xếp</span></p></td></tr></tbody></table></div><p><span style="font-size:17px"><br><strong>ContractDto:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">id</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">ID của hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractor</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Người ký hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày ký hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">centerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã trung   tâm</span></p></td></tr><tr><td><p><span style="font-size:17px">contactPhone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">contactAddress</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ   liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên đơn vị   thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentAddress</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ   thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">routeCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã tuyến</span></p></td></tr><tr><td><p><span style="font-size:17px">birthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh   người ký</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy chi tiết hợp đồng</span></h4><p><span style="font-size:17px"><br> response = cmpClient.getContractService().getDetailContract("LDG-LD/00048943");<br><strong>ContractDto:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">id</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">ID của hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã hợp đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractor</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Người ký hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">contractDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày ký hợp   đồng</span></p></td></tr><tr><td><p><span style="font-size:17px">centerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã trung   tâm</span></p></td></tr><tr><td><p><span style="font-size:17px">contactPhone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">contactAddress</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ   liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên đơn vị   thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">paymentAddress</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ   thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">routeCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã tuyến</span></p></td></tr><tr><td><p><span style="font-size:17px">birthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh   người ký</span></p></td></tr></tbody></table></div><h3 style="margin-left:36.0pt"><a name="_Toc204170628"></a><a name="_Toc204176497"><!--[if !supportLists]--><span style="font-size:17px">2.3.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Customer Service</span></a></h3><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy danh sách khách hàng</span></h4><p><span style="font-size:17px">SearchCustomer customerRequest = new SearchCustomer();<br> customerRequest.setCustomerCode("LDGCT00599332");<br> customerRequest.setCustomerName("NGUYỄN CÔNG");<br> customerRequest.setCustomerType(2);<br> customerRequest.setPhone("84911179900");<br> response = cmpClient.getCustomerService().getListCustomer(customerRequest);<br><strong>SearchCustomer:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">phone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại</span></p></td></tr><tr><td><p><span style="font-size:17px">taxId</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã số thuế</span></p></td></tr><tr><td><p><span style="font-size:17px">email</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ   email</span></p></td></tr><tr><td><p><span style="font-size:17px">customerType</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">page</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số trang</span></p></td></tr><tr><td><p><span style="font-size:17px">size</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số bản ghi   mỗi trang</span></p></td></tr><tr><td><p><span style="font-size:17px">sortBy</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Trường sắp   xếp</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">CustomerDto:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Trạng thái   khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">taxId</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã số thuế</span></p></td></tr><tr><td><p><span style="font-size:17px">provinceCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã tỉnh/thành   phố</span></p></td></tr><tr><td><p><span style="font-size:17px">customerType</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Loại khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">phone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">email</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Email liên   hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">birthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh</span></p></td></tr><tr><td><p><span style="font-size:17px">billName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên trên   hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">billPhone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại nhận hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">billEmail</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Email nhận   hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">billBirthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh   trên hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">addrStreet</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ đường/phố</span></p></td></tr><tr><td><p><span style="font-size:17px">addrDistrict</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Quận/huyện</span></p></td></tr><tr><td><p><span style="font-size:17px">addrProvince</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tỉnh/thành   phố</span></p></td></tr><tr><td><p><span style="font-size:17px">note</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ghi chú</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy chi tiết khách hàng</span></h4><p><span style="font-size:17px"><br> response = cmpClient.getCustomerService().getDetailCustomer(2624682L);</span></p><p><strong><span style="font-size:17px">CustomerDto:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">customerCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Trạng thái   khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">taxId</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã số thuế</span></p></td></tr><tr><td><p><span style="font-size:17px">provinceCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã tỉnh/thành   phố</span></p></td></tr><tr><td><p><span style="font-size:17px">customerType</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Loại khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">phone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại liên hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">email</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Email liên   hệ</span></p></td></tr><tr><td><p><span style="font-size:17px">birthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh</span></p></td></tr><tr><td><p><span style="font-size:17px">billName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên trên   hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">billPhone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại nhận hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">billEmail</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Email nhận   hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">billBirthday</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày sinh   trên hóa đơn</span></p></td></tr><tr><td><p><span style="font-size:17px">addrStreet</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Địa chỉ đường/phố</span></p></td></tr><tr><td><p><span style="font-size:17px">addrDistrict</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Quận/huyện</span></p></td></tr><tr><td><p><span style="font-size:17px">addrProvince</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tỉnh/thành   phố</span></p></td></tr><tr><td><p><span style="font-size:17px">note</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ghi chú</span></p></td></tr></tbody></table></div><h3 style="margin-left:36.0pt"><a name="_Toc204170629"></a><a name="_Toc204176498"><!--[if !supportLists]--><span style="font-size:17px">2.3.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Wallet Service</span></a></h3><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->2.3.4.1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Tìm kiếm ví lưu lượng theo từ khóa</span></h4><p><span style="font-size:17px">SearchWallet searchWallet = new SearchWallet("9cntt");<br> cmpClient.getWalletService().getListTrafficWallet(searchWallet);</span></p><p><strong><span style="font-size:17px">SearchWallet:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">size</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số lượng bản   ghi mỗi trang (mặc định: 10)</span></p></td></tr><tr><td><p><span style="font-size:17px">page</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số trang   (mặc định: 0)</span></p></td></tr><tr><td><p><span style="font-size:17px">searchWalletCode</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Tìm theo   mã ví (mặc định: 1)</span></p></td></tr><tr><td><p><span style="font-size:17px">searchPayCode</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Tìm theo   mã thanh toán (mặc định: 1)</span></p></td></tr><tr><td><p><span style="font-size:17px">value</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Giá trị   tìm kiếm</span></p></td></tr><tr><td><p><span style="font-size:17px">searchName</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Tìm theo   tên (mặc định: 1)</span></p></td></tr><tr><td><p><span style="font-size:17px">searchPhone</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Tìm theo   SĐT (mặc định: 1)</span></p></td></tr><tr><td><p><span style="font-size:17px">accuracyStartDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày bắt đầu   chính xác</span></p></td></tr><tr><td><p><span style="font-size:17px">accuracyEndDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Ngày kết   thúc chính xác</span></p></td></tr><tr><td><p><span style="font-size:17px">autoType</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại tự động   (mặc định: -1)</span></p></td></tr><tr><td><p><span style="font-size:17px">searchPackageName</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Tìm theo   tên gói (mặc định: 1)</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">WalletSearch:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">id</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">ID ví</span></p></td></tr><tr><td><p><span style="font-size:17px">purchasedTraffic</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Lưu lượng   đã mua</span></p></td></tr><tr><td><p><span style="font-size:17px">subName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên thuê   bao</span></p></td></tr><tr><td><p><span style="font-size:17px">totalTrafficShared</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Tổng lưu   lượng đã chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">payCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã thanh   toán</span></p></td></tr><tr><td><p><span style="font-size:17px">trafficType</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Loại lưu   lượng</span></p></td></tr><tr><td><p><span style="font-size:17px">startDate</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Ngày bắt đầu</span></p></td></tr><tr><td><p><span style="font-size:17px">endDate</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Ngày kết   thúc</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách   hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">totalRemainingTraffic</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Tổng lưu   lượng còn lại</span></p></td></tr><tr><td><p><span style="font-size:17px">tax</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã số thuế</span></p></td></tr><tr><td><p><span style="font-size:17px">time</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Thời gian</span></p></td></tr><tr><td><p><span style="font-size:17px">subCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã thuê   bao</span></p></td></tr><tr><td><p><span style="font-size:17px">phoneActive</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">SĐT kích   hoạt</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">accuracyDate</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Ngày chính   xác</span></p></td></tr><tr><td><p><span style="font-size:17px">packageName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên gói cước</span></p></td></tr><tr><td><p><span style="font-size:17px">canShare</span></p></td><td><p><span style="font-size:17px">Boolean</span></p></td><td><p><span style="font-size:17px">Có thể   chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">timeUsed</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thời gian   sử dụng</span></p></td></tr><tr><td><p><span style="font-size:17px">autoType</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại tự động</span></p></td></tr><tr><td><p><span style="font-size:17px">dayExprired</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Số ngày hết   hạn</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><a name="_Hlk204163846"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy chi tiết ví traffic</span></a></h4><p><span style="font-size:17px"><br> response = cmpClient.getWalletService().getDetailTrafficWallet("001214409Cxxx");<br><strong>TrafficDetailDto:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">purchasedTraffic</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Lưu lượng đã mua</span></p></td></tr><tr><td><p><span style="font-size:17px">subName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên thuê bao</span></p></td></tr><tr><td><p><span style="font-size:17px">totalTrafficShared</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Tổng lưu lượng đã chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">payCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã thanh toán</span></p></td></tr><tr><td><p><span style="font-size:17px">trafficType</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Loại lưu lượng</span></p></td></tr><tr><td><p><span style="font-size:17px">endDate</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Ngày kết thúc</span></p></td></tr><tr><td><p><span style="font-size:17px">customerName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên khách hàng</span></p></td></tr><tr><td><p><span style="font-size:17px">totalRemainingTraffic</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Tổng lưu lượng còn lại</span></p></td></tr><tr><td><p><span style="font-size:17px">tax</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã số thuế</span></p></td></tr><tr><td><p><span style="font-size:17px">startDate</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Ngày bắt đầu</span></p></td></tr><tr><td><p><span style="font-size:17px">time</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Thời gian</span></p></td></tr><tr><td><p><span style="font-size:17px">subCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã thuê bao</span></p></td></tr><tr><td><p><span style="font-size:17px">phoneActive</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">SĐT kích hoạt</span></p></td></tr><tr><td><p><span style="font-size:17px">packageName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên gói cước</span></p></td></tr><tr><td><p><span style="font-size:17px">listShared</span></p></td><td><p><span style="font-size:17px">List</span></p></td><td><p><span style="font-size:17px">Danh sách chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">canShare</span></p></td><td><p><span style="font-size:17px">Boolean</span></p></td><td><p><span style="font-size:17px">Có thể chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">autoType</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại tự động</span></p></td></tr><tr><td><p><span style="font-size:17px">dayExprired</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số ngày hết hạn</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->2.3.4.3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Lấy lịch sử hoạt động của ví</span></h4><p><span style="font-size:17px"><br> SearchListActivityHistory searchHistory = new SearchListActivityHistory();<br> searchHistory.setPage(10);<br> searchHistory.setSize(10);<br> searchHistory.setStatus(1);<br> searchHistory.setFromDate("11/04/2025");<br> searchHistory.setToDate("12/04/2025");<br> searchHistory.setWalletCode("001214409CNTT");<br> response = cmpClient.getWalletService().getWalletHistory(searchHistory);<br><strong>SearchListActivityHistory:</strong></span></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">fromDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thời điểm bắt đầu tìm kiếm lịch sử</span></p></td></tr><tr><td><p><span style="font-size:17px">toDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thời điểm kết thúc tìm kiếm lịch sử</span></p></td></tr><tr><td><p><span style="font-size:17px">walletCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã ví cần tra cứu lịch sử</span></p></td></tr><tr><td><p><span style="font-size:17px">page</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số trang hiện tại</span></p></td></tr><tr><td><p><span style="font-size:17px">size</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Số lượng bản ghi trên mỗi trang</span></p></td></tr><tr><td><p><span style="font-size:17px">sortBy</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Trường sắp xếp dữ liệu</span></p></td></tr><tr><td><p><span style="font-size:17px">activeType</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại hoạt động</span></p></td></tr><tr><td><p><span style="font-size:17px">typeShare</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái của hoạt động</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">TrafficActivityLogList:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">id</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">ID của log hoạt động</span></p></td></tr><tr><td><p><span style="font-size:17px">activeType</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại hoạt động</span></p></td></tr><tr><td><p><span style="font-size:17px">createdDate</span></p></td><td><p><span style="font-size:17px">Date</span></p></td><td><p><span style="font-size:17px">Ngày tạo</span></p></td></tr><tr><td><p><span style="font-size:17px">content</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Nội dung hoạt động</span></p></td></tr><tr><td><p><span style="font-size:17px">createdName</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Tên người tạo</span></p></td></tr><tr><td><p><span style="font-size:17px">email</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Email người thực hiện</span></p></td></tr><tr><td><p><span style="font-size:17px">phoneNumber</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện thoại</span></p></td></tr><tr><td><p><span style="font-size:17px">typeShare</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">walletCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã ví</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">dataResp</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Dữ liệu phản hồi</span></p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->2.3.4.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]--><span style="font-size:17px">Chia sẻ lưu lượng từ ví tới thuê bao</span></h4><p><span style="font-size:17px"><br> List&lt;TrafficShare.ShareInfo&gt; shareInfoList = new ArrayList&lt;&gt;();<br> shareInfoList.add(new TrafficShare.ShareInfo("84700054xxx", 100L));<br> shareInfoList.add(new TrafficShare.ShareInfo("84412341xxx", 100L));<br> shareInfoList.add(new TrafficShare.ShareInfo("84412343xxx", 100L));<br> TrafficShare trafficShare = new TrafficShare("001214409Cxxx", shareInfoList);<br> response = cmpClient.getWalletService().shareTraffic(trafficShare);</span></p><p><strong><span style="font-size:17px">TrafficShare:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">subCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã ví chia   sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">shareInfos</span></p></td><td><p><span style="font-size:17px">List&lt;ShareInfo&gt;</span></p></td><td><p><span style="font-size:17px">Danh sách   thông tin chia sẻ</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">TrafficShare.ShareInfo:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">phone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại nhận</span></p></td></tr><tr><td><p><span style="font-size:17px">trafficType</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Loại lưu   lượng (mặc định: "DATA")</span></p></td></tr><tr><td><p><span style="font-size:17px">numOfShare</span></p></td><td><p><span style="font-size:17px">Long</span></p></td><td><p><span style="font-size:17px">Số lượng   chia sẻ</span></p></td></tr><tr><td><p><span style="font-size:17px">expireDate</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thời hạn   (mặc định: "30 ngày")</span></p></td></tr><tr><td><p><span style="font-size:17px">type</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Loại (mặc   định: 0)</span></p></td></tr><tr><td><p><span style="font-size:17px">isAuto</span></p></td><td><p><span style="font-size:17px">Boolean</span></p></td><td><p><span style="font-size:17px">Tự động (mặc   định: false)</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">TrafficShareDHSXKDDto:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">error</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã lỗi</span></p></td></tr><tr><td><p><span style="font-size:17px">errorCode</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Mã lỗi chi   tiết</span></p></td></tr><tr><td><p><span style="font-size:17px">message</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Thông báo</span></p></td></tr><tr><td><p><span style="font-size:17px">messageDetail</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Chi tiết   thông báo</span></p></td></tr><tr><td><p><span style="font-size:17px">requestId</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">ID yêu cầu</span></p></td></tr><tr><td><p><span style="font-size:17px">data</span></p></td><td><p><span style="font-size:17px">List</span></p></td><td><p><span style="font-size:17px">Danh sách   lỗi</span></p></td></tr></tbody></table></div><p><strong><span style="font-size:17px">TrafficShareDHSXKDDto.DataError:</span></strong></p><div><table><tbody><tr><td><p><span style="font-size:17px">Field</span></p></td><td><p><span style="font-size:17px">Type</span></p></td><td><p><span style="font-size:17px">Description</span></p></td></tr><tr><td><p><span style="font-size:17px">phone</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Số điện   thoại</span></p></td></tr><tr><td><p><span style="font-size:17px">status</span></p></td><td><p><span style="font-size:17px">Integer</span></p></td><td><p><span style="font-size:17px">Trạng thái</span></p></td></tr><tr><td><p><span style="font-size:17px">error</span></p></td><td><p><span style="font-size:17px">String</span></p></td><td><p><span style="font-size:17px">Lỗi</span></p></td></tr></tbody></table></div><h3 style="margin-left:36.0pt"><!--[if !supportLists]--><span style="font-size:17px">2.3.5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Device Service</span></h3><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]-->Lấy danh sách thiết bị<br><!--[if !supportLineBreakNewLine]--><br><!--[endif]--></h4><p><strong>SearchDevice:</strong></p><div><table><tbody><tr><td><p>Field</p></td><td><p>Type</p></td><td><p>Description</p></td></tr><tr><td><p>imei</p></td><td><p>String</p></td><td><p>Mã định danh thiết bị</p></td></tr><tr><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao gắn với thiết bị</p></td></tr><tr><td><p>country</p></td><td><p>String</p></td><td><p>Quốc gia của thiết bị</p></td></tr><tr><td><p>deviceType</p></td><td><p>String</p></td><td><p>Loại thiết bị</p></td></tr><tr><td><p>expiredDateFrom</p></td><td><p>String</p></td><td><p>Ngày hết hạn từ</p></td></tr><tr><td><p>expiredDateTo</p></td><td><p>String</p></td><td><p>Ngày hết hạn đến</p></td></tr><tr><td><p>page</p></td><td><p>Integer</p></td><td><p>Số trang</p></td></tr><tr><td><p>size</p></td><td><p>Integer</p></td><td><p>Số bản ghi mỗi trang</p></td></tr><tr><td><p>sort</p></td><td><p>String</p></td><td><p>Trường sắp xếp</p></td></tr></tbody></table></div><p><strong>DeviceDto:</strong></p><div><table><tbody><tr><td><p>Field</p></td><td><p>Type</p></td><td><p>Description</p></td></tr><tr><td><p>msisdn</p></td><td><p>Long</p></td><td><p>Số thuê bao</p></td></tr><tr><td><p>expiredDate</p></td><td><p>Date</p></td><td><p>Ngày hết hạn</p></td></tr><tr><td><p>deviceType</p></td><td><p>String</p></td><td><p>Loại thiết bị</p></td></tr><tr><td><p>country</p></td><td><p>String</p></td><td><p>Quốc gia</p></td></tr><tr><td><p>location</p></td><td><p>String</p></td><td><p>Vị trí</p></td></tr><tr><td><p>imei</p></td><td><p>String</p></td><td><p>Mã IMEI</p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]-->Tạo mới thiết bị<br><!--[if !supportLineBreakNewLine]--><br><!--[endif]--></h4><p style="text-align:left"><span style="color:black">DeviceDetailDto deviceDetailDto </span><span style="color:#080808">= </span><span style="color:#0033B3">new </span><span style="color:#080808">DeviceDetailDto();<br></span><span style="color:black">deviceDetailDto</span><span style="color:#080808">.setImei(</span><span style="color:#067D17">"4567854903"</span><span style="color:#080808">);<br></span><span style="color:black">deviceDetailDto</span><span style="color:#080808">.setMsisdn(</span><span style="color:#1750EB">841388100</span><span style="color:#080808">xxxL);<br></span><span style="color:black">cmpClient</span><span style="color:#080808">.getDeviceService().createDevice(</span><span style="color:black">deviceDetailDto</span><span style="color:#080808">);</span></p><p><strong>DeviceDetailDto:</strong></p><div><table><tbody><tr><td><p>Field</p></td><td><p>Type</p></td><td><p>Description</p></td></tr><tr><td><p>msisdn</p></td><td><p>Long</p></td><td><p>Số thuê bao</p></td></tr><tr><td><p>iotLink</p></td><td><p>Integer</p></td><td><p>Liên kết IoT</p></td></tr><tr><td><p>expiredDate</p></td><td><p>Date</p></td><td><p>Ngày hết hạn</p></td></tr><tr><td><p>deviceType</p></td><td><p>String</p></td><td><p>Loại thiết bị</p></td></tr><tr><td><p>country</p></td><td><p>String</p></td><td><p>Quốc gia</p></td></tr><tr><td><p>imei</p></td><td><p>String</p></td><td><p>Mã IMEI</p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]-->Chi tiết thiết bị<br><!--[if !supportLineBreakNewLine]--><br><!--[endif]--></h4><p style="text-align:left"><span style="color:black">cmpClient</span><span style="color:#080808">.getDeviceService().getDetailDevice(</span><span style="color:#067D17">"84138810xxx"</span><span style="color:#080808">);</span></p><p><strong>DeviceDetailDto:</strong></p><div><table><tbody><tr><td><p>Field</p></td><td><p>Type</p></td><td><p>Description</p></td></tr><tr><td><p>msisdn</p></td><td><p>Long</p></td><td><p>Số thuê bao</p></td></tr><tr><td><p>iotLink</p></td><td><p>Integer</p></td><td><p>Liên kết IoT</p></td></tr><tr><td><p>expiredDate</p></td><td><p>Date</p></td><td><p>Ngày hết hạn</p></td></tr><tr><td><p>deviceType</p></td><td><p>String</p></td><td><p>Loại thiết bị</p></td></tr><tr><td><p>country</p></td><td><p>String</p></td><td><p>Quốc gia</p></td></tr><tr><td><p>imei</p></td><td><p>String</p></td><td><p>Mã IMEI</p></td></tr></tbody></table></div><h4 style="margin-left:43.2pt"><!--[if !supportLists]-->*******&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <!--[endif]-->Cập nhật thiết bị<br><!--[if !supportLineBreakNewLine]--><br><!--[endif]--></h4><p style="text-align:left"><span style="color:black">DetailResponse</span><span style="color:#080808">&lt;</span><span style="color:black">DeviceDetailDto</span><span style="color:#080808">&gt; </span><span style="color:black">dto </span><span style="color:#080808">= </span><span style="color:black">cmpClient</span><span style="color:#080808">.getDeviceService().getDetailDevice(</span><span style="color:#067D17">"84138810xxx"</span><span style="color:#080808">);<br></span><span style="color:#0033B3">if </span><span style="color:#080808">(</span><span style="color:black">dto</span><span style="color:#080808">.getErrorCode() == </span><span style="color:#1750EB">0</span><span style="color:#080808">) {<br></span><span style="color:black">ObjectMapper mapper </span><span style="color:#080808">= </span><span style="color:#0033B3">new </span><span style="color:#080808">ObjectMapper();<br></span><span style="color:black">DeviceDetailDto detail </span><span style="color:#080808">= </span><span style="color:black">mapper</span><span style="color:#080808">.convertValue(</span><span style="color:black">dto</span><span style="color:#080808">.getDetail(), </span><span style="color:black">DeviceDetailDto</span><span style="color:#080808">.</span><span style="color:#0033B3">class</span><span style="color:#080808">);<br></span><span style="color:black">detail</span><span style="color:#080808">.setImei(</span><span style="color:#067D17">"4567854xxx"</span><span style="color:#080808">);<br></span><span style="color:black">cmpClient</span><span style="color:#080808">.getDeviceService().updateDevice(</span><span style="color:black">detail</span><span style="color:#080808">);<br> }</span></p><p><strong>DeviceDetailDto:</strong></p><div><table><tbody><tr><td><p>Field</p></td><td><p>Type</p></td><td><p>Description</p></td></tr><tr><td><p>msisdn</p></td><td><p>Long</p></td><td><p>Số thuê bao</p></td></tr><tr><td><p>iotLink</p></td><td><p>Integer</p></td><td><p>Liên kết IoT</p></td></tr><tr><td><p>expiredDate</p></td><td><p>Date</p></td><td><p>Ngày hết hạn</p></td></tr><tr><td><p>deviceType</p></td><td><p>String</p></td><td><p>Loại thiết bị</p></td></tr><tr><td><p>country</p></td><td><p>String</p></td><td><p>Quốc gia</p></td></tr><tr><td><p>imei</p></td><td><p>String</p></td><td><p>Mã IMEI</p></td></tr></tbody></table></div><h2 style="margin-left:28.8pt"><a name="_Toc204176499"><!--[if !supportLists]--><span style="font-size:17px">2.4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><!--[endif]--><span style="font-size:17px">Ngoại lệ</span></a></h2><p>Khi HTTP không trả về status 200<br><em>@throws IOException </em></p><p style="margin-left:36.0pt"><em>HTTP 401 Không có quyền truy cập<br> HTTP 404 Không tồn tại api này <br> HTTP 422 Gửi quá nhiều yêu cầu liên tục</em></p><p><em><br> @throws AuthenticationException</em></p><p><em>HTTP 404 clientId/secretKey không chính xác/không hoạt động</em></p>