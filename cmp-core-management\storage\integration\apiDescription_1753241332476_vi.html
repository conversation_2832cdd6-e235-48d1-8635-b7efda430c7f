<p><span style="font-size: 16px"><strong>Nhóm Public API về thuê bao</strong></span></p><p><span style="font-size: 14px"><strong>1. GetListSimByAccount</strong></span></p><p><span style="font-size: 14px">- <strong><PERSON><PERSON><PERSON> đích sử dụng</strong>: Là API được đối tác sử dụng để lấy danh sách simcard theo tài khoản xác thực</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListSimByAccount?page=1&amp;pageSize=10</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Brearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>contractCode</p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>page</p></td><td><p>String</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>pageSize</p></td><td><p>String</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr></tbody></table><p><span style="font-size: 14px">​</span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>listSim</p></td><td><p>Json array</p></td><td><p>Danh sách thiết bị. Trong mỗi bản ghi chứa các trường từ 5 à 12</p></td><td><p>Y</p></td></tr><tr><td><p>5</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>ratePlanName</p></td><td><p>String</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr><tr><td><p style="text-align:left">7</p></td><td><p style="text-align:left">status</p></td><td><p style="text-align:left">Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>imsi</p></td><td><p>String</p></td><td><p>imsi</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>simGroupName</p></td><td><p>String</p></td><td><p>Nhóm sim</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>customerName</p></td><td><p>String</p></td><td><p>Tên khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>11</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>12</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr></tbody></table><p><!--[if !supportLists]--><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table><tbody><tr><td><p><span style="font-size: 16px;background-color: transparent">&nbsp;</span><span style="color: black;font-family: Courier New;font-size: 12px;background-color: transparent">{</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">0</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorDesc"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"SUCCESS"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"total"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">22</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"listSim"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;[</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"msisdn"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"8482*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"ratePlanName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"status"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">1</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"imsi"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"45*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"simGroupName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;]</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">}</span></p>​</td></tr></tbody></table><p style="margin-left:.25in;text-align:left"><br><br></p><p><span style="font-size: 14px"><strong>2. GetSimInfo</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là api cho phép lấy thông tin chi tiết của sim</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getSimInfo?msisdn=84823384832</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Brearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong><br></strong></span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><p><br></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>dataUsed</p></td><td><p>Numbẻ</p></td><td><p>Dung lượng sử dụng</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>chargesIncurred</p></td><td><p>String</p></td><td><p>Cước phát sinh</p></td><td><p>N</p></td></tr><tr><td><p style="text-align:left">6</p></td><td><p style="text-align:left">planName</p></td><td><p style="text-align:left">Number</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>apn</p></td><td><p>String</p></td><td><p>apn</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>status</p></td><td><p>Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>imsi</p></td><td><p>String</p></td><td><p>imsi</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>simGroupName</p></td><td><p>String</p></td><td><p>Nhóm sim</p></td><td><p>N</p></td></tr><tr><td><p>11</p></td><td><p style="text-align:left"><span style="color:black">customerName</span></p></td><td><p>String</p></td><td><p>Tên khách hang</p></td><td><p>N</p></td></tr><tr><td><p>12</p></td><td><p style="text-align:left"><span style="color:black">customerCode</span></p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>13</p></td><td><p style="text-align:left"><span style="color:black">contractCode</span></p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>14</p></td><td><p style="text-align:left"><span style="color:black">contractDate</span></p></td><td><p>Date</p></td><td><p>Ngày làm hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>15</p></td><td><p style="text-align:left"><span style="color:black">contractorInfo</span></p></td><td><p>String</p></td><td><p>Người làm hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>16</p></td><td><p style="text-align:left"><span style="color:black">centerCode</span></p></td><td><p>String</p></td><td><p>Mã trung tâm</p></td><td><p>N</p></td></tr><tr><td><p>17</p></td><td><p style="text-align:left"><span style="color:black">contactPhone</span></p></td><td><p>String</p></td><td><p>Số điện thoại liên hệ</p></td><td><p>N</p></td></tr><tr><td><p>18</p></td><td><p style="text-align:left"><span style="color:black">contactAddress</span></p></td><td><p>String</p></td><td><p>Địa chỉ liên hệ</p></td><td><p>N</p></td></tr><tr><td><p>19</p></td><td><p style="text-align:left"><span style="color:black">paymentName</span></p></td><td><p>String</p></td><td><p>Tên thanh toán</p></td><td><p>N</p></td></tr><tr><td><p>20</p></td><td><p style="text-align:left"><span style="color:black">paymentAddress</span></p></td><td><p>String</p></td><td><p>Địa chỉ thanh toán</p></td><td><p>N</p></td></tr><tr><td><p>21</p></td><td><p style="text-align:left"><span style="color:black">routeCode</span></p></td><td><p>String</p></td><td><p>Mã tuyến đường</p></td><td><p>N</p></td></tr><tr><td><p>22</p></td><td><p style="text-align:left"><span style="color:black">birthday</span></p></td><td><p>Date</p></td><td><p>Sinh nhật khách hàng</p></td><td><p>N</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table class="se-table-size-100 se-table-layout-auto"><tbody><tr><td><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">{</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#098658">0</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"errorDesc"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"SUCCESS"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"msisdn"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"84*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"dataUsed"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"chargesIncurred"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"planName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"apn"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"status"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"imsi"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"simGroupName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">null</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"customerCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractDate"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contractorInfo"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"centerCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"3"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contactPhone"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"84125*******"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"contactAddress"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"Số&nbsp;82&nbsp;Chùa&nbsp;Láng&nbsp;(số&nbsp;38&nbsp;cũ)&nbsp;phố&nbsp;Chùa&nbsp;Láng&nbsp;&nbsp;-&nbsp;Láng&nbsp;Thượng&nbsp;-&nbsp;Đống&nbsp;Đa&nbsp;-&nbsp;Hà&nbsp;Nội"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"paymentName"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"Công&nbsp;ty&nbsp;cổ&nbsp;phần&nbsp;tư&nbsp;vấn&nbsp;đầu&nbsp;tư&nbsp;và&nbsp;xây&nbsp;dựng&nbsp;bưu&nbsp;điện"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"paymentAddress"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"Số&nbsp;82&nbsp;Chùa&nbsp;Láng&nbsp;(số&nbsp;38&nbsp;cũ)&nbsp;phố&nbsp;Chùa&nbsp;Láng&nbsp;&nbsp;-&nbsp;Láng&nbsp;Thượng&nbsp;-&nbsp;Đống&nbsp;Đa&nbsp;-&nbsp;Hà&nbsp;Nội"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"routeCode"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"PNT000-99"</span><span style="font-size:12px;font-family:Courier New;color:black">,</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#A31515">"birthday"</span><span style="font-size:12px;font-family:Courier New;color:black">:&nbsp;</span><span style="font-size:12px;font-family:Courier New;color:#0451A5">"1998-08-14"</span></p><p style="margin-left:.25in;text-align:left"><span style="font-size:12px;font-family:Courier New;color:black">}</span></p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>3. GetSimInfoToAssign</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là API lấy danh sách số msisdn ứng với imsi đã gán</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getSimInfoToAssign?msisdn=84815147332&amp;imsi=452021163974144</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Brearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color: black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>​<span style="font-size: 14px">msisdn</span>​</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>​<span style="font-size: 14px">imsi</span>​</p></td><td><p>String</p></td><td><p>Số sim</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px">​</span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color: black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin sim</p></td><td><p>Y</p></td></tr><tr><td><p>5</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>​<span style="font-size: 14px">imsi</span>​</p></td><td><p>String</p></td><td><p>Số sim</p></td><td><p>N</p></td></tr><tr><td><p style="text-align: left">7</p></td><td><p style="text-align: left">status</p></td><td><p style="text-align: left">Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>planName</p></td><td><p>String</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"msisdn": "841388100419",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"imsi": "452021146247242",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"planName": "EZ35NEW",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"status": 2<br>&nbsp;&nbsp;&nbsp;&nbsp;}<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>4. CountSimByProvinceCode</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là API lấy thông tin số lượng sim hoạt động trong 1 tỉnh</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/countSimByProvinceCode?provinceCode=HNI</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Brearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color: black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>​<span style="font-size: 14px">provinceCode</span>​</p></td><td><p>String</p></td><td><p>Mã tỉnh</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px">​</span></p><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color: black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>status</p></td><td><p>Number</p></td><td><p>Mã lỗi.</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>message</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>currentDate</p></td><td><p>Date</p></td><td><p>Ngày request</p></td><td><p>Y</p></td></tr><tr><td><p>5</p></td><td><p>name</p></td><td><p>String</p></td><td><p>M2M SIM</p></td><td><p>Y</p></td></tr><tr><td><p>6</p></td><td><p>​<span style="font-size: 14px">unit</span>​</p></td><td><p>String</p></td><td><p>Thue bao</p></td><td><p>Y<br></p></td></tr><tr><td><p style="text-align: left">7</p></td><td><p style="text-align: left">data</p></td><td><p style="text-align: left">Json</p></td><td><p><br></p></td><td><p>Y<br></p></td></tr><tr><td><div>8</div></td><td><div>MaTinh<br></div></td><td><div>String<br></div></td><td><div>Mã tỉnh</div></td><td><div>Y<br></div></td></tr><tr><td><p>9</p></td><td><p>SoLuong</p></td><td><p>String</p></td><td><p>Số lượng thuê bao</p></td><td><p>Y<br></p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><p><br></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"status": 1,<br>&nbsp;&nbsp;&nbsp;&nbsp;"message": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"currentDate": "2025-04-09",<br>&nbsp;&nbsp;&nbsp;&nbsp;"name": "M2M SIM",<br>&nbsp;&nbsp;&nbsp;&nbsp;"unit": "Thue bao",<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"MaTinh": "HNI",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"SoLuong": 442877<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;]<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>5. GetSimInfoToImport</strong></span></p><p><span style="font-size: 14px">-<strong>Mục đích sử dụng</strong>: Là API lấy thông tin sim để import theo danh sách IMSI</span></p><p><span style="font-size: 14px">-<strong>Phương thức và URL</strong>:<strong>POST&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getSimInfoToImport</span></p><p><span style="font-size: 14px">-<strong>Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align: left"><strong><span style="color: black">Headers</span></strong></p></td></tr><tr><td><p style="text-align: left"><strong>STT</strong></p></td><td><p style="text-align: left"><strong>Tên tham số</strong></p></td><td><p style="text-align: left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align: left"><strong>Mô tả</strong></p></td><td><p style="text-align: left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align: left">1</p></td><td><p style="text-align: left">Authorization</p></td><td><div><br></div></td><td><p style="text-align: left">Sử dụng Bearer token<br>Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align: left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color: black">Request Body</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>listImsi</p></td><td><p>Array[String]</p></td><td><p>Danh sách IMSI</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color: black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Array[Json]</p></td><td><p>Danh sách thông tin sim</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>imsi</p></td><td><p>String</p></td><td><p>Số sim</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>status</p></td><td><p>Number</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>planName</p></td><td><p>String</p></td><td><p>Tên gói cước</p></td><td><p>N</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"msisdn": "841388100419",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"imsi": "452021146247242",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"planName": "EZ35NEW",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"status": 2<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;]<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 16px"><strong>Nhóm Public API về thiết bị</strong></span></p><p><span style="font-size: 14px"><strong>6. GetListDevice</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API được đối tác sử dụng để lấy danh sách thiết bị</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListDevice?page=0&amp;size=10</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>imei</p></td><td><p>String</p></td><td><p>Mã IMEI thiết bị</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>location</p></td><td><p>String</p></td><td><p>Vị trí</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>country</p></td><td><p>String</p></td><td><p>Quốc gia</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>deviceType</p></td><td><p>String</p></td><td><p>Loại thiết bị</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>contractDateFrom</p></td><td><p>Long</p></td><td><p>Ngày hết hạn từ</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>contractDateTo</p></td><td><p>Long</p></td><td><p>Ngày hết hạn đến</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>page</p></td><td><p>Integer</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>size</p></td><td><p>Integer</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>sort</p></td><td><p>String</p></td><td><p>Sắp xếp</p></td><td><p>N</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>data</p></td><td><p>Json array</p></td><td><p>Danh sách thiết bị</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"total": 10,<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"msisdn": "84823384832",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"imei": "123456789012345",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"location": "Hanoi",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"country": "Vietnam",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"deviceType": "IoT Device"<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;]<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>7. GetDetailDevice</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy thông tin chi tiết của thiết bị</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getDetailDevice?msisdn=84823384832</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>msisdn</p></td><td><p>Long</p></td><td><p>Số thuê bao</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin chi tiết thiết bị</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"msisdn": "84823384832",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"imei": "123456789012345",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"location": "Hanoi",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"country": "Vietnam",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"deviceType": "IoT Device"<br>&nbsp;&nbsp;&nbsp;&nbsp;}<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>8. CreateDevice</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API tạo mới thiết bị</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>POST&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/createDevice</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request Body</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>entity</p></td><td><p>ThirdPartyDeviceDetailDTO</p></td><td><p>Thông tin thiết bị</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin thiết bị đã tạo</p></td><td><p>Y</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>9. UpdateDevice</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API cập nhật thông tin thiết bị</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>PUT&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/updateDevice</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request Body</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>entity</p></td><td><p>ThirdPartyDeviceDetailDTO</p></td><td><p>Thông tin thiết bị cần cập nhật</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin thiết bị đã cập nhật</p></td><td><p>Y</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 16px"><strong>Nhóm Public API về khách hàng</strong></span></p><p><span style="font-size: 14px"><strong>10. GetListCustomer</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy danh sách khách hàng</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListCustomer?page=0&amp;size=10</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>customerName</p></td><td><p>String</p></td><td><p>Tên khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>customerType</p></td><td><p>Integer</p></td><td><p>Loại khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>taxId</p></td><td><p>String</p></td><td><p>Mã số thuế</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>phone</p></td><td><p>String</p></td><td><p>Số điện thoại</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>email</p></td><td><p>String</p></td><td><p>Email</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>status</p></td><td><p>Integer</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>page</p></td><td><p>Integer</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>size</p></td><td><p>Integer</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>sort</p></td><td><p>String</p></td><td><p>Sắp xếp</p></td><td><p>N</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>data</p></td><td><p>Json array</p></td><td><p>Danh sách khách hàng</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"total": 5,<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerCode": "CUST001",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerName": "Công ty ABC",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerType": 1,<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"taxId": "0123456789",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"phone": "0987654321",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"email": "<EMAIL>",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"status": 1<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;]<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>11. GetDetailCustomer</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy thông tin chi tiết khách hàng</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getDetailCustomer/{customerId}</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Path Parameters</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>customerId</p></td><td><p>Long</p></td><td><p>ID khách hàng</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin chi tiết khách hàng</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerId": 1,<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerCode": "CUST001",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerName": "Công ty ABC",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerType": 1,<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"taxId": "0123456789",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"phone": "0987654321",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"email": "<EMAIL>",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"address": "123 Đường ABC, Hà Nội",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"status": 1<br>&nbsp;&nbsp;&nbsp;&nbsp;}<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 16px"><strong>Nhóm Public API về hợp đồng</strong></span></p><p><span style="font-size: 14px"><strong>12. GetListContract</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy danh sách hợp đồng</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListContract?page=0&amp;size=10</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>centerCode</p></td><td><p>String</p></td><td><p>Mã trung tâm</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>contractCode</p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>paymentName</p></td><td><p>String</p></td><td><p>Tên thanh toán</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>customerCode</p></td><td><p>String</p></td><td><p>Mã khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>contactPhone</p></td><td><p>String</p></td><td><p>Số điện thoại liên hệ</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>customerName</p></td><td><p>String</p></td><td><p>Tên khách hàng</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>contractor</p></td><td><p>String</p></td><td><p>Người làm hợp đồng</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>page</p></td><td><p>Integer</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>size</p></td><td><p>Integer</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr><tr><td><p>10</p></td><td><p>sort</p></td><td><p>String</p></td><td><p>Sắp xếp</p></td><td><p>N</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>data</p></td><td><p>Json array</p></td><td><p>Danh sách hợp đồng</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"total": 3,<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contractCode": "HD001",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"centerCode": "HNI",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"paymentName": "Công ty ABC",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerCode": "CUST001",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerName": "Công ty ABC",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contactPhone": "0987654321",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contractor": "Nguyễn Văn A"<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;]<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>13. GetDetailContract</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy thông tin chi tiết hợp đồng</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getDetailContract?contractCode=HD001</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>contractCode</p></td><td><p>String</p></td><td><p>Mã hợp đồng</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin chi tiết hợp đồng</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contractCode": "HD001",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"centerCode": "HNI",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"paymentName": "Công ty ABC",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerCode": "CUST001",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"customerName": "Công ty ABC",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contactPhone": "0987654321",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contractor": "Nguyễn Văn A",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"contractDate": "2024-01-15",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"status": 1<br>&nbsp;&nbsp;&nbsp;&nbsp;}<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 16px"><strong>Nhóm Public API về Traffic Wallet</strong></span></p><p><span style="font-size: 14px"><strong>14. GetListTrafficWallet</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy danh sách ví traffic</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>POST&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListTrafficWallet</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request Body</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>trafficWalletReqDTO</p></td><td><p>TrafficWalletReqDTO</p></td><td><p>Thông tin tìm kiếm ví traffic</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>data</p></td><td><p>Json array</p></td><td><p>Danh sách ví traffic</p></td><td><p>Y</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>15. GetDetailTrafficWallet</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy thông tin chi tiết ví traffic</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getDetailTrafficWallet</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>paymentCode</p></td><td><p>String</p></td><td><p>Mã thanh toán</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>subCode</p></td><td><p>String</p></td><td><p>Mã phụ</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>fromDate</p></td><td><p>String</p></td><td><p>Từ ngày</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>toDate</p></td><td><p>String</p></td><td><p>Đến ngày</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>transId</p></td><td><p>String</p></td><td><p>ID giao dịch</p></td><td><p>N</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>data</p></td><td><p>Json</p></td><td><p>Thông tin chi tiết ví traffic</p></td><td><p>Y</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>16. GetListShareWallet</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy danh sách ví chia sẻ</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getListShareWallet?subCode=SUB001&amp;page=0&amp;size=10</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>subCode</p></td><td><p>String</p></td><td><p>Mã phụ</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>page</p></td><td><p>Integer</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>size</p></td><td><p>Integer</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>sort</p></td><td><p>String</p></td><td><p>Sắp xếp</p></td><td><p>N</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>17. GetWalletHistory</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy lịch sử hoạt động ví</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getWalletHistory</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>activeType</p></td><td><p>Integer</p></td><td><p>Loại hoạt động</p></td><td><p>N</p></td></tr><tr><td><p>2</p></td><td><p>typeShare</p></td><td><p>Integer</p></td><td><p>Loại chia sẻ</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>status</p></td><td><p>Integer</p></td><td><p>Trạng thái</p></td><td><p>N</p></td></tr><tr><td><p>4</p></td><td><p>walletCode</p></td><td><p>String</p></td><td><p>Mã ví</p></td><td><p>N</p></td></tr><tr><td><p>5</p></td><td><p>fromDate</p></td><td><p>Long</p></td><td><p>Từ ngày</p></td><td><p>N</p></td></tr><tr><td><p>6</p></td><td><p>toDate</p></td><td><p>Long</p></td><td><p>Đến ngày</p></td><td><p>N</p></td></tr><tr><td><p>7</p></td><td><p>page</p></td><td><p>Integer</p></td><td><p>Trang</p></td><td><p>N</p></td></tr><tr><td><p>8</p></td><td><p>size</p></td><td><p>Integer</p></td><td><p>Số lượng của một trang</p></td><td><p>N</p></td></tr><tr><td><p>9</p></td><td><p>sort</p></td><td><p>String</p></td><td><p>Sắp xếp</p></td><td><p>N</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>18. ShareTraffic</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API chia sẻ traffic</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>POST&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/shareTraffic</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request Body</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>trafficShareReqDTO</p></td><td><p>TrafficShareReqDTO</p></td><td><p>Thông tin chia sẻ traffic</p></td><td><p>Y</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px"><strong>19. GetTerminalUsageDataDetails</strong></span></p><p><span style="font-size: 14px">- <strong>Mục đích sử dụng</strong>: Là API lấy chi tiết dữ liệu sử dụng của terminal</span></p><p><span style="font-size: 14px">- <strong>Phương thức và URL</strong>: <strong>GET&nbsp;</strong>https://api-m2m.vinaphone.com.vn/api/msimapi/getTerminalUsageDataDetails?msisdn=84823384832&amp;fromDate=2024-01-01&amp;toDate=2024-01-31</span></p><p><span style="font-size: 14px">-<strong> Request</strong></span></p><table><tbody><tr><td colspan="5"><p style="text-align:left"><strong><span style="color:black">Headers</span></strong></p></td></tr><tr><td><p style="text-align:left"><strong>STT</strong></p></td><td><p style="text-align:left"><strong>Tên tham số</strong></p></td><td><p style="text-align:left"><strong>Kiểu dữ liệu</strong></p></td><td><p style="text-align:left"><strong>Mô tả</strong></p></td><td><p style="text-align:left"><strong>Bắt buộc</strong></p></td></tr><tr><td><p style="text-align:left">1</p></td><td><p style="text-align:left">Authorization</p></td><td><div><br></div></td><td><p style="text-align:left">Sử dụng Bearer token<br> Ví dụ: Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************</p></td><td><p style="text-align:left">Y</p></td></tr></tbody></table><p><br></p><table><tbody><tr><td colspan="5"><p><strong><span style="color:black">Request url params</span></strong></p></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>msisdn</p></td><td><p>String</p></td><td><p>Số thuê bao</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>fromDate</p></td><td><p>String</p></td><td><p>Từ ngày</p></td><td><p>Y</p></td></tr><tr><td><p>3</p></td><td><p>toDate</p></td><td><p>String</p></td><td><p>Đến ngày</p></td><td><p>Y</p></td></tr></tbody></table><p><span style="font-size: 14px"><strong>- Response</strong></span></p><table><tbody><tr><td colspan="4"><p><strong><span style="color:black">Response Body</span></strong></p></td><td><div><br></div></td></tr><tr><td><p><strong>STT</strong></p></td><td><p><strong>Tên tham số</strong></p></td><td><p><strong>Kiểu dữ liệu</strong></p></td><td><p><strong>Mô tả</strong></p></td><td><p><strong>Bắt buộc</strong></p></td></tr><tr><td><p>1</p></td><td><p>errorCode</p></td><td><p>Number</p></td><td><p>Mã lỗi</p></td><td><p>Y</p></td></tr><tr><td><p>2</p></td><td><p>errorDesc</p></td><td><p>String</p></td><td><p>Mô tả lỗi</p></td><td><p>N</p></td></tr><tr><td><p>3</p></td><td><p>total</p></td><td><p>Number</p></td><td><p>Tổng số bản ghi</p></td><td><p>Y</p></td></tr><tr><td><p>4</p></td><td><p>data</p></td><td><p>Json array</p></td><td><p>Chi tiết dữ liệu sử dụng</p></td><td><p>Y</p></td></tr></tbody></table><p><strong>-&nbsp;Dữ liệu đầu ra:</strong></p><table><tbody><tr><td><p>{<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 0,<br>&nbsp;&nbsp;&nbsp;&nbsp;"errorDesc": "SUCCESS",<br>&nbsp;&nbsp;&nbsp;&nbsp;"total": 30,<br>&nbsp;&nbsp;&nbsp;&nbsp;"data": [<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"msisdn": "84823384832",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"date": "2024-01-01",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"dataUsed": 1024000,<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"dataUnit": "bytes",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sessionCount": 5<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;]<br>}</p></td></tr></tbody></table><p><br></p><p><span style="font-size: 16px"><strong>Ghi chú</strong></span></p><p><span style="font-size: 14px">- Tất cả các API đều yêu cầu xác thực bằng Bearer token trong header Authorization</span></p><p><span style="font-size: 14px">- Các API có rate limiting để tránh spam, giới hạn 1 request/phút cho mỗi user</span></p><p><span style="font-size: 14px">- Định dạng ngày tháng: YYYY-MM-DD hoặc timestamp (Long)</span></p><p><span style="font-size: 14px">- Tất cả response đều có cấu trúc chung với errorCode và errorDesc</span></p><p><span style="font-size: 14px">- Base URL: https://api-m2m.vinaphone.com.vn/api</span></p>
