{"ast": null, "code": "export default {\n  label: {\n    customerCode: \"Customer Code\",\n    customerName: \"Customer Name\",\n    taxCode: \"Tax Code\",\n    contact: \"Contact\",\n    phoneNumber: \"Phone Number\",\n    type: \"Type\",\n    status: \"Status\",\n    email: \"Email\",\n    contract: \"Contract\",\n    provinceCode: \"Province Code\",\n    active: \"Active\",\n    inActive: \"Inactive\",\n    companyName: \"Company Name\",\n    birthday: \"Birthday\",\n    establishmentDate: \"Establishment Date\",\n    street: \"Street\",\n    district: \"District\",\n    city: \"Province/City\",\n    note: \"Note\",\n    fullName: \"Full name\",\n    listCustomer: \"List Customer\",\n    contractHeader: \"Contract\",\n    infoCustomer: \"Detail\",\n    editCustomer: \"Update\",\n    generalInfo: \"General Infomation\",\n    billingContact: \"Billing Contact\",\n    billingAddress: \"Billing Address\",\n    viewAccount: \"View Account\",\n    viewContract: \"View Contract\"\n  },\n  dropdown: {\n    personal: \"Personal\",\n    company: \"Company\",\n    agency: \"Agency\"\n  },\n  error: {\n    length: \"This field should have 2 - 255 characters \",\n    required: \"This field is required\",\n    character: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, Vietnamese, ! # $ % & ' * + - / = ?  ^ _ ` . { | } ~)\",\n    regular: \"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_, Vietnamese)\",\n    note: \"Wrong Format. Only Accept (chữ, số, Tiếng Việt, khoảng trắng, ! # $ % & ' * + - / = ?  ^ _ ` . , ( ) { | } ~ :)\"\n  }\n};", "map": {"version": 3, "names": ["label", "customerCode", "customerName", "taxCode", "contact", "phoneNumber", "type", "status", "email", "contract", "provinceCode", "active", "inActive", "companyName", "birthday", "establishmentDate", "street", "district", "city", "note", "fullName", "listCustomer", "contractHeader", "infoCustomer", "editCustomer", "generalInfo", "billingContact", "billing<PERSON><PERSON>ress", "viewAccount", "viewContract", "dropdown", "personal", "company", "agency", "error", "length", "required", "character", "regular"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\customer.ts"], "sourcesContent": ["export default{\r\n    label:{\r\n        customerCode:\"Customer Code\",\r\n        customerName:\"Customer Name\",\r\n        taxCode:\"Tax Code\",\r\n        contact:\"Contact\",\r\n        phoneNumber:\"Phone Number\",\r\n        type:\"Type\",\r\n        status:\"Status\",\r\n        email:\"Email\",\r\n        contract:\"Contract\",\r\n        provinceCode:\"Province Code\",\r\n        active:\"Active\",\r\n        inActive:\"Inactive\",\r\n        companyName:\"Company Name\",\r\n        birthday:\"Birthday\",\r\n        establishmentDate:\"Establishment Date\",\r\n        street:\"Street\",\r\n        district:\"District\",\r\n        city:\"Province/City\",\r\n        note:\"Note\",\r\n        fullName:\"Full name\",\r\n        listCustomer:\"List Customer\",\r\n        contractHeader:\"Contract\",\r\n        infoCustomer:\"Detail\",\r\n        editCustomer:\"Update\",\r\n        generalInfo: \"General Infomation\",\r\n        billingContact: \"Billing Contact\",\r\n        billingAddress:\"Billing Address\",\r\n        viewAccount:\"View Account\",\r\n        viewContract:\"View Contract\"\r\n    },\r\n    dropdown:{\r\n        personal:\"Personal\",\r\n        company:\"Company\",\r\n        agency:\"Agency\"\r\n    },\r\n    error:{\r\n        length:\"This field should have 2 - 255 characters \",\r\n        required:\"This field is required\",\r\n        character:\"Wrong Format. Only Accept (a-z, A-Z, 0-9, Vietnamese, ! # $ % & ' * + - / = ?  ^ _ ` . { | } ~)\",\r\n        regular:\"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_, Vietnamese)\",\r\n        note: \"Wrong Format. Only Accept (chữ, số, Tiếng Việt, khoảng trắng, ! # $ % & ' * + - / = ?  ^ _ ` . , ( ) { | } ~ :)\",\r\n    }\r\n}"], "mappings": "AAAA,eAAc;EACVA,KAAK,EAAC;IACFC,YAAY,EAAC,eAAe;IAC5BC,YAAY,EAAC,eAAe;IAC5BC,OAAO,EAAC,UAAU;IAClBC,OAAO,EAAC,SAAS;IACjBC,WAAW,EAAC,cAAc;IAC1BC,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,QAAQ;IACfC,KAAK,EAAC,OAAO;IACbC,QAAQ,EAAC,UAAU;IACnBC,YAAY,EAAC,eAAe;IAC5BC,MAAM,EAAC,QAAQ;IACfC,QAAQ,EAAC,UAAU;IACnBC,WAAW,EAAC,cAAc;IAC1BC,QAAQ,EAAC,UAAU;IACnBC,iBAAiB,EAAC,oBAAoB;IACtCC,MAAM,EAAC,QAAQ;IACfC,QAAQ,EAAC,UAAU;IACnBC,IAAI,EAAC,eAAe;IACpBC,IAAI,EAAC,MAAM;IACXC,QAAQ,EAAC,WAAW;IACpBC,YAAY,EAAC,eAAe;IAC5BC,cAAc,EAAC,UAAU;IACzBC,YAAY,EAAC,QAAQ;IACrBC,YAAY,EAAC,QAAQ;IACrBC,WAAW,EAAE,oBAAoB;IACjCC,cAAc,EAAE,iBAAiB;IACjCC,cAAc,EAAC,iBAAiB;IAChCC,WAAW,EAAC,cAAc;IAC1BC,YAAY,EAAC;GAChB;EACDC,QAAQ,EAAC;IACLC,QAAQ,EAAC,UAAU;IACnBC,OAAO,EAAC,SAAS;IACjBC,MAAM,EAAC;GACV;EACDC,KAAK,EAAC;IACFC,MAAM,EAAC,4CAA4C;IACnDC,QAAQ,EAAC,wBAAwB;IACjCC,SAAS,EAAC,iGAAiG;IAC3GC,OAAO,EAAC,2DAA2D;IACnEnB,IAAI,EAAE;;CAEb"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}