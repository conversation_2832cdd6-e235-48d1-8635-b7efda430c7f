{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { AppAccountListComponent } from \"./list/app.account.list.component\";\nimport { AppAccountCreateComponent } from \"./create/app.account.create.component\";\nimport { AppAccountEditComponent } from \"./edit/app.account.edit.component\";\nimport DataPage from \"src/app/service/data.page\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ApiLogComponent } from \"./api-log/api-log.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppAccountRoutingModule {\n  static {\n    this.ɵfac = function AppAccountRoutingModule_Factory(t) {\n      return new (t || AppAccountRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppAccountRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: AppAccountListComponent,\n        data: new DataPage(\"global.menu.listaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])\n      }, {\n        path: \"create\",\n        component: AppAccountCreateComponent,\n        data: new DataPage(\"global.titlepage.createaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])\n      }, {\n        path: \"edit/:id\",\n        component: AppAccountEditComponent,\n        data: new DataPage(\"global.titlepage.editaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])\n      },\n      // {path: \"detail/:id\", component: AppAccountDetailComponent, data: new DataPage(\"global.titlepage.detailaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_DETAIL])}\n      {\n        path: \"logApi\",\n        component: ApiLogComponent,\n        data: new DataPage(\"global.titlepage.apiLogs\", [CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppAccountRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppAccountListComponent", "AppAccountCreateComponent", "AppAccountEditComponent", "DataPage", "CONSTANTS", "ApiLogComponent", "AppAccountRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "ACCOUNT", "VIEW_LIST", "CREATE", "UPDATE", "SEARCH_LOG_API", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\app.account-routing.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { AppAccountListComponent } from \"./list/app.account.list.component\";\r\nimport { AppAccountCreateComponent } from \"./create/app.account.create.component\";\r\nimport { AppAccountEditComponent } from \"./edit/app.account.edit.component\";\r\nimport { AppAccountDetailComponent } from \"./detail/app.account.detail.component\";\r\nimport DataPage from \"src/app/service/data.page\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport {ApiLogComponent} from \"./api-log/api-log.component\";\r\n\r\n@NgModule({\r\n    imports:[\r\n        RouterModule.forChild([\r\n            {path: \"\", component: AppAccountListComponent, data: new DataPage(\"global.menu.listaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])},\r\n            {path: \"create\", component: AppAccountCreateComponent,data: new DataPage(\"global.titlepage.createaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])},\r\n            {path: \"edit/:id\", component: AppAccountEditComponent, data: new DataPage(\"global.titlepage.editaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])},\r\n            // {path: \"detail/:id\", component: AppAccountDetailComponent, data: new DataPage(\"global.titlepage.detailaccount\", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_DETAIL])}\r\n            {path: \"logApi\", component: ApiLogComponent, data: new DataPage(\"global.titlepage.apiLogs\", [CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])},\r\n        ])\r\n    ],\r\n    exports: [RouterModule]\r\n})\r\nexport class AppAccountRoutingModule{}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,uBAAuB,QAAQ,mCAAmC;AAE3E,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAAQC,eAAe,QAAO,6BAA6B;;;AAc3D,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAV5BP,YAAY,CAACQ,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAET,uBAAuB;QAAEU,IAAI,EAAE,IAAIP,QAAQ,CAAC,yBAAyB,EAAE,CAACC,SAAS,CAACO,WAAW,CAACC,OAAO,CAACC,SAAS,CAAC;MAAC,CAAC,EACxI;QAACL,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAER,yBAAyB;QAACS,IAAI,EAAE,IAAIP,QAAQ,CAAC,gCAAgC,EAAE,CAACC,SAAS,CAACO,WAAW,CAACC,OAAO,CAACE,MAAM,CAAC;MAAC,CAAC,EACnJ;QAACN,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEP,uBAAuB;QAAEQ,IAAI,EAAE,IAAIP,QAAQ,CAAC,8BAA8B,EAAE,CAACC,SAAS,CAACO,WAAW,CAACC,OAAO,CAACG,MAAM,CAAC;MAAC,CAAC;MAClJ;MACA;QAACP,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAEJ,eAAe;QAAEK,IAAI,EAAE,IAAIP,QAAQ,CAAC,0BAA0B,EAAE,CAACC,SAAS,CAACO,WAAW,CAACC,OAAO,CAACI,cAAc,CAAC;MAAC,CAAC,CAC/I,CAAC,EAEIjB,YAAY;IAAA;EAAA;;;2EAEbO,uBAAuB;IAAAW,OAAA,GAAAC,EAAA,CAAAnB,YAAA;IAAAoB,OAAA,GAFtBpB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}