{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/divider\";\nimport * as i4 from \"primeng/styleclass\";\nimport * as i5 from \"primeng/button\";\nexport class LandingComponent {\n  constructor(layoutService, router) {\n    this.layoutService = layoutService;\n    this.router = router;\n  }\n  static {\n    this.ɵfac = function LandingComponent_Factory(t) {\n      return new (t || LandingComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LandingComponent,\n      selectors: [[\"app-landing\"]],\n      decls: 310,\n      vars: 3,\n      consts: [[1, \"surface-0\", \"flex\", \"justify-content-center\"], [\"id\", \"home\", 1, \"landing-wrapper\", \"overflow-hidden\"], [1, \"py-4\", \"px-4\", \"mx-0\", \"md:mx-6\", \"lg:mx-8\", \"lg:px-8\", \"flex\", \"align-items-center\", \"justify-content-between\", \"relative\", \"lg:static\", \"mb-3\"], [\"href\", \"#\", 1, \"flex\", \"align-items-center\"], [\"alt\", \"Sakai Logo\", \"height\", \"50\", 1, \"mr-0\", \"lg:mr-2\", 3, \"src\"], [1, \"text-900\", \"font-medium\", \"text-2xl\", \"line-height-3\", \"mr-8\"], [\"pRipple\", \"\", \"pStyleClass\", \"@next\", \"enterClass\", \"hidden\", \"leaveToClass\", \"hidden\", 1, \"cursor-pointer\", \"block\", \"lg:hidden\", \"text-700\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-bars\", \"text-4xl\"], [1, \"align-items-center\", \"surface-0\", \"flex-grow-1\", \"justify-content-between\", \"hidden\", \"lg:flex\", \"absolute\", \"lg:static\", \"w-full\", \"left-0\", \"px-6\", \"lg:px-0\", \"z-2\", 2, \"top\", \"120px\"], [1, \"list-none\", \"p-0\", \"m-0\", \"flex\", \"lg:align-items-center\", \"select-none\", \"flex-column\", \"lg:flex-row\", \"cursor-pointer\"], [\"pRipple\", \"\", 1, \"flex\", \"m-0\", \"md:ml-5\", \"px-0\", \"py-3\", \"text-900\", \"font-medium\", \"line-height-3\", 3, \"click\"], [1, \"flex\", \"justify-content-between\", \"lg:block\", \"border-top-1\", \"lg:border-top-none\", \"surface-border\", \"py-3\", \"lg:py-0\", \"mt-3\", \"lg:mt-0\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Login\", 1, \"p-button-text\", \"p-button-rounded\", \"border-none\", \"font-light\", \"line-height-2\", \"text-blue-500\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Register\", 1, \"p-button-rounded\", \"border-none\", \"ml-5\", \"font-light\", \"line-height-2\", \"bg-blue-500\", \"text-white\"], [\"id\", \"hero\", 1, \"flex\", \"flex-column\", \"pt-4\", \"px-4\", \"lg:px-8\", \"overflow-hidden\", 2, \"background\", \"linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), radial-gradient(77.36% 256.97% at 77.36% 57.52%, #EEEFAF 0%, #C3E3FA 100%)\", \"clip-path\", \"ellipse(150% 87% at 93% 13%)\"], [1, \"mx-4\", \"md:mx-8\", \"mt-0\", \"md:mt-4\"], [1, \"text-6xl\", \"font-bold\", \"text-gray-900\", \"line-height-2\"], [1, \"font-light\", \"block\"], [1, \"font-normal\", \"text-2xl\", \"line-height-3\", \"md:mt-3\", \"text-gray-700\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Get Started\", 1, \"p-button-rounded\", \"text-xl\", \"border-none\", \"mt-3\", \"bg-blue-500\", \"font-normal\", \"line-height-3\", \"px-3\", \"text-white\"], [1, \"flex\", \"justify-content-center\", \"md:justify-content-end\"], [\"src\", \"assets/images/landing/screen-1.png\", \"alt\", \"Hero Image\", 1, \"w-9\", \"md:w-auto\"], [\"id\", \"features\", 1, \"py-4\", \"px-4\", \"lg:px-8\", \"mt-5\", \"mx-0\", \"lg:mx-8\"], [1, \"grid\", \"justify-content-center\"], [1, \"col-12\", \"text-center\", \"mt-8\", \"mb-4\"], [1, \"text-900\", \"font-normal\", \"mb-2\"], [1, \"text-600\", \"text-2xl\"], [1, \"col-12\", \"md:col-12\", \"lg:col-4\", \"p-0\", \"lg:pr-5\", \"lg:pb-5\", \"mt-4\", \"lg:mt-0\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(253, 228, 165, 0.2),rgba(187, 199, 205, 0.2)), linear-gradient(180deg, rgba(253, 228, 165, 0.2),rgba(187, 199, 205, 0.2))\"], [1, \"p-3\", \"surface-card\", \"h-full\", 2, \"border-radius\", \"8px\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-yellow-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-users\", \"text-2xl\", \"text-yellow-700\"], [1, \"mb-2\", \"text-900\"], [1, \"text-600\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(145,226,237,0.2),rgba(251, 199, 145, 0.2)), linear-gradient(180deg, rgba(253, 228, 165, 0.2), rgba(172, 180, 223, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-cyan-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-palette\", \"text-2xl\", \"text-cyan-700\"], [1, \"col-12\", \"md:col-12\", \"lg:col-4\", \"p-0\", \"lg:pb-5\", \"mt-4\", \"lg:mt-0\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(145, 226, 237, 0.2), rgba(172, 180, 223, 0.2)), linear-gradient(180deg, rgba(172, 180, 223, 0.2), rgba(246, 158, 188, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-indigo-200\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-map\", \"text-2xl\", \"text-indigo-700\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(187, 199, 205, 0.2),rgba(251, 199, 145, 0.2)), linear-gradient(180deg, rgba(253, 228, 165, 0.2),rgba(145, 210, 204, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-bluegray-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-id-card\", \"text-2xl\", \"text-bluegray-700\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(187, 199, 205, 0.2),rgba(246, 158, 188, 0.2)), linear-gradient(180deg, rgba(145, 226, 237, 0.2),rgba(160, 210, 250, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-star\", \"text-2xl\", \"text-orange-700\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(251, 199, 145, 0.2), rgba(246, 158, 188, 0.2)), linear-gradient(180deg, rgba(172, 180, 223, 0.2), rgba(212, 162, 221, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-pink-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-moon\", \"text-2xl\", \"text-pink-700\"], [1, \"col-12\", \"md:col-12\", \"lg:col-4\", \"p-0\", \"lg:pr-5\", \"mt-4\", \"lg:mt-0\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(145, 210, 204, 0.2), rgba(160, 210, 250, 0.2)), linear-gradient(180deg, rgba(187, 199, 205, 0.2), rgba(145, 210, 204, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-teal-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-shopping-cart\", \"text-2xl\", \"text-teal-700\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(145, 210, 204, 0.2), rgba(212, 162, 221, 0.2)), linear-gradient(180deg, rgba(251, 199, 145, 0.2), rgba(160, 210, 250, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-globe\", \"text-2xl\", \"text-blue-700\"], [1, \"col-12\", \"md:col-12\", \"lg:col-4\", \"p-0\", \"lg-4\", \"mt-4\", \"lg:mt-0\"], [2, \"height\", \"160px\", \"padding\", \"2px\", \"border-radius\", \"10px\", \"background\", \"linear-gradient(90deg, rgba(160, 210, 250, 0.2), rgba(212, 162, 221, 0.2)), linear-gradient(180deg, rgba(246, 158, 188, 0.2), rgba(212, 162, 221, 0.2))\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-purple-200\", \"mb-3\", 2, \"width\", \"3.5rem\", \"height\", \"3.5rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-eye\", \"text-2xl\", \"text-purple-700\"], [1, \"col-12\", \"mt-8\", \"mb-8\", \"p-2\", \"md:p-8\", 2, \"border-radius\", \"20px\", \"background\", \"linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), radial-gradient(77.36% 256.97% at 77.36% 57.52%, #EFE1AF 0%, #C3DCFA 100%)\"], [1, \"flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"text-center\", \"px-3\", \"py-3\", \"md:py-0\"], [1, \"text-gray-900\", \"mb-2\"], [1, \"text-gray-600\", \"text-2xl\"], [1, \"text-gray-900\", \"sm:line-height-2\", \"md:line-height-4\", \"text-2xl\", \"mt-4\", 2, \"max-width\", \"800px\"], [\"src\", \"assets/images/landing/peak-logo.svg\", \"alt\", \"Company logo\", 1, \"mt-4\"], [\"id\", \"highlights\", 1, \"py-4\", \"px-4\", \"lg:px-8\", \"mx-0\", \"my-6\", \"lg:mx-8\"], [1, \"text-center\"], [1, \"grid\", \"mt-8\", \"pb-2\", \"md:pb-8\"], [1, \"flex\", \"justify-content-center\", \"col-12\", \"lg:col-6\", \"bg-purple-100\", \"p-0\", \"flex-order-1\", \"lg:flex-order-0\", 2, \"border-radius\", \"8px\"], [\"src\", \"assets/images/landing/mockup.svg\", \"alt\", \"mockup mobile\", 1, \"w-11\"], [1, \"col-12\", \"lg:col-6\", \"my-auto\", \"flex\", \"flex-column\", \"lg:align-items-end\", \"text-center\", \"lg:text-right\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-purple-200\", \"align-self-center\", \"lg:align-self-end\", 2, \"width\", \"4.2rem\", \"height\", \"4.2rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-mobile\", \"text-5xl\", \"text-purple-700\"], [1, \"line-height-1\", \"text-900\", \"text-4xl\", \"font-normal\"], [1, \"text-700\", \"text-2xl\", \"line-height-3\", \"ml-0\", \"md:ml-2\", 2, \"max-width\", \"650px\"], [1, \"grid\", \"my-8\", \"pt-2\", \"md:pt-8\"], [1, \"col-12\", \"lg:col-6\", \"my-auto\", \"flex\", \"flex-column\", \"text-center\", \"lg:text-left\", \"lg:align-items-start\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-yellow-200\", \"align-self-center\", \"lg:align-self-start\", 2, \"width\", \"4.2rem\", \"height\", \"4.2rem\", \"border-radius\", \"10px\"], [1, \"pi\", \"pi-fw\", \"pi-desktop\", \"text-5xl\", \"text-yellow-700\"], [1, \"text-700\", \"text-2xl\", \"line-height-3\", \"mr-0\", \"md:mr-2\", 2, \"max-width\", \"650px\"], [1, \"flex\", \"justify-content-end\", \"flex-order-1\", \"sm:flex-order-2\", \"col-12\", \"lg:col-6\", \"bg-yellow-100\", \"p-0\", 2, \"border-radius\", \"8px\"], [\"src\", \"assets/images/landing/mockup-desktop.svg\", \"alt\", \"mockup\", 1, \"w-11\"], [\"id\", \"pricing\", 1, \"py-4\", \"px-4\", \"lg:px-8\", \"my-2\", \"md:my-4\"], [1, \"grid\", \"justify-content-between\", \"mt-8\", \"md:mt-0\"], [1, \"col-12\", \"lg:col-4\", \"p-0\", \"md:p-3\"], [1, \"p-3\", \"flex\", \"flex-column\", \"border-200\", \"pricing-card\", \"cursor-pointer\", \"border-2\", \"hover:border-primary\", \"transition-duration-300\", \"transition-all\", 2, \"border-radius\", \"10px\"], [1, \"text-900\", \"text-center\", \"my-5\"], [\"src\", \"assets/images/landing/free.svg\", \"alt\", \"free\", 1, \"w-10\", \"h-10\", \"mx-auto\"], [1, \"my-5\", \"text-center\"], [1, \"text-5xl\", \"font-bold\", \"mr-2\", \"text-900\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Get Started\", 1, \"block\", \"mx-auto\", \"mt-4\", \"p-button-rounded\", \"border-none\", \"ml-3\", \"font-light\", \"line-height-2\", \"bg-blue-500\", \"text-white\"], [1, \"w-full\", \"bg-surface-200\"], [1, \"my-5\", \"list-none\", \"p-0\", \"flex\", \"text-900\", \"flex-column\"], [1, \"py-2\"], [1, \"pi\", \"pi-fw\", \"pi-check\", \"text-xl\", \"text-cyan-500\", \"mr-2\"], [1, \"text-xl\", \"line-height-3\"], [1, \"col-12\", \"lg:col-4\", \"p-0\", \"md:p-3\", \"mt-4\", \"md:mt-0\"], [\"src\", \"assets/images/landing/startup.svg\", \"alt\", \"startup\", 1, \"w-10\", \"h-10\", \"mx-auto\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Try Free\", 1, \"block\", \"mx-auto\", \"mt-4\", \"p-button-rounded\", \"border-none\", \"ml-3\", \"font-light\", \"line-height-2\", \"bg-blue-500\", \"text-white\"], [\"src\", \"assets/images/landing/enterprise.svg\", \"alt\", \"enterprise\", 1, \"w-10\", \"h-10\", \"mx-auto\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Get a Quote\", 1, \"block\", \"mx-auto\", \"mt-4\", \"p-button-rounded\", \"border-none\", \"ml-3\", \"font-light\", \"line-height-2\", \"bg-blue-500\", \"text-white\"], [1, \"py-4\", \"px-4\", \"mx-0\", \"mt-8\", \"lg:mx-8\"], [1, \"grid\", \"justify-content-between\"], [1, \"col-12\", \"md:col-2\", 2, \"margin-top\", \"-1.5rem\"], [1, \"flex\", \"flex-wrap\", \"align-items-center\", \"justify-content-center\", \"md:justify-content-start\", \"md:mb-0\", \"mb-3\", \"cursor-pointer\", 3, \"click\"], [\"alt\", \"footer sections\", \"width\", \"50\", \"height\", \"50\", 1, \"mr-2\", 3, \"src\"], [1, \"font-medium\", \"text-3xl\", \"text-900\"], [1, \"col-12\", \"md:col-10\", \"lg:col-7\"], [1, \"grid\", \"text-center\", \"md:text-left\"], [1, \"col-12\", \"md:col-3\"], [1, \"font-medium\", \"text-2xl\", \"line-height-3\", \"mb-3\", \"text-900\"], [1, \"line-height-3\", \"text-xl\", \"block\", \"cursor-pointer\", \"mb-2\", \"text-700\"], [1, \"line-height-3\", \"text-xl\", \"block\", \"cursor-pointer\", \"text-700\"], [1, \"col-12\", \"md:col-3\", \"mt-4\", \"md:mt-0\"], [\"src\", \"assets/images/landing/new-badge.svg\", 1, \"ml-2\"]],\n      template: function LandingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"SAKAI\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"a\", 6);\n          i0.ɵɵelement(8, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"ul\", 9)(11, \"li\")(12, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function LandingComponent_Template_a_click_12_listener() {\n            return ctx.router.navigate([\"/landing\"], {\n              fragment: \"home\"\n            });\n          });\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \"Home\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"li\")(16, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function LandingComponent_Template_a_click_16_listener() {\n            return ctx.router.navigate([\"/landing\"], {\n              fragment: \"features\"\n            });\n          });\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"Features\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"li\")(20, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function LandingComponent_Template_a_click_20_listener() {\n            return ctx.router.navigate([\"/landing\"], {\n              fragment: \"highlights\"\n            });\n          });\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Highlights\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"li\")(24, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function LandingComponent_Template_a_click_24_listener() {\n            return ctx.router.navigate([\"/landing\"], {\n              fragment: \"pricing\"\n            });\n          });\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"Pricing\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 11);\n          i0.ɵɵelement(28, \"button\", 12)(29, \"button\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"div\", 15)(32, \"h1\", 16)(33, \"span\", 17);\n          i0.ɵɵtext(34, \"Eu sem integer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35, \"eget magna fermentum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\", 18);\n          i0.ɵɵtext(37, \"Sed blandit libero volutpat sed cras. Fames ac turpis egestas integer. Placerat in egestas erat... \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"button\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 20);\n          i0.ɵɵelement(40, \"img\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 22)(42, \"div\", 23)(43, \"div\", 24)(44, \"h2\", 25);\n          i0.ɵɵtext(45, \"Marvelous Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"span\", 26);\n          i0.ɵɵtext(47, \"Placerat in egestas erat...\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 27)(49, \"div\", 28)(50, \"div\", 29)(51, \"div\", 30);\n          i0.ɵɵelement(52, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"h5\", 32);\n          i0.ɵɵtext(54, \"Easy to Use\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"span\", 33);\n          i0.ɵɵtext(56, \"Posuere morbi leo urna molestie.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(57, \"div\", 27)(58, \"div\", 34)(59, \"div\", 29)(60, \"div\", 35);\n          i0.ɵɵelement(61, \"i\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"h5\", 32);\n          i0.ɵɵtext(63, \"Fresh Design\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 33);\n          i0.ɵɵtext(65, \"Semper risus in hendrerit.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(66, \"div\", 37)(67, \"div\", 38)(68, \"div\", 29)(69, \"div\", 39);\n          i0.ɵɵelement(70, \"i\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"h5\", 32);\n          i0.ɵɵtext(72, \"Well Documented\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\", 33);\n          i0.ɵɵtext(74, \"Non arcu risus quis varius quam quisque.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"div\", 27)(76, \"div\", 41)(77, \"div\", 29)(78, \"div\", 42);\n          i0.ɵɵelement(79, \"i\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"h5\", 32);\n          i0.ɵɵtext(81, \"Responsive Layout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"span\", 33);\n          i0.ɵɵtext(83, \"Nulla malesuada pellentesque elit.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(84, \"div\", 27)(85, \"div\", 44)(86, \"div\", 29)(87, \"div\", 45);\n          i0.ɵɵelement(88, \"i\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"h5\", 32);\n          i0.ɵɵtext(90, \"Clean Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"span\", 33);\n          i0.ɵɵtext(92, \"Condimentum lacinia quis vel eros.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(93, \"div\", 37)(94, \"div\", 47)(95, \"div\", 29)(96, \"div\", 48);\n          i0.ɵɵelement(97, \"i\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"h5\", 32);\n          i0.ɵɵtext(99, \"Dark Mode\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"span\", 33);\n          i0.ɵɵtext(101, \"Convallis tellus id interdum velit laoreet.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(102, \"div\", 50)(103, \"div\", 51)(104, \"div\", 29)(105, \"div\", 52);\n          i0.ɵɵelement(106, \"i\", 53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"h5\", 32);\n          i0.ɵɵtext(108, \"Ready to Use\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"span\", 33);\n          i0.ɵɵtext(110, \"Mauris sit amet massa vitae.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(111, \"div\", 50)(112, \"div\", 54)(113, \"div\", 29)(114, \"div\", 55);\n          i0.ɵɵelement(115, \"i\", 56);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"h5\", 32);\n          i0.ɵɵtext(117, \"Modern Practices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"span\", 33);\n          i0.ɵɵtext(119, \"Elementum nibh tellus molestie nunc non.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(120, \"div\", 57)(121, \"div\", 58)(122, \"div\", 29)(123, \"div\", 59);\n          i0.ɵɵelement(124, \"i\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"h5\", 32);\n          i0.ɵɵtext(126, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"span\", 33);\n          i0.ɵɵtext(128, \"Neque egestas congue quisque.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(129, \"div\", 61)(130, \"div\", 62)(131, \"h3\", 63);\n          i0.ɵɵtext(132, \"Jos\\u00E9phine Miller\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"span\", 64);\n          i0.ɵɵtext(134, \"Peak Interactive\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"p\", 65);\n          i0.ɵɵtext(136, \"\\u201CDuis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\\u201D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(137, \"img\", 66);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(138, \"div\", 67)(139, \"div\", 68)(140, \"h2\", 25);\n          i0.ɵɵtext(141, \"Powerful Everywhere\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"span\", 26);\n          i0.ɵɵtext(143, \"Amet consectetur adipiscing elit...\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(144, \"div\", 69)(145, \"div\", 70);\n          i0.ɵɵelement(146, \"img\", 71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"div\", 72)(148, \"div\", 73);\n          i0.ɵɵelement(149, \"i\", 74);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"h2\", 75);\n          i0.ɵɵtext(151, \"Congue Quisque Egestas\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"span\", 76);\n          i0.ɵɵtext(153, \"Lectus arcu bibendum at varius vel pharetra vel turpis nunc. Eget aliquet nibh praesent tristique magna sit amet purus gravida. Sit amet mattis vulputate enim nulla aliquet.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(154, \"div\", 77)(155, \"div\", 78)(156, \"div\", 79);\n          i0.ɵɵelement(157, \"i\", 80);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"h2\", 75);\n          i0.ɵɵtext(159, \"Celerisque Eu Ultrices\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"span\", 81);\n          i0.ɵɵtext(161, \"Adipiscing commodo elit at imperdiet dui. Viverra nibh cras pulvinar mattis nunc sed blandit libero. Suspendisse in est ante in. Mauris pharetra et ultrices neque ornare aenean euismod elementum nisi.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(162, \"div\", 82);\n          i0.ɵɵelement(163, \"img\", 83);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(164, \"div\", 84)(165, \"div\", 68)(166, \"h2\", 25);\n          i0.ɵɵtext(167, \"Matchless Pricing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(168, \"span\", 26);\n          i0.ɵɵtext(169, \"Amet consectetur adipiscing elit...\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(170, \"div\", 85)(171, \"div\", 86)(172, \"div\", 87)(173, \"h3\", 88);\n          i0.ɵɵtext(174, \"Free\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(175, \"img\", 89);\n          i0.ɵɵelementStart(176, \"div\", 90)(177, \"span\", 91);\n          i0.ɵɵtext(178, \"$0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(179, \"span\", 33);\n          i0.ɵɵtext(180, \"per month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(181, \"button\", 92);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(182, \"p-divider\", 93);\n          i0.ɵɵelementStart(183, \"ul\", 94)(184, \"li\", 95);\n          i0.ɵɵelement(185, \"i\", 96);\n          i0.ɵɵelementStart(186, \"span\", 97);\n          i0.ɵɵtext(187, \"Responsive Layout\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(188, \"li\", 95);\n          i0.ɵɵelement(189, \"i\", 96);\n          i0.ɵɵelementStart(190, \"span\", 97);\n          i0.ɵɵtext(191, \"Unlimited Push Messages\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(192, \"li\", 95);\n          i0.ɵɵelement(193, \"i\", 96);\n          i0.ɵɵelementStart(194, \"span\", 97);\n          i0.ɵɵtext(195, \"50 Support Ticket\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"li\", 95);\n          i0.ɵɵelement(197, \"i\", 96);\n          i0.ɵɵelementStart(198, \"span\", 97);\n          i0.ɵɵtext(199, \"Free Shipping\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(200, \"div\", 98)(201, \"div\", 87)(202, \"h3\", 88);\n          i0.ɵɵtext(203, \"Startup\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(204, \"img\", 99);\n          i0.ɵɵelementStart(205, \"div\", 90)(206, \"span\", 91);\n          i0.ɵɵtext(207, \"$1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(208, \"span\", 33);\n          i0.ɵɵtext(209, \"per month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(210, \"button\", 100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(211, \"p-divider\", 93);\n          i0.ɵɵelementStart(212, \"ul\", 94)(213, \"li\", 95);\n          i0.ɵɵelement(214, \"i\", 96);\n          i0.ɵɵelementStart(215, \"span\", 97);\n          i0.ɵɵtext(216, \"Responsive Layout\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(217, \"li\", 95);\n          i0.ɵɵelement(218, \"i\", 96);\n          i0.ɵɵelementStart(219, \"span\", 97);\n          i0.ɵɵtext(220, \"Unlimited Push Messages\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(221, \"li\", 95);\n          i0.ɵɵelement(222, \"i\", 96);\n          i0.ɵɵelementStart(223, \"span\", 97);\n          i0.ɵɵtext(224, \"50 Support Ticket\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(225, \"li\", 95);\n          i0.ɵɵelement(226, \"i\", 96);\n          i0.ɵɵelementStart(227, \"span\", 97);\n          i0.ɵɵtext(228, \"Free Shipping\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(229, \"div\", 98)(230, \"div\", 87)(231, \"h3\", 88);\n          i0.ɵɵtext(232, \"Enterprise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(233, \"img\", 101);\n          i0.ɵɵelementStart(234, \"div\", 90)(235, \"span\", 91);\n          i0.ɵɵtext(236, \"$999\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(237, \"span\", 33);\n          i0.ɵɵtext(238, \"per month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(239, \"button\", 102);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(240, \"p-divider\", 93);\n          i0.ɵɵelementStart(241, \"ul\", 94)(242, \"li\", 95);\n          i0.ɵɵelement(243, \"i\", 96);\n          i0.ɵɵelementStart(244, \"span\", 97);\n          i0.ɵɵtext(245, \"Responsive Layout\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(246, \"li\", 95);\n          i0.ɵɵelement(247, \"i\", 96);\n          i0.ɵɵelementStart(248, \"span\", 97);\n          i0.ɵɵtext(249, \"Unlimited Push Messages\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(250, \"li\", 95);\n          i0.ɵɵelement(251, \"i\", 96);\n          i0.ɵɵelementStart(252, \"span\", 97);\n          i0.ɵɵtext(253, \"50 Support Ticket\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(254, \"li\", 95);\n          i0.ɵɵelement(255, \"i\", 96);\n          i0.ɵɵelementStart(256, \"span\", 97);\n          i0.ɵɵtext(257, \"Free Shipping\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(258, \"div\", 103)(259, \"div\", 104)(260, \"div\", 105)(261, \"a\", 106);\n          i0.ɵɵlistener(\"click\", function LandingComponent_Template_a_click_261_listener() {\n            return ctx.router.navigate([\"/pages/landing\"], {\n              fragment: \"home\"\n            });\n          });\n          i0.ɵɵelement(262, \"img\", 107);\n          i0.ɵɵelementStart(263, \"h4\", 108);\n          i0.ɵɵtext(264, \"SAKAI\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(265, \"div\", 109)(266, \"div\", 110)(267, \"div\", 111)(268, \"h4\", 112);\n          i0.ɵɵtext(269, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(270, \"a\", 113);\n          i0.ɵɵtext(271, \"About Us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(272, \"a\", 113);\n          i0.ɵɵtext(273, \"News\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(274, \"a\", 113);\n          i0.ɵɵtext(275, \"Investor Relations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(276, \"a\", 113);\n          i0.ɵɵtext(277, \"Careers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(278, \"a\", 114);\n          i0.ɵɵtext(279, \"Media Kit\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(280, \"div\", 115)(281, \"h4\", 112);\n          i0.ɵɵtext(282, \"Resources\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(283, \"a\", 113);\n          i0.ɵɵtext(284, \"Get Started\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(285, \"a\", 113);\n          i0.ɵɵtext(286, \"Learn\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(287, \"a\", 114);\n          i0.ɵɵtext(288, \"Case Studies\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(289, \"div\", 115)(290, \"h4\", 112);\n          i0.ɵɵtext(291, \"Community\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(292, \"a\", 113);\n          i0.ɵɵtext(293, \"Discord\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(294, \"a\", 113);\n          i0.ɵɵtext(295, \"Events\");\n          i0.ɵɵelement(296, \"img\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(297, \"a\", 113);\n          i0.ɵɵtext(298, \"FAQ\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(299, \"a\", 114);\n          i0.ɵɵtext(300, \"Blog\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(301, \"div\", 115)(302, \"h4\", 112);\n          i0.ɵɵtext(303, \"Legal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(304, \"a\", 113);\n          i0.ɵɵtext(305, \"Brand Policy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(306, \"a\", 113);\n          i0.ɵɵtext(307, \"Privacy Policy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(308, \"a\", 114);\n          i0.ɵɵtext(309, \"Terms of Service\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config.colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".svg\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n          i0.ɵɵadvance(255);\n          i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config.colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".svg\", i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [i3.Divider, i4.StyleClass, i5.ButtonDirective],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["LandingComponent", "constructor", "layoutService", "router", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LandingComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LandingComponent_Template_a_click_12_listener", "navigate", "fragment", "LandingComponent_Template_a_click_16_listener", "LandingComponent_Template_a_click_20_listener", "LandingComponent_Template_a_click_24_listener", "LandingComponent_Template_a_click_261_listener", "ɵɵadvance", "ɵɵpropertyInterpolate1", "config", "colorScheme", "ɵɵsanitizeUrl", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\landing\\landing.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\landing\\landing.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { LayoutService } from 'src/app/service/app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-landing',\r\n    templateUrl: './landing.component.html'\r\n})\r\nexport class LandingComponent {\r\n\r\n    constructor(public layoutService: LayoutService, public router: Router) { }\r\n    \r\n}\r\n", "<div class=\"surface-0 flex justify-content-center\">\r\n    <div id=\"home\" class=\"landing-wrapper overflow-hidden\">\r\n        <div class=\"py-4 px-4 mx-0 md:mx-6 lg:mx-8 lg:px-8 flex align-items-center justify-content-between relative lg:static mb-3\">\r\n            <a class=\"flex align-items-center\" href=\"#\">\r\n                <img src=\"assets/layout/images/{{layoutService.config.colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.svg\" alt=\"Sakai Logo\" height=\"50\" class=\"mr-0 lg:mr-2\"><span class=\"text-900 font-medium text-2xl line-height-3 mr-8\">SAKAI</span>\r\n            </a>\r\n            <a pRipple class=\"cursor-pointer block lg:hidden text-700\" pStyleClass=\"@next\" enterClass=\"hidden\" leaveToClass=\"hidden\" [hideOnOutsideClick]=\"true\">\r\n                <i class=\"pi pi-bars text-4xl\"></i>\r\n            </a>\r\n            <div class=\"align-items-center surface-0 flex-grow-1 justify-content-between hidden lg:flex absolute lg:static w-full left-0 px-6 lg:px-0 z-2\" style=\"top:120px\">\r\n                <ul class=\"list-none p-0 m-0 flex lg:align-items-center select-none flex-column lg:flex-row cursor-pointer\">\r\n                    <li>\r\n                        <a (click)=\"router.navigate(['/landing'], {fragment: 'home'})\" pRipple class=\"flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3\">\r\n                           <span>Home</span>\r\n                        </a>\r\n                    </li>\r\n                    <li>\r\n                        <a (click)=\"router.navigate(['/landing'], {fragment: 'features'})\" pRipple class=\"flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3\">\r\n                            <span>Features</span>\r\n                        </a>\r\n                    </li>\r\n                    <li>\r\n                        <a (click)=\"router.navigate(['/landing'], {fragment: 'highlights'})\" pRipple class=\"flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3\">\r\n                            <span>Highlights</span>\r\n                        </a>\r\n                    </li>\r\n                    <li>\r\n                        <a (click)=\"router.navigate(['/landing'], {fragment: 'pricing'})\" pRipple class=\"flex m-0 md:ml-5 px-0 py-3 text-900 font-medium line-height-3\">\r\n                            <span>Pricing</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"flex justify-content-between lg:block border-top-1 lg:border-top-none surface-border py-3 lg:py-0 mt-3 lg:mt-0\">\r\n                    <button pButton pRipple label=\"Login\" class=\"p-button-text p-button-rounded border-none font-light line-height-2 text-blue-500\"></button>\r\n                    <button pButton pRipple label=\"Register\" class=\"p-button-rounded border-none ml-5 font-light line-height-2 bg-blue-500 text-white\"></button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    \r\n        <div id=\"hero\" class=\"flex flex-column pt-4 px-4 lg:px-8 overflow-hidden\" \r\n            style=\"background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), radial-gradient(77.36% 256.97% at 77.36% 57.52%, #EEEFAF 0%, #C3E3FA 100%); clip-path: ellipse(150% 87% at 93% 13%);\">\r\n            <div class=\"mx-4 md:mx-8 mt-0 md:mt-4\">\r\n                <h1 class=\"text-6xl font-bold text-gray-900 line-height-2\"><span class=\"font-light block\">Eu sem integer</span>eget magna fermentum</h1>\r\n                <p class=\"font-normal text-2xl line-height-3 md:mt-3 text-gray-700\">Sed blandit libero volutpat sed cras. Fames ac turpis egestas integer. Placerat in egestas erat... </p>\r\n                <button pButton pRipple type=\"button\" label=\"Get Started\" class=\"p-button-rounded text-xl border-none mt-3 bg-blue-500 font-normal line-height-3 px-3 text-white\"></button>\r\n            </div>\r\n            <div class=\"flex justify-content-center md:justify-content-end\">\r\n                <img src=\"assets/images/landing/screen-1.png\" alt=\"Hero Image\" class=\"w-9 md:w-auto\">\r\n            </div>\r\n        </div>\r\n        \r\n        <div id=\"features\" class=\"py-4 px-4 lg:px-8 mt-5 mx-0 lg:mx-8\">\r\n            <div class=\"grid justify-content-center\">\r\n                <div class=\"col-12 text-center mt-8 mb-4\">\r\n                    <h2 class=\"text-900 font-normal mb-2\">Marvelous Features</h2>\r\n                    <span class=\"text-600 text-2xl\">Placerat in egestas erat...</span>\r\n                </div>\r\n    \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pr-5 lg:pb-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(253, 228, 165, 0.2),rgba(187, 199, 205, 0.2)), linear-gradient(180deg, rgba(253, 228, 165, 0.2),rgba(187, 199, 205, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-yellow-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-users text-2xl text-yellow-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Easy to Use</h5>\r\n                            <span class=\"text-600\">Posuere morbi leo urna molestie.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pr-5 lg:pb-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(145,226,237,0.2),rgba(251, 199, 145, 0.2)), linear-gradient(180deg, rgba(253, 228, 165, 0.2), rgba(172, 180, 223, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-cyan-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-palette text-2xl text-cyan-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Fresh Design</h5>\r\n                            <span class=\"text-600\">Semper risus in hendrerit.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pb-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(145, 226, 237, 0.2), rgba(172, 180, 223, 0.2)), linear-gradient(180deg, rgba(172, 180, 223, 0.2), rgba(246, 158, 188, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-indigo-200\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-map text-2xl text-indigo-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Well Documented</h5>\r\n                            <span class=\"text-600\">Non arcu risus quis varius quam quisque.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pr-5 lg:pb-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(187, 199, 205, 0.2),rgba(251, 199, 145, 0.2)), linear-gradient(180deg, rgba(253, 228, 165, 0.2),rgba(145, 210, 204, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-bluegray-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-id-card text-2xl text-bluegray-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Responsive Layout</h5>\r\n                            <span class=\"text-600\">Nulla malesuada pellentesque elit.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pr-5 lg:pb-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(187, 199, 205, 0.2),rgba(246, 158, 188, 0.2)), linear-gradient(180deg, rgba(145, 226, 237, 0.2),rgba(160, 210, 250, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-orange-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-star text-2xl text-orange-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Clean Code</h5>\r\n                            <span class=\"text-600\">Condimentum lacinia quis vel eros.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pb-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(251, 199, 145, 0.2), rgba(246, 158, 188, 0.2)), linear-gradient(180deg, rgba(172, 180, 223, 0.2), rgba(212, 162, 221, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-pink-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-moon text-2xl text-pink-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Dark Mode</h5>\r\n                            <span class=\"text-600\">Convallis tellus id interdum velit laoreet.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pr-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(145, 210, 204, 0.2), rgba(160, 210, 250, 0.2)), linear-gradient(180deg, rgba(187, 199, 205, 0.2), rgba(145, 210, 204, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-teal-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-shopping-cart text-2xl text-teal-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Ready to Use</h5>\r\n                            <span class=\"text-600\">Mauris sit amet massa vitae.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg:pr-5 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(145, 210, 204, 0.2), rgba(212, 162, 221, 0.2)), linear-gradient(180deg, rgba(251, 199, 145, 0.2), rgba(160, 210, 250, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-blue-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-globe text-2xl text-blue-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Modern Practices</h5>\r\n                            <span class=\"text-600\">Elementum nibh tellus molestie nunc non.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n        \r\n                <div class=\"col-12 md:col-12 lg:col-4 p-0 lg-4 mt-4 lg:mt-0\">\r\n                    <div style=\"height:160px; padding:2px; border-radius:10px; background: linear-gradient(90deg, rgba(160, 210, 250, 0.2), rgba(212, 162, 221, 0.2)), linear-gradient(180deg, rgba(246, 158, 188, 0.2), rgba(212, 162, 221, 0.2));\">\r\n                        <div class=\"p-3 surface-card h-full\" style=\"border-radius:8px;\">\r\n                            <div class=\"flex align-items-center justify-content-center bg-purple-200 mb-3\" style=\"width:3.5rem;height:3.5rem; border-radius:10px;\">\r\n                                <i class=\"pi pi-fw pi-eye text-2xl text-purple-700\"></i>\r\n                            </div>\r\n                            <h5 class=\"mb-2 text-900\">Privacy</h5>\r\n                            <span class=\"text-600\">Neque egestas congue quisque.</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n    \r\n                <div class=\"col-12 mt-8 mb-8 p-2 md:p-8\" style=\"border-radius:20px; background:linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), radial-gradient(77.36% 256.97% at 77.36% 57.52%, #EFE1AF 0%, #C3DCFA 100%);\">\r\n                    <div class=\"flex flex-column justify-content-center align-items-center text-center px-3 py-3 md:py-0\">\r\n                        <h3 class=\"text-gray-900 mb-2\">Joséphine Miller</h3>\r\n                        <span class=\"text-gray-600 text-2xl\">Peak Interactive</span>\r\n                        <p class=\"text-gray-900 sm:line-height-2 md:line-height-4 text-2xl mt-4\" style=\"max-width:800px;\">“Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.”</p>\r\n                        <img src=\"assets/images/landing/peak-logo.svg\" class=\"mt-4\" alt=\"Company logo\">\r\n    \r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n            \r\n        <div id=\"highlights\" class=\"py-4 px-4 lg:px-8 mx-0 my-6 lg:mx-8\">\r\n            <div class=\"text-center\">\r\n                <h2 class=\"text-900 font-normal mb-2\">Powerful Everywhere</h2>\r\n                <span class=\"text-600 text-2xl\">Amet consectetur adipiscing elit...</span>\r\n            </div>\r\n    \r\n            <div class=\"grid mt-8 pb-2 md:pb-8\">\r\n                <div class=\"flex justify-content-center col-12 lg:col-6 bg-purple-100 p-0 flex-order-1 lg:flex-order-0\" style=\"border-radius:8px;\">\r\n                    <img src=\"assets/images/landing/mockup.svg\" class=\"w-11\" alt=\"mockup mobile\">\r\n                </div>\r\n    \r\n                <div class=\"col-12 lg:col-6 my-auto flex flex-column lg:align-items-end text-center lg:text-right\">\r\n                    <div class=\"flex align-items-center justify-content-center bg-purple-200 align-self-center lg:align-self-end\" style=\"width:4.2rem; height:4.2rem; border-radius: 10px;\">\r\n                        <i class=\"pi pi-fw pi-mobile text-5xl text-purple-700\"></i>\r\n                    </div>\r\n                    <h2 class=\"line-height-1 text-900 text-4xl font-normal\">Congue Quisque Egestas</h2>\r\n                    <span class=\"text-700 text-2xl line-height-3 ml-0 md:ml-2\" style=\"max-width:650px;\">Lectus arcu bibendum at varius vel pharetra vel turpis nunc. Eget aliquet nibh praesent tristique magna sit amet purus gravida. Sit amet mattis vulputate enim nulla aliquet.</span>\r\n                </div>\r\n            </div>\r\n    \r\n            <div class=\"grid my-8 pt-2 md:pt-8\">\r\n                <div class=\"col-12 lg:col-6 my-auto flex flex-column text-center lg:text-left lg:align-items-start\">\r\n                    <div class=\"flex align-items-center justify-content-center bg-yellow-200 align-self-center lg:align-self-start\" style=\"width:4.2rem; height:4.2rem; border-radius:10px;\">\r\n                        <i class=\"pi pi-fw pi-desktop text-5xl text-yellow-700\"></i>\r\n                    </div>\r\n                    <h2 class=\"line-height-1 text-900 text-4xl font-normal\">Celerisque Eu Ultrices</h2>\r\n                    <span class=\"text-700 text-2xl line-height-3 mr-0 md:mr-2\" style=\"max-width:650px;\">Adipiscing commodo elit at imperdiet dui. Viverra nibh cras pulvinar mattis nunc sed blandit libero. Suspendisse in est ante in. Mauris pharetra et ultrices neque ornare aenean euismod elementum nisi.</span>\r\n                </div>\r\n    \r\n                <div class=\"flex justify-content-end flex-order-1 sm:flex-order-2 col-12 lg:col-6 bg-yellow-100 p-0\" style=\"border-radius:8px;\">\r\n                    <img src=\"assets/images/landing/mockup-desktop.svg\" class=\"w-11\" alt=\"mockup\">\r\n                </div>\r\n            </div>\r\n        </div>\r\n    \r\n        <div id=\"pricing\" class=\"py-4 px-4 lg:px-8 my-2 md:my-4\">\r\n            <div class=\"text-center\">\r\n                <h2 class=\"text-900 font-normal mb-2\">Matchless Pricing</h2>\r\n                <span class=\"text-600 text-2xl\">Amet consectetur adipiscing elit...</span>\r\n            </div>\r\n    \r\n           <div class=\"grid justify-content-between mt-8 md:mt-0\">\r\n                <div class=\"col-12 lg:col-4 p-0 md:p-3\">\r\n                    <div class=\"p-3 flex flex-column border-200 pricing-card cursor-pointer border-2 hover:border-primary transition-duration-300 transition-all\" style=\"border-radius:10px\">\r\n                        <h3 class=\"text-900 text-center my-5\">Free</h3>\r\n                        <img src=\"assets/images/landing/free.svg\" class=\"w-10 h-10 mx-auto\" alt=\"free\">\r\n                        <div class=\"my-5 text-center\">\r\n                            <span class=\"text-5xl font-bold mr-2 text-900\">$0</span>\r\n                            <span class=\"text-600\">per month</span>\r\n                            <button pButton pRipple label=\"Get Started\" class=\"block mx-auto mt-4 p-button-rounded border-none ml-3 font-light line-height-2 bg-blue-500 text-white\"></button>\r\n                        </div>\r\n                        <p-divider class=\"w-full bg-surface-200\"></p-divider>\r\n                        <ul class=\"my-5 list-none p-0 flex text-900 flex-column\">\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Responsive Layout</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Unlimited Push Messages</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">50 Support Ticket</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Free Shipping</span>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"col-12 lg:col-4 p-0 md:p-3 mt-4 md:mt-0\">\r\n                    <div class=\"p-3 flex flex-column border-200 pricing-card cursor-pointer border-2 hover:border-primary transition-duration-300 transition-all\" style=\"border-radius:10px\">\r\n                        <h3 class=\"text-900 text-center my-5\">Startup</h3>\r\n                        <img src=\"assets/images/landing/startup.svg\" class=\"w-10 h-10 mx-auto\" alt=\"startup\">\r\n                        <div class=\"my-5 text-center\">\r\n                            <span class=\"text-5xl font-bold mr-2 text-900\">$1</span>\r\n                            <span class=\"text-600\">per month</span>\r\n                            <button pButton pRipple label=\"Try Free\" class=\"block mx-auto mt-4 p-button-rounded border-none ml-3 font-light line-height-2 bg-blue-500 text-white\"></button>\r\n                        </div>\r\n                        <p-divider class=\"w-full bg-surface-200\"></p-divider>\r\n                        <ul class=\"my-5 list-none p-0 flex text-900 flex-column\">\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Responsive Layout</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Unlimited Push Messages</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">50 Support Ticket</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Free Shipping</span>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"col-12 lg:col-4 p-0 md:p-3 mt-4 md:mt-0\">\r\n                    <div class=\"p-3 flex flex-column border-200 pricing-card cursor-pointer border-2 hover:border-primary transition-duration-300 transition-all\" style=\"border-radius:10px\">\r\n                        <h3 class=\"text-900 text-center my-5\">Enterprise</h3>\r\n                        <img src=\"assets/images/landing/enterprise.svg\" class=\"w-10 h-10 mx-auto\" alt=\"enterprise\">\r\n                        <div class=\"my-5 text-center\">\r\n                            <span class=\"text-5xl font-bold mr-2 text-900\">$999</span>\r\n                            <span class=\"text-600\">per month</span>\r\n                            <button pButton pRipple label=\"Get a Quote\" class=\"block mx-auto mt-4 p-button-rounded border-none ml-3 font-light line-height-2 bg-blue-500 text-white\"></button>\r\n                        </div>\r\n                        <p-divider class=\"w-full bg-surface-200\"></p-divider>\r\n                        <ul class=\"my-5 list-none p-0 flex text-900 flex-column\">\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Responsive Layout</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Unlimited Push Messages</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">50 Support Ticket</span>\r\n                            </li>\r\n                            <li class=\"py-2\">\r\n                                <i class=\"pi pi-fw pi-check text-xl text-cyan-500 mr-2\"></i>\r\n                                <span class=\"text-xl line-height-3\">Free Shipping</span>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n           </div> \r\n        </div>\r\n    \r\n        <div class=\"py-4 px-4 mx-0 mt-8 lg:mx-8\">\r\n            <div class=\"grid justify-content-between\">\r\n                <div class=\"col-12 md:col-2\" style=\"margin-top:-1.5rem;\">\r\n                    <a (click)=\"router.navigate(['/pages/landing'], {fragment: 'home'})\" class=\"flex flex-wrap align-items-center justify-content-center md:justify-content-start md:mb-0 mb-3 cursor-pointer\">\r\n                        <img src=\"assets/layout/images/{{layoutService.config.colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.svg\" alt=\"footer sections\" width=\"50\" height=\"50\" class=\"mr-2\">\r\n                        <h4 class=\"font-medium text-3xl text-900\">SAKAI</h4>\r\n                    </a>\r\n                </div>\r\n    \r\n                <div class=\"col-12 md:col-10 lg:col-7\">\r\n                    <div class=\"grid text-center md:text-left\">\r\n                        <div class=\"col-12 md:col-3\">\r\n                            <h4 class=\"font-medium text-2xl line-height-3 mb-3 text-900\">Company</h4>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">About Us</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">News</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Investor Relations</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Careers</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer text-700\">Media Kit</a>\r\n                        </div>\r\n                        \r\n                        <div class=\"col-12 md:col-3 mt-4 md:mt-0\">\r\n                            <h4 class=\"font-medium text-2xl line-height-3 mb-3 text-900\">Resources</h4>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Get Started</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Learn</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer text-700\">Case Studies</a>\r\n                        </div>\r\n        \r\n                        <div class=\"col-12 md:col-3 mt-4 md:mt-0\">\r\n                            <h4 class=\"font-medium text-2xl line-height-3 mb-3 text-900\">Community</h4>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Discord</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Events<img src=\"assets/images/landing/new-badge.svg\" class=\"ml-2\"/></a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">FAQ</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer text-700\">Blog</a>\r\n                        </div>\r\n        \r\n                        <div class=\"col-12 md:col-3 mt-4 md:mt-0\">\r\n                            <h4 class=\"font-medium text-2xl line-height-3 mb-3 text-900\">Legal</h4>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Brand Policy</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer mb-2 text-700\">Privacy Policy</a>\r\n                            <a class=\"line-height-3 text-xl block cursor-pointer text-700\">Terms of Service</a>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;AAQA,OAAM,MAAOA,gBAAgB;EAEzBC,YAAmBC,aAA4B,EAASC,MAAc;IAAnD,KAAAD,aAAa,GAAbA,aAAa;IAAwB,KAAAC,MAAM,GAANA,MAAM;EAAY;;;uBAFjEH,gBAAgB,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBT,gBAAgB;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR7BZ,EAAA,CAAAc,cAAA,aAAmD;UAInCd,EAAA,CAAAe,SAAA,aAAoK;UAAAf,EAAA,CAAAc,cAAA,cAA+D;UAAAd,EAAA,CAAAgB,MAAA,YAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEnPjB,EAAA,CAAAc,cAAA,WAAqJ;UACjJd,EAAA,CAAAe,SAAA,WAAmC;UACvCf,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAc,cAAA,aAAiK;UAGlJd,EAAA,CAAAkB,UAAA,mBAAAC,8CAAA;YAAA,OAASN,GAAA,CAAAd,MAAA,CAAAqB,QAAA,EAAiB,UAAU;cAAAC,QAAA,EAAc;YAAM,EAAE;UAAA,EAAC;UAC3DrB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAgB,MAAA,YAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAGxBjB,EAAA,CAAAc,cAAA,UAAI;UACGd,EAAA,CAAAkB,UAAA,mBAAAI,8CAAA;YAAA,OAAST,GAAA,CAAAd,MAAA,CAAAqB,QAAA,EAAiB,UAAU;cAAAC,QAAA,EAAc;YAAU,EAAE;UAAA,EAAC;UAC9DrB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAgB,MAAA,gBAAQ;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAG7BjB,EAAA,CAAAc,cAAA,UAAI;UACGd,EAAA,CAAAkB,UAAA,mBAAAK,8CAAA;YAAA,OAASV,GAAA,CAAAd,MAAA,CAAAqB,QAAA,EAAiB,UAAU;cAAAC,QAAA,EAAc;YAAY,EAAE;UAAA,EAAC;UAChErB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAgB,MAAA,kBAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAG/BjB,EAAA,CAAAc,cAAA,UAAI;UACGd,EAAA,CAAAkB,UAAA,mBAAAM,8CAAA;YAAA,OAASX,GAAA,CAAAd,MAAA,CAAAqB,QAAA,EAAiB,UAAU;cAAAC,QAAA,EAAc;YAAS,EAAE;UAAA,EAAC;UAC7DrB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAgB,MAAA,eAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAIhCjB,EAAA,CAAAc,cAAA,eAA4H;UACxHd,EAAA,CAAAe,SAAA,kBAAyI;UAE7If,EAAA,CAAAiB,YAAA,EAAM;UAIdjB,EAAA,CAAAc,cAAA,eACwN;UAEtHd,EAAA,CAAAgB,MAAA,sBAAc;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAAAjB,EAAA,CAAAgB,MAAA,4BAAoB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACxIjB,EAAA,CAAAc,cAAA,aAAoE;UAAAd,EAAA,CAAAgB,MAAA,2GAAmG;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC3KjB,EAAA,CAAAe,SAAA,kBAA2K;UAC/Kf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,eAAgE;UAC5Dd,EAAA,CAAAe,SAAA,eAAqF;UACzFf,EAAA,CAAAiB,YAAA,EAAM;UAGVjB,EAAA,CAAAc,cAAA,eAA+D;UAGbd,EAAA,CAAAgB,MAAA,0BAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC7DjB,EAAA,CAAAc,cAAA,gBAAgC;UAAAd,EAAA,CAAAgB,MAAA,mCAA2B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAGtEjB,EAAA,CAAAc,cAAA,eAAwE;UAIxDd,EAAA,CAAAe,SAAA,aAA0D;UAC9Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAgB,MAAA,mBAAW;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC1CjB,EAAA,CAAAc,cAAA,gBAAuB;UAAAd,EAAA,CAAAgB,MAAA,wCAAgC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAK1EjB,EAAA,CAAAc,cAAA,eAAwE;UAIxDd,EAAA,CAAAe,SAAA,aAA0D;UAC9Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAgB,MAAA,oBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC3CjB,EAAA,CAAAc,cAAA,gBAAuB;UAAAd,EAAA,CAAAgB,MAAA,kCAA0B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAKpEjB,EAAA,CAAAc,cAAA,eAAgE;UAIhDd,EAAA,CAAAe,SAAA,aAAwD;UAC5Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAgB,MAAA,uBAAe;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC9CjB,EAAA,CAAAc,cAAA,gBAAuB;UAAAd,EAAA,CAAAgB,MAAA,gDAAwC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAKlFjB,EAAA,CAAAc,cAAA,eAAwE;UAIxDd,EAAA,CAAAe,SAAA,aAA8D;UAClEf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAgB,MAAA,yBAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAChDjB,EAAA,CAAAc,cAAA,gBAAuB;UAAAd,EAAA,CAAAgB,MAAA,0CAAkC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAK5EjB,EAAA,CAAAc,cAAA,eAAwE;UAIxDd,EAAA,CAAAe,SAAA,aAAyD;UAC7Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAgB,MAAA,kBAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACzCjB,EAAA,CAAAc,cAAA,gBAAuB;UAAAd,EAAA,CAAAgB,MAAA,0CAAkC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAK5EjB,EAAA,CAAAc,cAAA,eAAgE;UAIhDd,EAAA,CAAAe,SAAA,aAAuD;UAC3Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACxCjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,oDAA2C;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAKrFjB,EAAA,CAAAc,cAAA,gBAAgE;UAIhDd,EAAA,CAAAe,SAAA,cAAgE;UACpEf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,eAA0B;UAAAd,EAAA,CAAAgB,MAAA,qBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC3CjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,qCAA4B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAKtEjB,EAAA,CAAAc,cAAA,gBAAgE;UAIhDd,EAAA,CAAAe,SAAA,cAAwD;UAC5Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,eAA0B;UAAAd,EAAA,CAAAgB,MAAA,yBAAgB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC/CjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,iDAAwC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAKlFjB,EAAA,CAAAc,cAAA,gBAA6D;UAI7Cd,EAAA,CAAAe,SAAA,cAAwD;UAC5Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,eAA0B;UAAAd,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACtCjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,sCAA6B;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAKvEjB,EAAA,CAAAc,cAAA,gBAAuO;UAEhMd,EAAA,CAAAgB,MAAA,8BAAgB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACpDjB,EAAA,CAAAc,cAAA,iBAAqC;UAAAd,EAAA,CAAAgB,MAAA,yBAAgB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC5DjB,EAAA,CAAAc,cAAA,cAAkG;UAAAd,EAAA,CAAAgB,MAAA,0OAAuN;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC7TjB,EAAA,CAAAe,SAAA,gBAA+E;UAEnFf,EAAA,CAAAiB,YAAA,EAAM;UAKlBjB,EAAA,CAAAc,cAAA,gBAAiE;UAEnBd,EAAA,CAAAgB,MAAA,4BAAmB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC9DjB,EAAA,CAAAc,cAAA,iBAAgC;UAAAd,EAAA,CAAAgB,MAAA,4CAAmC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAG9EjB,EAAA,CAAAc,cAAA,gBAAoC;UAE5Bd,EAAA,CAAAe,SAAA,gBAA6E;UACjFf,EAAA,CAAAiB,YAAA,EAAM;UAENjB,EAAA,CAAAc,cAAA,gBAAmG;UAE3Fd,EAAA,CAAAe,SAAA,cAA2D;UAC/Df,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,eAAwD;UAAAd,EAAA,CAAAgB,MAAA,+BAAsB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACnFjB,EAAA,CAAAc,cAAA,iBAAoF;UAAAd,EAAA,CAAAgB,MAAA,sLAA6K;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAIhRjB,EAAA,CAAAc,cAAA,gBAAoC;UAGxBd,EAAA,CAAAe,SAAA,cAA4D;UAChEf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAc,cAAA,eAAwD;UAAAd,EAAA,CAAAgB,MAAA,+BAAsB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACnFjB,EAAA,CAAAc,cAAA,iBAAoF;UAAAd,EAAA,CAAAgB,MAAA,iNAAwM;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAGvSjB,EAAA,CAAAc,cAAA,gBAAgI;UAC5Hd,EAAA,CAAAe,SAAA,gBAA8E;UAClFf,EAAA,CAAAiB,YAAA,EAAM;UAIdjB,EAAA,CAAAc,cAAA,gBAAyD;UAEXd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC5DjB,EAAA,CAAAc,cAAA,iBAAgC;UAAAd,EAAA,CAAAgB,MAAA,4CAAmC;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAG/EjB,EAAA,CAAAc,cAAA,gBAAuD;UAGJd,EAAA,CAAAgB,MAAA,aAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC/CjB,EAAA,CAAAe,SAAA,gBAA+E;UAC/Ef,EAAA,CAAAc,cAAA,gBAA8B;UACqBd,EAAA,CAAAgB,MAAA,WAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxDjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,kBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvCjB,EAAA,CAAAe,SAAA,mBAAkK;UACtKf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,SAAA,sBAAqD;UACrDf,EAAA,CAAAc,cAAA,eAAyD;UAEjDd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEhEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,gCAAuB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEtEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEhEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,sBAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAMxEjB,EAAA,CAAAc,cAAA,gBAAqD;UAEPd,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAClDjB,EAAA,CAAAe,SAAA,gBAAqF;UACrFf,EAAA,CAAAc,cAAA,gBAA8B;UACqBd,EAAA,CAAAgB,MAAA,WAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACxDjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,kBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvCjB,EAAA,CAAAe,SAAA,oBAA+J;UACnKf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,SAAA,sBAAqD;UACrDf,EAAA,CAAAc,cAAA,eAAyD;UAEjDd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEhEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,gCAAuB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEtEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEhEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,sBAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAMxEjB,EAAA,CAAAc,cAAA,gBAAqD;UAEPd,EAAA,CAAAgB,MAAA,mBAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACrDjB,EAAA,CAAAe,SAAA,iBAA2F;UAC3Ff,EAAA,CAAAc,cAAA,gBAA8B;UACqBd,EAAA,CAAAgB,MAAA,aAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAC1DjB,EAAA,CAAAc,cAAA,iBAAuB;UAAAd,EAAA,CAAAgB,MAAA,kBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UACvCjB,EAAA,CAAAe,SAAA,oBAAkK;UACtKf,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAe,SAAA,sBAAqD;UACrDf,EAAA,CAAAc,cAAA,eAAyD;UAEjDd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEhEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,gCAAuB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEtEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,0BAAiB;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAEhEjB,EAAA,CAAAc,cAAA,eAAiB;UACbd,EAAA,CAAAe,SAAA,cAA4D;UAC5Df,EAAA,CAAAc,cAAA,iBAAoC;UAAAd,EAAA,CAAAgB,MAAA,sBAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAO;UAQhFjB,EAAA,CAAAc,cAAA,iBAAyC;UAG1Bd,EAAA,CAAAkB,UAAA,mBAAAO,+CAAA;YAAA,OAASZ,GAAA,CAAAd,MAAA,CAAAqB,QAAA,EAAiB,gBAAgB;cAAAC,QAAA,EAAc;YAAM,EAAE;UAAA,EAAC;UAChErB,EAAA,CAAAe,SAAA,iBAA4K;UAC5Kf,EAAA,CAAAc,cAAA,gBAA0C;UAAAd,EAAA,CAAAgB,MAAA,cAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAI5DjB,EAAA,CAAAc,cAAA,iBAAuC;UAGkCd,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACzEjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,iBAAQ;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAChFjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,aAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC5EjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,2BAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC1FjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC/EjB,EAAA,CAAAc,cAAA,eAA+D;UAAAd,EAAA,CAAAgB,MAAA,kBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAGhFjB,EAAA,CAAAc,cAAA,iBAA0C;UACuBd,EAAA,CAAAgB,MAAA,kBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC3EjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,oBAAW;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UACnFjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,cAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC7EjB,EAAA,CAAAc,cAAA,eAA+D;UAAAd,EAAA,CAAAgB,MAAA,qBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAGnFjB,EAAA,CAAAc,cAAA,iBAA0C;UACuBd,EAAA,CAAAgB,MAAA,kBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAC3EjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,gBAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC/EjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,eAAM;UAAAhB,EAAA,CAAAe,SAAA,iBAA6D;UAAAf,EAAA,CAAAiB,YAAA,EAAI;UAC3IjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,YAAG;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAC3EjB,EAAA,CAAAc,cAAA,eAA+D;UAAAd,EAAA,CAAAgB,MAAA,aAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UAG3EjB,EAAA,CAAAc,cAAA,iBAA0C;UACuBd,EAAA,CAAAgB,MAAA,cAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACvEjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,qBAAY;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UACpFjB,EAAA,CAAAc,cAAA,eAAoE;UAAAd,EAAA,CAAAgB,MAAA,uBAAc;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;UACtFjB,EAAA,CAAAc,cAAA,eAA+D;UAAAd,EAAA,CAAAgB,MAAA,yBAAgB;UAAAhB,EAAA,CAAAiB,YAAA,EAAI;;;UA9V1FjB,EAAA,CAAA0B,SAAA,GAA4G;UAA5G1B,EAAA,CAAA2B,sBAAA,iCAAAd,GAAA,CAAAf,aAAA,CAAA8B,MAAA,CAAAC,WAAA,mDAAA7B,EAAA,CAAA8B,aAAA,CAA4G;UAEI9B,EAAA,CAAA0B,SAAA,GAA2B;UAA3B1B,EAAA,CAAA+B,UAAA,4BAA2B;UAyTnI/B,EAAA,CAAA0B,SAAA,KAA4G;UAA5G1B,EAAA,CAAA2B,sBAAA,iCAAAd,GAAA,CAAAf,aAAA,CAAA8B,MAAA,CAAAC,WAAA,mDAAA7B,EAAA,CAAA8B,aAAA,CAA4G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}