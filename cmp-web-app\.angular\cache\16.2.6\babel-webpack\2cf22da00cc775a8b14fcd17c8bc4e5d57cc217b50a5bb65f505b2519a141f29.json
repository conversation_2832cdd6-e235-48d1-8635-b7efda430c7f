{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { PermissionListComponent } from \"./app.permisstion.list.component\";\nimport DataPage from \"../../service/data.page\";\nimport { CONSTANTS } from \"../../service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class PermissionRoutingModule {\n  static {\n    this.ɵfac = function PermissionRoutingModule_Factory(t) {\n      return new (t || PermissionRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PermissionRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: PermissionListComponent,\n        data: new DataPage(\"global.menu.listpermission\", [CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PermissionRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PermissionListComponent", "DataPage", "CONSTANTS", "PermissionRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "PERMISSION", "VIEW_LIST", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\permission\\app.permission.routing.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { PermissionListComponent } from \"./app.permisstion.list.component\";\r\nimport DataPage from \"../../service/data.page\";\r\nimport {CONSTANTS} from \"../../service/comon/constants\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {path: \"\", component: PermissionListComponent, data: new DataPage(\"global.menu.listpermission\", [CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])}\r\n        ])\r\n    ],\r\n    exports:[\r\n        RouterModule\r\n    ]\r\n})\r\nexport class PermissionRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,SAAQC,SAAS,QAAO,+BAA+B;;;AAYvD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAR5BJ,YAAY,CAACK,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEN,uBAAuB;QAAEO,IAAI,EAAE,IAAIN,QAAQ,CAAC,4BAA4B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,UAAU,CAACC,SAAS,CAAC;MAAC,CAAC,CACjJ,CAAC,EAGFX,YAAY;IAAA;EAAA;;;2EAGPI,uBAAuB;IAAAQ,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAH5Bd,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}