{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ButtonModule } from 'primeng/button';\nimport { AccessRoutingModule } from './access-routing.module';\nimport { AccessComponent } from './access.component';\nimport * as i0 from \"@angular/core\";\nexport class AccessModule {\n  static {\n    this.ɵfac = function AccessModule_Factory(t) {\n      return new (t || AccessModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccessModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AccessRoutingModule, ButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccessModule, {\n    declarations: [AccessComponent],\n    imports: [CommonModule, AccessRoutingModule, ButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ButtonModule", "AccessRoutingModule", "AccessComponent", "AccessModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\access\\access.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ButtonModule } from 'primeng/button';\r\n\r\nimport { AccessRoutingModule } from './access-routing.module';\r\nimport { AccessComponent } from './access.component';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        AccessRoutingModule,\r\n        ButtonModule\r\n    ],\r\n    declarations: [AccessComponent]\r\n})\r\nexport class AccessModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;;AAUpD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBANjBJ,YAAY,EACZE,mBAAmB,EACnBD,YAAY;IAAA;EAAA;;;2EAIPG,YAAY;IAAAC,YAAA,GAFNF,eAAe;IAAAG,OAAA,GAJ1BN,YAAY,EACZE,mBAAmB,EACnBD,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}