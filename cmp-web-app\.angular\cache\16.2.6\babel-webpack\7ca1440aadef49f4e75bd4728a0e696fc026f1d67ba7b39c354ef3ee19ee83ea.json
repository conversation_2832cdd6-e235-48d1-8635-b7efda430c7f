{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { DynamicChartController } from \"../common-module/charts/dynamic.chart.component\";\nimport { ComboLazyControl } from \"../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/charts/ConfigChartService\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i7 from \"../common-module/charts/dynamic.chart.component\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/toolbar\";\nimport * as i10 from \"primeng/dragdrop\";\nimport * as i11 from \"primeng/inputnumber\";\nimport * as i12 from \"primeng/slider\";\nconst _c0 = function () {\n  return {\n    maxWidth: \"250px\",\n    minWidth: \"250px\"\n  };\n};\nfunction AppDashboard2Component_vnpt_select_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vnpt-select\", 14);\n    i0.ɵɵlistener(\"valueChange\", function AppDashboard2Component_vnpt_select_9_Template_vnpt_select_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.chartShows = $event);\n    })(\"onchange\", function AppDashboard2Component_vnpt_select_9_Template_vnpt_select_onchange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.changeChartShow($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c0));\n    i0.ɵɵproperty(\"control\", ctx_r0.chartComboboxController)(\"value\", ctx_r0.chartShows)(\"isAutoComplete\", false)(\"isMultiChoice\", true)(\"options\", ctx_r0.displayCharts)(\"lazyLoad\", false)(\"isFilterLocal\", true)(\"stylePositionBoxSelect\", ctx_r0.getBoxSelectStyle());\n  }\n}\nfunction AppDashboard2Component_p_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 15);\n    i0.ɵɵlistener(\"click\", function AppDashboard2Component_p_button_11_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.saveConfig());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r1.tranService.translate(\"global.button.save\"));\n  }\n}\nfunction AppDashboard2Component_p_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 15);\n    i0.ɵɵlistener(\"click\", function AppDashboard2Component_p_button_12_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.openEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r2.tranService.translate(\"global.button.edit\"));\n  }\n}\nfunction AppDashboard2Component_div_14_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"onDragStart\", function AppDashboard2Component_div_14_div_1_div_2_Template_div_onDragStart_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const chartPolicy_r13 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.dragXStart($event, chartPolicy_r13));\n    })(\"onDragEnd\", function AppDashboard2Component_div_14_div_1_div_2_Template_div_onDragEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.dragEnd($event));\n    })(\"onDrag\", function AppDashboard2Component_div_14_div_1_div_2_Template_div_onDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.dragX($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chartPolicy_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵstyleProp(\"z-index\", 100 + chartPolicy_r13.configPositionObject.zIndex + 1);\n  }\n}\nfunction AppDashboard2Component_div_14_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"onDragStart\", function AppDashboard2Component_div_14_div_1_div_3_Template_div_onDragStart_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const chartPolicy_r13 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.dragYStart($event, chartPolicy_r13));\n    })(\"onDragEnd\", function AppDashboard2Component_div_14_div_1_div_3_Template_div_onDragEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.dragEnd($event));\n    })(\"onDrag\", function AppDashboard2Component_div_14_div_1_div_3_Template_div_onDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r30 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r30.dragY($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chartPolicy_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵstyleProp(\"z-index\", 100 + chartPolicy_r13.configPositionObject.zIndex + 1);\n  }\n}\nfunction AppDashboard2Component_div_14_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"onDragStart\", function AppDashboard2Component_div_14_div_1_div_4_Template_div_onDragStart_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const chartPolicy_r13 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.dragXYStart($event, chartPolicy_r13));\n    })(\"onDragEnd\", function AppDashboard2Component_div_14_div_1_div_4_Template_div_onDragEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r35.dragEnd($event));\n    })(\"onDrag\", function AppDashboard2Component_div_14_div_1_div_4_Template_div_onDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.dragXY($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chartPolicy_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵstyleProp(\"z-index\", 100 + chartPolicy_r13.configPositionObject.zIndex + 2);\n  }\n}\nfunction AppDashboard2Component_div_14_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"i\", 28);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppDashboard2Component_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"dynamic-chart-vnpt\", 19);\n    i0.ɵɵtemplate(2, AppDashboard2Component_div_14_div_1_div_2_Template, 1, 2, \"div\", 20);\n    i0.ɵɵtemplate(3, AppDashboard2Component_div_14_div_1_div_3_Template, 1, 2, \"div\", 21);\n    i0.ɵɵtemplate(4, AppDashboard2Component_div_14_div_1_div_4_Template, 1, 2, \"div\", 22);\n    i0.ɵɵtemplate(5, AppDashboard2Component_div_14_div_1_div_5_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chartPolicy_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"mode\", ctx_r15.modeView)(\"chartConfig\", chartPolicy_r13.chartConfig)(\"chartPolicy\", chartPolicy_r13)(\"control\", ctx_r15.listDynamicChartController[chartPolicy_r13.chartConfig.id])(\"width\", chartPolicy_r13.configPositionObject.widthChart)(\"height\", chartPolicy_r13.configPositionObject.heightChart)(\"handleOpenSetting\", ctx_r15.handleOpenSetting.bind(ctx_r15))(\"isShowButtonExtra\", ctx_r15.modeView == ctx_r15.objectModeView.UPDATE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.modeView == ctx_r15.objectModeView.UPDATE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.modeView == ctx_r15.objectModeView.UPDATE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.modeView == ctx_r15.objectModeView.UPDATE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.modeView == ctx_r15.objectModeView.UPDATE);\n  }\n}\nconst _c1 = function (a1, a2, a3) {\n  return {\n    position: \"absolute\",\n    top: a1,\n    left: a2,\n    border: a3\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    \"stacked-mode\": a0\n  };\n};\nfunction AppDashboard2Component_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"onDragStart\", function AppDashboard2Component_div_14_Template_div_onDragStart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const chartPolicy_r13 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.dragStart($event, chartPolicy_r13));\n    })(\"onDragEnd\", function AppDashboard2Component_div_14_Template_div_onDragEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.dragEnd($event));\n    })(\"onDrag\", function AppDashboard2Component_div_14_Template_div_onDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.drag($event));\n    });\n    i0.ɵɵtemplate(1, AppDashboard2Component_div_14_div_1_Template, 6, 12, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chartPolicy_r13 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(!ctx_r3.isStackedView ? i0.ɵɵpureFunction3(5, _c1, chartPolicy_r13.configPositionObject.top + \"px\", chartPolicy_r13.configPositionObject.left + \"px\", ctx_r3.modeView === ctx_r3.objectModeView.DETAIL ? \"none\" : \"1px dashed gray\") : null);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c2, ctx_r3.isStackedView))(\"id\", \"chart\" + chartPolicy_r13.idChart);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chartPolicy_r13.chartConfig && chartPolicy_r13.status == 1);\n  }\n}\nfunction AppDashboard2Component_div_16_div_1_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 38);\n    i0.ɵɵlistener(\"click\", function AppDashboard2Component_div_16_div_1_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r48.minusSetting());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppDashboard2Component_div_16_div_1_p_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 39);\n    i0.ɵɵlistener(\"click\", function AppDashboard2Component_div_16_div_1_p_button_7_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r50.createSetting());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r45 = i0.ɵɵnextContext().index;\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r47.chartPolicyForcus.configLevelArray[i_r45].above || ctx_r47.chartPolicyForcus.configLevelArray[i_r45].above <= ctx_r47.chartPolicyForcus.configLevelArray[i_r45].below);\n  }\n}\nconst _c3 = function () {\n  return [];\n};\nfunction AppDashboard2Component_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"p-inputNumber\", 34);\n    i0.ɵɵlistener(\"ngModelChange\", function AppDashboard2Component_div_16_div_1_Template_p_inputNumber_ngModelChange_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const i_r45 = restoredCtx.index;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r53.chartPolicyForcus.configLevelArray[i_r45].below = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputNumber\", 35);\n    i0.ɵɵlistener(\"ngModelChange\", function AppDashboard2Component_div_16_div_1_Template_p_inputNumber_ngModelChange_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const i_r45 = restoredCtx.index;\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.chartPolicyForcus.configLevelArray[i_r45].above = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AppDashboard2Component_div_16_div_1_p_button_6_Template, 1, 0, \"p-button\", 36);\n    i0.ɵɵtemplate(7, AppDashboard2Component_div_16_div_1_p_button_7_Template, 1, 1, \"p-button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r45 = ctx.index;\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r43.tranService.translate(\"chart.label.threshold\"), \"\\u00A0\", i_r45 + 1, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r43.chartPolicyForcus.configLevelArray[i_r45].below)(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r43.chartPolicyForcus.configLevelArray[i_r45].above)(\"disabled\", i_r45 < ctx_r43.chartPolicyForcus.configLevelArray.length - 1 || (ctx_r43.chartPolicyForcus.configLevelArray || i0.ɵɵpureFunction0(9, _c3)).length >= 10)(\"min\", ctx_r43.chartPolicyForcus.configLevelArray[i_r45].below + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r45 != 0 && i_r45 == ctx_r43.chartPolicyForcus.configLevelArray.length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r45 == ctx_r43.chartPolicyForcus.configLevelArray.length - 1 && (ctx_r43.chartPolicyForcus.configLevelArray || i0.ɵɵpureFunction0(10, _c3)).length < 10);\n  }\n}\nfunction AppDashboard2Component_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, AppDashboard2Component_div_16_div_1_Template, 8, 11, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.chartPolicyForcus.configLevelArray);\n  }\n}\nfunction AppDashboard2Component_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"p-slider\", 40);\n    i0.ɵɵlistener(\"ngModelChange\", function AppDashboard2Component_div_18_div_1_Template_p_slider_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.sliderThreshold = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"lable\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p-button\", 41);\n    i0.ɵɵlistener(\"onClick\", function AppDashboard2Component_div_18_div_1_Template_p_button_onClick_7_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.createSliderThreshold());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r56.tranService.translate(\"chart.label.threshold\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r56.sliderThreshold)(\"step\", 5)(\"range\", true)(\"min\", 0)(\"max\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r56.sliderThreshold[0] + \"% - \" + ctx_r56.sliderThreshold[1] + \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r56.tranService.translate(\"global.button.add2\"));\n  }\n}\nfunction AppDashboard2Component_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, AppDashboard2Component_div_18_div_1_Template, 8, 8, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.chartPolicyForcus.configLevelArray);\n  }\n}\nconst _c4 = function () {\n  return {\n    padding: 0\n  };\n};\nconst _c5 = function (a1, a2, a4, a5) {\n  return {\n    minWidth: \"100%\",\n    height: a1,\n    width: a2,\n    padding: \"12px\",\n    border: a4,\n    backgroundColor: a5,\n    position: \"relative\",\n    boxSizing: \"border-box\"\n  };\n};\nconst _c6 = function () {\n  return {\n    width: \"700px\"\n  };\n};\nexport class AppDashboard2Component extends ComponentBase {\n  onResize() {\n    this.checkIfMobile();\n    this.calculateBorderline();\n  }\n  checkIfMobile() {\n    this.isStackedView = window.innerWidth <= 721;\n  }\n  // Dynamically get a style for vnpt-select on the dashboard\n  getBoxSelectStyle() {\n    if (this.isStackedView) {\n      return {\n        left: 'unset',\n        right: '65px',\n        top: '52px',\n        display: 'flex',\n        'flex-wrap': 'wrap',\n        width: '80vw'\n      };\n    } else {\n      return {\n        left: 'unset',\n        right: '65px',\n        top: '52px'\n      };\n    }\n  }\n  constructor(injector, configChartService, formBuilder, eRef) {\n    super(injector);\n    this.configChartService = configChartService;\n    this.formBuilder = formBuilder;\n    this.eRef = eRef;\n    this.charts = [];\n    this.displayCharts = [];\n    this.chartShows = [];\n    this.modeView = CONSTANTS.MODE_VIEW.DETAIL;\n    this.objectModeView = CONSTANTS.MODE_VIEW;\n    this.heightBoxWrapper = 1000;\n    this.widthBoxWrapper = 1000;\n    this.minX = 12;\n    this.minY = 12;\n    this.listChartPolicy = [];\n    this.listDynamicChartController = {};\n    //settingConfig\n    this.isShowEditSetting = false;\n    this.isShowEditThresholdSetting = false;\n    this.isStackedView = false;\n  }\n  ngOnInit() {\n    let me = this;\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.items = [{\n      label: 'Dashboard'\n    }];\n    this.chartShows = [];\n    this.getListChart();\n    this.calculateBorderline();\n    this.chartComboboxController = new ComboLazyControl();\n    this.sliderThreshold = [80, 90];\n    this.checkIfMobile();\n  }\n  ngAfterContentChecked() {\n    this.calculateSize();\n  }\n  getListChart() {\n    let me = this;\n    me.messageCommonService.onload();\n    this.configChartService.getAll(response => {\n      me.charts = response;\n      me.displayCharts = [...me.charts];\n      //get list config\n      me.configChartService.getListConfig(me.sessionService.userInfo.id, response => {\n        me.listChartPolicy = response.map(el => {\n          return {\n            configLevel: el.threshold,\n            configPosition: el.positionConfig,\n            id: el.id,\n            idChart: el.chartId,\n            status: el.status,\n            lastFilter: el.lastFilter\n          };\n        });\n        me.fillInfoForChartPolicy();\n        me.prepageDataShow();\n        me.messageCommonService.offload();\n      }, null, typeFinish => {\n        if (typeFinish == \"error\") {\n          me.messageCommonService.offload();\n        }\n      });\n    }, null, typeFinally => {\n      if (typeFinally == \"error\") {\n        me.messageCommonService.offload();\n      }\n    });\n  }\n  fillInfoForChartPolicy() {\n    let me = this;\n    this.charts.forEach(chartConfig => {\n      let index = me.listChartPolicy.findIndex(el => el.idChart == chartConfig.id);\n      if (index != null && index != undefined && index >= 0) {\n        me.listChartPolicy[index].chartConfig = chartConfig;\n      } else {\n        me.listChartPolicy.push({\n          id: null,\n          idChart: chartConfig.id,\n          configLevel: null,\n          configPosition: null,\n          status: 1,\n          chartConfig\n        });\n      }\n    });\n    this.listChartPolicy = this.listChartPolicy.filter(el => el.chartConfig != null);\n    let maxLeft = 12;\n    this.listChartPolicy.forEach((chartPolicy, index) => {\n      if (chartPolicy.configLevel) {\n        chartPolicy.configLevelArray = JSON.parse(chartPolicy.configLevel);\n      } else {\n        chartPolicy.configLevelArray = [{\n          below: 0,\n          above: null\n        }];\n      }\n      if (chartPolicy.configPosition) {\n        chartPolicy.configPositionObject = JSON.parse(chartPolicy.configPosition);\n      } else {\n        chartPolicy.configPositionObject = me.createPositionDefault();\n      }\n      if (chartPolicy.lastFilter) {\n        chartPolicy.lastFilter = JSON.parse(chartPolicy.lastFilter);\n      } else {\n        chartPolicy.lastFilter = null;\n      }\n      chartPolicy.configPositionObject.zIndex = index + 1;\n      if (!chartPolicy.configPositionObject.widthChart || chartPolicy.configPositionObject.widthChart < 0) {\n        chartPolicy.configPositionObject.widthChart = 400;\n      }\n      if (!chartPolicy.configPositionObject.heightChart || chartPolicy.configPositionObject.heightChart < 0) {\n        chartPolicy.configPositionObject.heightChart = 300;\n      }\n      if (chartPolicy.configPositionObject.left + chartPolicy.configPositionObject.widthChart > maxLeft && chartPolicy.configPositionObject.left >= 12) {\n        maxLeft = chartPolicy.configPositionObject.left + chartPolicy.configPositionObject.widthChart;\n      }\n    });\n    this.listChartPolicy.forEach((chartPolicy, index) => {\n      if (chartPolicy.configPositionObject.left == 0) {\n        chartPolicy.configPositionObject.left = maxLeft + 40;\n        maxLeft += 80 + chartPolicy.configPositionObject.widthChart;\n      }\n    });\n  }\n  createPositionDefault() {\n    return {\n      top: 0,\n      left: 0,\n      widthChart: 300,\n      heightChart: 400,\n      align: 'justify-content-start',\n      height: 300,\n      width: 400,\n      indexBudget: 0,\n      indexInBudget: 0,\n      marginLeft: 0,\n      marginRight: 0\n    };\n  }\n  prepageDataShow() {\n    let me = this;\n    this.chartShows = [];\n    this.listChartPolicy.forEach(chartPolicy => {\n      if (chartPolicy.chartConfig) {\n        me.listDynamicChartController[chartPolicy.chartConfig.id] = new DynamicChartController();\n      }\n      if (chartPolicy.status == 1) {\n        me.chartShows.push(chartPolicy.chartConfig);\n      }\n    });\n    setTimeout(() => {\n      Object.keys(me.listDynamicChartController).forEach(chartId => {\n        me.listDynamicChartController[chartId].reload(false, true);\n      });\n    });\n  }\n  changeChartShow(event) {\n    let me = this;\n    let chartIdShows = this.chartShows.map(el => el.id);\n    this.listChartPolicy.forEach(chartPolicy => {\n      if (chartIdShows.includes(chartPolicy.idChart)) {\n        chartPolicy.status = 1;\n        setTimeout(function () {\n          me.listDynamicChartController[chartPolicy.idChart].reloadParam();\n        });\n      } else {\n        chartPolicy.status = 0;\n      }\n    });\n  }\n  saveConfig() {\n    let me = this;\n    me.messageCommonService.onload();\n    let dataSend = [];\n    this.listChartPolicy.forEach(chartPolicy => {\n      chartPolicy.configLevel = JSON.stringify(chartPolicy.configLevelArray);\n      delete chartPolicy.configPositionObject.heightBox;\n      delete chartPolicy.configPositionObject.zIndex;\n      delete chartPolicy.configPositionObject.offsetX;\n      delete chartPolicy.configPositionObject.offsetY;\n      delete chartPolicy.configPositionObject.pageX;\n      delete chartPolicy.configPositionObject.pageY;\n      chartPolicy.configPosition = JSON.stringify(chartPolicy.configPositionObject);\n      dataSend.push({\n        id: chartPolicy.id,\n        chartId: chartPolicy.idChart,\n        userId: me.sessionService.userInfo.id,\n        status: chartPolicy.status,\n        threshold: chartPolicy.configLevel,\n        positionConfig: chartPolicy.configPosition,\n        lastFilter: typeof chartPolicy.lastFilter == 'string' ? chartPolicy.lastFilter : JSON.stringify(chartPolicy.lastFilter).toString()\n      });\n    });\n    this.configChartService.saveConfigDashboard(dataSend, response => {\n      me.messageCommonService.success(me.tranService.translate('global.message.success'));\n      me.modeView = CONSTANTS.MODE_VIEW.DETAIL;\n      me.messageCommonService.offload();\n      window.location.reload();\n    }, null, () => me.messageCommonService.offload());\n    // console.log(this.listChartPolicy)\n  }\n\n  openEdit() {\n    this.modeView = CONSTANTS.MODE_VIEW.UPDATE;\n  }\n  handleOpenSetting(chartId) {\n    let me = this;\n    this.chartPolicyForcus = this.listChartPolicy[this.listChartPolicy.findIndex(el => el.idChart == chartId)];\n    let subTypes = JSON.parse(this.chartPolicyForcus.chartConfig.subType);\n    let threshold = JSON.parse(this.chartPolicyForcus.configLevel);\n    if (threshold?.length > 0 && subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD) && threshold[0]?.below != null && threshold[0]?.above != null) {\n      me.sliderThreshold[0] = threshold[0].below;\n      me.sliderThreshold[1] = threshold[0].above;\n    }\n    setTimeout(function () {\n      if (subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD)) {\n        me.isShowEditThresholdSetting = true;\n      } else {\n        me.isShowEditSetting = true;\n      }\n    });\n  }\n  createSliderThreshold() {\n    this.chartPolicyForcus.configLevelArray = [{\n      below: this.sliderThreshold[0],\n      above: this.sliderThreshold[1]\n    }];\n    this.isShowEditThresholdSetting = false;\n    this.chartPolicyForcus = null;\n  }\n  minusSetting() {\n    this.chartPolicyForcus.configLevelArray.pop();\n    if (this.chartPolicyForcus.configLevelArray.length == 1) {\n      this.chartPolicyForcus.configLevelArray[0].above = null;\n    }\n  }\n  createSetting() {\n    let lastThreshold = this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1];\n    if (lastThreshold != null && lastThreshold.above > 0 && lastThreshold.above > lastThreshold.below) {\n      this.chartPolicyForcus.configLevelArray.push({\n        below: this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1].above\n      });\n    }\n  }\n  closeDialog() {\n    this.chartPolicyForcus = null;\n  }\n  onFilterChartname(event) {\n    let me = this;\n    let filter = event.filter || '';\n    if (filter.length > 0) {\n      me.displayCharts = me.charts.filter(chart => me.utilService.convertTextViToEnUpperCase(chart.name).indexOf(me.utilService.convertTextViToEnUpperCase(filter)) >= 0);\n      console.log(me.displayCharts);\n    }\n  }\n  //drag drop\n  // onResize(event){\n  // this.calculateBorderline();\n  //}\n  calculateSize() {\n    let me = this;\n    let maxHeight = 100;\n    let maxWidth = 100;\n    this.listChartPolicy.forEach(chartPolicy => {\n      let el = me.eRef.nativeElement.querySelector(`#chart${chartPolicy.idChart}`);\n      if (el) {\n        if (el[\"offsetTop\"] + el[\"offsetHeight\"] > maxHeight) {\n          maxHeight = el[\"offsetTop\"] + el[\"offsetHeight\"];\n        }\n        if (el[\"offsetLeft\"] + el[\"offsetWidth\"] > maxWidth) {\n          maxWidth = el[\"offsetLeft\"] + el[\"offsetWidth\"];\n        }\n      }\n    });\n    if (this.modeView == CONSTANTS.MODE_VIEW.UPDATE) {\n      maxHeight += 50;\n      maxWidth += 50;\n    }\n    if (!this.chartPolicyDragged) {\n      this.heightBoxWrapper = maxHeight;\n      this.widthBoxWrapper = maxWidth;\n    } else {\n      if (maxHeight > me.heightBoxWrapper) {\n        this.heightBoxWrapper = maxHeight + 50;\n      }\n      if (maxWidth > me.widthBoxWrapper) {\n        this.widthBoxWrapper = maxWidth + 50;\n      }\n    }\n  }\n  calculateBorderline() {\n    let boxWrapper = this.eRef.nativeElement.querySelector(\"#boxWrapper\");\n    this.minX = boxWrapper.getBoundingClientRect().left;\n    this.minY = boxWrapper.getBoundingClientRect().top;\n  }\n  drop(event) {\n    if (this.modeView != CONSTANTS.MODE_VIEW.DETAIL && this.chartPolicyDragged) {\n      this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.opacity = 1;\n    }\n    ;\n    this.typeDrag = null;\n    this.chartPolicyDragged = null;\n  }\n  dragEnd(event) {\n    if (this.modeView != CONSTANTS.MODE_VIEW.DETAIL && this.chartPolicyDragged) {\n      // this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.opacity = 1;\n    }\n    ;\n  }\n  dragStart(event, chartPolicy) {\n    if (!this.typeDrag) {\n      if (this.modeView != CONSTANTS.MODE_VIEW.DETAIL) {\n        this.typeDrag = null;\n        this.chartPolicyDragged = chartPolicy;\n        this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\n        this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\n        this.chartPolicyDragged.configPositionObject.pageX = event.pageX;\n        this.chartPolicyDragged.configPositionObject.pageY = event.pageY;\n        // this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.backgroundColor = 'transparent';\n      }\n    }\n  }\n\n  dragXStart(event, chartPolicy) {\n    if (!this.typeDrag) {\n      if (this.modeView != CONSTANTS.MODE_VIEW.DETAIL) {\n        this.chartPolicyDragged = chartPolicy;\n        let el = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);\n        this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\n        this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\n        this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;\n        this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;\n        this.typeDrag = 'scale-x';\n      }\n    }\n  }\n  dragYStart(event, chartPolicy) {\n    if (!this.typeDrag) {\n      if (this.modeView != CONSTANTS.MODE_VIEW.DETAIL) {\n        this.chartPolicyDragged = chartPolicy;\n        let el = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);\n        this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\n        this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\n        this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;\n        this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;\n        this.chartPolicyDragged.configPositionObject.heightBox = el.getBoundingClientRect().height;\n        this.typeDrag = 'scale-y';\n      }\n    }\n  }\n  dragXYStart(event, chartPolicy) {\n    if (!this.typeDrag) {\n      if (this.modeView != CONSTANTS.MODE_VIEW.DETAIL) {\n        this.chartPolicyDragged = chartPolicy;\n        let el = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);\n        this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\n        this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\n        this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;\n        this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;\n        this.chartPolicyDragged.configPositionObject.heightBox = el.getBoundingClientRect().height;\n        this.typeDrag = 'scale-xy';\n      }\n    }\n  }\n  drag(event) {\n    if (!this.typeDrag && this.chartPolicyDragged) {\n      if (!this.typeDrag) {\n        if (event.offsetX < 0 || event.offsetY < 0) return;\n        let x = event.pageX - this.chartPolicyDragged.configPositionObject.offsetX;\n        let y = event.pageY - this.chartPolicyDragged.configPositionObject.offsetY;\n        let top = y - this.minY;\n        let left = x - this.minX;\n        console.log(x, y, this.minX, this.minY, top, left);\n        if (top >= 12 && left >= 12) {\n          this.chartPolicyDragged.configPositionObject.top = top;\n          this.chartPolicyDragged.configPositionObject.left = left;\n          return;\n        } else if (top >= 12) {\n          this.chartPolicyDragged.configPositionObject.top = top;\n        } else if (left >= 12) {\n          this.chartPolicyDragged.configPositionObject.left = left;\n        }\n        event.preventDefault();\n      }\n    }\n  }\n  dragX(event) {\n    if (this.chartPolicyDragged) {\n      this.chartPolicyDragged.configPositionObject.widthChart = event.clientX - this.chartPolicyDragged.configPositionObject.pageX - 40;\n    }\n  }\n  dragY(event) {\n    if (this.chartPolicyDragged) {\n      let heightBox = event.clientY - this.chartPolicyDragged.configPositionObject.pageY;\n      this.chartPolicyDragged.configPositionObject.heightChart += heightBox - this.chartPolicyDragged.configPositionObject.heightBox;\n      this.chartPolicyDragged.configPositionObject.heightBox = heightBox;\n      console.log(event, this.chartPolicyDragged.configPositionObject);\n    }\n  }\n  dragXY(event) {\n    if (this.chartPolicyDragged) {\n      this.chartPolicyDragged.configPositionObject.widthChart = event.clientX - this.chartPolicyDragged.configPositionObject.pageX - 40;\n      let heightBox = event.clientY - this.chartPolicyDragged.configPositionObject.pageY;\n      this.chartPolicyDragged.configPositionObject.heightChart += heightBox - this.chartPolicyDragged.configPositionObject.heightBox;\n      this.chartPolicyDragged.configPositionObject.heightBox = heightBox;\n    }\n  }\n  static {\n    this.ɵfac = function AppDashboard2Component_Factory(t) {\n      return new (t || AppDashboard2Component)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.ConfigChartService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppDashboard2Component,\n      selectors: [[\"app-dashboard\"]],\n      hostBindings: function AppDashboard2Component_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function AppDashboard2Component_resize_HostBindingHandler() {\n            return ctx.onResize();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 19,\n      vars: 34,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [\"styleClass\", \"border-none bg-white\"], [1, \"p-toolbar-group-start\"], [1, \"p-toolbar-group-center\"], [\"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name}\", \"typeValue\", \"object\", \"styleClass\", \"vnpt-select-dashboard\", 3, \"control\", \"value\", \"isAutoComplete\", \"isMultiChoice\", \"options\", \"lazyLoad\", \"isFilterLocal\", \"style\", \"stylePositionBoxSelect\", \"valueChange\", \"onchange\", 4, \"ngIf\"], [1, \"p-toolbar-group-end\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"click\", 4, \"ngIf\"], [\"id\", \"boxWrapper\", \"pDroppable\", \"\", 1, \"relative\", \"mt-2\", 3, \"onDrop\"], [\"pDraggable\", \"\", \"class\", \"box-chart\", 3, \"ngClass\", \"style\", \"id\", \"onDragStart\", \"onDragEnd\", \"onDrag\", 4, \"ngFor\", \"ngForOf\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"onHide\", \"visibleChange\"], [\"class\", \"w-full field grid p-0 m-0\", 4, \"ngIf\"], [\"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name}\", \"typeValue\", \"object\", \"styleClass\", \"vnpt-select-dashboard\", 3, \"control\", \"value\", \"isAutoComplete\", \"isMultiChoice\", \"options\", \"lazyLoad\", \"isFilterLocal\", \"stylePositionBoxSelect\", \"valueChange\", \"onchange\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"click\"], [\"pDraggable\", \"\", 1, \"box-chart\", 3, \"ngClass\", \"id\", \"onDragStart\", \"onDragEnd\", \"onDrag\"], [\"class\", \"w-full\", \"style\", \"height: 100%;position: relative;\", 4, \"ngIf\"], [1, \"w-full\", 2, \"height\", \"100%\", \"position\", \"relative\"], [3, \"mode\", \"chartConfig\", \"chartPolicy\", \"control\", \"width\", \"height\", \"handleOpenSetting\", \"isShowButtonExtra\"], [\"class\", \"button-scale-x\", \"dragEffect\", \"move\", \"pDraggable\", \"\", \"style\", \"width: 10px;height: 100%; position: absolute; top: 0;left: calc(100% - 5px);background-color: transparent;\", 3, \"zIndex\", \"onDragStart\", \"onDragEnd\", \"onDrag\", 4, \"ngIf\"], [\"class\", \"button-scale-y\", \"pDraggable\", \"\", \"dragEffect\", \"move\", \"style\", \"width: 100%;height: 10px; position: absolute; left: 0;top: calc(100% - 5px);background-color: transparent;\", 3, \"zIndex\", \"onDragStart\", \"onDragEnd\", \"onDrag\", 4, \"ngIf\"], [\"class\", \"button-scale-z\", \"pDraggable\", \"\", \"dragEffect\", \"move\", \"style\", \"width: 10px;height: 10px; position: absolute; top: calc(100% - 5px);left: calc(100% - 5px);background-color: transparent;\", 3, \"zIndex\", \"onDragStart\", \"onDragEnd\", \"onDrag\", 4, \"ngIf\"], [\"class\", \"button-scale-z\", \"style\", \"width: 10px;height: 10px; position: absolute; top: calc(100% - 10px);left: calc(100% - 10px);background-color: transparent;\", 4, \"ngIf\"], [\"dragEffect\", \"move\", \"pDraggable\", \"\", 1, \"button-scale-x\", 2, \"width\", \"10px\", \"height\", \"100%\", \"position\", \"absolute\", \"top\", \"0\", \"left\", \"calc(100% - 5px)\", \"background-color\", \"transparent\", 3, \"onDragStart\", \"onDragEnd\", \"onDrag\"], [\"pDraggable\", \"\", \"dragEffect\", \"move\", 1, \"button-scale-y\", 2, \"width\", \"100%\", \"height\", \"10px\", \"position\", \"absolute\", \"left\", \"0\", \"top\", \"calc(100% - 5px)\", \"background-color\", \"transparent\", 3, \"onDragStart\", \"onDragEnd\", \"onDrag\"], [\"pDraggable\", \"\", \"dragEffect\", \"move\", 1, \"button-scale-z\", 2, \"width\", \"10px\", \"height\", \"10px\", \"position\", \"absolute\", \"top\", \"calc(100% - 5px)\", \"left\", \"calc(100% - 5px)\", \"background-color\", \"transparent\", 3, \"onDragStart\", \"onDragEnd\", \"onDrag\"], [1, \"button-scale-z\", 2, \"width\", \"10px\", \"height\", \"10px\", \"position\", \"absolute\", \"top\", \"calc(100% - 10px)\", \"left\", \"calc(100% - 10px)\", \"background-color\", \"transparent\"], [1, \"pi\", \"pi-chevron-down\", 2, \"font-size\", \"10px\", \"transform\", \"rotate(-45deg)\", \"vertical-align\", \"top\"], [1, \"w-full\", \"field\", \"grid\", \"p-0\", \"m-0\"], [\"class\", \"w-full field grid\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"threshold\", 1, \"col-fixed\", 2, \"width\", \"150px\", \"max-width\", \"150px\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\"], [1, \"col\", \"flex\", \"flex-row\", \"flex-wrap\", \"justify-content-start\", \"align-items-center\"], [\"disabled\", \"true\", \"inputId\", \"minmax\", \"mode\", \"decimal\", 1, \"mr-2\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"inputId\", \"minmax\", \"mode\", \"decimal\", 1, \"mr-2\", 3, \"ngModel\", \"disabled\", \"min\", \"ngModelChange\"], [\"styleClass\", \"mr-2 p-button-info\", \"icon\", \"pi pi-minus\", 3, \"click\", 4, \"ngIf\"], [\"icon\", \"pi pi-plus\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"mr-2 p-button-info\", \"icon\", \"pi pi-minus\", 3, \"click\"], [\"icon\", \"pi pi-plus\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"click\"], [1, \"col-8\", \"mt-3\", 3, \"ngModel\", \"step\", \"range\", \"min\", \"max\", \"ngModelChange\"], [\"styleClass\", \"p-button-info ml-3 \", 3, \"label\", \"onClick\"]],\n      template: function AppDashboard2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\")(6, \"p-toolbar\", 4);\n          i0.ɵɵelement(7, \"div\", 5);\n          i0.ɵɵelementStart(8, \"div\", 6);\n          i0.ɵɵtemplate(9, AppDashboard2Component_vnpt_select_9_Template, 1, 11, \"vnpt-select\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵtemplate(11, AppDashboard2Component_p_button_11_Template, 1, 1, \"p-button\", 9);\n          i0.ɵɵtemplate(12, AppDashboard2Component_p_button_12_Template, 1, 1, \"p-button\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵlistener(\"onDrop\", function AppDashboard2Component_Template_div_onDrop_13_listener($event) {\n            return ctx.drop($event);\n          });\n          i0.ɵɵtemplate(14, AppDashboard2Component_div_14_Template, 2, 11, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-dialog\", 12);\n          i0.ɵɵlistener(\"onHide\", function AppDashboard2Component_Template_p_dialog_onHide_15_listener() {\n            return ctx.closeDialog();\n          })(\"visibleChange\", function AppDashboard2Component_Template_p_dialog_visibleChange_15_listener($event) {\n            return ctx.isShowEditSetting = $event;\n          });\n          i0.ɵɵtemplate(16, AppDashboard2Component_div_16_Template, 2, 1, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p-dialog\", 12);\n          i0.ɵɵlistener(\"onHide\", function AppDashboard2Component_Template_p_dialog_onHide_17_listener() {\n            return ctx.closeDialog();\n          })(\"visibleChange\", function AppDashboard2Component_Template_p_dialog_visibleChange_17_listener($event) {\n            return ctx.isShowEditThresholdSetting = $event;\n          });\n          i0.ɵɵtemplate(18, AppDashboard2Component_div_18_Template, 2, 1, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c4));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.modeView == ctx.objectModeView.UPDATE);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.modeView == ctx.objectModeView.UPDATE);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.modeView == ctx.objectModeView.DETAIL);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction4(27, _c5, ctx.heightBoxWrapper + 24 + \"px\", ctx.widthBoxWrapper + 24 + \"px\", ctx.modeView == ctx.objectModeView.DETAIL ? \"none\" : \"1px dashed #777777\", ctx.modeView == ctx.objectModeView.DETAIL ? \"transparent\" : \"#CCCCCC\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listChartPolicy);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(32, _c6));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"chart.label.thresholdConfig\"))(\"visible\", ctx.isShowEditSetting)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.chartPolicyForcus);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(33, _c6));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"chart.label.thresholdConfig\"))(\"visible\", ctx.isShowEditThresholdSetting)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.chartPolicyForcus);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.Breadcrumb, i2.NgControlStatus, i2.NgModel, i5.Button, i6.VnptCombobox, i7.DynamicChartComponent, i8.Dialog, i9.Toolbar, i10.Draggable, i10.Droppable, i11.InputNumber, i12.Slider],\n      styles: [\"[_nghost-%COMP%]     {\\n        [pDraggable] {\\n            cursor: move;\\n            .button-scale-x {\\n                cursor: e-resize !important;\\n            }\\n            .button-scale-y {\\n                cursor: n-resize !important;\\n            }\\n            .button-scale-z {\\n                cursor: se-resize !important;\\n            }\\n        }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "DynamicChartController", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵlistener", "AppDashboard2Component_vnpt_select_9_Template_vnpt_select_valueChange_0_listener", "$event", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "chartShows", "AppDashboard2Component_vnpt_select_9_Template_vnpt_select_onchange_0_listener", "ctx_r8", "changeChartShow", "ɵɵelementEnd", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵproperty", "ctx_r0", "chartComboboxController", "displayCharts", "getBoxSelectStyle", "AppDashboard2Component_p_button_11_Template_p_button_click_0_listener", "_r10", "ctx_r9", "saveConfig", "ctx_r1", "tranService", "translate", "AppDashboard2Component_p_button_12_Template_p_button_click_0_listener", "_r12", "ctx_r11", "openEdit", "ctx_r2", "AppDashboard2Component_div_14_div_1_div_2_Template_div_onDragStart_0_listener", "_r22", "chartPolicy_r13", "$implicit", "ctx_r20", "dragXStart", "AppDashboard2Component_div_14_div_1_div_2_Template_div_onDragEnd_0_listener", "ctx_r23", "dragEnd", "AppDashboard2Component_div_14_div_1_div_2_Template_div_onDrag_0_listener", "ctx_r24", "dragX", "ɵɵstyleProp", "configPositionObject", "zIndex", "AppDashboard2Component_div_14_div_1_div_3_Template_div_onDragStart_0_listener", "_r28", "ctx_r26", "dragYStart", "AppDashboard2Component_div_14_div_1_div_3_Template_div_onDragEnd_0_listener", "ctx_r29", "AppDashboard2Component_div_14_div_1_div_3_Template_div_onDrag_0_listener", "ctx_r30", "dragY", "AppDashboard2Component_div_14_div_1_div_4_Template_div_onDragStart_0_listener", "_r34", "ctx_r32", "dragXYStart", "AppDashboard2Component_div_14_div_1_div_4_Template_div_onDragEnd_0_listener", "ctx_r35", "AppDashboard2Component_div_14_div_1_div_4_Template_div_onDrag_0_listener", "ctx_r36", "dragXY", "ɵɵelement", "ɵɵtemplate", "AppDashboard2Component_div_14_div_1_div_2_Template", "AppDashboard2Component_div_14_div_1_div_3_Template", "AppDashboard2Component_div_14_div_1_div_4_Template", "AppDashboard2Component_div_14_div_1_div_5_Template", "ɵɵadvance", "ctx_r15", "modeView", "chartConfig", "listDynamicChartController", "id", "<PERSON><PERSON><PERSON>", "heightChart", "handleOpenSetting", "bind", "objectModeView", "UPDATE", "AppDashboard2Component_div_14_Template_div_onDragStart_0_listener", "restoredCtx", "_r40", "ctx_r39", "dragStart", "AppDashboard2Component_div_14_Template_div_onDragEnd_0_listener", "ctx_r41", "AppDashboard2Component_div_14_Template_div_onDrag_0_listener", "ctx_r42", "drag", "AppDashboard2Component_div_14_div_1_Template", "ctx_r3", "isStackedView", "ɵɵpureFunction3", "_c1", "top", "left", "DETAIL", "ɵɵpureFunction1", "_c2", "idChart", "status", "AppDashboard2Component_div_16_div_1_p_button_6_Template_p_button_click_0_listener", "_r49", "ctx_r48", "minusSetting", "AppDashboard2Component_div_16_div_1_p_button_7_Template_p_button_click_0_listener", "_r51", "ctx_r50", "createSetting", "ctx_r47", "chartPolicyForcus", "configLevelArray", "i_r45", "above", "below", "ɵɵtext", "AppDashboard2Component_div_16_div_1_Template_p_inputNumber_ngModelChange_4_listener", "_r54", "index", "ctx_r53", "AppDashboard2Component_div_16_div_1_Template_p_inputNumber_ngModelChange_5_listener", "ctx_r55", "AppDashboard2Component_div_16_div_1_p_button_6_Template", "AppDashboard2Component_div_16_div_1_p_button_7_Template", "ɵɵtextInterpolate2", "ctx_r43", "length", "_c3", "AppDashboard2Component_div_16_div_1_Template", "ctx_r4", "AppDashboard2Component_div_18_div_1_Template_p_slider_ngModelChange_4_listener", "_r60", "ctx_r59", "slider<PERSON><PERSON><PERSON><PERSON>", "AppDashboard2Component_div_18_div_1_Template_p_button_onClick_7_listener", "ctx_r61", "createSliderThreshold", "ɵɵtextInterpolate1", "ctx_r56", "ɵɵtextInterpolate", "AppDashboard2Component_div_18_div_1_Template", "ctx_r5", "AppDashboard2Component", "onResize", "checkIfMobile", "calculateBorderline", "window", "innerWidth", "right", "display", "width", "constructor", "injector", "configChartService", "formBuilder", "eRef", "charts", "MODE_VIEW", "heightBoxWrapper", "widthBoxWrapper", "minX", "minY", "listChartPolicy", "isShowEditSetting", "isShowEditThresholdSetting", "ngOnInit", "me", "home", "icon", "routerLink", "items", "label", "get<PERSON><PERSON><PERSON><PERSON>", "ngAfterContentChecked", "calculateSize", "messageCommonService", "onload", "getAll", "response", "getListConfig", "sessionService", "userInfo", "map", "el", "configLevel", "threshold", "configPosition", "positionConfig", "chartId", "lastFilter", "fillInfoForChartPolicy", "prepageDataShow", "offload", "typeFinish", "typeFinally", "for<PERSON>ach", "findIndex", "undefined", "push", "filter", "maxLeft", "chartPolicy", "JSON", "parse", "createPositionDefault", "align", "height", "indexBudget", "indexInBudget", "marginLeft", "marginRight", "setTimeout", "Object", "keys", "reload", "event", "chartIdShows", "includes", "reloadParam", "dataSend", "stringify", "heightBox", "offsetX", "offsetY", "pageX", "pageY", "userId", "toString", "saveConfigDashboard", "success", "location", "subTypes", "subType", "CHART_SUB_TYPE", "SLIDER_THRESHOLD", "pop", "last<PERSON><PERSON><PERSON><PERSON>", "closeDialog", "onFilterChartname", "chart", "utilService", "convertTextViToEnUpperCase", "name", "indexOf", "console", "log", "maxHeight", "max<PERSON><PERSON><PERSON>", "nativeElement", "querySelector", "chartPolicyDragged", "boxWrapper", "getBoundingClientRect", "drop", "style", "opacity", "typeDrag", "x", "y", "preventDefault", "clientX", "clientY", "ɵɵdirectiveInject", "Injector", "i1", "ConfigChartService", "i2", "FormBuilder", "ElementRef", "selectors", "hostBindings", "AppDashboard2Component_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "AppDashboard2Component_vnpt_select_9_Template", "AppDashboard2Component_p_button_11_Template", "AppDashboard2Component_p_button_12_Template", "AppDashboard2Component_Template_div_onDrop_13_listener", "AppDashboard2Component_div_14_Template", "AppDashboard2Component_Template_p_dialog_onHide_15_listener", "AppDashboard2Component_Template_p_dialog_visibleChange_15_listener", "AppDashboard2Component_div_16_Template", "AppDashboard2Component_Template_p_dialog_onHide_17_listener", "AppDashboard2Component_Template_p_dialog_visibleChange_17_listener", "AppDashboard2Component_div_18_Template", "_c4", "ɵɵpureFunction4", "_c5", "_c6"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\dashboard\\app.dashboard2.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\dashboard\\app.dashboard2.component.html"], "sourcesContent": ["import { AfterContent<PERSON>hecked, Component, <PERSON>ement<PERSON><PERSON>, Injector, OnInit, HostListener } from \"@angular/core\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { ConfigChartService } from \"src/app/service/charts/ConfigChartService\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { DynamicChartController } from \"../common-module/charts/dynamic.chart.component\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { ComboLazyControl } from \"../common-module/combobox-lazyload/combobox.lazyload\";\r\n\r\ninterface PositionConfig {\r\n    // config drag position absolute\r\n    top?: number;\r\n    left?: number;\r\n    widthChart?: number;\r\n    heightChart?: number;\r\n    heightBox?: number;\r\n    zIndex?: number;\r\n    offsetX?: number;\r\n    offsetY?: number;\r\n    pageX?: number;\r\n    pageY?: number;\r\n    //config drag row\r\n    indexBudget?: number,\r\n    indexInBudget?: number,\r\n    width?: number,\r\n    height?: number,\r\n    marginLeft?: number,\r\n    marginRight?: number,\r\n    align?: string\r\n}\r\n\r\ninterface ChartPolicy{\r\n    id: number | null;\r\n    idChart: any | null;\r\n    status: number | null;\r\n    configLevel: string | null;\r\n    configPosition: string | null;\r\n\r\n    chartConfig?: any;\r\n    lastFilter?: any;\r\n    configLevelArray?: {below?: number, above?: number}[];\r\n    configPositionObject?: PositionConfig\r\n}\r\n\r\n@Component({\r\n    selector: \"app-dashboard\",\r\n    templateUrl: \"./app.dashboard2.component.html\",\r\n})\r\nexport class AppDashboard2Component extends ComponentBase implements OnInit,  AfterContentChecked {\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    charts: Array<any> = [];\r\n    displayCharts: Array<any> = [];\r\n    chartShows: Array<any> = [];\r\n    modeView: any = CONSTANTS.MODE_VIEW.DETAIL;\r\n    objectModeView = CONSTANTS.MODE_VIEW;\r\n    heightBoxWrapper: number = 1000;\r\n    widthBoxWrapper: number = 1000;\r\n    minX: number = 12;\r\n    minY: number = 12;\r\n\r\n    listChartPolicy: Array<ChartPolicy> = [];\r\n    listDynamicChartController: {[key: number|string]:DynamicChartController} = {};\r\n\r\n    chartPolicyDragged: ChartPolicy;\r\n    typeDrag: string;\r\n    chartPolicyForcus: ChartPolicy;\r\n    //settingConfig\r\n    isShowEditSetting: boolean = false;\r\n\r\n    isShowEditThresholdSetting: boolean = false;\r\n    sliderThreshold: [number, number];\r\n    chartComboboxController: ComboLazyControl;\r\n    isStackedView: boolean = false;\r\n\r\n    @HostListener('window:resize', [])\r\n    onResize() {\r\n      this.checkIfMobile();\r\n      this.calculateBorderline();\r\n    }\r\n\r\n    checkIfMobile() {\r\n      this.isStackedView = window.innerWidth <= 721;\r\n    }\r\n\r\n    // Dynamically get a style for vnpt-select on the dashboard\r\n    getBoxSelectStyle(): {[key: string]: any} {\r\n    if (this.isStackedView) {\r\n        return {\r\n            left: 'unset',\r\n            right: '65px',\r\n            top: '52px',\r\n            display: 'flex',\r\n            'flex-wrap': 'wrap',\r\n            width: '80vw',\r\n        };\r\n    } else {\r\n        return {\r\n            left: 'unset',\r\n            right: '65px',\r\n            top: '52px',\r\n            };\r\n        }\r\n    }\r\n\r\n\r\n    constructor(injector: Injector, private configChartService: ConfigChartService, private formBuilder: FormBuilder,\r\n        private eRef: ElementRef) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.home = {icon: 'pi pi-home', routerLink: '/'};\r\n        this.items = [{label: 'Dashboard'}];\r\n        this.chartShows = [];\r\n        this.getListChart();\r\n        this.calculateBorderline();\r\n        this.chartComboboxController = new ComboLazyControl();\r\n        this.sliderThreshold = [80,90];\r\n        this.checkIfMobile();\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        this.calculateSize();\r\n    }\r\n\r\n    getListChart(){\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.configChartService.getAll((response)=> {\r\n            me.charts = response;\r\n            me.displayCharts = [...me.charts];\r\n            //get list config\r\n            me.configChartService.getListConfig(me.sessionService.userInfo.id, (response)=> {\r\n                me.listChartPolicy = response.map(el => {\r\n                    return {\r\n                        configLevel: el.threshold,\r\n                        configPosition: el.positionConfig,\r\n                        id: el.id,\r\n                        idChart: el.chartId,\r\n                        status: el.status,\r\n                        lastFilter: el.lastFilter\r\n                    }\r\n                });\r\n                me.fillInfoForChartPolicy();\r\n                me.prepageDataShow();\r\n                me.messageCommonService.offload();\r\n            }, null, (typeFinish) => {\r\n                if(typeFinish == \"error\"){\r\n                    me.messageCommonService.offload();\r\n                }\r\n            })\r\n        },null, (typeFinally) => {\r\n            if(typeFinally==\"error\"){\r\n                me.messageCommonService.offload();\r\n            }\r\n        })\r\n    }\r\n\r\n    fillInfoForChartPolicy(){\r\n        let me = this;\r\n        this.charts.forEach(chartConfig => {\r\n            let index = me.listChartPolicy.findIndex(el => el.idChart == chartConfig.id);\r\n            if(index != null && index != undefined && index >= 0){\r\n                me.listChartPolicy[index].chartConfig = chartConfig;\r\n            }else{\r\n                me.listChartPolicy.push({\r\n                    id: null,\r\n                    idChart: chartConfig.id,\r\n                    configLevel: null,\r\n                    configPosition: null,\r\n                    status: 1,\r\n                    chartConfig\r\n                })\r\n            }\r\n        })\r\n        this.listChartPolicy = this.listChartPolicy.filter(el => el.chartConfig != null);\r\n        let maxLeft = 12;\r\n        this.listChartPolicy.forEach((chartPolicy, index) => {\r\n            if(chartPolicy.configLevel){\r\n                chartPolicy.configLevelArray = JSON.parse(chartPolicy.configLevel);\r\n            }else{\r\n                chartPolicy.configLevelArray = [{below: 0, above: null}];\r\n            }\r\n            if(chartPolicy.configPosition){\r\n                chartPolicy.configPositionObject = JSON.parse(chartPolicy.configPosition);\r\n            }else{\r\n                chartPolicy.configPositionObject = me.createPositionDefault();\r\n            }\r\n            if (chartPolicy.lastFilter) {\r\n                chartPolicy.lastFilter = JSON.parse(chartPolicy.lastFilter);\r\n            } else {\r\n                chartPolicy.lastFilter = null;\r\n            }\r\n            chartPolicy.configPositionObject.zIndex = index + 1;\r\n            if(!chartPolicy.configPositionObject.widthChart || chartPolicy.configPositionObject.widthChart < 0){\r\n                chartPolicy.configPositionObject.widthChart = 400;\r\n            }\r\n            if(!chartPolicy.configPositionObject.heightChart || chartPolicy.configPositionObject.heightChart < 0){\r\n                chartPolicy.configPositionObject.heightChart = 300;\r\n            }\r\n            if(chartPolicy.configPositionObject.left + chartPolicy.configPositionObject.widthChart > maxLeft && chartPolicy.configPositionObject.left >= 12){\r\n                maxLeft = chartPolicy.configPositionObject.left + chartPolicy.configPositionObject.widthChart;\r\n            }\r\n        });\r\n        this.listChartPolicy.forEach((chartPolicy, index) => {\r\n           if(chartPolicy.configPositionObject.left == 0){\r\n                chartPolicy.configPositionObject.left = maxLeft + 40;\r\n                maxLeft += 80 + chartPolicy.configPositionObject.widthChart;\r\n           }\r\n        });\r\n    }\r\n\r\n    createPositionDefault():PositionConfig{\r\n        return {\r\n            top: 0,\r\n            left: 0,\r\n            widthChart: 300,\r\n            heightChart: 400,\r\n            align: 'justify-content-start', //justify-content-start justify-content-end justify-content-center justify-content-between\r\n            height: 300,\r\n            width: 400,\r\n            indexBudget: 0,\r\n            indexInBudget: 0,\r\n            marginLeft: 0,\r\n            marginRight: 0\r\n        }\r\n    }\r\n\r\n    prepageDataShow(){\r\n        let me = this;\r\n        this.chartShows = [];\r\n        this.listChartPolicy.forEach(chartPolicy => {\r\n            if(chartPolicy.chartConfig){\r\n                me.listDynamicChartController[chartPolicy.chartConfig.id] = new DynamicChartController();\r\n            }\r\n            if(chartPolicy.status == 1){\r\n                me.chartShows.push(chartPolicy.chartConfig);\r\n            }\r\n        })\r\n        setTimeout(() => {\r\n            Object.keys(me.listDynamicChartController).forEach(chartId => {\r\n                me.listDynamicChartController[chartId].reload(false, true);\r\n            })\r\n        })\r\n    }\r\n\r\n    changeChartShow(event){\r\n        let me = this;\r\n        let chartIdShows = (this.chartShows).map(el => el.id);\r\n        this.listChartPolicy.forEach(chartPolicy => {\r\n            if(chartIdShows.includes(chartPolicy.idChart)){\r\n                chartPolicy.status = 1;\r\n                setTimeout(function(){\r\n                    me.listDynamicChartController[chartPolicy.idChart].reloadParam();\r\n                })\r\n            }else{\r\n                chartPolicy.status = 0;\r\n            }\r\n        })\r\n    }\r\n\r\n    saveConfig(){\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        let dataSend = [];\r\n        this.listChartPolicy.forEach(chartPolicy => {\r\n            chartPolicy.configLevel = JSON.stringify(chartPolicy.configLevelArray);\r\n            delete chartPolicy.configPositionObject.heightBox;\r\n            delete chartPolicy.configPositionObject.zIndex;\r\n            delete chartPolicy.configPositionObject.offsetX;\r\n            delete chartPolicy.configPositionObject.offsetY;\r\n            delete chartPolicy.configPositionObject.pageX;\r\n            delete chartPolicy.configPositionObject.pageY;\r\n            chartPolicy.configPosition = JSON.stringify(chartPolicy.configPositionObject);\r\n            dataSend.push({\r\n                id: chartPolicy.id,\r\n                chartId: chartPolicy.idChart,\r\n                userId: me.sessionService.userInfo.id,\r\n                status: chartPolicy.status,\r\n                threshold: chartPolicy.configLevel,\r\n                positionConfig: chartPolicy.configPosition,\r\n                lastFilter: typeof chartPolicy.lastFilter == 'string' ? chartPolicy.lastFilter : JSON.stringify(chartPolicy.lastFilter).toString(),\r\n            });\r\n        })\r\n        this.configChartService.saveConfigDashboard(dataSend, (response)=> {\r\n            me.messageCommonService.success(me.tranService.translate('global.message.success'))\r\n            me.modeView = CONSTANTS.MODE_VIEW.DETAIL;\r\n            me.messageCommonService.offload();\r\n            window.location.reload();\r\n        }, null, () => me.messageCommonService.offload());\r\n        // console.log(this.listChartPolicy)\r\n    }\r\n\r\n    openEdit(){\r\n        this.modeView = CONSTANTS.MODE_VIEW.UPDATE;\r\n    }\r\n\r\n    handleOpenSetting(chartId){\r\n        let me = this;\r\n        this.chartPolicyForcus = this.listChartPolicy[this.listChartPolicy.findIndex(el => el.idChart == chartId)];\r\n        let subTypes = JSON.parse(this.chartPolicyForcus.chartConfig.subType)\r\n        let threshold = JSON.parse(this.chartPolicyForcus.configLevel)\r\n        if(threshold?.length > 0 && subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD) && threshold[0]?.below != null && threshold[0]?.above != null){\r\n            me.sliderThreshold[0] = threshold[0].below;\r\n            me.sliderThreshold[1] = threshold[0].above;\r\n        }\r\n        setTimeout(function(){\r\n            if (subTypes.includes(CONSTANTS.CHART_SUB_TYPE.SLIDER_THRESHOLD)){\r\n                    me.isShowEditThresholdSetting = true;\r\n            } else {\r\n                me.isShowEditSetting = true;\r\n            }\r\n        })\r\n    }\r\n\r\n    createSliderThreshold() {\r\n        this.chartPolicyForcus.configLevelArray = [{below: this.sliderThreshold[0], above: this.sliderThreshold[1]}]\r\n        this.isShowEditThresholdSetting = false\r\n        this.chartPolicyForcus = null;\r\n    }\r\n\r\n    minusSetting(){\r\n        this.chartPolicyForcus.configLevelArray.pop();\r\n        if(this.chartPolicyForcus.configLevelArray.length == 1){\r\n            this.chartPolicyForcus.configLevelArray[0].above = null;\r\n        }\r\n    }\r\n\r\n    createSetting(){\r\n        let lastThreshold = this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1];\r\n        if(lastThreshold!=null && lastThreshold.above > 0 && lastThreshold.above > lastThreshold.below){\r\n            this.chartPolicyForcus.configLevelArray.push({\r\n                below: this.chartPolicyForcus.configLevelArray[this.chartPolicyForcus.configLevelArray.length - 1].above\r\n            });\r\n        }\r\n    }\r\n\r\n    closeDialog(){\r\n        this.chartPolicyForcus = null;\r\n    }\r\n\r\n    onFilterChartname(event){\r\n        let me = this;\r\n        let filter = event.filter || '';\r\n        if(filter.length > 0){\r\n            me.displayCharts = me.charts.filter(chart => me.utilService.convertTextViToEnUpperCase(chart.name).indexOf(me.utilService.convertTextViToEnUpperCase(filter)) >= 0);\r\n            console.log(me.displayCharts);\r\n        }\r\n    }\r\n\r\n    //drag drop\r\n    // onResize(event){\r\n       // this.calculateBorderline();\r\n    //}\r\n    calculateSize(){\r\n        let me = this;\r\n        let maxHeight = 100;\r\n        let maxWidth = 100;\r\n        this.listChartPolicy.forEach(chartPolicy => {\r\n            let el: ElementRef = me.eRef.nativeElement.querySelector(`#chart${chartPolicy.idChart}`);\r\n            if(el){\r\n                if(el[\"offsetTop\"] + el[\"offsetHeight\"] > maxHeight){\r\n                    maxHeight = el[\"offsetTop\"] + el[\"offsetHeight\"]\r\n                }\r\n                if(el[\"offsetLeft\"] + el[\"offsetWidth\"] > maxWidth){\r\n                    maxWidth = el[\"offsetLeft\"] + el[\"offsetWidth\"]\r\n                }\r\n            }\r\n        })\r\n        if(this.modeView == CONSTANTS.MODE_VIEW.UPDATE){\r\n            maxHeight += 50;\r\n            maxWidth += 50;\r\n        }\r\n        if(!this.chartPolicyDragged){\r\n            this.heightBoxWrapper = maxHeight;\r\n            this.widthBoxWrapper = maxWidth;\r\n        }else{\r\n            if(maxHeight > me.heightBoxWrapper){\r\n                this.heightBoxWrapper = maxHeight+50;\r\n            }\r\n            if(maxWidth > me.widthBoxWrapper){\r\n                this.widthBoxWrapper = maxWidth+50;\r\n            }\r\n        }\r\n    }\r\n    calculateBorderline(){\r\n        let boxWrapper:Element = this.eRef.nativeElement.querySelector(\"#boxWrapper\");\r\n        this.minX = boxWrapper.getBoundingClientRect().left;\r\n        this.minY = boxWrapper.getBoundingClientRect().top;\r\n    }\r\n\r\n    drop(event){\r\n        if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL && this.chartPolicyDragged){\r\n            this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.opacity = 1;\r\n        };\r\n        this.typeDrag = null;\r\n        this.chartPolicyDragged = null;\r\n    }\r\n\r\n    dragEnd(event){\r\n        if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL && this.chartPolicyDragged){\r\n            // this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.opacity = 1;\r\n        };\r\n    }\r\n\r\n    dragStart(event, chartPolicy){\r\n        if(!this.typeDrag){\r\n            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){\r\n                this.typeDrag = null;\r\n                this.chartPolicyDragged = chartPolicy;\r\n                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\r\n                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\r\n                this.chartPolicyDragged.configPositionObject.pageX = event.pageX;\r\n                this.chartPolicyDragged.configPositionObject.pageY = event.pageY;\r\n                // this.eRef.nativeElement.querySelector(`div#chart${this.chartPolicyDragged.idChart}`).style.backgroundColor = 'transparent';\r\n            }\r\n        }\r\n    }\r\n\r\n    dragXStart(event, chartPolicy){\r\n        if(!this.typeDrag){\r\n            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){\r\n                this.chartPolicyDragged = chartPolicy;\r\n                let el: Element = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);\r\n                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\r\n                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\r\n                this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;\r\n                this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;\r\n                this.typeDrag = 'scale-x';\r\n            }\r\n        }\r\n    }\r\n\r\n    dragYStart(event, chartPolicy){\r\n        if(!this.typeDrag){\r\n            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){\r\n                this.chartPolicyDragged = chartPolicy;\r\n                let el: Element = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);\r\n                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\r\n                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\r\n                this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;\r\n                this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;\r\n                this.chartPolicyDragged.configPositionObject.heightBox = el.getBoundingClientRect().height;\r\n                this.typeDrag = 'scale-y';\r\n            }\r\n        }\r\n    }\r\n\r\n    dragXYStart(event, chartPolicy){\r\n        if(!this.typeDrag){\r\n            if(this.modeView != CONSTANTS.MODE_VIEW.DETAIL){\r\n                this.chartPolicyDragged = chartPolicy;\r\n                let el: Element = this.eRef.nativeElement.querySelector(`#chart${this.chartPolicyDragged.idChart}`);\r\n                this.chartPolicyDragged.configPositionObject.offsetX = event.offsetX;\r\n                this.chartPolicyDragged.configPositionObject.offsetY = event.offsetY;\r\n                this.chartPolicyDragged.configPositionObject.pageX = el.getBoundingClientRect().left;\r\n                this.chartPolicyDragged.configPositionObject.pageY = el.getBoundingClientRect().top;\r\n                this.chartPolicyDragged.configPositionObject.heightBox = el.getBoundingClientRect().height;\r\n                this.typeDrag = 'scale-xy';\r\n            }\r\n        }\r\n    }\r\n\r\n    drag(event){\r\n        if(!this.typeDrag && this.chartPolicyDragged){\r\n            if(!this.typeDrag){\r\n                if(event.offsetX < 0 || event.offsetY < 0) return;\r\n                    let x = event.pageX - this.chartPolicyDragged.configPositionObject.offsetX;\r\n                    let y = event.pageY - this.chartPolicyDragged.configPositionObject.offsetY;\r\n                    let top = y - this.minY;\r\n                    let left = x - this.minX;\r\n                    console.log(x, y, this.minX, this.minY, top, left);\r\n                if(top >= 12 && left >= 12){\r\n                    this.chartPolicyDragged.configPositionObject.top = top;\r\n                    this.chartPolicyDragged.configPositionObject.left = left;\r\n                    return;\r\n                }else if(top >= 12){\r\n                    this.chartPolicyDragged.configPositionObject.top = top;\r\n                }else if(left >= 12){\r\n                    this.chartPolicyDragged.configPositionObject.left = left;\r\n                }\r\n                event.preventDefault();\r\n            }\r\n        }\r\n    }\r\n\r\n    dragX(event){\r\n        if(this.chartPolicyDragged){\r\n            this.chartPolicyDragged.configPositionObject.widthChart = event.clientX - this.chartPolicyDragged.configPositionObject.pageX - 40;\r\n        }\r\n    }\r\n\r\n    dragY(event){\r\n        if(this.chartPolicyDragged){\r\n            let heightBox = event.clientY - this.chartPolicyDragged.configPositionObject.pageY;\r\n            this.chartPolicyDragged.configPositionObject.heightChart += (heightBox - this.chartPolicyDragged.configPositionObject.heightBox);\r\n            this.chartPolicyDragged.configPositionObject.heightBox = heightBox;\r\n            console.log(event, this.chartPolicyDragged.configPositionObject)\r\n        }\r\n    }\r\n\r\n    dragXY(event){\r\n        if(this.chartPolicyDragged){\r\n            this.chartPolicyDragged.configPositionObject.widthChart = event.clientX - this.chartPolicyDragged.configPositionObject.pageX - 40;\r\n            let heightBox = event.clientY - this.chartPolicyDragged.configPositionObject.pageY;\r\n            this.chartPolicyDragged.configPositionObject.heightChart += (heightBox - this.chartPolicyDragged.configPositionObject.heightBox);\r\n            this.chartPolicyDragged.configPositionObject.heightBox = heightBox;\r\n        }\r\n    }\r\n}\r\n", "<style>\r\n    :host ::ng-deep {\r\n        [pDraggable] {\r\n            cursor: move;\r\n            .button-scale-x {\r\n                cursor: e-resize !important;\r\n            }\r\n            .button-scale-y {\r\n                cursor: n-resize !important;\r\n            }\r\n            .button-scale-z {\r\n                cursor: se-resize !important;\r\n            }\r\n        }\r\n    }\r\n</style>\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">Dashboard</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div>\r\n        <p-toolbar styleClass=\"border-none bg-white\" [style]=\"{padding: 0}\">\r\n            <div class=\"p-toolbar-group-start\">\r\n\r\n            </div>\r\n            <div class=\"p-toolbar-group-center\">\r\n                <!-- <p-multiSelect *ngIf=\"modeView == objectModeView.UPDATE\" class=\"custom-dropdown-right\"\r\n                                [overlayVisible]=\"false\"\r\n                                [style]=\"{maxWidth:'150px'}\"\r\n                                [options]=\"displayCharts\"\r\n                                [(ngModel)]=\"chartShows\"\r\n                                optionLabel=\"name\"\r\n                                filterLocale=\"vi\"\r\n                                (ngModelChange)=\"changeChartShow()\"\r\n                                (onFilter)=\"onFilterChartname($event)\"\r\n                ></p-multiSelect> -->\r\n                <vnpt-select *ngIf=\"modeView == objectModeView.UPDATE\"\r\n                    [control]=\"chartComboboxController\"\r\n                    [(value)]=\"chartShows\"\r\n                    (onchange)=\"changeChartShow($event)\"\r\n                    [isAutoComplete]=\"false\"\r\n                    [isMultiChoice]=\"true\"\r\n                    [options]=\"displayCharts\"\r\n                    paramKey=\"name\"\r\n                    keyReturn=\"id\"\r\n                    displayPattern=\"${name}\"\r\n                    [lazyLoad]=\"false\"\r\n                    [isFilterLocal]=\"true\"\r\n                    typeValue=\"object\"\r\n                    [style]=\"{maxWidth: '250px', minWidth: '250px'}\"\r\n                    [stylePositionBoxSelect]=\"getBoxSelectStyle()\"\r\n                    styleClass=\"vnpt-select-dashboard\"\r\n                >\r\n                </vnpt-select>\r\n            </div>\r\n            <div class=\"p-toolbar-group-end\">\r\n                <p-button *ngIf=\"modeView == objectModeView.UPDATE\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"saveConfig()\"></p-button>\r\n                <p-button *ngIf=\"modeView == objectModeView.DETAIL\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.edit')\" (click)=\"openEdit()\"></p-button>\r\n            </div>\r\n        </p-toolbar>\r\n    </div>\r\n</div>\r\n\r\n<div id=\"boxWrapper\" class=\"relative mt-2\" [style]=\"{\r\n    minWidth: '100%',\r\n    height: (heightBoxWrapper + 24)+'px',\r\n    width: (widthBoxWrapper + 24) + 'px',\r\n    padding: '12px',\r\n    border: modeView == objectModeView.DETAIL ? 'none' : '1px dashed #777777',\r\n    backgroundColor: modeView == objectModeView.DETAIL ? 'transparent' : '#CCCCCC',\r\n    position: 'relative',\r\n    boxSizing: 'border-box'\r\n}\" pDroppable (onDrop)=\"drop($event)\">\r\n    <!-- zIndex: chartPolicy.configPositionObject.zIndex,-->\r\n    <div *ngFor=\"let chartPolicy of listChartPolicy;let i = index\" pDraggable\r\n        (onDragStart)=\"dragStart($event, chartPolicy)\"\r\n        (onDragEnd)=\"dragEnd($event)\" (onDrag)=\"drag($event)\"\r\n        class=\"box-chart\"\r\n         [ngClass]=\"{ 'stacked-mode': isStackedView }\"\r\n         [style]=\"!isStackedView ? {\r\n              position: 'absolute',\r\n              top: chartPolicy.configPositionObject.top + 'px',\r\n              left: chartPolicy.configPositionObject.left + 'px',\r\n              border: modeView === objectModeView.DETAIL ? 'none' : '1px dashed gray'\r\n            } : null\"\r\n        [id]=\"'chart'+chartPolicy.idChart\"\r\n    >\r\n        <div *ngIf=\"chartPolicy.chartConfig && chartPolicy.status == 1\" class=\"w-full\" style=\"height: 100%;position: relative;\">\r\n            <dynamic-chart-vnpt\r\n                [mode]=\"modeView\"\r\n                [chartConfig]=\"chartPolicy.chartConfig\"\r\n                [chartPolicy]=\"chartPolicy\"\r\n                [control]=\"listDynamicChartController[chartPolicy.chartConfig.id]\"\r\n                [width]=\"chartPolicy.configPositionObject.widthChart\"\r\n                [height]=\"chartPolicy.configPositionObject.heightChart\"\r\n                [handleOpenSetting]=\"handleOpenSetting.bind(this)\"\r\n                [isShowButtonExtra]=\"modeView == objectModeView.UPDATE\"\r\n            ></dynamic-chart-vnpt>\r\n            <div *ngIf=\"modeView == objectModeView.UPDATE\" [style.zIndex]=\"100 + chartPolicy.configPositionObject.zIndex + 1\" class=\"button-scale-x\" dragEffect=\"move\"\r\n                pDraggable (onDragStart)=\"dragXStart($event, chartPolicy)\" (onDragEnd)=\"dragEnd($event)\" (onDrag)=\"dragX($event)\"\r\n                style=\"width: 10px;height: 100%; position: absolute; top: 0;left: calc(100% - 5px);background-color: transparent;\"></div>\r\n\r\n            <div *ngIf=\"modeView == objectModeView.UPDATE\" [style.zIndex]=\"100 + chartPolicy.configPositionObject.zIndex + 1\" class=\"button-scale-y\" pDraggable dragEffect=\"move\"\r\n            (onDragStart)=\"dragYStart($event, chartPolicy)\" (onDragEnd)=\"dragEnd($event)\" (onDrag)=\"dragY($event)\"\r\n             style=\"width: 100%;height: 10px; position: absolute; left: 0;top: calc(100% - 5px);background-color: transparent;\"></div>\r\n\r\n            <div *ngIf=\"modeView == objectModeView.UPDATE\" [style.zIndex]=\"100 + chartPolicy.configPositionObject.zIndex + 2\" class=\"button-scale-z\" pDraggable dragEffect=\"move\"\r\n            (onDragStart)=\"dragXYStart($event, chartPolicy)\" (onDragEnd)=\"dragEnd($event)\" (onDrag)=\"dragXY($event)\"\r\n            style=\"width: 10px;height: 10px; position: absolute; top: calc(100% - 5px);left: calc(100% - 5px);background-color: transparent;\">\r\n\r\n            </div>\r\n            <div *ngIf=\"modeView == objectModeView.UPDATE\" class=\"button-scale-z\" style=\"width: 10px;height: 10px; position: absolute; top: calc(100% - 10px);left: calc(100% - 10px);background-color: transparent;\">\r\n                <i class=\"pi pi-chevron-down\" style=\"font-size: 10px;\r\n                transform: rotate(-45deg);\r\n                vertical-align: top;\"></i>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<p-dialog (onHide)=\"closeDialog()\" [header]=\"tranService.translate('chart.label.thresholdConfig')\" [(visible)]=\"isShowEditSetting\" [modal]=\"true\" [style]=\"{ width: '700px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n    <div class=\"w-full field grid p-0 m-0\" *ngIf=\"chartPolicyForcus\">\r\n        <div class=\"w-full field grid\" *ngFor=\"let threshold of chartPolicyForcus.configLevelArray;let i = index\">\r\n            <label htmlFor=\"threshold\" class=\"col-fixed\" style=\"width:150px;max-width: 150px;overflow: hidden; text-overflow: ellipsis;\">\r\n                {{tranService.translate(\"chart.label.threshold\")}}&nbsp;{{i + 1}}\r\n            </label>\r\n            <div class=\"col flex flex-row flex-wrap justify-content-start align-items-center\">\r\n                <p-inputNumber\r\n                    class=\"mr-2\" disabled=\"true\"\r\n                    [(ngModel)]=\"chartPolicyForcus.configLevelArray[i].below\"\r\n                    inputId=\"minmax\" mode=\"decimal\" [disabled]=\"true\"\r\n                > </p-inputNumber>\r\n                <p-inputNumber\r\n                    class=\"mr-2\"\r\n                    [(ngModel)]=\"chartPolicyForcus.configLevelArray[i].above\"\r\n                    inputId=\"minmax\" mode=\"decimal\" [disabled]=\"i < (chartPolicyForcus.configLevelArray.length - 1) || (chartPolicyForcus.configLevelArray || []).length >= 10\"\r\n                    [min]=\"chartPolicyForcus.configLevelArray[i].below + 1\"\r\n                > </p-inputNumber>\r\n                <p-button (click)=\"minusSetting()\" styleClass=\"mr-2 p-button-info\" icon=\"pi pi-minus\" *ngIf=\"i != 0 && i == chartPolicyForcus.configLevelArray.length - 1\"></p-button>\r\n                <p-button (click)=\"createSetting()\" icon=\"pi pi-plus\" styleClass=\"p-button-info\" *ngIf=\"(i == chartPolicyForcus.configLevelArray.length - 1) && (chartPolicyForcus.configLevelArray || []).length < 10\" [disabled]=\"!chartPolicyForcus.configLevelArray[i].above || chartPolicyForcus.configLevelArray[i].above <= chartPolicyForcus.configLevelArray[i].below\"></p-button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</p-dialog>\r\n<p-dialog (onHide)=\"closeDialog()\" [header]=\"tranService.translate('chart.label.thresholdConfig')\" [(visible)]=\"isShowEditThresholdSetting\" [modal]=\"true\" [style]=\"{ width: '700px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n    <div class=\"w-full field grid p-0 m-0\" *ngIf=\"chartPolicyForcus\">\r\n        <div class=\"w-full field grid\" *ngFor=\"let threshold of chartPolicyForcus.configLevelArray; let i = index\">\r\n            <label htmlFor=\"threshold\" class=\"col-fixed\" style=\"width:150px; max-width: 150px; overflow: hidden; text-overflow: ellipsis;\">\r\n                {{tranService.translate(\"chart.label.threshold\")}}\r\n            </label>\r\n\r\n            <div class=\"col flex flex-row flex-wrap justify-content-start align-items-center\">\r\n\r\n                <p-slider [(ngModel)]=\"sliderThreshold\" [step]=\"5\" class=\"col-8 mt-3\"\r\n                          [range]=\"true\"\r\n                          [min]=\"0\"\r\n                          [max]=\"100\"\r\n                ></p-slider>\r\n\r\n                <lable>{{ sliderThreshold[0] + '% - ' + sliderThreshold[1] + '%'}}</lable>\r\n\r\n\r\n                <p-button (onClick)=\"createSliderThreshold()\" styleClass=\"p-button-info ml-3 \" [label]=\"tranService.translate('global.button.add2')\">\r\n                </p-button>\r\n\r\n            </div>\r\n        </div>\r\n    </div>\r\n</p-dialog>\r\n\r\n"], "mappings": "AAEA,SAASA,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAASC,sBAAsB,QAAQ,iDAAiD;AAExF,SAASC,gBAAgB,QAAQ,sDAAsD;;;;;;;;;;;;;;;;;;;;;;;IC8BvEC,EAAA,CAAAC,cAAA,sBAgBC;IAdGD,EAAA,CAAAE,UAAA,yBAAAC,iFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,UAAA,GAAAN,MAAA;IAAA,EAAsB,sBAAAO,8EAAAP,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAZ,EAAA,CAAAQ,aAAA;MAAA,OACVR,EAAA,CAAAS,WAAA,CAAAG,MAAA,CAAAC,eAAA,CAAAT,MAAA,CAAuB;IAAA,EADb;IAe1BJ,EAAA,CAAAc,YAAA,EAAc;;;;IAJVd,EAAA,CAAAe,UAAA,CAAAf,EAAA,CAAAgB,eAAA,KAAAC,GAAA,EAAgD;IAZhDjB,EAAA,CAAAkB,UAAA,YAAAC,MAAA,CAAAC,uBAAA,CAAmC,UAAAD,MAAA,CAAAT,UAAA,6DAAAS,MAAA,CAAAE,aAAA,sEAAAF,MAAA,CAAAG,iBAAA;;;;;;IAmBvCtB,EAAA,CAAAC,cAAA,mBAA4J;IAAvBD,EAAA,CAAAE,UAAA,mBAAAqB,sEAAA;MAAAvB,EAAA,CAAAK,aAAA,CAAAmB,IAAA;MAAA,MAAAC,MAAA,GAAAzB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAgB,MAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAAC1B,EAAA,CAAAc,YAAA,EAAW;;;;IAAxFd,EAAA,CAAAkB,UAAA,UAAAS,MAAA,CAAAC,WAAA,CAAAC,SAAA,uBAAqD;;;;;;IACpI7B,EAAA,CAAAC,cAAA,mBAA0J;IAArBD,EAAA,CAAAE,UAAA,mBAAA4B,sEAAA;MAAA9B,EAAA,CAAAK,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAuB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAACjC,EAAA,CAAAc,YAAA,EAAW;;;;IAAtFd,EAAA,CAAAkB,UAAA,UAAAgB,MAAA,CAAAN,WAAA,CAAAC,SAAA,uBAAqD;;;;;;IAyCxI7B,EAAA,CAAAC,cAAA,cAEuH;IADxGD,EAAA,CAAAE,UAAA,yBAAAiC,8EAAA/B,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+B,IAAA;MAAA,MAAAC,eAAA,GAAArC,EAAA,CAAAQ,aAAA,IAAA8B,SAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAA8B,OAAA,CAAAC,UAAA,CAAApC,MAAA,EAAAiC,eAAA,CAA+B;IAAA,EAAC,uBAAAI,4EAAArC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+B,IAAA;MAAA,MAAAM,OAAA,GAAA1C,EAAA,CAAAQ,aAAA;MAAA,OAAcR,EAAA,CAAAS,WAAA,CAAAiC,OAAA,CAAAC,OAAA,CAAAvC,MAAA,CAAe;IAAA,EAA7B,oBAAAwC,yEAAAxC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+B,IAAA;MAAA,MAAAS,OAAA,GAAA7C,EAAA,CAAAQ,aAAA;MAAA,OAAyCR,EAAA,CAAAS,WAAA,CAAAoC,OAAA,CAAAC,KAAA,CAAA1C,MAAA,CAAa;IAAA,EAAtD;IACyDJ,EAAA,CAAAc,YAAA,EAAM;;;;IAF9Ed,EAAA,CAAA+C,WAAA,kBAAAV,eAAA,CAAAW,oBAAA,CAAAC,MAAA,KAAkE;;;;;;IAIjHjD,EAAA,CAAAC,cAAA,cAEoH;IADpHD,EAAA,CAAAE,UAAA,yBAAAgD,8EAAA9C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA8C,IAAA;MAAA,MAAAd,eAAA,GAAArC,EAAA,CAAAQ,aAAA,IAAA8B,SAAA;MAAA,MAAAc,OAAA,GAAApD,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAA2C,OAAA,CAAAC,UAAA,CAAAjD,MAAA,EAAAiC,eAAA,CAA+B;IAAA,EAAC,uBAAAiB,4EAAAlD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA8C,IAAA;MAAA,MAAAI,OAAA,GAAAvD,EAAA,CAAAQ,aAAA;MAAA,OAAcR,EAAA,CAAAS,WAAA,CAAA8C,OAAA,CAAAZ,OAAA,CAAAvC,MAAA,CAAe;IAAA,EAA7B,oBAAAoD,yEAAApD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA8C,IAAA;MAAA,MAAAM,OAAA,GAAAzD,EAAA,CAAAQ,aAAA;MAAA,OAAyCR,EAAA,CAAAS,WAAA,CAAAgD,OAAA,CAAAC,KAAA,CAAAtD,MAAA,CAAa;IAAA,EAAtD;IACqEJ,EAAA,CAAAc,YAAA,EAAM;;;;IAF3Ed,EAAA,CAAA+C,WAAA,kBAAAV,eAAA,CAAAW,oBAAA,CAAAC,MAAA,KAAkE;;;;;;IAIjHjD,EAAA,CAAAC,cAAA,cAEkI;IADlID,EAAA,CAAAE,UAAA,yBAAAyD,8EAAAvD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuD,IAAA;MAAA,MAAAvB,eAAA,GAAArC,EAAA,CAAAQ,aAAA,IAAA8B,SAAA;MAAA,MAAAuB,OAAA,GAAA7D,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAAoD,OAAA,CAAAC,WAAA,CAAA1D,MAAA,EAAAiC,eAAA,CAAgC;IAAA,EAAC,uBAAA0B,4EAAA3D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuD,IAAA;MAAA,MAAAI,OAAA,GAAAhE,EAAA,CAAAQ,aAAA;MAAA,OAAcR,EAAA,CAAAS,WAAA,CAAAuD,OAAA,CAAArB,OAAA,CAAAvC,MAAA,CAAe;IAAA,EAA7B,oBAAA6D,yEAAA7D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuD,IAAA;MAAA,MAAAM,OAAA,GAAAlE,EAAA,CAAAQ,aAAA;MAAA,OAAyCR,EAAA,CAAAS,WAAA,CAAAyD,OAAA,CAAAC,MAAA,CAAA/D,MAAA,CAAc;IAAA,EAAvD;IAGhDJ,EAAA,CAAAc,YAAA,EAAM;;;;IAJyCd,EAAA,CAAA+C,WAAA,kBAAAV,eAAA,CAAAW,oBAAA,CAAAC,MAAA,KAAkE;;;;;IAKjHjD,EAAA,CAAAC,cAAA,cAA0M;IACtMD,EAAA,CAAAoE,SAAA,YAE0B;IAC9BpE,EAAA,CAAAc,YAAA,EAAM;;;;;IA5BVd,EAAA,CAAAC,cAAA,cAAwH;IACpHD,EAAA,CAAAoE,SAAA,6BASsB;IACtBpE,EAAA,CAAAqE,UAAA,IAAAC,kDAAA,kBAE6H;IAE7HtE,EAAA,CAAAqE,UAAA,IAAAE,kDAAA,kBAE0H;IAE1HvE,EAAA,CAAAqE,UAAA,IAAAG,kDAAA,kBAIM;IACNxE,EAAA,CAAAqE,UAAA,IAAAI,kDAAA,kBAIM;IACVzE,EAAA,CAAAc,YAAA,EAAM;;;;;IA3BEd,EAAA,CAAA0E,SAAA,GAAiB;IAAjB1E,EAAA,CAAAkB,UAAA,SAAAyD,OAAA,CAAAC,QAAA,CAAiB,gBAAAvC,eAAA,CAAAwC,WAAA,iBAAAxC,eAAA,aAAAsC,OAAA,CAAAG,0BAAA,CAAAzC,eAAA,CAAAwC,WAAA,CAAAE,EAAA,YAAA1C,eAAA,CAAAW,oBAAA,CAAAgC,UAAA,YAAA3C,eAAA,CAAAW,oBAAA,CAAAiC,WAAA,uBAAAN,OAAA,CAAAO,iBAAA,CAAAC,IAAA,CAAAR,OAAA,wBAAAA,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAS,cAAA,CAAAC,MAAA;IASfrF,EAAA,CAAA0E,SAAA,GAAuC;IAAvC1E,EAAA,CAAAkB,UAAA,SAAAyD,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAS,cAAA,CAAAC,MAAA,CAAuC;IAIvCrF,EAAA,CAAA0E,SAAA,GAAuC;IAAvC1E,EAAA,CAAAkB,UAAA,SAAAyD,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAS,cAAA,CAAAC,MAAA,CAAuC;IAIvCrF,EAAA,CAAA0E,SAAA,GAAuC;IAAvC1E,EAAA,CAAAkB,UAAA,SAAAyD,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAS,cAAA,CAAAC,MAAA,CAAuC;IAKvCrF,EAAA,CAAA0E,SAAA,GAAuC;IAAvC1E,EAAA,CAAAkB,UAAA,SAAAyD,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAS,cAAA,CAAAC,MAAA,CAAuC;;;;;;;;;;;;;;;;;;;IArCrDrF,EAAA,CAAAC,cAAA,cAYC;IAXGD,EAAA,CAAAE,UAAA,yBAAAoF,kEAAAlF,MAAA;MAAA,MAAAmF,WAAA,GAAAvF,EAAA,CAAAK,aAAA,CAAAmF,IAAA;MAAA,MAAAnD,eAAA,GAAAkD,WAAA,CAAAjD,SAAA;MAAA,MAAAmD,OAAA,GAAAzF,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAAgF,OAAA,CAAAC,SAAA,CAAAtF,MAAA,EAAAiC,eAAA,CAA8B;IAAA,EAAC,uBAAAsD,gEAAAvF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmF,IAAA;MAAA,MAAAI,OAAA,GAAA5F,EAAA,CAAAQ,aAAA;MAAA,OACjCR,EAAA,CAAAS,WAAA,CAAAmF,OAAA,CAAAjD,OAAA,CAAAvC,MAAA,CAAe;IAAA,EADkB,oBAAAyF,6DAAAzF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmF,IAAA;MAAA,MAAAM,OAAA,GAAA9F,EAAA,CAAAQ,aAAA;MAAA,OACNR,EAAA,CAAAS,WAAA,CAAAqF,OAAA,CAAAC,IAAA,CAAA3F,MAAA,CAAY;IAAA,EADN;IAY9CJ,EAAA,CAAAqE,UAAA,IAAA2B,4CAAA,mBA6BM;IACVhG,EAAA,CAAAc,YAAA,EAAM;;;;;IAtCDd,EAAA,CAAAe,UAAA,EAAAkF,MAAA,CAAAC,aAAA,GAAAlG,EAAA,CAAAmG,eAAA,IAAAC,GAAA,EAAA/D,eAAA,CAAAW,oBAAA,CAAAqD,GAAA,SAAAhE,eAAA,CAAAW,oBAAA,CAAAsD,IAAA,SAAAL,MAAA,CAAArB,QAAA,KAAAqB,MAAA,CAAAb,cAAA,CAAAmB,MAAA,sCAKY;IANZvG,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwG,eAAA,IAAAC,GAAA,EAAAR,MAAA,CAAAC,aAAA,EAA6C,iBAAA7D,eAAA,CAAAqE,OAAA;IASxC1G,EAAA,CAAA0E,SAAA,GAAwD;IAAxD1E,EAAA,CAAAkB,UAAA,SAAAmB,eAAA,CAAAwC,WAAA,IAAAxC,eAAA,CAAAsE,MAAA,MAAwD;;;;;;IAgEtD3G,EAAA,CAAAC,cAAA,mBAA2J;IAAjJD,EAAA,CAAAE,UAAA,mBAAA0G,kFAAA;MAAA5G,EAAA,CAAAK,aAAA,CAAAwG,IAAA;MAAA,MAAAC,OAAA,GAAA9G,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAqG,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAAyH/G,EAAA,CAAAc,YAAA,EAAW;;;;;;IACtKd,EAAA,CAAAC,cAAA,mBAAgW;IAAtVD,EAAA,CAAAE,UAAA,mBAAA8G,kFAAA;MAAAhH,EAAA,CAAAK,aAAA,CAAA4G,IAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyG,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAA6TnH,EAAA,CAAAc,YAAA,EAAW;;;;;IAAnKd,EAAA,CAAAkB,UAAA,cAAAkG,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAC,KAAA,IAAAJ,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAC,KAAA,IAAAJ,OAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAE,KAAA,CAAuJ;;;;;;;;;IAjBvWzH,EAAA,CAAAC,cAAA,cAA0G;IAElGD,EAAA,CAAA0H,MAAA,GACJ;IAAA1H,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAkF;IAG1ED,EAAA,CAAAE,UAAA,2BAAAyH,oFAAAvH,MAAA;MAAA,MAAAmF,WAAA,GAAAvF,EAAA,CAAAK,aAAA,CAAAuH,IAAA;MAAA,MAAAL,KAAA,GAAAhC,WAAA,CAAAsC,KAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAQ,aAAA;MAAA,OAAaR,EAAA,CAAAS,WAAA,CAAAqH,OAAA,CAAAT,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAE,KAAA,GAAArH,MAAA,CAC5B;IAAA,EADwE;IAE3DJ,EAAA,CAAAc,YAAA,EAAgB;IAClBd,EAAA,CAAAC,cAAA,wBAKC;IAHGD,EAAA,CAAAE,UAAA,2BAAA6H,oFAAA3H,MAAA;MAAA,MAAAmF,WAAA,GAAAvF,EAAA,CAAAK,aAAA,CAAAuH,IAAA;MAAA,MAAAL,KAAA,GAAAhC,WAAA,CAAAsC,KAAA;MAAA,MAAAG,OAAA,GAAAhI,EAAA,CAAAQ,aAAA;MAAA,OAAaR,EAAA,CAAAS,WAAA,CAAAuH,OAAA,CAAAX,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAC,KAAA,GAAApH,MAAA,CAC5B;IAAA,EADwE;IAG3DJ,EAAA,CAAAc,YAAA,EAAgB;IAClBd,EAAA,CAAAqE,UAAA,IAAA4D,uDAAA,uBAAsK;IACtKjI,EAAA,CAAAqE,UAAA,IAAA6D,uDAAA,uBAA2W;IAC/WlI,EAAA,CAAAc,YAAA,EAAM;;;;;IAhBFd,EAAA,CAAA0E,SAAA,GACJ;IADI1E,EAAA,CAAAmI,kBAAA,MAAAC,OAAA,CAAAxG,WAAA,CAAAC,SAAA,qCAAA0F,KAAA,UACJ;IAIQvH,EAAA,CAAA0E,SAAA,GAAyD;IAAzD1E,EAAA,CAAAkB,UAAA,YAAAkH,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAE,KAAA,CAAyD;IAKzDzH,EAAA,CAAA0E,SAAA,GAAyD;IAAzD1E,EAAA,CAAAkB,UAAA,YAAAkH,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAC,KAAA,CAAyD,aAAAD,KAAA,GAAAa,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,CAAAe,MAAA,SAAAD,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,IAAAtH,EAAA,CAAAgB,eAAA,IAAAsH,GAAA,GAAAD,MAAA,eAAAD,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,CAAAC,KAAA,EAAAE,KAAA;IAI0BzH,EAAA,CAAA0E,SAAA,GAAkE;IAAlE1E,EAAA,CAAAkB,UAAA,SAAAqG,KAAA,SAAAA,KAAA,IAAAa,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,CAAAe,MAAA,KAAkE;IACvErI,EAAA,CAAA0E,SAAA,GAAoH;IAApH1E,EAAA,CAAAkB,UAAA,SAAAqG,KAAA,IAAAa,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,CAAAe,MAAA,SAAAD,OAAA,CAAAf,iBAAA,CAAAC,gBAAA,IAAAtH,EAAA,CAAAgB,eAAA,KAAAsH,GAAA,GAAAD,MAAA,MAAoH;;;;;IAlBlNrI,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAqE,UAAA,IAAAkE,4CAAA,mBAmBM;IACVvI,EAAA,CAAAc,YAAA,EAAM;;;;IApBmDd,EAAA,CAAA0E,SAAA,GAAsC;IAAtC1E,EAAA,CAAAkB,UAAA,YAAAsH,MAAA,CAAAnB,iBAAA,CAAAC,gBAAA,CAAsC;;;;;;IAwB3FtH,EAAA,CAAAC,cAAA,cAA2G;IAEnGD,EAAA,CAAA0H,MAAA,GACJ;IAAA1H,EAAA,CAAAc,YAAA,EAAQ;IAERd,EAAA,CAAAC,cAAA,cAAkF;IAEpED,EAAA,CAAAE,UAAA,2BAAAuI,+EAAArI,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAC,OAAA,GAAA3I,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAAkI,OAAA,CAAAC,eAAA,GAAAxI,MAAA;IAAA,EAA6B;IAItCJ,EAAA,CAAAc,YAAA,EAAW;IAEZd,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAA0H,MAAA,GAA2D;IAAA1H,EAAA,CAAAc,YAAA,EAAQ;IAG1Ed,EAAA,CAAAC,cAAA,mBAAqI;IAA3HD,EAAA,CAAAE,UAAA,qBAAA2I,yEAAA;MAAA7I,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAI,OAAA,GAAA9I,EAAA,CAAAQ,aAAA;MAAA,OAAWR,EAAA,CAAAS,WAAA,CAAAqI,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAC7C/I,EAAA,CAAAc,YAAA,EAAW;;;;IAfXd,EAAA,CAAA0E,SAAA,GACJ;IADI1E,EAAA,CAAAgJ,kBAAA,MAAAC,OAAA,CAAArH,WAAA,CAAAC,SAAA,+BACJ;IAIc7B,EAAA,CAAA0E,SAAA,GAA6B;IAA7B1E,EAAA,CAAAkB,UAAA,YAAA+H,OAAA,CAAAL,eAAA,CAA6B;IAMhC5I,EAAA,CAAA0E,SAAA,GAA2D;IAA3D1E,EAAA,CAAAkJ,iBAAA,CAAAD,OAAA,CAAAL,eAAA,eAAAK,OAAA,CAAAL,eAAA,UAA2D;IAGa5I,EAAA,CAAA0E,SAAA,GAAqD;IAArD1E,EAAA,CAAAkB,UAAA,UAAA+H,OAAA,CAAArH,WAAA,CAAAC,SAAA,uBAAqD;;;;;IAjBhJ7B,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAqE,UAAA,IAAA8E,4CAAA,kBAoBM;IACVnJ,EAAA,CAAAc,YAAA,EAAM;;;;IArBmDd,EAAA,CAAA0E,SAAA,GAAuC;IAAvC1E,EAAA,CAAAkB,UAAA,YAAAkI,MAAA,CAAA/B,iBAAA,CAAAC,gBAAA,CAAuC;;;;;;;;;;;;;;;;;;;;;;;;;ADhHpG,OAAM,MAAO+B,sBAAuB,SAAQzJ,aAAa;EA4BrD0J,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAD,aAAaA,CAAA;IACX,IAAI,CAACrD,aAAa,GAAGuD,MAAM,CAACC,UAAU,IAAI,GAAG;EAC/C;EAEA;EACApI,iBAAiBA,CAAA;IACjB,IAAI,IAAI,CAAC4E,aAAa,EAAE;MACpB,OAAO;QACHI,IAAI,EAAE,OAAO;QACbqD,KAAK,EAAE,MAAM;QACbtD,GAAG,EAAE,MAAM;QACXuD,OAAO,EAAE,MAAM;QACf,WAAW,EAAE,MAAM;QACnBC,KAAK,EAAE;OACV;KACJ,MAAM;MACH,OAAO;QACHvD,IAAI,EAAE,OAAO;QACbqD,KAAK,EAAE,MAAM;QACbtD,GAAG,EAAE;OACJ;;EAET;EAGAyD,YAAYC,QAAkB,EAAUC,kBAAsC,EAAUC,WAAwB,EACpGC,IAAgB;IACxB,KAAK,CAACH,QAAQ,CAAC;IAFqB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,WAAW,GAAXA,WAAW;IACvF,KAAAC,IAAI,GAAJA,IAAI;IAxDhB,KAAAC,MAAM,GAAe,EAAE;IACvB,KAAA9I,aAAa,GAAe,EAAE;IAC9B,KAAAX,UAAU,GAAe,EAAE;IAC3B,KAAAkE,QAAQ,GAAQ/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM;IAC1C,KAAAnB,cAAc,GAAGvF,SAAS,CAACuK,SAAS;IACpC,KAAAC,gBAAgB,GAAW,IAAI;IAC/B,KAAAC,eAAe,GAAW,IAAI;IAC9B,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,IAAI,GAAW,EAAE;IAEjB,KAAAC,eAAe,GAAuB,EAAE;IACxC,KAAA3F,0BAA0B,GAAkD,EAAE;IAK9E;IACA,KAAA4F,iBAAiB,GAAY,KAAK;IAElC,KAAAC,0BAA0B,GAAY,KAAK;IAG3C,KAAAzE,aAAa,GAAY,KAAK;EAoC9B;EAEA0E,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,IAAI,GAAG;MAACC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAC;IACjD,IAAI,CAACC,KAAK,GAAG,CAAC;MAACC,KAAK,EAAE;IAAW,CAAC,CAAC;IACnC,IAAI,CAACxK,UAAU,GAAG,EAAE;IACpB,IAAI,CAACyK,YAAY,EAAE;IACnB,IAAI,CAAC3B,mBAAmB,EAAE;IAC1B,IAAI,CAACpI,uBAAuB,GAAG,IAAIrB,gBAAgB,EAAE;IACrD,IAAI,CAAC6I,eAAe,GAAG,CAAC,EAAE,EAAC,EAAE,CAAC;IAC9B,IAAI,CAACW,aAAa,EAAE;EACxB;EAEA6B,qBAAqBA,CAAA;IACjB,IAAI,CAACC,aAAa,EAAE;EACxB;EAEAF,YAAYA,CAAA;IACR,IAAIN,EAAE,GAAG,IAAI;IACbA,EAAE,CAACS,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACvB,kBAAkB,CAACwB,MAAM,CAAEC,QAAQ,IAAG;MACvCZ,EAAE,CAACV,MAAM,GAAGsB,QAAQ;MACpBZ,EAAE,CAACxJ,aAAa,GAAG,CAAC,GAAGwJ,EAAE,CAACV,MAAM,CAAC;MACjC;MACAU,EAAE,CAACb,kBAAkB,CAAC0B,aAAa,CAACb,EAAE,CAACc,cAAc,CAACC,QAAQ,CAAC7G,EAAE,EAAG0G,QAAQ,IAAG;QAC3EZ,EAAE,CAACJ,eAAe,GAAGgB,QAAQ,CAACI,GAAG,CAACC,EAAE,IAAG;UACnC,OAAO;YACHC,WAAW,EAAED,EAAE,CAACE,SAAS;YACzBC,cAAc,EAAEH,EAAE,CAACI,cAAc;YACjCnH,EAAE,EAAE+G,EAAE,CAAC/G,EAAE;YACT2B,OAAO,EAAEoF,EAAE,CAACK,OAAO;YACnBxF,MAAM,EAAEmF,EAAE,CAACnF,MAAM;YACjByF,UAAU,EAAEN,EAAE,CAACM;WAClB;QACL,CAAC,CAAC;QACFvB,EAAE,CAACwB,sBAAsB,EAAE;QAC3BxB,EAAE,CAACyB,eAAe,EAAE;QACpBzB,EAAE,CAACS,oBAAoB,CAACiB,OAAO,EAAE;MACrC,CAAC,EAAE,IAAI,EAAGC,UAAU,IAAI;QACpB,IAAGA,UAAU,IAAI,OAAO,EAAC;UACrB3B,EAAE,CAACS,oBAAoB,CAACiB,OAAO,EAAE;;MAEzC,CAAC,CAAC;IACN,CAAC,EAAC,IAAI,EAAGE,WAAW,IAAI;MACpB,IAAGA,WAAW,IAAE,OAAO,EAAC;QACpB5B,EAAE,CAACS,oBAAoB,CAACiB,OAAO,EAAE;;IAEzC,CAAC,CAAC;EACN;EAEAF,sBAAsBA,CAAA;IAClB,IAAIxB,EAAE,GAAG,IAAI;IACb,IAAI,CAACV,MAAM,CAACuC,OAAO,CAAC7H,WAAW,IAAG;MAC9B,IAAIgD,KAAK,GAAGgD,EAAE,CAACJ,eAAe,CAACkC,SAAS,CAACb,EAAE,IAAIA,EAAE,CAACpF,OAAO,IAAI7B,WAAW,CAACE,EAAE,CAAC;MAC5E,IAAG8C,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI+E,SAAS,IAAI/E,KAAK,IAAI,CAAC,EAAC;QACjDgD,EAAE,CAACJ,eAAe,CAAC5C,KAAK,CAAC,CAAChD,WAAW,GAAGA,WAAW;OACtD,MAAI;QACDgG,EAAE,CAACJ,eAAe,CAACoC,IAAI,CAAC;UACpB9H,EAAE,EAAE,IAAI;UACR2B,OAAO,EAAE7B,WAAW,CAACE,EAAE;UACvBgH,WAAW,EAAE,IAAI;UACjBE,cAAc,EAAE,IAAI;UACpBtF,MAAM,EAAE,CAAC;UACT9B;SACH,CAAC;;IAEV,CAAC,CAAC;IACF,IAAI,CAAC4F,eAAe,GAAG,IAAI,CAACA,eAAe,CAACqC,MAAM,CAAChB,EAAE,IAAIA,EAAE,CAACjH,WAAW,IAAI,IAAI,CAAC;IAChF,IAAIkI,OAAO,GAAG,EAAE;IAChB,IAAI,CAACtC,eAAe,CAACiC,OAAO,CAAC,CAACM,WAAW,EAAEnF,KAAK,KAAI;MAChD,IAAGmF,WAAW,CAACjB,WAAW,EAAC;QACvBiB,WAAW,CAAC1F,gBAAgB,GAAG2F,IAAI,CAACC,KAAK,CAACF,WAAW,CAACjB,WAAW,CAAC;OACrE,MAAI;QACDiB,WAAW,CAAC1F,gBAAgB,GAAG,CAAC;UAACG,KAAK,EAAE,CAAC;UAAED,KAAK,EAAE;QAAI,CAAC,CAAC;;MAE5D,IAAGwF,WAAW,CAACf,cAAc,EAAC;QAC1Be,WAAW,CAAChK,oBAAoB,GAAGiK,IAAI,CAACC,KAAK,CAACF,WAAW,CAACf,cAAc,CAAC;OAC5E,MAAI;QACDe,WAAW,CAAChK,oBAAoB,GAAG6H,EAAE,CAACsC,qBAAqB,EAAE;;MAEjE,IAAIH,WAAW,CAACZ,UAAU,EAAE;QACxBY,WAAW,CAACZ,UAAU,GAAGa,IAAI,CAACC,KAAK,CAACF,WAAW,CAACZ,UAAU,CAAC;OAC9D,MAAM;QACHY,WAAW,CAACZ,UAAU,GAAG,IAAI;;MAEjCY,WAAW,CAAChK,oBAAoB,CAACC,MAAM,GAAG4E,KAAK,GAAG,CAAC;MACnD,IAAG,CAACmF,WAAW,CAAChK,oBAAoB,CAACgC,UAAU,IAAIgI,WAAW,CAAChK,oBAAoB,CAACgC,UAAU,GAAG,CAAC,EAAC;QAC/FgI,WAAW,CAAChK,oBAAoB,CAACgC,UAAU,GAAG,GAAG;;MAErD,IAAG,CAACgI,WAAW,CAAChK,oBAAoB,CAACiC,WAAW,IAAI+H,WAAW,CAAChK,oBAAoB,CAACiC,WAAW,GAAG,CAAC,EAAC;QACjG+H,WAAW,CAAChK,oBAAoB,CAACiC,WAAW,GAAG,GAAG;;MAEtD,IAAG+H,WAAW,CAAChK,oBAAoB,CAACsD,IAAI,GAAG0G,WAAW,CAAChK,oBAAoB,CAACgC,UAAU,GAAG+H,OAAO,IAAIC,WAAW,CAAChK,oBAAoB,CAACsD,IAAI,IAAI,EAAE,EAAC;QAC5IyG,OAAO,GAAGC,WAAW,CAAChK,oBAAoB,CAACsD,IAAI,GAAG0G,WAAW,CAAChK,oBAAoB,CAACgC,UAAU;;IAErG,CAAC,CAAC;IACF,IAAI,CAACyF,eAAe,CAACiC,OAAO,CAAC,CAACM,WAAW,EAAEnF,KAAK,KAAI;MACjD,IAAGmF,WAAW,CAAChK,oBAAoB,CAACsD,IAAI,IAAI,CAAC,EAAC;QACzC0G,WAAW,CAAChK,oBAAoB,CAACsD,IAAI,GAAGyG,OAAO,GAAG,EAAE;QACpDA,OAAO,IAAI,EAAE,GAAGC,WAAW,CAAChK,oBAAoB,CAACgC,UAAU;;IAEnE,CAAC,CAAC;EACN;EAEAmI,qBAAqBA,CAAA;IACjB,OAAO;MACH9G,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPtB,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE,GAAG;MAChBmI,KAAK,EAAE,uBAAuB;MAC9BC,MAAM,EAAE,GAAG;MACXxD,KAAK,EAAE,GAAG;MACVyD,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE;KAChB;EACL;EAEAnB,eAAeA,CAAA;IACX,IAAIzB,EAAE,GAAG,IAAI;IACb,IAAI,CAACnK,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC+J,eAAe,CAACiC,OAAO,CAACM,WAAW,IAAG;MACvC,IAAGA,WAAW,CAACnI,WAAW,EAAC;QACvBgG,EAAE,CAAC/F,0BAA0B,CAACkI,WAAW,CAACnI,WAAW,CAACE,EAAE,CAAC,GAAG,IAAIjF,sBAAsB,EAAE;;MAE5F,IAAGkN,WAAW,CAACrG,MAAM,IAAI,CAAC,EAAC;QACvBkE,EAAE,CAACnK,UAAU,CAACmM,IAAI,CAACG,WAAW,CAACnI,WAAW,CAAC;;IAEnD,CAAC,CAAC;IACF6I,UAAU,CAAC,MAAK;MACZC,MAAM,CAACC,IAAI,CAAC/C,EAAE,CAAC/F,0BAA0B,CAAC,CAAC4H,OAAO,CAACP,OAAO,IAAG;QACzDtB,EAAE,CAAC/F,0BAA0B,CAACqH,OAAO,CAAC,CAAC0B,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC;MAC9D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAhN,eAAeA,CAACiN,KAAK;IACjB,IAAIjD,EAAE,GAAG,IAAI;IACb,IAAIkD,YAAY,GAAI,IAAI,CAACrN,UAAU,CAAEmL,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAC/G,EAAE,CAAC;IACrD,IAAI,CAAC0F,eAAe,CAACiC,OAAO,CAACM,WAAW,IAAG;MACvC,IAAGe,YAAY,CAACC,QAAQ,CAAChB,WAAW,CAACtG,OAAO,CAAC,EAAC;QAC1CsG,WAAW,CAACrG,MAAM,GAAG,CAAC;QACtB+G,UAAU,CAAC;UACP7C,EAAE,CAAC/F,0BAA0B,CAACkI,WAAW,CAACtG,OAAO,CAAC,CAACuH,WAAW,EAAE;QACpE,CAAC,CAAC;OACL,MAAI;QACDjB,WAAW,CAACrG,MAAM,GAAG,CAAC;;IAE9B,CAAC,CAAC;EACN;EAEAjF,UAAUA,CAAA;IACN,IAAImJ,EAAE,GAAG,IAAI;IACbA,EAAE,CAACS,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI2C,QAAQ,GAAG,EAAE;IACjB,IAAI,CAACzD,eAAe,CAACiC,OAAO,CAACM,WAAW,IAAG;MACvCA,WAAW,CAACjB,WAAW,GAAGkB,IAAI,CAACkB,SAAS,CAACnB,WAAW,CAAC1F,gBAAgB,CAAC;MACtE,OAAO0F,WAAW,CAAChK,oBAAoB,CAACoL,SAAS;MACjD,OAAOpB,WAAW,CAAChK,oBAAoB,CAACC,MAAM;MAC9C,OAAO+J,WAAW,CAAChK,oBAAoB,CAACqL,OAAO;MAC/C,OAAOrB,WAAW,CAAChK,oBAAoB,CAACsL,OAAO;MAC/C,OAAOtB,WAAW,CAAChK,oBAAoB,CAACuL,KAAK;MAC7C,OAAOvB,WAAW,CAAChK,oBAAoB,CAACwL,KAAK;MAC7CxB,WAAW,CAACf,cAAc,GAAGgB,IAAI,CAACkB,SAAS,CAACnB,WAAW,CAAChK,oBAAoB,CAAC;MAC7EkL,QAAQ,CAACrB,IAAI,CAAC;QACV9H,EAAE,EAAEiI,WAAW,CAACjI,EAAE;QAClBoH,OAAO,EAAEa,WAAW,CAACtG,OAAO;QAC5B+H,MAAM,EAAE5D,EAAE,CAACc,cAAc,CAACC,QAAQ,CAAC7G,EAAE;QACrC4B,MAAM,EAAEqG,WAAW,CAACrG,MAAM;QAC1BqF,SAAS,EAAEgB,WAAW,CAACjB,WAAW;QAClCG,cAAc,EAAEc,WAAW,CAACf,cAAc;QAC1CG,UAAU,EAAE,OAAOY,WAAW,CAACZ,UAAU,IAAI,QAAQ,GAAGY,WAAW,CAACZ,UAAU,GAAGa,IAAI,CAACkB,SAAS,CAACnB,WAAW,CAACZ,UAAU,CAAC,CAACsC,QAAQ;OACnI,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC1E,kBAAkB,CAAC2E,mBAAmB,CAACT,QAAQ,EAAGzC,QAAQ,IAAG;MAC9DZ,EAAE,CAACS,oBAAoB,CAACsD,OAAO,CAAC/D,EAAE,CAACjJ,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC;MACnFgJ,EAAE,CAACjG,QAAQ,GAAG/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM;MACxCsE,EAAE,CAACS,oBAAoB,CAACiB,OAAO,EAAE;MACjC9C,MAAM,CAACoF,QAAQ,CAAChB,MAAM,EAAE;IAC5B,CAAC,EAAE,IAAI,EAAE,MAAMhD,EAAE,CAACS,oBAAoB,CAACiB,OAAO,EAAE,CAAC;IACjD;EACJ;;EAEAtK,QAAQA,CAAA;IACJ,IAAI,CAAC2C,QAAQ,GAAG/E,SAAS,CAACuK,SAAS,CAAC/E,MAAM;EAC9C;EAEAH,iBAAiBA,CAACiH,OAAO;IACrB,IAAItB,EAAE,GAAG,IAAI;IACb,IAAI,CAACxD,iBAAiB,GAAG,IAAI,CAACoD,eAAe,CAAC,IAAI,CAACA,eAAe,CAACkC,SAAS,CAACb,EAAE,IAAIA,EAAE,CAACpF,OAAO,IAAIyF,OAAO,CAAC,CAAC;IAC1G,IAAI2C,QAAQ,GAAG7B,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC7F,iBAAiB,CAACxC,WAAW,CAACkK,OAAO,CAAC;IACrE,IAAI/C,SAAS,GAAGiB,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC7F,iBAAiB,CAAC0E,WAAW,CAAC;IAC9D,IAAGC,SAAS,EAAE3D,MAAM,GAAG,CAAC,IAAIyG,QAAQ,CAACd,QAAQ,CAACnO,SAAS,CAACmP,cAAc,CAACC,gBAAgB,CAAC,IAAIjD,SAAS,CAAC,CAAC,CAAC,EAAEvE,KAAK,IAAI,IAAI,IAAIuE,SAAS,CAAC,CAAC,CAAC,EAAExE,KAAK,IAAI,IAAI,EAAC;MACnJqD,EAAE,CAACjC,eAAe,CAAC,CAAC,CAAC,GAAGoD,SAAS,CAAC,CAAC,CAAC,CAACvE,KAAK;MAC1CoD,EAAE,CAACjC,eAAe,CAAC,CAAC,CAAC,GAAGoD,SAAS,CAAC,CAAC,CAAC,CAACxE,KAAK;;IAE9CkG,UAAU,CAAC;MACP,IAAIoB,QAAQ,CAACd,QAAQ,CAACnO,SAAS,CAACmP,cAAc,CAACC,gBAAgB,CAAC,EAAC;QACzDpE,EAAE,CAACF,0BAA0B,GAAG,IAAI;OAC3C,MAAM;QACHE,EAAE,CAACH,iBAAiB,GAAG,IAAI;;IAEnC,CAAC,CAAC;EACN;EAEA3B,qBAAqBA,CAAA;IACjB,IAAI,CAAC1B,iBAAiB,CAACC,gBAAgB,GAAG,CAAC;MAACG,KAAK,EAAE,IAAI,CAACmB,eAAe,CAAC,CAAC,CAAC;MAAEpB,KAAK,EAAE,IAAI,CAACoB,eAAe,CAAC,CAAC;IAAC,CAAC,CAAC;IAC5G,IAAI,CAAC+B,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACtD,iBAAiB,GAAG,IAAI;EACjC;EAEAN,YAAYA,CAAA;IACR,IAAI,CAACM,iBAAiB,CAACC,gBAAgB,CAAC4H,GAAG,EAAE;IAC7C,IAAG,IAAI,CAAC7H,iBAAiB,CAACC,gBAAgB,CAACe,MAAM,IAAI,CAAC,EAAC;MACnD,IAAI,CAAChB,iBAAiB,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAACE,KAAK,GAAG,IAAI;;EAE/D;EAEAL,aAAaA,CAAA;IACT,IAAIgI,aAAa,GAAG,IAAI,CAAC9H,iBAAiB,CAACC,gBAAgB,CAAC,IAAI,CAACD,iBAAiB,CAACC,gBAAgB,CAACe,MAAM,GAAG,CAAC,CAAC;IAC/G,IAAG8G,aAAa,IAAE,IAAI,IAAIA,aAAa,CAAC3H,KAAK,GAAG,CAAC,IAAI2H,aAAa,CAAC3H,KAAK,GAAG2H,aAAa,CAAC1H,KAAK,EAAC;MAC3F,IAAI,CAACJ,iBAAiB,CAACC,gBAAgB,CAACuF,IAAI,CAAC;QACzCpF,KAAK,EAAE,IAAI,CAACJ,iBAAiB,CAACC,gBAAgB,CAAC,IAAI,CAACD,iBAAiB,CAACC,gBAAgB,CAACe,MAAM,GAAG,CAAC,CAAC,CAACb;OACtG,CAAC;;EAEV;EAEA4H,WAAWA,CAAA;IACP,IAAI,CAAC/H,iBAAiB,GAAG,IAAI;EACjC;EAEAgI,iBAAiBA,CAACvB,KAAK;IACnB,IAAIjD,EAAE,GAAG,IAAI;IACb,IAAIiC,MAAM,GAAGgB,KAAK,CAAChB,MAAM,IAAI,EAAE;IAC/B,IAAGA,MAAM,CAACzE,MAAM,GAAG,CAAC,EAAC;MACjBwC,EAAE,CAACxJ,aAAa,GAAGwJ,EAAE,CAACV,MAAM,CAAC2C,MAAM,CAACwC,KAAK,IAAIzE,EAAE,CAAC0E,WAAW,CAACC,0BAA0B,CAACF,KAAK,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC7E,EAAE,CAAC0E,WAAW,CAACC,0BAA0B,CAAC1C,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;MACnK6C,OAAO,CAACC,GAAG,CAAC/E,EAAE,CAACxJ,aAAa,CAAC;;EAErC;EAEA;EACA;EACG;EACH;EACAgK,aAAaA,CAAA;IACT,IAAIR,EAAE,GAAG,IAAI;IACb,IAAIgF,SAAS,GAAG,GAAG;IACnB,IAAIC,QAAQ,GAAG,GAAG;IAClB,IAAI,CAACrF,eAAe,CAACiC,OAAO,CAACM,WAAW,IAAG;MACvC,IAAIlB,EAAE,GAAejB,EAAE,CAACX,IAAI,CAAC6F,aAAa,CAACC,aAAa,CAAC,SAAShD,WAAW,CAACtG,OAAO,EAAE,CAAC;MACxF,IAAGoF,EAAE,EAAC;QACF,IAAGA,EAAE,CAAC,WAAW,CAAC,GAAGA,EAAE,CAAC,cAAc,CAAC,GAAG+D,SAAS,EAAC;UAChDA,SAAS,GAAG/D,EAAE,CAAC,WAAW,CAAC,GAAGA,EAAE,CAAC,cAAc,CAAC;;QAEpD,IAAGA,EAAE,CAAC,YAAY,CAAC,GAAGA,EAAE,CAAC,aAAa,CAAC,GAAGgE,QAAQ,EAAC;UAC/CA,QAAQ,GAAGhE,EAAE,CAAC,YAAY,CAAC,GAAGA,EAAE,CAAC,aAAa,CAAC;;;IAG3D,CAAC,CAAC;IACF,IAAG,IAAI,CAAClH,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC/E,MAAM,EAAC;MAC3CwK,SAAS,IAAI,EAAE;MACfC,QAAQ,IAAI,EAAE;;IAElB,IAAG,CAAC,IAAI,CAACG,kBAAkB,EAAC;MACxB,IAAI,CAAC5F,gBAAgB,GAAGwF,SAAS;MACjC,IAAI,CAACvF,eAAe,GAAGwF,QAAQ;KAClC,MAAI;MACD,IAAGD,SAAS,GAAGhF,EAAE,CAACR,gBAAgB,EAAC;QAC/B,IAAI,CAACA,gBAAgB,GAAGwF,SAAS,GAAC,EAAE;;MAExC,IAAGC,QAAQ,GAAGjF,EAAE,CAACP,eAAe,EAAC;QAC7B,IAAI,CAACA,eAAe,GAAGwF,QAAQ,GAAC,EAAE;;;EAG9C;EACAtG,mBAAmBA,CAAA;IACf,IAAI0G,UAAU,GAAW,IAAI,CAAChG,IAAI,CAAC6F,aAAa,CAACC,aAAa,CAAC,aAAa,CAAC;IAC7E,IAAI,CAACzF,IAAI,GAAG2F,UAAU,CAACC,qBAAqB,EAAE,CAAC7J,IAAI;IACnD,IAAI,CAACkE,IAAI,GAAG0F,UAAU,CAACC,qBAAqB,EAAE,CAAC9J,GAAG;EACtD;EAEA+J,IAAIA,CAACtC,KAAK;IACN,IAAG,IAAI,CAAClJ,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM,IAAI,IAAI,CAAC0J,kBAAkB,EAAC;MACtE,IAAI,CAAC/F,IAAI,CAAC6F,aAAa,CAACC,aAAa,CAAC,YAAY,IAAI,CAACC,kBAAkB,CAACvJ,OAAO,EAAE,CAAC,CAAC2J,KAAK,CAACC,OAAO,GAAG,CAAC;;IACzG;IACD,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACN,kBAAkB,GAAG,IAAI;EAClC;EAEAtN,OAAOA,CAACmL,KAAK;IACT,IAAG,IAAI,CAAClJ,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM,IAAI,IAAI,CAAC0J,kBAAkB,EAAC;MACtE;IAAA;IACH;EACL;EAEAvK,SAASA,CAACoI,KAAK,EAAEd,WAAW;IACxB,IAAG,CAAC,IAAI,CAACuD,QAAQ,EAAC;MACd,IAAG,IAAI,CAAC3L,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM,EAAC;QAC3C,IAAI,CAACgK,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACN,kBAAkB,GAAGjD,WAAW;QACrC,IAAI,CAACiD,kBAAkB,CAACjN,oBAAoB,CAACqL,OAAO,GAAGP,KAAK,CAACO,OAAO;QACpE,IAAI,CAAC4B,kBAAkB,CAACjN,oBAAoB,CAACsL,OAAO,GAAGR,KAAK,CAACQ,OAAO;QACpE,IAAI,CAAC2B,kBAAkB,CAACjN,oBAAoB,CAACuL,KAAK,GAAGT,KAAK,CAACS,KAAK;QAChE,IAAI,CAAC0B,kBAAkB,CAACjN,oBAAoB,CAACwL,KAAK,GAAGV,KAAK,CAACU,KAAK;QAChE;;;EAGZ;;EAEAhM,UAAUA,CAACsL,KAAK,EAAEd,WAAW;IACzB,IAAG,CAAC,IAAI,CAACuD,QAAQ,EAAC;MACd,IAAG,IAAI,CAAC3L,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM,EAAC;QAC3C,IAAI,CAAC0J,kBAAkB,GAAGjD,WAAW;QACrC,IAAIlB,EAAE,GAAY,IAAI,CAAC5B,IAAI,CAAC6F,aAAa,CAACC,aAAa,CAAC,SAAS,IAAI,CAACC,kBAAkB,CAACvJ,OAAO,EAAE,CAAC;QACnG,IAAI,CAACuJ,kBAAkB,CAACjN,oBAAoB,CAACqL,OAAO,GAAGP,KAAK,CAACO,OAAO;QACpE,IAAI,CAAC4B,kBAAkB,CAACjN,oBAAoB,CAACsL,OAAO,GAAGR,KAAK,CAACQ,OAAO;QACpE,IAAI,CAAC2B,kBAAkB,CAACjN,oBAAoB,CAACuL,KAAK,GAAGzC,EAAE,CAACqE,qBAAqB,EAAE,CAAC7J,IAAI;QACpF,IAAI,CAAC2J,kBAAkB,CAACjN,oBAAoB,CAACwL,KAAK,GAAG1C,EAAE,CAACqE,qBAAqB,EAAE,CAAC9J,GAAG;QACnF,IAAI,CAACkK,QAAQ,GAAG,SAAS;;;EAGrC;EAEAlN,UAAUA,CAACyK,KAAK,EAAEd,WAAW;IACzB,IAAG,CAAC,IAAI,CAACuD,QAAQ,EAAC;MACd,IAAG,IAAI,CAAC3L,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM,EAAC;QAC3C,IAAI,CAAC0J,kBAAkB,GAAGjD,WAAW;QACrC,IAAIlB,EAAE,GAAY,IAAI,CAAC5B,IAAI,CAAC6F,aAAa,CAACC,aAAa,CAAC,SAAS,IAAI,CAACC,kBAAkB,CAACvJ,OAAO,EAAE,CAAC;QACnG,IAAI,CAACuJ,kBAAkB,CAACjN,oBAAoB,CAACqL,OAAO,GAAGP,KAAK,CAACO,OAAO;QACpE,IAAI,CAAC4B,kBAAkB,CAACjN,oBAAoB,CAACsL,OAAO,GAAGR,KAAK,CAACQ,OAAO;QACpE,IAAI,CAAC2B,kBAAkB,CAACjN,oBAAoB,CAACuL,KAAK,GAAGzC,EAAE,CAACqE,qBAAqB,EAAE,CAAC7J,IAAI;QACpF,IAAI,CAAC2J,kBAAkB,CAACjN,oBAAoB,CAACwL,KAAK,GAAG1C,EAAE,CAACqE,qBAAqB,EAAE,CAAC9J,GAAG;QACnF,IAAI,CAAC4J,kBAAkB,CAACjN,oBAAoB,CAACoL,SAAS,GAAGtC,EAAE,CAACqE,qBAAqB,EAAE,CAAC9C,MAAM;QAC1F,IAAI,CAACkD,QAAQ,GAAG,SAAS;;;EAGrC;EAEAzM,WAAWA,CAACgK,KAAK,EAAEd,WAAW;IAC1B,IAAG,CAAC,IAAI,CAACuD,QAAQ,EAAC;MACd,IAAG,IAAI,CAAC3L,QAAQ,IAAI/E,SAAS,CAACuK,SAAS,CAAC7D,MAAM,EAAC;QAC3C,IAAI,CAAC0J,kBAAkB,GAAGjD,WAAW;QACrC,IAAIlB,EAAE,GAAY,IAAI,CAAC5B,IAAI,CAAC6F,aAAa,CAACC,aAAa,CAAC,SAAS,IAAI,CAACC,kBAAkB,CAACvJ,OAAO,EAAE,CAAC;QACnG,IAAI,CAACuJ,kBAAkB,CAACjN,oBAAoB,CAACqL,OAAO,GAAGP,KAAK,CAACO,OAAO;QACpE,IAAI,CAAC4B,kBAAkB,CAACjN,oBAAoB,CAACsL,OAAO,GAAGR,KAAK,CAACQ,OAAO;QACpE,IAAI,CAAC2B,kBAAkB,CAACjN,oBAAoB,CAACuL,KAAK,GAAGzC,EAAE,CAACqE,qBAAqB,EAAE,CAAC7J,IAAI;QACpF,IAAI,CAAC2J,kBAAkB,CAACjN,oBAAoB,CAACwL,KAAK,GAAG1C,EAAE,CAACqE,qBAAqB,EAAE,CAAC9J,GAAG;QACnF,IAAI,CAAC4J,kBAAkB,CAACjN,oBAAoB,CAACoL,SAAS,GAAGtC,EAAE,CAACqE,qBAAqB,EAAE,CAAC9C,MAAM;QAC1F,IAAI,CAACkD,QAAQ,GAAG,UAAU;;;EAGtC;EAEAxK,IAAIA,CAAC+H,KAAK;IACN,IAAG,CAAC,IAAI,CAACyC,QAAQ,IAAI,IAAI,CAACN,kBAAkB,EAAC;MACzC,IAAG,CAAC,IAAI,CAACM,QAAQ,EAAC;QACd,IAAGzC,KAAK,CAACO,OAAO,GAAG,CAAC,IAAIP,KAAK,CAACQ,OAAO,GAAG,CAAC,EAAE;QACvC,IAAIkC,CAAC,GAAG1C,KAAK,CAACS,KAAK,GAAG,IAAI,CAAC0B,kBAAkB,CAACjN,oBAAoB,CAACqL,OAAO;QAC1E,IAAIoC,CAAC,GAAG3C,KAAK,CAACU,KAAK,GAAG,IAAI,CAACyB,kBAAkB,CAACjN,oBAAoB,CAACsL,OAAO;QAC1E,IAAIjI,GAAG,GAAGoK,CAAC,GAAG,IAAI,CAACjG,IAAI;QACvB,IAAIlE,IAAI,GAAGkK,CAAC,GAAG,IAAI,CAACjG,IAAI;QACxBoF,OAAO,CAACC,GAAG,CAACY,CAAC,EAAEC,CAAC,EAAE,IAAI,CAAClG,IAAI,EAAE,IAAI,CAACC,IAAI,EAAEnE,GAAG,EAAEC,IAAI,CAAC;QACtD,IAAGD,GAAG,IAAI,EAAE,IAAIC,IAAI,IAAI,EAAE,EAAC;UACvB,IAAI,CAAC2J,kBAAkB,CAACjN,oBAAoB,CAACqD,GAAG,GAAGA,GAAG;UACtD,IAAI,CAAC4J,kBAAkB,CAACjN,oBAAoB,CAACsD,IAAI,GAAGA,IAAI;UACxD;SACH,MAAK,IAAGD,GAAG,IAAI,EAAE,EAAC;UACf,IAAI,CAAC4J,kBAAkB,CAACjN,oBAAoB,CAACqD,GAAG,GAAGA,GAAG;SACzD,MAAK,IAAGC,IAAI,IAAI,EAAE,EAAC;UAChB,IAAI,CAAC2J,kBAAkB,CAACjN,oBAAoB,CAACsD,IAAI,GAAGA,IAAI;;QAE5DwH,KAAK,CAAC4C,cAAc,EAAE;;;EAGlC;EAEA5N,KAAKA,CAACgL,KAAK;IACP,IAAG,IAAI,CAACmC,kBAAkB,EAAC;MACvB,IAAI,CAACA,kBAAkB,CAACjN,oBAAoB,CAACgC,UAAU,GAAG8I,KAAK,CAAC6C,OAAO,GAAG,IAAI,CAACV,kBAAkB,CAACjN,oBAAoB,CAACuL,KAAK,GAAG,EAAE;;EAEzI;EAEA7K,KAAKA,CAACoK,KAAK;IACP,IAAG,IAAI,CAACmC,kBAAkB,EAAC;MACvB,IAAI7B,SAAS,GAAGN,KAAK,CAAC8C,OAAO,GAAG,IAAI,CAACX,kBAAkB,CAACjN,oBAAoB,CAACwL,KAAK;MAClF,IAAI,CAACyB,kBAAkB,CAACjN,oBAAoB,CAACiC,WAAW,IAAKmJ,SAAS,GAAG,IAAI,CAAC6B,kBAAkB,CAACjN,oBAAoB,CAACoL,SAAU;MAChI,IAAI,CAAC6B,kBAAkB,CAACjN,oBAAoB,CAACoL,SAAS,GAAGA,SAAS;MAClEuB,OAAO,CAACC,GAAG,CAAC9B,KAAK,EAAE,IAAI,CAACmC,kBAAkB,CAACjN,oBAAoB,CAAC;;EAExE;EAEAmB,MAAMA,CAAC2J,KAAK;IACR,IAAG,IAAI,CAACmC,kBAAkB,EAAC;MACvB,IAAI,CAACA,kBAAkB,CAACjN,oBAAoB,CAACgC,UAAU,GAAG8I,KAAK,CAAC6C,OAAO,GAAG,IAAI,CAACV,kBAAkB,CAACjN,oBAAoB,CAACuL,KAAK,GAAG,EAAE;MACjI,IAAIH,SAAS,GAAGN,KAAK,CAAC8C,OAAO,GAAG,IAAI,CAACX,kBAAkB,CAACjN,oBAAoB,CAACwL,KAAK;MAClF,IAAI,CAACyB,kBAAkB,CAACjN,oBAAoB,CAACiC,WAAW,IAAKmJ,SAAS,GAAG,IAAI,CAAC6B,kBAAkB,CAACjN,oBAAoB,CAACoL,SAAU;MAChI,IAAI,CAAC6B,kBAAkB,CAACjN,oBAAoB,CAACoL,SAAS,GAAGA,SAAS;;EAE1E;;;uBA9cS/E,sBAAsB,EAAArJ,EAAA,CAAA6Q,iBAAA,CAAA7Q,EAAA,CAAA8Q,QAAA,GAAA9Q,EAAA,CAAA6Q,iBAAA,CAAAE,EAAA,CAAAC,kBAAA,GAAAhR,EAAA,CAAA6Q,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAlR,EAAA,CAAA6Q,iBAAA,CAAA7Q,EAAA,CAAAmR,UAAA;IAAA;EAAA;;;YAAtB9H,sBAAsB;MAAA+H,SAAA;MAAAC,YAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAtBC,GAAA,CAAAlI,QAAA,EAAU;UAAA,UAAAtJ,EAAA,CAAAyR,eAAA;;;;;;;;;UChCvBzR,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAA0H,MAAA,gBAAS;UAAA1H,EAAA,CAAAc,YAAA,EAAM;UACnDd,EAAA,CAAAoE,SAAA,sBAAoF;UACxFpE,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,UAAK;UAEGD,EAAA,CAAAoE,SAAA,aAEM;UACNpE,EAAA,CAAAC,cAAA,aAAoC;UAWhCD,EAAA,CAAAqE,UAAA,IAAAqN,6CAAA,0BAiBc;UAClB1R,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,cAAiC;UAC7BD,EAAA,CAAAqE,UAAA,KAAAsN,2CAAA,sBAAuK;UACvK3R,EAAA,CAAAqE,UAAA,KAAAuN,2CAAA,sBAAqK;UACzK5R,EAAA,CAAAc,YAAA,EAAM;UAKlBd,EAAA,CAAAC,cAAA,eASsC;UAAxBD,EAAA,CAAAE,UAAA,oBAAA2R,uDAAAzR,MAAA;YAAA,OAAUoR,GAAA,CAAApB,IAAA,CAAAhQ,MAAA,CAAY;UAAA,EAAC;UAEjCJ,EAAA,CAAAqE,UAAA,KAAAyN,sCAAA,mBA2CM;UACV9R,EAAA,CAAAc,YAAA,EAAM;UAeNd,EAAA,CAAAC,cAAA,oBAAuN;UAA7MD,EAAA,CAAAE,UAAA,oBAAA6R,4DAAA;YAAA,OAAUP,GAAA,CAAApC,WAAA,EAAa;UAAA,EAAC,2BAAA4C,mEAAA5R,MAAA;YAAA,OAAAoR,GAAA,CAAA9G,iBAAA,GAAAtK,MAAA;UAAA;UAC9BJ,EAAA,CAAAqE,UAAA,KAAA4N,sCAAA,kBAqBM;UACVjS,EAAA,CAAAc,YAAA,EAAW;UACXd,EAAA,CAAAC,cAAA,oBAAgO;UAAtND,EAAA,CAAAE,UAAA,oBAAAgS,4DAAA;YAAA,OAAUV,GAAA,CAAApC,WAAA,EAAa;UAAA,EAAC,2BAAA+C,mEAAA/R,MAAA;YAAA,OAAAoR,GAAA,CAAA7G,0BAAA,GAAAvK,MAAA;UAAA;UAC9BJ,EAAA,CAAAqE,UAAA,KAAA+N,sCAAA,kBAsBM;UACVpS,EAAA,CAAAc,YAAA,EAAW;;;UAnKoCd,EAAA,CAAA0E,SAAA,GAAe;UAAf1E,EAAA,CAAAkB,UAAA,UAAAsQ,GAAA,CAAAvG,KAAA,CAAe,SAAAuG,GAAA,CAAA1G,IAAA;UAGT9K,EAAA,CAAA0E,SAAA,GAAsB;UAAtB1E,EAAA,CAAAe,UAAA,CAAAf,EAAA,CAAAgB,eAAA,KAAAqR,GAAA,EAAsB;UAe7CrS,EAAA,CAAA0E,SAAA,GAAuC;UAAvC1E,EAAA,CAAAkB,UAAA,SAAAsQ,GAAA,CAAA5M,QAAA,IAAA4M,GAAA,CAAApM,cAAA,CAAAC,MAAA,CAAuC;UAoB1CrF,EAAA,CAAA0E,SAAA,GAAuC;UAAvC1E,EAAA,CAAAkB,UAAA,SAAAsQ,GAAA,CAAA5M,QAAA,IAAA4M,GAAA,CAAApM,cAAA,CAAAC,MAAA,CAAuC;UACvCrF,EAAA,CAAA0E,SAAA,GAAuC;UAAvC1E,EAAA,CAAAkB,UAAA,SAAAsQ,GAAA,CAAA5M,QAAA,IAAA4M,GAAA,CAAApM,cAAA,CAAAmB,MAAA,CAAuC;UAMvBvG,EAAA,CAAA0E,SAAA,GASzC;UATyC1E,EAAA,CAAAe,UAAA,CAAAf,EAAA,CAAAsS,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnH,gBAAA,cAAAmH,GAAA,CAAAlH,eAAA,cAAAkH,GAAA,CAAA5M,QAAA,IAAA4M,GAAA,CAAApM,cAAA,CAAAmB,MAAA,kCAAAiL,GAAA,CAAA5M,QAAA,IAAA4M,GAAA,CAAApM,cAAA,CAAAmB,MAAA,8BASzC;UAE+BvG,EAAA,CAAA0E,SAAA,GAAmB;UAAnB1E,EAAA,CAAAkB,UAAA,YAAAsQ,GAAA,CAAA/G,eAAA,CAAmB;UA2D8FzK,EAAA,CAAA0E,SAAA,GAA4B;UAA5B1E,EAAA,CAAAe,UAAA,CAAAf,EAAA,CAAAgB,eAAA,KAAAwR,GAAA,EAA4B;UAA3IxS,EAAA,CAAAkB,UAAA,WAAAsQ,GAAA,CAAA5P,WAAA,CAAAC,SAAA,gCAA+D,YAAA2P,GAAA,CAAA9G,iBAAA;UACtD1K,EAAA,CAAA0E,SAAA,GAAuB;UAAvB1E,EAAA,CAAAkB,UAAA,SAAAsQ,GAAA,CAAAnK,iBAAA,CAAuB;UAuBwFrH,EAAA,CAAA0E,SAAA,GAA4B;UAA5B1E,EAAA,CAAAe,UAAA,CAAAf,EAAA,CAAAgB,eAAA,KAAAwR,GAAA,EAA4B;UAApJxS,EAAA,CAAAkB,UAAA,WAAAsQ,GAAA,CAAA5P,WAAA,CAAAC,SAAA,gCAA+D,YAAA2P,GAAA,CAAA7G,0BAAA;UACtD3K,EAAA,CAAA0E,SAAA,GAAuB;UAAvB1E,EAAA,CAAAkB,UAAA,SAAAsQ,GAAA,CAAAnK,iBAAA,CAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}