{"ast": null, "code": "export default {\n  label: {\n    username: \"<PERSON><PERSON><PERSON>\",\n    fullname: \"Full Name\",\n    userType: \"User Type\",\n    email: \"Email\",\n    provinceCode: \"Province Code\",\n    time: \"Time\",\n    status: \"Status\",\n    phone: \"Phone Number\",\n    description: \"Description\",\n    manager: \"Manager Level\",\n    province: \"Province/City\",\n    role: \"Role\",\n    permission: {\n      name: \"Permission Name\",\n      object: \"Object\"\n    },\n    customerName: \"Customer Name\",\n    oldPass: \"Old password\",\n    newPass: \"New password\",\n    confirmPass: \"Confirm password\",\n    submitChangePass: \"Agree & Sign In\",\n    linkExpired: \"This password recovery has expired\",\n    cmpForgotPass: \"M2M SIM - Forgot Password\",\n    deviceType: \"Device Type\",\n    os: \"Operating System\",\n    ip: \"IP Address\",\n    managerName: \"Manager\",\n    customerAccount: \"Superior Customer Accouunt\",\n    generalInfo: \"General Information\",\n    addCustomerAccount: \"Add Customer Accouunt\",\n    showCustomerAccount: \"Show Customer Accouunt\",\n    notiChangePass: \"Password has expired. Please change your password to continue using. The new password is valid for 6 months from the date of the last password change.\"\n  },\n  text: {\n    detailaccount: \"Detail Account\",\n    infoAccount: \"Info Account\",\n    active: \"Active\",\n    inactive: \"Inactive\",\n    account: \"Account\",\n    titleChangeManageLevel: \"Change Manager Level\",\n    selectAccount: \"Select Account\",\n    inputUsername: \"Input Account Name\",\n    inputFullname: \"Input Full Name\",\n    inputEmail: \"Input Email\",\n    inputPhone: \"Input Phone Number\",\n    selectUserType: \"Select User Type\",\n    selectRoles: \"Select Roles\",\n    selectManager: \"Select Manager Level\",\n    selectProvince: \"Select Province/City\",\n    selectCustomers: \"Select Customers\",\n    disagreePolicy: \"You haven't agree to this policy yet\",\n    typeSelectAll: \"Change All\",\n    typeSelectList: \"Change By List\",\n    selectGDV: \"Select manager\",\n    selectCustomerAccount: \"Select superior customer account\",\n    addCustomer: \"Add Customer\",\n    addContract: \"Add Contract Code\",\n    grantApi: \"Grant API\",\n    module: \"Module\",\n    gen: \"Gen\",\n    working: \"Working\",\n    notWorking: \"Not Working\"\n  },\n  usertype: {\n    admin: \"Admin\",\n    customer: \"Customer\",\n    district: \"Teller\",\n    province: \"Province/City\",\n    agency: \"Agency\"\n  },\n  userstatus: {\n    active: \"Active\",\n    inactive: \"Inactive\"\n  },\n  button: {\n    disagreePolicy: \"Disagree Policy\",\n    viewPolicyProtectPersonalData: \"View Personal Data Protection Policy\"\n  },\n  message: {\n    customerRequired: \"Must select at least one customer\",\n    managerRequired: '“Manage” cannot be left blank'\n  }\n};", "map": {"version": 3, "names": ["label", "username", "fullname", "userType", "email", "provinceCode", "time", "status", "phone", "description", "manager", "province", "role", "permission", "name", "object", "customerName", "old<PERSON><PERSON>", "newPass", "confirmPass", "submitChangePass", "linkExpired", "cmpForgotPass", "deviceType", "os", "ip", "<PERSON><PERSON><PERSON>", "customerAccount", "generalInfo", "addCustomerAccount", "showCustomerAccount", "notiChangePass", "text", "detailaccount", "infoAccount", "active", "inactive", "account", "titleChangeManageLevel", "selectAccount", "inputUsername", "inputFullname", "inputEmail", "inputPhone", "selectUserType", "selectRoles", "selectManager", "selectProvince", "selectCustomers", "disagreePolicy", "typeSelectAll", "typeSelectList", "selectGDV", "selectCustomerAccount", "addCustomer", "addContract", "grantApi", "module", "gen", "working", "notWorking", "usertype", "admin", "customer", "district", "agency", "userstatus", "button", "viewPolicyProtectPersonalData", "message", "customerRequired", "manager<PERSON><PERSON><PERSON>d"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\account.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        username: \"<PERSON><PERSON><PERSON>\",\r\n        fullname: \"Full Name\",\r\n        userType: \"User Type\",\r\n        email: \"Email\",\r\n        provinceCode: \"Province Code\",\r\n        time: \"Time\",\r\n        status: \"Status\",\r\n        phone: \"Phone Number\",\r\n        description: \"Description\",\r\n        manager: \"Manager Level\",\r\n        province: \"Province/City\",\r\n        role: \"Role\",\r\n        permission:{\r\n            name: \"Permission Name\",\r\n            object: \"Object\"\r\n        },\r\n        customerName: \"Customer Name\",\r\n        oldPass : \"Old password\",\r\n        newPass : \"New password\",\r\n        confirmPass: \"Confirm password\",\r\n        submitChangePass : \"Agree & Sign In\",\r\n        linkExpired : \"This password recovery has expired\",\r\n        cmpForgotPass : \"M2M SIM - Forgot Password\",\r\n        deviceType: \"Device Type\",\r\n        os: \"Operating System\",\r\n        ip: \"IP Address\",\r\n        managerName : \"Manager\",\r\n        customerAccount : \"Superior Customer Accouunt\",\r\n        generalInfo: \"General Information\",\r\n        addCustomerAccount : \"Add Customer Accouunt\",\r\n        showCustomerAccount : \"Show Customer Accouunt\",\r\n        notiChangePass: \"Password has expired. Please change your password to continue using. The new password is valid for 6 months from the date of the last password change.\"\r\n\r\n    },\r\n    text: {\r\n        detailaccount: \"Detail Account\",\r\n        infoAccount: \"Info Account\",\r\n        active: \"Active\",\r\n        inactive: \"Inactive\",\r\n        account: \"Account\",\r\n        titleChangeManageLevel: \"Change Manager Level\",\r\n        selectAccount: \"Select Account\",\r\n        inputUsername: \"Input Account Name\",\r\n        inputFullname: \"Input Full Name\",\r\n        inputEmail: \"Input Email\",\r\n        inputPhone: \"Input Phone Number\",\r\n        selectUserType: \"Select User Type\",\r\n        selectRoles: \"Select Roles\",\r\n        selectManager: \"Select Manager Level\",\r\n        selectProvince: \"Select Province/City\",\r\n        selectCustomers: \"Select Customers\",\r\n        disagreePolicy: \"You haven't agree to this policy yet\",\r\n        typeSelectAll: \"Change All\",\r\n        typeSelectList: \"Change By List\",\r\n        selectGDV : \"Select manager\",\r\n        selectCustomerAccount : \"Select superior customer account\",\r\n        addCustomer: \"Add Customer\",\r\n        addContract: \"Add Contract Code\",\r\n        grantApi : \"Grant API\",\r\n        module : \"Module\",\r\n        gen : \"Gen\",\r\n        working: \"Working\",\r\n        notWorking : \"Not Working\"\r\n    },\r\n    usertype: {\r\n        admin: \"Admin\",\r\n        customer: \"Customer\",\r\n        district: \"Teller\",\r\n        province: \"Province/City\",\r\n        agency: \"Agency\"\r\n    },\r\n    userstatus: {\r\n        active: \"Active\",\r\n        inactive: \"Inactive\"\r\n    },\r\n    button: {\r\n        disagreePolicy: \"Disagree Policy\",\r\n        viewPolicyProtectPersonalData: \"View Personal Data Protection Policy\"\r\n    },\r\n    message: {\r\n        customerRequired: \"Must select at least one customer\",\r\n        managerRequired: '“Manage” cannot be left blank'\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,eAAe;IAC7BC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,eAAe;IACxBC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;KACX;IACDC,YAAY,EAAE,eAAe;IAC7BC,OAAO,EAAG,cAAc;IACxBC,OAAO,EAAG,cAAc;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,gBAAgB,EAAG,iBAAiB;IACpCC,WAAW,EAAG,oCAAoC;IAClDC,aAAa,EAAG,2BAA2B;IAC3CC,UAAU,EAAE,aAAa;IACzBC,EAAE,EAAE,kBAAkB;IACtBC,EAAE,EAAE,YAAY;IAChBC,WAAW,EAAG,SAAS;IACvBC,eAAe,EAAG,4BAA4B;IAC9CC,WAAW,EAAE,qBAAqB;IAClCC,kBAAkB,EAAG,uBAAuB;IAC5CC,mBAAmB,EAAG,wBAAwB;IAC9CC,cAAc,EAAE;GAEnB;EACDC,IAAI,EAAE;IACFC,aAAa,EAAE,gBAAgB;IAC/BC,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,sBAAsB,EAAE,sBAAsB;IAC9CC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAE,oBAAoB;IACnCC,aAAa,EAAE,iBAAiB;IAChCC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,oBAAoB;IAChCC,cAAc,EAAE,kBAAkB;IAClCC,WAAW,EAAE,cAAc;IAC3BC,aAAa,EAAE,sBAAsB;IACrCC,cAAc,EAAE,sBAAsB;IACtCC,eAAe,EAAE,kBAAkB;IACnCC,cAAc,EAAE,sCAAsC;IACtDC,aAAa,EAAE,YAAY;IAC3BC,cAAc,EAAE,gBAAgB;IAChCC,SAAS,EAAG,gBAAgB;IAC5BC,qBAAqB,EAAG,kCAAkC;IAC1DC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,mBAAmB;IAChCC,QAAQ,EAAG,WAAW;IACtBC,MAAM,EAAG,QAAQ;IACjBC,GAAG,EAAG,KAAK;IACXC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAG;GAChB;EACDC,QAAQ,EAAE;IACNC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBrD,QAAQ,EAAE,eAAe;IACzBsD,MAAM,EAAE;GACX;EACDC,UAAU,EAAE;IACR/B,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;GACb;EACD+B,MAAM,EAAE;IACJlB,cAAc,EAAE,iBAAiB;IACjCmB,6BAA6B,EAAE;GAClC;EACDC,OAAO,EAAE;IACLC,gBAAgB,EAAE,mCAAmC;IACrDC,eAAe,EAAE;;CAExB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}