{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Northern Sami [se]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/karamell\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var se = moment.defineLocale('se', {\n    months: 'ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu'.split('_'),\n    monthsShort: 'ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov'.split('_'),\n    weekdays: 'sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat'.split('_'),\n    weekdaysShort: 'sotn_vuos_maŋ_gask_duor_bear_láv'.split('_'),\n    weekdaysMin: 's_v_m_g_d_b_L'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'MMMM D. [b.] YYYY',\n      LLL: 'MMMM D. [b.] YYYY [ti.] HH:mm',\n      LLLL: 'dddd, MMMM D. [b.] YYYY [ti.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[otne ti] LT',\n      nextDay: '[ihttin ti] LT',\n      nextWeek: 'dddd [ti] LT',\n      lastDay: '[ikte ti] LT',\n      lastWeek: '[ovddit] dddd [ti] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s geažes',\n      past: 'maŋit %s',\n      s: 'moadde sekunddat',\n      ss: '%d sekunddat',\n      m: 'okta minuhta',\n      mm: '%d minuhtat',\n      h: 'okta diimmu',\n      hh: '%d diimmut',\n      d: 'okta beaivi',\n      dd: '%d beaivvit',\n      M: 'okta mánnu',\n      MM: '%d mánut',\n      y: 'okta jahki',\n      yy: '%d jagit'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return se;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "se", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/moment/locale/se.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Northern Sami [se]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/karamell\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var se = moment.defineLocale('se', {\n        months: 'ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu'.split(\n            '_'\n        ),\n        monthsShort:\n            'ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov'.split('_'),\n        weekdays:\n            'sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat'.split(\n                '_'\n            ),\n        weekdaysShort: 'sotn_vuos_maŋ_gask_duor_bear_láv'.split('_'),\n        weekdaysMin: 's_v_m_g_d_b_L'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'MMMM D. [b.] YYYY',\n            LLL: 'MMMM D. [b.] YYYY [ti.] HH:mm',\n            LLLL: 'dddd, MMMM D. [b.] YYYY [ti.] HH:mm',\n        },\n        calendar: {\n            sameDay: '[otne ti] LT',\n            nextDay: '[ihttin ti] LT',\n            nextWeek: 'dddd [ti] LT',\n            lastDay: '[ikte ti] LT',\n            lastWeek: '[ovddit] dddd [ti] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s geažes',\n            past: 'maŋit %s',\n            s: 'moadde sekunddat',\n            ss: '%d sekunddat',\n            m: 'okta minuhta',\n            mm: '%d minuhtat',\n            h: 'okta diimmu',\n            hh: '%d diimmut',\n            d: 'okta beaivi',\n            dd: '%d beaivvit',\n            M: 'okta mánnu',\n            MM: '%d mánut',\n            y: 'okta jahki',\n            yy: '%d jagit',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return se;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,kJAAkJ,CAACC,KAAK,CAC5J,GACJ,CAAC;IACDC,WAAW,EACP,4DAA4D,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,QAAQ,EACJ,4EAA4E,CAACF,KAAK,CAC9E,GACJ,CAAC;IACLG,aAAa,EAAE,kCAAkC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC5DI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,mBAAmB;MACvBC,GAAG,EAAE,+BAA+B;MACpCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,cAAc;MAClBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOzC,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}