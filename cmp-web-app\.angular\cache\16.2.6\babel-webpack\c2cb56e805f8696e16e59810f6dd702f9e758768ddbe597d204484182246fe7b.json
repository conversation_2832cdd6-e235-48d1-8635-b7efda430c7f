{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport DataPage from \"../../service/data.page\";\nimport { ListHistoryActivityComponent } from \"./list/app.list.history.component\";\nimport { CONSTANTS } from \"../../service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppHistoryRouting {\n  static {\n    this.ɵfac = function AppHistoryRouting_Factory(t) {\n      return new (t || AppHistoryRouting)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppHistoryRouting\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: 'list',\n        component: ListHistoryActivityComponent,\n        data: new DataPage(\"logs.menu.log\", [CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppHistoryRouting, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DataPage", "ListHistoryActivityComponent", "CONSTANTS", "AppHistoryRouting", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "LOG", "VIEW_LIST", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\history_activity\\app.history-routing.ts"], "sourcesContent": ["import {NgModule} from \"@angular/core\";\r\nimport {RouterModule} from \"@angular/router\";\r\nimport DataPage from \"../../service/data.page\";\r\nimport {ListHistoryActivityComponent} from \"./list/app.list.history.component\";\r\nimport {CONSTANTS} from \"../../service/comon/constants\";\r\n\r\n\r\n@NgModule({\r\n  imports: [\r\n    RouterModule.forChild([\r\n      {path: 'list', component: ListHistoryActivityComponent, data: new DataPage(\"logs.menu.log\",[CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])},\r\n    ]),\r\n  ],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppHistoryRouting {\r\n}\r\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,iBAAiB;AAC5C,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,SAAQC,4BAA4B,QAAO,mCAAmC;AAC9E,SAAQC,SAAS,QAAO,+BAA+B;;;AAWvD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAN1BJ,YAAY,CAACK,QAAQ,CAAC,CACpB;QAACC,IAAI,EAAE,MAAM;QAAEC,SAAS,EAAEL,4BAA4B;QAAEM,IAAI,EAAE,IAAIP,QAAQ,CAAC,eAAe,EAAC,CAACE,SAAS,CAACM,WAAW,CAACC,GAAG,CAACC,SAAS,CAAC;MAAC,CAAC,CACnI,CAAC,EAEMX,YAAY;IAAA;EAAA;;;2EAEXI,iBAAiB;IAAAQ,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFlBd,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}