{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppAlertListComponent } from \"./alert-setting/list/app.alert.list.component\";\nimport { AppAlertCreateComponent } from \"./alert-setting/create/app.alert.create.component\";\nimport { AppAlertDetailComponent } from \"./alert-setting/detail/app.alert.detail.component\";\nimport { AppAlertEditComponent } from \"./alert-setting/edit/app.alert.edit.component\";\nimport { AppGroupReceivingCreateComponent } from \"./alert-receiving-group/create/app.group-receiving.create.component\";\nimport { AppGroupReceivingDetailComponent } from \"./alert-receiving-group/detail/app.group-receiving.detail.component\";\nimport { AppGroupReceivingEditComponent } from \"./alert-receiving-group/edit/app.group-receiving.edit.component\";\nimport { AppAlertsAlertHistoryComponent } from \"../alert/alert-history/app.alerts.alert.history\";\nimport { AppAlertsAlertReceivingGroupComponent } from \"./alert-receiving-group/list/app.alerts.alert.receiving.group.component\";\nimport DataPage from \"../../service/data.page\";\nimport { CONSTANTS } from \"../../service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppAlertRoutingModule {\n  static {\n    this.ɵfac = function AppAlertRoutingModule_Factory(t) {\n      return new (t || AppAlertRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppAlertRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: \"\",\n        component: AppAlertListComponent,\n        data: new DataPage(\"global.menu.alertList\", [CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])\n      }, {\n        path: \"create\",\n        component: AppAlertCreateComponent,\n        data: new DataPage(\"global.titlepage.createAlarm\", [CONSTANTS.PERMISSIONS.ALERT.CREATE, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD])\n      }, {\n        path: \"detail/:id\",\n        component: AppAlertDetailComponent,\n        data: new DataPage(\"global.titlepage.detailAlarm\", [CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL])\n      }, {\n        path: \"edit/:id\",\n        component: AppAlertEditComponent,\n        data: new DataPage(\"global.titlepage.editAlarm\", [CONSTANTS.PERMISSIONS.ALERT.UPDATE])\n      }, {\n        path: \"wallet-threshold/edit/:id\",\n        component: AppAlertEditComponent,\n        data: new DataPage(\"global.titlepage.editAlarm\", [CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD])\n      }, {\n        path: \"wallet-expiry/edit/:id\",\n        component: AppAlertEditComponent,\n        data: new DataPage(\"global.titlepage.editAlarm\", [CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY])\n      }, {\n        path: \"receiving-group/create\",\n        component: AppGroupReceivingCreateComponent,\n        data: new DataPage(\"global.titlepage.createAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.CREATE])\n      }, {\n        path: \"receiving-group/detail/:id\",\n        component: AppGroupReceivingDetailComponent,\n        data: new DataPage(\"global.titlepage.detailAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_DETAIL])\n      }, {\n        path: \"receiving-group/edit/:id\",\n        component: AppGroupReceivingEditComponent,\n        data: new DataPage(\"global.titlepage.editAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.UPDATE])\n      }, {\n        path: \"receiving-group\",\n        component: AppAlertsAlertReceivingGroupComponent,\n        data: new DataPage(\"global.titlepage.listAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])\n      }, {\n        path: \"history\",\n        component: AppAlertsAlertHistoryComponent,\n        data: new DataPage(\"global.titlepage.listAlertHistory\", [CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppAlertRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppAlertListComponent", "AppAlertCreateComponent", "AppAlertDetailComponent", "AppAlertEditComponent", "AppGroupReceivingCreateComponent", "AppGroupReceivingDetailComponent", "AppGroupReceivingEditComponent", "AppAlertsAlertHistoryComponent", "AppAlertsAlertReceivingGroupComponent", "DataPage", "CONSTANTS", "AppAlertRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "ALERT", "VIEW_LIST", "CREATE", "CREATE_WALLET_EXPIRY", "CREATE_WALLET_THRESHOLD", "VIEW_DETAIL", "UPDATE", "UPDATE_WALLET_THRESHOLD", "UPDATE_WALLET_EXPIRY", "ALERT_RECEIVING_GROUP", "ALERT_HISTORY", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\alert\\app.alert-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport {AppAlertListComponent} from \"./alert-setting/list/app.alert.list.component\";\r\nimport {AppAlertCreateComponent} from \"./alert-setting/create/app.alert.create.component\";\r\nimport {AppAlertDetailComponent} from \"./alert-setting/detail/app.alert.detail.component\";\r\nimport {AppAlertEditComponent} from \"./alert-setting/edit/app.alert.edit.component\";\r\nimport {AppGroupReceivingCreateComponent} from \"./alert-receiving-group/create/app.group-receiving.create.component\";\r\nimport {AppGroupReceivingDetailComponent} from \"./alert-receiving-group/detail/app.group-receiving.detail.component\";\r\nimport {AppGroupReceivingEditComponent} from \"./alert-receiving-group/edit/app.group-receiving.edit.component\";\r\nimport {AppAlertsAlertHistoryComponent} from \"../alert/alert-history/app.alerts.alert.history\";\r\nimport {\r\n    AppAlertsAlertReceivingGroupComponent\r\n} from \"./alert-receiving-group/list/app.alerts.alert.receiving.group.component\";\r\nimport DataPage from \"../../service/data.page\";\r\nimport {CONSTANTS} from \"../../service/comon/constants\";\r\n\r\n@NgModule({\r\n    imports:[\r\n        RouterModule.forChild([\r\n            {path: \"\", component: AppAlertListComponent, data: new DataPage(\"global.menu.alertList\", [CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])},\r\n            {path: \"create\", component: AppAlertCreateComponent, data: new DataPage(\"global.titlepage.createAlarm\", [CONSTANTS.PERMISSIONS.ALERT.CREATE, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD])},\r\n            {path: \"detail/:id\", component: AppAlertDetailComponent, data: new DataPage(\"global.titlepage.detailAlarm\", [CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL])},\r\n            {path: \"edit/:id\", component: AppAlertEditComponent, data: new DataPage(\"global.titlepage.editAlarm\", [CONSTANTS.PERMISSIONS.ALERT.UPDATE])},\r\n            {path: \"wallet-threshold/edit/:id\", component: AppAlertEditComponent, data: new DataPage(\"global.titlepage.editAlarm\", [CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD])},\r\n            {path: \"wallet-expiry/edit/:id\", component: AppAlertEditComponent, data: new DataPage(\"global.titlepage.editAlarm\", [CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY])},\r\n            {path: \"receiving-group/create\", component: AppGroupReceivingCreateComponent, data: new DataPage(\"global.titlepage.createAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.CREATE])},\r\n            {path: \"receiving-group/detail/:id\", component: AppGroupReceivingDetailComponent, data: new DataPage(\"global.titlepage.detailAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_DETAIL])},\r\n            {path: \"receiving-group/edit/:id\", component: AppGroupReceivingEditComponent, data: new DataPage(\"global.titlepage.editAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.UPDATE])},\r\n            {path: \"receiving-group\", component: AppAlertsAlertReceivingGroupComponent, data: new DataPage(\"global.titlepage.listAlertReceivingGroup\", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])},\r\n            {path: \"history\", component: AppAlertsAlertHistoryComponent, data: new DataPage(\"global.titlepage.listAlertHistory\", [CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])}\r\n        ])\r\n    ],\r\n    exports: [RouterModule]\r\n})\r\nexport class AppAlertRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAAQC,qBAAqB,QAAO,+CAA+C;AACnF,SAAQC,uBAAuB,QAAO,mDAAmD;AACzF,SAAQC,uBAAuB,QAAO,mDAAmD;AACzF,SAAQC,qBAAqB,QAAO,+CAA+C;AACnF,SAAQC,gCAAgC,QAAO,qEAAqE;AACpH,SAAQC,gCAAgC,QAAO,qEAAqE;AACpH,SAAQC,8BAA8B,QAAO,iEAAiE;AAC9G,SAAQC,8BAA8B,QAAO,iDAAiD;AAC9F,SACIC,qCAAqC,QAClC,yEAAyE;AAChF,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,SAAQC,SAAS,QAAO,+BAA+B;;;AAoBvD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAhB1BZ,YAAY,CAACa,QAAQ,CAAC,CAClB;QAACC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEd,qBAAqB;QAAEe,IAAI,EAAE,IAAIN,QAAQ,CAAC,uBAAuB,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,KAAK,CAACC,SAAS,CAAC;MAAC,CAAC,EAClI;QAACL,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAEb,uBAAuB;QAAEc,IAAI,EAAE,IAAIN,QAAQ,CAAC,8BAA8B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,KAAK,CAACE,MAAM,EAAET,SAAS,CAACM,WAAW,CAACC,KAAK,CAACG,oBAAoB,EAAEV,SAAS,CAACM,WAAW,CAACC,KAAK,CAACI,uBAAuB,CAAC;MAAC,CAAC,EACrP;QAACR,IAAI,EAAE,YAAY;QAAEC,SAAS,EAAEZ,uBAAuB;QAAEa,IAAI,EAAE,IAAIN,QAAQ,CAAC,8BAA8B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,KAAK,CAACK,WAAW,CAAC;MAAC,CAAC,EACvJ;QAACT,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEX,qBAAqB;QAAEY,IAAI,EAAE,IAAIN,QAAQ,CAAC,4BAA4B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,KAAK,CAACM,MAAM,CAAC;MAAC,CAAC,EAC5I;QAACV,IAAI,EAAE,2BAA2B;QAAEC,SAAS,EAAEX,qBAAqB;QAAEY,IAAI,EAAE,IAAIN,QAAQ,CAAC,4BAA4B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,KAAK,CAACO,uBAAuB,CAAC;MAAC,CAAC,EAC9K;QAACX,IAAI,EAAE,wBAAwB;QAAEC,SAAS,EAAEX,qBAAqB;QAAEY,IAAI,EAAE,IAAIN,QAAQ,CAAC,4BAA4B,EAAE,CAACC,SAAS,CAACM,WAAW,CAACC,KAAK,CAACQ,oBAAoB,CAAC;MAAC,CAAC,EACxK;QAACZ,IAAI,EAAE,wBAAwB;QAAEC,SAAS,EAAEV,gCAAgC;QAAEW,IAAI,EAAE,IAAIN,QAAQ,CAAC,4CAA4C,EAAE,CAACC,SAAS,CAACM,WAAW,CAACU,qBAAqB,CAACP,MAAM,CAAC;MAAC,CAAC,EACrM;QAACN,IAAI,EAAE,4BAA4B;QAAEC,SAAS,EAAET,gCAAgC;QAAEU,IAAI,EAAE,IAAIN,QAAQ,CAAC,4CAA4C,EAAE,CAACC,SAAS,CAACM,WAAW,CAACU,qBAAqB,CAACJ,WAAW,CAAC;MAAC,CAAC,EAC9M;QAACT,IAAI,EAAE,0BAA0B;QAAEC,SAAS,EAAER,8BAA8B;QAAES,IAAI,EAAE,IAAIN,QAAQ,CAAC,0CAA0C,EAAE,CAACC,SAAS,CAACM,WAAW,CAACU,qBAAqB,CAACH,MAAM,CAAC;MAAC,CAAC,EACnM;QAACV,IAAI,EAAE,iBAAiB;QAAEC,SAAS,EAAEN,qCAAqC;QAAEO,IAAI,EAAE,IAAIN,QAAQ,CAAC,0CAA0C,EAAE,CAACC,SAAS,CAACM,WAAW,CAACU,qBAAqB,CAACR,SAAS,CAAC;MAAC,CAAC,EACpM;QAACL,IAAI,EAAE,SAAS;QAAEC,SAAS,EAAEP,8BAA8B;QAAEQ,IAAI,EAAE,IAAIN,QAAQ,CAAC,mCAAmC,EAAE,CAACC,SAAS,CAACM,WAAW,CAACW,aAAa,CAACT,SAAS,CAAC;MAAC,CAAC,CACzK,CAAC,EAEInB,YAAY;IAAA;EAAA;;;2EAEbY,qBAAqB;IAAAiB,OAAA,GAAAC,EAAA,CAAA9B,YAAA;IAAA+B,OAAA,GAFpB/B,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}