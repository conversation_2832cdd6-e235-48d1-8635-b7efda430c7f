{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { Observable, debounceTime, distinctUntilChanged, map, of, switchMap, take } from 'rxjs';\nimport { ComponentBase } from 'src/app/component.base';\nimport { AccountService } from 'src/app/service/account/AccountService';\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { RatingPlanService } from 'src/app/service/rating-plan/RatingPlanService';\nimport { ComboLazyControl } from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"../../../common-module/table/table.component\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/card\";\nimport * as i11 from \"primeng/dialog\";\nimport * as i12 from \"primeng/multiselect\";\nimport * as i13 from \"primeng/inputswitch\";\nimport * as i14 from \"primeng/radiobutton\";\nimport * as i15 from \"src/app/service/rating-plan/RatingPlanService\";\nimport * as i16 from \"src/app/service/account/AccountService\";\nfunction UpdatePlanComponent_p_card_5_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.tranService.translate(\"ratingPlan.error.lengthError_64\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.tranService.translate(\"ratingPlan.error.characterError_code\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.tranService.translate(\"ratingPlan.error.existedCodeError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.tranService.translate(\"ratingPlan.error.lengthError_255\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.tranService.translate(\"global.message.wrongFormatName\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.tranService.translate(\"ratingPlan.error.existedNameError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.tranService.translate(\"ratingPlan.error.lengthError_64\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.tranService.translate(\"ratingPlan.error.characterError_code\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.tranService.translate(\"ratingPlan.error.lengthError_255\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nconst _c0 = function () {\n  return {\n    value: 0\n  };\n};\nfunction UpdatePlanComponent_p_card_5_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"p-radioButton\", 103);\n    i0.ɵɵlistener(\"ngModelChange\", function UpdatePlanComponent_p_card_5_div_85_Template_p_radioButton_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.selectedPaidCategory = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 104);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const paidType_r57 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"inputId\", paidType_r57.key)(\"value\", paidType_r57.key)(\"ngModel\", ctx_r19.selectedPaidCategory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", paidType_r57.key);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(paidType_r57.name);\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.tranService.translate(\"ratingPlan.cycle.day\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.tranService.translate(\"ratingPlan.cycle.month\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.tranService.translate(\"ratingPlan.text.dayMonth\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r24.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_131_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"p-multiSelect\", 111);\n    i0.ɵɵlistener(\"onChange\", function UpdatePlanComponent_p_card_5_div_131_div_5_Template_p_multiSelect_onChange_1_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r63.changeProvince());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r60.provinces)(\"showToggleAll\", false)(\"placeholder\", ctx_r60.placeHolder.provinceCode);\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_131_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r61.provinceInfo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r61.provinceInfo, \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_131_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 113);\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"label\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 26);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, UpdatePlanComponent_p_card_5_div_131_div_5_Template, 2, 3, \"div\", 107);\n    i0.ɵɵtemplate(6, UpdatePlanComponent_p_card_5_div_131_div_6_Template, 2, 2, \"div\", 108);\n    i0.ɵɵtemplate(7, UpdatePlanComponent_p_card_5_div_131_div_7_Template, 1, 0, \"div\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r28.tranService.translate(\"ratingPlan.label.province\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.userType == ctx_r28.allUserType.ADMIN);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.userType != ctx_r28.allUserType.ADMIN);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.userType == ctx_r28.allUserType.ADMIN);\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_136_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"label\", 115);\n    i0.ɵɵlistener(\"click\", function UpdatePlanComponent_p_card_5_div_136_Template_label_click_1_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.openDialogAddCustomerAccount());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r30.tranService.translate(\"account.label.addCustomerAccount\"));\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_154_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_155_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_168_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r34.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_169_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r35.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_170_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_180_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_181_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r38.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_190_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_191_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_213_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_214_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_215_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r43.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_217_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r44.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_218_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r45.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_219_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_234_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r47.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_235_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r48.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_237_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r49.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_238_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_250_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r51.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_251_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r52.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_263_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_264_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_276_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r55.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_div_277_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r56.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction UpdatePlanComponent_p_card_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-card\", 20)(1, \"form\", 21);\n    i0.ɵɵlistener(\"submit\", function UpdatePlanComponent_p_card_5_Template_form_submit_1_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.submitForm());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24)(5, \"label\", 25);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementStart(7, \"span\", 26);\n    i0.ɵɵtext(8, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 27);\n    i0.ɵɵelement(10, \"input\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 29);\n    i0.ɵɵelement(12, \"div\", 30);\n    i0.ɵɵelementStart(13, \"div\", 31);\n    i0.ɵɵtemplate(14, UpdatePlanComponent_p_card_5_div_14_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(15, UpdatePlanComponent_p_card_5_div_15_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(16, UpdatePlanComponent_p_card_5_div_16_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(17, UpdatePlanComponent_p_card_5_div_17_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 33)(19, \"label\", 34);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementStart(21, \"span\", 26);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 27);\n    i0.ɵɵelement(24, \"input\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 29);\n    i0.ɵɵelement(26, \"div\", 30);\n    i0.ɵɵelementStart(27, \"div\", 31);\n    i0.ɵɵtemplate(28, UpdatePlanComponent_p_card_5_div_28_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(29, UpdatePlanComponent_p_card_5_div_29_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(30, UpdatePlanComponent_p_card_5_div_30_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(31, UpdatePlanComponent_p_card_5_div_31_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 33)(33, \"label\", 36);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementStart(35, \"span\", 26);\n    i0.ɵɵtext(36, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 27);\n    i0.ɵɵelement(38, \"input\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 29);\n    i0.ɵɵelement(40, \"div\", 30);\n    i0.ɵɵelementStart(41, \"div\", 31);\n    i0.ɵɵtemplate(42, UpdatePlanComponent_p_card_5_div_42_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(43, UpdatePlanComponent_p_card_5_div_43_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(44, UpdatePlanComponent_p_card_5_div_44_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 33)(46, \"label\", 38);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementStart(48, \"span\", 26);\n    i0.ɵɵtext(49, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 27);\n    i0.ɵɵelement(51, \"p-dropdown\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 29);\n    i0.ɵɵelement(53, \"div\", 30);\n    i0.ɵɵelementStart(54, \"div\", 31);\n    i0.ɵɵtemplate(55, UpdatePlanComponent_p_card_5_div_55_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 40)(57, \"label\", 41);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 27);\n    i0.ɵɵelement(60, \"textarea\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 29);\n    i0.ɵɵelement(62, \"div\", 30);\n    i0.ɵɵelementStart(63, \"div\", 31);\n    i0.ɵɵtemplate(64, UpdatePlanComponent_p_card_5_div_64_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 23)(66, \"div\", 24)(67, \"label\", 43);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementStart(69, \"span\", 26);\n    i0.ɵɵtext(70, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 27)(72, \"input\", 44);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_72_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_72_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 45);\n    i0.ɵɵtext(74, \" (\\u0111\\u1ED3ng/th\\u00E1ng \");\n    i0.ɵɵelement(75, \"br\");\n    i0.ɵɵtext(76, \" \\u0111\\u00E3 g\\u1ED3m VAT) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 29);\n    i0.ɵɵelement(78, \"div\", 30);\n    i0.ɵɵelementStart(79, \"div\", 31);\n    i0.ɵɵtemplate(80, UpdatePlanComponent_p_card_5_div_80_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(81, UpdatePlanComponent_p_card_5_div_81_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(82, UpdatePlanComponent_p_card_5_div_82_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 46)(84, \"div\", 47);\n    i0.ɵɵtemplate(85, UpdatePlanComponent_p_card_5_div_85_Template, 4, 5, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"div\", 49)(87, \"label\", 50);\n    i0.ɵɵtext(88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(89, \"div\", 27);\n    i0.ɵɵelement(90, \"p-inputSwitch\", 51);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(91, \"div\", 33)(92, \"label\", 52);\n    i0.ɵɵtext(93);\n    i0.ɵɵelementStart(94, \"span\", 26);\n    i0.ɵɵtext(95, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(96, \"div\", 27);\n    i0.ɵɵelement(97, \"p-dropdown\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(98, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"div\", 29);\n    i0.ɵɵelement(100, \"div\", 30);\n    i0.ɵɵelementStart(101, \"div\", 31);\n    i0.ɵɵtemplate(102, UpdatePlanComponent_p_card_5_div_102_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(103, \"div\", 33)(104, \"label\", 54);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementStart(106, \"span\", 26);\n    i0.ɵɵtext(107, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(108, \"div\", 27)(109, \"input\", 55);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_109_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_109_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(110, UpdatePlanComponent_p_card_5_div_110_Template, 2, 1, \"div\", 56);\n    i0.ɵɵtemplate(111, UpdatePlanComponent_p_card_5_div_111_Template, 2, 1, \"div\", 56);\n    i0.ɵɵtemplate(112, UpdatePlanComponent_p_card_5_div_112_Template, 2, 1, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(113, \"div\", 29);\n    i0.ɵɵelement(114, \"div\", 30);\n    i0.ɵɵelementStart(115, \"div\", 31);\n    i0.ɵɵtemplate(116, UpdatePlanComponent_p_card_5_div_116_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(117, UpdatePlanComponent_p_card_5_div_117_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(118, UpdatePlanComponent_p_card_5_div_118_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(119, \"div\", 33)(120, \"label\", 57);\n    i0.ɵɵtext(121);\n    i0.ɵɵelementStart(122, \"span\", 26);\n    i0.ɵɵtext(123, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(124, \"div\", 27)(125, \"p-dropdown\", 58);\n    i0.ɵɵlistener(\"onChange\", function UpdatePlanComponent_p_card_5_Template_p_dropdown_onChange_125_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.onDropdownChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(126, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(127, \"div\", 29);\n    i0.ɵɵelement(128, \"div\", 30);\n    i0.ɵɵelementStart(129, \"div\", 31);\n    i0.ɵɵtemplate(130, UpdatePlanComponent_p_card_5_div_130_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(131, UpdatePlanComponent_p_card_5_div_131_Template, 8, 4, \"div\", 59);\n    i0.ɵɵelementStart(132, \"div\", 60);\n    i0.ɵɵelement(133, \"div\", 30);\n    i0.ɵɵelementStart(134, \"div\", 31);\n    i0.ɵɵtemplate(135, UpdatePlanComponent_p_card_5_div_135_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(136, UpdatePlanComponent_p_card_5_div_136_Template, 3, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(137, \"h4\", 62);\n    i0.ɵɵtext(138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"div\", 22)(140, \"div\", 63)(141, \"div\", 24)(142, \"label\", 64);\n    i0.ɵɵtext(143);\n    i0.ɵɵelementStart(144, \"span\", 26);\n    i0.ɵɵtext(145, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(146, \"div\", 27)(147, \"input\", 65);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_147_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_147_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(148, \"div\", 66);\n    i0.ɵɵtext(149, \" (MB) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(150, \"div\", 29);\n    i0.ɵɵelement(151, \"div\", 67);\n    i0.ɵɵelementStart(152, \"div\", 31);\n    i0.ɵɵtemplate(153, UpdatePlanComponent_p_card_5_div_153_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(154, UpdatePlanComponent_p_card_5_div_154_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(155, UpdatePlanComponent_p_card_5_div_155_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(156, \"div\", 33)(157, \"label\", 64);\n    i0.ɵɵtext(158);\n    i0.ɵɵelementStart(159, \"span\", 26);\n    i0.ɵɵtext(160, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(161, \"div\", 27)(162, \"input\", 68);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_162_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_162_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(163, \"div\", 66);\n    i0.ɵɵtext(164, \" (MB) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(165, \"div\", 29);\n    i0.ɵɵelement(166, \"div\", 67);\n    i0.ɵɵelementStart(167, \"div\", 31);\n    i0.ɵɵtemplate(168, UpdatePlanComponent_p_card_5_div_168_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(169, UpdatePlanComponent_p_card_5_div_169_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(170, UpdatePlanComponent_p_card_5_div_170_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(171, \"div\", 63)(172, \"div\", 24)(173, \"label\", 69);\n    i0.ɵɵtext(174);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(175, \"div\", 27)(176, \"input\", 70);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_176_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_176_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(177, \"div\", 29);\n    i0.ɵɵelement(178, \"div\", 67);\n    i0.ɵɵelementStart(179, \"div\", 31);\n    i0.ɵɵtemplate(180, UpdatePlanComponent_p_card_5_div_180_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(181, UpdatePlanComponent_p_card_5_div_181_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(182, \"div\", 33)(183, \"label\", 71);\n    i0.ɵɵtext(184);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(185, \"div\", 27)(186, \"input\", 72);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_186_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_186_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(187, \"div\", 29);\n    i0.ɵɵelement(188, \"div\", 67);\n    i0.ɵɵelementStart(189, \"div\", 31);\n    i0.ɵɵtemplate(190, UpdatePlanComponent_p_card_5_div_190_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(191, UpdatePlanComponent_p_card_5_div_191_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(192, \"div\", 73)(193, \"h4\", 74);\n    i0.ɵɵtext(194);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(195, \"p-inputSwitch\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function UpdatePlanComponent_p_card_5_Template_p_inputSwitch_ngModelChange_195_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.isFlexible = $event);\n    })(\"onChange\", function UpdatePlanComponent_p_card_5_Template_p_inputSwitch_onChange_195_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onSwitchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(196, \"div\", 76)(197, \"div\", 24)(198, \"label\", 77);\n    i0.ɵɵtext(199);\n    i0.ɵɵelementStart(200, \"span\", 26);\n    i0.ɵɵtext(201, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(202, \"div\", 27)(203, \"input\", 78);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_203_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_203_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(204, \"div\", 79);\n    i0.ɵɵtext(205, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(206, \"div\", 27)(207, \"input\", 80);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_207_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_207_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r87 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r87.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(208, \"div\", 81);\n    i0.ɵɵtext(209, \"(KB)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(210, \"div\", 60);\n    i0.ɵɵelement(211, \"div\", 82);\n    i0.ɵɵelementStart(212, \"div\", 83);\n    i0.ɵɵtemplate(213, UpdatePlanComponent_p_card_5_div_213_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(214, UpdatePlanComponent_p_card_5_div_214_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(215, UpdatePlanComponent_p_card_5_div_215_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(216, \"div\", 84);\n    i0.ɵɵtemplate(217, UpdatePlanComponent_p_card_5_div_217_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(218, UpdatePlanComponent_p_card_5_div_218_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(219, UpdatePlanComponent_p_card_5_div_219_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(220, \"div\", 33)(221, \"label\", 85);\n    i0.ɵɵtext(222);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(223, \"div\", 27)(224, \"input\", 86);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_224_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_224_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r89 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r89.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(225, \"div\", 79);\n    i0.ɵɵtext(226, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(227, \"div\", 27)(228, \"input\", 87);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_228_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r90 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r90.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_228_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r91 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r91.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(229, \"div\", 81);\n    i0.ɵɵtext(230, \"(Kbps)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(231, \"div\", 60);\n    i0.ɵɵelement(232, \"div\", 82);\n    i0.ɵɵelementStart(233, \"div\", 83);\n    i0.ɵɵtemplate(234, UpdatePlanComponent_p_card_5_div_234_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(235, UpdatePlanComponent_p_card_5_div_235_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(236, \"div\", 84);\n    i0.ɵɵtemplate(237, UpdatePlanComponent_p_card_5_div_237_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(238, UpdatePlanComponent_p_card_5_div_238_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(239, \"div\", 88)(240, \"label\", 89);\n    i0.ɵɵtext(241);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(242, \"div\", 27)(243, \"input\", 90);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_243_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r92 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r92.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_243_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r93 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r93.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(244, \"div\", 79)(245, \"div\", 27)(246, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(247, \"div\", 60);\n    i0.ɵɵelement(248, \"div\", 91);\n    i0.ɵɵelementStart(249, \"div\", 92);\n    i0.ɵɵtemplate(250, UpdatePlanComponent_p_card_5_div_250_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(251, UpdatePlanComponent_p_card_5_div_251_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(252, \"div\", 88)(253, \"label\", 93);\n    i0.ɵɵtext(254);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(255, \"div\", 27)(256, \"input\", 94);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_256_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r94 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r94.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_256_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(257, \"div\", 79)(258, \"div\", 27)(259, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(260, \"div\", 60);\n    i0.ɵɵelement(261, \"div\", 91);\n    i0.ɵɵelementStart(262, \"div\", 92);\n    i0.ɵɵtemplate(263, UpdatePlanComponent_p_card_5_div_263_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(264, UpdatePlanComponent_p_card_5_div_264_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(265, \"div\", 95)(266, \"label\", 96);\n    i0.ɵɵtext(267);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(268, \"div\", 27)(269, \"input\", 97);\n    i0.ɵɵlistener(\"keydown\", function UpdatePlanComponent_p_card_5_Template_input_keydown_269_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r96 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r96.blockMinus($event));\n    })(\"input\", function UpdatePlanComponent_p_card_5_Template_input_input_269_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.checkInputValue($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(270, \"div\", 79)(271, \"div\", 27)(272, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(273, \"div\", 60);\n    i0.ɵɵelement(274, \"div\", 91);\n    i0.ɵɵelementStart(275, \"div\", 92);\n    i0.ɵɵtemplate(276, UpdatePlanComponent_p_card_5_div_276_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(277, UpdatePlanComponent_p_card_5_div_277_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(278, \"div\", 98)(279, \"a\", 99);\n    i0.ɵɵelement(280, \"button\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(281, \"button\", 101);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.updatePlanForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.planCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isRead)(\"placeholder\", ctx_r0.placeHolder.planCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlanCodeValid && ctx_r0.updatePlanForm.get(\"code\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlanCodeValid && ctx_r0.updatePlanForm.get(\"code\").hasError(\"maxlength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlanCodeValid && ctx_r0.updatePlanForm.get(\"code\").hasError(\"invalidCharacters\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlanCodeValid && ctx_r0.updatePlanForm.get(\"code\").hasError(\"exited\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.planName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.planName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlaneNameValid && ctx_r0.updatePlanForm.get(\"name\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlaneNameValid && ctx_r0.updatePlanForm.get(\"name\").hasError(\"maxlength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlaneNameValid && ctx_r0.updatePlanForm.get(\"name\").hasError(\"invalidCharacters\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlaneNameValid && ctx_r0.updatePlanForm.get(\"name\").hasError(\"exited\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.dispatchCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isRead)(\"placeholder\", ctx_r0.placeHolder.dispatchCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDispatchCodeValid && ctx_r0.updatePlanForm.get(\"dispatchCode\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDispatchCodeValid && ctx_r0.updatePlanForm.get(\"dispatchCode\").hasError(\"maxlength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDispatchCodeValid && ctx_r0.updatePlanForm.get(\"dispatchCode\").hasError(\"invalidCharacters\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.customerType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r0.customerTypes)(\"placeholder\", ctx_r0.placeHolder.customerType);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isCustomerTypeValid && ctx_r0.updatePlanForm.get(\"customerType\").hasError(\"required\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDescriptionValid && ctx_r0.updatePlanForm.get(\"description\").hasError(\"maxlength\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.subscriptionFee\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.subscriptionFee);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSubscriptionFeeValid && ctx_r0.updatePlanForm.get(\"subscriptionFee\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSubscriptionFeeValid && ctx_r0.updatePlanForm.get(\"subscriptionFee\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSubscriptionFeeValid && ctx_r0.updatePlanForm.get(\"subscriptionFee\").hasError(\"min\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.paidCategories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.autoReload\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.cycle\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r0.cycles)(\"placeholder\", ctx_r0.placeHolder.planCycle);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlanCycleValid && ctx_r0.updatePlanForm.get(\"cycleTimeUnit\").hasError(\"required\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.duration\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.duration);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updatePlanForm.get(\"cycleTimeUnit\").value == \"1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updatePlanForm.get(\"cycleTimeUnit\").value == \"3\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.updatePlanForm.get(\"cycleTimeUnit\").value != \"1\" && ctx_r0.updatePlanForm.get(\"cycleTimeUnit\").value != \"3\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDurationValid && ctx_r0.updatePlanForm.get(\"cycleInterval\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDurationValid && ctx_r0.updatePlanForm.get(\"cycleInterval\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDurationValid && ctx_r0.updatePlanForm.get(\"cycleInterval\").hasError(\"min\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.ratingScope\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r0.ratingScopes)(\"placeholder\", ctx_r0.placeHolder.planScope);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPlanScopeValid && ctx_r0.updatePlanForm.get(\"ratingScope\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isProvince);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isProvinceCodeValid && ctx_r0.updatePlanForm.get(\"provinceCode\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isCustomer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.flat\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.freeData\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.freeData);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFreeDataValid && ctx_r0.updatePlanForm.get(\"limitDataUsage\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFreeDataValid && ctx_r0.updatePlanForm.get(\"limitDataUsage\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFreeDataValid && ctx_r0.updatePlanForm.get(\"limitDataUsage\").hasError(\"min\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.placeHolder.dataMax\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.dataMax);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDataMaxValid && ctx_r0.updatePlanForm.get(\"dataMax\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDataMaxValid && ctx_r0.updatePlanForm.get(\"dataMax\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDataMaxValid && ctx_r0.updatePlanForm.get(\"dataMax\").hasError(\"min\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.insideSMSFree\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.insideSMSFree);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLimitInsideSMSFreeValid && ctx_r0.updatePlanForm.get(\"limitSmsInside\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLimitInsideSMSFreeValid && ctx_r0.updatePlanForm.get(\"limitSmsInside\").hasError(\"min\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.outsideSMSFree\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.placeHolder.outsideSMSFree);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLimitOutsideSMSFreeValid && ctx_r0.updatePlanForm.get(\"limitSmsOutside\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLimitOutsideSMSFreeValid && ctx_r0.updatePlanForm.get(\"limitSmsOutside\").hasError(\"min\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.flexible\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.isFlexible);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.feePerUnit\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.feePerUnit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.feePerUnit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerUnitNumberatorValid && ctx_r0.updatePlanForm.get(\"feePerDataUnit\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerUnitNumberatorValid && ctx_r0.updatePlanForm.get(\"feePerDataUnit\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerUnitNumberatorValid && ctx_r0.updatePlanForm.get(\"feePerDataUnit\").hasError(\"min\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerUnitDenominatorValid && ctx_r0.updatePlanForm.get(\"dataRoundUnit\").hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerUnitDenominatorValid && ctx_r0.updatePlanForm.get(\"dataRoundUnit\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerUnitDenominatorValid && ctx_r0.updatePlanForm.get(\"dataRoundUnit\").hasError(\"min\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.squeezeSpeed\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.squeezedSpeed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.squeezedSpeed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSqueezeSpeedNumberatorValid && ctx_r0.updatePlanForm.get(\"downSpeed\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSqueezeSpeedNumberatorValid && ctx_r0.updatePlanForm.get(\"downSpeed\").hasError(\"min\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSqueezeSpeedDenominatorValid && ctx_r0.updatePlanForm.get(\"squeezedSpeed\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isSqueezeSpeedDenominatorValid && ctx_r0.updatePlanForm.get(\"squeezedSpeed\").hasError(\"min\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.feePerInsideSMS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.feePerInsideSMS);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerInsideSMSValid && ctx_r0.updatePlanForm.get(\"feeSmsInside\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerInsideSMSValid && ctx_r0.updatePlanForm.get(\"feeSmsInside\").hasError(\"min\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.feePerOutsideSMS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.feePerOutsideSMS);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerOutsideSMSValid && ctx_r0.updatePlanForm.get(\"feeSmsOutside\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isFeePerOutsideSMSValid && ctx_r0.updatePlanForm.get(\"feeSmsOutside\").hasError(\"min\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"ratingPlan.label.maxFee\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isFlexible)(\"placeholder\", ctx_r0.placeHolder.maxFee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", !ctx_r0.isFlexible ? \"gray\" : \"black\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isMaxFeeValid && ctx_r0.updatePlanForm.get(\"maximumFee\").hasError(\"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isMaxFeeValid && ctx_r0.updatePlanForm.get(\"maximumFee\").hasError(\"min\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.save\"))(\"disabled\", ctx_r0.isPlanCodeExisted || ctx_r0.isPlanNameExisted || ctx_r0.updatePlanForm.invalid);\n  }\n}\nfunction UpdatePlanComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 10)(1, \"p-multiSelect\", 116);\n    i0.ɵɵlistener(\"ngModelChange\", function UpdatePlanComponent_span_21_Template_p_multiSelect_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.searchInfoUser.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 117);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfoUser.provinceCode)(\"options\", ctx_r1.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"ratingPlan.label.province\"));\n  }\n}\nfunction UpdatePlanComponent_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.tranService.translate(\"account.label.province\"), \": \", ctx_r2.provinceInfo, \"\");\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"850px\"\n  };\n};\nconst _c2 = function () {\n  return [5, 10, 20, 25, 50];\n};\nexport class UpdatePlanComponent extends ComponentBase {\n  constructor(ratingPlanService, accountService, formBuilder, injector) {\n    super(injector);\n    this.ratingPlanService = ratingPlanService;\n    this.accountService = accountService;\n    this.formBuilder = formBuilder;\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.ratingplanmgmt\"),\n      routerLink: '../../'\n    }, {\n      label: this.tranService.translate(\"global.menu.listplan\"),\n      routerLink: '../../'\n    }, {\n      label: this.tranService.translate(\"global.button.edit\")\n    }];\n    this.isPlanCodeExisted = false;\n    this.isPlanNameExisted = false;\n    this.customerTypes = [{\n      id: 1,\n      name: this.tranService.translate(\"ratingPlan.customerType.personal\")\n    }, {\n      id: 2,\n      name: this.tranService.translate(\"ratingPlan.customerType.enterprise\")\n    }, {\n      id: 0,\n      name: this.tranService.translate(\"ratingPlan.customerType.agency\")\n    }\n    // ,\n    // {\n    //   id: 0,\n    //   name: this.tranService.translate(\"ratingPlan.customerType.agency\"),\n    // },\n    ];\n\n    this.ratingScopes = [{\n      id: 0,\n      name: this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\")\n    }, {\n      id: 2,\n      name: this.tranService.translate(\"ratingPlan.ratingScope.province\")\n    }, {\n      id: 1,\n      name: this.tranService.translate(\"ratingPlan.ratingScope.customer\")\n    }];\n    this.userType = this.sessionService.userInfo.type;\n    this.allUserType = CONSTANTS.USER_TYPE;\n    this.provinceInfo = \"\";\n    this.isPlanCodeValid = false;\n    this.isPlaneNameValid = false;\n    this.isDispatchCodeValid = false;\n    this.isCustomerTypeValid = false;\n    this.isSubscriptionFeeValid = false;\n    this.isSubscriptionTypeValid = false; // Đã set default\n    this.isPlanScopeValid = false;\n    this.isProvinceCodeValid = false;\n    this.isPlanCycleValid = false;\n    this.isDurationValid = false;\n    this.isDescriptionValid = false;\n    this.isFreeDataValid = false;\n    this.isLimitInsideSMSFreeValid = false;\n    this.isLimitOutsideSMSFreeValid = false;\n    this.isFeePerUnitNumberatorValid = false;\n    this.isFeePerUnitDenominatorValid = false;\n    this.isSqueezeSpeedNumberatorValid = false;\n    this.isSqueezeSpeedDenominatorValid = false;\n    this.isFeePerInsideSMSValid = false;\n    this.isFeePerOutsideSMSValid = false;\n    this.isMaxFeeValid = false;\n    this.isDataMaxValid = false;\n    this.controlComboSelect = new ComboLazyControl();\n    this.isProvince = false;\n    this.isCustomer = false;\n    this.isRead = false;\n    this.isShowDialogAddCustomerAccount = false;\n    this.selectItemsUser = [];\n    this.selectItemsUserOld = [{\n      id: -1,\n      provinceCode: \"\"\n    }];\n    this.cycles = [{\n      id: 1,\n      name: this.tranService.translate(\"ratingPlan.cycle.day\")\n    }, {\n      id: 3,\n      name: this.tranService.translate(\"ratingPlan.cycle.month\")\n    }];\n    this.isFlexible = false;\n    this.isReload = false;\n    this.paidCategories = [{\n      name: 'Trả trước',\n      key: '1'\n    }, {\n      name: 'Trả sau',\n      key: '0'\n    }];\n    this.selectedPaidCategory = null;\n    this.placeHolderDescription = this.tranService.translate(\"groupSim.placeHolder.description\");\n    this.placeHolder = {\n      planCode: this.tranService.translate(\"ratingPlan.placeHolder.planCode\"),\n      planName: this.tranService.translate(\"ratingPlan.placeHolder.planeName\"),\n      dispatchCode: this.tranService.translate(\"ratingPlan.placeHolder.dispatchCode\"),\n      customerType: this.tranService.translate(\"ratingPlan.placeHolder.customerType\"),\n      description: this.tranService.translate(\"ratingPlan.placeHolder.description\"),\n      subscriptionFee: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionFee\"),\n      subscriptionType: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionType\"),\n      planScope: this.tranService.translate(\"ratingPlan.placeHolder.planScope\"),\n      provinceCode: this.tranService.translate(\"ratingPlan.placeHolder.provinceCode\"),\n      planCycle: this.tranService.translate(\"ratingPlan.placeHolder.planCycle\"),\n      duration: this.tranService.translate(\"ratingPlan.placeHolder.duration\"),\n      freeData: this.tranService.translate(\"ratingPlan.placeHolder.freeData\"),\n      insideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.insideSMSFree\"),\n      outsideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.outsideSMSFree\"),\n      feePerUnit: this.tranService.translate(\"ratingPlan.placeHolder.feePerUnit\"),\n      squeezedSpeed: this.tranService.translate(\"ratingPlan.placeHolder.squeezeSpeed\"),\n      feePerInsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerInsideSMS\"),\n      feePerOutsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerOutsideSMS\"),\n      maxFee: this.tranService.translate(\"ratingPlan.placeHolder.maxFee\"),\n      dataMax: this.tranService.translate(\"ratingPlan.placeHolder.dataMax\")\n    };\n  }\n  checkCodeExisted(query) {\n    return new Observable(observer => {\n      this.ratingPlanService.checkingPlanCodeExisted(query, response => {\n        observer.next(response);\n        observer.complete();\n      });\n    });\n  }\n  checkNameExisted(query) {\n    return new Observable(observer => {\n      this.ratingPlanService.checkingPlanNameExisted(query, response => {\n        observer.next(response);\n        observer.complete();\n      });\n    });\n  }\n  planCodeValidator() {\n    return control => {\n      if (!control.valueChanges || control.pristine) {\n        return of(null);\n      } else return control.valueChanges.pipe(debounceTime(500), distinctUntilChanged(), switchMap(value => {\n        this.currentPlanCodeInput = value;\n        return this.checkCodeExisted({\n          code: value\n        });\n      }), take(1), map(result => {\n        if (result === 0 || this.currentPlanCodeInput == this.initialData.code) {\n          this.isPlanCodeExisted = false;\n          return null;\n        } else {\n          this.isPlanCodeExisted = true;\n          return {\n            'exited': true\n          };\n        }\n      }));\n    };\n  }\n  planNameValidator() {\n    return control => {\n      if (!control.valueChanges || control.pristine) {\n        return of(null);\n      } else return control.valueChanges.pipe(debounceTime(500), distinctUntilChanged(), switchMap(value => {\n        this.currentPlanNameInput = value;\n        return this.checkNameExisted({\n          name: value\n        });\n      }), take(1), map(result => {\n        if (result === 0 || this.currentPlanNameInput == this.initialData.name) {\n          this.isPlanNameExisted = false;\n          return null;\n        } else {\n          this.isPlanNameExisted = true;\n          return {\n            'exited': true\n          };\n        }\n      }));\n    };\n  }\n  customCodeCharacterValidator() {\n    return control => {\n      const value = control.value;\n      const isValid = /^[a-zA-Z0-9\\-_]*$/.test(value);\n      return isValid ? null : {\n        'invalidCharacters': {\n          value\n        }\n      };\n    };\n  }\n  customNameCharacterValidator() {\n    return control => {\n      const value = control.value;\n      if (value == '') {\n        return null;\n      }\n      const isValid = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơỲỴÝỳỵỷỹƯứừữựụợ́̉̃À-ỹ0-9 _-]+$/.test(value);\n      return isValid ? null : {\n        'invalidCharacters': {\n          value\n        }\n      };\n    };\n  }\n  blockMinus(event) {\n    const invalidChars = ['-', '+', ',', '.', 'e', 'E', 'r', 'R']; // Danh sách các ký tự không cho phép\n    if (invalidChars.includes(event.key)) {\n      event.preventDefault();\n    }\n    // if (event.key === '-' || event.key ==='+' || event.key === ',' || event.key === '.') {\n    //   event.preventDefault();\n    // }\n  }\n\n  checkInputValue(event) {\n    const input = event.target;\n    input.value = input.value.replace(/[^0-9]/g, ''); // Chỉ cho phép nhập số\n  }\n\n  onDropdownChange(event) {\n    if (event.value == 0) {\n      this.isProvince = false;\n      this.isCustomer = false;\n      this.updatePlanForm.get('provinceCode').disable({\n        emitEvent: false\n      });\n      // this.createPlanForm.get('userIds').disable({ emitEvent: false });\n    } else if (event.value == 1) {\n      // gói cước loại khách hàng\n      this.isProvince = true;\n      this.isCustomer = false;\n      this.updatePlanForm.get('provinceCode').enable({\n        emitEvent: false\n      });\n      this.changeProvince();\n      // this.createPlanForm.get('userIds').enable({ emitEvent: false });\n    } else if (event.value == 2) {\n      // gói cước loại tỉnh/thành phố\n      this.isProvince = true;\n      this.isCustomer = false;\n      this.updatePlanForm.get('provinceCode').enable({\n        emitEvent: false\n      });\n      this.changeProvince();\n      // this.createPlanForm.get('userIds').disable({ emitEvent: false });\n    }\n  }\n\n  onSwitchChange() {\n    if (this.isFlexible) {\n      this.updatePlanForm.get('feePerDataUnit').enable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('dataRoundUnit').enable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('downSpeed').enable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('squeezedSpeed').enable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('feeSmsInside').enable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('feeSmsOutside').enable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('maximumFee').enable({\n        emitEvent: false\n      });\n    } else {\n      this.updatePlanForm.get('feePerDataUnit').disable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('dataRoundUnit').disable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('downSpeed').disable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('squeezedSpeed').disable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('feeSmsInside').disable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('feeSmsOutside').disable({\n        emitEvent: false\n      });\n      this.updatePlanForm.get('maximumFee').disable({\n        emitEvent: false\n      });\n    }\n  }\n  submitForm() {\n    this.messageCommonService.onload();\n    let me = this;\n    if (this.updatePlanForm.valid) {\n      let data = {\n        ...this.updatePlanForm.value\n      };\n      if (data.reload) {\n        data.reload = 1;\n      } else {\n        data.reload = 0;\n      }\n      if (data.flexible) {\n        data.flexible = 1;\n        data.uploadSpeed = data.downSpeed - data.squeezedSpeed;\n      } else {\n        data.flexible = 0;\n      }\n      if (this.selectItemsUser.length > 0) {\n        let provinceSelected = me.updatePlanForm.get(\"provinceCode\").value;\n        let currentSelected = me.selectItemsUser.filter(el => provinceSelected.includes(el.provinceCode)).map(e => e.id);\n        data.userIds = currentSelected;\n      }\n      this.ratingPlanService.editRatingPlan(this.idForEdit, data, response => {\n        // this.messageCommonService.success(\"Update okr\")\n        this.messageCommonService.success(this.tranService.translate('global.message.saveSuccess'));\n        this.router.navigate(['/plans']);\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  onChangeCustomers() {\n    this.updatePlanForm.get(\"userIds\").setValue(this.customerCode);\n  }\n  ngOnInit() {\n    let me = this;\n    if (!this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])) {\n      window.location.hash = \"/access\";\n    }\n    this.idForEdit = Number(this.route.snapshot.params[\"id\"]);\n    // this.provinces = [\n    //   {\n    //     id:\"1\",\n    //     name:\"Hà Nội\"\n    //   },\n    //   {\n    //     id:\"2\",\n    //     name:\"Hồ Chí Minh\"\n    //   },\n    //   {\n    //     id:\"3\",\n    //     name:\"Nghệ Tĩnh\"\n    //   },\n    // ]\n    this.optionTableAddCustomerAccount = {\n      hasClearSelected: false,\n      hasShowIndex: true,\n      hasShowChoose: true,\n      hasShowToggleColumn: false\n    };\n    this.dataSetAssignPlan = {\n      content: [],\n      total: 0\n    };\n    this.searchInfoUser = {\n      username: null,\n      fullName: null,\n      email: null,\n      provinceCode: null\n    };\n    this.formSearchUser = this.formBuilder.group(this.searchInfoUser);\n    this.columnsInfoUser = [{\n      name: this.tranService.translate(\"ratingPlan.label.username\"),\n      key: \"username\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.fullName\"),\n      key: \"fullName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.email\"),\n      key: \"email\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.accountService.getListProvince(data => {\n      this.provinces = data.map(el => {\n        return {\n          code: el.code,\n          name: `${el.name} (${el.code})`\n        };\n      });\n    });\n    // this.selectedPaidCategory = this.paidCategories[0].key\n    // console.log(this.selectedPaidCategory)\n    this.ratingPlanService.getById(this.idForEdit, response => {\n      me.initialData = response;\n      this.updatePlanForm = this.initFormGroup();\n      this.updatePlanForm.patchValue(response);\n      me.customerCode = response.userIds;\n      this.setupValidate();\n      this.selectedPaidCategory = response.paidType;\n      if (response.flexible == 1) {\n        this.isFlexible = true;\n        this.onSwitchChange();\n      }\n      if (response.ratingScope == 1) {\n        this.isProvince = true;\n        this.isCustomer = true;\n        this.updatePlanForm.get(\"provinceCode\").enable({\n          emitEvent: false\n        });\n        // this.updatePlanForm.get(\"userIds\").enable({emitEvent: false})\n        this.updatePlanForm.get('userIds').setValue(response.userIds);\n        this.searchInfoUser.provinceCode = response.provinceCode;\n        me.accountService.getUserAssignedOnRatingPlan({\n          ratingPlanId: response.id\n        }, resp => {\n          this.selectItemsUser = resp;\n          this.selectItemsUserOld = this.selectItemsUser;\n        });\n      } else if (response.ratingScope == 2) {\n        this.isProvince = true;\n        this.isCustomer = false;\n        this.updatePlanForm.get(\"provinceCode\").enable({\n          emitEvent: false\n        });\n        // this.updatePlanForm.get(\"userIds\").disable({emitEvent: false})\n      }\n\n      if (response.reload == 1) {\n        this.isReload = true;\n      }\n      if (response.status == 1 || response.status == 5) {\n        me.isRead = true;\n        // this.updatePlanForm.controls['code'].disable()\n        // this.updatePlanForm.controls['dispatchCode'].disable()\n        // this.updatePlanForm.get('code').disable({ emitEvent: false });\n        // this.updatePlanForm.get('dispatchCode').disable({ emitEvent: false });\n      }\n\n      this.updatePlanForm.updateValueAndValidity();\n      me.provinces.forEach(el => {\n        if (me.initialData.provinceCode.includes(el.code)) {\n          me.provinceInfo += `${el.name}, `;\n        }\n      });\n      if (me.provinceInfo.length > 0) {\n        me.provinceInfo = me.provinceInfo.substring(0, me.provinceInfo.length - 2);\n      }\n    });\n  }\n  initFormGroup() {\n    return new FormGroup({\n      status: new FormControl(this.initialData.status),\n      createdDate: new FormControl(this.initialData.createdDate),\n      createdBy: new FormControl(this.initialData.createdBy),\n      code: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()], [this.planCodeValidator()]),\n      name: new FormControl(\"\", [Validators.required, Validators.maxLength(255), this.customNameCharacterValidator()], this.planNameValidator()),\n      dispatchCode: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()]),\n      customerType: new FormControl(\"\", [Validators.required]),\n      subscriptionFee: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      paidType: new FormControl(\"\", [Validators.required]),\n      ratingScope: new FormControl(\"\", [Validators.required]),\n      provinceCode: new FormControl({\n        value: \"\",\n        disabled: !this.isProvince\n      }, [Validators.required]),\n      userIds: new FormControl({\n        value: null\n      }),\n      cycleTimeUnit: new FormControl(\"\", [Validators.required]),\n      cycleInterval: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      reload: new FormControl(0),\n      description: new FormControl(\"\", [Validators.maxLength(255)]),\n      limitDataUsage: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      limitSmsInside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),\n      limitSmsOutside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),\n      dataMax: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      flexible: new FormControl(0),\n      feePerDataUnit: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      dataRoundUnit: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      downSpeed: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      squeezedSpeed: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      feeSmsInside: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      feeSmsOutside: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      maximumFee: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      uploadSpeed: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      })\n    });\n  }\n  setupValidate() {\n    this.subPlanCode = this.updatePlanForm.get('code').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('code').errors;\n      if (errors) {\n        this.isPlanCodeValid = true;\n      } else {\n        this.isPlanCodeValid = false;\n      }\n    });\n    this.subPlaneName = this.updatePlanForm.get('name').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('name').errors;\n      if (errors) {\n        this.isPlaneNameValid = true;\n      } else {\n        this.isPlaneNameValid = false;\n      }\n    });\n    this.subDispatchCode = this.updatePlanForm.get('dispatchCode').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('dispatchCode').errors;\n      if (errors) {\n        this.isDispatchCodeValid = true;\n      } else {\n        this.isDispatchCodeValid = false;\n      }\n    });\n    this.subCustomerType = this.updatePlanForm.get('customerType').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('customerType').errors;\n      if (errors) {\n        this.isCustomerTypeValid = true;\n      } else {\n        this.isCustomerTypeValid = false;\n      }\n    });\n    this.subSubscriptionFee = this.updatePlanForm.get('subscriptionFee').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('subscriptionFee').errors;\n      if (errors) {\n        this.isSubscriptionFeeValid = true;\n      } else {\n        this.isSubscriptionFeeValid = false;\n      }\n    });\n    this.subSubscriptionType = this.updatePlanForm.get('paidType').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('paidType').errors;\n      if (errors) {\n        this.isSubscriptionTypeValid = true;\n      } else {\n        this.isSubscriptionTypeValid = false;\n      }\n    });\n    this.subPlanScope = this.updatePlanForm.get('ratingScope').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('ratingScope').errors;\n      if (errors) {\n        this.isPlanScopeValid = true;\n      } else {\n        this.isPlanScopeValid = false;\n      }\n    });\n    this.subProvinceCode = this.updatePlanForm.get('provinceCode').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('provinceCode').errors;\n      if (errors) {\n        this.isProvinceCodeValid = true;\n      } else {\n        this.isProvinceCodeValid = false;\n      }\n    });\n    this.subPlanCycle = this.updatePlanForm.get('cycleTimeUnit').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('cycleTimeUnit').errors;\n      if (errors) {\n        this.isPlanCycleValid = true;\n      } else {\n        this.isPlanCycleValid = false;\n      }\n    });\n    this.subDuration = this.updatePlanForm.get('cycleInterval').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('cycleInterval').errors;\n      if (errors) {\n        this.isDurationValid = true;\n      } else {\n        this.isDurationValid = false;\n      }\n    });\n    this.subDescription = this.updatePlanForm.get('description').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('description').errors;\n      if (errors) {\n        this.isDescriptionValid = true;\n      } else {\n        this.isDescriptionValid = false;\n      }\n    });\n    this.subFreeData = this.updatePlanForm.get('limitDataUsage').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('limitDataUsage').errors;\n      if (errors) {\n        this.isFreeDataValid = true;\n      } else {\n        this.isFreeDataValid = false;\n      }\n    });\n    this.subDataMax = this.updatePlanForm.get('dataMax').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('dataMax').errors;\n      if (errors) {\n        this.isDataMaxValid = true;\n      } else {\n        this.isDataMaxValid = false;\n      }\n    });\n    this.subLimitInsideSMSFree = this.updatePlanForm.get('limitSmsInside').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('limitSmsInside').errors;\n      if (errors) {\n        this.isLimitInsideSMSFreeValid = true;\n      } else {\n        this.isLimitInsideSMSFreeValid = false;\n      }\n    });\n    this.subLimitOutsideSMSFree = this.updatePlanForm.get('limitSmsOutside').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('limitSmsOutside').errors;\n      if (errors) {\n        this.isLimitOutsideSMSFreeValid = true;\n      } else {\n        this.isLimitOutsideSMSFreeValid = false;\n      }\n    });\n    this.subFeePerUnitNumberator = this.updatePlanForm.get('feePerDataUnit').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('feePerDataUnit').errors;\n      if (errors) {\n        this.isFeePerUnitNumberatorValid = true;\n      } else {\n        this.isFeePerUnitNumberatorValid = false;\n      }\n    });\n    this.subFeePerUnitDenominator = this.updatePlanForm.get('dataRoundUnit').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('dataRoundUnit').errors;\n      if (errors) {\n        this.isFeePerUnitDenominatorValid = true;\n      } else {\n        this.isFeePerUnitDenominatorValid = false;\n      }\n    });\n    this.subSqueezeSpeedNumberator = this.updatePlanForm.get('downSpeed').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('downSpeed').errors;\n      if (errors) {\n        this.isSqueezeSpeedNumberatorValid = true;\n      } else {\n        this.isSqueezeSpeedNumberatorValid = false;\n      }\n    });\n    this.subSqueezeSpeedDenominator = this.updatePlanForm.get('squeezedSpeed').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('squeezedSpeed').errors;\n      if (errors) {\n        this.isSqueezeSpeedDenominatorValid = true;\n      } else {\n        this.isSqueezeSpeedDenominatorValid = false;\n      }\n    });\n    this.subFeePerInsideSMS = this.updatePlanForm.get('feeSmsInside').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('feeSmsInside').errors;\n      if (errors) {\n        this.isFeePerInsideSMSValid = true;\n      } else {\n        this.isFeePerInsideSMSValid = false;\n      }\n    });\n    this.subFeePerOutsideSMS = this.updatePlanForm.get('feeSmsOutside').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('feeSmsOutside').errors;\n      if (errors) {\n        this.isFeePerOutsideSMSValid = true;\n      } else {\n        this.isFeePerOutsideSMSValid = false;\n      }\n    });\n    this.subMaxFee = this.updatePlanForm.get('maximumFee').statusChanges.subscribe(() => {\n      const errors = this.updatePlanForm.get('maximumFee').errors;\n      if (errors) {\n        this.isMaxFeeValid = true;\n      } else {\n        this.isMaxFeeValid = false;\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.subPlanCode && !this.subPlanCode.closed) {\n      this.subPlanCode.unsubscribe();\n    }\n    if (this.subPlaneName && !this.subPlaneName.closed) {\n      this.subPlaneName.unsubscribe();\n    }\n    if (this.subDispatchCode && !this.subDispatchCode.closed) {\n      this.subDispatchCode.unsubscribe();\n    }\n    if (this.subCustomerType && !this.subCustomerType.closed) {\n      this.subCustomerType.unsubscribe();\n    }\n    if (this.subSubscriptionFee && !this.subSubscriptionFee.closed) {\n      this.subSubscriptionFee.unsubscribe();\n    }\n    if (this.subSubscriptionType && !this.subSubscriptionType.closed) {\n      this.subSubscriptionType.unsubscribe();\n    }\n    if (this.subPlanScope && !this.subPlanScope.closed) {\n      this.subPlanScope.unsubscribe();\n    }\n    if (this.subProvinceCode && !this.subProvinceCode.closed) {\n      this.subProvinceCode.unsubscribe();\n    }\n    if (this.subPlanCycle && !this.subPlanCycle.closed) {\n      this.subPlanCycle.unsubscribe();\n    }\n    if (this.subDuration && !this.subDuration.closed) {\n      this.subDuration.unsubscribe();\n    }\n    if (this.subDescription && !this.subDescription.closed) {\n      this.subDescription.unsubscribe();\n    }\n    if (this.subDescription && !this.subDescription.closed) {\n      this.subDescription.unsubscribe();\n    }\n    if (this.subFreeData && !this.subFreeData.closed) {\n      this.subFreeData.unsubscribe();\n    }\n    if (this.subLimitInsideSMSFree && !this.subLimitInsideSMSFree.closed) {\n      this.subLimitInsideSMSFree.unsubscribe();\n    }\n    if (this.subLimitOutsideSMSFree && !this.subLimitOutsideSMSFree.closed) {\n      this.subLimitOutsideSMSFree.unsubscribe();\n    }\n    if (this.subFeePerUnitNumberator && !this.subFeePerUnitNumberator.closed) {\n      this.subFeePerUnitNumberator.unsubscribe();\n    }\n    if (this.subFeePerUnitDenominator && !this.subFeePerUnitDenominator.closed) {\n      this.subFeePerUnitDenominator.unsubscribe();\n    }\n    if (this.subSqueezeSpeedNumberator && !this.subSqueezeSpeedNumberator.closed) {\n      this.subSqueezeSpeedNumberator.unsubscribe();\n    }\n    if (this.subSqueezeSpeedDenominator && !this.subSqueezeSpeedDenominator.closed) {\n      this.subSqueezeSpeedDenominator.unsubscribe();\n    }\n    if (this.subFeePerInsideSMS && !this.subFeePerInsideSMS.closed) {\n      this.subFeePerInsideSMS.unsubscribe();\n    }\n    if (this.subFeePerOutsideSMS && !this.subFeePerOutsideSMS.closed) {\n      this.subFeePerOutsideSMS.unsubscribe();\n    }\n    if (this.subMaxFee && !this.subMaxFee.closed) {\n      this.subMaxFee.unsubscribe();\n    }\n    if (this.subPlaneName && !this.subPlaneName.closed) {\n      this.subPlaneName.unsubscribe();\n    }\n    if (this.subDispatchCode && !this.subDispatchCode.closed) {\n      this.subDispatchCode.unsubscribe();\n    }\n    if (this.subCustomerType && !this.subCustomerType.closed) {\n      this.subCustomerType.unsubscribe();\n    }\n    if (this.subSubscriptionFee && !this.subSubscriptionFee.closed) {\n      this.subSubscriptionFee.unsubscribe();\n    }\n    if (this.subSubscriptionType && !this.subSubscriptionType.closed) {\n      this.subSubscriptionType.unsubscribe();\n    }\n    if (this.subPlanScope && !this.subPlanScope.closed) {\n      this.subPlanScope.unsubscribe();\n    }\n    if (this.subProvinceCode && !this.subProvinceCode.closed) {\n      this.subProvinceCode.unsubscribe();\n    }\n    if (this.subPlanCycle && !this.subPlanCycle.closed) {\n      this.subPlanCycle.unsubscribe();\n    }\n    if (this.subDuration && !this.subDuration.closed) {\n      this.subDuration.unsubscribe();\n    }\n    if (this.subDescription && !this.subDescription.closed) {\n      this.subDescription.unsubscribe();\n    }\n    if (this.subDescription && !this.subDescription.closed) {\n      this.subDescription.unsubscribe();\n    }\n    if (this.subFreeData && !this.subFreeData.closed) {\n      this.subFreeData.unsubscribe();\n    }\n    if (this.subLimitInsideSMSFree && !this.subLimitInsideSMSFree.closed) {\n      this.subLimitInsideSMSFree.unsubscribe();\n    }\n    if (this.subLimitOutsideSMSFree && !this.subLimitOutsideSMSFree.closed) {\n      this.subLimitOutsideSMSFree.unsubscribe();\n    }\n    if (this.subFeePerUnitNumberator && !this.subFeePerUnitNumberator.closed) {\n      this.subFeePerUnitNumberator.unsubscribe();\n    }\n    if (this.subFeePerUnitNumberator && !this.subFeePerUnitDenominator.closed) {\n      this.subFeePerUnitDenominator.unsubscribe();\n    }\n    if (this.subSqueezeSpeedNumberator && !this.subSqueezeSpeedNumberator.closed) {\n      this.subSqueezeSpeedNumberator.unsubscribe();\n    }\n    if (this.subSqueezeSpeedDenominator && !this.subSqueezeSpeedDenominator.closed) {\n      this.subSqueezeSpeedDenominator.unsubscribe();\n    }\n    if (this.subFeePerInsideSMS && !this.subFeePerInsideSMS.closed) {\n      this.subFeePerInsideSMS.unsubscribe();\n    }\n    if (this.subFeePerOutsideSMS && !this.subFeePerOutsideSMS.closed) {\n      this.subFeePerOutsideSMS.unsubscribe();\n    }\n    if (this.subMaxFee && !this.subMaxFee.closed) {\n      this.subMaxFee.unsubscribe();\n    }\n  }\n  onSubmitSearchUser() {\n    this.pageNumberAssign = 0;\n    this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\n  }\n  searchUser(page, limit, sort, params) {\n    let me = this;\n    this.pageNumberAssign = page;\n    this.pageSizeAssign = limit;\n    if (sort == null || sort == undefined) {\n      this.sortAssign = \"id,desc\";\n      sort = \"id,desc\";\n    } else {\n      this.sortAssign = sort;\n    }\n    let dataParams = {\n      page,\n      size: limit,\n      sort,\n      provinceCode: this.listProvince.map(e => e.code)\n    };\n    Object.keys(this.searchInfoUser).forEach(key => {\n      if (this.searchInfoUser[key] != null) {\n        dataParams[key] = this.searchInfoUser[key];\n      }\n    });\n    if (this.searchInfoUser.provinceCode == null) {\n      dataParams.provinceCode = this.listProvince.map(e => e.code);\n    }\n    this.selectItemsUserOld = [...this.selectItemsUser];\n    this.ratingPlanService.getUserToAddAccount(dataParams, response => {\n      me.dataSetAssignPlan = {\n        content: response.content,\n        total: response.totalElements\n      };\n      this.selectItemsUser = [...this.selectItemsUserOld];\n    });\n  }\n  openDialogAddCustomerAccount() {\n    let provincesSelected = this.updatePlanForm.get('provinceCode').value;\n    this.listProvince = this.provinces.filter(prov => provincesSelected.includes(prov.code));\n    if (this.pageNumberAssign == null) {\n      this.pageNumberAssign = 0;\n    }\n    if (this.pageSizeAssign == null) {\n      this.pageSizeAssign = 10;\n    }\n    if (this.sortAssign == null) {\n      this.sortAssign = \"id,desc\";\n    }\n    this.searchInfoUser.provinceCode = this.listProvince.map(e => e.code);\n    this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\n    this.isShowDialogAddCustomerAccount = true;\n  }\n  changeProvince() {\n    let provinceSelected = this.updatePlanForm.get(\"provinceCode\").value;\n    let ratingScope = this.updatePlanForm.get(\"ratingScope\").value;\n    if (ratingScope == 1) {\n      if (provinceSelected.length > 0) {\n        this.isCustomer = true;\n      } else {\n        this.isCustomer = false;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function UpdatePlanComponent_Factory(t) {\n      return new (t || UpdatePlanComponent)(i0.ɵɵdirectiveInject(RatingPlanService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdatePlanComponent,\n      selectors: [[\"app-update-plan\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 26,\n      vars: 34,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [\"class\", \"p-4\", \"styleClass\", \"responsive-form-plans\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"grid\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"username\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"username\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"fullName\"], [\"class\", \"p-float-label\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"selectItemsChange\"], [\"styleClass\", \"responsive-form-plans\", 1, \"p-4\"], [\"action\", \"\", 3, \"formGroup\", \"submit\"], [1, \"pt-0\", \"shadow-2\", \"border-round-md\", \"m-1\", \"flex\", \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"col-6\", \"grid-col\"], [1, \"field\", \"grid\", \"px-4\", \"pt-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"code\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [1, \"text-red-500\"], [1, \"col-12\", \"md:col-10\", \"flex-1\"], [\"formControlName\", \"code\", \"pInputText\", \"\", \"id\", \"code\", \"type\", \"text\", 3, \"readonly\", \"placeholder\"], [1, \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"mb-3\", \"responsive-size-input\"], [2, \"min-width\", \"140px\"], [1, \"col-11\", \"md:col-11\", \"py-0\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"name\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"name\", \"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", 3, \"placeholder\"], [\"htmlFor\", \"dispatchCode\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"dispatchCode\", \"pInputText\", \"\", \"id\", \"dispatchCode\", \"type\", \"text\", 3, \"readonly\", \"placeholder\"], [\"htmlFor\", \"customerType\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"customerType\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"placeholder\"], [1, \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"description\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"description\", \"id\", \"description\", \"rows\", \"4\", \"cols\", \"30\", \"pInputText\", \"\", 3, \"placeholder\"], [\"htmlFor\", \"subscriptionFee\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"subscriptionFee\", \"pInputText\", \"\", \"id\", \"subscriptionFee\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", \"pr-1\", 2, \"min-width\", \"90px\"], [1, \"field\", \"grid\", \"px-4\", \"pb-2\", \"custom-responsive-field\"], [1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", \"flex\", \"flex-row\", \"align-items-center\", \"radio-group\", \"wrapper\"], [\"class\", \"field-checkbox my-auto\", \"style\", \"min-width: 140px; min-height:35px;\", 4, \"ngFor\", \"ngForOf\"], [1, \"switch-group-wrapper\", \"flex\", \"flex-row\", \"align-items-center\", \"mt-2\"], [\"htmlFor\", \"reload\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", \"responsive-switch-label\", 2, \"min-width\", \"130px\"], [\"formControlName\", \"reload\", 1, \"flex\", \"align-items-center\"], [\"htmlFor\", \"cycle\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"cycleTimeUnit\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"autoDisplayFirst\", \"true\", 3, \"options\", \"placeholder\"], [\"htmlFor\", \"cycleInterval\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"cycleInterval\", \"pInputText\", \"\", \"id\", \"cycleInterval\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [\"class\", \"my-auto pr-1\", \"style\", \"min-width: 90px;\", 4, \"ngIf\"], [\"htmlFor\", \"ratingScope\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"ratingScope\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"placeholder\", \"onChange\"], [\"class\", \"field grid px-4 flex flex-row flex-nowrap\", 4, \"ngIf\"], [1, \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"mb-3\"], [\"class\", \"field grid px-4 flex flex-row flex-nowrap3 responsive-size-input\", 4, \"ngIf\"], [1, \"ml-2\"], [1, \"flex-1\"], [\"htmlFor\", \"limitDataUsage\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"170px\"], [\"formControlName\", \"limitDataUsage\", \"pInputText\", \"\", \"id\", \"limitDataUsage\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", \"pr-1\", 2, \"min-width\", \"40px\"], [2, \"min-width\", \"170px\"], [\"formControlName\", \"dataMax\", \"pInputText\", \"\", \"id\", \"dataMax\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [\"htmlFor\", \"insideSMSFree\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"170px\"], [\"formControlName\", \"limitSmsInside\", \"pInputText\", \"\", \"id\", \"insideSMSFree\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [\"htmlFor\", \"outsideSMSFree\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"170px\"], [\"formControlName\", \"limitSmsOutside\", \"pInputText\", \"\", \"id\", \"outsideSMSFree\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"ml-2\", \"mt-4\", \"mb-3\"], [1, \"m-0\"], [\"formControlName\", \"flexible\", 1, \"\", 3, \"ngModel\", \"ngModelChange\", \"onChange\"], [1, \"pt-0\", \"shadow-2\", \"border-round-md\", \"mb-4\", \"m-1\", \"flex\", \"p-fluid\", \"p-formgrid\", \"flex-column\", \"grid\"], [\"htmlFor\", \"feePerDataUnit\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"feePerDataUnit\", \"pInputText\", \"\", \"id\", \"feePerDataUnit\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", \"mx-auto\", 2, \"min-width\", \"10px\"], [\"formControlName\", \"dataRoundUnit\", \"pInputText\", \"\", \"id\", \"dataRoundUnit\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", 2, \"min-width\", \"40px\"], [1, \"col-12\", \"md:col-2\", \"md:mb-0\", \"p-0\", \"responsive-div-error-2\", 2, \"min-width\", \"190px\"], [1, \"col-11\", \"md:col-5\", \"py-0\"], [1, \"col-11\", \"md:col-5\", \"py-0\", \"responsive-error-2\"], [\"htmlFor\", \"downSpeed\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"downSpeed\", \"pInputText\", \"\", \"id\", \"downSpeed\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [\"formControlName\", \"squeezedSpeed\", \"pInputText\", \"\", \"id\", \"squeezedSpeed\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-div\"], [\"htmlFor\", \"feeSmsInside\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"feeSmsInside\", \"pInputText\", \"\", \"id\", \"feeSmsInside\", \"type\", \"number\", 1, \"input-edit\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"col-12\", \"md:col-2\", \"md:mb-0\", \"p-0\", \"responsive-div-error\", 2, \"min-width\", \"190px\"], [1, \"col-11\", \"md:col-11\", \"py-0\", \"responsive-error-1\"], [\"htmlFor\", \"feeSmsOutside\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"feeSmsOutside\", \"pInputText\", \"\", \"id\", \"feeSmsOutside\", \"type\", \"number\", 1, \"input-edit\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"field\", \"grid\", \"px-4\", \"pb-3\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-div\"], [\"htmlFor\", \"maximumFee\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"maximumFee\", \"pInputText\", \"\", \"id\", \"maximumFee\", \"type\", \"number\", 1, \"input-edit\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"p-2\"], [\"routerLink\", \"/plans\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-secondary\", \"p-button-outlined\", 3, \"label\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-info\", 3, \"label\", \"disabled\"], [1, \"field-checkbox\", \"my-auto\", 2, \"min-width\", \"140px\", \"min-height\", \"35px\"], [\"formControlName\", \"paidType\", \"name\", \"paidType\", 3, \"inputId\", \"value\", \"ngModel\", \"ngModelChange\"], [1, \"ml-2\", 3, \"for\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\"], [\"htmlFor\", \"provinceCode\", 1, \"col-fixed\", 2, \"min-width\", \"140px\"], [\"class\", \"col\", \"style\", \"max-width: calc(100% - 230px);\", 4, \"ngIf\"], [\"class\", \"col\", \"tooltipPosition\", \"top\", \"style\", \"max-width: calc(100% - 140px);white-space: nowrap;overflow: hidden;text-overflow: ellipsis;\", 3, \"pTooltip\", 4, \"ngIf\"], [\"class\", \"col-fixed\", \"style\", \"min-width: 90px;\", 4, \"ngIf\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 230px)\"], [\"formControlName\", \"provinceCode\", \"display\", \"chip\", \"optionLabel\", \"name\", \"optionValue\", \"code\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"showToggleAll\", \"placeholder\", \"onChange\"], [\"tooltipPosition\", \"top\", 1, \"col\", 2, \"max-width\", \"calc(100% - 140px)\", \"white-space\", \"nowrap\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\", 3, \"pTooltip\"], [1, \"col-fixed\", 2, \"min-width\", \"90px\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap3\", \"responsive-size-input\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"min-width\", \"140px\", \"cursor\", \"pointer\", \"text-decoration\", \"underline\", \"color\", \"blue\", \"transition\", \"color 0.3s\", 3, \"click\"], [\"styleClass\", \"w-full\", \"id\", \"provinceCode\", \"optionLabel\", \"name\", \"optionValue\", \"code\", \"formControlName\", \"provinceCode\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"provinceCode\"]],\n      template: function UpdatePlanComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, UpdatePlanComponent_p_card_5_Template, 282, 142, \"p-card\", 4);\n          i0.ɵɵelementStart(6, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function UpdatePlanComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.onSubmitSearchUser();\n          });\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-dialog\", 7);\n          i0.ɵɵlistener(\"visibleChange\", function UpdatePlanComponent_Template_p_dialog_visibleChange_8_listener($event) {\n            return ctx.isShowDialogAddCustomerAccount = $event;\n          });\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"span\", 10)(12, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function UpdatePlanComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.searchInfoUser.username = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"label\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"span\", 10)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function UpdatePlanComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.searchInfoUser.fullName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"label\", 14);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 9);\n          i0.ɵɵtemplate(21, UpdatePlanComponent_span_21_Template, 4, 5, \"span\", 15);\n          i0.ɵɵtemplate(22, UpdatePlanComponent_span_22_Template, 2, 2, \"span\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 17);\n          i0.ɵɵelement(24, \"p-button\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"table-vnpt\", 19);\n          i0.ɵɵlistener(\"selectItemsChange\", function UpdatePlanComponent_Template_table_vnpt_selectItemsChange_25_listener($event) {\n            return ctx.selectItemsUser = $event;\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listplan\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.updatePlanForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(32, _c1));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"account.label.addCustomerAccount\"))(\"visible\", ctx.isShowDialogAddCustomerAccount)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfoUser.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.username\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfoUser.fullName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.fullName\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.userType == ctx.allUserType.ADMIN ? \"\" : \"flex flex-row justify-content-start align-items-center\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType == ctx.allUserType.ADMIN);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType != ctx.allUserType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx.pageNumberAssign)(\"pageSize\", ctx.pageSizeAssign)(\"selectItems\", ctx.selectItemsUser)(\"columns\", ctx.columnsInfoUser)(\"dataSet\", ctx.dataSetAssignPlan)(\"options\", ctx.optionTableAddCustomerAccount)(\"loadData\", ctx.searchUser.bind(ctx))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(33, _c2))(\"scrollHeight\", \"400px\")(\"sort\", ctx.sort)(\"params\", ctx.searchInfoUser);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.RouterLink, i4.Breadcrumb, i5.Tooltip, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.InputText, i7.ButtonDirective, i7.Button, i8.TableVnptComponent, i9.Dropdown, i10.Card, i11.Dialog, i12.MultiSelect, i13.InputSwitch, i14.RadioButton],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "Observable", "debounceTime", "distinctUntilChanged", "map", "of", "switchMap", "take", "ComponentBase", "AccountService", "CONSTANTS", "RatingPlanService", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r3", "tranService", "translate", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ctx_r15", "ctx_r16", "ctx_r17", "ctx_r18", "ɵɵpureFunction0", "_c0", "ɵɵlistener", "UpdatePlanComponent_p_card_5_div_85_Template_p_radioButton_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r59", "ctx_r58", "ɵɵnextContext", "ɵɵresetView", "selectedPaid<PERSON>ate<PERSON><PERSON>", "ɵɵproperty", "paidType_r57", "key", "ctx_r19", "ɵɵtextInterpolate", "name", "ctx_r20", "ctx_r21", "ctx_r22", "ctx_r23", "ctx_r24", "ctx_r25", "ctx_r26", "ctx_r27", "UpdatePlanComponent_p_card_5_div_131_div_5_Template_p_multiSelect_onChange_1_listener", "_r64", "ctx_r63", "changeProvince", "ctx_r60", "provinces", "placeHolder", "provinceCode", "ctx_r61", "provinceInfo", "ɵɵelement", "ɵɵtemplate", "UpdatePlanComponent_p_card_5_div_131_div_5_Template", "UpdatePlanComponent_p_card_5_div_131_div_6_Template", "UpdatePlanComponent_p_card_5_div_131_div_7_Template", "ctx_r28", "userType", "allUserType", "ADMIN", "ctx_r29", "UpdatePlanComponent_p_card_5_div_136_Template_label_click_1_listener", "_r66", "ctx_r65", "openDialogAddCustomerAccount", "ctx_r30", "ctx_r31", "ctx_r32", "ctx_r33", "ctx_r34", "ctx_r35", "ctx_r36", "ctx_r37", "ctx_r38", "ctx_r39", "ctx_r40", "ctx_r41", "ctx_r42", "ctx_r43", "ctx_r44", "ctx_r45", "ctx_r46", "ctx_r47", "ctx_r48", "ctx_r49", "ctx_r50", "ctx_r51", "ctx_r52", "ctx_r53", "ctx_r54", "ctx_r55", "ctx_r56", "UpdatePlanComponent_p_card_5_Template_form_submit_1_listener", "_r68", "ctx_r67", "submitForm", "UpdatePlanComponent_p_card_5_div_14_Template", "UpdatePlanComponent_p_card_5_div_15_Template", "UpdatePlanComponent_p_card_5_div_16_Template", "UpdatePlanComponent_p_card_5_div_17_Template", "UpdatePlanComponent_p_card_5_div_28_Template", "UpdatePlanComponent_p_card_5_div_29_Template", "UpdatePlanComponent_p_card_5_div_30_Template", "UpdatePlanComponent_p_card_5_div_31_Template", "UpdatePlanComponent_p_card_5_div_42_Template", "UpdatePlanComponent_p_card_5_div_43_Template", "UpdatePlanComponent_p_card_5_div_44_Template", "UpdatePlanComponent_p_card_5_div_55_Template", "UpdatePlanComponent_p_card_5_div_64_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_72_listener", "ctx_r69", "blockMinus", "UpdatePlanComponent_p_card_5_Template_input_input_72_listener", "ctx_r70", "checkInputValue", "UpdatePlanComponent_p_card_5_div_80_Template", "UpdatePlanComponent_p_card_5_div_81_Template", "UpdatePlanComponent_p_card_5_div_82_Template", "UpdatePlanComponent_p_card_5_div_85_Template", "UpdatePlanComponent_p_card_5_div_102_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_109_listener", "ctx_r71", "UpdatePlanComponent_p_card_5_Template_input_input_109_listener", "ctx_r72", "UpdatePlanComponent_p_card_5_div_110_Template", "UpdatePlanComponent_p_card_5_div_111_Template", "UpdatePlanComponent_p_card_5_div_112_Template", "UpdatePlanComponent_p_card_5_div_116_Template", "UpdatePlanComponent_p_card_5_div_117_Template", "UpdatePlanComponent_p_card_5_div_118_Template", "UpdatePlanComponent_p_card_5_Template_p_dropdown_onChange_125_listener", "ctx_r73", "onDropdownChange", "UpdatePlanComponent_p_card_5_div_130_Template", "UpdatePlanComponent_p_card_5_div_131_Template", "UpdatePlanComponent_p_card_5_div_135_Template", "UpdatePlanComponent_p_card_5_div_136_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_147_listener", "ctx_r74", "UpdatePlanComponent_p_card_5_Template_input_input_147_listener", "ctx_r75", "UpdatePlanComponent_p_card_5_div_153_Template", "UpdatePlanComponent_p_card_5_div_154_Template", "UpdatePlanComponent_p_card_5_div_155_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_162_listener", "ctx_r76", "UpdatePlanComponent_p_card_5_Template_input_input_162_listener", "ctx_r77", "UpdatePlanComponent_p_card_5_div_168_Template", "UpdatePlanComponent_p_card_5_div_169_Template", "UpdatePlanComponent_p_card_5_div_170_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_176_listener", "ctx_r78", "UpdatePlanComponent_p_card_5_Template_input_input_176_listener", "ctx_r79", "UpdatePlanComponent_p_card_5_div_180_Template", "UpdatePlanComponent_p_card_5_div_181_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_186_listener", "ctx_r80", "UpdatePlanComponent_p_card_5_Template_input_input_186_listener", "ctx_r81", "UpdatePlanComponent_p_card_5_div_190_Template", "UpdatePlanComponent_p_card_5_div_191_Template", "UpdatePlanComponent_p_card_5_Template_p_inputSwitch_ngModelChange_195_listener", "ctx_r82", "isFlexible", "UpdatePlanComponent_p_card_5_Template_p_inputSwitch_onChange_195_listener", "ctx_r83", "onSwitchChange", "UpdatePlanComponent_p_card_5_Template_input_keydown_203_listener", "ctx_r84", "UpdatePlanComponent_p_card_5_Template_input_input_203_listener", "ctx_r85", "UpdatePlanComponent_p_card_5_Template_input_keydown_207_listener", "ctx_r86", "UpdatePlanComponent_p_card_5_Template_input_input_207_listener", "ctx_r87", "UpdatePlanComponent_p_card_5_div_213_Template", "UpdatePlanComponent_p_card_5_div_214_Template", "UpdatePlanComponent_p_card_5_div_215_Template", "UpdatePlanComponent_p_card_5_div_217_Template", "UpdatePlanComponent_p_card_5_div_218_Template", "UpdatePlanComponent_p_card_5_div_219_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_224_listener", "ctx_r88", "UpdatePlanComponent_p_card_5_Template_input_input_224_listener", "ctx_r89", "UpdatePlanComponent_p_card_5_Template_input_keydown_228_listener", "ctx_r90", "UpdatePlanComponent_p_card_5_Template_input_input_228_listener", "ctx_r91", "UpdatePlanComponent_p_card_5_div_234_Template", "UpdatePlanComponent_p_card_5_div_235_Template", "UpdatePlanComponent_p_card_5_div_237_Template", "UpdatePlanComponent_p_card_5_div_238_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_243_listener", "ctx_r92", "UpdatePlanComponent_p_card_5_Template_input_input_243_listener", "ctx_r93", "UpdatePlanComponent_p_card_5_div_250_Template", "UpdatePlanComponent_p_card_5_div_251_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_256_listener", "ctx_r94", "UpdatePlanComponent_p_card_5_Template_input_input_256_listener", "ctx_r95", "UpdatePlanComponent_p_card_5_div_263_Template", "UpdatePlanComponent_p_card_5_div_264_Template", "UpdatePlanComponent_p_card_5_Template_input_keydown_269_listener", "ctx_r96", "UpdatePlanComponent_p_card_5_Template_input_input_269_listener", "ctx_r97", "UpdatePlanComponent_p_card_5_div_276_Template", "UpdatePlanComponent_p_card_5_div_277_Template", "ctx_r0", "updatePlanForm", "isRead", "planCode", "isPlanCodeValid", "get", "<PERSON><PERSON><PERSON><PERSON>", "planName", "isPlaneNameValid", "dispatchCode", "isDispatchCodeValid", "customerTypes", "customerType", "isCustomerTypeValid", "description", "isDescriptionValid", "subscriptionFee", "isSubscriptionFeeValid", "paidCategories", "cycles", "planCycle", "isPlanCycleValid", "duration", "value", "isDurationValid", "ratingScopes", "planScope", "isPlanScopeValid", "isProvince", "isProvinceCodeValid", "isCustomer", "freeData", "isFreeDataValid", "dataMax", "isDataMaxValid", "insideSMSFree", "isLimitInsideSMSFreeValid", "outsideSMSFree", "isLimitOutsideSMSFreeValid", "ɵɵstyleProp", "feePerUnit", "isFeePerUnitNumberatorValid", "isFeePerUnitDenominatorValid", "squeezedSpeed", "isSqueezeSpeedNumberatorValid", "isSqueezeSpeedDenominatorValid", "feePerInsideSMS", "isFeePerInsideSMSValid", "feePerOutsideSMS", "isFeePerOutsideSMSValid", "maxFee", "isMaxFeeValid", "isPlanCodeExisted", "isPlanNameExisted", "invalid", "UpdatePlanComponent_span_21_Template_p_multiSelect_ngModelChange_1_listener", "_r99", "ctx_r98", "searchInfoUser", "ctx_r1", "listProvince", "ɵɵtextInterpolate2", "ctx_r2", "UpdatePlanComponent", "constructor", "ratingPlanService", "accountService", "formBuilder", "injector", "home", "icon", "routerLink", "items", "label", "id", "sessionService", "userInfo", "type", "USER_TYPE", "isSubscriptionTypeValid", "controlComboSelect", "isShowDialogAddCustomerAccount", "selectItemsUser", "selectItemsUserOld", "isReload", "placeHolderDescription", "subscriptionType", "checkCodeExisted", "query", "observer", "checkingPlanCodeExisted", "response", "next", "complete", "checkNameExisted", "checkingPlanNameExisted", "planCodeValidator", "control", "valueChanges", "pristine", "pipe", "currentPlanCodeInput", "code", "result", "initialData", "planNameValidator", "currentPlanNameInput", "customCodeCharacterValidator", "<PERSON><PERSON><PERSON><PERSON>", "test", "customNameCharacterValidator", "event", "invalid<PERSON>hars", "includes", "preventDefault", "input", "target", "replace", "disable", "emitEvent", "enable", "messageCommonService", "onload", "me", "valid", "data", "reload", "flexible", "uploadSpeed", "downSpeed", "length", "provinceSelected", "currentSelected", "filter", "el", "e", "userIds", "editRatingPlan", "idForEdit", "success", "router", "navigate", "offload", "onChangeCustomers", "setValue", "customerCode", "ngOnInit", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "RATING_PLAN", "UPDATE", "window", "location", "hash", "Number", "route", "snapshot", "params", "optionTableAddCustomerAccount", "hasClearSelected", "hasShowIndex", "hasShowChoose", "hasShowToggleColumn", "dataSetAssignPlan", "content", "total", "username", "fullName", "email", "formSearchUser", "group", "columnsInfoUser", "size", "align", "isShow", "isSort", "getListProvince", "getById", "initFormGroup", "patchValue", "setupValidate", "paidType", "ratingScope", "getUserAssignedOnRatingPlan", "ratingPlanId", "resp", "status", "updateValueAndValidity", "for<PERSON>ach", "substring", "createdDate", "created<PERSON>y", "required", "max<PERSON><PERSON><PERSON>", "max", "min", "disabled", "cycleTimeUnit", "cycleInterval", "limitDataUsage", "limitSmsInside", "limitSmsOutside", "feePerDataUnit", "dataRoundUnit", "feeSmsInside", "feeSmsOutside", "maximumFee", "subPlanCode", "statusChanges", "subscribe", "errors", "subPlaneName", "subDispatchCode", "subCustomerType", "subSubscriptionFee", "subSubscriptionType", "subPlanScope", "subProvinceCode", "subPlanCycle", "subDuration", "subDescription", "subFreeData", "subDataMax", "subLimitInsideSMSFree", "subLimitOutsideSMSFree", "subFeePerUnitNumberator", "subFeePerUnitDenominator", "subSqueezeSpeedNumberator", "subSqueezeSpeedDenominator", "subFeePerInsideSMS", "subFeePerOutsideSMS", "subMaxFee", "ngOnDestroy", "closed", "unsubscribe", "onSubmitSearchUser", "pageNumberAssign", "searchUser", "pageSizeAssign", "sortAssign", "page", "limit", "sort", "undefined", "dataParams", "Object", "keys", "getUserToAddAccount", "totalElements", "provincesSelected", "prov", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "UpdatePlanComponent_Template", "rf", "ctx", "UpdatePlanComponent_p_card_5_Template", "UpdatePlanComponent_Template_form_ngSubmit_6_listener", "UpdatePlanComponent_Template_p_dialog_visibleChange_8_listener", "UpdatePlanComponent_Template_input_ngModelChange_12_listener", "UpdatePlanComponent_Template_input_ngModelChange_17_listener", "UpdatePlanComponent_span_21_Template", "UpdatePlanComponent_span_22_Template", "UpdatePlanComponent_Template_table_vnpt_selectItemsChange_25_listener", "ɵɵstyleMap", "_c1", "ɵɵclassMap", "bind", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-plan\\update-plan\\update-plan.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-plan\\update-plan\\update-plan.component.html"], "sourcesContent": ["import {On<PERSON><PERSON>roy, Injector} from '@angular/core';\r\nimport {Component, Inject, inject} from '@angular/core';\r\nimport {\r\n    AbstractControl,\r\n    AsyncValidatorFn, FormBuilder,\r\n    FormControl,\r\n    FormGroup,\r\n    ValidationErrors,\r\n    ValidatorFn,\r\n    Validators\r\n} from '@angular/forms';\r\nimport {MenuItem} from 'primeng/api';\r\nimport {Observable, Subscription, debounceTime, distinctUntilChanged, map, of, switchMap, take} from 'rxjs';\r\nimport {ComponentBase} from 'src/app/component.base';\r\nimport {AccountService} from 'src/app/service/account/AccountService';\r\nimport {CONSTANTS} from 'src/app/service/comon/constants';\r\nimport {RatingPlanService} from 'src/app/service/rating-plan/RatingPlanService';\r\nimport {ComboLazyControl} from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\n\r\ninterface DropDownValue {\r\n    id: number | string;\r\n    name: string;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-update-plan',\r\n    templateUrl: './update-plan.component.html',\r\n})\r\nexport class UpdatePlanComponent extends ComponentBase implements OnDestroy {\r\n    idForEdit: number;\r\n    home: MenuItem = {icon: 'pi pi-home', routerLink: '/'};\r\n    items: MenuItem[] = [\r\n        {label: this.tranService.translate(\"global.menu.ratingplanmgmt\"), routerLink: '../../'},\r\n        {label: this.tranService.translate(\"global.menu.listplan\"), routerLink: '../../'},\r\n        {label: this.tranService.translate(\"global.button.edit\")},\r\n    ];\r\n    isPlanCodeExisted: boolean = false;\r\n    isPlanNameExisted: boolean = false;\r\n    currentPlanCodeInput: string;\r\n    currentPlanNameInput: string;\r\n    initialData: any;\r\n    updatePlanForm: FormGroup\r\n\r\n    customerTypes: DropDownValue[] | undefined = [\r\n        {\r\n            id: 1,\r\n            name: this.tranService.translate(\"ratingPlan.customerType.personal\"),\r\n        },\r\n        {\r\n            id: 2,\r\n            name: this.tranService.translate(\"ratingPlan.customerType.enterprise\"),\r\n        },\r\n        {\r\n            id: 0,\r\n            name: this.tranService.translate(\"ratingPlan.customerType.agency\"),\r\n        },\r\n        // ,\r\n        // {\r\n        //   id: 0,\r\n        //   name: this.tranService.translate(\"ratingPlan.customerType.agency\"),\r\n        // },\r\n    ];\r\n    ratingScopes: DropDownValue[] | undefined = [\r\n        {\r\n            id: 0,\r\n            name: this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\"),\r\n        },\r\n        {\r\n            id: 2,\r\n            name: this.tranService.translate(\"ratingPlan.ratingScope.province\"),\r\n        },\r\n        {\r\n            id: 1,\r\n            name: this.tranService.translate(\"ratingPlan.ratingScope.customer\"),\r\n        },\r\n    ];\r\n    userType = this.sessionService.userInfo.type;\r\n    allUserType = CONSTANTS.USER_TYPE;\r\n    provinceInfo: string = \"\";\r\n\r\n    provinces: any[] | undefined;\r\n    isPlanCodeValid: boolean = false;\r\n    isPlaneNameValid: boolean = false;\r\n    isDispatchCodeValid: boolean = false;\r\n    isCustomerTypeValid: boolean = false;\r\n    isSubscriptionFeeValid: boolean = false;\r\n    isSubscriptionTypeValid: boolean = false; // Đã set default\r\n    isPlanScopeValid: boolean = false;\r\n    isProvinceCodeValid: boolean = false;\r\n    isPlanCycleValid: boolean = false;\r\n    isDurationValid: boolean = false;\r\n    isDescriptionValid: boolean = false;\r\n    isFreeDataValid: boolean = false;\r\n    isLimitInsideSMSFreeValid: boolean = false;\r\n    isLimitOutsideSMSFreeValid: boolean = false;\r\n    isFeePerUnitNumberatorValid: boolean = false;\r\n    isFeePerUnitDenominatorValid: boolean = false;\r\n    isSqueezeSpeedNumberatorValid: boolean = false;\r\n    isSqueezeSpeedDenominatorValid: boolean = false;\r\n    isFeePerInsideSMSValid: boolean = false;\r\n    isFeePerOutsideSMSValid: boolean = false;\r\n    isMaxFeeValid: boolean = false;\r\n    isDataMaxValid: boolean = false;\r\n\r\n    subPlanCode: Subscription;\r\n    subPlaneName: Subscription;\r\n    subDispatchCode: Subscription;\r\n    subCustomerType: Subscription;\r\n    subSubscriptionFee: Subscription;\r\n    subSubscriptionType: Subscription;\r\n    subPlanScope: Subscription;\r\n    subProvinceCode: Subscription;\r\n    subPlanCycle: Subscription;\r\n    subDuration: Subscription;\r\n    subDescription: Subscription;\r\n    subFreeData: Subscription;\r\n    subLimitInsideSMSFree: Subscription;\r\n    subLimitOutsideSMSFree: Subscription;\r\n    subFeePerUnitNumberator: Subscription;\r\n    subFeePerUnitDenominator: Subscription;\r\n    subSqueezeSpeedNumberator: Subscription;\r\n    subSqueezeSpeedDenominator: Subscription;\r\n    subFeePerInsideSMS: Subscription;\r\n    subFeePerOutsideSMS: Subscription;\r\n    subMaxFee: Subscription;\r\n    subDataMax: Subscription;\r\n\r\n    controlComboSelect: ComboLazyControl = new ComboLazyControl();\r\n    customerCode: [];\r\n    isProvince = false\r\n    isCustomer = false\r\n    isRead: boolean = false\r\n\r\n    isShowDialogAddCustomerAccount: boolean = false;\r\n    searchInfoUser: {\r\n        username: string | null,\r\n        fullName: string | null,\r\n        email: string | null,\r\n        provinceCode: any | null,\r\n    }\r\n    listProvince: any[] | undefined;\r\n    selectItemsUser: Array<{id: number, provinceCode: string, [key:string]:any}> = [];\r\n    selectItemsUserOld: Array<{id: number, provinceCode: string, [key:string]:any}> = [{id: -1, provinceCode: \"\"}];\r\n    pageNumberAssign: number;\r\n    pageSizeAssign: number;\r\n    sortAssign: string;\r\n    formSearchUser: any;\r\n    columnsInfoUser: Array<ColumnInfo>;\r\n    dataSetAssignPlan: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTableAddCustomerAccount: OptionTable;\r\n    sort: string;\r\n    cycles: DropDownValue[] | undefined = [\r\n        {\r\n            id: 1,\r\n            name: this.tranService.translate(\"ratingPlan.cycle.day\")\r\n        },\r\n        {\r\n            id: 3,\r\n            name: this.tranService.translate(\"ratingPlan.cycle.month\")\r\n        },\r\n    ];\r\n    isFlexible: boolean = false;\r\n    isReload: boolean = false;\r\n\r\n    paidCategories: any[] = [\r\n        {name: 'Trả trước', key: '1'},\r\n        {name: 'Trả sau', key: '0'},\r\n    ];\r\n    selectedPaidCategory: any = null;\r\n\r\n    placeHolderDescription: string = this.tranService.translate(\"groupSim.placeHolder.description\")\r\n\r\n    constructor(@Inject(RatingPlanService) public ratingPlanService: RatingPlanService,\r\n                @Inject(AccountService) private accountService: AccountService,private formBuilder: FormBuilder, injector: Injector) {\r\n        super(injector)\r\n    }\r\n\r\n    placeHolder = {\r\n        planCode: this.tranService.translate(\"ratingPlan.placeHolder.planCode\"),\r\n        planName: this.tranService.translate(\"ratingPlan.placeHolder.planeName\"),\r\n        dispatchCode: this.tranService.translate(\"ratingPlan.placeHolder.dispatchCode\"),\r\n        customerType: this.tranService.translate(\"ratingPlan.placeHolder.customerType\"),\r\n        description: this.tranService.translate(\"ratingPlan.placeHolder.description\"),\r\n        subscriptionFee: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionFee\"),\r\n        subscriptionType: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionType\"),\r\n        planScope: this.tranService.translate(\"ratingPlan.placeHolder.planScope\"),\r\n        provinceCode: this.tranService.translate(\"ratingPlan.placeHolder.provinceCode\"),\r\n        planCycle: this.tranService.translate(\"ratingPlan.placeHolder.planCycle\"),\r\n        duration: this.tranService.translate(\"ratingPlan.placeHolder.duration\"),\r\n        freeData: this.tranService.translate(\"ratingPlan.placeHolder.freeData\"),\r\n        insideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.insideSMSFree\"),\r\n        outsideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.outsideSMSFree\"),\r\n        feePerUnit: this.tranService.translate(\"ratingPlan.placeHolder.feePerUnit\"),\r\n        squeezedSpeed: this.tranService.translate(\"ratingPlan.placeHolder.squeezeSpeed\"),\r\n        feePerInsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerInsideSMS\"),\r\n        feePerOutsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerOutsideSMS\"),\r\n        maxFee: this.tranService.translate(\"ratingPlan.placeHolder.maxFee\"),\r\n        dataMax: this.tranService.translate(\"ratingPlan.placeHolder.dataMax\"),\r\n    }\r\n\r\n    checkCodeExisted(query: {}): Observable<number> {\r\n        return new Observable(observer => {\r\n            this.ratingPlanService.checkingPlanCodeExisted(query, (response) => {\r\n                observer.next(response);\r\n                observer.complete();\r\n            });\r\n        });\r\n    }\r\n\r\n    checkNameExisted(query: {}): Observable<number> {\r\n        return new Observable(observer => {\r\n            this.ratingPlanService.checkingPlanNameExisted(query, (response) => {\r\n                observer.next(response);\r\n                observer.complete();\r\n            });\r\n        });\r\n    }\r\n\r\n    planCodeValidator(): AsyncValidatorFn {\r\n        return (control: AbstractControl): Observable<ValidationErrors | null> => {\r\n            if (!control.valueChanges || control.pristine) {\r\n                return of(null);\r\n            } else\r\n                return control.valueChanges.pipe(\r\n                    debounceTime(500),\r\n                    distinctUntilChanged(),\r\n                    switchMap(value => {\r\n                        this.currentPlanCodeInput = value;\r\n                        return this.checkCodeExisted({code: value})\r\n                    }), take(1),\r\n                    map(result => {\r\n                        if (result === 0 || this.currentPlanCodeInput == this.initialData.code) {\r\n                            this.isPlanCodeExisted = false;\r\n                            return null;\r\n                        } else {\r\n                            this.isPlanCodeExisted = true;\r\n                            return {'exited': true};\r\n                        }\r\n                    }),\r\n                );\r\n        };\r\n    }\r\n\r\n    planNameValidator(): AsyncValidatorFn {\r\n        return (control: AbstractControl): Observable<ValidationErrors | null> => {\r\n            if (!control.valueChanges || control.pristine) {\r\n                return of(null);\r\n            } else\r\n                return control.valueChanges.pipe(\r\n                    debounceTime(500),\r\n                    distinctUntilChanged(),\r\n                    switchMap(value => {\r\n                        this.currentPlanNameInput = value;\r\n                        return this.checkNameExisted({name: value})\r\n                    }),\r\n                    take(1),\r\n                    map(result => {\r\n                        if (result === 0 || this.currentPlanNameInput == this.initialData.name) {\r\n                            this.isPlanNameExisted = false\r\n                            return null;\r\n                        } else {\r\n                            this.isPlanNameExisted = true;\r\n                            return {'exited': true};\r\n                        }\r\n                    }),\r\n                );\r\n        };\r\n    }\r\n\r\n    customCodeCharacterValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const value = control.value;\r\n            const isValid = /^[a-zA-Z0-9\\-_]*$/.test(value);\r\n            return isValid ? null : {'invalidCharacters': {value}};\r\n        };\r\n    }\r\n\r\n    customNameCharacterValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const value = control.value;\r\n            if (value == '') {\r\n                return null;\r\n            }\r\n            const isValid = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơỲỴÝỳỵỷỹƯứừữựụợ́̉̃À-ỹ0-9 _-]+$/.test(value);\r\n            return isValid ? null : {'invalidCharacters': {value}};\r\n        };\r\n    }\r\n\r\n    blockMinus(event: KeyboardEvent) {\r\n        const invalidChars = ['-', '+', ',', '.', 'e', 'E', 'r', 'R']; // Danh sách các ký tự không cho phép\r\n        if (invalidChars.includes(event.key)) {\r\n            event.preventDefault();\r\n        }\r\n        // if (event.key === '-' || event.key ==='+' || event.key === ',' || event.key === '.') {\r\n        //   event.preventDefault();\r\n        // }\r\n    }\r\n\r\n    checkInputValue(event: InputEvent) {\r\n        const input = event.target as HTMLInputElement;\r\n        input.value = input.value.replace(/[^0-9]/g, ''); // Chỉ cho phép nhập số\r\n    }\r\n\r\n    onDropdownChange(event) {\r\n        if(event.value==0){\r\n            this.isProvince = false\r\n            this.isCustomer = false\r\n            this.updatePlanForm.get('provinceCode').disable({ emitEvent: false });\r\n            // this.createPlanForm.get('userIds').disable({ emitEvent: false });\r\n        }else if (event.value == 1){\r\n            // gói cước loại khách hàng\r\n            this.isProvince = true\r\n            this.isCustomer = false\r\n            this.updatePlanForm.get('provinceCode').enable({ emitEvent: false });\r\n            this.changeProvince()\r\n            // this.createPlanForm.get('userIds').enable({ emitEvent: false });\r\n        }else if (event.value == 2){\r\n            // gói cước loại tỉnh/thành phố\r\n            this.isProvince = true\r\n            this.isCustomer = false\r\n            this.updatePlanForm.get('provinceCode').enable({ emitEvent: false });\r\n            this.changeProvince()\r\n            // this.createPlanForm.get('userIds').disable({ emitEvent: false });\r\n        }\r\n    }\r\n\r\n    onSwitchChange() {\r\n        if (this.isFlexible) {\r\n            this.updatePlanForm.get('feePerDataUnit').enable({emitEvent: false});\r\n            this.updatePlanForm.get('dataRoundUnit').enable({emitEvent: false});\r\n            this.updatePlanForm.get('downSpeed').enable({emitEvent: false});\r\n            this.updatePlanForm.get('squeezedSpeed').enable({emitEvent: false});\r\n            this.updatePlanForm.get('feeSmsInside').enable({emitEvent: false});\r\n            this.updatePlanForm.get('feeSmsOutside').enable({emitEvent: false});\r\n            this.updatePlanForm.get('maximumFee').enable({emitEvent: false});\r\n        } else {\r\n            this.updatePlanForm.get('feePerDataUnit').disable({emitEvent: false});\r\n            this.updatePlanForm.get('dataRoundUnit').disable({emitEvent: false});\r\n            this.updatePlanForm.get('downSpeed').disable({emitEvent: false});\r\n            this.updatePlanForm.get('squeezedSpeed').disable({emitEvent: false});\r\n            this.updatePlanForm.get('feeSmsInside').disable({emitEvent: false});\r\n            this.updatePlanForm.get('feeSmsOutside').disable({emitEvent: false});\r\n            this.updatePlanForm.get('maximumFee').disable({emitEvent: false});\r\n        }\r\n    }\r\n\r\n    submitForm() {\r\n        this.messageCommonService.onload();\r\n        let me = this;\r\n        if (this.updatePlanForm.valid) {\r\n            let data = {...this.updatePlanForm.value};\r\n            if (data.reload) {\r\n                data.reload = 1\r\n            } else {\r\n                data.reload = 0;\r\n            }\r\n            if (data.flexible) {\r\n                data.flexible = 1\r\n                data.uploadSpeed = data.downSpeed - data.squeezedSpeed\r\n            } else {\r\n                data.flexible = 0;\r\n            }\r\n            if (this.selectItemsUser.length > 0){\r\n                let provinceSelected = me.updatePlanForm.get(\"provinceCode\").value;\r\n                let currentSelected = me.selectItemsUser.filter((el) => provinceSelected.includes(el.provinceCode)).map(e => e.id);\r\n                data.userIds = currentSelected;\r\n            }\r\n            this.ratingPlanService.editRatingPlan(this.idForEdit, data, (response) => {\r\n                // this.messageCommonService.success(\"Update okr\")\r\n                this.messageCommonService.success(this.tranService.translate('global.message.saveSuccess'))\r\n                this.router.navigate(['/plans'])\r\n            }, null, () => {\r\n                me.messageCommonService.offload();\r\n            })\r\n        }\r\n    }\r\n\r\n    onChangeCustomers() {\r\n        this.updatePlanForm.get(\"userIds\").setValue(this.customerCode)\r\n    }\r\n\r\n    ngOnInit() {\r\n        let me = this;\r\n        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])) {\r\n            window.location.hash = \"/access\";\r\n        }\r\n        this.idForEdit = Number(this.route.snapshot.params[\"id\"]);\r\n\r\n        // this.provinces = [\r\n        //   {\r\n        //     id:\"1\",\r\n        //     name:\"Hà Nội\"\r\n        //   },\r\n        //   {\r\n        //     id:\"2\",\r\n        //     name:\"Hồ Chí Minh\"\r\n        //   },\r\n        //   {\r\n        //     id:\"3\",\r\n        //     name:\"Nghệ Tĩnh\"\r\n        //   },\r\n        // ]\r\n        this.optionTableAddCustomerAccount = {\r\n            hasClearSelected: false,\r\n            hasShowIndex: true,\r\n            hasShowChoose: true,\r\n            hasShowToggleColumn: false,\r\n        };\r\n        this.dataSetAssignPlan = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.searchInfoUser = {\r\n            username: null,\r\n            fullName: null,\r\n            email: null,\r\n            provinceCode: null\r\n        }\r\n        this.formSearchUser = this.formBuilder.group(this.searchInfoUser);\r\n        this.columnsInfoUser = [\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.username\"),\r\n                key: \"username\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.fullName\"),\r\n                key: \"fullName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.email\"),\r\n                key: \"email\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.province\"),\r\n                key: \"provinceName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.accountService.getListProvince((data) => {\r\n            this.provinces = data.map(el => {\r\n                return {\r\n                    code: el.code,\r\n                    name: `${el.name} (${el.code})`\r\n                }\r\n            })\r\n        })\r\n\r\n        // this.selectedPaidCategory = this.paidCategories[0].key\r\n        // console.log(this.selectedPaidCategory)\r\n\r\n        this.ratingPlanService.getById(this.idForEdit, (response) => {\r\n            me.initialData = response;\r\n            this.updatePlanForm = this.initFormGroup();\r\n            this.updatePlanForm.patchValue(response)\r\n            me.customerCode = response.userIds\r\n            this.setupValidate();\r\n\r\n            this.selectedPaidCategory = response.paidType\r\n\r\n            if (response.flexible == 1) {\r\n                this.isFlexible = true\r\n                this.onSwitchChange()\r\n            }\r\n            if (response.ratingScope == 1) {\r\n                this.isProvince = true;\r\n                this.isCustomer = true;\r\n                this.updatePlanForm.get(\"provinceCode\").enable({emitEvent: false});\r\n                // this.updatePlanForm.get(\"userIds\").enable({emitEvent: false})\r\n                this.updatePlanForm.get('userIds').setValue(response.userIds)\r\n                this.searchInfoUser.provinceCode = response.provinceCode\r\n\r\n                me.accountService.getUserAssignedOnRatingPlan({ratingPlanId: response.id}, (resp) => {\r\n                    this.selectItemsUser = resp;\r\n                    this.selectItemsUserOld = this.selectItemsUser\r\n                })\r\n\r\n\r\n            } else if (response.ratingScope == 2) {\r\n                this.isProvince = true;\r\n                this.isCustomer = false;\r\n                this.updatePlanForm.get(\"provinceCode\").enable({emitEvent: false});\r\n                // this.updatePlanForm.get(\"userIds\").disable({emitEvent: false})\r\n            }\r\n            if (response.reload == 1) {\r\n                this.isReload = true\r\n            }\r\n            if (response.status == 1 || response.status == 5) {\r\n                me.isRead = true;\r\n                // this.updatePlanForm.controls['code'].disable()\r\n                // this.updatePlanForm.controls['dispatchCode'].disable()\r\n\r\n                // this.updatePlanForm.get('code').disable({ emitEvent: false });\r\n                // this.updatePlanForm.get('dispatchCode').disable({ emitEvent: false });\r\n            }\r\n            this.updatePlanForm.updateValueAndValidity();\r\n            me.provinces.forEach(el => {\r\n                if (me.initialData.provinceCode.includes(el.code)) {\r\n                    me.provinceInfo += `${el.name}, `;\r\n                }\r\n            })\r\n            if (me.provinceInfo.length > 0) {\r\n                me.provinceInfo = me.provinceInfo.substring(0, me.provinceInfo.length - 2);\r\n            }\r\n        })\r\n    }\r\n\r\n    initFormGroup(): FormGroup {\r\n        return new FormGroup({\r\n            status: new FormControl(this.initialData.status),\r\n            createdDate: new FormControl(this.initialData.createdDate),\r\n            createdBy: new FormControl(this.initialData.createdBy),\r\n            code: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()], [this.planCodeValidator()]),\r\n            name: new FormControl(\"\", [Validators.required, Validators.maxLength(255), this.customNameCharacterValidator()], this.planNameValidator()),\r\n            dispatchCode: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()]),\r\n            customerType: new FormControl(\"\", [Validators.required]),\r\n            subscriptionFee: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n            paidType: new FormControl(\"\", [Validators.required]),\r\n            ratingScope: new FormControl(\"\", [Validators.required]),\r\n            provinceCode: new FormControl({value: \"\", disabled: !this.isProvince}, [Validators.required]),\r\n            userIds: new FormControl({value: null}),\r\n            cycleTimeUnit: new FormControl(\"\", [Validators.required]),\r\n            cycleInterval: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n            reload: new FormControl(0),\r\n            description: new FormControl(\"\", [Validators.maxLength(255)]),//MS\r\n\r\n            limitDataUsage: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n            limitSmsInside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//insideSMSFree\r\n            limitSmsOutside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//outsideSMSFree\r\n            dataMax: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n\r\n            flexible: new FormControl(0),\r\n            feePerDataUnit: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n            dataRoundUnit: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n            downSpeed: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.max(9999999999), Validators.min(0)]),\r\n            squeezedSpeed: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.max(9999999999), Validators.min(0)]),\r\n            feeSmsInside: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.max(9999999999), Validators.min(0)]),\r\n            feeSmsOutside: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.max(9999999999), Validators.min(0)]),\r\n            maximumFee: new FormControl({\r\n                value: 0,\r\n                disabled: !this.isFlexible\r\n            }, [Validators.max(9999999999), Validators.min(0)]),\r\n            uploadSpeed: new FormControl({value: 0, disabled: !this.isFlexible}),\r\n        });\r\n    }\r\n\r\n    setupValidate() {\r\n        this.subPlanCode = this.updatePlanForm.get('code').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('code').errors;\r\n            if (errors) {\r\n                this.isPlanCodeValid = true;\r\n            } else {\r\n                this.isPlanCodeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subPlaneName = this.updatePlanForm.get('name').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('name').errors;\r\n            if (errors) {\r\n                this.isPlaneNameValid = true;\r\n            } else {\r\n                this.isPlaneNameValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDispatchCode = this.updatePlanForm.get('dispatchCode').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('dispatchCode').errors;\r\n            if (errors) {\r\n                this.isDispatchCodeValid = true;\r\n            } else {\r\n                this.isDispatchCodeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subCustomerType = this.updatePlanForm.get('customerType').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('customerType').errors;\r\n            if (errors) {\r\n                this.isCustomerTypeValid = true;\r\n            } else {\r\n                this.isCustomerTypeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSubscriptionFee = this.updatePlanForm.get('subscriptionFee').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('subscriptionFee').errors;\r\n            if (errors) {\r\n                this.isSubscriptionFeeValid = true;\r\n            } else {\r\n                this.isSubscriptionFeeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSubscriptionType = this.updatePlanForm.get('paidType').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('paidType').errors;\r\n            if (errors) {\r\n                this.isSubscriptionTypeValid = true;\r\n            } else {\r\n                this.isSubscriptionTypeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subPlanScope = this.updatePlanForm.get('ratingScope').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('ratingScope').errors;\r\n            if (errors) {\r\n                this.isPlanScopeValid = true;\r\n            } else {\r\n                this.isPlanScopeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subProvinceCode = this.updatePlanForm.get('provinceCode').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('provinceCode').errors;\r\n            if (errors) {\r\n                this.isProvinceCodeValid = true;\r\n            } else {\r\n                this.isProvinceCodeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subPlanCycle = this.updatePlanForm.get('cycleTimeUnit').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('cycleTimeUnit').errors;\r\n            if (errors) {\r\n                this.isPlanCycleValid = true;\r\n            } else {\r\n                this.isPlanCycleValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDuration = this.updatePlanForm.get('cycleInterval').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('cycleInterval').errors;\r\n            if (errors) {\r\n                this.isDurationValid = true;\r\n            } else {\r\n                this.isDurationValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDescription = this.updatePlanForm.get('description').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('description').errors;\r\n            if (errors) {\r\n                this.isDescriptionValid = true;\r\n            } else {\r\n                this.isDescriptionValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFreeData = this.updatePlanForm.get('limitDataUsage').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('limitDataUsage').errors;\r\n            if (errors) {\r\n                this.isFreeDataValid = true;\r\n            } else {\r\n                this.isFreeDataValid = false;\r\n            }\r\n        });\r\n        this.subDataMax = this.updatePlanForm.get('dataMax').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('dataMax').errors;\r\n            if (errors) {\r\n                this.isDataMaxValid = true;\r\n            } else {\r\n                this.isDataMaxValid = false;\r\n            }\r\n        });\r\n\r\n        this.subLimitInsideSMSFree = this.updatePlanForm.get('limitSmsInside').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('limitSmsInside').errors;\r\n            if (errors) {\r\n                this.isLimitInsideSMSFreeValid = true;\r\n            } else {\r\n                this.isLimitInsideSMSFreeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subLimitOutsideSMSFree = this.updatePlanForm.get('limitSmsOutside').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('limitSmsOutside').errors;\r\n            if (errors) {\r\n                this.isLimitOutsideSMSFreeValid = true;\r\n            } else {\r\n                this.isLimitOutsideSMSFreeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerUnitNumberator = this.updatePlanForm.get('feePerDataUnit').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('feePerDataUnit').errors;\r\n            if (errors) {\r\n                this.isFeePerUnitNumberatorValid = true;\r\n            } else {\r\n                this.isFeePerUnitNumberatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerUnitDenominator = this.updatePlanForm.get('dataRoundUnit').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('dataRoundUnit').errors;\r\n            if (errors) {\r\n                this.isFeePerUnitDenominatorValid = true;\r\n            } else {\r\n                this.isFeePerUnitDenominatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSqueezeSpeedNumberator = this.updatePlanForm.get('downSpeed').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('downSpeed').errors;\r\n            if (errors) {\r\n                this.isSqueezeSpeedNumberatorValid = true;\r\n            } else {\r\n                this.isSqueezeSpeedNumberatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSqueezeSpeedDenominator = this.updatePlanForm.get('squeezedSpeed').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('squeezedSpeed').errors;\r\n            if (errors) {\r\n                this.isSqueezeSpeedDenominatorValid = true;\r\n            } else {\r\n                this.isSqueezeSpeedDenominatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerInsideSMS = this.updatePlanForm.get('feeSmsInside').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('feeSmsInside').errors;\r\n            if (errors) {\r\n                this.isFeePerInsideSMSValid = true;\r\n            } else {\r\n                this.isFeePerInsideSMSValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerOutsideSMS = this.updatePlanForm.get('feeSmsOutside').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('feeSmsOutside').errors;\r\n            if (errors) {\r\n                this.isFeePerOutsideSMSValid = true;\r\n            } else {\r\n                this.isFeePerOutsideSMSValid = false;\r\n            }\r\n        });\r\n\r\n        this.subMaxFee = this.updatePlanForm.get('maximumFee').statusChanges.subscribe(() => {\r\n            const errors = this.updatePlanForm.get('maximumFee').errors;\r\n            if (errors) {\r\n                this.isMaxFeeValid = true;\r\n            } else {\r\n                this.isMaxFeeValid = false;\r\n            }\r\n        });\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        if (this.subPlanCode && !this.subPlanCode.closed) {\r\n            this.subPlanCode.unsubscribe();\r\n        }\r\n        if (this.subPlaneName && !this.subPlaneName.closed) {\r\n            this.subPlaneName.unsubscribe();\r\n        }\r\n        if (this.subDispatchCode && !this.subDispatchCode.closed) {\r\n            this.subDispatchCode.unsubscribe();\r\n        }\r\n        if (this.subCustomerType && !this.subCustomerType.closed) {\r\n            this.subCustomerType.unsubscribe();\r\n        }\r\n        if (this.subSubscriptionFee && !this.subSubscriptionFee.closed) {\r\n            this.subSubscriptionFee.unsubscribe();\r\n        }\r\n        if (this.subSubscriptionType && !this.subSubscriptionType.closed) {\r\n            this.subSubscriptionType.unsubscribe();\r\n        }\r\n        if (this.subPlanScope && !this.subPlanScope.closed) {\r\n            this.subPlanScope.unsubscribe();\r\n        }\r\n        if (this.subProvinceCode && !this.subProvinceCode.closed) {\r\n            this.subProvinceCode.unsubscribe();\r\n        }\r\n        if (this.subPlanCycle && !this.subPlanCycle.closed) {\r\n            this.subPlanCycle.unsubscribe();\r\n        }\r\n        if (this.subDuration && !this.subDuration.closed) {\r\n            this.subDuration.unsubscribe();\r\n        }\r\n        if (this.subDescription && !this.subDescription.closed) {\r\n            this.subDescription.unsubscribe();\r\n        }\r\n        if (this.subDescription && !this.subDescription.closed) {\r\n            this.subDescription.unsubscribe();\r\n        }\r\n        if (this.subFreeData && !this.subFreeData.closed) {\r\n            this.subFreeData.unsubscribe();\r\n        }\r\n        if (this.subLimitInsideSMSFree && !this.subLimitInsideSMSFree.closed) {\r\n            this.subLimitInsideSMSFree.unsubscribe();\r\n        }\r\n        if (this.subLimitOutsideSMSFree && !this.subLimitOutsideSMSFree.closed) {\r\n            this.subLimitOutsideSMSFree.unsubscribe();\r\n        }\r\n        if (this.subFeePerUnitNumberator && !this.subFeePerUnitNumberator.closed) {\r\n            this.subFeePerUnitNumberator.unsubscribe();\r\n        }\r\n        if (this.subFeePerUnitDenominator && !this.subFeePerUnitDenominator.closed) {\r\n            this.subFeePerUnitDenominator.unsubscribe();\r\n        }\r\n        if (this.subSqueezeSpeedNumberator && !this.subSqueezeSpeedNumberator.closed) {\r\n            this.subSqueezeSpeedNumberator.unsubscribe();\r\n        }\r\n        if (this.subSqueezeSpeedDenominator && !this.subSqueezeSpeedDenominator.closed) {\r\n            this.subSqueezeSpeedDenominator.unsubscribe();\r\n        }\r\n        if (this.subFeePerInsideSMS && !this.subFeePerInsideSMS.closed) {\r\n            this.subFeePerInsideSMS.unsubscribe();\r\n        }\r\n        if (this.subFeePerOutsideSMS && !this.subFeePerOutsideSMS.closed) {\r\n            this.subFeePerOutsideSMS.unsubscribe();\r\n        }\r\n        if (this.subMaxFee && !this.subMaxFee.closed) {\r\n            this.subMaxFee.unsubscribe();\r\n        }\r\n        if (this.subPlaneName && !this.subPlaneName.closed) {\r\n            this.subPlaneName.unsubscribe();\r\n        }\r\n        if (this.subDispatchCode && !this.subDispatchCode.closed) {\r\n            this.subDispatchCode.unsubscribe();\r\n        }\r\n        if (this.subCustomerType && !this.subCustomerType.closed) {\r\n            this.subCustomerType.unsubscribe();\r\n        }\r\n        if (this.subSubscriptionFee && !this.subSubscriptionFee.closed) {\r\n            this.subSubscriptionFee.unsubscribe();\r\n        }\r\n        if (this.subSubscriptionType && !this.subSubscriptionType.closed) {\r\n            this.subSubscriptionType.unsubscribe();\r\n        }\r\n        if (this.subPlanScope && !this.subPlanScope.closed) {\r\n            this.subPlanScope.unsubscribe();\r\n        }\r\n        if (this.subProvinceCode && !this.subProvinceCode.closed) {\r\n            this.subProvinceCode.unsubscribe();\r\n        }\r\n        if (this.subPlanCycle && !this.subPlanCycle.closed) {\r\n            this.subPlanCycle.unsubscribe();\r\n        }\r\n        if (this.subDuration && !this.subDuration.closed) {\r\n            this.subDuration.unsubscribe();\r\n        }\r\n        if (this.subDescription && !this.subDescription.closed) {\r\n            this.subDescription.unsubscribe();\r\n        }\r\n        if (this.subDescription && !this.subDescription.closed) {\r\n            this.subDescription.unsubscribe();\r\n        }\r\n        if (this.subFreeData && !this.subFreeData.closed) {\r\n            this.subFreeData.unsubscribe();\r\n        }\r\n        if (this.subLimitInsideSMSFree && !this.subLimitInsideSMSFree.closed) {\r\n            this.subLimitInsideSMSFree.unsubscribe();\r\n        }\r\n        if (this.subLimitOutsideSMSFree && !this.subLimitOutsideSMSFree.closed) {\r\n            this.subLimitOutsideSMSFree.unsubscribe();\r\n        }\r\n        if (this.subFeePerUnitNumberator && !this.subFeePerUnitNumberator.closed) {\r\n            this.subFeePerUnitNumberator.unsubscribe();\r\n        }\r\n        if (this.subFeePerUnitNumberator && !this.subFeePerUnitDenominator.closed) {\r\n            this.subFeePerUnitDenominator.unsubscribe();\r\n        }\r\n        if (this.subSqueezeSpeedNumberator && !this.subSqueezeSpeedNumberator.closed) {\r\n            this.subSqueezeSpeedNumberator.unsubscribe();\r\n        }\r\n        if (this.subSqueezeSpeedDenominator && !this.subSqueezeSpeedDenominator.closed) {\r\n            this.subSqueezeSpeedDenominator.unsubscribe();\r\n        }\r\n        if (this.subFeePerInsideSMS && !this.subFeePerInsideSMS.closed) {\r\n            this.subFeePerInsideSMS.unsubscribe();\r\n        }\r\n        if (this.subFeePerOutsideSMS && !this.subFeePerOutsideSMS.closed) {\r\n            this.subFeePerOutsideSMS.unsubscribe();\r\n        }\r\n        if (this.subMaxFee && !this.subMaxFee.closed) {\r\n            this.subMaxFee.unsubscribe();\r\n        }\r\n    }\r\n    onSubmitSearchUser(){\r\n        this.pageNumberAssign = 0;\r\n        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\r\n    }\r\n\r\n    searchUser(page, limit, sort, params){\r\n        let me = this;\r\n        this.pageNumberAssign = page;\r\n        this.pageSizeAssign = limit;\r\n        if (sort == null || sort == undefined){\r\n            this.sortAssign = \"id,desc\";\r\n            sort = \"id,desc\";\r\n        }else {\r\n            this.sortAssign = sort;\r\n        }\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n            provinceCode: this.listProvince.map(e => e.code)\r\n        }\r\n        Object.keys(this.searchInfoUser).forEach(key => {\r\n            if(this.searchInfoUser[key] != null){\r\n                dataParams[key] = this.searchInfoUser[key];\r\n            }\r\n        })\r\n        if (this.searchInfoUser.provinceCode == null){\r\n            dataParams.provinceCode = this.listProvince.map(e => e.code);\r\n        }\r\n        this.selectItemsUserOld = [...this.selectItemsUser]\r\n        this.ratingPlanService.getUserToAddAccount(dataParams, (response) => {\r\n            me.dataSetAssignPlan = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            this.selectItemsUser = [...this.selectItemsUserOld]\r\n        })\r\n\r\n    }\r\n\r\n    openDialogAddCustomerAccount() {\r\n        let provincesSelected = this.updatePlanForm.get('provinceCode').value;\r\n        this.listProvince = this.provinces.filter((prov) =>\r\n            provincesSelected.includes(prov.code)\r\n        )\r\n        if (this.pageNumberAssign == null){\r\n            this.pageNumberAssign = 0;\r\n        }\r\n        if (this.pageSizeAssign == null){\r\n            this.pageSizeAssign = 10;\r\n        }\r\n        if (this.sortAssign == null){\r\n            this.sortAssign = \"id,desc\";\r\n        }\r\n        this.searchInfoUser.provinceCode = this.listProvince.map(e => e.code);\r\n        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser)\r\n        this.isShowDialogAddCustomerAccount = true\r\n    }\r\n    changeProvince() {\r\n        let provinceSelected = this.updatePlanForm.get(\"provinceCode\").value\r\n        let ratingScope = this.updatePlanForm.get(\"ratingScope\").value\r\n        if (ratingScope == 1){\r\n            if (provinceSelected.length > 0){\r\n                this.isCustomer = true;\r\n            }else {\r\n                this.isCustomer = false;\r\n            }\r\n        }\r\n    }\r\n}\r\n", "\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.listplan\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n</div>\r\n\r\n<p-card class=\"p-4\" styleClass=\"responsive-form-plans\" *ngIf=\"updatePlanForm\">\r\n    <form action=\"\" [formGroup]=\"updatePlanForm\" (submit)=\"submitForm()\">\r\n        <div class=\"pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid\">\r\n            <div class=\"col-6 grid-col\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"code\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.planCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"code\" pInputText id=\"code\" [readonly]=isRead type=\"text\" [placeholder]=\"placeHolder.planCode\"/>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlanCodeValid && updatePlanForm.get('code').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlanCodeValid && updatePlanForm.get('code').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_64\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlanCodeValid && updatePlanForm.get('code').hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.characterError_code\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlanCodeValid && updatePlanForm.get('code').hasError('exited')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.existedCodeError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"name\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.planName\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"name\" pInputText id=\"name\" type=\"text\" [placeholder]=\"placeHolder.planName\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlaneNameValid && updatePlanForm.get('name').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlaneNameValid && updatePlanForm.get('name').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_255\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlaneNameValid && updatePlanForm.get('name').hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.wrongFormatName\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlaneNameValid && updatePlanForm.get('name').hasError('exited')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.existedNameError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"dispatchCode\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.dispatchCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"dispatchCode\" pInputText id=\"dispatchCode\" [readonly]=isRead type=\"text\" [placeholder]=\"placeHolder.dispatchCode\"/>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDispatchCodeValid && updatePlanForm.get('dispatchCode').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDispatchCodeValid && updatePlanForm.get('dispatchCode').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_64\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDispatchCodeValid && updatePlanForm.get('dispatchCode').hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.characterError_code\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"customerType\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.customerType\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <p-dropdown formControlName=\"customerType\" [options]=\"customerTypes\" optionLabel=\"name\" optionValue=\"id\" autoDisplayFirst=\"false\" [placeholder]=\"placeHolder.customerType\"></p-dropdown>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isCustomerTypeValid && updatePlanForm.get('customerType').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"description\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.description\")}}</label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <textarea formControlName=\"description\" id=\"description\" rows=\"4\" cols=\"30\" [placeholder]=\"placeHolder.description\" pInputText></textarea>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDescriptionValid && updatePlanForm.get('description').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_255\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 grid-col\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"subscriptionFee\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.subscriptionFee\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"subscriptionFee\" pInputText id=\"subscriptionFee\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.subscriptionFee\"/>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        (đồng/tháng <br/> đã gồm VAT)\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isSubscriptionFeeValid && updatePlanForm.get('subscriptionFee').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isSubscriptionFeeValid && updatePlanForm.get('subscriptionFee').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isSubscriptionFeeValid && updatePlanForm.get('subscriptionFee').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 pb-2 custom-responsive-field\">\r\n                    <div class=\"col-12 mb-2 md:col-2 md:mb-0 flex flex-row align-items-center radio-group wrapper\">\r\n                        <div *ngFor=\"let paidType of paidCategories\" class=\"field-checkbox my-auto\"  style=\"min-width: 140px; min-height:35px;\">\r\n                            <p-radioButton formControlName=\"paidType\" [inputId]=\"paidType.key\" name=\"paidType\" [value]=\"paidType.key\" [(ngModel)]=\"selectedPaidCategory\"></p-radioButton>\r\n                            <label [for]=\"paidType.key\" class=\"ml-2\">{{ paidType.name }}</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"switch-group-wrapper flex flex-row align-items-center mt-2\">\r\n                        <label htmlFor=\"reload\"  style=\"min-width: 130px;\" class=\"col-12 mb-2 md:col-2 md:mb-0 responsive-switch-label\">{{tranService.translate(\"ratingPlan.label.autoReload\")}}</label>\r\n                        <div class=\"col-12 md:col-10 flex-1\">\r\n                            <p-inputSwitch formControlName=\"reload\" class=\"flex align-items-center\"></p-inputSwitch>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"cycle\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.cycle\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <p-dropdown formControlName=\"cycleTimeUnit\" [options]=\"cycles\" optionLabel=\"name\" optionValue=\"id\" autoDisplayFirst=\"true\" [placeholder]=\"placeHolder.planCycle\"></p-dropdown>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlanCycleValid && updatePlanForm.get('cycleTimeUnit').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"cycleInterval\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.duration\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"cycleInterval\" pInputText id=\"cycleInterval\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.duration\"/>\r\n                    </div>\r\n                    <div *ngIf=\"updatePlanForm.get('cycleTimeUnit').value == '1'\" class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.cycle.day\")}}\r\n                    </div>\r\n                    <div *ngIf=\"updatePlanForm.get('cycleTimeUnit').value == '3'\" class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.cycle.month\")}}\r\n                    </div>\r\n                    <div *ngIf=\"updatePlanForm.get('cycleTimeUnit').value != '1' && updatePlanForm.get('cycleTimeUnit').value != '3'\" class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.text.dayMonth\")}}\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDurationValid && updatePlanForm.get('cycleInterval').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDurationValid && updatePlanForm.get('cycleInterval').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDurationValid && updatePlanForm.get('cycleInterval').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"ratingScope\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.ratingScope\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <p-dropdown (onChange)=\"onDropdownChange($event)\" formControlName=\"ratingScope\" [options]=\"ratingScopes\" optionLabel=\"name\" optionValue=\"id\" autoDisplayFirst=\"false\" [placeholder]=\"placeHolder.planScope\"></p-dropdown>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlanScopeValid && updatePlanForm.get('ratingScope').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div *ngIf=\"isProvince\" class=\"field grid px-4 flex flex-row flex-nowrap\" >\r\n                    <label htmlFor=\"provinceCode\" style=\"min-width: 140px;\" class=\"col-fixed\">{{tranService.translate(\"ratingPlan.label.province\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 230px);\"  *ngIf=\"userType == allUserType.ADMIN\">\r\n                        <p-multiSelect\r\n                            formControlName=\"provinceCode\"\r\n                            [options]=\"provinces\"\r\n                            [showToggleAll]=\"false\"\r\n                            display=\"chip\" optionLabel=\"name\"\r\n                            optionValue=\"code\" autoDisplayFirst=\"false\"\r\n                            [placeholder]=\"placeHolder.provinceCode\"\r\n                            (onChange)=\"changeProvince()\"\r\n                        ></p-multiSelect>\r\n                    </div>\r\n                    <div class=\"col\" [pTooltip]=\"provinceInfo\" tooltipPosition=\"top\" style=\"max-width: calc(100% - 140px);white-space: nowrap;overflow: hidden;text-overflow: ellipsis;\"  *ngIf=\"userType != allUserType.ADMIN\">\r\n                        {{provinceInfo}}\r\n                    </div>\r\n                    <div class=\"col-fixed\" style=\"min-width: 90px;\" *ngIf=\"userType == allUserType.ADMIN\">\r\n                    </div>\r\n                </div>\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isProvinceCodeValid && updatePlanForm.get('provinceCode').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div *ngIf=\"isCustomer\" class=\"field grid px-4 flex flex-row flex-nowrap3 responsive-size-input\">\r\n                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"min-width: 140px; cursor: pointer; text-decoration: underline; color: blue; transition: color 0.3s;\" (click)=\"openDialogAddCustomerAccount()\">{{tranService.translate(\"account.label.addCustomerAccount\")}}</label>\r\n<!--                    <div class=\"col\" style=\"max-width: calc(100% - 230px) !important;\">-->\r\n<!--                        <vnpt-select-->\r\n<!--                            [control]=\"controlComboSelect\"-->\r\n<!--                            class=\"w-full\"-->\r\n<!--                            [(value)]=\"customerCode\"-->\r\n<!--                            [placeholder]=\"tranService.translate('account.text.selectCustomers')\"-->\r\n<!--                            objectKey=\"searchUserForRatingPlan\"-->\r\n<!--                            paramKey=\"keySearch\"-->\r\n<!--                            keyReturn=\"id\"-->\r\n<!--                            displayPattern=\"${provinceName}-${username}-${fullName}\"-->\r\n<!--                            typeValue=\"primitive\"-->\r\n<!--                            [required]=\"isCustomer\"-->\r\n<!--                            (onchange)=\"onChangeCustomers()\"-->\r\n<!--                        ></vnpt-select>-->\r\n<!--                    </div>-->\r\n                </div>\r\n\r\n\r\n            </div>\r\n        </div>\r\n        <h4 class=\"ml-2\">{{tranService.translate(\"ratingPlan.label.flat\")}}</h4>\r\n        <div class=\"pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid\">\r\n            <div class=\"flex-1\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"limitDataUsage\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.freeData\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"limitDataUsage\" pInputText id=\"limitDataUsage\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.freeData\"/>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\">\r\n                        (MB)\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isFreeDataValid && updatePlanForm.get('limitDataUsage').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isFreeDataValid && updatePlanForm.get('limitDataUsage').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isFreeDataValid && updatePlanForm.get('limitDataUsage').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"limitDataUsage\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.placeHolder.dataMax\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"dataMax\" pInputText id=\"dataMax\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.dataMax\"/>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\">\r\n                        (MB)\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDataMaxValid && updatePlanForm.get('dataMax').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDataMaxValid && updatePlanForm.get('dataMax').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDataMaxValid && updatePlanForm.get('dataMax').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"insideSMSFree\"  style=\"min-width: 170px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.insideSMSFree\")}}</label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"limitSmsInside\" pInputText id=\"insideSMSFree\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.insideSMSFree\"/>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isLimitInsideSMSFreeValid && updatePlanForm.get('limitSmsInside').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isLimitInsideSMSFreeValid && updatePlanForm.get('limitSmsInside').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"outsideSMSFree\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.outsideSMSFree\")}}</label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"limitSmsOutside\" pInputText id=\"outsideSMSFree\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.outsideSMSFree\"/>\r\n                    </div>\r\n<!--                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\"></div>-->\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isLimitOutsideSMSFreeValid && updatePlanForm.get('limitSmsOutside').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isLimitOutsideSMSFreeValid && updatePlanForm.get('limitSmsOutside').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row gap-3 ml-2 mt-4 mb-3\">\r\n            <h4 class=\"m-0\">{{tranService.translate(\"ratingPlan.label.flexible\")}}</h4>\r\n            <p-inputSwitch formControlName=\"flexible\" class=\"\" [(ngModel)]=\"isFlexible\" (onChange)=\"onSwitchChange()\"></p-inputSwitch>\r\n        </div>\r\n        <div class=\"pt-0 shadow-2 border-round-md mb-4 m-1 flex p-fluid p-formgrid flex-column grid\">\r\n            <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                <label htmlFor=\"feePerDataUnit\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.feePerUnit\")}}<span class=\"text-red-500\">*</span></label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"feePerDataUnit\" pInputText id=\"feePerDataUnit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerUnit\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\">/</div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"dataRoundUnit\" pInputText id=\"dataRoundUnit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerUnit\"/>\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\">(KB)</div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error-2\"></div>\r\n                <div class=\"col-11 md:col-5 py-0\">\r\n                    <div *ngIf=\"isFeePerUnitNumberatorValid && updatePlanForm.get('feePerDataUnit').hasError('required')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitNumberatorValid && updatePlanForm.get('feePerDataUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitNumberatorValid && updatePlanForm.get('feePerDataUnit').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                    <!-- <div *ngIf=\"isShowValid && createPlanForm.get('dataRoundUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div> -->\r\n                </div>\r\n                <div class=\"col-11 md:col-5 py-0 responsive-error-2\">\r\n                    <div *ngIf=\"isFeePerUnitDenominatorValid && updatePlanForm.get('dataRoundUnit').hasError('required')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitDenominatorValid && updatePlanForm.get('dataRoundUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitDenominatorValid && updatePlanForm.get('dataRoundUnit').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                    <!-- <div *ngIf=\"isShowValid && createPlanForm.get('dataRoundUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div> -->\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                <label htmlFor=\"downSpeed\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.squeezeSpeed\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"downSpeed\" pInputText id=\"downSpeed\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.squeezedSpeed\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\">/</div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"squeezedSpeed\" pInputText id=\"squeezedSpeed\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.squeezedSpeed\"/>\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\">(Kbps)</div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error-2\"></div>\r\n                <div class=\"col-11 md:col-5 py-0\">\r\n                    <div *ngIf=\"isSqueezeSpeedNumberatorValid && updatePlanForm.get('downSpeed').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isSqueezeSpeedNumberatorValid && updatePlanForm.get('downSpeed').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-11 md:col-5 py-0 responsive-error-2\">\r\n                    <div *ngIf=\"isSqueezeSpeedDenominatorValid && updatePlanForm.get('squeezedSpeed').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isSqueezeSpeedDenominatorValid && updatePlanForm.get('squeezedSpeed').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-div\">\r\n                <label htmlFor=\"feeSmsInside\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.feePerInsideSMS\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"feeSmsInside\" pInputText id=\"feeSmsInside\" class=\"input-edit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerInsideSMS\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\"></div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\"></div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error\"></div>\r\n                <div class=\"col-11 md:col-11 py-0 responsive-error-1\">\r\n                    <div *ngIf=\"isFeePerInsideSMSValid && updatePlanForm.get('feeSmsInside').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerInsideSMSValid && updatePlanForm.get('feeSmsInside').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-div\">\r\n                <label htmlFor=\"feeSmsOutside\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.feePerOutsideSMS\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"feeSmsOutside\" pInputText id=\"feeSmsOutside\" class=\"input-edit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerOutsideSMS\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\"></div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\"></div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error\"></div>\r\n                <div class=\"col-11 md:col-11 py-0 responsive-error-1\">\r\n                    <div *ngIf=\"isFeePerOutsideSMSValid && updatePlanForm.get('feeSmsOutside').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerOutsideSMSValid && updatePlanForm.get('feeSmsOutside').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 pb-3 flex flex-row flex-nowrap responsive-div\" >\r\n                <label htmlFor=\"maximumFee\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.maxFee\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"maximumFee\" pInputText id=\"maximumFee\" class=\"input-edit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.maxFee\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\"></div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\"></div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error\"></div>\r\n                <div class=\"col-11 md:col-11 py-0 responsive-error-1\">\r\n                    <div *ngIf=\"isMaxFeeValid && updatePlanForm.get('maximumFee').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isMaxFeeValid && updatePlanForm.get('maximumFee').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center gap-3 p-2\">\r\n            <a routerLink=\"/plans\"><button pButton [label]=\"tranService.translate('global.button.cancel')\" class=\"p-button-secondary p-button-outlined\" type=\"button\"></button></a>\r\n            <button pButton type=\"submit\" [label]=\"tranService.translate('global.button.save')\" class=\"p-button-info\" [disabled]=\"isPlanCodeExisted||isPlanNameExisted||updatePlanForm.invalid\"></button>\r\n        </div>\r\n    </form>\r\n</p-card>\r\n\r\n<form [formGroup]=\"formSearchUser\" (ngSubmit)=\"onSubmitSearchUser()\">\r\n    <div class=\"flex justify-content-center dialog-push-group\">\r\n        <p-dialog [header]=\"tranService.translate('account.label.addCustomerAccount')\" [(visible)]=\"isShowDialogAddCustomerAccount\" [modal]=\"true\" [style]=\"{ width: '850px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n            <div class=\"grid\">\r\n                <!-- Ten dang nhap -->\r\n                <div class=\"col-3\">\r\n                    <span class=\"p-float-label\">\r\n                        <input pInputText\r\n                               class=\"w-full\"\r\n                               [(ngModel)]=\"searchInfoUser.username\"\r\n                               pInputText id=\"username\"\r\n                               formControlName=\"username\"\r\n                        />\r\n                        <label htmlFor=\"username\">{{tranService.translate(\"ratingPlan.label.username\")}}</label>\r\n                    </span>\r\n                </div>\r\n                <!-- Ho ten -->\r\n                <div class=\"col-3\">\r\n                    <span class=\"p-float-label\">\r\n                        <input pInputText\r\n                               class=\"w-full\"\r\n                               [(ngModel)]=\"searchInfoUser.fullName\"\r\n                               pInputText id=\"fullName\"\r\n                               formControlName=\"fullName\"\r\n                        />\r\n                        <label htmlFor=\"fullName\">{{tranService.translate(\"ratingPlan.label.fullName\")}}</label>\r\n                    </span>\r\n                </div>\r\n                <!-- Thanh pho -->\r\n                <div class=\"col-3\" [class]=\"userType == allUserType.ADMIN ? '' : 'flex flex-row justify-content-start align-items-center'\">\r\n                    <span class=\"p-float-label\" *ngIf=\"userType == allUserType.ADMIN\">\r\n                        <p-multiSelect styleClass=\"w-full\" [showClear]=\"true\"\r\n                                       id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                       [(ngModel)]=\"searchInfoUser.provinceCode\"\r\n                                       [options]=\"listProvince\"\r\n                                       optionLabel=\"name\"\r\n                                       optionValue=\"code\"\r\n                                       formControlName=\"provinceCode\"\r\n                        ></p-multiSelect>\r\n                        <label for=\"provinceCode\">{{tranService.translate(\"ratingPlan.label.province\")}}</label>\r\n                    </span>\r\n                    <span *ngIf=\"userType != allUserType.ADMIN\">{{tranService.translate(\"account.label.province\")}}: {{provinceInfo}}</span>\r\n                </div>\r\n                <div class=\"col-3 pb-0\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"submit\"\r\n                    ></p-button>\r\n                </div>\r\n            </div>\r\n            <table-vnpt\r\n                [fieldId]=\"'id'\"\r\n                [pageNumber]=\"pageNumberAssign\"\r\n                [pageSize]=\"pageSizeAssign\"\r\n                [(selectItems)]=\"selectItemsUser\"\r\n                [columns]=\"columnsInfoUser\"\r\n                [dataSet]=\"dataSetAssignPlan\"\r\n                [options]=\"optionTableAddCustomerAccount\"\r\n                [loadData]=\"searchUser.bind(this)\"\r\n                [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                [scrollHeight]=\"'400px'\"\r\n                [sort]=\"sort\"\r\n                [params]=\"searchInfoUser\"\r\n            ></table-vnpt>\r\n<!--            <div class=\"flex flex-row justify-content-center align-items-center\" style=\"padding-top: 30px\">-->\r\n<!--&lt;!&ndash;                <p-button styleClass=\"p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogAddCustomerAccount = false\" [style]=\"{'margin-right': '20px'}\"></p-button>&ndash;&gt;-->\r\n<!--                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"isShowDialogAddCustomerAccount = false\" ></p-button>-->\r\n<!--            </div>-->\r\n        </p-dialog>\r\n    </div>\r\n</form>\r\n"], "mappings": "AAEA,SAGIA,WAAW,EACXC,SAAS,EAGTC,UAAU,QACP,gBAAgB;AAEvB,SAAQC,UAAU,EAAgBC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,QAAO,MAAM;AAC3G,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,cAAc,QAAO,wCAAwC;AACrE,SAAQC,SAAS,QAAO,iCAAiC;AACzD,SAAQC,iBAAiB,QAAO,+CAA+C;AAC/E,SAAQC,gBAAgB,QAAO,4DAA4D;;;;;;;;;;;;;;;;;;;;ICKnEC,EAAA,CAAAC,cAAA,cAAqG;IACjGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsG;IAClGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,yCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA8G;IAC1GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAK,MAAA,CAAAH,WAAA,CAAAC,SAAA,8CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAmG;IAC/FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAM,MAAA,CAAAJ,WAAA,CAAAC,SAAA,2CACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAsG;IAClGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAuG;IACnGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,0CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAS,MAAA,CAAAP,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAU,OAAA,CAAAR,WAAA,CAAAC,SAAA,2CACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAW,OAAA,CAAAT,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkH;IAC9GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAY,OAAA,CAAAV,WAAA,CAAAC,SAAA,yCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA0H;IACtHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAa,OAAA,CAAAX,WAAA,CAAAC,SAAA,8CACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAc,OAAA,CAAAZ,WAAA,CAAAC,SAAA,wCACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAgH;IAC5GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAe,OAAA,CAAAb,WAAA,CAAAC,SAAA,0CACJ;;;;;IAkBAR,EAAA,CAAAC,cAAA,cAAuH;IACnHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAgB,OAAA,CAAAd,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkH;IAC9GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAiB,OAAA,CAAAf,WAAA,CAAAC,SAAA,6CACJ;;;;;;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkH;IAC9GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAkB,OAAA,CAAAhB,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;;IAMAzB,EAAA,CAAAC,cAAA,eAAwH;IACVD,EAAA,CAAA0B,UAAA,2BAAAC,oFAAAC,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAAAF,OAAA,CAAAG,oBAAA,GAAAN,MAAA;IAAA,EAAkC;IAAC5B,EAAA,CAAAG,YAAA,EAAgB;IAC7JH,EAAA,CAAAC,cAAA,iBAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAD1BH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,YAAAC,YAAA,CAAAC,GAAA,CAAwB,UAAAD,YAAA,CAAAC,GAAA,aAAAC,OAAA,CAAAJ,oBAAA;IAC3DlC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAmC,UAAA,QAAAC,YAAA,CAAAC,GAAA,CAAoB;IAAcrC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuC,iBAAA,CAAAH,YAAA,CAAAI,IAAA,CAAmB;;;;;IAwBhExC,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAoC,OAAA,CAAAlC,WAAA,CAAAC,SAAA,wCACJ;;;;;IASJR,EAAA,CAAAC,cAAA,cAA4G;IACxGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqC,OAAA,CAAAnC,WAAA,CAAAC,SAAA,8BACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA4G;IACxGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsC,OAAA,CAAApC,WAAA,CAAAC,SAAA,gCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAgK;IAC5JD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAuC,OAAA,CAAArC,WAAA,CAAAC,SAAA,kCACJ;;;;;IAMIR,EAAA,CAAAC,cAAA,cAA8G;IAC1GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAwC,OAAA,CAAAtC,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyG;IACrGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyC,OAAA,CAAAvC,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyG;IACrGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAxC,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAgBAzB,EAAA,CAAAC,cAAA,cAA6G;IACzGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2C,OAAA,CAAAzC,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAMJR,EAAA,CAAAC,cAAA,eAA+F;IAQvFD,EAAA,CAAA0B,UAAA,sBAAAuB,sFAAA;MAAAjD,EAAA,CAAA6B,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAgC,aAAA;MAAA,OAAYhC,EAAA,CAAAiC,WAAA,CAAAkB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAChCpD,EAAA,CAAAG,YAAA,EAAgB;;;;IANbH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAmC,UAAA,YAAAkB,OAAA,CAAAC,SAAA,CAAqB,wCAAAD,OAAA,CAAAE,WAAA,CAAAC,YAAA;;;;;IAQ7BxD,EAAA,CAAAC,cAAA,eAA4M;IACxMD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFWH,EAAA,CAAAmC,UAAA,aAAAsB,OAAA,CAAAC,YAAA,CAAyB;IACtC1D,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAoD,OAAA,CAAAC,YAAA,MACJ;;;;;IACA1D,EAAA,CAAA2D,SAAA,eACM;;;;;IAjBV3D,EAAA,CAAAC,cAAA,eAA2E;IACGD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnKH,EAAA,CAAA4D,UAAA,IAAAC,mDAAA,mBAUM;IACN7D,EAAA,CAAA4D,UAAA,IAAAE,mDAAA,mBAEM;IACN9D,EAAA,CAAA4D,UAAA,IAAAG,mDAAA,mBACM;IACV/D,EAAA,CAAAG,YAAA,EAAM;;;;IAjBwEH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAyB,OAAA,CAAAzD,WAAA,CAAAC,SAAA,8BAAsD;IACtER,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,SAAA6B,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAE,WAAA,CAAAC,KAAA,CAAmC;IAW0EnE,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,SAAA6B,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAE,WAAA,CAAAC,KAAA,CAAmC;IAGzJnE,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,SAAA6B,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAE,WAAA,CAAAC,KAAA,CAAmC;;;;;IAMhFnE,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA+D,OAAA,CAAA7D,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAIRR,EAAA,CAAAC,cAAA,eAAiG;IACwDD,EAAA,CAAA0B,UAAA,mBAAA2C,qEAAA;MAAArE,EAAA,CAAA6B,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAsC,OAAA,CAAAC,4BAAA,EAA8B;IAAA,EAAC;IAACxE,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAArEH,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAuC,iBAAA,CAAAkC,OAAA,CAAAlE,WAAA,CAAAC,SAAA,qCAA6D;;;;;IAqCvPR,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqE,OAAA,CAAAnE,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA0G;IACtGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsE,OAAA,CAAApE,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA0G;IACtGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAuE,OAAA,CAAArE,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAiBAzB,EAAA,CAAAC,cAAA,cAAuG;IACnGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAwE,OAAA,CAAAtE,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkG;IAC9FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyE,OAAA,CAAAvE,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkG;IAC9FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA0E,OAAA,CAAAxE,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAgBAzB,EAAA,CAAAC,cAAA,cAAoH;IAChHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2E,OAAA,CAAAzE,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAoH;IAChHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA4E,OAAA,CAAA1E,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAcAzB,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA6E,OAAA,CAAA3E,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8E,OAAA,CAAA5E,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAyBJzB,EAAA,CAAAC,cAAA,cAA2H;IACvHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA+E,OAAA,CAAA7E,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAgF,OAAA,CAAA9E,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAiF,OAAA,CAAA/E,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAMAzB,EAAA,CAAAC,cAAA,cAA2H;IACvHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAkF,OAAA,CAAAhF,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAmF,OAAA,CAAAjF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAoF,OAAA,CAAAlF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAsBAzB,EAAA,CAAAC,cAAA,cAAmH;IAC/GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqF,OAAA,CAAAnF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAmH;IAC/GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsF,OAAA,CAAApF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAGAzB,EAAA,CAAAC,cAAA,cAAwH;IACpHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAwH;IACpHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAwF,OAAA,CAAAtF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAkBAzB,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyF,OAAA,CAAAvF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA0F,OAAA,CAAAxF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAkBAzB,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2F,OAAA,CAAAzF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA4F,OAAA,CAAA1F,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAkBAzB,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA6F,OAAA,CAAA3F,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8F,OAAA,CAAA5F,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;;IA3fpBzB,EAAA,CAAAC,cAAA,iBAA8E;IAC7BD,EAAA,CAAA0B,UAAA,oBAAA0E,6DAAA;MAAApG,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAC,OAAA,GAAAtG,EAAA,CAAAgC,aAAA;MAAA,OAAUhC,EAAA,CAAAiC,WAAA,CAAAqE,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAChEvG,EAAA,CAAAC,cAAA,cAA4E;IAGsBD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/KH,EAAA,CAAAC,cAAA,cAAqC;IACjCD,EAAA,CAAA2D,SAAA,iBAAuH;IAC3H3D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,eAAqC;IACrC3D,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAA4D,UAAA,KAAA4C,4CAAA,kBAEM;IACNxG,EAAA,CAAA4D,UAAA,KAAA6C,4CAAA,kBAEM;IACNzG,EAAA,CAAA4D,UAAA,KAAA8C,4CAAA,kBAEM;IACN1G,EAAA,CAAA4D,UAAA,KAAA+C,4CAAA,kBAEM;IACV3G,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA6E;IACaD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/KH,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAA2D,SAAA,iBAAoG;IACxG3D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,eAAqC;IACrC3D,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAA4D,UAAA,KAAAgD,4CAAA,kBAEM;IACN5G,EAAA,CAAA4D,UAAA,KAAAiD,4CAAA,kBAEM;IACN7G,EAAA,CAAA4D,UAAA,KAAAkD,4CAAA,kBAEM;IACN9G,EAAA,CAAA4D,UAAA,KAAAmD,4CAAA,kBAEM;IACV/G,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA6E;IACqBD,EAAA,CAAAE,MAAA,IAA0D;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3LH,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAA2D,SAAA,iBAA2I;IAC/I3D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,eAAqC;IACrC3D,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAA4D,UAAA,KAAAoD,4CAAA,kBAEM;IACNhH,EAAA,CAAA4D,UAAA,KAAAqD,4CAAA,kBAEM;IACNjH,EAAA,CAAA4D,UAAA,KAAAsD,4CAAA,kBAEM;IACVlH,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA6E;IACqBD,EAAA,CAAAE,MAAA,IAA0D;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3LH,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAA2D,SAAA,sBAAwL;IAC5L3D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,eAAqC;IACrC3D,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAA4D,UAAA,KAAAuD,4CAAA,kBAEM;IACVnH,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAuE;IAC0BD,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9JH,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAA2D,SAAA,oBAA0I;IAC9I3D,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,eAAqC;IACrC3D,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAA4D,UAAA,KAAAwD,4CAAA,kBAEM;IACVpH,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAA4B;IAE6ED,EAAA,CAAAE,MAAA,IAA6D;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjMH,EAAA,CAAAC,cAAA,eAAqC;IACsDD,EAAA,CAAA0B,UAAA,qBAAA2F,gEAAAzF,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAiB,OAAA,GAAAtH,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAqF,OAAA,CAAAC,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAA4F,8DAAA5F,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAoB,OAAA,GAAAzH,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAwF,OAAA,CAAAC,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAArH5B,EAAA,CAAAG,YAAA,EAAsM;IAE1MH,EAAA,CAAAC,cAAA,eAAmD;IAC/CD,EAAA,CAAAE,MAAA,oCAAY;IAAAF,EAAA,CAAA2D,SAAA,UAAK;IAAC3D,EAAA,CAAAE,MAAA,oCACtB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,eAAqC;IACrC3D,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAA4D,UAAA,KAAA+D,4CAAA,kBAEM;IACN3H,EAAA,CAAA4D,UAAA,KAAAgE,4CAAA,kBAEM;IACN5H,EAAA,CAAA4D,UAAA,KAAAiE,4CAAA,kBAEM;IACV7H,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA0D;IAElDD,EAAA,CAAA4D,UAAA,KAAAkE,4CAAA,kBAGM;IACV9H,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwE;IAC4CD,EAAA,CAAAE,MAAA,IAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChLH,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAA2D,SAAA,yBAAwF;IAC5F3D,EAAA,CAAAG,YAAA,EAAM;IAKdH,EAAA,CAAAC,cAAA,eAA8E;IACaD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7KH,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAA2D,SAAA,sBAA8K;IAClL3D,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA2D,SAAA,eACM;IACV3D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAAmE,6CAAA,kBAEM;IACV/H,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA8E;IACqBD,EAAA,CAAAE,MAAA,KAAsD;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxLH,EAAA,CAAAC,cAAA,gBAAqC;IACkDD,EAAA,CAAA0B,UAAA,qBAAAsG,iEAAApG,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA4B,OAAA,GAAAjI,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAgG,OAAA,CAAAV,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAsG,+DAAAtG,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA8B,OAAA,GAAAnI,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAkG,OAAA,CAAAT,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAjH5B,EAAA,CAAAG,YAAA,EAA2L;IAE/LH,EAAA,CAAA4D,UAAA,MAAAwE,6CAAA,kBAEM;IACNpI,EAAA,CAAA4D,UAAA,MAAAyE,6CAAA,kBAEM;IACNrI,EAAA,CAAA4D,UAAA,MAAA0E,6CAAA,kBAEM;IACVtI,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAA2E,6CAAA,kBAEM;IACNvI,EAAA,CAAA4D,UAAA,MAAA4E,6CAAA,kBAEM;IACNxI,EAAA,CAAA4D,UAAA,MAAA6E,6CAAA,kBAEM;IACVzI,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA8E;IACmBD,EAAA,CAAAE,MAAA,KAAyD;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzLH,EAAA,CAAAC,cAAA,gBAAqC;IACrBD,EAAA,CAAA0B,UAAA,sBAAAgH,uEAAA9G,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAsC,OAAA,GAAA3I,EAAA,CAAAgC,aAAA;MAAA,OAAYhC,EAAA,CAAAiC,WAAA,CAAA0G,OAAA,CAAAC,gBAAA,CAAAhH,MAAA,CAAwB;IAAA,EAAC;IAA2J5B,EAAA,CAAAG,YAAA,EAAa;IAE7NH,EAAA,CAAA2D,SAAA,gBACM;IACV3D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAAiF,6CAAA,kBAEM;IACV7I,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAA4D,UAAA,MAAAkF,6CAAA,kBAkBM;IACN9I,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAAmF,6CAAA,kBAEM;IACV/I,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAA4D,UAAA,MAAAoF,6CAAA,kBAiBM;IAGVhJ,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,eAAiB;IAAAD,EAAA,CAAAE,MAAA,KAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxEH,EAAA,CAAAC,cAAA,gBAA4E;IAGgCD,EAAA,CAAAE,MAAA,KAAsD;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzLH,EAAA,CAAAC,cAAA,gBAAqC;IACoDD,EAAA,CAAA0B,UAAA,qBAAAuH,iEAAArH,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA6C,OAAA,GAAAlJ,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAiH,OAAA,CAAA3B,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAuH,+DAAAvH,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA+C,OAAA,GAAApJ,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAmH,OAAA,CAAA1B,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAnH5B,EAAA,CAAAG,YAAA,EAA6L;IAEjMH,EAAA,CAAAC,cAAA,gBAAmD;IAC/CD,EAAA,CAAAE,MAAA,eACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAAyF,6CAAA,kBAEM;IACNrJ,EAAA,CAAA4D,UAAA,MAAA0F,6CAAA,kBAEM;IACNtJ,EAAA,CAAA4D,UAAA,MAAA2F,6CAAA,kBAEM;IACVvJ,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA6E;IACuBD,EAAA,CAAAE,MAAA,KAA2D;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9LH,EAAA,CAAAC,cAAA,gBAAqC;IACsCD,EAAA,CAAA0B,UAAA,qBAAA8H,iEAAA5H,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAoD,OAAA,GAAAzJ,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAwH,OAAA,CAAAlC,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAA8H,+DAAA9H,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAsD,OAAA,GAAA3J,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAA0H,OAAA,CAAAjC,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAArG5B,EAAA,CAAAG,YAAA,EAA8K;IAElLH,EAAA,CAAAC,cAAA,gBAAmD;IAC/CD,EAAA,CAAAE,MAAA,eACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAAgG,6CAAA,kBAEM;IACN5J,EAAA,CAAA4D,UAAA,MAAAiG,6CAAA,kBAEM;IACN7J,EAAA,CAAA4D,UAAA,MAAAkG,6CAAA,kBAEM;IACV9J,EAAA,CAAAG,YAAA,EAAM;IAIdH,EAAA,CAAAC,cAAA,gBAAoB;IAE8ED,EAAA,CAAAE,MAAA,KAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7JH,EAAA,CAAAC,cAAA,gBAAqC;IACmDD,EAAA,CAAA0B,UAAA,qBAAAqI,iEAAAnI,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA2D,OAAA,GAAAhK,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAA+H,OAAA,CAAAzC,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAqI,+DAAArI,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA6D,OAAA,GAAAlK,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAiI,OAAA,CAAAxC,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAlH5B,EAAA,CAAAG,YAAA,EAAiM;IAIzMH,EAAA,CAAAC,cAAA,gBAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAAuG,6CAAA,kBAEM;IACNnK,EAAA,CAAA4D,UAAA,MAAAwG,6CAAA,kBAEM;IACVpK,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,gBAA6E;IACuBD,EAAA,CAAAE,MAAA,KAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpKH,EAAA,CAAAC,cAAA,gBAAqC;IACqDD,EAAA,CAAA0B,UAAA,qBAAA2I,iEAAAzI,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAiE,OAAA,GAAAtK,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAqI,OAAA,CAAA/C,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAA2I,+DAAA3I,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAmE,OAAA,GAAAxK,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAuI,OAAA,CAAA9C,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAApH5B,EAAA,CAAAG,YAAA,EAAoM;IAK5MH,EAAA,CAAAC,cAAA,gBAA4E;IACxED,EAAA,CAAA2D,SAAA,gBAAqC;IACrC3D,EAAA,CAAAC,cAAA,gBAAmC;IAC/BD,EAAA,CAAA4D,UAAA,MAAA6G,6CAAA,kBAEM;IACNzK,EAAA,CAAA4D,UAAA,MAAA8G,6CAAA,kBAEM;IACV1K,EAAA,CAAAG,YAAA,EAAM;IAIlBH,EAAA,CAAAC,cAAA,gBAAgD;IAC5BD,EAAA,CAAAE,MAAA,KAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3EH,EAAA,CAAAC,cAAA,0BAA0G;IAAvDD,EAAA,CAAA0B,UAAA,2BAAAiJ,+EAAA/I,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAuE,OAAA,GAAA5K,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAAA2I,OAAA,CAAAC,UAAA,GAAAjJ,MAAA;IAAA,EAAwB,sBAAAkJ,0EAAA;MAAA9K,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA0E,OAAA,GAAA/K,EAAA,CAAAgC,aAAA;MAAA,OAAahC,EAAA,CAAAiC,WAAA,CAAA8I,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAA7B;IAA+BhL,EAAA,CAAAG,YAAA,EAAgB;IAE9HH,EAAA,CAAAC,cAAA,gBAA6F;IAEoDD,EAAA,CAAAE,MAAA,KAAwD;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpOH,EAAA,CAAAC,cAAA,gBAAqC;IACoDD,EAAA,CAAA0B,UAAA,qBAAAuJ,iEAAArJ,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA6E,OAAA,GAAAlL,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAiJ,OAAA,CAAA3D,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAuJ,+DAAAvJ,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA+E,OAAA,GAAApL,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAmJ,OAAA,CAAA1D,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAnH5B,EAAA,CAAAG,YAAA,EAAwN;IAE5NH,EAAA,CAAAC,cAAA,gBAAqG;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5GH,EAAA,CAAAC,cAAA,gBAAqC;IACkDD,EAAA,CAAA0B,UAAA,qBAAA2J,iEAAAzJ,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAiF,OAAA,GAAAtL,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAqJ,OAAA,CAAA/D,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAA2J,+DAAA3J,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAmF,OAAA,GAAAxL,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAuJ,OAAA,CAAA9D,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAjH5B,EAAA,CAAAG,YAAA,EAAsN;IAE1NH,EAAA,CAAAC,cAAA,gBAA6F;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG3GH,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA2D,SAAA,gBAAgG;IAChG3D,EAAA,CAAAC,cAAA,gBAAkC;IAC9BD,EAAA,CAAA4D,UAAA,MAAA6H,6CAAA,kBAEM;IACNzL,EAAA,CAAA4D,UAAA,MAAA8H,6CAAA,kBAEM;IACN1L,EAAA,CAAA4D,UAAA,MAAA+H,6CAAA,kBAEM;IAIV3L,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAqD;IACjDD,EAAA,CAAA4D,UAAA,MAAAgI,6CAAA,kBAEM;IACN5L,EAAA,CAAA4D,UAAA,MAAAiI,6CAAA,kBAEM;IACN7L,EAAA,CAAA4D,UAAA,MAAAkI,6CAAA,kBAEM;IAIV9L,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA6E;IAC2DD,EAAA,CAAAE,MAAA,KAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtMH,EAAA,CAAAC,cAAA,gBAAqC;IAC0CD,EAAA,CAAA0B,UAAA,qBAAAqK,iEAAAnK,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA2F,OAAA,GAAAhM,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAA+J,OAAA,CAAAzE,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAqK,+DAAArK,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA6F,OAAA,GAAAlM,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAiK,OAAA,CAAAxE,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAzG5B,EAAA,CAAAG,YAAA,EAAiN;IAErNH,EAAA,CAAAC,cAAA,gBAAqG;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5GH,EAAA,CAAAC,cAAA,gBAAqC;IACkDD,EAAA,CAAA0B,UAAA,qBAAAyK,iEAAAvK,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA+F,OAAA,GAAApM,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAmK,OAAA,CAAA7E,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAyK,+DAAAzK,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAiG,OAAA,GAAAtM,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAqK,OAAA,CAAA5E,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAjH5B,EAAA,CAAAG,YAAA,EAAyN;IAE7NH,EAAA,CAAAC,cAAA,gBAA6F;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG7GH,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA2D,SAAA,gBAAgG;IAChG3D,EAAA,CAAAC,cAAA,gBAAkC;IAC9BD,EAAA,CAAA4D,UAAA,MAAA2I,6CAAA,kBAEM;IACNvM,EAAA,CAAA4D,UAAA,MAAA4I,6CAAA,kBAEM;IACVxM,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAqD;IACjDD,EAAA,CAAA4D,UAAA,MAAA6I,6CAAA,kBAEM;IACNzM,EAAA,CAAA4D,UAAA,MAAA8I,6CAAA,kBAEM;IACV1M,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAsE;IACqED,EAAA,CAAAE,MAAA,KAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5MH,EAAA,CAAAC,cAAA,gBAAqC;IACmED,EAAA,CAAA0B,UAAA,qBAAAiL,iEAAA/K,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAuG,OAAA,GAAA5M,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAA2K,OAAA,CAAArF,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAiL,+DAAAjL,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAyG,OAAA,GAAA9M,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAA6K,OAAA,CAAApF,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAAlI5B,EAAA,CAAAG,YAAA,EAA4O;IAEhPH,EAAA,CAAA2D,SAAA,gBAA2G;IAI/G3D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA2D,SAAA,gBAA8F;IAC9F3D,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA4D,UAAA,MAAAmJ,6CAAA,kBAEM;IACN/M,EAAA,CAAA4D,UAAA,MAAAoJ,6CAAA,kBAEM;IACVhN,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAsE;IACsED,EAAA,CAAAE,MAAA,KAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9MH,EAAA,CAAAC,cAAA,gBAAqC;IACqED,EAAA,CAAA0B,UAAA,qBAAAuL,iEAAArL,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA6G,OAAA,GAAAlN,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAiL,OAAA,CAAA3F,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAAuL,+DAAAvL,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAA+G,OAAA,GAAApN,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAmL,OAAA,CAAA1F,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAApI5B,EAAA,CAAAG,YAAA,EAA+O;IAEnPH,EAAA,CAAA2D,SAAA,gBAA2G;IAI/G3D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA2D,SAAA,gBAA8F;IAC9F3D,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA4D,UAAA,MAAAyJ,6CAAA,kBAEM;IACNrN,EAAA,CAAA4D,UAAA,MAAA0J,6CAAA,kBAEM;IACVtN,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAA4E;IAC6DD,EAAA,CAAAE,MAAA,KAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjMH,EAAA,CAAAC,cAAA,gBAAqC;IAC+DD,EAAA,CAAA0B,UAAA,qBAAA6L,iEAAA3L,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAmH,OAAA,GAAAxN,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAuL,OAAA,CAAAjG,UAAA,CAAA3F,MAAA,CAAkB;IAAA,EAAC,mBAAA6L,+DAAA7L,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwE,IAAA;MAAA,MAAAqH,OAAA,GAAA1N,EAAA,CAAAgC,aAAA;MAAA,OAAWhC,EAAA,CAAAiC,WAAA,CAAAyL,OAAA,CAAAhG,eAAA,CAAA9F,MAAA,CAAuB;IAAA,EAAlC;IAA9H5B,EAAA,CAAAG,YAAA,EAA+N;IAEnOH,EAAA,CAAA2D,SAAA,gBAA2G;IAI/G3D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA2D,SAAA,gBAA8F;IAC9F3D,EAAA,CAAAC,cAAA,gBAAsD;IAClDD,EAAA,CAAA4D,UAAA,MAAA+J,6CAAA,kBAEM;IACN3N,EAAA,CAAA4D,UAAA,MAAAgK,6CAAA,kBAEM;IACV5N,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,gBAA4D;IACjCD,EAAA,CAAA2D,SAAA,oBAA4I;IAAA3D,EAAA,CAAAG,YAAA,EAAI;IACvKH,EAAA,CAAA2D,SAAA,oBAA6L;IACjM3D,EAAA,CAAAG,YAAA,EAAM;;;;IAjgBMH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAC,cAAA,CAA4B;IAI0D9N,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,8BAAsD;IAErFR,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAmC,UAAA,aAAA0L,MAAA,CAAAE,MAAA,CAAiB,gBAAAF,MAAA,CAAAtK,WAAA,CAAAyK,QAAA;IAO9DhO,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAI,eAAA,IAAAJ,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,aAAwE;IAGxEnO,EAAA,CAAAI,SAAA,GAAyE;IAAzEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAI,eAAA,IAAAJ,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,cAAyE;IAGzEnO,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAI,eAAA,IAAAJ,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,sBAAiF;IAGjFnO,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAI,eAAA,IAAAJ,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,WAAsE;IAOMnO,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,8BAAsD;IAEzER,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAA6K,QAAA,CAAoC;IAO7FpO,EAAA,CAAAI,SAAA,GAAyE;IAAzEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAQ,gBAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,aAAyE;IAGzEnO,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAQ,gBAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,cAA0E;IAG1EnO,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAQ,gBAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,sBAAkF;IAGlFnO,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAQ,gBAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAI,GAAA,SAAAC,QAAA,WAAuE;IAOanO,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,kCAA0D;IAEjFR,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAmC,UAAA,aAAA0L,MAAA,CAAAE,MAAA,CAAiB,gBAAAF,MAAA,CAAAtK,WAAA,CAAA+K,YAAA;IAO9EtO,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAU,mBAAA,IAAAV,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,aAAoF;IAGpFnO,EAAA,CAAAI,SAAA,GAAqF;IAArFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAU,mBAAA,IAAAV,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,cAAqF;IAGrFnO,EAAA,CAAAI,SAAA,GAA6F;IAA7FJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAU,mBAAA,IAAAV,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,sBAA6F;IAOTnO,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,kCAA0D;IAEzGR,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAmC,UAAA,YAAA0L,MAAA,CAAAW,aAAA,CAAyB,gBAAAX,MAAA,CAAAtK,WAAA,CAAAkL,YAAA;IAO9DzO,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAa,mBAAA,IAAAb,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,aAAoF;IAODnO,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,iCAAyD;IAEtER,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAAoL,WAAA,CAAuC;IAO7G3O,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAe,kBAAA,IAAAf,MAAA,CAAAC,cAAA,CAAAI,GAAA,gBAAAC,QAAA,cAAmF;IAQInO,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,qCAA6D;IAEDR,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAAsL,eAAA,CAA2C;IAU9L7O,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAiB,sBAAA,IAAAjB,MAAA,CAAAC,cAAA,CAAAI,GAAA,oBAAAC,QAAA,aAA0F;IAG1FnO,EAAA,CAAAI,SAAA,GAAqF;IAArFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAiB,sBAAA,IAAAjB,MAAA,CAAAC,cAAA,CAAAI,GAAA,oBAAAC,QAAA,QAAqF;IAGrFnO,EAAA,CAAAI,SAAA,GAAqF;IAArFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAiB,sBAAA,IAAAjB,MAAA,CAAAC,cAAA,CAAAI,GAAA,oBAAAC,QAAA,QAAqF;IAQjEnO,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAmC,UAAA,YAAA0L,MAAA,CAAAkB,cAAA,CAAiB;IAMqE/O,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,gCAAwD;IASrFR,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,2BAAmD;IAE1FR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmC,UAAA,YAAA0L,MAAA,CAAAmB,MAAA,CAAkB,gBAAAnB,MAAA,CAAAtK,WAAA,CAAA0L,SAAA;IASxDjP,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAqB,gBAAA,IAAArB,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,aAAkF;IAOGnO,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,8BAAsD;IAEIR,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAA4L,QAAA,CAAoC;IAEvLnP,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAkB,KAAA,QAAsD;IAGtDpP,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAkB,KAAA,QAAsD;IAGtDpP,EAAA,CAAAI,SAAA,GAA0G;IAA1GJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAkB,KAAA,WAAAvB,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAkB,KAAA,QAA0G;IAQtGpP,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,aAAiF;IAGjFnO,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAA4E;IAG5EnO,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAA4E;IAOOnO,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,iCAAyD;IAElER,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,YAAA0L,MAAA,CAAAyB,YAAA,CAAwB,gBAAAzB,MAAA,CAAAtK,WAAA,CAAAgM,SAAA;IASlGvP,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA2B,gBAAA,IAAA3B,MAAA,CAAAC,cAAA,CAAAI,GAAA,gBAAAC,QAAA,aAAgF;IAMxFnO,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA4B,UAAA,CAAgB;IAsBRzP,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA6B,mBAAA,IAAA7B,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,aAAoF;IAM5FnO,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA8B,UAAA,CAAgB;IAsBb3P,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,0BAAkD;IAIyCR,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,8BAAsD;IAEKR,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAAqM,QAAA,CAAoC;IAUrL5P,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAgC,eAAA,IAAAhC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,aAAkF;IAGlFnO,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAgC,eAAA,IAAAhC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,QAA6E;IAG7EnO,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAgC,eAAA,IAAAhC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,QAA6E;IAOSnO,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,mCAA2D;IAEdR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAAuM,OAAA,CAAmC;IAUtK9P,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAkC,cAAA,IAAAlC,MAAA,CAAAC,cAAA,CAAAI,GAAA,YAAAC,QAAA,aAA0E;IAG1EnO,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAkC,cAAA,IAAAlC,MAAA,CAAAC,cAAA,CAAAI,GAAA,YAAAC,QAAA,QAAqE;IAGrEnO,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAkC,cAAA,IAAAlC,MAAA,CAAAC,cAAA,CAAAI,GAAA,YAAAC,QAAA,QAAqE;IASWnO,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,mCAA2D;IAEKR,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAAyM,aAAA,CAAyC;IAOzLhQ,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAoC,yBAAA,IAAApC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,QAAuF;IAGvFnO,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAoC,yBAAA,IAAApC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,QAAuF;IAMDnO,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,oCAA4D;IAEAR,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAmC,UAAA,gBAAA0L,MAAA,CAAAtK,WAAA,CAAA2M,cAAA,CAA0C;IAQ5LlQ,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAsC,0BAAA,IAAAtC,MAAA,CAAAC,cAAA,CAAAI,GAAA,oBAAAC,QAAA,QAAyF;IAGzFnO,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAsC,0BAAA,IAAAtC,MAAA,CAAAC,cAAA,CAAAI,GAAA,oBAAAC,QAAA,QAAyF;IAQ3FnO,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,8BAAsD;IACnBR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,YAAA0L,MAAA,CAAAhD,UAAA,CAAwB;IAIvC7K,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAA2D7K,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,gCAAwD;IAEtCR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAA8M,UAAA;IAEtJrQ,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAE8E7K,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAA8M,UAAA;IAE5KrQ,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAMzC7K,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAyC,2BAAA,IAAAzC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,aAA8F;IAG9FnO,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAyC,2BAAA,IAAAzC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,QAAyF;IAGzFnO,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAyC,2BAAA,IAAAzC,MAAA,CAAAC,cAAA,CAAAI,GAAA,mBAAAC,QAAA,QAAyF;IAQzFnO,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA0C,4BAAA,IAAA1C,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,aAA8F;IAG9FnO,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA0C,4BAAA,IAAA1C,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAAyF;IAGzFnO,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA0C,4BAAA,IAAA1C,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAAyF;IAUxEnO,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAA2D7K,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,kCAA0D;IAE7CR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAAiN,aAAA;IAE5IxQ,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAE8E7K,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAAiN,aAAA;IAE5KxQ,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAMzC7K,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA4C,6BAAA,IAAA5C,MAAA,CAAAC,cAAA,CAAAI,GAAA,cAAAC,QAAA,QAAsF;IAGtFnO,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA4C,6BAAA,IAAA5C,MAAA,CAAAC,cAAA,CAAAI,GAAA,cAAAC,QAAA,QAAsF;IAKtFnO,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA6C,8BAAA,IAAA7C,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAA2F;IAG3FnO,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA6C,8BAAA,IAAA7C,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAA2F;IAOvEnO,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAA2D7K,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,qCAA6D;IAE1BR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAAoN,eAAA;IAErK3Q,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAGtE7K,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAMzC7K,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA+C,sBAAA,IAAA/C,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,QAAkF;IAGlFnO,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAA+C,sBAAA,IAAA/C,MAAA,CAAAC,cAAA,CAAAI,GAAA,iBAAAC,QAAA,QAAkF;IAO7DnO,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAA2D7K,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,sCAA8D;IAE1BR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAAsN,gBAAA;IAEvK7Q,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAGtE7K,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAMzC7K,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAiD,uBAAA,IAAAjD,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAAoF;IAGpFnO,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAiD,uBAAA,IAAAjD,MAAA,CAAAC,cAAA,CAAAI,GAAA,kBAAAC,QAAA,QAAoF;IAOlEnO,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAA2D7K,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAuC,iBAAA,CAAAsL,MAAA,CAAAtN,WAAA,CAAAC,SAAA,4BAAoD;IAEnBR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,cAAA0L,MAAA,CAAAhD,UAAA,CAAwB,gBAAAgD,MAAA,CAAAtK,WAAA,CAAAwN,MAAA;IAEjK/Q,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAGtE7K,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAoQ,WAAA,WAAAvC,MAAA,CAAAhD,UAAA,oBAA8C;IAMzC7K,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAmD,aAAA,IAAAnD,MAAA,CAAAC,cAAA,CAAAI,GAAA,eAAAC,QAAA,QAAuE;IAGvEnO,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAmC,UAAA,SAAA0L,MAAA,CAAAmD,aAAA,IAAAnD,MAAA,CAAAC,cAAA,CAAAI,GAAA,eAAAC,QAAA,QAAuE;IAO9CnO,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAmC,UAAA,UAAA0L,MAAA,CAAAtN,WAAA,CAAAC,SAAA,yBAAuD;IAChER,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAmC,UAAA,UAAA0L,MAAA,CAAAtN,WAAA,CAAAC,SAAA,uBAAqD,aAAAqN,MAAA,CAAAoD,iBAAA,IAAApD,MAAA,CAAAqD,iBAAA,IAAArD,MAAA,CAAAC,cAAA,CAAAqD,OAAA;;;;;;IAmC3EnR,EAAA,CAAAC,cAAA,eAAkE;IAG/CD,EAAA,CAAA0B,UAAA,2BAAA0P,4EAAAxP,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAwP,IAAA;MAAA,MAAAC,OAAA,GAAAtR,EAAA,CAAAgC,aAAA;MAAA,OAAahC,EAAA,CAAAiC,WAAA,CAAAqP,OAAA,CAAAC,cAAA,CAAA/N,YAAA,GAAA5B,MAAA,CAC/C;IAAA,EAD2E;IAKvD5B,EAAA,CAAAG,YAAA,EAAgB;IACjBH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IARrDH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmC,UAAA,mBAAkB,uCAAAqP,MAAA,CAAAD,cAAA,CAAA/N,YAAA,aAAAgO,MAAA,CAAAC,YAAA;IAQ3BzR,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAiP,MAAA,CAAAjR,WAAA,CAAAC,SAAA,8BAAsD;;;;;IAEpFR,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5EH,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAA0R,kBAAA,KAAAC,MAAA,CAAApR,WAAA,CAAAC,SAAA,kCAAAmR,MAAA,CAAAjO,YAAA,KAAqE;;;;;;;;;;;AD1hBrI,OAAM,MAAOkO,mBAAoB,SAAQjS,aAAa;EAmJlDkS,YAA8CC,iBAAoC,EACtCC,cAA8B,EAASC,WAAwB,EAAEC,QAAkB;IAC3H,KAAK,CAACA,QAAQ,CAAC;IAF2B,KAAAH,iBAAiB,GAAjBA,iBAAiB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IAAyB,KAAAC,WAAW,GAAXA,WAAW;IAlJ9F,KAAAE,IAAI,GAAa;MAACC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAC;IACtD,KAAAC,KAAK,GAAe,CAChB;MAACC,KAAK,EAAE,IAAI,CAAC/R,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAAE4R,UAAU,EAAE;IAAQ,CAAC,EACvF;MAACE,KAAK,EAAE,IAAI,CAAC/R,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MAAE4R,UAAU,EAAE;IAAQ,CAAC,EACjF;MAACE,KAAK,EAAE,IAAI,CAAC/R,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAC,CAC5D;IACD,KAAAyQ,iBAAiB,GAAY,KAAK;IAClC,KAAAC,iBAAiB,GAAY,KAAK;IAMlC,KAAA1C,aAAa,GAAgC,CACzC;MACI+D,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,kCAAkC;KACtE,EACD;MACI+R,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,oCAAoC;KACxE,EACD;MACI+R,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,gCAAgC;;IAErE;IACA;IACA;IACA;IACA;IAAA,CACH;;IACD,KAAA8O,YAAY,GAAgC,CACxC;MACIiD,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,mCAAmC;KACvE,EACD;MACI+R,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,iCAAiC;KACrE,EACD;MACI+R,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,iCAAiC;KACrE,CACJ;IACD,KAAAyD,QAAQ,GAAG,IAAI,CAACuO,cAAc,CAACC,QAAQ,CAACC,IAAI;IAC5C,KAAAxO,WAAW,GAAGrE,SAAS,CAAC8S,SAAS;IACjC,KAAAjP,YAAY,GAAW,EAAE;IAGzB,KAAAuK,eAAe,GAAY,KAAK;IAChC,KAAAI,gBAAgB,GAAY,KAAK;IACjC,KAAAE,mBAAmB,GAAY,KAAK;IACpC,KAAAG,mBAAmB,GAAY,KAAK;IACpC,KAAAI,sBAAsB,GAAY,KAAK;IACvC,KAAA8D,uBAAuB,GAAY,KAAK,CAAC,CAAC;IAC1C,KAAApD,gBAAgB,GAAY,KAAK;IACjC,KAAAE,mBAAmB,GAAY,KAAK;IACpC,KAAAR,gBAAgB,GAAY,KAAK;IACjC,KAAAG,eAAe,GAAY,KAAK;IAChC,KAAAT,kBAAkB,GAAY,KAAK;IACnC,KAAAiB,eAAe,GAAY,KAAK;IAChC,KAAAI,yBAAyB,GAAY,KAAK;IAC1C,KAAAE,0BAA0B,GAAY,KAAK;IAC3C,KAAAG,2BAA2B,GAAY,KAAK;IAC5C,KAAAC,4BAA4B,GAAY,KAAK;IAC7C,KAAAE,6BAA6B,GAAY,KAAK;IAC9C,KAAAC,8BAA8B,GAAY,KAAK;IAC/C,KAAAE,sBAAsB,GAAY,KAAK;IACvC,KAAAE,uBAAuB,GAAY,KAAK;IACxC,KAAAE,aAAa,GAAY,KAAK;IAC9B,KAAAjB,cAAc,GAAY,KAAK;IAyB/B,KAAA8C,kBAAkB,GAAqB,IAAI9S,gBAAgB,EAAE;IAE7D,KAAA0P,UAAU,GAAG,KAAK;IAClB,KAAAE,UAAU,GAAG,KAAK;IAClB,KAAA5B,MAAM,GAAY,KAAK;IAEvB,KAAA+E,8BAA8B,GAAY,KAAK;IAQ/C,KAAAC,eAAe,GAAgE,EAAE;IACjF,KAAAC,kBAAkB,GAAgE,CAAC;MAACT,EAAE,EAAE,CAAC,CAAC;MAAE/O,YAAY,EAAE;IAAE,CAAC,CAAC;IAY9G,KAAAwL,MAAM,GAAgC,CAClC;MACIuD,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,sBAAsB;KAC1D,EACD;MACI+R,EAAE,EAAE,CAAC;MACL/P,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,wBAAwB;KAC5D,CACJ;IACD,KAAAqK,UAAU,GAAY,KAAK;IAC3B,KAAAoI,QAAQ,GAAY,KAAK;IAEzB,KAAAlE,cAAc,GAAU,CACpB;MAACvM,IAAI,EAAE,WAAW;MAAEH,GAAG,EAAE;IAAG,CAAC,EAC7B;MAACG,IAAI,EAAE,SAAS;MAAEH,GAAG,EAAE;IAAG,CAAC,CAC9B;IACD,KAAAH,oBAAoB,GAAQ,IAAI;IAEhC,KAAAgR,sBAAsB,GAAW,IAAI,CAAC3S,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;IAO/F,KAAA+C,WAAW,GAAG;MACVyK,QAAQ,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACvE4N,QAAQ,EAAE,IAAI,CAAC7N,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACxE8N,YAAY,EAAE,IAAI,CAAC/N,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAC/EiO,YAAY,EAAE,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAC/EmO,WAAW,EAAE,IAAI,CAACpO,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;MAC7EqO,eAAe,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC;MACrF2S,gBAAgB,EAAE,IAAI,CAAC5S,WAAW,CAACC,SAAS,CAAC,yCAAyC,CAAC;MACvF+O,SAAS,EAAE,IAAI,CAAChP,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACzEgD,YAAY,EAAE,IAAI,CAACjD,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAC/EyO,SAAS,EAAE,IAAI,CAAC1O,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACzE2O,QAAQ,EAAE,IAAI,CAAC5O,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACvEoP,QAAQ,EAAE,IAAI,CAACrP,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACvEwP,aAAa,EAAE,IAAI,CAACzP,WAAW,CAACC,SAAS,CAAC,sCAAsC,CAAC;MACjF0P,cAAc,EAAE,IAAI,CAAC3P,WAAW,CAACC,SAAS,CAAC,uCAAuC,CAAC;MACnF6P,UAAU,EAAE,IAAI,CAAC9P,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;MAC3EgQ,aAAa,EAAE,IAAI,CAACjQ,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAChFmQ,eAAe,EAAE,IAAI,CAACpQ,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC;MACrFqQ,gBAAgB,EAAE,IAAI,CAACtQ,WAAW,CAACC,SAAS,CAAC,yCAAyC,CAAC;MACvFuQ,MAAM,EAAE,IAAI,CAACxQ,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MACnEsP,OAAO,EAAE,IAAI,CAACvP,WAAW,CAACC,SAAS,CAAC,gCAAgC;KACvE;EAvBD;EAyBA4S,gBAAgBA,CAACC,KAAS;IACtB,OAAO,IAAIjU,UAAU,CAACkU,QAAQ,IAAG;MAC7B,IAAI,CAACxB,iBAAiB,CAACyB,uBAAuB,CAACF,KAAK,EAAGG,QAAQ,IAAI;QAC/DF,QAAQ,CAACG,IAAI,CAACD,QAAQ,CAAC;QACvBF,QAAQ,CAACI,QAAQ,EAAE;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAC,gBAAgBA,CAACN,KAAS;IACtB,OAAO,IAAIjU,UAAU,CAACkU,QAAQ,IAAG;MAC7B,IAAI,CAACxB,iBAAiB,CAAC8B,uBAAuB,CAACP,KAAK,EAAGG,QAAQ,IAAI;QAC/DF,QAAQ,CAACG,IAAI,CAACD,QAAQ,CAAC;QACvBF,QAAQ,CAACI,QAAQ,EAAE;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAG,iBAAiBA,CAAA;IACb,OAAQC,OAAwB,IAAyC;MACrE,IAAI,CAACA,OAAO,CAACC,YAAY,IAAID,OAAO,CAACE,QAAQ,EAAE;QAC3C,OAAOxU,EAAE,CAAC,IAAI,CAAC;OAClB,MACG,OAAOsU,OAAO,CAACC,YAAY,CAACE,IAAI,CAC5B5U,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,SAAS,CAAC2P,KAAK,IAAG;QACd,IAAI,CAAC8E,oBAAoB,GAAG9E,KAAK;QACjC,OAAO,IAAI,CAACgE,gBAAgB,CAAC;UAACe,IAAI,EAAE/E;QAAK,CAAC,CAAC;MAC/C,CAAC,CAAC,EAAE1P,IAAI,CAAC,CAAC,CAAC,EACXH,GAAG,CAAC6U,MAAM,IAAG;QACT,IAAIA,MAAM,KAAK,CAAC,IAAI,IAAI,CAACF,oBAAoB,IAAI,IAAI,CAACG,WAAW,CAACF,IAAI,EAAE;UACpE,IAAI,CAAClD,iBAAiB,GAAG,KAAK;UAC9B,OAAO,IAAI;SACd,MAAM;UACH,IAAI,CAACA,iBAAiB,GAAG,IAAI;UAC7B,OAAO;YAAC,QAAQ,EAAE;UAAI,CAAC;;MAE/B,CAAC,CAAC,CACL;IACT,CAAC;EACL;EAEAqD,iBAAiBA,CAAA;IACb,OAAQR,OAAwB,IAAyC;MACrE,IAAI,CAACA,OAAO,CAACC,YAAY,IAAID,OAAO,CAACE,QAAQ,EAAE;QAC3C,OAAOxU,EAAE,CAAC,IAAI,CAAC;OAClB,MACG,OAAOsU,OAAO,CAACC,YAAY,CAACE,IAAI,CAC5B5U,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,SAAS,CAAC2P,KAAK,IAAG;QACd,IAAI,CAACmF,oBAAoB,GAAGnF,KAAK;QACjC,OAAO,IAAI,CAACuE,gBAAgB,CAAC;UAACnR,IAAI,EAAE4M;QAAK,CAAC,CAAC;MAC/C,CAAC,CAAC,EACF1P,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAAC6U,MAAM,IAAG;QACT,IAAIA,MAAM,KAAK,CAAC,IAAI,IAAI,CAACG,oBAAoB,IAAI,IAAI,CAACF,WAAW,CAAC7R,IAAI,EAAE;UACpE,IAAI,CAAC0O,iBAAiB,GAAG,KAAK;UAC9B,OAAO,IAAI;SACd,MAAM;UACH,IAAI,CAACA,iBAAiB,GAAG,IAAI;UAC7B,OAAO;YAAC,QAAQ,EAAE;UAAI,CAAC;;MAE/B,CAAC,CAAC,CACL;IACT,CAAC;EACL;EAEAsD,4BAA4BA,CAAA;IACxB,OAAQV,OAAwB,IAA6B;MACzD,MAAM1E,KAAK,GAAG0E,OAAO,CAAC1E,KAAK;MAC3B,MAAMqF,OAAO,GAAG,mBAAmB,CAACC,IAAI,CAACtF,KAAK,CAAC;MAC/C,OAAOqF,OAAO,GAAG,IAAI,GAAG;QAAC,mBAAmB,EAAE;UAACrF;QAAK;MAAC,CAAC;IAC1D,CAAC;EACL;EAEAuF,4BAA4BA,CAAA;IACxB,OAAQb,OAAwB,IAA6B;MACzD,MAAM1E,KAAK,GAAG0E,OAAO,CAAC1E,KAAK;MAC3B,IAAIA,KAAK,IAAI,EAAE,EAAE;QACb,OAAO,IAAI;;MAEf,MAAMqF,OAAO,GAAG,gFAAgF,CAACC,IAAI,CAACtF,KAAK,CAAC;MAC5G,OAAOqF,OAAO,GAAG,IAAI,GAAG;QAAC,mBAAmB,EAAE;UAACrF;QAAK;MAAC,CAAC;IAC1D,CAAC;EACL;EAEA7H,UAAUA,CAACqN,KAAoB;IAC3B,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/D,IAAIA,YAAY,CAACC,QAAQ,CAACF,KAAK,CAACvS,GAAG,CAAC,EAAE;MAClCuS,KAAK,CAACG,cAAc,EAAE;;IAE1B;IACA;IACA;EACJ;;EAEArN,eAAeA,CAACkN,KAAiB;IAC7B,MAAMI,KAAK,GAAGJ,KAAK,CAACK,MAA0B;IAC9CD,KAAK,CAAC5F,KAAK,GAAG4F,KAAK,CAAC5F,KAAK,CAAC8F,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;EACtD;;EAEAtM,gBAAgBA,CAACgM,KAAK;IAClB,IAAGA,KAAK,CAACxF,KAAK,IAAE,CAAC,EAAC;MACd,IAAI,CAACK,UAAU,GAAG,KAAK;MACvB,IAAI,CAACE,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC7B,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACiH,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACrE;KACH,MAAK,IAAIR,KAAK,CAACxF,KAAK,IAAI,CAAC,EAAC;MACvB;MACA,IAAI,CAACK,UAAU,GAAG,IAAI;MACtB,IAAI,CAACE,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC7B,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACmH,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE,IAAI,CAAChS,cAAc,EAAE;MACrB;KACH,MAAK,IAAIwR,KAAK,CAACxF,KAAK,IAAI,CAAC,EAAC;MACvB;MACA,IAAI,CAACK,UAAU,GAAG,IAAI;MACtB,IAAI,CAACE,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC7B,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACmH,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE,IAAI,CAAChS,cAAc,EAAE;MACrB;;EAER;;EAEA4H,cAAcA,CAAA;IACV,IAAI,IAAI,CAACH,UAAU,EAAE;MACjB,IAAI,CAACiD,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;MACpE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;MACnE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,WAAW,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;MAC/D,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;MACnE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;MAClE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;MACnE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,YAAY,CAAC,CAACmH,MAAM,CAAC;QAACD,SAAS,EAAE;MAAK,CAAC,CAAC;KACnE,MAAM;MACH,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;MACrE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;MACpE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,WAAW,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;MAChE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;MACpE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;MACnE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;MACpE,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,YAAY,CAAC,CAACiH,OAAO,CAAC;QAACC,SAAS,EAAE;MAAK,CAAC,CAAC;;EAEzE;EAEA7O,UAAUA,CAAA;IACN,IAAI,CAAC+O,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,IAAI,CAAC1H,cAAc,CAAC2H,KAAK,EAAE;MAC3B,IAAIC,IAAI,GAAG;QAAC,GAAG,IAAI,CAAC5H,cAAc,CAACsB;MAAK,CAAC;MACzC,IAAIsG,IAAI,CAACC,MAAM,EAAE;QACbD,IAAI,CAACC,MAAM,GAAG,CAAC;OAClB,MAAM;QACHD,IAAI,CAACC,MAAM,GAAG,CAAC;;MAEnB,IAAID,IAAI,CAACE,QAAQ,EAAE;QACfF,IAAI,CAACE,QAAQ,GAAG,CAAC;QACjBF,IAAI,CAACG,WAAW,GAAGH,IAAI,CAACI,SAAS,GAAGJ,IAAI,CAAClF,aAAa;OACzD,MAAM;QACHkF,IAAI,CAACE,QAAQ,GAAG,CAAC;;MAErB,IAAI,IAAI,CAAC7C,eAAe,CAACgD,MAAM,GAAG,CAAC,EAAC;QAChC,IAAIC,gBAAgB,GAAGR,EAAE,CAAC1H,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACkB,KAAK;QAClE,IAAI6G,eAAe,GAAGT,EAAE,CAACzC,eAAe,CAACmD,MAAM,CAAEC,EAAE,IAAKH,gBAAgB,CAAClB,QAAQ,CAACqB,EAAE,CAAC3S,YAAY,CAAC,CAAC,CAACjE,GAAG,CAAC6W,CAAC,IAAIA,CAAC,CAAC7D,EAAE,CAAC;QAClHmD,IAAI,CAACW,OAAO,GAAGJ,eAAe;;MAElC,IAAI,CAACnE,iBAAiB,CAACwE,cAAc,CAAC,IAAI,CAACC,SAAS,EAAEb,IAAI,EAAGlC,QAAQ,IAAI;QACrE;QACA,IAAI,CAAC8B,oBAAoB,CAACkB,OAAO,CAAC,IAAI,CAACjW,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC3F,IAAI,CAACiW,MAAM,CAACC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MACpC,CAAC,EAAE,IAAI,EAAE,MAAK;QACVlB,EAAE,CAACF,oBAAoB,CAACqB,OAAO,EAAE;MACrC,CAAC,CAAC;;EAEV;EAEAC,iBAAiBA,CAAA;IACb,IAAI,CAAC9I,cAAc,CAACI,GAAG,CAAC,SAAS,CAAC,CAAC2I,QAAQ,CAAC,IAAI,CAACC,YAAY,CAAC;EAClE;EAEAC,QAAQA,CAAA;IACJ,IAAIvB,EAAE,GAAG,IAAI;IACb,IAAI,CAAC,IAAI,CAACwB,WAAW,CAAC,CAACnX,SAAS,CAACoX,WAAW,CAACC,WAAW,CAACC,MAAM,CAAC,CAAC,EAAE;MAC/DC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;;IAEpC,IAAI,CAACf,SAAS,GAAGgB,MAAM,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEzD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,6BAA6B,GAAG;MACjCC,gBAAgB,EAAE,KAAK;MACvBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACC,iBAAiB,GAAG;MACrBC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC3G,cAAc,GAAG;MAClB4G,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACX7U,YAAY,EAAE;KACjB;IACD,IAAI,CAAC8U,cAAc,GAAG,IAAI,CAACtG,WAAW,CAACuG,KAAK,CAAC,IAAI,CAAChH,cAAc,CAAC;IACjE,IAAI,CAACiH,eAAe,GAAG,CACnB;MACIhW,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D6B,GAAG,EAAE,UAAU;MACfoW,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIpW,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D6B,GAAG,EAAE,UAAU;MACfoW,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIpW,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D6B,GAAG,EAAE,OAAO;MACZoW,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIpW,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D6B,GAAG,EAAE,cAAc;MACnBoW,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAC7G,cAAc,CAAC8G,eAAe,CAAEnD,IAAI,IAAI;MACzC,IAAI,CAACpS,SAAS,GAAGoS,IAAI,CAACnW,GAAG,CAAC4W,EAAE,IAAG;QAC3B,OAAO;UACHhC,IAAI,EAAEgC,EAAE,CAAChC,IAAI;UACb3R,IAAI,EAAE,GAAG2T,EAAE,CAAC3T,IAAI,KAAK2T,EAAE,CAAChC,IAAI;SAC/B;MACL,CAAC,CAAC;IACN,CAAC,CAAC;IAEF;IACA;IAEA,IAAI,CAACrC,iBAAiB,CAACgH,OAAO,CAAC,IAAI,CAACvC,SAAS,EAAG/C,QAAQ,IAAI;MACxDgC,EAAE,CAACnB,WAAW,GAAGb,QAAQ;MACzB,IAAI,CAAC1F,cAAc,GAAG,IAAI,CAACiL,aAAa,EAAE;MAC1C,IAAI,CAACjL,cAAc,CAACkL,UAAU,CAACxF,QAAQ,CAAC;MACxCgC,EAAE,CAACsB,YAAY,GAAGtD,QAAQ,CAAC6C,OAAO;MAClC,IAAI,CAAC4C,aAAa,EAAE;MAEpB,IAAI,CAAC/W,oBAAoB,GAAGsR,QAAQ,CAAC0F,QAAQ;MAE7C,IAAI1F,QAAQ,CAACoC,QAAQ,IAAI,CAAC,EAAE;QACxB,IAAI,CAAC/K,UAAU,GAAG,IAAI;QACtB,IAAI,CAACG,cAAc,EAAE;;MAEzB,IAAIwI,QAAQ,CAAC2F,WAAW,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC1J,UAAU,GAAG,IAAI;QACtB,IAAI,CAACE,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC7B,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACmH,MAAM,CAAC;UAACD,SAAS,EAAE;QAAK,CAAC,CAAC;QAClE;QACA,IAAI,CAACtH,cAAc,CAACI,GAAG,CAAC,SAAS,CAAC,CAAC2I,QAAQ,CAACrD,QAAQ,CAAC6C,OAAO,CAAC;QAC7D,IAAI,CAAC9E,cAAc,CAAC/N,YAAY,GAAGgQ,QAAQ,CAAChQ,YAAY;QAExDgS,EAAE,CAACzD,cAAc,CAACqH,2BAA2B,CAAC;UAACC,YAAY,EAAE7F,QAAQ,CAACjB;QAAE,CAAC,EAAG+G,IAAI,IAAI;UAChF,IAAI,CAACvG,eAAe,GAAGuG,IAAI;UAC3B,IAAI,CAACtG,kBAAkB,GAAG,IAAI,CAACD,eAAe;QAClD,CAAC,CAAC;OAGL,MAAM,IAAIS,QAAQ,CAAC2F,WAAW,IAAI,CAAC,EAAE;QAClC,IAAI,CAAC1J,UAAU,GAAG,IAAI;QACtB,IAAI,CAACE,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC7B,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACmH,MAAM,CAAC;UAACD,SAAS,EAAE;QAAK,CAAC,CAAC;QAClE;;;MAEJ,IAAI5B,QAAQ,CAACmC,MAAM,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC1C,QAAQ,GAAG,IAAI;;MAExB,IAAIO,QAAQ,CAAC+F,MAAM,IAAI,CAAC,IAAI/F,QAAQ,CAAC+F,MAAM,IAAI,CAAC,EAAE;QAC9C/D,EAAE,CAACzH,MAAM,GAAG,IAAI;QAChB;QACA;QAEA;QACA;;;MAEJ,IAAI,CAACD,cAAc,CAAC0L,sBAAsB,EAAE;MAC5ChE,EAAE,CAAClS,SAAS,CAACmW,OAAO,CAACtD,EAAE,IAAG;QACtB,IAAIX,EAAE,CAACnB,WAAW,CAAC7Q,YAAY,CAACsR,QAAQ,CAACqB,EAAE,CAAChC,IAAI,CAAC,EAAE;UAC/CqB,EAAE,CAAC9R,YAAY,IAAI,GAAGyS,EAAE,CAAC3T,IAAI,IAAI;;MAEzC,CAAC,CAAC;MACF,IAAIgT,EAAE,CAAC9R,YAAY,CAACqS,MAAM,GAAG,CAAC,EAAE;QAC5BP,EAAE,CAAC9R,YAAY,GAAG8R,EAAE,CAAC9R,YAAY,CAACgW,SAAS,CAAC,CAAC,EAAElE,EAAE,CAAC9R,YAAY,CAACqS,MAAM,GAAG,CAAC,CAAC;;IAElF,CAAC,CAAC;EACN;EAEAgD,aAAaA,CAAA;IACT,OAAO,IAAI7Z,SAAS,CAAC;MACjBqa,MAAM,EAAE,IAAIta,WAAW,CAAC,IAAI,CAACoV,WAAW,CAACkF,MAAM,CAAC;MAChDI,WAAW,EAAE,IAAI1a,WAAW,CAAC,IAAI,CAACoV,WAAW,CAACsF,WAAW,CAAC;MAC1DC,SAAS,EAAE,IAAI3a,WAAW,CAAC,IAAI,CAACoV,WAAW,CAACuF,SAAS,CAAC;MACtDzF,IAAI,EAAE,IAAIlV,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC2a,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAACtF,4BAA4B,EAAE,CAAC,EAAE,CAAC,IAAI,CAACX,iBAAiB,EAAE,CAAC,CAAC;MAC3IrR,IAAI,EAAE,IAAIvD,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC2a,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAACnF,4BAA4B,EAAE,CAAC,EAAE,IAAI,CAACL,iBAAiB,EAAE,CAAC;MAC1IhG,YAAY,EAAE,IAAIrP,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC2a,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAACtF,4BAA4B,EAAE,CAAC,CAAC;MACvH/F,YAAY,EAAE,IAAIxP,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,CAAC,CAAC;MACxDhL,eAAe,EAAE,IAAI5P,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzGd,QAAQ,EAAE,IAAIja,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,CAAC,CAAC;MACpDV,WAAW,EAAE,IAAIla,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,CAAC,CAAC;MACvDrW,YAAY,EAAE,IAAIvE,WAAW,CAAC;QAACmQ,KAAK,EAAE,EAAE;QAAE6K,QAAQ,EAAE,CAAC,IAAI,CAACxK;MAAU,CAAC,EAAE,CAACtQ,UAAU,CAAC0a,QAAQ,CAAC,CAAC;MAC7FxD,OAAO,EAAE,IAAIpX,WAAW,CAAC;QAACmQ,KAAK,EAAE;MAAI,CAAC,CAAC;MACvC8K,aAAa,EAAE,IAAIjb,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC0a,QAAQ,CAAC,CAAC;MACzDM,aAAa,EAAE,IAAIlb,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvGrE,MAAM,EAAE,IAAI1W,WAAW,CAAC,CAAC,CAAC;MAC1B0P,WAAW,EAAE,IAAI1P,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAAC2a,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAE7DM,cAAc,EAAE,IAAInb,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxGK,cAAc,EAAE,IAAIpb,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnFM,eAAe,EAAE,IAAIrb,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpFlK,OAAO,EAAE,IAAI7Q,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAEjGpE,QAAQ,EAAE,IAAI3W,WAAW,CAAC,CAAC,CAAC;MAC5Bsb,cAAc,EAAE,IAAItb,WAAW,CAAC;QAC5BmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxEQ,aAAa,EAAE,IAAIvb,WAAW,CAAC;QAC3BmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC0a,QAAQ,EAAE1a,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxElE,SAAS,EAAE,IAAI7W,WAAW,CAAC;QACvBmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDxJ,aAAa,EAAE,IAAIvR,WAAW,CAAC;QAC3BmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDS,YAAY,EAAE,IAAIxb,WAAW,CAAC;QAC1BmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDU,aAAa,EAAE,IAAIzb,WAAW,CAAC;QAC3BmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDW,UAAU,EAAE,IAAI1b,WAAW,CAAC;QACxBmQ,KAAK,EAAE,CAAC;QACR6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;OACnB,EAAE,CAAC1L,UAAU,CAAC4a,GAAG,CAAC,UAAU,CAAC,EAAE5a,UAAU,CAAC6a,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDnE,WAAW,EAAE,IAAI5W,WAAW,CAAC;QAACmQ,KAAK,EAAE,CAAC;QAAE6K,QAAQ,EAAE,CAAC,IAAI,CAACpP;MAAU,CAAC;KACtE,CAAC;EACN;EAEAoO,aAAaA,CAAA;IACT,IAAI,CAAC2B,WAAW,GAAG,IAAI,CAAC9M,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC5E,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC,CAAC6M,MAAM;MACrD,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC9M,eAAe,GAAG,IAAI;OAC9B,MAAM;QACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEpC,CAAC,CAAC;IAEF,IAAI,CAAC+M,YAAY,GAAG,IAAI,CAAClN,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC7E,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC,CAAC6M,MAAM;MACrD,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC1M,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;IAEF,IAAI,CAAC4M,eAAe,GAAG,IAAI,CAACnN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC6M,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACxM,mBAAmB,GAAG,IAAI;OAClC,MAAM;QACH,IAAI,CAACA,mBAAmB,GAAG,KAAK;;IAExC,CAAC,CAAC;IAEF,IAAI,CAAC2M,eAAe,GAAG,IAAI,CAACpN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC6M,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACrM,mBAAmB,GAAG,IAAI;OAClC,MAAM;QACH,IAAI,CAACA,mBAAmB,GAAG,KAAK;;IAExC,CAAC,CAAC;IAEF,IAAI,CAACyM,kBAAkB,GAAG,IAAI,CAACrN,cAAc,CAACI,GAAG,CAAC,iBAAiB,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC9F,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,iBAAiB,CAAC,CAAC6M,MAAM;MAChE,IAAIA,MAAM,EAAE;QACR,IAAI,CAACjM,sBAAsB,GAAG,IAAI;OACrC,MAAM;QACH,IAAI,CAACA,sBAAsB,GAAG,KAAK;;IAE3C,CAAC,CAAC;IAEF,IAAI,CAACsM,mBAAmB,GAAG,IAAI,CAACtN,cAAc,CAACI,GAAG,CAAC,UAAU,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,UAAU,CAAC,CAAC6M,MAAM;MACzD,IAAIA,MAAM,EAAE;QACR,IAAI,CAACnI,uBAAuB,GAAG,IAAI;OACtC,MAAM;QACH,IAAI,CAACA,uBAAuB,GAAG,KAAK;;IAE5C,CAAC,CAAC;IAEF,IAAI,CAACyI,YAAY,GAAG,IAAI,CAACvN,cAAc,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACpF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC6M,MAAM;MAC5D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACvL,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;IAEF,IAAI,CAAC8L,eAAe,GAAG,IAAI,CAACxN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC6M,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACrL,mBAAmB,GAAG,IAAI;OAClC,MAAM;QACH,IAAI,CAACA,mBAAmB,GAAG,KAAK;;IAExC,CAAC,CAAC;IAEF,IAAI,CAAC6L,YAAY,GAAG,IAAI,CAACzN,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACtF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC6M,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC7L,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;IAEF,IAAI,CAACsM,WAAW,GAAG,IAAI,CAAC1N,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACrF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC6M,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC1L,eAAe,GAAG,IAAI;OAC9B,MAAM;QACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEpC,CAAC,CAAC;IAEF,IAAI,CAACoM,cAAc,GAAG,IAAI,CAAC3N,cAAc,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACtF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC6M,MAAM;MAC5D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACnM,kBAAkB,GAAG,IAAI;OACjC,MAAM;QACH,IAAI,CAACA,kBAAkB,GAAG,KAAK;;IAEvC,CAAC,CAAC;IAEF,IAAI,CAAC8M,WAAW,GAAG,IAAI,CAAC5N,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACtF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAAC6M,MAAM;MAC/D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAClL,eAAe,GAAG,IAAI;OAC9B,MAAM;QACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEpC,CAAC,CAAC;IACF,IAAI,CAAC8L,UAAU,GAAG,IAAI,CAAC7N,cAAc,CAACI,GAAG,CAAC,SAAS,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC9E,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,SAAS,CAAC,CAAC6M,MAAM;MACxD,IAAIA,MAAM,EAAE;QACR,IAAI,CAAChL,cAAc,GAAG,IAAI;OAC7B,MAAM;QACH,IAAI,CAACA,cAAc,GAAG,KAAK;;IAEnC,CAAC,CAAC;IAEF,IAAI,CAAC6L,qBAAqB,GAAG,IAAI,CAAC9N,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAChG,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAAC6M,MAAM;MAC/D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC9K,yBAAyB,GAAG,IAAI;OACxC,MAAM;QACH,IAAI,CAACA,yBAAyB,GAAG,KAAK;;IAE9C,CAAC,CAAC;IAEF,IAAI,CAAC4L,sBAAsB,GAAG,IAAI,CAAC/N,cAAc,CAACI,GAAG,CAAC,iBAAiB,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAClG,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,iBAAiB,CAAC,CAAC6M,MAAM;MAChE,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC5K,0BAA0B,GAAG,IAAI;OACzC,MAAM;QACH,IAAI,CAACA,0BAA0B,GAAG,KAAK;;IAE/C,CAAC,CAAC;IAEF,IAAI,CAAC2L,uBAAuB,GAAG,IAAI,CAAChO,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAClG,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,gBAAgB,CAAC,CAAC6M,MAAM;MAC/D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACzK,2BAA2B,GAAG,IAAI;OAC1C,MAAM;QACH,IAAI,CAACA,2BAA2B,GAAG,KAAK;;IAEhD,CAAC,CAAC;IAEF,IAAI,CAACyL,wBAAwB,GAAG,IAAI,CAACjO,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAClG,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC6M,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACxK,4BAA4B,GAAG,IAAI;OAC3C,MAAM;QACH,IAAI,CAACA,4BAA4B,GAAG,KAAK;;IAEjD,CAAC,CAAC;IAEF,IAAI,CAACyL,yBAAyB,GAAG,IAAI,CAAClO,cAAc,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC/F,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC6M,MAAM;MAC1D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACtK,6BAA6B,GAAG,IAAI;OAC5C,MAAM;QACH,IAAI,CAACA,6BAA6B,GAAG,KAAK;;IAElD,CAAC,CAAC;IAEF,IAAI,CAACwL,0BAA0B,GAAG,IAAI,CAACnO,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MACpG,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC6M,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACrK,8BAA8B,GAAG,IAAI;OAC7C,MAAM;QACH,IAAI,CAACA,8BAA8B,GAAG,KAAK;;IAEnD,CAAC,CAAC;IAEF,IAAI,CAACwL,kBAAkB,GAAG,IAAI,CAACpO,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC3F,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAAC6M,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACnK,sBAAsB,GAAG,IAAI;OACrC,MAAM;QACH,IAAI,CAACA,sBAAsB,GAAG,KAAK;;IAE3C,CAAC,CAAC;IAEF,IAAI,CAACuL,mBAAmB,GAAG,IAAI,CAACrO,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAC7F,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,eAAe,CAAC,CAAC6M,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACjK,uBAAuB,GAAG,IAAI;OACtC,MAAM;QACH,IAAI,CAACA,uBAAuB,GAAG,KAAK;;IAE5C,CAAC,CAAC;IAEF,IAAI,CAACsL,SAAS,GAAG,IAAI,CAACtO,cAAc,CAACI,GAAG,CAAC,YAAY,CAAC,CAAC2M,aAAa,CAACC,SAAS,CAAC,MAAK;MAChF,MAAMC,MAAM,GAAG,IAAI,CAACjN,cAAc,CAACI,GAAG,CAAC,YAAY,CAAC,CAAC6M,MAAM;MAC3D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC/J,aAAa,GAAG,IAAI;OAC5B,MAAM;QACH,IAAI,CAACA,aAAa,GAAG,KAAK;;IAElC,CAAC,CAAC;EACN;EAEAqL,WAAWA,CAAA;IACP,IAAI,IAAI,CAACzB,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAAC0B,MAAM,EAAE;MAC9C,IAAI,CAAC1B,WAAW,CAAC2B,WAAW,EAAE;;IAElC,IAAI,IAAI,CAACvB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACsB,MAAM,EAAE;MAChD,IAAI,CAACtB,YAAY,CAACuB,WAAW,EAAE;;IAEnC,IAAI,IAAI,CAACtB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACqB,MAAM,EAAE;MACtD,IAAI,CAACrB,eAAe,CAACsB,WAAW,EAAE;;IAEtC,IAAI,IAAI,CAACrB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACoB,MAAM,EAAE;MACtD,IAAI,CAACpB,eAAe,CAACqB,WAAW,EAAE;;IAEtC,IAAI,IAAI,CAACpB,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAACmB,MAAM,EAAE;MAC5D,IAAI,CAACnB,kBAAkB,CAACoB,WAAW,EAAE;;IAEzC,IAAI,IAAI,CAACnB,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAACkB,MAAM,EAAE;MAC9D,IAAI,CAAClB,mBAAmB,CAACmB,WAAW,EAAE;;IAE1C,IAAI,IAAI,CAAClB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACiB,MAAM,EAAE;MAChD,IAAI,CAACjB,YAAY,CAACkB,WAAW,EAAE;;IAEnC,IAAI,IAAI,CAACjB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACgB,MAAM,EAAE;MACtD,IAAI,CAAChB,eAAe,CAACiB,WAAW,EAAE;;IAEtC,IAAI,IAAI,CAAChB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACe,MAAM,EAAE;MAChD,IAAI,CAACf,YAAY,CAACgB,WAAW,EAAE;;IAEnC,IAAI,IAAI,CAACf,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACc,MAAM,EAAE;MAC9C,IAAI,CAACd,WAAW,CAACe,WAAW,EAAE;;IAElC,IAAI,IAAI,CAACd,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACa,MAAM,EAAE;MACpD,IAAI,CAACb,cAAc,CAACc,WAAW,EAAE;;IAErC,IAAI,IAAI,CAACd,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACa,MAAM,EAAE;MACpD,IAAI,CAACb,cAAc,CAACc,WAAW,EAAE;;IAErC,IAAI,IAAI,CAACb,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACY,MAAM,EAAE;MAC9C,IAAI,CAACZ,WAAW,CAACa,WAAW,EAAE;;IAElC,IAAI,IAAI,CAACX,qBAAqB,IAAI,CAAC,IAAI,CAACA,qBAAqB,CAACU,MAAM,EAAE;MAClE,IAAI,CAACV,qBAAqB,CAACW,WAAW,EAAE;;IAE5C,IAAI,IAAI,CAACV,sBAAsB,IAAI,CAAC,IAAI,CAACA,sBAAsB,CAACS,MAAM,EAAE;MACpE,IAAI,CAACT,sBAAsB,CAACU,WAAW,EAAE;;IAE7C,IAAI,IAAI,CAACT,uBAAuB,IAAI,CAAC,IAAI,CAACA,uBAAuB,CAACQ,MAAM,EAAE;MACtE,IAAI,CAACR,uBAAuB,CAACS,WAAW,EAAE;;IAE9C,IAAI,IAAI,CAACR,wBAAwB,IAAI,CAAC,IAAI,CAACA,wBAAwB,CAACO,MAAM,EAAE;MACxE,IAAI,CAACP,wBAAwB,CAACQ,WAAW,EAAE;;IAE/C,IAAI,IAAI,CAACP,yBAAyB,IAAI,CAAC,IAAI,CAACA,yBAAyB,CAACM,MAAM,EAAE;MAC1E,IAAI,CAACN,yBAAyB,CAACO,WAAW,EAAE;;IAEhD,IAAI,IAAI,CAACN,0BAA0B,IAAI,CAAC,IAAI,CAACA,0BAA0B,CAACK,MAAM,EAAE;MAC5E,IAAI,CAACL,0BAA0B,CAACM,WAAW,EAAE;;IAEjD,IAAI,IAAI,CAACL,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAACI,MAAM,EAAE;MAC5D,IAAI,CAACJ,kBAAkB,CAACK,WAAW,EAAE;;IAEzC,IAAI,IAAI,CAACJ,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAACG,MAAM,EAAE;MAC9D,IAAI,CAACH,mBAAmB,CAACI,WAAW,EAAE;;IAE1C,IAAI,IAAI,CAACH,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACE,MAAM,EAAE;MAC1C,IAAI,CAACF,SAAS,CAACG,WAAW,EAAE;;IAEhC,IAAI,IAAI,CAACvB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACsB,MAAM,EAAE;MAChD,IAAI,CAACtB,YAAY,CAACuB,WAAW,EAAE;;IAEnC,IAAI,IAAI,CAACtB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACqB,MAAM,EAAE;MACtD,IAAI,CAACrB,eAAe,CAACsB,WAAW,EAAE;;IAEtC,IAAI,IAAI,CAACrB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACoB,MAAM,EAAE;MACtD,IAAI,CAACpB,eAAe,CAACqB,WAAW,EAAE;;IAEtC,IAAI,IAAI,CAACpB,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAACmB,MAAM,EAAE;MAC5D,IAAI,CAACnB,kBAAkB,CAACoB,WAAW,EAAE;;IAEzC,IAAI,IAAI,CAACnB,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAACkB,MAAM,EAAE;MAC9D,IAAI,CAAClB,mBAAmB,CAACmB,WAAW,EAAE;;IAE1C,IAAI,IAAI,CAAClB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACiB,MAAM,EAAE;MAChD,IAAI,CAACjB,YAAY,CAACkB,WAAW,EAAE;;IAEnC,IAAI,IAAI,CAACjB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACgB,MAAM,EAAE;MACtD,IAAI,CAAChB,eAAe,CAACiB,WAAW,EAAE;;IAEtC,IAAI,IAAI,CAAChB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACe,MAAM,EAAE;MAChD,IAAI,CAACf,YAAY,CAACgB,WAAW,EAAE;;IAEnC,IAAI,IAAI,CAACf,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACc,MAAM,EAAE;MAC9C,IAAI,CAACd,WAAW,CAACe,WAAW,EAAE;;IAElC,IAAI,IAAI,CAACd,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACa,MAAM,EAAE;MACpD,IAAI,CAACb,cAAc,CAACc,WAAW,EAAE;;IAErC,IAAI,IAAI,CAACd,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACa,MAAM,EAAE;MACpD,IAAI,CAACb,cAAc,CAACc,WAAW,EAAE;;IAErC,IAAI,IAAI,CAACb,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACY,MAAM,EAAE;MAC9C,IAAI,CAACZ,WAAW,CAACa,WAAW,EAAE;;IAElC,IAAI,IAAI,CAACX,qBAAqB,IAAI,CAAC,IAAI,CAACA,qBAAqB,CAACU,MAAM,EAAE;MAClE,IAAI,CAACV,qBAAqB,CAACW,WAAW,EAAE;;IAE5C,IAAI,IAAI,CAACV,sBAAsB,IAAI,CAAC,IAAI,CAACA,sBAAsB,CAACS,MAAM,EAAE;MACpE,IAAI,CAACT,sBAAsB,CAACU,WAAW,EAAE;;IAE7C,IAAI,IAAI,CAACT,uBAAuB,IAAI,CAAC,IAAI,CAACA,uBAAuB,CAACQ,MAAM,EAAE;MACtE,IAAI,CAACR,uBAAuB,CAACS,WAAW,EAAE;;IAE9C,IAAI,IAAI,CAACT,uBAAuB,IAAI,CAAC,IAAI,CAACC,wBAAwB,CAACO,MAAM,EAAE;MACvE,IAAI,CAACP,wBAAwB,CAACQ,WAAW,EAAE;;IAE/C,IAAI,IAAI,CAACP,yBAAyB,IAAI,CAAC,IAAI,CAACA,yBAAyB,CAACM,MAAM,EAAE;MAC1E,IAAI,CAACN,yBAAyB,CAACO,WAAW,EAAE;;IAEhD,IAAI,IAAI,CAACN,0BAA0B,IAAI,CAAC,IAAI,CAACA,0BAA0B,CAACK,MAAM,EAAE;MAC5E,IAAI,CAACL,0BAA0B,CAACM,WAAW,EAAE;;IAEjD,IAAI,IAAI,CAACL,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAACI,MAAM,EAAE;MAC5D,IAAI,CAACJ,kBAAkB,CAACK,WAAW,EAAE;;IAEzC,IAAI,IAAI,CAACJ,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAACG,MAAM,EAAE;MAC9D,IAAI,CAACH,mBAAmB,CAACI,WAAW,EAAE;;IAE1C,IAAI,IAAI,CAACH,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACE,MAAM,EAAE;MAC1C,IAAI,CAACF,SAAS,CAACG,WAAW,EAAE;;EAEpC;EACAC,kBAAkBA,CAAA;IACd,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,UAAU,CAAC,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACrL,cAAc,CAAC;EACrG;EAEAmL,UAAUA,CAACG,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAErF,MAAM;IAChC,IAAIlC,EAAE,GAAG,IAAI;IACb,IAAI,CAACiH,gBAAgB,GAAGI,IAAI;IAC5B,IAAI,CAACF,cAAc,GAAGG,KAAK;IAC3B,IAAIC,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAIC,SAAS,EAAC;MAClC,IAAI,CAACJ,UAAU,GAAG,SAAS;MAC3BG,IAAI,GAAG,SAAS;KACnB,MAAK;MACF,IAAI,CAACH,UAAU,GAAGG,IAAI;;IAE1B,IAAIE,UAAU,GAAG;MACbJ,IAAI;MACJpE,IAAI,EAAEqE,KAAK;MACXC,IAAI;MACJvZ,YAAY,EAAE,IAAI,CAACiO,YAAY,CAAClS,GAAG,CAAC6W,CAAC,IAAIA,CAAC,CAACjC,IAAI;KAClD;IACD+I,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5L,cAAc,CAAC,CAACkI,OAAO,CAACpX,GAAG,IAAG;MAC3C,IAAG,IAAI,CAACkP,cAAc,CAAClP,GAAG,CAAC,IAAI,IAAI,EAAC;QAChC4a,UAAU,CAAC5a,GAAG,CAAC,GAAG,IAAI,CAACkP,cAAc,CAAClP,GAAG,CAAC;;IAElD,CAAC,CAAC;IACF,IAAI,IAAI,CAACkP,cAAc,CAAC/N,YAAY,IAAI,IAAI,EAAC;MACzCyZ,UAAU,CAACzZ,YAAY,GAAG,IAAI,CAACiO,YAAY,CAAClS,GAAG,CAAC6W,CAAC,IAAIA,CAAC,CAACjC,IAAI,CAAC;;IAEhE,IAAI,CAACnB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;IACnD,IAAI,CAACjB,iBAAiB,CAACsL,mBAAmB,CAACH,UAAU,EAAGzJ,QAAQ,IAAI;MAChEgC,EAAE,CAACwC,iBAAiB,GAAG;QACnBC,OAAO,EAAEzE,QAAQ,CAACyE,OAAO;QACzBC,KAAK,EAAE1E,QAAQ,CAAC6J;OACnB;MACD,IAAI,CAACtK,eAAe,GAAG,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAC;IACvD,CAAC,CAAC;EAEN;EAEAxO,4BAA4BA,CAAA;IACxB,IAAI8Y,iBAAiB,GAAG,IAAI,CAACxP,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACkB,KAAK;IACrE,IAAI,CAACqC,YAAY,GAAG,IAAI,CAACnO,SAAS,CAAC4S,MAAM,CAAEqH,IAAI,IAC3CD,iBAAiB,CAACxI,QAAQ,CAACyI,IAAI,CAACpJ,IAAI,CAAC,CACxC;IACD,IAAI,IAAI,CAACsI,gBAAgB,IAAI,IAAI,EAAC;MAC9B,IAAI,CAACA,gBAAgB,GAAG,CAAC;;IAE7B,IAAI,IAAI,CAACE,cAAc,IAAI,IAAI,EAAC;MAC5B,IAAI,CAACA,cAAc,GAAG,EAAE;;IAE5B,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,EAAC;MACxB,IAAI,CAACA,UAAU,GAAG,SAAS;;IAE/B,IAAI,CAACrL,cAAc,CAAC/N,YAAY,GAAG,IAAI,CAACiO,YAAY,CAAClS,GAAG,CAAC6W,CAAC,IAAIA,CAAC,CAACjC,IAAI,CAAC;IACrE,IAAI,CAACuI,UAAU,CAAC,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACrL,cAAc,CAAC;IACjG,IAAI,CAACuB,8BAA8B,GAAG,IAAI;EAC9C;EACA1P,cAAcA,CAAA;IACV,IAAI4S,gBAAgB,GAAG,IAAI,CAAClI,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC,CAACkB,KAAK;IACpE,IAAI+J,WAAW,GAAG,IAAI,CAACrL,cAAc,CAACI,GAAG,CAAC,aAAa,CAAC,CAACkB,KAAK;IAC9D,IAAI+J,WAAW,IAAI,CAAC,EAAC;MACjB,IAAInD,gBAAgB,CAACD,MAAM,GAAG,CAAC,EAAC;QAC5B,IAAI,CAACpG,UAAU,GAAG,IAAI;OACzB,MAAK;QACF,IAAI,CAACA,UAAU,GAAG,KAAK;;;EAGnC;;;uBAv7BSiC,mBAAmB,EAAA5R,EAAA,CAAAwd,iBAAA,CAmJR1d,iBAAiB,GAAAE,EAAA,CAAAwd,iBAAA,CACjB5d,cAAc,GAAAI,EAAA,CAAAwd,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1d,EAAA,CAAAwd,iBAAA,CAAAxd,EAAA,CAAA2d,QAAA;IAAA;EAAA;;;YApJzB/L,mBAAmB;MAAAgM,SAAA;MAAAC,QAAA,GAAA7d,EAAA,CAAA8d,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5BhCpe,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3FH,EAAA,CAAA2D,SAAA,sBAAoF;UACxF3D,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAA4D,UAAA,IAAA0a,qCAAA,wBAogBS;UAETte,EAAA,CAAAC,cAAA,cAAqE;UAAlCD,EAAA,CAAA0B,UAAA,sBAAA6c,sDAAA;YAAA,OAAYF,GAAA,CAAA7B,kBAAA,EAAoB;UAAA,EAAC;UAChExc,EAAA,CAAAC,cAAA,aAA2D;UACwBD,EAAA,CAAA0B,UAAA,2BAAA8c,+DAAA5c,MAAA;YAAA,OAAAyc,GAAA,CAAAvL,8BAAA,GAAAlR,MAAA;UAAA,EAA4C;UACvH5B,EAAA,CAAAC,cAAA,aAAkB;UAMCD,EAAA,CAAA0B,UAAA,2BAAA+c,6DAAA7c,MAAA;YAAA,OAAAyc,GAAA,CAAA9M,cAAA,CAAA4G,QAAA,GAAAvW,MAAA;UAAA,EAAqC;UAF5C5B,EAAA,CAAAG,YAAA,EAKE;UACFH,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIhGH,EAAA,CAAAC,cAAA,cAAmB;UAIJD,EAAA,CAAA0B,UAAA,2BAAAgd,6DAAA9c,MAAA;YAAA,OAAAyc,GAAA,CAAA9M,cAAA,CAAA6G,QAAA,GAAAxW,MAAA;UAAA,EAAqC;UAF5C5B,EAAA,CAAAG,YAAA,EAKE;UACFH,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIhGH,EAAA,CAAAC,cAAA,cAA2H;UACvHD,EAAA,CAAA4D,UAAA,KAAA+a,oCAAA,mBAUO;UACP3e,EAAA,CAAA4D,UAAA,KAAAgb,oCAAA,mBAAwH;UAC5H5e,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA2D,SAAA,oBAGY;UAChB3D,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,sBAaC;UATGD,EAAA,CAAA0B,UAAA,+BAAAmd,sEAAAjd,MAAA;YAAA,OAAAyc,GAAA,CAAAtL,eAAA,GAAAnR,MAAA;UAAA,EAAiC;UASpC5B,EAAA,CAAAG,YAAA,EAAa;;;UA1kBkBH,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAuC,iBAAA,CAAA8b,GAAA,CAAA9d,WAAA,CAAAC,SAAA,yBAAiD;UAC9CR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAmC,UAAA,UAAAkc,GAAA,CAAAhM,KAAA,CAAe,SAAAgM,GAAA,CAAAnM,IAAA;UAINlS,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAmC,UAAA,SAAAkc,GAAA,CAAAvQ,cAAA,CAAoB;UAsgBtE9N,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAmC,UAAA,cAAAkc,GAAA,CAAA/F,cAAA,CAA4B;UAEiHtY,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAA8e,UAAA,CAAA9e,EAAA,CAAAwB,eAAA,KAAAud,GAAA,EAA4B;UAA7J/e,EAAA,CAAAmC,UAAA,WAAAkc,GAAA,CAAA9d,WAAA,CAAAC,SAAA,qCAAoE,YAAA6d,GAAA,CAAAvL,8BAAA;UAOvD9S,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAmC,UAAA,YAAAkc,GAAA,CAAA9M,cAAA,CAAA4G,QAAA,CAAqC;UAIlBnY,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAA8b,GAAA,CAAA9d,WAAA,CAAAC,SAAA,8BAAsD;UAQzER,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAmC,UAAA,YAAAkc,GAAA,CAAA9M,cAAA,CAAA6G,QAAA,CAAqC;UAIlBpY,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAA8b,GAAA,CAAA9d,WAAA,CAAAC,SAAA,8BAAsD;UAIrER,EAAA,CAAAI,SAAA,GAAuG;UAAvGJ,EAAA,CAAAgf,UAAA,CAAAX,GAAA,CAAApa,QAAA,IAAAoa,GAAA,CAAAna,WAAA,CAAAC,KAAA,iEAAuG;UACzFnE,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAmC,UAAA,SAAAkc,GAAA,CAAApa,QAAA,IAAAoa,GAAA,CAAAna,WAAA,CAAAC,KAAA,CAAmC;UAWzDnE,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAmC,UAAA,SAAAkc,GAAA,CAAApa,QAAA,IAAAoa,GAAA,CAAAna,WAAA,CAAAC,KAAA,CAAmC;UAU9CnE,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAmC,UAAA,iBAAgB,eAAAkc,GAAA,CAAA5B,gBAAA,cAAA4B,GAAA,CAAA1B,cAAA,iBAAA0B,GAAA,CAAAtL,eAAA,aAAAsL,GAAA,CAAA7F,eAAA,aAAA6F,GAAA,CAAArG,iBAAA,aAAAqG,GAAA,CAAA1G,6BAAA,cAAA0G,GAAA,CAAA3B,UAAA,CAAAuC,IAAA,CAAAZ,GAAA,yBAAAre,EAAA,CAAAwB,eAAA,KAAA0d,GAAA,oCAAAb,GAAA,CAAAtB,IAAA,YAAAsB,GAAA,CAAA9M,cAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}