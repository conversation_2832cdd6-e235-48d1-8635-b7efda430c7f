{"ast": null, "code": "export default {\n  label: {\n    centerCode: \"Center Code\",\n    paymentName: \"Payment Name\",\n    contactPhone: \"Contact Phone\",\n    contractor: \"Contractor\",\n    contractCode: \"Contract Code\",\n    customerCode: \"Customer Code\",\n    customerName: \"Customer Name\",\n    contractDate: \"Contract Date\",\n    title: \"Contract List\",\n    contactAddress: \"Contact Address\",\n    paymentAddress: \"Payment Address\",\n    routeCode: \"Route Code\",\n    customerBirthday: \"Customer Birthday\",\n    viewSimList: \"View Sim List\",\n    headerModal: \"Sim List\"\n  }\n};", "map": {"version": 3, "names": ["label", "centerCode", "paymentName", "contactPhone", "contractor", "contractCode", "customerCode", "customerName", "contractDate", "title", "contactAddress", "paymentAddress", "routeCode", "customerBirthday", "viewSimList", "headerModal"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\contract.ts"], "sourcesContent": ["export default{\r\n    label:{\r\n        centerCode:\"Center Code\",\r\n        paymentName:\"Payment Name\",\r\n        contactPhone:\"Contact Phone\",\r\n        contractor:\"Contractor\",\r\n        contractCode:\"Contract Code\",\r\n        customerCode:\"Customer Code\",\r\n        customerName:\"Customer Name\",\r\n        contractDate:\"Contract Date\",\r\n        title:\"Contract List\",\r\n        contactAddress:\"Contact Address\",\r\n        paymentAddress:\"Payment Address\",\r\n        routeCode:\"Route Code\",\r\n        customerBirthday:\"Customer Birthday\",\r\n        viewSimList:\"View Sim List\",\r\n        headerModal:\"Sim List\",\r\n    }\r\n}"], "mappings": "AAAA,eAAc;EACVA,KAAK,EAAC;IACFC,UAAU,EAAC,aAAa;IACxBC,WAAW,EAAC,cAAc;IAC1BC,YAAY,EAAC,eAAe;IAC5BC,UAAU,EAAC,YAAY;IACvBC,YAAY,EAAC,eAAe;IAC5BC,YAAY,EAAC,eAAe;IAC5BC,YAAY,EAAC,eAAe;IAC5BC,YAAY,EAAC,eAAe;IAC5BC,KAAK,EAAC,eAAe;IACrBC,cAAc,EAAC,iBAAiB;IAChCC,cAAc,EAAC,iBAAiB;IAChCC,SAAS,EAAC,YAAY;IACtBC,gBAAgB,EAAC,mBAAmB;IACpCC,WAAW,EAAC,eAAe;IAC3BC,WAAW,EAAC;;CAEnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}