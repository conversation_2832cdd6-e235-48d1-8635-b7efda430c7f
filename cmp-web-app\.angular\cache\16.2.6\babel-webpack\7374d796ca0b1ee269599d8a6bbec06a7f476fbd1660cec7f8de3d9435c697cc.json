{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppSimListComponent } from './list/app.sim.list.component';\nimport { AppSimCreateComponent } from './create/app.sim.create.component';\nimport { GroupSimComponent } from './group-sim/group-sim.list.component';\nimport { CreateGroupSimComponent } from './group-sim/create-group-sim/create-group-sim.component';\nimport { UpdateGroupSimComponent } from './group-sim/update-group-sim/update-group-sim.component';\nimport { ListGroupComponent } from './group-sim/detail-group/group.detail.component';\nimport { SimDetailComponent } from \"./detail/app.sim.detail.component\";\nimport { ContractManagementComponent } from './contract-management/contract-management.component';\nimport DataPage from 'src/app/service/data.page';\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { AppRechargeMoneyComponent } from \"./recharge-money/app.sim.recharge-money-history.list.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppSimRoutingModule {\n  static {\n    this.ɵfac = function AppSimRoutingModule_Factory(t) {\n      return new (t || AppSimRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppSimRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: '',\n        component: AppSimListComponent,\n        data: new DataPage(\"global.menu.listsim\", [CONSTANTS.PERMISSIONS.SIM.VIEW_LIST])\n      }, {\n        path: 'create',\n        component: AppSimCreateComponent,\n        data: new DataPage()\n      }, {\n        path: \"detail/:id\",\n        component: SimDetailComponent,\n        data: new DataPage(\"global.titlepage.detailsim\", [CONSTANTS.PERMISSIONS.SIM.VIEW_DETAIL])\n      }, {\n        path: 'group',\n        component: GroupSimComponent,\n        data: new DataPage(\"global.titlepage.listGroupSim\", [CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST])\n      }, {\n        path: 'group/create',\n        component: CreateGroupSimComponent,\n        data: new DataPage(\"global.titlepage.createGroupSim\", [CONSTANTS.PERMISSIONS.GROUP_SIM.CREATE])\n      }, {\n        path: 'group/update/:idgroup',\n        component: UpdateGroupSimComponent,\n        data: new DataPage(\"global.titlepage.editGroupSim\", [CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])\n      }, {\n        path: 'group/detail/:idgroup',\n        component: ListGroupComponent,\n        data: new DataPage(\"global.titlepage.detailGroupSim\", ['getSimGroup'])\n      }, {\n        path: 'contract',\n        component: ContractManagementComponent,\n        data: new DataPage(\"global.titlepage.listContract\", [CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])\n      }, {\n        path: 'recharge-money',\n        component: AppRechargeMoneyComponent,\n        data: new DataPage()\n      }]), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppSimRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppSimListComponent", "AppSimCreateComponent", "GroupSimComponent", "CreateGroupSimComponent", "UpdateGroupSimComponent", "ListGroupComponent", "SimDetailComponent", "ContractManagementComponent", "DataPage", "CONSTANTS", "AppRechargeMoneyComponent", "AppSimRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "data", "PERMISSIONS", "SIM", "VIEW_LIST", "VIEW_DETAIL", "GROUP_SIM", "CREATE", "UPDATE", "CONTRACT", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\sim-management\\app.sim-routing.ts"], "sourcesContent": ["import { RouterModule } from '@angular/router';\r\nimport { NgModule } from '@angular/core';\r\nimport { AppSimListComponent } from './list/app.sim.list.component';\r\nimport { AppSimCreateComponent } from './create/app.sim.create.component';\r\nimport { GroupSimComponent } from './group-sim/group-sim.list.component';\r\nimport { CreateGroupSimComponent } from './group-sim/create-group-sim/create-group-sim.component';\r\nimport { UpdateGroupSimComponent } from './group-sim/update-group-sim/update-group-sim.component';\r\nimport { ListGroupComponent } from './group-sim/detail-group/group.detail.component';\r\nimport { SimDetailComponent } from \"./detail/app.sim.detail.component\";\r\nimport { ContractManagementComponent } from './contract-management/contract-management.component';\r\nimport DataPage from 'src/app/service/data.page';\r\nimport { CONSTANTS } from 'src/app/service/comon/constants';\r\nimport {AppRechargeMoneyComponent} from \"./recharge-money/app.sim.recharge-money-history.list.component\";\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            { path: '', component: AppSimListComponent, data: new DataPage(\"global.menu.listsim\", [CONSTANTS.PERMISSIONS.SIM.VIEW_LIST])},\r\n            { path: 'create', component: AppSimCreateComponent , data: new DataPage()},\r\n            { path: \"detail/:id\", component: SimDetailComponent, data: new DataPage(\"global.titlepage.detailsim\", [CONSTANTS.PERMISSIONS.SIM.VIEW_DETAIL])},\r\n            { path: 'group', component: GroupSimComponent, data: new DataPage(\"global.titlepage.listGroupSim\",[CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST]) },\r\n            { path: 'group/create', component: CreateGroupSimComponent, data: new DataPage(\"global.titlepage.createGroupSim\", [CONSTANTS.PERMISSIONS.GROUP_SIM.CREATE]) },\r\n            { path: 'group/update/:idgroup', component: UpdateGroupSimComponent, data: new DataPage(\"global.titlepage.editGroupSim\", [CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE]) },\r\n            { path: 'group/detail/:idgroup', component: ListGroupComponent ,data: new DataPage(\"global.titlepage.detailGroupSim\", ['getSimGroup'])},\r\n            { path: 'contract', component: ContractManagementComponent, data: new DataPage(\"global.titlepage.listContract\",[CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST]) },\r\n            { path: 'recharge-money', component: AppRechargeMoneyComponent, data: new DataPage()}\r\n        ]),\r\n    ],\r\n    exports: [RouterModule],\r\n})\r\nexport class AppSimRoutingModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,uBAAuB,QAAQ,yDAAyD;AACjG,SAASC,uBAAuB,QAAQ,yDAAyD;AACjG,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAAQC,yBAAyB,QAAO,gEAAgE;;;AAkBxG,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAdxBZ,YAAY,CAACa,QAAQ,CAAC,CAClB;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAEd,mBAAmB;QAAEe,IAAI,EAAE,IAAIP,QAAQ,CAAC,qBAAqB,EAAE,CAACC,SAAS,CAACO,WAAW,CAACC,GAAG,CAACC,SAAS,CAAC;MAAC,CAAC,EAC7H;QAAEL,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAEb,qBAAqB;QAAGc,IAAI,EAAE,IAAIP,QAAQ;MAAE,CAAC,EAC1E;QAAEK,IAAI,EAAE,YAAY;QAAEC,SAAS,EAAER,kBAAkB;QAAES,IAAI,EAAE,IAAIP,QAAQ,CAAC,4BAA4B,EAAE,CAACC,SAAS,CAACO,WAAW,CAACC,GAAG,CAACE,WAAW,CAAC;MAAC,CAAC,EAC/I;QAAEN,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAEZ,iBAAiB;QAAEa,IAAI,EAAE,IAAIP,QAAQ,CAAC,+BAA+B,EAAC,CAACC,SAAS,CAACO,WAAW,CAACI,SAAS,CAACF,SAAS,CAAC;MAAC,CAAE,EAChJ;QAAEL,IAAI,EAAE,cAAc;QAAEC,SAAS,EAAEX,uBAAuB;QAAEY,IAAI,EAAE,IAAIP,QAAQ,CAAC,iCAAiC,EAAE,CAACC,SAAS,CAACO,WAAW,CAACI,SAAS,CAACC,MAAM,CAAC;MAAC,CAAE,EAC7J;QAAER,IAAI,EAAE,uBAAuB;QAAEC,SAAS,EAAEV,uBAAuB;QAAEW,IAAI,EAAE,IAAIP,QAAQ,CAAC,+BAA+B,EAAE,CAACC,SAAS,CAACO,WAAW,CAACI,SAAS,CAACE,MAAM,CAAC;MAAC,CAAE,EACpK;QAAET,IAAI,EAAE,uBAAuB;QAAEC,SAAS,EAAET,kBAAkB;QAAEU,IAAI,EAAE,IAAIP,QAAQ,CAAC,iCAAiC,EAAE,CAAC,aAAa,CAAC;MAAC,CAAC,EACvI;QAAEK,IAAI,EAAE,UAAU;QAAEC,SAAS,EAAEP,2BAA2B;QAAEQ,IAAI,EAAE,IAAIP,QAAQ,CAAC,+BAA+B,EAAC,CAACC,SAAS,CAACO,WAAW,CAACO,QAAQ,CAACL,SAAS,CAAC;MAAC,CAAE,EAC5J;QAAEL,IAAI,EAAE,gBAAgB;QAAEC,SAAS,EAAEJ,yBAAyB;QAAEK,IAAI,EAAE,IAAIP,QAAQ;MAAE,CAAC,CACxF,CAAC,EAEIT,YAAY;IAAA;EAAA;;;2EAEbY,mBAAmB;IAAAa,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFlB3B,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}