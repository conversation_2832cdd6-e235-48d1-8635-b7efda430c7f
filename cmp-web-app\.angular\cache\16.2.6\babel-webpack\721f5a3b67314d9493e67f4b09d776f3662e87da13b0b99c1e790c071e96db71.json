{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport { CONSTANTS } from \"../../../service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/app.layout.service\";\nimport * as i2 from \"src/app/service/guide/GuideService\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../common-module/choose-language/choose-language.component\";\nimport * as i8 from \"primeng/ripple\";\nimport * as i9 from \"primeng/overlaypanel\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/panelmenu\";\nimport * as i12 from \"primeng/divider\";\nimport * as i13 from \"primeng/breadcrumb\";\nconst _c0 = function () {\n  return [\"/profile\"];\n};\nfunction AppGuideComponent_div_8_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u00A0 \", ctx_r5.tranService.translate(\"login.label.editProfile\"), \" \");\n  }\n}\nconst _c1 = function (a0) {\n  return [a0];\n};\nconst _c2 = function () {\n  return [\"profile/change-password\"];\n};\nfunction AppGuideComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function AppGuideComponent_div_8_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const _r4 = i0.ɵɵreference(7);\n      return i0.ɵɵresetView(_r4.toggle($event));\n    });\n    i0.ɵɵelement(2, \"i\", 27);\n    i0.ɵɵtext(3, \"\\u00A0\");\n    i0.ɵɵelementStart(4, \"span\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p-overlayPanel\", null, 29);\n    i0.ɵɵtemplate(8, AppGuideComponent_div_8_a_8_Template, 4, 3, \"a\", 30);\n    i0.ɵɵelementStart(9, \"a\", 31)(10, \"div\", 32);\n    i0.ɵɵelement(11, \"i\", 33);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function AppGuideComponent_div_8_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.logout());\n    });\n    i0.ɵɵelementStart(14, \"div\", 32);\n    i0.ɵɵelement(15, \"i\", 35);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.userInfo.fullName.length > 20 ? ctx_r1.userInfo.fullName : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.userInfo.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checkAuthen(i0.ɵɵpureFunction1(6, _c1, ctx_r1.CONSTANTS.PERMISSIONS.PROFILE.VIEW)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c2));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u00A0 \", ctx_r1.tranService.translate(\"login.label.changePassword\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u00A0 \", ctx_r1.tranService.translate(\"login.label.logout\"), \" \");\n  }\n}\nfunction AppGuideComponent_div_27_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 39)(1, \"span\", 40);\n    i0.ɵɵlistener(\"click\", function AppGuideComponent_div_27_li_2_Template_span_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const page_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.handleClickMenu({\n        item: page_r10\n      }, 1));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r10.label);\n  }\n}\nfunction AppGuideComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"ul\");\n    i0.ɵɵtemplate(2, AppGuideComponent_div_27_li_2_Template, 3, 1, \"li\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.listPageChildren);\n  }\n}\nfunction AppGuideComponent_div_28_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 44);\n    i0.ɵɵelement(2, \"i\", 45)(3, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-button\", 47);\n    i0.ɵɵlistener(\"click\", function AppGuideComponent_div_28_div_2_Template_p_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.handleClickMenu({\n        item: ctx_r15.pagePrevious\n      }, 1));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.pagePrevious.label, \" \");\n  }\n}\nfunction AppGuideComponent_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"div\")(3, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-button\", 47);\n    i0.ɵɵlistener(\"click\", function AppGuideComponent_div_28_div_4_Template_p_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.handleClickMenu({\n        item: ctx_r17.pageNext\n      }, 1));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.pageNext.label, \" \");\n  }\n}\nfunction AppGuideComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\");\n    i0.ɵɵtemplate(2, AppGuideComponent_div_28_div_2_Template, 6, 1, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtemplate(4, AppGuideComponent_div_28_div_4_Template, 6, 1, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.pagePrevious);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.pageNext);\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"layout-topbar-menu-mobile-active\": a0\n  };\n};\nconst _c4 = function () {\n  return {\n    \"width\": \"100%\",\n    \"margin-top\": \"20px\"\n  };\n};\nconst _c5 = function () {\n  return [];\n};\nexport class AppGuideComponent extends ComponentBase {\n  constructor(injector, layoutService, guideService, eRef, titleService) {\n    super(injector);\n    this.layoutService = layoutService;\n    this.guideService = guideService;\n    this.eRef = eRef;\n    this.titleService = titleService;\n    this.userInfo = {};\n    this.projectInfo = {};\n    this.projectTitle = \"\";\n    this.pageInfo = {};\n    this.listPages = [];\n    this.listSortedPages = [];\n    this.currentPath = \"\";\n    this.items = [];\n    this.listItems = [];\n    this.tabindex = 0;\n    this.styleActive = {\n      'outline': '0 none',\n      'outline-offset': 0,\n      'box-shadow': '0 0 0 0.2rem #C7D2FE'\n    };\n    this.home = null;\n    this.pathItems = [];\n    this.pageTitle = null;\n    this.listPageChildren = [];\n    this.pageNext = null;\n    this.pagePrevious = null;\n    this.pageContent = null;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    this.userInfo = this.sessionService.userInfo;\n    this.currentPath = this.router.url.replace(\"/docs\", \"\");\n    this.init();\n  }\n  logout() {\n    let me = this;\n    this.messageCommonService.onload();\n    setTimeout(() => {\n      localStorage.clear();\n      me.messageCommonService.offload();\n      window.location.hash = '/login';\n    }, 500);\n  }\n  init() {\n    this.getProjectInfo();\n  }\n  getProjectInfo() {\n    let me = this;\n    me.messageCommonService.onload();\n    this.guideService.getProjectInfo({}, response => {\n      // console.log(response)\n      me.projectInfo = response;\n      me.projectTitle = me.projectInfo.title[\"vi\"];\n      // console.log(me.projectTitle);\n      me.pageTitle = me.projectTitle;\n      this.titleService.setTitle(this.pageTitle);\n      me.getListPage();\n    }, null, type => {\n      if (type === \"error\") {\n        me.messageCommonService.offload();\n      }\n    });\n  }\n  getListPage() {\n    let me = this;\n    me.messageCommonService.onload();\n    this.guideService.getListPage({}, response => {\n      me.listPages = response;\n      me.handleListPage();\n      if (me.currentPath != \"\") {\n        me.getPageInfo();\n      } else {\n        me.goPageHome();\n        me.messageCommonService.offload();\n      }\n    }, null, type => {\n      if (type === \"error\") {\n        me.messageCommonService.offload();\n      }\n    });\n  }\n  handleListPage() {\n    this.sortPage();\n  }\n  sortPage() {\n    let tree = {\n      \"root\": []\n    };\n    let me = this;\n    this.listPages.forEach(page => {\n      if (page.parentId == null) {\n        tree[\"root\"].push(page);\n      } else {\n        if (page.parentId in tree) {\n          tree[page.parentId].push(page);\n        } else {\n          tree[page.parentId] = [page];\n        }\n      }\n    });\n    Object.keys(tree).forEach(parentKey => {\n      let listNode = tree[parentKey];\n      let arrSort = [];\n      let firstNodes = listNode.filter(el => el.previous == null);\n      firstNodes.forEach(node => {\n        let n = node;\n        while (n != null) {\n          arrSort.push(n);\n          if (n.next != null) {\n            n = listNode[listNode.findIndex(el => el.id == n.next)];\n          } else {\n            n = null;\n          }\n        }\n      });\n      tree[parentKey] = arrSort;\n    });\n    tree[\"root\"].forEach(node => {\n      me.addListSortAndLoadChilden(null, node, tree);\n    });\n    this.items = [...this.items];\n    this.tabindex = this.listSortedPages[0].id;\n  }\n  addListSortAndLoadChilden(parent, node, tree) {\n    let me = this;\n    me.listSortedPages.push(node);\n    let mItem = {\n      id: node.id,\n      label: node.title[\"vi\"],\n      routerLink: [`/docs${node.path}`],\n      tabindex: node.id,\n      title: node.title[\"vi\"],\n      command(event) {\n        me.handleClickMenu(event, 0);\n      }\n    };\n    me.listItems.push(mItem);\n    if (node.id in tree) {\n      tree[node.id].forEach(n => {\n        me.addListSortAndLoadChilden(mItem, n, tree);\n      });\n    }\n    if (parent != null) {\n      if ((parent.items || []).length == 0) {\n        parent.items = [mItem];\n      } else {\n        parent.items.push(mItem);\n      }\n    } else {\n      me.items.push(mItem);\n    }\n  }\n  handleClickMenu(event, type) {\n    let item = event.item;\n    if ((item.routerLink || []).length == 0) return;\n    this.currentPath = item.routerLink[0].replace(\"/docs\", \"\");\n    this.router.navigate(item.routerLink);\n    if (type == 0) {\n      //menu\n      this.getPageInfo();\n      item.style = this.styleActive;\n    } else {\n      //breadcrumb\n      if (this.currentPath != \"\") {\n        this.getPageInfo();\n      } else {\n        this.goPageHome();\n      }\n    }\n  }\n  clearStyleActive() {\n    this.listItems.forEach(el => {\n      el.style = null;\n      el.expanded = false;\n    });\n  }\n  getPageInfo() {\n    let me = this;\n    me.pageTitle = me.projectTitle;\n    me.messageCommonService.onload();\n    this.guideService.getPageInfo(this.currentPath, \"vi\", \"\", response => {\n      me.pageInfo = response;\n      me.pageTitle = me.pageInfo.title[\"vi\"];\n      this.titleService.setTitle(this.pageTitle);\n      me.handlePageInfo();\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  handlePageInfo() {\n    let me = this;\n    this.clearStyleActive();\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/docs'],\n      command(event) {\n        me.handleClickMenu(event, 1);\n      }\n    };\n    this.pageContent = null;\n    if (this.pageInfo.contentHtml != null) {\n      this.pageContent = this.pageInfo.contentHtml[\"vi\"];\n    }\n    let element = this.eRef.nativeElement.querySelector(\"#page-content\");\n    if (element != null) {\n      element.innerHTML = this.pageContent;\n    }\n    this.pathItems = [];\n    let node = this.listPages[this.listPages.findIndex(el => el.id == me.pageInfo.id)];\n    let indexSortPage = this.listItems.findIndex(el => el.id == node.id);\n    let firstNode = true;\n    while (node != null) {\n      this.pathItems.push({\n        label: node.title[\"vi\"],\n        routerLink: firstNode ? null : [`/docs${node.path}`],\n        command(event) {\n          me.handleClickMenu(event, 1);\n        },\n        tooltip: node.title[\"vi\"].length > 50 ? node.title[\"vi\"] : null\n      });\n      if (firstNode == true) {\n        let item = this.listItems[this.listItems.findIndex(el => el.id == node.id)];\n        item.style = this.styleActive;\n      }\n      this.listItems[this.listItems.findIndex(el => el.id == node.id)].expanded = true;\n      firstNode = false;\n      if (node.parentId != null) {\n        node = this.listPages[this.listPages.findIndex(el => el.id == node.parentId)];\n      } else {\n        node = null;\n      }\n    }\n    this.pathItems.reverse();\n    if (indexSortPage === 0) this.pagePrevious = null;else this.pagePrevious = this.listItems[indexSortPage - 1];\n    if (indexSortPage === this.listItems.length - 1) this.pageNext = null;else this.pageNext = this.listItems[indexSortPage + 1];\n    this.listPageChildren = this.listItems[indexSortPage].items || [];\n    this.items = [...this.items];\n  }\n  goPageHome() {\n    this.clearStyleActive();\n    this.pageContent = null;\n    this.pageTitle = this.projectTitle;\n    this.home = null;\n    this.pathItems = [];\n    this.pageNext = null;\n    this.pagePrevious = null;\n    this.listPageChildren = [...this.items];\n    this.items = [...this.items];\n  }\n  static {\n    this.ɵfac = function AppGuideComponent_Factory(t) {\n      return new (t || AppGuideComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.GuideService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Title));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppGuideComponent,\n      selectors: [[\"app-guide\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 29,\n      vars: 21,\n      consts: [[1, \"layout-topbar\", \"header-cmp\"], [\"routerLink\", \"\", 1, \"layout-topbar-logo\"], [\"src\", \"assets/images/m2m.png\", \"alt\", \"logo\"], [2, \"font-size\", \"xx-large\", \"color\", \"white\", \"position\", \"absolute\", \"top\", \"50%\", \"left\", \"50%\", \"transform\", \"translate(-50%, -50%)\"], [1, \"layout-topbar-menu\", 3, \"ngClass\"], [\"topbarmenu\", \"\"], [\"class\", \"ml-2\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-start\", 2, \"margin-top\", \"56px\", \"min-height\", \"100vh\"], [2, \"width\", \"350px\", \"min-width\", \"350px\", \"min-height\", \"100vh\"], [1, \"flex\", \"align-items-center\"], [\"routerLink\", \"\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Back\", 1, \"p-button-outlined\", \"p-button-secondary\"], [2, \"font-size\", \"14px\", \"text-align\", \"center\", \"font-weight\", \"bold\", \"text-transform\", \"uppercase\", \"padding\", \"12px 8px\"], [\"styleClass\", \"m-0 bold \", \"type\", \"solid\", 3, \"align\"], [3, \"model\", \"multiple\", \"tabindex\"], [1, \"flex-grow-1\", 2, \"min-height\", \"100vh\"], [1, \"w-full\", \"p-2\", 2, \"box-sizing\", \"border-box\", \"min-height\", \"calc(100vh - 56px)\"], [1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"id\", \"page-content\", 1, \"bg-white\", \"p-2\", \"border-round\", \"mt-4\", \"se-wrapper\", \"se-wrapper-inner\", \"se-wrapper-wysiwyg\", \"sun-editor-editable\"], [\"class\", \"bg-white p-2 border-round mt-4\", 4, \"ngIf\"], [\"class\", \"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round mt-4\", 4, \"ngIf\"], [1, \"ml-2\"], [1, \"cursor-poiter\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [2, \"display\", \"inline-block\", \"max-width\", \"150px\", \"overflow\", \"hidden\", \"text-overflow\", \"ellipsis\", \"white-space\", \"nowrap\", 3, \"pTooltip\"], [\"session\", \"\"], [\"class\", \"text-black-alpha-90\", \"routerLinkActive\", \"router-link-active\", 3, \"routerLink\", 4, \"ngIf\"], [\"routerLinkActive\", \"router-link-active\", 1, \"text-black-alpha-90\", 3, \"routerLink\"], [1, \"p-2\", \"hover:surface-300\"], [1, \"pi\", \"pi-lock\"], [\"routerLinkActive\", \"router-link-active\", 1, \"text-black-alpha-90\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-sign-out\"], [1, \"pi\", \"pi-user-edit\"], [1, \"bg-white\", \"p-2\", \"border-round\", \"mt-4\"], [\"class\", \"p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-2\"], [2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\", \"mt-4\"], [4, \"ngIf\"], [\"style\", \"text-align: right;\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\", \"mb-3\", 2, \"width\", \"280px\"], [1, \"pi\", \"pi-arrow-left\", \"mr-4\"], [\"className\", \"ml-12px\"], [\"styleClass\", \"p-button-info\", 3, \"click\"], [2, \"text-align\", \"right\"], [1, \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\", \"mb-3\", 2, \"width\", \"280px\"], [1, \"pi\", \"pi-arrow-right\", \"ml-4\"]],\n      template: function AppGuideComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"a\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4, 5);\n          i0.ɵɵelement(7, \"choose-language\");\n          i0.ɵɵtemplate(8, AppGuideComponent_div_8_Template, 17, 9, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"a\", 10);\n          i0.ɵɵelement(13, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 12);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(16, \"p-divider\", 13)(17, \"p-panelMenu\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"div\", 16)(20, \"div\", 17)(21, \"div\", 18)(22, \"div\", 19);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"p-breadcrumb\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"div\", 22);\n          i0.ɵɵtemplate(27, AppGuideComponent_div_27_Template, 3, 1, \"div\", 23);\n          i0.ɵɵtemplate(28, AppGuideComponent_div_28_Template, 5, 2, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.titlepage.m2SubscriptionManagementSystem\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c3, ctx.layoutService.state.profileSidebarVisible));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.projectTitle);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"align\", \"center\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(19, _c4));\n          i0.ɵɵproperty(\"model\", ctx.items)(\"multiple\", false)(\"tabindex\", ctx.tabindex);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.pageTitle);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.pathItems)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.pageContent == null ? \"hidden\" : \"\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.listPageChildren || i0.ɵɵpureFunction0(20, _c5)).length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.pagePrevious != null || ctx.pageNext != null);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.RouterLink, i5.RouterLinkActive, i6.ButtonDirective, i6.Button, i7.ChooseLanguageComponent, i8.Ripple, i9.OverlayPanel, i10.Tooltip, i11.PanelMenu, i12.Divider, i13.Breadcrumb],\n      styles: [\".p-menuitem-link[_ngcontent-%COMP%]{\\n        display: flex;\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r5", "tranService", "translate", "ɵɵlistener", "AppGuideComponent_div_8_Template_div_click_1_listener", "$event", "ɵɵrestoreView", "_r7", "_r4", "ɵɵreference", "ɵɵresetView", "toggle", "ɵɵtemplate", "AppGuideComponent_div_8_a_8_Template", "AppGuideComponent_div_8_Template_a_click_13_listener", "ctx_r8", "ɵɵnextContext", "logout", "ctx_r1", "userInfo", "fullName", "length", "ɵɵtextInterpolate", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction1", "_c1", "PERMISSIONS", "PROFILE", "VIEW", "_c2", "AppGuideComponent_div_27_li_2_Template_span_click_1_listener", "restoredCtx", "_r12", "page_r10", "$implicit", "ctx_r11", "handleClickMenu", "item", "label", "AppGuideComponent_div_27_li_2_Template", "ctx_r2", "listPageChildren", "AppGuideComponent_div_28_div_2_Template_p_button_click_4_listener", "_r16", "ctx_r15", "pagePrevious", "ctx_r13", "AppGuideComponent_div_28_div_4_Template_p_button_click_4_listener", "_r18", "ctx_r17", "pageNext", "ctx_r14", "AppGuideComponent_div_28_div_2_Template", "AppGuideComponent_div_28_div_4_Template", "ctx_r3", "AppGuideComponent", "constructor", "injector", "layoutService", "guideService", "eRef", "titleService", "projectInfo", "projectTitle", "pageInfo", "listPages", "listSortedPages", "currentPath", "items", "listItems", "tabindex", "styleActive", "home", "pathItems", "pageTitle", "pageContent", "ngOnInit", "sessionService", "router", "url", "replace", "init", "me", "messageCommonService", "onload", "setTimeout", "localStorage", "clear", "offload", "window", "location", "hash", "getProjectInfo", "response", "title", "setTitle", "getListPage", "type", "handleListPage", "getPageInfo", "goPageHome", "sortPage", "tree", "for<PERSON>ach", "page", "parentId", "push", "Object", "keys", "parent<PERSON><PERSON>", "listNode", "arrSort", "firstNodes", "filter", "el", "previous", "node", "n", "next", "findIndex", "id", "addListSortAndLoadChilden", "parent", "mItem", "routerLink", "path", "command", "event", "navigate", "style", "clearStyleActive", "expanded", "handlePageInfo", "icon", "contentHtml", "element", "nativeElement", "querySelector", "innerHTML", "indexSortPage", "firstNode", "tooltip", "reverse", "ɵɵdirectiveInject", "Injector", "i1", "LayoutService", "i2", "GuideService", "ElementRef", "i3", "Title", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppGuideComponent_Template", "rf", "ctx", "AppGuideComponent_div_8_Template", "AppGuideComponent_div_27_Template", "AppGuideComponent_div_28_Template", "_c3", "state", "profileSidebarVisible", "ɵɵstyleMap", "_c4", "ɵɵclassMap", "_c5"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\guide\\app.guide.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\pages\\guide\\app.guide.component.html"], "sourcesContent": ["import { Component, ElementRef, Injector, OnInit } from \"@angular/core\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { LayoutService } from \"src/app/service/app.layout.service\";\r\nimport { GuideService } from \"src/app/service/guide/GuideService\";\r\nimport {Title} from \"@angular/platform-browser\";\r\nimport {CONSTANTS} from \"../../../service/comon/constants\";\r\n\r\n@Component({\r\n    selector: \"app-guide\",\r\n    templateUrl: \"./app.guide.component.html\",\r\n})\r\nexport class AppGuideComponent extends ComponentBase implements OnInit {\r\n    userInfo: any = {};\r\n    projectInfo: any = {};\r\n    projectTitle: string = \"\";\r\n    pageInfo: any = {};\r\n    listPages: Array<any> = [];\r\n    listSortedPages: Array<any> = [];\r\n    currentPath: string = \"\";\r\n    items: MenuItem[] = [];\r\n    listItems: MenuItem[] = [];\r\n    tabindex: number = 0;\r\n    styleActive = {\r\n        'outline': '0 none',\r\n        'outline-offset': 0,\r\n        'box-shadow': '0 0 0 0.2rem #C7D2FE'\r\n    };\r\n    home: MenuItem = null;\r\n    pathItems: MenuItem[] = [];\r\n    pageTitle: string = null;\r\n    listPageChildren: MenuItem[] = [];\r\n    pageNext: MenuItem = null;\r\n    pagePrevious: MenuItem = null;\r\n    pageContent: string = null;\r\n    constructor(injector: Injector, public layoutService: LayoutService, private guideService: GuideService, private eRef: ElementRef, private titleService:Title) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        this.userInfo = this.sessionService.userInfo;\r\n        this.currentPath = this.router.url.replace(\"/docs\", \"\");\r\n        this.init();\r\n    }\r\n\r\n    logout(){\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        setTimeout(()=>{\r\n            localStorage.clear();\r\n            me.messageCommonService.offload();\r\n            window.location.hash = '/login';\r\n        }, 500)\r\n    }\r\n\r\n    init(){\r\n        this.getProjectInfo();\r\n    }\r\n\r\n    getProjectInfo(){\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.guideService.getProjectInfo({}, (response)=>{\r\n            // console.log(response)\r\n            me.projectInfo = response;\r\n            me.projectTitle = me.projectInfo.title[\"vi\"];\r\n            // console.log(me.projectTitle);\r\n            me.pageTitle = me.projectTitle;\r\n            this.titleService.setTitle(this.pageTitle)\r\n            me.getListPage();\r\n        }, null, (type)=>{\r\n            if(type === \"error\"){\r\n                me.messageCommonService.offload();\r\n            }\r\n        })\r\n    }\r\n\r\n    getListPage(){\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.guideService.getListPage({}, (response)=> {\r\n            me.listPages = response;\r\n            me.handleListPage();\r\n            if(me.currentPath != \"\"){\r\n                me.getPageInfo()\r\n            }else{\r\n                me.goPageHome();\r\n                me.messageCommonService.offload();\r\n            }\r\n        }, null, (type)=>{\r\n            if(type === \"error\"){\r\n                me.messageCommonService.offload();\r\n            }\r\n        })\r\n    }\r\n\r\n    handleListPage(){\r\n        this.sortPage();\r\n    }\r\n\r\n    sortPage(){\r\n        let tree = {\r\n            \"root\": []\r\n        };\r\n        let me = this;\r\n        this.listPages.forEach(page => {\r\n            if(page.parentId == null){\r\n                tree[\"root\"].push(page);\r\n            }else {\r\n                if(page.parentId in tree){\r\n                    tree[page.parentId].push(page);\r\n                }else{\r\n                    tree[page.parentId] = [page];\r\n                }\r\n            }\r\n        });\r\n        Object.keys(tree).forEach(parentKey => {\r\n            let listNode = tree[parentKey];\r\n            let arrSort = [];\r\n            let firstNodes = listNode.filter(el => el.previous == null);\r\n            firstNodes.forEach(node => {\r\n                let n = node;\r\n                while (n != null) {\r\n                    arrSort.push(n);\r\n                    if(n.next != null){\r\n                        n = listNode[listNode.findIndex(el => el.id == n.next)];\r\n                    }else{\r\n                        n = null;\r\n                    }\r\n                }\r\n            });\r\n            tree[parentKey] = arrSort;\r\n        })\r\n        tree[\"root\"].forEach(node => {\r\n            me.addListSortAndLoadChilden(null, node, tree);\r\n        });\r\n        this.items = [...this.items]\r\n        this.tabindex = this.listSortedPages[0].id;\r\n    }\r\n\r\n    addListSortAndLoadChilden(parent: MenuItem, node, tree){\r\n        let me = this;\r\n        me.listSortedPages.push(node);\r\n        let mItem: MenuItem = {\r\n            id: node.id,\r\n            label: node.title[\"vi\"],\r\n            routerLink: [`/docs${node.path}`],\r\n            tabindex: node.id,\r\n            title: node.title[\"vi\"],\r\n            command(event) {\r\n                me.handleClickMenu(event,0);\r\n            }\r\n        };\r\n        me.listItems.push(mItem);\r\n        if(node.id in tree){\r\n            tree[node.id].forEach(n => {\r\n                me.addListSortAndLoadChilden(mItem, n, tree);\r\n            })\r\n        }\r\n        if(parent != null){\r\n            if((parent.items || []).length == 0){\r\n                parent.items = [mItem];\r\n            }else{\r\n                parent.items.push(mItem);\r\n            }\r\n        }else{\r\n            me.items.push(mItem);\r\n        }\r\n    }\r\n\r\n    handleClickMenu(event, type: 0 | 1){\r\n        let item: MenuItem = event.item;\r\n        if((item.routerLink || []).length == 0) return;\r\n        this.currentPath = item.routerLink[0].replace(\"/docs\",\"\");\r\n        this.router.navigate(item.routerLink);\r\n        if(type == 0){//menu\r\n            this.getPageInfo();\r\n            item.style = this.styleActive;\r\n        }else{//breadcrumb\r\n            if(this.currentPath != \"\"){\r\n                this.getPageInfo();\r\n            }else{\r\n                this.goPageHome();\r\n            }\r\n        }\r\n    }\r\n\r\n    clearStyleActive(){\r\n        this.listItems.forEach(el => {\r\n            el.style = null;\r\n            el.expanded = false;\r\n        })\r\n    }\r\n\r\n    getPageInfo(){\r\n        let me = this;\r\n        me.pageTitle = me.projectTitle;\r\n        me.messageCommonService.onload();\r\n        this.guideService.getPageInfo(this.currentPath, \"vi\", \"\", (response)=> {\r\n            me.pageInfo = response;\r\n            me.pageTitle = me.pageInfo.title[\"vi\"];\r\n            this.titleService.setTitle(this.pageTitle)\r\n            me.handlePageInfo();\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    handlePageInfo(){\r\n        let me = this;\r\n        this.clearStyleActive();\r\n        this.home = { icon: 'pi pi-home', routerLink: ['/docs'], command(event) {\r\n            me.handleClickMenu(event,1);\r\n        }, };\r\n        this.pageContent = null;\r\n        if(this.pageInfo.contentHtml != null){\r\n            this.pageContent = this.pageInfo.contentHtml[\"vi\"];\r\n        }\r\n        let element:Element = this.eRef.nativeElement.querySelector(\"#page-content\");\r\n        if(element != null){\r\n            element.innerHTML = this.pageContent;\r\n        }\r\n        this.pathItems = [];\r\n        let node = this.listPages[this.listPages.findIndex(el => el.id == me.pageInfo.id)];\r\n        let indexSortPage = this.listItems.findIndex(el => el.id == node.id);\r\n        let firstNode = true;\r\n        while(node != null){\r\n            this.pathItems.push({\r\n                label: node.title[\"vi\"],\r\n                routerLink: firstNode ? null : [`/docs${node.path}`],\r\n                command(event) {\r\n                    me.handleClickMenu(event,1);\r\n                },\r\n                tooltip: node.title[\"vi\"].length > 50 ? node.title[\"vi\"] : null\r\n            });\r\n            if(firstNode == true){\r\n                let item = this.listItems[this.listItems.findIndex(el => el.id == node.id)];\r\n                item.style = this.styleActive;\r\n            }\r\n            this.listItems[this.listItems.findIndex(el => el.id == node.id)].expanded = true;\r\n            firstNode = false;\r\n            if(node.parentId != null){\r\n                node = this.listPages[this.listPages.findIndex(el => el.id == node.parentId)];\r\n            }else{\r\n                node = null;\r\n            }\r\n        }\r\n        this.pathItems.reverse();\r\n        if(indexSortPage === 0) this.pagePrevious = null;\r\n        else this.pagePrevious = this.listItems[indexSortPage - 1];\r\n        if(indexSortPage === this.listItems.length - 1) this.pageNext = null;\r\n        else this.pageNext = this.listItems[indexSortPage + 1];\r\n        this.listPageChildren = this.listItems[indexSortPage].items || [];\r\n        this.items = [...this.items];\r\n    }\r\n\r\n    goPageHome(){\r\n        this.clearStyleActive();\r\n        this.pageContent = null;\r\n        this.pageTitle = this.projectTitle;\r\n        this.home = null;\r\n        this.pathItems = [];\r\n        this.pageNext = null;\r\n        this.pagePrevious = null;\r\n        this.listPageChildren = [...this.items];\r\n        this.items = [...this.items];\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "\r\n<style>\r\n    .p-menuitem-link{\r\n        display: flex;\r\n    }\r\n</style>\r\n<div class=\"layout-topbar header-cmp\">\r\n    <a class=\"layout-topbar-logo\" routerLink=\"\">\r\n        <img src=\"assets/images/m2m.png\" alt=\"logo\">\r\n        <span style=\"font-size: xx-large; color: white; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);\">{{tranService.translate('global.titlepage.m2SubscriptionManagementSystem')}}</span>\r\n    </a>\r\n\r\n    <div #topbarmenu class=\"layout-topbar-menu\" [ngClass]=\"{'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible}\">\r\n        <choose-language></choose-language>\r\n        <div class=\"ml-2\" *ngIf=\"userInfo\">\r\n            <div (click)=\"session.toggle($event)\" class=\"cursor-poiter\"><i class=\"pi pi-fw pi-user\"></i>&nbsp;<span style=\"display: inline-block; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\"\r\n                 [pTooltip]=\"userInfo.fullName.length > 20 ? userInfo.fullName : ''\">{{userInfo.fullName}}</span></div>\r\n            <p-overlayPanel #session>\r\n                <a class=\"text-black-alpha-90\" [routerLink]=\"['/profile']\" routerLinkActive=\"router-link-active\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.PROFILE.VIEW])\">\r\n                    <div class=\"p-2 hover:surface-300\">\r\n                        <i class=\"pi pi-user-edit\"></i>&nbsp;\r\n                        {{tranService.translate(\"login.label.editProfile\")}}\r\n                    </div>\r\n                </a>\r\n                <a class=\"text-black-alpha-90\" [routerLink]=\"['profile/change-password']\" routerLinkActive=\"router-link-active\" >\r\n                    <div class=\"p-2 hover:surface-300\">\r\n                        <i class=\"pi pi-lock\"></i>&nbsp;\r\n                        {{tranService.translate(\"login.label.changePassword\")}}\r\n                    </div>\r\n                </a>\r\n                <a class=\"text-black-alpha-90 cursor-pointer\" (click)=\"logout()\" routerLinkActive=\"router-link-active\" >\r\n                    <div class=\"p-2 hover:surface-300\">\r\n                        <i class=\"pi pi-sign-out\"></i>&nbsp;\r\n                        {{tranService.translate(\"login.label.logout\")}}\r\n                    </div>\r\n                </a>\r\n            </p-overlayPanel>\r\n        </div>\r\n    </div>\r\n</div>\r\n<div class=\"flex flex-row justify-content-between align-items-start\" style=\"margin-top: 56px; min-height: 100vh\">\r\n    <div style=\"width: 350px;min-width: 350px; min-height: 100vh;\">\r\n        <div class=\"flex align-items-center\">\r\n            <a routerLink=\"\">\r\n                <button pButton pRipple type=\"button\" label=\"Back\" class=\"p-button-outlined p-button-secondary\"></button>\r\n            </a>\r\n            <span style=\"font-size: 14px;\r\n        text-align: center;\r\n        font-weight: bold;\r\n        text-transform: uppercase;\r\n        padding: 12px 8px;\">{{projectTitle}}</span>\r\n        </div>\r\n\r\n        <p-divider styleClass=\"m-0 bold \" [align]=\"'center'\" type=\"solid\"></p-divider>\r\n        <p-panelMenu [model]=\"items\" [style]=\"{'width':'100%', 'margin-top': '20px'}\" [multiple]=\"false\" [tabindex]=\"tabindex\"></p-panelMenu>\r\n        <!-- <p-tieredMenu [model]=\"items\"></p-tieredMenu> -->\r\n    </div>\r\n    <div class=\"flex-grow-1\" style=\"min-height: 100vh;\">\r\n        <div class=\"w-full p-2\" style=\"box-sizing: border-box; min-height: calc(100vh - 56px);\">\r\n            <div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n                <div class=\"\">\r\n                    <div class=\"text-xl font-bold mb-1\">{{pageTitle}}</div>\r\n                    <p-breadcrumb class=\"max-w-full col-7\" [model]=\"pathItems\" [home]=\"home\"></p-breadcrumb>\r\n                </div>\r\n                <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n\r\n                </div>\r\n            </div>\r\n            <div id=\"page-content\" class=\"bg-white p-2 border-round mt-4 se-wrapper se-wrapper-inner se-wrapper-wysiwyg sun-editor-editable\" [class]=\"pageContent == null ? 'hidden' : ''\">\r\n\r\n            </div>\r\n            <div class=\"bg-white p-2 border-round mt-4\" *ngIf=\"(listPageChildren || []).length > 0\">\r\n                <ul>\r\n                    <li class=\"p-2\" *ngFor=\"let page of listPageChildren\">\r\n                        <span style=\"cursor: pointer;\" (click)=\"handleClickMenu({item: page}, 1)\">{{page.label}}</span>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round mt-4\" *ngIf=\"pagePrevious != null || pageNext != null\">\r\n                <div>\r\n                    <div *ngIf=\"pagePrevious\" >\r\n                        <div style=\"width: 280px\" class=\"flex flex-row justify-content-start align-items-center mb-3\"><i class=\"pi pi-arrow-left mr-4\"></i><div className=\"ml-12px\"></div></div>\r\n                        <p-button styleClass=\"p-button-info\" (click)=\"handleClickMenu({item: pagePrevious}, 1)\">\r\n                            {{ pagePrevious.label }}\r\n                        </p-button>\r\n                    </div>\r\n                </div>\r\n                <div>\r\n                    <div *ngIf=\"pageNext\" style=\"text-align: right;\">\r\n                        <div style=\"width: 280px\" class=\"flex flex-row justify-content-end align-items-center mb-3\"><div></div><i class=\"pi pi-arrow-right ml-4\"></i></div>\r\n                        <p-button styleClass=\"p-button-info\" (click)=\"handleClickMenu({item: pageNext}, 1)\">\r\n                            {{ pageNext.label }}\r\n                        </p-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n<!--        <div class=\"layout-footer\">-->\r\n<!--            <img src=\"assets/images/m2m.png\" alt=\"Logo\" height=\"40px\" class=\"mr-2\"/>-->\r\n<!--        &lt;!&ndash;    <span class=\"font-medium ml-2\">VNPT Technology</span>&ndash;&gt;-->\r\n<!--        </div>-->\r\n    </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,aAAa,QAAQ,wBAAwB;AAItD,SAAQC,SAAS,QAAO,kCAAkC;;;;;;;;;;;;;;;;;;;;ICY1CC,EAAA,CAAAC,cAAA,YAA2J;IAEnJD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,GAEnC;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAJqBJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAA2B;IAEnBP,EAAA,CAAAQ,SAAA,GAEnC;IAFmCR,EAAA,CAAAS,kBAAA,YAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,iCAEnC;;;;;;;;;;;;IARZZ,EAAA,CAAAC,cAAA,cAAmC;IAC1BD,EAAA,CAAAa,UAAA,mBAAAC,sDAAAC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,GAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAF,GAAA,CAAAG,MAAA,CAAAN,MAAA,CAAsB;IAAA,EAAC;IAAuBf,EAAA,CAAAE,SAAA,YAAgC;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAC,cAAA,eACzB;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrGJ,EAAA,CAAAC,cAAA,+BAAyB;IACrBD,EAAA,CAAAsB,UAAA,IAAAC,oCAAA,gBAKI;IACJvB,EAAA,CAAAC,cAAA,YAAiH;IAEzGD,EAAA,CAAAE,SAAA,aAA0B;IAAAF,EAAA,CAAAG,MAAA,IAE9B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,aAAwG;IAA1DD,EAAA,CAAAa,UAAA,mBAAAW,qDAAA;MAAAxB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAQ,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAAoB,WAAA,CAAAK,MAAA,CAAAE,MAAA,EAAQ;IAAA,EAAC;IAC5D3B,EAAA,CAAAC,cAAA,eAAmC;IAC/BD,EAAA,CAAAE,SAAA,aAA8B;IAAAF,EAAA,CAAAG,MAAA,IAElC;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAlBTJ,EAAA,CAAAQ,SAAA,GAAmE;IAAnER,EAAA,CAAAK,UAAA,aAAAuB,MAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,MAAA,QAAAH,MAAA,CAAAC,QAAA,CAAAC,QAAA,MAAmE;IAAC9B,EAAA,CAAAQ,SAAA,GAAqB;IAArBR,EAAA,CAAAgC,iBAAA,CAAAJ,MAAA,CAAAC,QAAA,CAAAC,QAAA,CAAqB;IAEQ9B,EAAA,CAAAQ,SAAA,GAAuD;IAAvDR,EAAA,CAAAK,UAAA,SAAAuB,MAAA,CAAAK,WAAA,CAAAjC,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAAP,MAAA,CAAA7B,SAAA,CAAAqC,WAAA,CAAAC,OAAA,CAAAC,IAAA,GAAuD;IAM1HtC,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAiC,GAAA,EAA0C;IAEvCvC,EAAA,CAAAQ,SAAA,GAE9B;IAF8BR,EAAA,CAAAS,kBAAA,YAAAmB,MAAA,CAAAjB,WAAA,CAAAC,SAAA,oCAE9B;IAIkCZ,EAAA,CAAAQ,SAAA,GAElC;IAFkCR,EAAA,CAAAS,kBAAA,YAAAmB,MAAA,CAAAjB,WAAA,CAAAC,SAAA,4BAElC;;;;;;IAuCAZ,EAAA,CAAAC,cAAA,aAAsD;IACnBD,EAAA,CAAAa,UAAA,mBAAA2B,6DAAA;MAAA,MAAAC,WAAA,GAAAzC,EAAA,CAAAgB,aAAA,CAAA0B,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAAoB,WAAA,CAAAyB,OAAA,CAAAC,eAAA;QAAAC,IAAA,EAAAJ;MAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAAC3C,EAAA,CAAAG,MAAA,GAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAArBJ,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAgC,iBAAA,CAAAW,QAAA,CAAAK,KAAA,CAAc;;;;;IAHpGhD,EAAA,CAAAC,cAAA,cAAwF;IAEhFD,EAAA,CAAAsB,UAAA,IAAA2B,sCAAA,iBAEK;IACTjD,EAAA,CAAAI,YAAA,EAAK;;;;IAHgCJ,EAAA,CAAAQ,SAAA,GAAmB;IAAnBR,EAAA,CAAAK,UAAA,YAAA6C,MAAA,CAAAC,gBAAA,CAAmB;;;;;;IAOpDnD,EAAA,CAAAC,cAAA,UAA2B;IACuED,EAAA,CAAAE,SAAA,YAAqC;IAA+BF,EAAA,CAAAI,YAAA,EAAM;IACxKJ,EAAA,CAAAC,cAAA,mBAAwF;IAAnDD,EAAA,CAAAa,UAAA,mBAAAuC,kEAAA;MAAApD,EAAA,CAAAgB,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAAoB,WAAA,CAAAkC,OAAA,CAAAR,eAAA;QAAAC,IAAA,EAAAO,OAAA,CAAAC;MAAA,GAAsC,CAAC,CAAC;IAAA,EAAC;IACnFvD,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IADPJ,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,MAAA+C,OAAA,CAAAD,YAAA,CAAAP,KAAA,MACJ;;;;;;IAIJhD,EAAA,CAAAC,cAAA,cAAiD;IAC+CD,EAAA,CAAAE,SAAA,UAAW;IAAsCF,EAAA,CAAAI,YAAA,EAAM;IACnJJ,EAAA,CAAAC,cAAA,mBAAoF;IAA/CD,EAAA,CAAAa,UAAA,mBAAA4C,kEAAA;MAAAzD,EAAA,CAAAgB,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAAoB,WAAA,CAAAuC,OAAA,CAAAb,eAAA;QAAAC,IAAA,EAAAY,OAAA,CAAAC;MAAA,GAAkC,CAAC,CAAC;IAAA,EAAC;IAC/E5D,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAW;;;;IADPJ,EAAA,CAAAQ,SAAA,GACJ;IADIR,EAAA,CAAAS,kBAAA,MAAAoD,OAAA,CAAAD,QAAA,CAAAZ,KAAA,MACJ;;;;;IAdZhD,EAAA,CAAAC,cAAA,cAA2J;IAEnJD,EAAA,CAAAsB,UAAA,IAAAwC,uCAAA,kBAKM;IACV9D,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,UAAK;IACDD,EAAA,CAAAsB,UAAA,IAAAyC,uCAAA,kBAKM;IACV/D,EAAA,CAAAI,YAAA,EAAM;;;;IAdIJ,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAK,UAAA,SAAA2D,MAAA,CAAAT,YAAA,CAAkB;IAQlBvD,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAK,UAAA,SAAA2D,MAAA,CAAAJ,QAAA,CAAc;;;;;;;;;;;;;;;;;AD5ExC,OAAM,MAAOK,iBAAkB,SAAQnE,aAAa;EAuBhDoE,YAAYC,QAAkB,EAASC,aAA4B,EAAUC,YAA0B,EAAUC,IAAgB,EAAUC,YAAkB;IACzJ,KAAK,CAACJ,QAAQ,CAAC;IADoB,KAAAC,aAAa,GAAbA,aAAa;IAAyB,KAAAC,YAAY,GAAZA,YAAY;IAAwB,KAAAC,IAAI,GAAJA,IAAI;IAAsB,KAAAC,YAAY,GAAZA,YAAY;IAtBvJ,KAAA1C,QAAQ,GAAQ,EAAE;IAClB,KAAA2C,WAAW,GAAQ,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,WAAW,GAAG;MACV,SAAS,EAAE,QAAQ;MACnB,gBAAgB,EAAE,CAAC;MACnB,YAAY,EAAE;KACjB;IACD,KAAAC,IAAI,GAAa,IAAI;IACrB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,SAAS,GAAW,IAAI;IACxB,KAAAjC,gBAAgB,GAAe,EAAE;IACjC,KAAAS,QAAQ,GAAa,IAAI;IACzB,KAAAL,YAAY,GAAa,IAAI;IAC7B,KAAA8B,WAAW,GAAW,IAAI;IA0OP,KAAAtF,SAAS,GAAGA,SAAS;EAvOxC;EAEAuF,QAAQA,CAAA;IACJ,IAAI,CAACzD,QAAQ,GAAG,IAAI,CAAC0D,cAAc,CAAC1D,QAAQ;IAC5C,IAAI,CAACgD,WAAW,GAAG,IAAI,CAACW,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IACvD,IAAI,CAACC,IAAI,EAAE;EACf;EAEAhE,MAAMA,CAAA;IACF,IAAIiE,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,oBAAoB,CAACC,MAAM,EAAE;IAClCC,UAAU,CAAC,MAAI;MACXC,YAAY,CAACC,KAAK,EAAE;MACpBL,EAAE,CAACC,oBAAoB,CAACK,OAAO,EAAE;MACjCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACnC,CAAC,EAAE,GAAG,CAAC;EACX;EAEAV,IAAIA,CAAA;IACA,IAAI,CAACW,cAAc,EAAE;EACzB;EAEAA,cAAcA,CAAA;IACV,IAAIV,EAAE,GAAG,IAAI;IACbA,EAAE,CAACC,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACzB,YAAY,CAACiC,cAAc,CAAC,EAAE,EAAGC,QAAQ,IAAG;MAC7C;MACAX,EAAE,CAACpB,WAAW,GAAG+B,QAAQ;MACzBX,EAAE,CAACnB,YAAY,GAAGmB,EAAE,CAACpB,WAAW,CAACgC,KAAK,CAAC,IAAI,CAAC;MAC5C;MACAZ,EAAE,CAACR,SAAS,GAAGQ,EAAE,CAACnB,YAAY;MAC9B,IAAI,CAACF,YAAY,CAACkC,QAAQ,CAAC,IAAI,CAACrB,SAAS,CAAC;MAC1CQ,EAAE,CAACc,WAAW,EAAE;IACpB,CAAC,EAAE,IAAI,EAAGC,IAAI,IAAG;MACb,IAAGA,IAAI,KAAK,OAAO,EAAC;QAChBf,EAAE,CAACC,oBAAoB,CAACK,OAAO,EAAE;;IAEzC,CAAC,CAAC;EACN;EAEAQ,WAAWA,CAAA;IACP,IAAId,EAAE,GAAG,IAAI;IACbA,EAAE,CAACC,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACzB,YAAY,CAACqC,WAAW,CAAC,EAAE,EAAGH,QAAQ,IAAG;MAC1CX,EAAE,CAACjB,SAAS,GAAG4B,QAAQ;MACvBX,EAAE,CAACgB,cAAc,EAAE;MACnB,IAAGhB,EAAE,CAACf,WAAW,IAAI,EAAE,EAAC;QACpBe,EAAE,CAACiB,WAAW,EAAE;OACnB,MAAI;QACDjB,EAAE,CAACkB,UAAU,EAAE;QACflB,EAAE,CAACC,oBAAoB,CAACK,OAAO,EAAE;;IAEzC,CAAC,EAAE,IAAI,EAAGS,IAAI,IAAG;MACb,IAAGA,IAAI,KAAK,OAAO,EAAC;QAChBf,EAAE,CAACC,oBAAoB,CAACK,OAAO,EAAE;;IAEzC,CAAC,CAAC;EACN;EAEAU,cAAcA,CAAA;IACV,IAAI,CAACG,QAAQ,EAAE;EACnB;EAEAA,QAAQA,CAAA;IACJ,IAAIC,IAAI,GAAG;MACP,MAAM,EAAE;KACX;IACD,IAAIpB,EAAE,GAAG,IAAI;IACb,IAAI,CAACjB,SAAS,CAACsC,OAAO,CAACC,IAAI,IAAG;MAC1B,IAAGA,IAAI,CAACC,QAAQ,IAAI,IAAI,EAAC;QACrBH,IAAI,CAAC,MAAM,CAAC,CAACI,IAAI,CAACF,IAAI,CAAC;OAC1B,MAAK;QACF,IAAGA,IAAI,CAACC,QAAQ,IAAIH,IAAI,EAAC;UACrBA,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC;SACjC,MAAI;UACDF,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,GAAG,CAACD,IAAI,CAAC;;;IAGxC,CAAC,CAAC;IACFG,MAAM,CAACC,IAAI,CAACN,IAAI,CAAC,CAACC,OAAO,CAACM,SAAS,IAAG;MAClC,IAAIC,QAAQ,GAAGR,IAAI,CAACO,SAAS,CAAC;MAC9B,IAAIE,OAAO,GAAG,EAAE;MAChB,IAAIC,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,QAAQ,IAAI,IAAI,CAAC;MAC3DH,UAAU,CAACT,OAAO,CAACa,IAAI,IAAG;QACtB,IAAIC,CAAC,GAAGD,IAAI;QACZ,OAAOC,CAAC,IAAI,IAAI,EAAE;UACdN,OAAO,CAACL,IAAI,CAACW,CAAC,CAAC;UACf,IAAGA,CAAC,CAACC,IAAI,IAAI,IAAI,EAAC;YACdD,CAAC,GAAGP,QAAQ,CAACA,QAAQ,CAACS,SAAS,CAACL,EAAE,IAAIA,EAAE,CAACM,EAAE,IAAIH,CAAC,CAACC,IAAI,CAAC,CAAC;WAC1D,MAAI;YACDD,CAAC,GAAG,IAAI;;;MAGpB,CAAC,CAAC;MACFf,IAAI,CAACO,SAAS,CAAC,GAAGE,OAAO;IAC7B,CAAC,CAAC;IACFT,IAAI,CAAC,MAAM,CAAC,CAACC,OAAO,CAACa,IAAI,IAAG;MACxBlC,EAAE,CAACuC,yBAAyB,CAAC,IAAI,EAAEL,IAAI,EAAEd,IAAI,CAAC;IAClD,CAAC,CAAC;IACF,IAAI,CAAClC,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC;IAC5B,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC,CAACsD,EAAE;EAC9C;EAEAC,yBAAyBA,CAACC,MAAgB,EAAEN,IAAI,EAAEd,IAAI;IAClD,IAAIpB,EAAE,GAAG,IAAI;IACbA,EAAE,CAAChB,eAAe,CAACwC,IAAI,CAACU,IAAI,CAAC;IAC7B,IAAIO,KAAK,GAAa;MAClBH,EAAE,EAAEJ,IAAI,CAACI,EAAE;MACXlF,KAAK,EAAE8E,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC;MACvB8B,UAAU,EAAE,CAAC,QAAQR,IAAI,CAACS,IAAI,EAAE,CAAC;MACjCvD,QAAQ,EAAE8C,IAAI,CAACI,EAAE;MACjB1B,KAAK,EAAEsB,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC;MACvBgC,OAAOA,CAACC,KAAK;QACT7C,EAAE,CAAC9C,eAAe,CAAC2F,KAAK,EAAC,CAAC,CAAC;MAC/B;KACH;IACD7C,EAAE,CAACb,SAAS,CAACqC,IAAI,CAACiB,KAAK,CAAC;IACxB,IAAGP,IAAI,CAACI,EAAE,IAAIlB,IAAI,EAAC;MACfA,IAAI,CAACc,IAAI,CAACI,EAAE,CAAC,CAACjB,OAAO,CAACc,CAAC,IAAG;QACtBnC,EAAE,CAACuC,yBAAyB,CAACE,KAAK,EAAEN,CAAC,EAAEf,IAAI,CAAC;MAChD,CAAC,CAAC;;IAEN,IAAGoB,MAAM,IAAI,IAAI,EAAC;MACd,IAAG,CAACA,MAAM,CAACtD,KAAK,IAAI,EAAE,EAAE/C,MAAM,IAAI,CAAC,EAAC;QAChCqG,MAAM,CAACtD,KAAK,GAAG,CAACuD,KAAK,CAAC;OACzB,MAAI;QACDD,MAAM,CAACtD,KAAK,CAACsC,IAAI,CAACiB,KAAK,CAAC;;KAE/B,MAAI;MACDzC,EAAE,CAACd,KAAK,CAACsC,IAAI,CAACiB,KAAK,CAAC;;EAE5B;EAEAvF,eAAeA,CAAC2F,KAAK,EAAE9B,IAAW;IAC9B,IAAI5D,IAAI,GAAa0F,KAAK,CAAC1F,IAAI;IAC/B,IAAG,CAACA,IAAI,CAACuF,UAAU,IAAI,EAAE,EAAEvG,MAAM,IAAI,CAAC,EAAE;IACxC,IAAI,CAAC8C,WAAW,GAAG9B,IAAI,CAACuF,UAAU,CAAC,CAAC,CAAC,CAAC5C,OAAO,CAAC,OAAO,EAAC,EAAE,CAAC;IACzD,IAAI,CAACF,MAAM,CAACkD,QAAQ,CAAC3F,IAAI,CAACuF,UAAU,CAAC;IACrC,IAAG3B,IAAI,IAAI,CAAC,EAAC;MAAC;MACV,IAAI,CAACE,WAAW,EAAE;MAClB9D,IAAI,CAAC4F,KAAK,GAAG,IAAI,CAAC1D,WAAW;KAChC,MAAI;MAAC;MACF,IAAG,IAAI,CAACJ,WAAW,IAAI,EAAE,EAAC;QACtB,IAAI,CAACgC,WAAW,EAAE;OACrB,MAAI;QACD,IAAI,CAACC,UAAU,EAAE;;;EAG7B;EAEA8B,gBAAgBA,CAAA;IACZ,IAAI,CAAC7D,SAAS,CAACkC,OAAO,CAACW,EAAE,IAAG;MACxBA,EAAE,CAACe,KAAK,GAAG,IAAI;MACff,EAAE,CAACiB,QAAQ,GAAG,KAAK;IACvB,CAAC,CAAC;EACN;EAEAhC,WAAWA,CAAA;IACP,IAAIjB,EAAE,GAAG,IAAI;IACbA,EAAE,CAACR,SAAS,GAAGQ,EAAE,CAACnB,YAAY;IAC9BmB,EAAE,CAACC,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACzB,YAAY,CAACwC,WAAW,CAAC,IAAI,CAAChC,WAAW,EAAE,IAAI,EAAE,EAAE,EAAG0B,QAAQ,IAAG;MAClEX,EAAE,CAAClB,QAAQ,GAAG6B,QAAQ;MACtBX,EAAE,CAACR,SAAS,GAAGQ,EAAE,CAAClB,QAAQ,CAAC8B,KAAK,CAAC,IAAI,CAAC;MACtC,IAAI,CAACjC,YAAY,CAACkC,QAAQ,CAAC,IAAI,CAACrB,SAAS,CAAC;MAC1CQ,EAAE,CAACkD,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,EAAE,MAAI;MACTlD,EAAE,CAACC,oBAAoB,CAACK,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA4C,cAAcA,CAAA;IACV,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAACgD,gBAAgB,EAAE;IACvB,IAAI,CAAC1D,IAAI,GAAG;MAAE6D,IAAI,EAAE,YAAY;MAAET,UAAU,EAAE,CAAC,OAAO,CAAC;MAAEE,OAAOA,CAACC,KAAK;QAClE7C,EAAE,CAAC9C,eAAe,CAAC2F,KAAK,EAAC,CAAC,CAAC;MAC/B;IAAC,CAAG;IACJ,IAAI,CAACpD,WAAW,GAAG,IAAI;IACvB,IAAG,IAAI,CAACX,QAAQ,CAACsE,WAAW,IAAI,IAAI,EAAC;MACjC,IAAI,CAAC3D,WAAW,GAAG,IAAI,CAACX,QAAQ,CAACsE,WAAW,CAAC,IAAI,CAAC;;IAEtD,IAAIC,OAAO,GAAW,IAAI,CAAC3E,IAAI,CAAC4E,aAAa,CAACC,aAAa,CAAC,eAAe,CAAC;IAC5E,IAAGF,OAAO,IAAI,IAAI,EAAC;MACfA,OAAO,CAACG,SAAS,GAAG,IAAI,CAAC/D,WAAW;;IAExC,IAAI,CAACF,SAAS,GAAG,EAAE;IACnB,IAAI2C,IAAI,GAAG,IAAI,CAACnD,SAAS,CAAC,IAAI,CAACA,SAAS,CAACsD,SAAS,CAACL,EAAE,IAAIA,EAAE,CAACM,EAAE,IAAItC,EAAE,CAAClB,QAAQ,CAACwD,EAAE,CAAC,CAAC;IAClF,IAAImB,aAAa,GAAG,IAAI,CAACtE,SAAS,CAACkD,SAAS,CAACL,EAAE,IAAIA,EAAE,CAACM,EAAE,IAAIJ,IAAI,CAACI,EAAE,CAAC;IACpE,IAAIoB,SAAS,GAAG,IAAI;IACpB,OAAMxB,IAAI,IAAI,IAAI,EAAC;MACf,IAAI,CAAC3C,SAAS,CAACiC,IAAI,CAAC;QAChBpE,KAAK,EAAE8E,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC;QACvB8B,UAAU,EAAEgB,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQxB,IAAI,CAACS,IAAI,EAAE,CAAC;QACpDC,OAAOA,CAACC,KAAK;UACT7C,EAAE,CAAC9C,eAAe,CAAC2F,KAAK,EAAC,CAAC,CAAC;QAC/B,CAAC;QACDc,OAAO,EAAEzB,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC,CAACzE,MAAM,GAAG,EAAE,GAAG+F,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC,GAAG;OAC9D,CAAC;MACF,IAAG8C,SAAS,IAAI,IAAI,EAAC;QACjB,IAAIvG,IAAI,GAAG,IAAI,CAACgC,SAAS,CAAC,IAAI,CAACA,SAAS,CAACkD,SAAS,CAACL,EAAE,IAAIA,EAAE,CAACM,EAAE,IAAIJ,IAAI,CAACI,EAAE,CAAC,CAAC;QAC3EnF,IAAI,CAAC4F,KAAK,GAAG,IAAI,CAAC1D,WAAW;;MAEjC,IAAI,CAACF,SAAS,CAAC,IAAI,CAACA,SAAS,CAACkD,SAAS,CAACL,EAAE,IAAIA,EAAE,CAACM,EAAE,IAAIJ,IAAI,CAACI,EAAE,CAAC,CAAC,CAACW,QAAQ,GAAG,IAAI;MAChFS,SAAS,GAAG,KAAK;MACjB,IAAGxB,IAAI,CAACX,QAAQ,IAAI,IAAI,EAAC;QACrBW,IAAI,GAAG,IAAI,CAACnD,SAAS,CAAC,IAAI,CAACA,SAAS,CAACsD,SAAS,CAACL,EAAE,IAAIA,EAAE,CAACM,EAAE,IAAIJ,IAAI,CAACX,QAAQ,CAAC,CAAC;OAChF,MAAI;QACDW,IAAI,GAAG,IAAI;;;IAGnB,IAAI,CAAC3C,SAAS,CAACqE,OAAO,EAAE;IACxB,IAAGH,aAAa,KAAK,CAAC,EAAE,IAAI,CAAC9F,YAAY,GAAG,IAAI,CAAC,KAC5C,IAAI,CAACA,YAAY,GAAG,IAAI,CAACwB,SAAS,CAACsE,aAAa,GAAG,CAAC,CAAC;IAC1D,IAAGA,aAAa,KAAK,IAAI,CAACtE,SAAS,CAAChD,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC6B,QAAQ,GAAG,IAAI,CAAC,KAChE,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACmB,SAAS,CAACsE,aAAa,GAAG,CAAC,CAAC;IACtD,IAAI,CAAClG,gBAAgB,GAAG,IAAI,CAAC4B,SAAS,CAACsE,aAAa,CAAC,CAACvE,KAAK,IAAI,EAAE;IACjE,IAAI,CAACA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC;EAChC;EAEAgC,UAAUA,CAAA;IACN,IAAI,CAAC8B,gBAAgB,EAAE;IACvB,IAAI,CAACvD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACX,YAAY;IAClC,IAAI,CAACS,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACvB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACL,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC2B,KAAK,CAAC;IACvC,IAAI,CAACA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC;EAChC;;;uBA9PSb,iBAAiB,EAAAjE,EAAA,CAAAyJ,iBAAA,CAAAzJ,EAAA,CAAA0J,QAAA,GAAA1J,EAAA,CAAAyJ,iBAAA,CAAAE,EAAA,CAAAC,aAAA,GAAA5J,EAAA,CAAAyJ,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAA9J,EAAA,CAAAyJ,iBAAA,CAAAzJ,EAAA,CAAA+J,UAAA,GAAA/J,EAAA,CAAAyJ,iBAAA,CAAAO,EAAA,CAAAC,KAAA;IAAA;EAAA;;;YAAjBhG,iBAAiB;MAAAiG,SAAA;MAAAC,QAAA,GAAAnK,EAAA,CAAAoK,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCN9B1K,EAAA,CAAAC,cAAA,aAAsC;UAE9BD,EAAA,CAAAE,SAAA,aAA4C;UAC5CF,EAAA,CAAAC,cAAA,cAA4H;UAAAD,EAAA,CAAAG,MAAA,GAA4E;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAGnNJ,EAAA,CAAAC,cAAA,gBAAwI;UACpID,EAAA,CAAAE,SAAA,sBAAmC;UACnCF,EAAA,CAAAsB,UAAA,IAAAsJ,gCAAA,kBAuBM;UACV5K,EAAA,CAAAI,YAAA,EAAM;UAEVJ,EAAA,CAAAC,cAAA,aAAiH;UAIjGD,EAAA,CAAAE,SAAA,kBAAyG;UAC7GF,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,gBAIgB;UAAAD,EAAA,CAAAG,MAAA,IAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAG3CJ,EAAA,CAAAE,SAAA,qBAA8E;UAGlFF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAoD;UAIAD,EAAA,CAAAG,MAAA,IAAa;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACvDJ,EAAA,CAAAE,SAAA,wBAAwF;UAC5FF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,eAEM;UACVF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,eAEM;UACNF,EAAA,CAAAsB,UAAA,KAAAuJ,iCAAA,kBAMM;UACN7K,EAAA,CAAAsB,UAAA,KAAAwJ,iCAAA,kBAiBM;UACV9K,EAAA,CAAAI,YAAA,EAAM;;;UAvFsHJ,EAAA,CAAAQ,SAAA,GAA4E;UAA5ER,EAAA,CAAAgC,iBAAA,CAAA2I,GAAA,CAAAhK,WAAA,CAAAC,SAAA,oDAA4E;UAGhKZ,EAAA,CAAAQ,SAAA,GAA2F;UAA3FR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAkC,eAAA,KAAA6I,GAAA,EAAAJ,GAAA,CAAAvG,aAAA,CAAA4G,KAAA,CAAAC,qBAAA,EAA2F;UAEhHjL,EAAA,CAAAQ,SAAA,GAAc;UAAdR,EAAA,CAAAK,UAAA,SAAAsK,GAAA,CAAA9I,QAAA,CAAc;UAoCb7B,EAAA,CAAAQ,SAAA,GAAgB;UAAhBR,EAAA,CAAAgC,iBAAA,CAAA2I,GAAA,CAAAlG,YAAA,CAAgB;UAGFzE,EAAA,CAAAQ,SAAA,GAAkB;UAAlBR,EAAA,CAAAK,UAAA,mBAAkB;UACvBL,EAAA,CAAAQ,SAAA,GAAgD;UAAhDR,EAAA,CAAAkL,UAAA,CAAAlL,EAAA,CAAAM,eAAA,KAAA6K,GAAA,EAAgD;UAAhEnL,EAAA,CAAAK,UAAA,UAAAsK,GAAA,CAAA7F,KAAA,CAAe,gCAAA6F,GAAA,CAAA3F,QAAA;UAOoBhF,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAgC,iBAAA,CAAA2I,GAAA,CAAAvF,SAAA,CAAa;UACVpF,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAK,UAAA,UAAAsK,GAAA,CAAAxF,SAAA,CAAmB,SAAAwF,GAAA,CAAAzF,IAAA;UAM+DlF,EAAA,CAAAQ,SAAA,GAA6C;UAA7CR,EAAA,CAAAoL,UAAA,CAAAT,GAAA,CAAAtF,WAAA,yBAA6C;UAGjIrF,EAAA,CAAAQ,SAAA,GAAyC;UAAzCR,EAAA,CAAAK,UAAA,UAAAsK,GAAA,CAAAxH,gBAAA,IAAAnD,EAAA,CAAAM,eAAA,KAAA+K,GAAA,GAAAtJ,MAAA,KAAyC;UAOqB/B,EAAA,CAAAQ,SAAA,GAA8C;UAA9CR,EAAA,CAAAK,UAAA,SAAAsK,GAAA,CAAApH,YAAA,YAAAoH,GAAA,CAAA/G,QAAA,SAA8C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}