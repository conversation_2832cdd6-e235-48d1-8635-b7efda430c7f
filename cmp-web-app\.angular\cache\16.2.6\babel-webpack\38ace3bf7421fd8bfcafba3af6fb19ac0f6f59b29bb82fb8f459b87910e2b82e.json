{"ast": null, "code": "import { EventEmitter } from \"@angular/core\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport { TranslateService } from \"src/app/service/comon/translate.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/overlaypanel\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/checkbox\";\nimport * as i8 from \"primeng/inputnumber\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"@angular/router\";\nimport * as i11 from \"primeng/tooltip\";\nimport * as i12 from \"src/app/service/comon/translate.service\";\nfunction TableVnptComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.labelTable);\n  }\n}\nfunction TableVnptComponent_div_1_div_3_p_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-checkbox\", 17);\n    i0.ɵɵlistener(\"ngModelChange\", function TableVnptComponent_div_1_div_3_p_checkbox_1_Template_p_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r11.customSelectAll = $event);\n    })(\"click\", function TableVnptComponent_div_1_div_3_p_checkbox_1_Template_p_checkbox_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r13.onChangeCustomSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r8.customSelectAll)(\"binary\", true)(\"pTooltip\", ctx_r8.tooltipSelectAll);\n  }\n}\nfunction TableVnptComponent_div_1_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r9.transService.translate(\"global.text.itemselected\"), \" \", ctx_r9.selectItems.length, \"\");\n  }\n}\nfunction TableVnptComponent_div_1_div_3_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TableVnptComponent_div_1_div_3_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      ctx_r14.selectItems = [];\n      return i0.ɵɵresetView(ctx_r14.modelSelected = []);\n    });\n    i0.ɵɵelement(1, \"i\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r10.clearSelected);\n  }\n}\nfunction TableVnptComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, TableVnptComponent_div_1_div_3_p_checkbox_1_Template, 1, 3, \"p-checkbox\", 14);\n    i0.ɵɵtemplate(2, TableVnptComponent_div_1_div_3_span_2_Template, 2, 2, \"span\", 15);\n    i0.ɵɵtemplate(3, TableVnptComponent_div_1_div_3_button_3_Template, 2, 1, \"button\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isUseCustomSelectAll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.options.hasClearSelected && ctx_r6.selectItems.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectItems.length > 0);\n  }\n}\nfunction TableVnptComponent_div_1_div_4_div_1_ng_template_3_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"p-checkbox\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function TableVnptComponent_div_1_div_4_div_1_ng_template_3_li_2_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r22.columnShows = $event);\n    })(\"ngModelChange\", function TableVnptComponent_div_1_div_4_div_1_ng_template_3_li_2_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r24.columnShowChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r20.columnShows)(\"value\", item_r21.key)(\"label\", item_r21.name);\n  }\n}\nfunction TableVnptComponent_div_1_div_4_div_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"ol\", 28);\n    i0.ɵɵtemplate(2, TableVnptComponent_div_1_div_4_div_1_ng_template_3_li_2_Template, 2, 3, \"li\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.columns);\n  }\n}\nfunction TableVnptComponent_div_1_div_4_div_1_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function TableVnptComponent_div_1_div_4_div_1_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      i0.ɵɵnextContext();\n      const _r17 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(_r17.toggle($event));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TableVnptComponent_div_1_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p-overlayPanel\", 23, 24);\n    i0.ɵɵtemplate(3, TableVnptComponent_div_1_div_4_div_1_ng_template_3_Template, 3, 1, \"ng-template\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TableVnptComponent_div_1_div_4_div_1_button_4_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.options.hasShowToggleColumn);\n  }\n}\nfunction TableVnptComponent_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, TableVnptComponent_div_1_div_4_div_1_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.options.hasShowToggleColumn);\n  }\n}\nfunction TableVnptComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8);\n    i0.ɵɵtemplate(2, TableVnptComponent_div_1_div_2_Template, 2, 1, \"div\", 9);\n    i0.ɵɵtemplate(3, TableVnptComponent_div_1_div_3_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TableVnptComponent_div_1_div_4_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.labelTable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.options.hasClearSelected && ctx_r0.selectItems.length > 0 || ctx_r0.isUseCustomSelectAll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.options.hasShowToggleColumn);\n  }\n}\nfunction TableVnptComponent_ng_template_4_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\");\n  }\n}\nfunction TableVnptComponent_ng_template_4_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 37)(1, \"p-tableHeaderCheckbox\", 38);\n    i0.ɵɵlistener(\"click\", function TableVnptComponent_ng_template_4_th_2_Template_p_tableHeaderCheckbox_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.onChangeSelectAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r28.selectionWidth, \"rem\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r28.options.disabledCheckBox);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r28.tableSelectionText);\n  }\n}\nfunction TableVnptComponent_ng_template_4_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r29.transService.translate(\"global.text.stt\"));\n  }\n}\nfunction TableVnptComponent_ng_template_4_th_4_p_sortIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-sortIcon\", 42);\n  }\n  if (rf & 2) {\n    const item_r35 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"field\", item_r35.key);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"min-width\": a0,\n    \"text-align\": a1,\n    \"width\": a2\n  };\n};\nfunction TableVnptComponent_ng_template_4_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_4_th_4_p_sortIcon_2_Template, 1, 1, \"p-sortIcon\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r35 = ctx.$implicit;\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction3(5, _c0, item_r35.size, item_r35.align, item_r35.size));\n    i0.ɵɵproperty(\"pSortableColumn\", item_r35.key);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", item_r35.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r35.isSort);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"width\": a0\n  };\n};\nfunction TableVnptComponent_ng_template_4_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c1, ctx_r31.actionWidth ? ctx_r31.actionWidth : null));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r31.transService.translate(\"global.text.action\"));\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"min-width\": a0\n  };\n};\nfunction TableVnptComponent_ng_template_4_tr_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 44)(2, \"span\", 45);\n    i0.ɵɵtext(3, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(4, _c2, ctx_r32.filterColumnShow(ctx_r32.columns)[0].size));\n    i0.ɵɵattribute(\"colspan\", ctx_r32.getNumberColumnShow());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r32.transService.translate(\"global.text.nodata\"), \" \");\n  }\n}\nfunction TableVnptComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, TableVnptComponent_ng_template_4_th_1_Template, 1, 0, \"th\", 15);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_4_th_2_Template, 4, 4, \"th\", 33);\n    i0.ɵɵtemplate(3, TableVnptComponent_ng_template_4_th_3_Template, 2, 1, \"th\", 34);\n    i0.ɵɵtemplate(4, TableVnptComponent_ng_template_4_th_4_Template, 3, 9, \"th\", 35);\n    i0.ɵɵtemplate(5, TableVnptComponent_ng_template_4_th_5_Template, 2, 4, \"th\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TableVnptComponent_ng_template_4_tr_6_Template, 5, 6, \"tr\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRowDraggable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.options.hasShowChoose);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.options.hasShowIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filterColumnShow(ctx_r2.columns));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.options.action);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.checkEmpty());\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelement(1, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"p-tableCheckbox\", 50);\n    i0.ɵɵlistener(\"click\", function TableVnptComponent_ng_template_5_td_2_Template_p_tableCheckbox_click_1_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.onClickItemCheckbox());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r41 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", data_r38)(\"disabled\", ctx_r41.options.disabledCheckBox);\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r39 = i0.ɵɵnextContext().rowIndex;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i_r39 + 1);\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"border-red-500\": a0\n  };\n};\nfunction TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(data_r38[item_r49.key] = $event);\n    })(\"keydown\", function TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r64 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r64.filterInput($event, item_r49.validate));\n    })(\"blur\", function TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onBlurInput($event, ctx_r66.isFormInvalid(data_r38[item_r49.key], item_r49.validate), data_r38, item_r49.editInputType));\n    })(\"ngModelChange\", function TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.onValueChange($event, data_r38, item_r49));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r52 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(4, _c2, item_r49.size));\n    i0.ɵɵproperty(\"ngModel\", data_r38[item_r49.key])(\"ngClass\", i0.ɵɵpureFunction1(6, _c3, ctx_r52.isFormInvalid(data_r38[item_r49.key], item_r49.validate)));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-inputNumber\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(data_r38[item_r49.key] = $event);\n    })(\"keydown\", function TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.filterInput($event, item_r49.validate));\n    })(\"onBlur\", function TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_onBlur_0_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.onBlurInput($event, ctx_r80.isFormInvalid(data_r38[item_r49.key], item_r49.validate), data_r38, item_r49.editInputType));\n    })(\"ngModelChange\", function TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onValueChange($event, data_r38, item_r49));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r53 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(4, _c2, item_r49.size));\n    i0.ɵɵproperty(\"ngModel\", data_r38[item_r49.key])(\"ngClass\", i0.ɵɵpureFunction1(6, _c3, ctx_r53.isFormInvalid(data_r38[item_r49.key], item_r49.validate)));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r49.validate.messageErrorPattern);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    len: a0\n  };\n};\nfunction TableVnptComponent_ng_template_5_td_4_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r55.tranService.translate(\"global.message.required\", i0.ɵɵpureFunction1(1, _c4, item_r49.validate.maxLength)));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction1(1, _c4, item_r49.validate.maxLength)));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r57.tranService.translate(\"global.message.minLength\", i0.ɵɵpureFunction1(1, _c4, item_r49.validate.minLength)));\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    value: a0\n  };\n};\nfunction TableVnptComponent_ng_template_5_td_4_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r58.tranService.translate(\"global.message.max\", i0.ɵɵpureFunction1(1, _c5, ctx_r58.formatNumber(item_r49.validate.max))));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r59.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction1(1, _c5, ctx_r59.formatNumber(item_r49.validate.min))));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template, 1, 8, \"input\", 54);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template, 1, 8, \"p-inputNumber\", 55);\n    i0.ɵɵtemplate(3, TableVnptComponent_ng_template_5_td_4_div_1_div_3_Template, 2, 1, \"div\", 56);\n    i0.ɵɵtemplate(4, TableVnptComponent_ng_template_5_td_4_div_1_div_4_Template, 2, 3, \"div\", 56);\n    i0.ɵɵtemplate(5, TableVnptComponent_ng_template_5_td_4_div_1_div_5_Template, 2, 3, \"div\", 56);\n    i0.ɵɵtemplate(6, TableVnptComponent_ng_template_5_td_4_div_1_div_6_Template, 2, 3, \"div\", 56);\n    i0.ɵɵtemplate(7, TableVnptComponent_ng_template_5_td_4_div_1_div_7_Template, 2, 3, \"div\", 56);\n    i0.ɵɵtemplate(8, TableVnptComponent_ng_template_5_td_4_div_1_div_8_Template, 2, 3, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext().$implicit;\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r50 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r49.isEditable && item_r49.editInputType === \"string\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r49.isEditable && item_r49.editInputType === \"number\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.isFormInvalid(data_r38[item_r49.key], item_r49.validate, \"pattern\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.isFormInvalid(data_r38[item_r49.key], item_r49.validate, \"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.isFormInvalid(data_r38[item_r49.key], item_r49.validate, \"maxLength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.isFormInvalid(data_r38[item_r49.key], item_r49.validate, \"minLength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.isFormInvalid(data_r38[item_r49.key], item_r49.validate, \"max\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.isFormInvalid(data_r38[item_r49.key], item_r49.validate, \"min\"));\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r100 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵlistener(\"click\", function TableVnptComponent_ng_template_5_td_4_ng_container_2_span_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r100);\n      const item_r49 = i0.ɵɵnextContext(2).$implicit;\n      const data_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.handleClickText(item_r49, data_r38));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleMap(item_r49.style);\n    i0.ɵɵclassMap(item_r49.funcGetClassname ? item_r49.funcGetClassname(data_r38[item_r49.key]) : item_r49.className);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r49.funcConvertText ? item_r49.funcConvertText(data_r38[item_r49.key], data_r38) : data_r38[item_r49.key], \" \");\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_ng_container_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext(2).$implicit;\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleMap(item_r49.style);\n    i0.ɵɵclassMap(item_r49.funcGetClassname ? item_r49.funcGetClassname(data_r38[item_r49.key]) : item_r49.className);\n    i0.ɵɵproperty(\"routerLink\", item_r49.funcGetRouting(data_r38));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r49.funcConvertText ? item_r49.funcConvertText(data_r38[item_r49.key], data_r38) : data_r38[item_r49.key], \" \");\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TableVnptComponent_ng_template_5_td_4_ng_container_2_span_1_Template, 2, 5, \"span\", 60);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_5_td_4_ng_container_2_a_2_Template, 2, 6, \"a\", 61);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r49.funcGetRouting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r49.funcGetRouting);\n  }\n}\nconst _c6 = function (a0, a1, a2) {\n  return {\n    \"min-width\": a0,\n    \"width\": a1,\n    \"text-align\": a2\n  };\n};\nfunction TableVnptComponent_ng_template_5_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtemplate(1, TableVnptComponent_ng_template_5_td_4_div_1_Template, 9, 8, \"div\", 52);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_5_td_4_ng_container_2_Template, 3, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r49 = ctx.$implicit;\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction3(5, _c6, item_r49.size, item_r49.size, item_r49.align));\n    i0.ɵɵproperty(\"pTooltip\", item_r49.isShowTooltip ? item_r49.funcCustomizeToolTip ? item_r49.funcCustomizeToolTip(data_r38[item_r49.key], data_r38) : item_r49.funcConvertText ? item_r49.funcConvertText(data_r38[item_r49.key], data_r38) : data_r38[item_r49.key] : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r49.isEditable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r49.isEditable);\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵlistener(\"click\", function TableVnptComponent_ng_template_5_td_5_span_2_Template_span_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r112);\n      const op_r109 = restoredCtx.$implicit;\n      const data_r38 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r110 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(op_r109.func(data_r38[ctx_r110.fieldId], data_r38));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const op_r109 = ctx.$implicit;\n    i0.ɵɵclassMap(op_r109.icon);\n    i0.ɵɵproperty(\"pTooltip\", op_r109.tooltip);\n  }\n}\nfunction TableVnptComponent_ng_template_5_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 64)(1, \"div\", 65);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_5_td_5_span_2_Template, 1, 3, \"span\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r38 = i0.ɵɵnextContext().$implicit;\n    const ctx_r44 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c1, ctx_r44.actionWidth ? ctx_r44.actionWidth : null));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r44.filterAction(ctx_r44.options.action, data_r38, data_r38[ctx_r44.fieldId]));\n  }\n}\nfunction TableVnptComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 46);\n    i0.ɵɵtemplate(1, TableVnptComponent_ng_template_5_td_1_Template, 2, 0, \"td\", 15);\n    i0.ɵɵtemplate(2, TableVnptComponent_ng_template_5_td_2_Template, 2, 2, \"td\", 15);\n    i0.ɵɵtemplate(3, TableVnptComponent_ng_template_5_td_3_Template, 2, 1, \"td\", 34);\n    i0.ɵɵtemplate(4, TableVnptComponent_ng_template_5_td_4_Template, 3, 9, \"td\", 47);\n    i0.ɵɵtemplate(5, TableVnptComponent_ng_template_5_td_5_Template, 3, 4, \"td\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r39 = ctx.rowIndex;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"pReorderableRow\", ctx_r3.isRowDraggable ? i_r39 : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRowDraggable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.options.hasShowChoose);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.options.hasShowIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filterColumnShow(ctx_r3.columns));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.options.action);\n  }\n}\nfunction TableVnptComponent_ng_template_6_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputNumber\", 71);\n    i0.ɵɵlistener(\"keyup.enter\", function TableVnptComponent_ng_template_6_div_0_Template_p_inputNumber_keyup_enter_3_listener() {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r115 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r115.jumpPage(ctx_r115.pageC));\n    })(\"ngModelChange\", function TableVnptComponent_ng_template_6_div_0_Template_p_inputNumber_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r116);\n      const ctx_r117 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r117.pageCurrent = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r114 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r114.transService.translate(\"global.text.page\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r114.pageCurrent)(\"min\", 1)(\"max\", ctx_r114.getMaxPage());\n  }\n}\nfunction TableVnptComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TableVnptComponent_ng_template_6_div_0_Template, 4, 4, \"div\", 68);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.options.hasShowJumpPage !== false);\n  }\n}\nconst _c7 = function () {\n  return {\n    \"min-width\": \"100%\"\n  };\n};\nexport class TableVnptComponent extends ComponentBase {\n  constructor(transService, injector) {\n    super(injector);\n    this.transService = transService;\n    this.injector = injector;\n    this.selectItems = []; // giá trị được lựa chọn\n    this.selectItemsChange = new EventEmitter();\n    this.blurInputEvent = new EventEmitter();\n    this.pageNumber = 0; // trang hiển thị\n    this.pageSize = 0; // số lượng dòng trên 1 trang\n    this.sort = \"\"; // format sắp xếp\n    this.params = {}; // tham số tìm kiếm\n    this.rowsPerPageOptions = [5, 10, 20, 25, 50]; // danh sách lựa chọn số lượng dòng trên 1 trang\n    this.scrollHeight = 'flex'; //độ cao tối đa của table\n    this.isRowDraggable = false;\n    this.onChangeSelectAllItems = new EventEmitter();\n    this.onChangeSelectItem = new EventEmitter();\n    this.onChangeCustomSelectAllEmmiter = new EventEmitter();\n    this.tableSelectionText = \"\";\n    this.selectionWidth = 4;\n    this.customSelectAll = false;\n    this.customSelectAllChange = new EventEmitter();\n    this.isUseCustomSelectAll = false;\n    this.styleOutLineTable = {}; //vì primeflex dùng !important nên nếu viết 1 số thuộc tính tồn tại trong thẻ chứa input này, nên thêm !important\n    this.clearSelected = this.tranService.translate('global.text.clearSelected');\n    this.dataSetOld = {};\n    this.tooltipSelectAll = \"\";\n  }\n  ngOnInit() {\n    if (!this.options) {\n      this.options = {\n        hasClearSelected: true,\n        hasShowChoose: false,\n        hasShowIndex: false,\n        hasShowToggleColumn: false,\n        hasShowJumpPage: false,\n        action: [],\n        paginator: false\n      };\n    }\n    if (this.options.paginator !== false) {\n      this.options.paginator = true;\n      this.rowFirst = this.pageNumber * this.pageSize;\n      this.pageNumberOld = this.pageNumber;\n      this.pageCurrent = this.pageNumber + 1;\n    }\n    let flagCheckSession = false;\n    if (this.options.hasShowToggleColumn) {\n      let dataSessionTable = localStorage.getItem('dataSessionTable');\n      if (dataSessionTable) {\n        dataSessionTable = JSON.parse(dataSessionTable);\n        if (dataSessionTable[this.sessionService.userInfo.username]) {\n          if (dataSessionTable[this.sessionService.userInfo.username][this.tableId]) {\n            this.columnShows = [...dataSessionTable[this.sessionService.userInfo.username][this.tableId]];\n            flagCheckSession = true;\n          }\n        }\n      }\n    }\n    if (!flagCheckSession) {\n      this.columnShows = this.columns.filter(column => column.isShow).map(column => column.key);\n    }\n    this.selectItemsOld = [...this.selectItems];\n  }\n  isFormInvalid(value, validate, validateCheck) {\n    let invalidRequired = false;\n    let invalidPattern = false;\n    let invalidMaxLength = false;\n    let invalidMinLength = false;\n    let invalidMax = false;\n    let invalidMin = false;\n    // Check if the field is required and if the value is empty\n    if (validate.required && !value) {\n      invalidRequired = true; // Value is required but empty\n    }\n    // Check if the field value matches the regex pattern\n    if (validate.pattern && value) {\n      if (!new RegExp(validate.pattern).test(value.toString())) {\n        invalidPattern = true; // Value doesn't match the pattern\n      }\n    }\n    // Check if the length of the value is greater than maxLength\n    if (validate.maxLength && value) {\n      if (typeof value == 'string') {\n        if (value.length > validate.maxLength) invalidMaxLength = true; // Value is less than min length\n      }\n    }\n    // Check if the length of the value is less than minLength\n    if (validate.minLength && value) {\n      if (typeof value == 'string') {\n        if (value.length < validate.minLength) invalidMinLength = true; // Value is less than min length\n      }\n    }\n    // Check if the value is greater than the maximum allowed value (for numbers)\n    if (value && validate.max && value > validate.max && typeof value == 'number') {\n      invalidMax = true; // Value exceeds max value\n    }\n    // Check if the value is less than the minimum allowed value (for numbers)\n    if (value && validate.min && value < validate.min && typeof value == 'number') {\n      invalidMin = true; // Value is below min value\n    }\n\n    if (validateCheck) {\n      switch (validateCheck) {\n        case 'required':\n          return invalidRequired;\n        case 'pattern':\n          return invalidPattern;\n        case 'minLength':\n          return invalidMinLength;\n        case 'maxLength':\n          return invalidMaxLength;\n        case 'max':\n          return invalidMax;\n        case 'min':\n          return invalidMin;\n        default:\n          return false;\n      }\n    }\n    return invalidRequired || invalidPattern || invalidMinLength || invalidMaxLength || invalidMax || invalidMin;\n  }\n  // Handle value change and trigger any logic, such as saving changes\n  onValueChange(newValue, data, item) {\n    // Handle the change (e.g., update the dataset or trigger a validation)\n    // console.log(`Updated value for ${item.key}: ${newValue}`);\n    data[item.key] = newValue; // Update the model with the new value\n    // Optionally, trigger custom validation logic or update backend here\n  }\n\n  onBlurInput(event, isFormInValid, rowData, editInputType) {\n    let valueEmit;\n    switch (editInputType) {\n      case 'string':\n        {\n          valueEmit = event.target.value;\n          break;\n        }\n      case 'number':\n        {\n          valueEmit = Number(event.target.value.replace(/\\./g, ''));\n          break;\n        }\n    }\n    if (!isFormInValid) {\n      this.blurInputEvent.emit({\n        value: valueEmit,\n        data: rowData\n      });\n    }\n  }\n  ngDoCheck() {\n    let me = this;\n    if (JSON.stringify(this.selectItems) !== JSON.stringify(this.selectItemsOld)) {\n      setTimeout(() => {\n        this.selectItemsChange.emit(this.selectItems);\n        this.selectItemsOld = [...this.selectItems];\n      });\n    }\n    if (JSON.stringify(this.dataSet) !== JSON.stringify(this.dataSetOld)) {\n      this.dataSetOld = {\n        ...this.dataSet\n      };\n      if (!this.options.hasClearSelected) {\n        let idSelecteds = this.selectItems.map(el => el[me.fieldId]);\n        this.modelSelected = this.dataSet.content.filter(el => idSelecteds.includes(el[me.fieldId]));\n      }\n    }\n    if (this.pageNumber != this.pageNumberOld && this.options.paginator) {\n      this.rowFirst = this.pageNumber * this.pageSize;\n      this.pageCurrent = this.pageNumber + 1;\n      this.pageNumberOld = this.pageNumber;\n    }\n    if (this.selectItems.length == 0) {\n      this.modelSelected = [];\n    } else {\n      let valueSelected = this.selectItems.map(el => el[me.fieldId]);\n      this.modelSelected = this.dataSet.content.filter(el => valueSelected.includes(el[me.fieldId]));\n    }\n  }\n  ngAfterViewChecked() {}\n  pageChange(event) {\n    this.pageSize = event.rows;\n    this.pageNumber = event.first / event.rows;\n    this.pageCurrent = this.pageNumber + 1;\n    this.modelSelected = [];\n    if (this.options.hasClearSelected === true) {\n      this.selectItems = [];\n    }\n    this.loadData(this.pageNumber, this.pageSize, this.sort, this.params);\n  }\n  ngOnChanges(changes) {\n    if (changes['customSelectAll']) {\n      this.tooltipSelectAll = this.customSelectAll ? \"Bỏ chọn tất cả\" : \"Chọn tất cả\";\n    }\n    if (changes['dataSet']) {\n      const prev = changes['dataSet'].previousValue;\n      const curr = changes['dataSet'].currentValue;\n      if (prev?.total !== curr?.total) {\n        this.selectItems.length == curr.total && curr.total != 0 ? this.customSelectAll = true : this.customSelectAll = false;\n        this.tooltipSelectAll = this.customSelectAll ? \"Bỏ chọn tất cả\" : \"Chọn tất cả\";\n      }\n    }\n    if (changes['resetPageNumberTrigger'] && changes['resetPageNumberTrigger'].currentValue) {\n      this.resetPageNumber();\n    }\n  }\n  filterColumnShow(columns) {\n    return columns.filter(el => this.columnShows.includes(el.key));\n  }\n  getMaxPage() {\n    if (this.dataSet.total % this.pageSize == 0) {\n      return this.dataSet.total / this.pageSize;\n    } else {\n      return Math.ceil(this.dataSet.total / this.pageSize);\n    }\n  }\n  jumpPage() {\n    this.modelSelected = [];\n    if (this.options.hasClearSelected === true) {\n      this.selectItems = [];\n    }\n    this.pageNumber = this.pageCurrent - 1;\n    this.rowFirst = this.pageNumber * this.pageSize;\n    this.loadData(this.pageNumber, this.pageSize, this.sort, this.params);\n  }\n  handleSort(event) {\n    if (!this.checkSort(event.field)) {\n      return;\n    }\n    if (JSON.stringify(this.oldSort) == JSON.stringify(event)) return;\n    this.oldSort = event;\n    this.modelSelected = [];\n    if (this.options.hasClearSelected === true) {\n      this.selectItems = [];\n    }\n    this.sort = `${event.field},${event.order == 1 ? 'asc' : 'desc'}`;\n    this.loadData(this.pageNumber, this.pageSize, this.sort, this.params);\n  }\n  checkSort(field) {\n    for (let i = 0; i < this.columns.length; i++) {\n      let column = this.columns[i];\n      if (column.key == field) {\n        return column.isSort;\n      }\n    }\n    return false;\n  }\n  getSortField() {\n    if (this.sort) {\n      return this.sort.split(\",\")[0];\n    }\n    return \"\";\n  }\n  getSortOrder() {\n    if (this.sort) {\n      let typeSort = this.sort.split(\",\")[1];\n      return typeSort == 'asc' ? 1 : -1;\n    }\n    return \"\";\n  }\n  checkEmpty() {\n    return (this.dataSet?.content || []).length == 0;\n  }\n  getNumberColumnShow() {\n    return this.filterColumnShow(this.columns).length + (this.options.action ? 1 : 0) + (this.options.hasShowToggleColumn == true ? 1 : 0) + (this.options.hasShowIndex ? 1 : 0) + (this.options.hasShowChoose ? 1 : 0);\n  }\n  filterAction(actions, item, id) {\n    return actions.filter(el => {\n      return el.funcAppear == undefined || el.funcAppear(id, item);\n    });\n  }\n  handleSelectAllChange(values) {\n    let me = this;\n    if (!this.options.hasClearSelected) {\n      let idInPage = this.dataSet.content.map(el => el[me.fieldId]);\n      let ortherItem = this.selectItems.filter(el => !idInPage.includes(el[me.fieldId]));\n      this.selectItems = [...ortherItem, ...values];\n    } else {\n      this.selectItems = [...values];\n    }\n  }\n  handleClickText(item, data) {\n    if (item.funcClick) {\n      item.funcClick(data[this.fieldId], data);\n    }\n  }\n  handleContextMenu(event) {\n    let me = this;\n    me.utilService.copyToClipboard(event.target.innerHTML, () => {\n      me.messageCommonService.success(me.tranService.translate(\"global.message.copied\"));\n    });\n    event.preventDefault();\n  }\n  columnShowChanged(value) {\n    if (this.options.hasShowToggleColumn) {\n      if (this.sessionService.userInfo) {\n        if ((this.tableId || \"\").trim().length > 0) {\n          let dataSessionTable = localStorage.getItem(\"dataSessionTable\");\n          if (dataSessionTable) {\n            dataSessionTable = JSON.parse(dataSessionTable);\n            dataSessionTable[this.sessionService.userInfo.username] = {\n              [this.tableId]: value\n            };\n          } else {\n            dataSessionTable = {\n              [this.sessionService.userInfo.username]: {\n                [this.tableId]: value\n              }\n            };\n          }\n          localStorage.setItem(\"dataSessionTable\", JSON.stringify(dataSessionTable));\n        }\n      }\n    }\n  }\n  filterInput(event, validate) {\n    if (validate.filterInput) {\n      if (!validate.filterInput(event)) {\n        event.preventDefault();\n      }\n    }\n  }\n  onChangeSelectAll() {\n    this.onChangeSelectAllItems.emit();\n  }\n  onChangeCustomSelectAll(event) {\n    this.onChangeCustomSelectAllEmmiter.emit();\n  }\n  onClickItemCheckbox() {\n    this.onChangeSelectItem.emit();\n  }\n  test(event) {}\n  formatNumber(value) {\n    return new Intl.NumberFormat('vi-VN').format(value);\n  }\n  //Do trang chỉ chạy onInit 1 lần nếu bảng là động trong 1 trang\n  resetPageNumber() {\n    this.pageNumber = 0;\n  }\n  static {\n    this.ɵfac = function TableVnptComponent_Factory(t) {\n      return new (t || TableVnptComponent)(i0.ɵɵdirectiveInject(TranslateService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TableVnptComponent,\n      selectors: [[\"table-vnpt\"]],\n      inputs: {\n        tableId: \"tableId\",\n        selectItems: \"selectItems\",\n        labelTable: \"labelTable\",\n        columns: \"columns\",\n        dataSet: \"dataSet\",\n        loadData: \"loadData\",\n        options: \"options\",\n        pageNumber: \"pageNumber\",\n        pageSize: \"pageSize\",\n        sort: \"sort\",\n        params: \"params\",\n        fieldId: \"fieldId\",\n        rowsPerPageOptions: \"rowsPerPageOptions\",\n        scrollHeight: \"scrollHeight\",\n        actionWidth: \"actionWidth\",\n        isRowDraggable: \"isRowDraggable\",\n        tableSelectionText: \"tableSelectionText\",\n        selectionWidth: \"selectionWidth\",\n        customSelectAll: \"customSelectAll\",\n        isUseCustomSelectAll: \"isUseCustomSelectAll\",\n        styleOutLineTable: \"styleOutLineTable\"\n      },\n      outputs: {\n        selectItemsChange: \"selectItemsChange\",\n        blurInputEvent: \"blurInputEvent\",\n        onChangeSelectAllItems: \"onChangeSelectAllItems\",\n        onChangeSelectItem: \"onChangeSelectItem\",\n        onChangeCustomSelectAllEmmiter: \"onChangeCustomSelectAllEmmiter\",\n        customSelectAllChange: \"customSelectAllChange\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 7,\n      vars: 23,\n      consts: [[1, \"mt-2\", \"table-vnpt\", \"relative\", \"p-4\", \"bg-white\", \"border-round-lg\"], [\"class\", \"flex flex-row justify-content-between mb-2\", 4, \"ngIf\"], [\"paginatorDropdownAppendTo\", \"body\", 3, \"value\", \"paginator\", \"rows\", \"first\", \"showCurrentPageReport\", \"tableStyle\", \"currentPageReportTemplate\", \"rowsPerPageOptions\", \"styleClass\", \"selection\", \"totalRecords\", \"lazy\", \"customSort\", \"sortField\", \"sortOrder\", \"resetPageOnSort\", \"scrollHeight\", \"scrollable\", \"reorderableColumns\", \"onPage\", \"selectionChange\", \"onSort\", \"selectAllChange\"], [\"dataTable\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"paginatorright\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"mb-2\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"mb-1\"], [\"class\", \"flex justify-content-center align-items-center text-lg font-bold\", 4, \"ngIf\"], [\"class\", \"flex flex-row justify-content-center align-items-center text-base px-2\", 4, \"ngIf\"], [\"class\", \"flex flex-row align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"text-lg\", \"font-bold\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"text-base\", \"px-2\"], [\"styleClass\", \"mr-2\", 3, \"ngModel\", \"binary\", \"pTooltip\", \"ngModelChange\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [\"pButton\", \"\", \"tooltipPosition\", \"bottom\", \"class\", \"ml-2\", \"styleClass\", \"\", 3, \"pTooltip\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"mr-2\", 3, \"ngModel\", \"binary\", \"pTooltip\", \"ngModelChange\", \"click\"], [\"pButton\", \"\", \"tooltipPosition\", \"bottom\", \"styleClass\", \"\", 1, \"ml-2\", 3, \"pTooltip\", \"click\"], [1, \"pi\", \"pi-times\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"gap-2\"], [\"style\", \"text-align: right;\", 4, \"ngIf\"], [2, \"text-align\", \"right\"], [\"styleClass\", \"p-0\"], [\"op\", \"\"], [\"pTemplate\", \"content\"], [\"pButton\", \"\", \"class\", \"p-button-outlined\", 3, \"click\", 4, \"ngIf\"], [1, \"max-h-20rem\", 2, \"overflow-y\", \"scroll\"], [2, \"padding\", \"0\"], [4, \"ngFor\", \"ngForOf\"], [\"inputId\", \"item.key\", 1, \"mb-1\", 3, \"ngModel\", \"value\", \"label\", \"ngModelChange\"], [\"pButton\", \"\", 1, \"p-button-outlined\", 3, \"click\"], [1, \"pi\", \"pi-filter\"], [\"class\", \"flex flex-row align-items-center gap-3\", 3, \"width\", 4, \"ngIf\"], [\"style\", \"width: 100px;\", 4, \"ngIf\"], [\"class\", \"white-space-nowrap\", 3, \"style\", \"pSortableColumn\", 4, \"ngFor\", \"ngForOf\"], [\"pFrozenColumn\", \"\", \"alignFrozen\", \"right\", \"style\", \"text-align: center;\", \"class\", \"white-space-nowrap border-left-1\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"gap-3\"], [3, \"disabled\", \"click\"], [2, \"width\", \"100px\"], [1, \"white-space-nowrap\", 3, \"pSortableColumn\"], [3, \"field\", 4, \"ngIf\"], [3, \"field\"], [\"pFrozenColumn\", \"\", \"alignFrozen\", \"right\", 1, \"white-space-nowrap\", \"border-left-1\", 2, \"text-align\", \"center\", 3, \"ngStyle\"], [1, \"box-table-nodata\"], [1, \"pi\", \"pi-inbox\", 2, \"font-size\", \"x-large\"], [3, \"pReorderableRow\"], [\"class\", \"table-column-vnpt\", 3, \"style\", \"pTooltip\", 4, \"ngFor\", \"ngForOf\"], [\"pFrozenColumn\", \"\", \"alignFrozen\", \"right\", \"class\", \"border-left-1\", 3, \"ngStyle\", 4, \"ngIf\"], [\"pReorderableRowHandle\", \"\", 1, \"pi\", \"pi-sliders-h\"], [3, \"value\", \"disabled\", \"click\"], [1, \"table-column-vnpt\", 3, \"pTooltip\"], [\"style\", \"text-wrap : auto\", 4, \"ngIf\"], [2, \"text-wrap\", \"auto\"], [\"pInputText\", \"\", \"class\", \"form-control\", \"type\", \"text\", \"required\", \"\", 3, \"ngModel\", \"style\", \"ngClass\", \"ngModelChange\", \"keydown\", \"blur\", 4, \"ngIf\"], [\"mode\", \"decimal\", \"locale\", \"vi-VN\", 3, \"ngModel\", \"style\", \"ngClass\", \"ngModelChange\", \"keydown\", \"onBlur\", 4, \"ngIf\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"pInputText\", \"\", \"type\", \"text\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\", \"keydown\", \"blur\"], [\"mode\", \"decimal\", \"locale\", \"vi-VN\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\", \"keydown\", \"onBlur\"], [1, \"text-red-500\"], [3, \"style\", \"class\", \"click\", 4, \"ngIf\"], [\"routerLinkActive\", \"router-link-active\", 3, \"routerLink\", \"style\", \"class\", 4, \"ngIf\"], [3, \"click\"], [\"routerLinkActive\", \"router-link-active\", 3, \"routerLink\"], [\"pFrozenColumn\", \"\", \"alignFrozen\", \"right\", 1, \"border-left-1\", 3, \"ngStyle\"], [1, \"flex\", \"flex-row\", \"grap-2\", \"justify-content-center\", \"align-items-center\"], [\"class\", \"inline-block mr-1 cursor-pointer\", \"tooltipPosition\", \"left\", 3, \"class\", \"pTooltip\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"tooltipPosition\", \"left\", 1, \"inline-block\", \"mr-1\", \"cursor-pointer\", 3, \"pTooltip\", \"click\"], [\"class\", \"flex flex-row justify-content-start align-items-center grap-2 ml-3\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\", \"grap-2\", \"ml-3\"], [1, \"mr-2\"], [\"mode\", \"decimal\", 3, \"ngModel\", \"min\", \"max\", \"keyup.enter\", \"ngModelChange\"]],\n      template: function TableVnptComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TableVnptComponent_div_1_Template, 5, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"p-table\", 2, 3);\n          i0.ɵɵlistener(\"onPage\", function TableVnptComponent_Template_p_table_onPage_2_listener($event) {\n            return ctx.pageChange($event);\n          })(\"selectionChange\", function TableVnptComponent_Template_p_table_selectionChange_2_listener($event) {\n            return ctx.modelSelected = $event;\n          })(\"onSort\", function TableVnptComponent_Template_p_table_onSort_2_listener($event) {\n            return ctx.handleSort($event);\n          })(\"selectAllChange\", function TableVnptComponent_Template_p_table_selectAllChange_2_listener($event) {\n            return ctx.test($event);\n          })(\"selectionChange\", function TableVnptComponent_Template_p_table_selectionChange_2_listener($event) {\n            return ctx.handleSelectAllChange($event);\n          });\n          i0.ɵɵtemplate(4, TableVnptComponent_ng_template_4_Template, 7, 6, \"ng-template\", 4);\n          i0.ɵɵtemplate(5, TableVnptComponent_ng_template_5_Template, 6, 6, \"ng-template\", 5);\n          i0.ɵɵtemplate(6, TableVnptComponent_ng_template_6_Template, 1, 1, \"ng-template\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleMap(ctx.styleOutLineTable);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.options.hasClearSelected && ctx.selectItems.length > 0 || ctx.labelTable || ctx.options.hasShowToggleColumn || ctx.isUseCustomSelectAll);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.dataSet == null ? null : ctx.dataSet.content)(\"paginator\", (ctx.dataSet == null ? null : ctx.dataSet.total) > 0 && ctx.options.paginator)(\"rows\", ctx.pageSize)(\"first\", ctx.rowFirst)(\"showCurrentPageReport\", true)(\"tableStyle\", i0.ɵɵpureFunction0(22, _c7))(\"currentPageReportTemplate\", ctx.transService.translate(\"global.text.templateTextPagination\"))(\"rowsPerPageOptions\", ctx.rowsPerPageOptions)(\"styleClass\", \"p-datatable-sm responsive-table\")(\"selection\", ctx.modelSelected)(\"totalRecords\", ctx.dataSet == null ? null : ctx.dataSet.total)(\"lazy\", true)(\"customSort\", true)(\"sortField\", ctx.getSortField())(\"sortOrder\", ctx.getSortOrder())(\"resetPageOnSort\", false)(\"scrollHeight\", ctx.scrollHeight)(\"scrollable\", true)(\"reorderableColumns\", ctx.isRowDraggable);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.Table, i3.PrimeTemplate, i2.SortableColumn, i2.FrozenColumn, i2.SortIcon, i2.TableCheckbox, i2.TableHeaderCheckbox, i2.ReorderableRowHandle, i2.ReorderableRow, i4.OverlayPanel, i5.ButtonDirective, i6.DefaultValueAccessor, i6.NgControlStatus, i6.RequiredValidator, i6.NgModel, i7.Checkbox, i8.InputNumber, i9.InputText, i10.RouterLink, i10.RouterLinkActive, i11.Tooltip],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "ComponentBase", "TranslateService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r5", "labelTable", "ɵɵlistener", "TableVnptComponent_div_1_div_3_p_checkbox_1_Template_p_checkbox_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "customSelectAll", "TableVnptComponent_div_1_div_3_p_checkbox_1_Template_p_checkbox_click_0_listener", "ctx_r13", "onChangeCustomSelectAll", "ɵɵproperty", "ctx_r8", "tooltipSelectAll", "ɵɵtextInterpolate2", "ctx_r9", "transService", "translate", "selectItems", "length", "TableVnptComponent_div_1_div_3_button_3_Template_button_click_0_listener", "_r15", "ctx_r14", "modelSelected", "ɵɵelement", "ctx_r10", "clearSelected", "ɵɵtemplate", "TableVnptComponent_div_1_div_3_p_checkbox_1_Template", "TableVnptComponent_div_1_div_3_span_2_Template", "TableVnptComponent_div_1_div_3_button_3_Template", "ctx_r6", "isUseCustomSelectAll", "options", "hasClearSelected", "TableVnptComponent_div_1_div_4_div_1_ng_template_3_li_2_Template_p_checkbox_ngModelChange_1_listener", "_r23", "ctx_r22", "columnShows", "ctx_r24", "columnShowChanged", "ctx_r20", "item_r21", "key", "name", "TableVnptComponent_div_1_div_4_div_1_ng_template_3_li_2_Template", "ctx_r18", "columns", "TableVnptComponent_div_1_div_4_div_1_button_4_Template_button_click_0_listener", "_r26", "_r17", "ɵɵreference", "toggle", "TableVnptComponent_div_1_div_4_div_1_ng_template_3_Template", "TableVnptComponent_div_1_div_4_div_1_button_4_Template", "ctx_r16", "hasShowToggleColumn", "TableVnptComponent_div_1_div_4_div_1_Template", "ctx_r7", "TableVnptComponent_div_1_div_2_Template", "TableVnptComponent_div_1_div_3_Template", "TableVnptComponent_div_1_div_4_Template", "ctx_r0", "TableVnptComponent_ng_template_4_th_2_Template_p_tableHeaderCheckbox_click_1_listener", "_r34", "ctx_r33", "onChangeSelectAll", "ɵɵstyleProp", "ctx_r28", "<PERSON><PERSON><PERSON><PERSON>", "disabledCheckBox", "tableSelectionText", "ctx_r29", "item_r35", "TableVnptComponent_ng_template_4_th_4_p_sortIcon_2_Template", "ɵɵstyleMap", "ɵɵpureFunction3", "_c0", "size", "align", "ɵɵtextInterpolate1", "isSort", "ɵɵpureFunction1", "_c1", "ctx_r31", "actionWidth", "_c2", "ctx_r32", "filterColumnShow", "ɵɵattribute", "getNumberColumnShow", "TableVnptComponent_ng_template_4_th_1_Template", "TableVnptComponent_ng_template_4_th_2_Template", "TableVnptComponent_ng_template_4_th_3_Template", "TableVnptComponent_ng_template_4_th_4_Template", "TableVnptComponent_ng_template_4_th_5_Template", "TableVnptComponent_ng_template_4_tr_6_Template", "ctx_r2", "isRowDraggable", "hasShowChoose", "hasShowIndex", "action", "checkEmpty", "TableVnptComponent_ng_template_5_td_2_Template_p_tableCheckbox_click_1_listener", "_r46", "ctx_r45", "onClickItemCheckbox", "data_r38", "ctx_r41", "i_r39", "TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_ngModelChange_0_listener", "_r62", "item_r49", "$implicit", "TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_keydown_0_listener", "ctx_r64", "filterInput", "validate", "TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template_input_blur_0_listener", "ctx_r66", "onBlurInput", "isFormInvalid", "editInputType", "ctx_r69", "onValueChange", "_c3", "ctx_r52", "TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_ngModelChange_0_listener", "_r76", "TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_keydown_0_listener", "ctx_r78", "TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template_p_inputNumber_onBlur_0_listener", "ctx_r80", "ctx_r83", "ctx_r53", "messageErrorPattern", "ctx_r55", "tranService", "_c4", "max<PERSON><PERSON><PERSON>", "ctx_r56", "ctx_r57", "<PERSON><PERSON><PERSON><PERSON>", "ctx_r58", "_c5", "formatNumber", "max", "ctx_r59", "min", "TableVnptComponent_ng_template_5_td_4_div_1_input_1_Template", "TableVnptComponent_ng_template_5_td_4_div_1_p_inputNumber_2_Template", "TableVnptComponent_ng_template_5_td_4_div_1_div_3_Template", "TableVnptComponent_ng_template_5_td_4_div_1_div_4_Template", "TableVnptComponent_ng_template_5_td_4_div_1_div_5_Template", "TableVnptComponent_ng_template_5_td_4_div_1_div_6_Template", "TableVnptComponent_ng_template_5_td_4_div_1_div_7_Template", "TableVnptComponent_ng_template_5_td_4_div_1_div_8_Template", "isEditable", "ctx_r50", "TableVnptComponent_ng_template_5_td_4_ng_container_2_span_1_Template_span_click_0_listener", "_r100", "ctx_r98", "handleClickText", "style", "ɵɵclassMap", "funcGetClassname", "className", "funcConvertText", "funcGetRouting", "ɵɵelementContainerStart", "TableVnptComponent_ng_template_5_td_4_ng_container_2_span_1_Template", "TableVnptComponent_ng_template_5_td_4_ng_container_2_a_2_Template", "ɵɵelementContainerEnd", "TableVnptComponent_ng_template_5_td_4_div_1_Template", "TableVnptComponent_ng_template_5_td_4_ng_container_2_Template", "_c6", "isShowTooltip", "funcCustomizeToolTip", "TableVnptComponent_ng_template_5_td_5_span_2_Template_span_click_0_listener", "restoredCtx", "_r112", "op_r109", "ctx_r110", "func", "fieldId", "icon", "tooltip", "TableVnptComponent_ng_template_5_td_5_span_2_Template", "ctx_r44", "filterAction", "TableVnptComponent_ng_template_5_td_1_Template", "TableVnptComponent_ng_template_5_td_2_Template", "TableVnptComponent_ng_template_5_td_3_Template", "TableVnptComponent_ng_template_5_td_4_Template", "TableVnptComponent_ng_template_5_td_5_Template", "ctx_r3", "TableVnptComponent_ng_template_6_div_0_Template_p_inputNumber_keyup_enter_3_listener", "_r116", "ctx_r115", "jumpPage", "pageC", "TableVnptComponent_ng_template_6_div_0_Template_p_inputNumber_ngModelChange_3_listener", "ctx_r117", "pageCurrent", "ctx_r114", "getMaxPage", "TableVnptComponent_ng_template_6_div_0_Template", "ctx_r4", "hasShowJumpPage", "TableVnptComponent", "constructor", "injector", "selectItemsChange", "blurInputEvent", "pageNumber", "pageSize", "sort", "params", "rowsPerPageOptions", "scrollHeight", "onChangeSelectAllItems", "onChangeSelectItem", "onChangeCustomSelectAllEmmiter", "customSelectAllChange", "styleOutLineTable", "dataSetOld", "ngOnInit", "paginator", "<PERSON><PERSON><PERSON><PERSON>", "pageNumberOld", "flagCheckSession", "dataSessionTable", "localStorage", "getItem", "JSON", "parse", "sessionService", "userInfo", "username", "tableId", "filter", "column", "isShow", "map", "selectItemsOld", "value", "validate<PERSON><PERSON><PERSON>", "invalidRequired", "invalidPattern", "invalid<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidMax", "invalidMin", "required", "pattern", "RegExp", "test", "toString", "newValue", "data", "item", "event", "isFormInValid", "rowData", "valueEmit", "target", "Number", "replace", "emit", "ngDoCheck", "me", "stringify", "setTimeout", "dataSet", "idSelecteds", "el", "content", "includes", "valueSelected", "ngAfterViewChecked", "pageChange", "rows", "first", "loadData", "ngOnChanges", "changes", "prev", "previousValue", "curr", "currentValue", "total", "resetPageNumber", "Math", "ceil", "handleSort", "checkSort", "field", "oldSort", "order", "i", "getSortField", "split", "getSortOrder", "typeSort", "actions", "id", "funcAppear", "undefined", "handleSelectAllChange", "values", "idInPage", "ortherItem", "funcClick", "handleContextMenu", "utilService", "copyToClipboard", "innerHTML", "messageCommonService", "success", "preventDefault", "trim", "setItem", "Intl", "NumberFormat", "format", "ɵɵdirectiveInject", "Injector", "selectors", "inputs", "outputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "TableVnptComponent_Template", "rf", "ctx", "TableVnptComponent_div_1_Template", "TableVnptComponent_Template_p_table_onPage_2_listener", "TableVnptComponent_Template_p_table_selectionChange_2_listener", "TableVnptComponent_Template_p_table_onSort_2_listener", "TableVnptComponent_Template_p_table_selectAllChange_2_listener", "TableVnptComponent_ng_template_4_Template", "TableVnptComponent_ng_template_5_Template", "TableVnptComponent_ng_template_6_Template", "ɵɵpureFunction0", "_c7"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\table\\table.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\common-module\\table\\table.component.html"], "sourcesContent": ["import { Component, Input, OnInit, Output, Do<PERSON>he<PERSON>, AfterViewChecked, Inject, Injector, SimpleChanges} from \"@angular/core\";\r\nimport { EventEmitter } from \"@angular/core\";\r\nimport { options } from \"@fullcalendar/core/preact\";\r\nimport { TableSelectAllChangeEvent } from \"primeng/table\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { TranslateService } from \"src/app/service/comon/translate.service\";\r\nimport {a} from \"@fullcalendar/core/internal-common\";\r\nexport interface ColumnInfo{\r\n    name: string,//tên hiển thị\r\n    key: string,//key lấy giá trị hiển thị\r\n    size: string,//kích thước cột\r\n    align: \"left\"|\"right\"|\"center\",//căn lề\r\n    isShow: boolean,//có cho hiển thi hay không\r\n    isSort: boolean,// có cho sắp xếp hay không\r\n    style?: {[className: string]: any},// css riêng\r\n    className?: string | Array<string>,// class css riêng\r\n    funcGetClassname?: (value: any) => string | Array<string>,//hàm chuyển đổi class css\r\n    funcConvertText?: (value: any, item?: any) => string,// hàm chuyển đổi giá trị hiển thị\r\n    funcGetRouting?:(item: any) => Array<any>,// hàm lấy giá trị link\r\n    funcClick?: (id:any, item:any) => void//hàm xử lý khi click\r\n    funcCustomizeToolTip?: (value?: any, item?:any) => void,\r\n    isShowTooltip?:boolean,\r\n    isEditable? : boolean,\r\n    editInputType?: \"string\"|\"number\" // :D\r\n    validate?: { // validate cho trường hợp editable = true\r\n        required?: boolean | null,//có bắt buộc hay không\r\n        pattern?: string | RegExp | null,//regex\r\n        messageErrorPattern?: string | null,//message khi sai regex\r\n        maxLength?: number | null,//độ dài tối đa nếu là kiểu chuỗi\r\n        minLength?: number | null,//độ dài tối thiểu nếu là kiểu chuỗi\r\n        max?: number | null,//giá trị tối đa nếu là kiểu số\r\n        min?: number | null,// giá trị tối thiểu nếu là kiểu số,\r\n        filterInput?: Function // Hàm chặn ký tự nhập\r\n    }\r\n}\r\nexport interface ActionInfo{\r\n    icon: string,//icon là gì\r\n    tooltip: string, // text chú thích là gì\r\n    func: (id: any, item:any, ...args: any)=>void,//hàm xủ lý là gì\r\n    funcAppear?: (id:any, item: any)=>boolean// hàm xử lý có cho phép hiển thị hay không\r\n}\r\nexport interface OptionTable{\r\n    hasClearSelected: boolean,//có clear lựa chọn khi chuyển trang hay không\r\n    hasShowChoose?: boolean,//có hiển thị ô lựa chọn hay không\r\n    hasShowIndex?: boolean,//có hiển thị cột index hay không\r\n    hasShowToggleColumn?: boolean,// có hiển thị nút lựa chọn cột hiển thị hay không\r\n    hasShowJumpPage?: boolean,// có hiển thị ô nhảy trang hay không\r\n    action?: Array<ActionInfo>;// danh sách các nút ở cột thao tác\r\n    paginator?: boolean;// có phân trang hay không\r\n    disabledCheckBox?: boolean;// Lựa chọn disable ô checkbox\r\n}\r\n@Component({\r\n    selector: \"table-vnpt\",\r\n    templateUrl: './table.component.html'\r\n})\r\nexport class TableVnptComponent extends ComponentBase implements OnInit, DoCheck, AfterViewChecked{\r\n    constructor(@Inject(TranslateService) public transService: TranslateService, private injector: Injector) {\r\n        super(injector);\r\n    }\r\n    @Input() tableId?: string;\r\n    @Input() selectItems?: Array<any> = [];// giá trị được lựa chọn\r\n    @Input() labelTable?: string;// text chú thích bảng\r\n    @Output() selectItemsChange: EventEmitter<Array<any>> = new EventEmitter();\r\n    @Output() blurInputEvent: EventEmitter<any> = new EventEmitter();\r\n\r\n    @Input() columns!: Array<ColumnInfo>;//danh sách cột\r\n    @Input() dataSet!: {\r\n        content: Array<Object>,\r\n        total: number\r\n    };\r\n\r\n    @Input() loadData?: (page, limit, sort, params)=>void;//hàm load dữ liệu\r\n\r\n    @Input() options!:OptionTable;//option của table\r\n    @Input() pageNumber?: number = 0;// trang hiển thị\r\n    @Input() pageSize?: number=0;// số lượng dòng trên 1 trang\r\n    @Input() sort?: string=\"\"; // format sắp xếp\r\n    @Input() params?: any={};// tham số tìm kiếm\r\n    @Input() fieldId!: string;//key được lựa chọn làm id\r\n    @Input() rowsPerPageOptions?: Array<number> = [5, 10, 20, 25, 50];// danh sách lựa chọn số lượng dòng trên 1 trang\r\n    @Input() scrollHeight?: string = 'flex'//độ cao tối đa của table\r\n    @Input() actionWidth?:string // định nghĩa chiều rộng ô action\r\n    @Input() isRowDraggable?:boolean = false\r\n    @Output() onChangeSelectAllItems: EventEmitter<any> = new EventEmitter();\r\n    @Output() onChangeSelectItem: EventEmitter<any> = new EventEmitter();\r\n    @Output() onChangeCustomSelectAllEmmiter: EventEmitter<any> = new EventEmitter();\r\n    @Input() tableSelectionText?: string = \"\";\r\n    @Input() selectionWidth:number = 4;\r\n    @Input() customSelectAll: boolean = false;\r\n    @Output() customSelectAllChange: EventEmitter<boolean> = new EventEmitter<boolean>();\r\n    @Input() isUseCustomSelectAll: boolean = false;\r\n    @Input() styleOutLineTable: any ={}; //vì primeflex dùng !important nên nếu viết 1 số thuộc tính tồn tại trong thẻ chứa input này, nên thêm !important\r\n\r\n    clearSelected = this.tranService.translate('global.text.clearSelected');\r\n    columnShows: Array<string>;\r\n    rowFirst: number;\r\n    maxPage: number;\r\n    pageCurrent: number;\r\n    selectItemsOld: Array<any>;\r\n    oldSort: any;\r\n    pageNumberOld: number;\r\n    modelSelected: Array<any>;\r\n    dataSetOld: any = {};\r\n    tooltipSelectAll: string = \"\";\r\n\r\n    ngOnInit(): void {\r\n        if(!this.options){\r\n            this.options = {\r\n                hasClearSelected: true,\r\n                hasShowChoose: false,\r\n                hasShowIndex: false,\r\n                hasShowToggleColumn: false,\r\n                hasShowJumpPage: false,\r\n                action: [],\r\n                paginator: false\r\n            }\r\n        }\r\n        if(this.options.paginator !== false){\r\n            this.options.paginator = true;\r\n            this.rowFirst = this.pageNumber*this.pageSize;\r\n            this.pageNumberOld = this.pageNumber;\r\n            this.pageCurrent = this.pageNumber + 1;\r\n        }\r\n        let flagCheckSession = false;\r\n        if(this.options.hasShowToggleColumn){\r\n            let dataSessionTable = localStorage.getItem('dataSessionTable');\r\n            if(dataSessionTable){\r\n                dataSessionTable = JSON.parse(dataSessionTable);\r\n                if(dataSessionTable[this.sessionService.userInfo.username]){\r\n                    if(dataSessionTable[this.sessionService.userInfo.username][this.tableId]){\r\n                        this.columnShows = [...dataSessionTable[this.sessionService.userInfo.username][this.tableId]];\r\n                        flagCheckSession = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if(!flagCheckSession){\r\n            this.columnShows = this.columns.filter(column => column.isShow)\r\n                                        .map(column => column.key);\r\n        }\r\n        this.selectItemsOld = [...this.selectItems];\r\n    }\r\n\r\n    isFormInvalid(value: any, validate: any, validateCheck: string): boolean {\r\n        let invalidRequired = false;\r\n        let invalidPattern = false;\r\n        let invalidMaxLength = false;\r\n        let invalidMinLength = false;\r\n        let invalidMax = false;\r\n        let invalidMin = false;\r\n        // Check if the field is required and if the value is empty\r\n        if (validate.required && (!value)) {\r\n            invalidRequired = true; // Value is required but empty\r\n        }\r\n\r\n        // Check if the field value matches the regex pattern\r\n        if (validate.pattern && value) {\r\n            if(!new RegExp(validate.pattern).test(value.toString())) {\r\n                invalidPattern = true; // Value doesn't match the pattern\r\n            }\r\n        }\r\n\r\n        // Check if the length of the value is greater than maxLength\r\n        if (validate.maxLength && value) {\r\n            if(typeof value == 'string') {\r\n                if(value.length > validate.maxLength)\r\n                    invalidMaxLength = true; // Value is less than min length\r\n            }\r\n        }\r\n\r\n        // Check if the length of the value is less than minLength\r\n        if (validate.minLength && value) {\r\n            if(typeof value == 'string') {\r\n                if(value.length < validate.minLength)\r\n                    invalidMinLength = true; // Value is less than min length\r\n            }\r\n        }\r\n\r\n        // Check if the value is greater than the maximum allowed value (for numbers)\r\n        if (value && validate.max && value > validate.max && typeof value == 'number') {\r\n            invalidMax = true; // Value exceeds max value\r\n        }\r\n\r\n        // Check if the value is less than the minimum allowed value (for numbers)\r\n        if (value && validate.min && value < validate.min && typeof value == 'number') {\r\n            invalidMin = true; // Value is below min value\r\n        }\r\n        if(validateCheck) {\r\n            switch (validateCheck) {\r\n                case 'required' :\r\n                    return invalidRequired\r\n                case 'pattern':\r\n                    return invalidPattern\r\n                case 'minLength' :\r\n                    return invalidMinLength\r\n                case 'maxLength':\r\n                    return invalidMaxLength\r\n                case 'max' :\r\n                    return invalidMax\r\n                case 'min':\r\n                    return invalidMin\r\n                default:\r\n                    return false;\r\n            }\r\n        }\r\n        return invalidRequired || invalidPattern || invalidMinLength || invalidMaxLength || invalidMax || invalidMin;\r\n    }\r\n\r\n    // Handle value change and trigger any logic, such as saving changes\r\n    onValueChange(newValue: any, data: any, item: ColumnInfo) {\r\n        // Handle the change (e.g., update the dataset or trigger a validation)\r\n        // console.log(`Updated value for ${item.key}: ${newValue}`);\r\n        data[item.key] = newValue;  // Update the model with the new value\r\n\r\n        // Optionally, trigger custom validation logic or update backend here\r\n    }\r\n\r\n    onBlurInput(event, isFormInValid, rowData, editInputType) {\r\n        let valueEmit;\r\n        switch (editInputType) {\r\n            case 'string': {\r\n                valueEmit = event.target.value\r\n                break;\r\n            }\r\n            case 'number': {\r\n                valueEmit = Number(event.target.value.replace(/\\./g, ''));\r\n                break;\r\n            }\r\n        }\r\n        if(!isFormInValid) {\r\n            this.blurInputEvent.emit({\r\n                value : valueEmit,\r\n                data : rowData\r\n            })\r\n        }\r\n    }\r\n\r\n    ngDoCheck(){\r\n        let me = this;\r\n        if(JSON.stringify(this.selectItems) !== JSON.stringify(this.selectItemsOld)){\r\n            setTimeout(() => {\r\n                this.selectItemsChange.emit(this.selectItems);\r\n                this.selectItemsOld = [...this.selectItems];\r\n            })\r\n        }\r\n        if(JSON.stringify(this.dataSet) !== JSON.stringify(this.dataSetOld)){\r\n            this.dataSetOld = {...this.dataSet};\r\n            if(!this.options.hasClearSelected){\r\n                let idSelecteds = this.selectItems.map(el => el[me.fieldId]);\r\n                this.modelSelected = this.dataSet.content.filter(el => idSelecteds.includes(el[me.fieldId]));\r\n            }\r\n        }\r\n        if(this.pageNumber != this.pageNumberOld && this.options.paginator){\r\n            this.rowFirst = this.pageNumber*this.pageSize;\r\n            this.pageCurrent = this.pageNumber + 1;\r\n            this.pageNumberOld = this.pageNumber;\r\n        }\r\n        if(this.selectItems.length == 0){\r\n            this.modelSelected = [];\r\n        }else{\r\n            let valueSelected = this.selectItems.map(el => el[me.fieldId]);\r\n            this.modelSelected = this.dataSet.content.filter(el => valueSelected.includes(el[me.fieldId]))\r\n        }\r\n    }\r\n\r\n    ngAfterViewChecked(){\r\n    }\r\n\r\n    pageChange(event){//{first/rows}\r\n        this.pageSize = event.rows;\r\n        this.pageNumber = event.first/event.rows;\r\n        this.pageCurrent = this.pageNumber + 1;\r\n        this.modelSelected = [];\r\n        if(this.options.hasClearSelected === true){\r\n            this.selectItems = [];\r\n        }\r\n        this.loadData(this.pageNumber, this.pageSize, this.sort, this.params);\r\n    }\r\n\r\n    ngOnChanges(changes: SimpleChanges) {\r\n        if (changes['customSelectAll']) {\r\n            this.tooltipSelectAll = this.customSelectAll ? \"Bỏ chọn tất cả\" : \"Chọn tất cả\";\r\n        }\r\n        if (changes['dataSet']) {\r\n            const prev = changes['dataSet'].previousValue;\r\n            const curr = changes['dataSet'].currentValue;\r\n\r\n            if (prev?.total !== curr?.total) {\r\n                (this.selectItems.length == curr.total && curr.total != 0) ? this.customSelectAll = true : this.customSelectAll =false;\r\n                this.tooltipSelectAll = this.customSelectAll ? \"Bỏ chọn tất cả\" : \"Chọn tất cả\";\r\n            }\r\n        }\r\n        if (changes['resetPageNumberTrigger'] && changes['resetPageNumberTrigger'].currentValue) {\r\n            this.resetPageNumber();\r\n        }\r\n    }\r\n\r\n    filterColumnShow(columns){\r\n        return columns.filter(el => this.columnShows.includes(el.key));\r\n    }\r\n\r\n    getMaxPage(){\r\n        if(this.dataSet.total % this.pageSize == 0){\r\n            return this.dataSet.total/this.pageSize;\r\n        }else{\r\n            return Math.ceil(this.dataSet.total/this.pageSize);\r\n        }\r\n    }\r\n\r\n    jumpPage(){\r\n        this.modelSelected = [];\r\n        if(this.options.hasClearSelected === true){\r\n            this.selectItems = [];\r\n        }\r\n        this.pageNumber = this.pageCurrent - 1;\r\n        this.rowFirst = this.pageNumber * this.pageSize;\r\n        this.loadData(this.pageNumber, this.pageSize, this.sort, this.params);\r\n    }\r\n\r\n    handleSort(event){\r\n        if(!this.checkSort(event.field)){\r\n            return;\r\n        }\r\n        if(JSON.stringify(this.oldSort) == JSON.stringify(event)) return;\r\n        this.oldSort = event;\r\n        this.modelSelected = [];\r\n        if(this.options.hasClearSelected === true){\r\n            this.selectItems = [];\r\n        }\r\n        this.sort = `${event.field},${event.order == 1 ? 'asc': 'desc'}`;\r\n        this.loadData(this.pageNumber, this.pageSize, this.sort, this.params);\r\n    }\r\n\r\n    checkSort(field): boolean{\r\n        for(let i = 0;i<this.columns.length;i++){\r\n            let column = this.columns[i];\r\n            if(column.key == field){\r\n                return column.isSort;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    getSortField(){\r\n        if(this.sort){\r\n            return this.sort.split(\",\")[0];\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getSortOrder(){\r\n        if(this.sort){\r\n            let typeSort = this.sort.split(\",\")[1];\r\n            return typeSort == 'asc' ? 1 : -1;\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    checkEmpty(){\r\n        return (this.dataSet?.content || []).length == 0;\r\n    }\r\n\r\n    getNumberColumnShow(){\r\n        return this.filterColumnShow(this.columns).length\r\n                + (this.options.action?1:0) + (this.options.hasShowToggleColumn == true?1:0)\r\n                + (this.options.hasShowIndex?1:0) + (this.options.hasShowChoose?1:0);\r\n    }\r\n\r\n    filterAction(actions: Array<ActionInfo>, item: any, id: string){\r\n        return actions.filter(el => {\r\n            return el.funcAppear == undefined || el.funcAppear(id, item);\r\n        })\r\n    }\r\n\r\n    handleSelectAllChange(values){\r\n        let me = this;\r\n        if(!this.options.hasClearSelected){\r\n            let idInPage = this.dataSet.content.map(el => el[me.fieldId]);\r\n            let ortherItem = this.selectItems.filter(el => !idInPage.includes(el[me.fieldId]));\r\n            this.selectItems = [...ortherItem, ...values];\r\n        }else{\r\n            this.selectItems = [...values]\r\n        }\r\n    }\r\n\r\n    handleClickText(item: ColumnInfo, data){\r\n        if(item.funcClick){\r\n            item.funcClick(data[this.fieldId], data);\r\n        }\r\n    }\r\n\r\n    handleContextMenu(event){\r\n        let me = this;\r\n        me.utilService.copyToClipboard(event.target.innerHTML, () => {\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.copied\"));\r\n        });\r\n        event.preventDefault();\r\n    }\r\n\r\n    columnShowChanged(value){\r\n        if(this.options.hasShowToggleColumn){\r\n            if(this.sessionService.userInfo){\r\n                if((this.tableId || \"\").trim().length > 0){\r\n                    let dataSessionTable: any = localStorage.getItem(\"dataSessionTable\");\r\n                    if(dataSessionTable){\r\n                        dataSessionTable = JSON.parse(dataSessionTable);\r\n                        dataSessionTable[this.sessionService.userInfo.username] = {[this.tableId]: value};\r\n                    }else{\r\n                        dataSessionTable = {[this.sessionService.userInfo.username]: {[this.tableId]: value}};\r\n                    }\r\n                    localStorage.setItem(\"dataSessionTable\", JSON.stringify(dataSessionTable));\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    filterInput(event, validate) {\r\n        if(validate.filterInput) {\r\n            if (!validate.filterInput(event)) {\r\n                event.preventDefault();\r\n            }\r\n        }\r\n    }\r\n\r\n    onChangeSelectAll(){\r\n        this.onChangeSelectAllItems.emit();\r\n    }\r\n\r\n    onChangeCustomSelectAll(event){\r\n        this.onChangeCustomSelectAllEmmiter.emit();\r\n    }\r\n\r\n    onClickItemCheckbox(){\r\n        this.onChangeSelectItem.emit();\r\n    }\r\n\r\n    test(event:TableSelectAllChangeEvent){\r\n\r\n    }\r\n\r\n    formatNumber(value: number): string {\r\n        return new Intl.NumberFormat('vi-VN').format(value);\r\n    }\r\n\r\n    //Do trang chỉ chạy onInit 1 lần nếu bảng là động trong 1 trang\r\n    resetPageNumber(){\r\n        this.pageNumber = 0;\r\n    }\r\n}\r\n", "<div class=\"mt-2 table-vnpt relative p-4 bg-white border-round-lg\" [style]=\"styleOutLineTable\">\r\n    <div class=\"flex flex-row justify-content-between mb-2\" *ngIf=\"(!options.hasClearSelected && selectItems.length > 0)||labelTable||options.hasShowToggleColumn || isUseCustomSelectAll\">\r\n        <div class=\"flex flex-row gap-3 mb-1\">\r\n            <div *ngIf=\"labelTable\" class=\"flex justify-content-center align-items-center text-lg font-bold\">{{labelTable}}</div>\r\n            <div *ngIf=\"(!options.hasClearSelected && selectItems.length > 0) || isUseCustomSelectAll\" class=\"flex flex-row justify-content-center align-items-center text-base px-2\">\r\n                <p-checkbox *ngIf=\"isUseCustomSelectAll\" [(ngModel)]=\"customSelectAll\" [binary]=\"true\" styleClass=\"mr-2\" (click)=\"onChangeCustomSelectAll($event)\" [pTooltip]=\"tooltipSelectAll\"></p-checkbox>\r\n                <span *ngIf=\"!options.hasClearSelected && selectItems.length > 0\">{{transService.translate(\"global.text.itemselected\")}} {{selectItems.length}}</span>\r\n                <button *ngIf=\"selectItems.length > 0\" pButton  [pTooltip]=\"clearSelected\" tooltipPosition=\"bottom\" class=\"ml-2\" styleClass=\"\" (click)=\"selectItems=[];modelSelected=[]\"><i class=\" pi pi-times \"></i></button>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row align-items-center gap-2\" *ngIf=\"options.hasShowToggleColumn\">\r\n            <div style=\"text-align: right;\"  *ngIf=\"options.hasShowToggleColumn\">\r\n                <p-overlayPanel styleClass=\"p-0\" #op>\r\n                    <ng-template pTemplate=\"content\">\r\n                        <div class=\"max-h-20rem\" style=\"overflow-y: scroll;\">\r\n                            <ol style=\"padding: 0;\">\r\n                                <li *ngFor=\"let item of columns\">\r\n                                    <p-checkbox class=\"mb-1\" [(ngModel)]=\"columnShows\" [value]=\"item.key\" inputId=\"item.key\" [label]=\"item.name\" (ngModelChange)=\"columnShowChanged($event)\"></p-checkbox>\r\n                                </li>\r\n                            </ol>\r\n                        </div>\r\n                    </ng-template>\r\n                </p-overlayPanel>\r\n                <button *ngIf=\"options.hasShowToggleColumn\" pButton class=\"p-button-outlined\" (click)=\"op.toggle($event)\"><i class=\" pi pi-filter \"></i></button>\r\n            </div>\r\n            <!-- <button *ngIf=\"selectItems.length > 0\" pButton  [pTooltip]=\"clearSelected\" tooltipPosition=\"bottom\" class=\"ml-2\" styleClass=\"\" (click)=\"selectItems=[];modelSelected=[]\"><i class=\" pi pi-times \"></i></button> -->\r\n        </div>\r\n\r\n\r\n    </div>\r\n    <p-table\r\n        [value]=\"dataSet?.content\"\r\n        [paginator]=\"dataSet?.total > 0 && options.paginator\"\r\n        [rows]=\"pageSize\"\r\n        [first]=\"rowFirst\"\r\n        [showCurrentPageReport]=\"true\"\r\n        [tableStyle]=\"{ 'min-width': '100%' }\"\r\n        [currentPageReportTemplate]=\"transService.translate('global.text.templateTextPagination')\"\r\n        (onPage)=\"pageChange($event)\"\r\n        [rowsPerPageOptions]=\"rowsPerPageOptions\"\r\n        [styleClass]=\"'p-datatable-sm responsive-table'\"\r\n        [(selection)]=\"modelSelected\"\r\n        [totalRecords]=\"dataSet?.total\"\r\n        [lazy]=\"true\"\r\n        (onSort)=\"handleSort($event)\" [customSort]=\"true\"\r\n        [sortField]=\"getSortField()\"\r\n        [sortOrder]=\"getSortOrder()\"\r\n        paginatorDropdownAppendTo=\"body\"\r\n        #dataTable\r\n        (selectAllChange)=\"test($event)\"\r\n        (selectionChange)=\"handleSelectAllChange($event)\"\r\n        [resetPageOnSort]=\"false\"\r\n        [scrollHeight]=\"scrollHeight\"\r\n        [scrollable]=\"true\"\r\n        [reorderableColumns]=\"isRowDraggable\"\r\n    >\r\n        <ng-template pTemplate=\"header\">\r\n            <tr>\r\n                <th *ngIf=\"isRowDraggable\"></th>\r\n                <th [style.width.rem]=\"selectionWidth\" class=\"flex flex-row align-items-center gap-3\" *ngIf=\"options.hasShowChoose\">\r\n                    <p-tableHeaderCheckbox [disabled]=\"options.disabledCheckBox\" (click)=\"onChangeSelectAll()\"></p-tableHeaderCheckbox>\r\n                    <div>{{tableSelectionText}}</div>\r\n                </th>\r\n                <th *ngIf=\"options.hasShowIndex\" style=\"width: 100px;\">{{transService.translate('global.text.stt')}}</th>\r\n                <th *ngFor=\"let item of filterColumnShow(columns)\" class=\"white-space-nowrap\"\r\n                    [style]=\"{'min-width': item.size, 'text-align': item.align, 'width': item.size}\"\r\n                    [pSortableColumn]=\"item.key\"\r\n                >{{ item.name }} <p-sortIcon [field]=\"item.key\" *ngIf=\"item.isSort\"></p-sortIcon></th>\r\n                <th pFrozenColumn alignFrozen=\"right\" *ngIf=\"options.action\" style=\"text-align: center;\" [ngStyle]=\"{'width': actionWidth ? actionWidth : null}\" class=\"white-space-nowrap border-left-1\">{{transService.translate(\"global.text.action\")}}</th>\r\n            </tr>\r\n            <tr *ngIf=\"checkEmpty()\">\r\n                <td [attr.colspan]=\"getNumberColumnShow()\" [style]=\"{'min-width': filterColumnShow(columns)[0].size}\" class=\"box-table-nodata\">\r\n                    <span class=\"pi pi-inbox\" style=\"font-size: x-large;\">&nbsp;</span>{{transService.translate(\"global.text.nodata\")}}\r\n                </td>\r\n            </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"body\" let-data let-i=\"rowIndex\">\r\n            <tr [pReorderableRow]=\"isRowDraggable ? i : ''\">\r\n                <td *ngIf=\"isRowDraggable\">\r\n                    <span class=\"pi pi-sliders-h\" pReorderableRowHandle></span>\r\n                </td>\r\n                <td *ngIf=\"options.hasShowChoose\">\r\n                    <p-tableCheckbox [value]=\"data\" [disabled]=\"options.disabledCheckBox\" (click)=\"onClickItemCheckbox()\"></p-tableCheckbox>\r\n                </td>\r\n                <td *ngIf=\"options.hasShowIndex\" style=\"width: 100px;\">{{i + 1}}</td>\r\n                <td *ngFor=\"let item of filterColumnShow(columns)\" [style]=\"{'min-width': item.size,'width':item.size,'text-align':item.align}\" [pTooltip]=\"item.isShowTooltip ? (item.funcCustomizeToolTip ? item.funcCustomizeToolTip(data[item.key], data) : (item.funcConvertText ? item.funcConvertText(data[item.key], data) : data[item.key])) : ''\" class=\"table-column-vnpt\"\r\n                >\r\n                    <!-- Editable input for specific columns -->\r\n                    <div *ngIf=\"item.isEditable\" style=\"text-wrap : auto\">\r\n                        <input pInputText *ngIf=\"item.isEditable && item.editInputType === 'string'\" [(ngModel)]=\"data[item.key]\" [style]=\"{'min-width': item.size}\" (keydown)=\"filterInput($event, item.validate)\"\r\n                               (blur)=\"onBlurInput($event, isFormInvalid(data[item.key], item.validate), data, item.editInputType)\"\r\n                               [ngClass]=\"{'border-red-500': isFormInvalid(data[item.key], item.validate)}\"\r\n                               (ngModelChange)=\"onValueChange($event, data, item)\"\r\n                               class=\"form-control\" type=\"text\" required/>\r\n                        <p-inputNumber *ngIf=\"item.isEditable && item.editInputType === 'number'\"\r\n                                mode=\"decimal\"\r\n                                locale=\"vi-VN\"\r\n                                [(ngModel)]=\"data[item.key]\"\r\n                                [style]=\"{'min-width': item.size}\"\r\n                                (keydown)=\"filterInput($event, item.validate)\"\r\n                                (onBlur)=\"onBlurInput($event, isFormInvalid(data[item.key], item.validate), data, item.editInputType)\"\r\n                                [ngClass]=\"{'border-red-500': isFormInvalid(data[item.key], item.validate)}\"\r\n                                (ngModelChange)=\"onValueChange($event, data, item)\">\r\n                        </p-inputNumber>\r\n                        <div class=\"text-red-500\" *ngIf=\"isFormInvalid(data[item.key], item.validate, 'pattern')\">{{ item.validate.messageErrorPattern}}</div>\r\n                        <div class=\"text-red-500\" *ngIf=\"isFormInvalid(data[item.key], item.validate, 'required')\">{{tranService.translate(\"global.message.required\",{len:item.validate.maxLength})}}</div>\r\n                        <div class=\"text-red-500\" *ngIf=\"isFormInvalid(data[item.key], item.validate, 'maxLength')\">{{tranService.translate(\"global.message.maxLength\",{len:item.validate.maxLength})}}</div>\r\n                        <div class=\"text-red-500\" *ngIf=\"isFormInvalid(data[item.key], item.validate, 'minLength')\">{{tranService.translate(\"global.message.minLength\",{len:item.validate.minLength})}}</div>\r\n                        <div class=\"text-red-500\" *ngIf=\"isFormInvalid(data[item.key], item.validate, 'max')\">{{tranService.translate(\"global.message.max\",{value:formatNumber(item.validate.max)})}}</div>\r\n                        <div class=\"text-red-500\" *ngIf=\"isFormInvalid(data[item.key], item.validate, 'min')\">{{tranService.translate(\"global.message.min\",{value:formatNumber(item.validate.min)})}}</div>\r\n                    </div>\r\n\r\n                    <!-- Display static text when not editable -->\r\n                    <ng-container *ngIf=\"!item.isEditable\">\r\n                <span *ngIf=\"!item.funcGetRouting\" (click)=\"handleClickText(item, data)\"\r\n                      [style]=\"item.style\"\r\n                      [class]=\"item.funcGetClassname ? item.funcGetClassname(data[item.key]) : item.className\">\r\n                    {{ item.funcConvertText ? item.funcConvertText(data[item.key], data) : data[item.key] }}\r\n                </span>\r\n                        <a [routerLink]=\"item.funcGetRouting(data)\"\r\n                           routerLinkActive=\"router-link-active\"\r\n                           *ngIf=\"item.funcGetRouting\"\r\n                           [style]=\"item.style\"\r\n                           [class]=\"item.funcGetClassname ? item.funcGetClassname(data[item.key]) : item.className\">\r\n                            {{ item.funcConvertText ? item.funcConvertText(data[item.key], data) : data[item.key] }}\r\n                        </a>\r\n                    </ng-container>\r\n                </td>\r\n                <td pFrozenColumn alignFrozen=\"right\" *ngIf=\"options.action\" class=\"border-left-1\" [ngStyle]=\"{'width': actionWidth ? actionWidth : null}\">\r\n                    <div class=\"flex flex-row grap-2 justify-content-center align-items-center\">\r\n                        <span *ngFor=\"let op of filterAction(options.action, data, data[fieldId])\"\r\n                            class=\"inline-block mr-1 cursor-pointer\"\r\n                            [class]=\"op.icon\"\r\n                            (click)=\"op.func(data[fieldId], data)\"\r\n                            [pTooltip]=\"op.tooltip\"\r\n                            tooltipPosition=\"left\"\r\n                        ></span>\r\n                    </div>\r\n                </td>\r\n            </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"paginatorright\">\r\n            <div class=\"flex flex-row justify-content-start align-items-center grap-2 ml-3\" *ngIf=\"options.hasShowJumpPage !== false\">\r\n                <div class=\"mr-2\">{{transService.translate('global.text.page')}}</div>\r\n                <p-inputNumber (keyup.enter)=\"jumpPage(pageC)\" [(ngModel)]=\"pageCurrent\" mode=\"decimal\" [min]=\"1\" [max]=\"getMaxPage()\"> </p-inputNumber>\r\n            </div>\r\n        </ng-template>\r\n    </p-table>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,eAAe;AAG5C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,yCAAyC;;;;;;;;;;;;;;;;ICF9DC,EAAA,CAAAC,cAAA,cAAiG;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAApBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAc;;;;;;IAE3GP,EAAA,CAAAC,cAAA,qBAAiL;IAAxID,EAAA,CAAAQ,UAAA,2BAAAC,yFAAAC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAF,OAAA,CAAAG,eAAA,GAAAN,MAAA;IAAA,EAA6B,mBAAAO,iFAAAP,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAlB,EAAA,CAAAc,aAAA;MAAA,OAA4Cd,EAAA,CAAAe,WAAA,CAAAG,OAAA,CAAAC,uBAAA,CAAAT,MAAA,CAA+B;IAAA,EAA3E;IAA2GV,EAAA,CAAAG,YAAA,EAAa;;;;IAArJH,EAAA,CAAAoB,UAAA,YAAAC,MAAA,CAAAL,eAAA,CAA6B,6BAAAK,MAAA,CAAAC,gBAAA;;;;;IACtEtB,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,GAA6E;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApFH,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAuB,kBAAA,KAAAC,MAAA,CAAAC,YAAA,CAAAC,SAAA,mCAAAF,MAAA,CAAAG,WAAA,CAAAC,MAAA,KAA6E;;;;;;IAC/I5B,EAAA,CAAAC,cAAA,iBAAyK;IAA1CD,EAAA,CAAAQ,UAAA,mBAAAqB,yEAAA;MAAA7B,EAAA,CAAAW,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAc,aAAA;MAAAiB,OAAA,CAAAJ,WAAA;MAAA,OAAA3B,EAAA,CAAAe,WAAA,CAAAgB,OAAA,CAAAC,aAAA;IAAA,EAAyC;IAAChC,EAAA,CAAAiC,SAAA,YAA6B;IAAAjC,EAAA,CAAAG,YAAA,EAAS;;;;IAA/JH,EAAA,CAAAoB,UAAA,aAAAc,OAAA,CAAAC,aAAA,CAA0B;;;;;IAH9EnC,EAAA,CAAAC,cAAA,cAA0K;IACtKD,EAAA,CAAAoC,UAAA,IAAAC,oDAAA,yBAA8L;IAC9LrC,EAAA,CAAAoC,UAAA,IAAAE,8CAAA,mBAAsJ;IACtJtC,EAAA,CAAAoC,UAAA,IAAAG,gDAAA,qBAA+M;IACnNvC,EAAA,CAAAG,YAAA,EAAM;;;;IAHWH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoB,UAAA,SAAAoB,MAAA,CAAAC,oBAAA,CAA0B;IAChCzC,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAoB,UAAA,UAAAoB,MAAA,CAAAE,OAAA,CAAAC,gBAAA,IAAAH,MAAA,CAAAb,WAAA,CAAAC,MAAA,KAAyD;IACvD5B,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAoB,UAAA,SAAAoB,MAAA,CAAAb,WAAA,CAAAC,MAAA,KAA4B;;;;;;IASrB5B,EAAA,CAAAC,cAAA,SAAiC;IACJD,EAAA,CAAAQ,UAAA,2BAAAoC,qGAAAlC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAA+B,OAAA,CAAAC,WAAA,GAAArC,MAAA;IAAA,EAAyB,2BAAAkC,qGAAAlC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAkC,IAAA;MAAA,MAAAG,OAAA,GAAAhD,EAAA,CAAAc,aAAA;MAAA,OAA4Ed,EAAA,CAAAe,WAAA,CAAAiC,OAAA,CAAAC,iBAAA,CAAAvC,MAAA,CAAyB;IAAA,EAArG;IAAuGV,EAAA,CAAAG,YAAA,EAAa;;;;;IAA7IH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAoB,UAAA,YAAA8B,OAAA,CAAAH,WAAA,CAAyB,UAAAI,QAAA,CAAAC,GAAA,WAAAD,QAAA,CAAAE,IAAA;;;;;IAH9DrD,EAAA,CAAAC,cAAA,cAAqD;IAE7CD,EAAA,CAAAoC,UAAA,IAAAkB,gEAAA,iBAEK;IACTtD,EAAA,CAAAG,YAAA,EAAK;;;;IAHoBH,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAoB,UAAA,YAAAmC,OAAA,CAAAC,OAAA,CAAU;;;;;;IAO/CxD,EAAA,CAAAC,cAAA,iBAA0G;IAA5BD,EAAA,CAAAQ,UAAA,mBAAAiD,+EAAA/C,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA+C,IAAA;MAAA1D,EAAA,CAAAc,aAAA;MAAA,MAAA6C,IAAA,GAAA3D,EAAA,CAAA4D,WAAA;MAAA,OAAS5D,EAAA,CAAAe,WAAA,CAAA4C,IAAA,CAAAE,MAAA,CAAAnD,MAAA,CAAiB;IAAA,EAAC;IAACV,EAAA,CAAAiC,SAAA,YAA8B;IAAAjC,EAAA,CAAAG,YAAA,EAAS;;;;;IAZrJH,EAAA,CAAAC,cAAA,cAAqE;IAE7DD,EAAA,CAAAoC,UAAA,IAAA0B,2DAAA,0BAQc;IAClB9D,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAoC,UAAA,IAAA2B,sDAAA,qBAAiJ;IACrJ/D,EAAA,CAAAG,YAAA,EAAM;;;;IADOH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoB,UAAA,SAAA4C,OAAA,CAAAtB,OAAA,CAAAuB,mBAAA,CAAiC;;;;;IAblDjE,EAAA,CAAAC,cAAA,cAAwF;IACpFD,EAAA,CAAAoC,UAAA,IAAA8B,6CAAA,kBAaM;IAEVlE,EAAA,CAAAG,YAAA,EAAM;;;;IAfgCH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoB,UAAA,SAAA+C,MAAA,CAAAzB,OAAA,CAAAuB,mBAAA,CAAiC;;;;;IAV3EjE,EAAA,CAAAC,cAAA,aAAuL;IAE/KD,EAAA,CAAAoC,UAAA,IAAAgC,uCAAA,iBAAqH;IACrHpE,EAAA,CAAAoC,UAAA,IAAAiC,uCAAA,kBAIM;IACVrE,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAoC,UAAA,IAAAkC,uCAAA,kBAgBM;IAGVtE,EAAA,CAAAG,YAAA,EAAM;;;;IA1BQH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAoB,UAAA,SAAAmD,MAAA,CAAAhE,UAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAoB,UAAA,UAAAmD,MAAA,CAAA7B,OAAA,CAAAC,gBAAA,IAAA4B,MAAA,CAAA5C,WAAA,CAAAC,MAAA,QAAA2C,MAAA,CAAA9B,oBAAA,CAAmF;IAMxCzC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAoB,UAAA,SAAAmD,MAAA,CAAA7B,OAAA,CAAAuB,mBAAA,CAAiC;;;;;IAgD9EjE,EAAA,CAAAiC,SAAA,SAAgC;;;;;;IAChCjC,EAAA,CAAAC,cAAA,aAAoH;IACnDD,EAAA,CAAAQ,UAAA,mBAAAgE,sFAAA;MAAAxE,EAAA,CAAAW,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAA1E,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAA2D,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAAC3E,EAAA,CAAAG,YAAA,EAAwB;IACnHH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFjCH,EAAA,CAAA4E,WAAA,UAAAC,OAAA,CAAAC,cAAA,QAAkC;IACX9E,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAoB,UAAA,aAAAyD,OAAA,CAAAnC,OAAA,CAAAqC,gBAAA,CAAqC;IACvD/E,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAwE,OAAA,CAAAG,kBAAA,CAAsB;;;;;IAE/BhF,EAAA,CAAAC,cAAA,aAAuD;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAA4E,OAAA,CAAAxD,YAAA,CAAAC,SAAA,oBAA6C;;;;;IAInF1B,EAAA,CAAAiC,SAAA,qBAAgE;;;;IAApDjC,EAAA,CAAAoB,UAAA,UAAA8D,QAAA,CAAA9B,GAAA,CAAkB;;;;;;;;;;;;IAH/CpD,EAAA,CAAAC,cAAA,aAGC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAoC,UAAA,IAAA+C,2DAAA,yBAAgE;IAAAnF,EAAA,CAAAG,YAAA,EAAK;;;;IAFlFH,EAAA,CAAAoF,UAAA,CAAApF,EAAA,CAAAqF,eAAA,IAAAC,GAAA,EAAAJ,QAAA,CAAAK,IAAA,EAAAL,QAAA,CAAAM,KAAA,EAAAN,QAAA,CAAAK,IAAA,EAAgF;IAChFvF,EAAA,CAAAoB,UAAA,oBAAA8D,QAAA,CAAA9B,GAAA,CAA4B;IAC/BpD,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAyF,kBAAA,KAAAP,QAAA,CAAA7B,IAAA,MAAgB;IAAgCrD,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAoB,UAAA,SAAA8D,QAAA,CAAAQ,MAAA,CAAiB;;;;;;;;;;IAClE1F,EAAA,CAAAC,cAAA,aAA0L;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAtJH,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAA2F,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,WAAA,GAAAD,OAAA,CAAAC,WAAA,SAAuD;IAA0C9F,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAwF,OAAA,CAAApE,YAAA,CAAAC,SAAA,uBAAgD;;;;;;;;;;IAE9O1B,EAAA,CAAAC,cAAA,SAAyB;IAEqCD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAFsCH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAoF,UAAA,CAAApF,EAAA,CAAA2F,eAAA,IAAAI,GAAA,EAAAC,OAAA,CAAAC,gBAAA,CAAAD,OAAA,CAAAxC,OAAA,KAAA+B,IAAA,EAA0D;IAAjGvF,EAAA,CAAAkG,WAAA,YAAAF,OAAA,CAAAG,mBAAA,GAAsC;IAC6BnG,EAAA,CAAAI,SAAA,GACvE;IADuEJ,EAAA,CAAAyF,kBAAA,KAAAO,OAAA,CAAAvE,YAAA,CAAAC,SAAA,4BACvE;;;;;IAhBJ1B,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAoC,UAAA,IAAAgE,8CAAA,iBAAgC;IAChCpG,EAAA,CAAAoC,UAAA,IAAAiE,8CAAA,iBAGK;IACLrG,EAAA,CAAAoC,UAAA,IAAAkE,8CAAA,iBAAyG;IACzGtG,EAAA,CAAAoC,UAAA,IAAAmE,8CAAA,iBAGsF;IACtFvG,EAAA,CAAAoC,UAAA,IAAAoE,8CAAA,iBAA+O;IACnPxG,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAoC,UAAA,IAAAqE,8CAAA,iBAIK;;;;IAhBIzG,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoB,UAAA,SAAAsF,MAAA,CAAAC,cAAA,CAAoB;IAC8D3G,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAoB,UAAA,SAAAsF,MAAA,CAAAhE,OAAA,CAAAkE,aAAA,CAA2B;IAI7G5G,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoB,UAAA,SAAAsF,MAAA,CAAAhE,OAAA,CAAAmE,YAAA,CAA0B;IACV7G,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAoB,UAAA,YAAAsF,MAAA,CAAAT,gBAAA,CAAAS,MAAA,CAAAlD,OAAA,EAA4B;IAIVxD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoB,UAAA,SAAAsF,MAAA,CAAAhE,OAAA,CAAAoE,MAAA,CAAoB;IAE1D9G,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAoB,UAAA,SAAAsF,MAAA,CAAAK,UAAA,GAAkB;;;;;IAQnB/G,EAAA,CAAAC,cAAA,SAA2B;IACvBD,EAAA,CAAAiC,SAAA,eAA2D;IAC/DjC,EAAA,CAAAG,YAAA,EAAK;;;;;;IACLH,EAAA,CAAAC,cAAA,SAAkC;IACwCD,EAAA,CAAAQ,UAAA,mBAAAwG,gFAAA;MAAAhH,EAAA,CAAAW,aAAA,CAAAsG,IAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAmG,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAACnH,EAAA,CAAAG,YAAA,EAAkB;;;;;IAAvGH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAoB,UAAA,UAAAgG,QAAA,CAAc,aAAAC,OAAA,CAAA3E,OAAA,CAAAqC,gBAAA;;;;;IAEnC/E,EAAA,CAAAC,cAAA,aAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAdH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAiH,KAAA,KAAS;;;;;;;;;;;IAKxDtH,EAAA,CAAAC,cAAA,gBAIkD;IAJ2BD,EAAA,CAAAQ,UAAA,2BAAA+G,4FAAA7G,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAC,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,OAAA1H,EAAA,CAAAe,WAAA,CAAAqG,QAAA,CAAAK,QAAA,CAAArE,GAAA,IAAA1C,MAAA;IAAA,EAA4B,qBAAAiH,sFAAAjH,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAC,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAE,OAAA,GAAA5H,EAAA,CAAAc,aAAA;MAAA,OAA+Cd,EAAA,CAAAe,WAAA,CAAA6G,OAAA,CAAAC,WAAA,CAAAnH,MAAA,EAAA+G,QAAA,CAAAK,QAAA,CAAkC;IAAA,EAAjF,kBAAAC,mFAAArH,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAC,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,MAAAM,OAAA,GAAAhI,EAAA,CAAAc,aAAA;MAAA,OAC1Fd,EAAA,CAAAe,WAAA,CAAAiH,OAAA,CAAAC,WAAA,CAAAvH,MAAA,EAAoBsH,OAAA,CAAAE,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,CAA4C,EAAAV,QAAA,EAAAK,QAAA,CAAAU,aAAA,CAA2B;IAAA,EADD,2BAAAZ,4FAAA7G,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6G,IAAA;MAAA,MAAAC,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,MAAAU,OAAA,GAAApI,EAAA,CAAAc,aAAA;MAAA,OAGjFd,EAAA,CAAAe,WAAA,CAAAqH,OAAA,CAAAC,aAAA,CAAA3H,MAAA,EAAA0G,QAAA,EAAAK,QAAA,CAAiC;IAAA,EAHgD;IAAzGzH,EAAA,CAAAG,YAAA,EAIkD;;;;;;IAJwDH,EAAA,CAAAoF,UAAA,CAAApF,EAAA,CAAA2F,eAAA,IAAAI,GAAA,EAAA0B,QAAA,CAAAlC,IAAA,EAAkC;IAA/DvF,EAAA,CAAAoB,UAAA,YAAAgG,QAAA,CAAAK,QAAA,CAAArE,GAAA,EAA4B,YAAApD,EAAA,CAAA2F,eAAA,IAAA2C,GAAA,EAAAC,OAAA,CAAAL,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA;;;;;;IAKzG9H,EAAA,CAAAC,cAAA,wBAQ4D;IALpDD,EAAA,CAAAQ,UAAA,2BAAAgI,4GAAA9H,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA8H,IAAA;MAAA,MAAAhB,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,OAAA1H,EAAA,CAAAe,WAAA,CAAAqG,QAAA,CAAAK,QAAA,CAAArE,GAAA,IAAA1C,MAAA;IAAA,EAA4B,qBAAAgI,sGAAAhI,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA8H,IAAA;MAAA,MAAAhB,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAiB,OAAA,GAAA3I,EAAA,CAAAc,aAAA;MAAA,OAEjBd,EAAA,CAAAe,WAAA,CAAA4H,OAAA,CAAAd,WAAA,CAAAnH,MAAA,EAAA+G,QAAA,CAAAK,QAAA,CAAkC;IAAA,EAFjB,oBAAAc,qGAAAlI,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA8H,IAAA;MAAA,MAAAhB,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,MAAAmB,OAAA,GAAA7I,EAAA,CAAAc,aAAA;MAAA,OAGlBd,EAAA,CAAAe,WAAA,CAAA8H,OAAA,CAAAZ,WAAA,CAAAvH,MAAA,EAAoBmI,OAAA,CAAAX,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,CAA4C,EAAAV,QAAA,EAAAK,QAAA,CAAAU,aAAA,CAA2B;IAAA,EAHzE,2BAAAK,4GAAA9H,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA8H,IAAA;MAAA,MAAAhB,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,MAAAoB,OAAA,GAAA9I,EAAA,CAAAc,aAAA;MAAA,OAKXd,EAAA,CAAAe,WAAA,CAAA+H,OAAA,CAAAT,aAAA,CAAA3H,MAAA,EAAA0G,QAAA,EAAAK,QAAA,CAAiC;IAAA,EALtB;IAMpCzH,EAAA,CAAAG,YAAA,EAAgB;;;;;;IALRH,EAAA,CAAAoF,UAAA,CAAApF,EAAA,CAAA2F,eAAA,IAAAI,GAAA,EAAA0B,QAAA,CAAAlC,IAAA,EAAkC;IADlCvF,EAAA,CAAAoB,UAAA,YAAAgG,QAAA,CAAAK,QAAA,CAAArE,GAAA,EAA4B,YAAApD,EAAA,CAAA2F,eAAA,IAAA2C,GAAA,EAAAS,OAAA,CAAAb,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA;;;;;IAOpC9H,EAAA,CAAAC,cAAA,cAA0F;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA5CH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAoH,QAAA,CAAAK,QAAA,CAAAkB,mBAAA,CAAsC;;;;;;;;;;IAChIhJ,EAAA,CAAAC,cAAA,cAA2F;IAAAD,EAAA,CAAAE,MAAA,GAAkF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAAxFH,EAAA,CAAAI,SAAA,GAAkF;IAAlFJ,EAAA,CAAAK,iBAAA,CAAA4I,OAAA,CAAAC,WAAA,CAAAxH,SAAA,4BAAA1B,EAAA,CAAA2F,eAAA,IAAAwD,GAAA,EAAA1B,QAAA,CAAAK,QAAA,CAAAsB,SAAA,GAAkF;;;;;IAC7KpJ,EAAA,CAAAC,cAAA,cAA4F;IAAAD,EAAA,CAAAE,MAAA,GAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAAzFH,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAK,iBAAA,CAAAgJ,OAAA,CAAAH,WAAA,CAAAxH,SAAA,6BAAA1B,EAAA,CAAA2F,eAAA,IAAAwD,GAAA,EAAA1B,QAAA,CAAAK,QAAA,CAAAsB,SAAA,GAAmF;;;;;IAC/KpJ,EAAA,CAAAC,cAAA,cAA4F;IAAAD,EAAA,CAAAE,MAAA,GAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAAzFH,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAK,iBAAA,CAAAiJ,OAAA,CAAAJ,WAAA,CAAAxH,SAAA,6BAAA1B,EAAA,CAAA2F,eAAA,IAAAwD,GAAA,EAAA1B,QAAA,CAAAK,QAAA,CAAAyB,SAAA,GAAmF;;;;;;;;;;IAC/KvJ,EAAA,CAAAC,cAAA,cAAsF;IAAAD,EAAA,CAAAE,MAAA,GAAuF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAA7FH,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAK,iBAAA,CAAAmJ,OAAA,CAAAN,WAAA,CAAAxH,SAAA,uBAAA1B,EAAA,CAAA2F,eAAA,IAAA8D,GAAA,EAAAD,OAAA,CAAAE,YAAA,CAAAjC,QAAA,CAAAK,QAAA,CAAA6B,GAAA,IAAuF;;;;;IAC7K3J,EAAA,CAAAC,cAAA,cAAsF;IAAAD,EAAA,CAAAE,MAAA,GAAuF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAA7FH,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAK,iBAAA,CAAAuJ,OAAA,CAAAV,WAAA,CAAAxH,SAAA,uBAAA1B,EAAA,CAAA2F,eAAA,IAAA8D,GAAA,EAAAG,OAAA,CAAAF,YAAA,CAAAjC,QAAA,CAAAK,QAAA,CAAA+B,GAAA,IAAuF;;;;;IArBjL7J,EAAA,CAAAC,cAAA,cAAsD;IAClDD,EAAA,CAAAoC,UAAA,IAAA0H,4DAAA,oBAIkD;IAClD9J,EAAA,CAAAoC,UAAA,IAAA2H,oEAAA,4BASgB;IAChB/J,EAAA,CAAAoC,UAAA,IAAA4H,0DAAA,kBAAsI;IACtIhK,EAAA,CAAAoC,UAAA,IAAA6H,0DAAA,kBAAmL;IACnLjK,EAAA,CAAAoC,UAAA,IAAA8H,0DAAA,kBAAqL;IACrLlK,EAAA,CAAAoC,UAAA,IAAA+H,0DAAA,kBAAqL;IACrLnK,EAAA,CAAAoC,UAAA,IAAAgI,0DAAA,kBAAmL;IACnLpK,EAAA,CAAAoC,UAAA,IAAAiI,0DAAA,kBAAmL;IACvLrK,EAAA,CAAAG,YAAA,EAAM;;;;;;IArBiBH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAoB,UAAA,SAAAqG,QAAA,CAAA6C,UAAA,IAAA7C,QAAA,CAAAU,aAAA,cAAwD;IAK3DnI,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAoB,UAAA,SAAAqG,QAAA,CAAA6C,UAAA,IAAA7C,QAAA,CAAAU,aAAA,cAAwD;IAU7CnI,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAoB,UAAA,SAAAmJ,OAAA,CAAArC,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,aAA6D;IAC7D9H,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAoB,UAAA,SAAAmJ,OAAA,CAAArC,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,cAA8D;IAC9D9H,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAoB,UAAA,SAAAmJ,OAAA,CAAArC,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,eAA+D;IAC/D9H,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAoB,UAAA,SAAAmJ,OAAA,CAAArC,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,eAA+D;IAC/D9H,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAoB,UAAA,SAAAmJ,OAAA,CAAArC,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,SAAyD;IACzD9H,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAoB,UAAA,SAAAmJ,OAAA,CAAArC,aAAA,CAAAd,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAqE,QAAA,CAAAK,QAAA,SAAyD;;;;;;IAK5F9H,EAAA,CAAAC,cAAA,eAE+F;IAF5DD,EAAA,CAAAQ,UAAA,mBAAAgK,2FAAA;MAAAxK,EAAA,CAAAW,aAAA,CAAA8J,KAAA;MAAA,MAAAhD,QAAA,GAAAzH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,GAAA4G,SAAA;MAAA,MAAAgD,OAAA,GAAA1K,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAA2J,OAAA,CAAAC,eAAA,CAAAlD,QAAA,EAAAL,QAAA,CAA2B;IAAA,EAAC;IAGpEpH,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHDH,EAAA,CAAAoF,UAAA,CAAAqC,QAAA,CAAAmD,KAAA,CAAoB;IACpB5K,EAAA,CAAA6K,UAAA,CAAApD,QAAA,CAAAqD,gBAAA,GAAArD,QAAA,CAAAqD,gBAAA,CAAA1D,QAAA,CAAAK,QAAA,CAAArE,GAAA,KAAAqE,QAAA,CAAAsD,SAAA,CAAwF;IAC1F/K,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAyF,kBAAA,MAAAgC,QAAA,CAAAuD,eAAA,GAAAvD,QAAA,CAAAuD,eAAA,CAAA5D,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAgE,QAAA,IAAAA,QAAA,CAAAK,QAAA,CAAArE,GAAA,OACJ;;;;;IACQpD,EAAA,CAAAC,cAAA,YAI4F;IACxFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAHDH,EAAA,CAAAoF,UAAA,CAAAqC,QAAA,CAAAmD,KAAA,CAAoB;IACpB5K,EAAA,CAAA6K,UAAA,CAAApD,QAAA,CAAAqD,gBAAA,GAAArD,QAAA,CAAAqD,gBAAA,CAAA1D,QAAA,CAAAK,QAAA,CAAArE,GAAA,KAAAqE,QAAA,CAAAsD,SAAA,CAAwF;IAJxF/K,EAAA,CAAAoB,UAAA,eAAAqG,QAAA,CAAAwD,cAAA,CAAA7D,QAAA,EAAwC;IAKvCpH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAyF,kBAAA,MAAAgC,QAAA,CAAAuD,eAAA,GAAAvD,QAAA,CAAAuD,eAAA,CAAA5D,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAgE,QAAA,IAAAA,QAAA,CAAAK,QAAA,CAAArE,GAAA,OACJ;;;;;IAZJpD,EAAA,CAAAkL,uBAAA,GAAuC;IAC3ClL,EAAA,CAAAoC,UAAA,IAAA+I,oEAAA,mBAIO;IACCnL,EAAA,CAAAoC,UAAA,IAAAgJ,iEAAA,gBAMI;IACRpL,EAAA,CAAAqL,qBAAA,EAAe;;;;IAZZrL,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoB,UAAA,UAAAqG,QAAA,CAAAwD,cAAA,CAA0B;IAOrBjL,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAoB,UAAA,SAAAqG,QAAA,CAAAwD,cAAA,CAAyB;;;;;;;;;;;;IApCrCjL,EAAA,CAAAC,cAAA,aACC;IAEGD,EAAA,CAAAoC,UAAA,IAAAkJ,oDAAA,kBAsBM;IAGNtL,EAAA,CAAAoC,UAAA,IAAAmJ,6DAAA,2BAae;IACnBvL,EAAA,CAAAG,YAAA,EAAK;;;;;IA1C8CH,EAAA,CAAAoF,UAAA,CAAApF,EAAA,CAAAqF,eAAA,IAAAmG,GAAA,EAAA/D,QAAA,CAAAlC,IAAA,EAAAkC,QAAA,CAAAlC,IAAA,EAAAkC,QAAA,CAAAjC,KAAA,EAA4E;IAACxF,EAAA,CAAAoB,UAAA,aAAAqG,QAAA,CAAAgE,aAAA,GAAAhE,QAAA,CAAAiE,oBAAA,GAAAjE,QAAA,CAAAiE,oBAAA,CAAAtE,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAgE,QAAA,IAAAK,QAAA,CAAAuD,eAAA,GAAAvD,QAAA,CAAAuD,eAAA,CAAA5D,QAAA,CAAAK,QAAA,CAAArE,GAAA,GAAAgE,QAAA,IAAAA,QAAA,CAAAK,QAAA,CAAArE,GAAA,OAA2M;IAGjUpD,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAoB,UAAA,SAAAqG,QAAA,CAAA6C,UAAA,CAAqB;IAyBZtK,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAoB,UAAA,UAAAqG,QAAA,CAAA6C,UAAA,CAAsB;;;;;;IAiBjCtK,EAAA,CAAAC,cAAA,eAMC;IAHGD,EAAA,CAAAQ,UAAA,mBAAAmL,4EAAA;MAAA,MAAAC,WAAA,GAAA5L,EAAA,CAAAW,aAAA,CAAAkL,KAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAlE,SAAA;MAAA,MAAAN,QAAA,GAAApH,EAAA,CAAAc,aAAA,IAAA4G,SAAA;MAAA,MAAAqE,QAAA,GAAA/L,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAA+K,OAAA,CAAAE,IAAA,CAAA5E,QAAA,CAAA2E,QAAA,CAAAE,OAAA,GAAA7E,QAAA,CAA4B;IAAA,EAAC;IAGzCpH,EAAA,CAAAG,YAAA,EAAO;;;;IAJJH,EAAA,CAAA6K,UAAA,CAAAiB,OAAA,CAAAI,IAAA,CAAiB;IAEjBlM,EAAA,CAAAoB,UAAA,aAAA0K,OAAA,CAAAK,OAAA,CAAuB;;;;;IANnCnM,EAAA,CAAAC,cAAA,aAA2I;IAEnID,EAAA,CAAAoC,UAAA,IAAAgK,qDAAA,mBAMQ;IACZpM,EAAA,CAAAG,YAAA,EAAM;;;;;IATyEH,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAA2F,eAAA,IAAAC,GAAA,EAAAyG,OAAA,CAAAvG,WAAA,GAAAuG,OAAA,CAAAvG,WAAA,SAAuD;IAE7G9F,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAoB,UAAA,YAAAiL,OAAA,CAAAC,YAAA,CAAAD,OAAA,CAAA3J,OAAA,CAAAoE,MAAA,EAAAM,QAAA,EAAAA,QAAA,CAAAiF,OAAA,CAAAJ,OAAA,GAAoD;;;;;IArDrFjM,EAAA,CAAAC,cAAA,aAAgD;IAC5CD,EAAA,CAAAoC,UAAA,IAAAmK,8CAAA,iBAEK;IACLvM,EAAA,CAAAoC,UAAA,IAAAoK,8CAAA,iBAEK;IACLxM,EAAA,CAAAoC,UAAA,IAAAqK,8CAAA,iBAAqE;IACrEzM,EAAA,CAAAoC,UAAA,IAAAsK,8CAAA,iBA0CK;IACL1M,EAAA,CAAAoC,UAAA,IAAAuK,8CAAA,iBAUK;IACT3M,EAAA,CAAAG,YAAA,EAAK;;;;;IA9DDH,EAAA,CAAAoB,UAAA,oBAAAwL,MAAA,CAAAjG,cAAA,GAAAW,KAAA,MAA2C;IACtCtH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoB,UAAA,SAAAwL,MAAA,CAAAjG,cAAA,CAAoB;IAGpB3G,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAoB,UAAA,SAAAwL,MAAA,CAAAlK,OAAA,CAAAkE,aAAA,CAA2B;IAG3B5G,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAoB,UAAA,SAAAwL,MAAA,CAAAlK,OAAA,CAAAmE,YAAA,CAA0B;IACV7G,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAoB,UAAA,YAAAwL,MAAA,CAAA3G,gBAAA,CAAA2G,MAAA,CAAApJ,OAAA,EAA4B;IA2CVxD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoB,UAAA,SAAAwL,MAAA,CAAAlK,OAAA,CAAAoE,MAAA,CAAoB;;;;;;IAc/D9G,EAAA,CAAAC,cAAA,cAA0H;IACpGD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,wBAAuH;IAAxGD,EAAA,CAAAQ,UAAA,yBAAAqM,qFAAA;MAAA7M,EAAA,CAAAW,aAAA,CAAAmM,KAAA;MAAA,MAAAC,QAAA,GAAA/M,EAAA,CAAAc,aAAA;MAAA,OAAed,EAAA,CAAAe,WAAA,CAAAgM,QAAA,CAAAC,QAAA,CAAAD,QAAA,CAAAE,KAAA,CAAe;IAAA,EAAC,2BAAAC,uFAAAxM,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAmM,KAAA;MAAA,MAAAK,QAAA,GAAAnN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAoM,QAAA,CAAAC,WAAA,GAAA1M,MAAA;IAAA;IAA0EV,EAAA,CAAAG,YAAA,EAAgB;;;;IADtHH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,CAAAgN,QAAA,CAAA5L,YAAA,CAAAC,SAAA,qBAA8C;IACjB1B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAoB,UAAA,YAAAiM,QAAA,CAAAD,WAAA,CAAyB,kBAAAC,QAAA,CAAAC,UAAA;;;;;IAF5EtN,EAAA,CAAAoC,UAAA,IAAAmL,+CAAA,kBAGM;;;;IAH2EvN,EAAA,CAAAoB,UAAA,SAAAoM,MAAA,CAAA9K,OAAA,CAAA+K,eAAA,WAAuC;;;;;;;;ADvFpI,OAAM,MAAOC,kBAAmB,SAAQ5N,aAAa;EACjD6N,YAA6ClM,YAA8B,EAAUmM,QAAkB;IACnG,KAAK,CAACA,QAAQ,CAAC;IAD0B,KAAAnM,YAAY,GAAZA,YAAY;IAA4B,KAAAmM,QAAQ,GAARA,QAAQ;IAIpF,KAAAjM,WAAW,GAAgB,EAAE,CAAC;IAE7B,KAAAkM,iBAAiB,GAA6B,IAAIhO,YAAY,EAAE;IAChE,KAAAiO,cAAc,GAAsB,IAAIjO,YAAY,EAAE;IAWvD,KAAAkO,UAAU,GAAY,CAAC,CAAC;IACxB,KAAAC,QAAQ,GAAU,CAAC,CAAC;IACpB,KAAAC,IAAI,GAAU,EAAE,CAAC,CAAC;IAClB,KAAAC,MAAM,GAAO,EAAE,CAAC;IAEhB,KAAAC,kBAAkB,GAAmB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACzD,KAAAC,YAAY,GAAY,MAAM;IAE9B,KAAAzH,cAAc,GAAY,KAAK;IAC9B,KAAA0H,sBAAsB,GAAsB,IAAIxO,YAAY,EAAE;IAC9D,KAAAyO,kBAAkB,GAAsB,IAAIzO,YAAY,EAAE;IAC1D,KAAA0O,8BAA8B,GAAsB,IAAI1O,YAAY,EAAE;IACvE,KAAAmF,kBAAkB,GAAY,EAAE;IAChC,KAAAF,cAAc,GAAU,CAAC;IACzB,KAAA9D,eAAe,GAAY,KAAK;IAC/B,KAAAwN,qBAAqB,GAA0B,IAAI3O,YAAY,EAAW;IAC3E,KAAA4C,oBAAoB,GAAY,KAAK;IACrC,KAAAgM,iBAAiB,GAAO,EAAE,CAAC,CAAC;IAErC,KAAAtM,aAAa,GAAG,IAAI,CAAC+G,WAAW,CAACxH,SAAS,CAAC,2BAA2B,CAAC;IASvE,KAAAgN,UAAU,GAAQ,EAAE;IACpB,KAAApN,gBAAgB,GAAW,EAAE;EA7C7B;EA+CAqN,QAAQA,CAAA;IACJ,IAAG,CAAC,IAAI,CAACjM,OAAO,EAAC;MACb,IAAI,CAACA,OAAO,GAAG;QACXC,gBAAgB,EAAE,IAAI;QACtBiE,aAAa,EAAE,KAAK;QACpBC,YAAY,EAAE,KAAK;QACnB5C,mBAAmB,EAAE,KAAK;QAC1BwJ,eAAe,EAAE,KAAK;QACtB3G,MAAM,EAAE,EAAE;QACV8H,SAAS,EAAE;OACd;;IAEL,IAAG,IAAI,CAAClM,OAAO,CAACkM,SAAS,KAAK,KAAK,EAAC;MAChC,IAAI,CAAClM,OAAO,CAACkM,SAAS,GAAG,IAAI;MAC7B,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACd,UAAU,GAAC,IAAI,CAACC,QAAQ;MAC7C,IAAI,CAACc,aAAa,GAAG,IAAI,CAACf,UAAU;MACpC,IAAI,CAACX,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC;;IAE1C,IAAIgB,gBAAgB,GAAG,KAAK;IAC5B,IAAG,IAAI,CAACrM,OAAO,CAACuB,mBAAmB,EAAC;MAChC,IAAI+K,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAC/D,IAAGF,gBAAgB,EAAC;QAChBA,gBAAgB,GAAGG,IAAI,CAACC,KAAK,CAACJ,gBAAgB,CAAC;QAC/C,IAAGA,gBAAgB,CAAC,IAAI,CAACK,cAAc,CAACC,QAAQ,CAACC,QAAQ,CAAC,EAAC;UACvD,IAAGP,gBAAgB,CAAC,IAAI,CAACK,cAAc,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACC,OAAO,CAAC,EAAC;YACrE,IAAI,CAACzM,WAAW,GAAG,CAAC,GAAGiM,gBAAgB,CAAC,IAAI,CAACK,cAAc,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC;YAC7FT,gBAAgB,GAAG,IAAI;;;;;IAKvC,IAAG,CAACA,gBAAgB,EAAC;MACjB,IAAI,CAAChM,WAAW,GAAG,IAAI,CAACS,OAAO,CAACiM,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,MAAM,CAAC,CAClCC,GAAG,CAACF,MAAM,IAAIA,MAAM,CAACtM,GAAG,CAAC;;IAE1D,IAAI,CAACyM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAClO,WAAW,CAAC;EAC/C;EAEAuG,aAAaA,CAAC4H,KAAU,EAAEhI,QAAa,EAAEiI,aAAqB;IAC1D,IAAIC,eAAe,GAAG,KAAK;IAC3B,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,UAAU,GAAG,KAAK;IACtB;IACA,IAAIvI,QAAQ,CAACwI,QAAQ,IAAK,CAACR,KAAM,EAAE;MAC/BE,eAAe,GAAG,IAAI,CAAC,CAAC;;IAG5B;IACA,IAAIlI,QAAQ,CAACyI,OAAO,IAAIT,KAAK,EAAE;MAC3B,IAAG,CAAC,IAAIU,MAAM,CAAC1I,QAAQ,CAACyI,OAAO,CAAC,CAACE,IAAI,CAACX,KAAK,CAACY,QAAQ,EAAE,CAAC,EAAE;QACrDT,cAAc,GAAG,IAAI,CAAC,CAAC;;;IAI/B;IACA,IAAInI,QAAQ,CAACsB,SAAS,IAAI0G,KAAK,EAAE;MAC7B,IAAG,OAAOA,KAAK,IAAI,QAAQ,EAAE;QACzB,IAAGA,KAAK,CAAClO,MAAM,GAAGkG,QAAQ,CAACsB,SAAS,EAChC8G,gBAAgB,GAAG,IAAI,CAAC,CAAC;;;IAIrC;IACA,IAAIpI,QAAQ,CAACyB,SAAS,IAAIuG,KAAK,EAAE;MAC7B,IAAG,OAAOA,KAAK,IAAI,QAAQ,EAAE;QACzB,IAAGA,KAAK,CAAClO,MAAM,GAAGkG,QAAQ,CAACyB,SAAS,EAChC4G,gBAAgB,GAAG,IAAI,CAAC,CAAC;;;IAIrC;IACA,IAAIL,KAAK,IAAIhI,QAAQ,CAAC6B,GAAG,IAAImG,KAAK,GAAGhI,QAAQ,CAAC6B,GAAG,IAAI,OAAOmG,KAAK,IAAI,QAAQ,EAAE;MAC3EM,UAAU,GAAG,IAAI,CAAC,CAAC;;IAGvB;IACA,IAAIN,KAAK,IAAIhI,QAAQ,CAAC+B,GAAG,IAAIiG,KAAK,GAAGhI,QAAQ,CAAC+B,GAAG,IAAI,OAAOiG,KAAK,IAAI,QAAQ,EAAE;MAC3EO,UAAU,GAAG,IAAI,CAAC,CAAC;;;IAEvB,IAAGN,aAAa,EAAE;MACd,QAAQA,aAAa;QACjB,KAAK,UAAU;UACX,OAAOC,eAAe;QAC1B,KAAK,SAAS;UACV,OAAOC,cAAc;QACzB,KAAK,WAAW;UACZ,OAAOE,gBAAgB;QAC3B,KAAK,WAAW;UACZ,OAAOD,gBAAgB;QAC3B,KAAK,KAAK;UACN,OAAOE,UAAU;QACrB,KAAK,KAAK;UACN,OAAOC,UAAU;QACrB;UACI,OAAO,KAAK;;;IAGxB,OAAOL,eAAe,IAAIC,cAAc,IAAIE,gBAAgB,IAAID,gBAAgB,IAAIE,UAAU,IAAIC,UAAU;EAChH;EAEA;EACAhI,aAAaA,CAACsI,QAAa,EAAEC,IAAS,EAAEC,IAAgB;IACpD;IACA;IACAD,IAAI,CAACC,IAAI,CAACzN,GAAG,CAAC,GAAGuN,QAAQ,CAAC,CAAE;IAE5B;EACJ;;EAEA1I,WAAWA,CAAC6I,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAE7I,aAAa;IACpD,IAAI8I,SAAS;IACb,QAAQ9I,aAAa;MACjB,KAAK,QAAQ;QAAE;UACX8I,SAAS,GAAGH,KAAK,CAACI,MAAM,CAACpB,KAAK;UAC9B;;MAEJ,KAAK,QAAQ;QAAE;UACXmB,SAAS,GAAGE,MAAM,CAACL,KAAK,CAACI,MAAM,CAACpB,KAAK,CAACsB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;UACzD;;;IAGR,IAAG,CAACL,aAAa,EAAE;MACf,IAAI,CAACjD,cAAc,CAACuD,IAAI,CAAC;QACrBvB,KAAK,EAAGmB,SAAS;QACjBL,IAAI,EAAGI;OACV,CAAC;;EAEV;EAEAM,SAASA,CAAA;IACL,IAAIC,EAAE,GAAG,IAAI;IACb,IAAGpC,IAAI,CAACqC,SAAS,CAAC,IAAI,CAAC7P,WAAW,CAAC,KAAKwN,IAAI,CAACqC,SAAS,CAAC,IAAI,CAAC3B,cAAc,CAAC,EAAC;MACxE4B,UAAU,CAAC,MAAK;QACZ,IAAI,CAAC5D,iBAAiB,CAACwD,IAAI,CAAC,IAAI,CAAC1P,WAAW,CAAC;QAC7C,IAAI,CAACkO,cAAc,GAAG,CAAC,GAAG,IAAI,CAAClO,WAAW,CAAC;MAC/C,CAAC,CAAC;;IAEN,IAAGwN,IAAI,CAACqC,SAAS,CAAC,IAAI,CAACE,OAAO,CAAC,KAAKvC,IAAI,CAACqC,SAAS,CAAC,IAAI,CAAC9C,UAAU,CAAC,EAAC;MAChE,IAAI,CAACA,UAAU,GAAG;QAAC,GAAG,IAAI,CAACgD;MAAO,CAAC;MACnC,IAAG,CAAC,IAAI,CAAChP,OAAO,CAACC,gBAAgB,EAAC;QAC9B,IAAIgP,WAAW,GAAG,IAAI,CAAChQ,WAAW,CAACiO,GAAG,CAACgC,EAAE,IAAIA,EAAE,CAACL,EAAE,CAACtF,OAAO,CAAC,CAAC;QAC5D,IAAI,CAACjK,aAAa,GAAG,IAAI,CAAC0P,OAAO,CAACG,OAAO,CAACpC,MAAM,CAACmC,EAAE,IAAID,WAAW,CAACG,QAAQ,CAACF,EAAE,CAACL,EAAE,CAACtF,OAAO,CAAC,CAAC,CAAC;;;IAGpG,IAAG,IAAI,CAAC8B,UAAU,IAAI,IAAI,CAACe,aAAa,IAAI,IAAI,CAACpM,OAAO,CAACkM,SAAS,EAAC;MAC/D,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACd,UAAU,GAAC,IAAI,CAACC,QAAQ;MAC7C,IAAI,CAACZ,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC;MACtC,IAAI,CAACe,aAAa,GAAG,IAAI,CAACf,UAAU;;IAExC,IAAG,IAAI,CAACpM,WAAW,CAACC,MAAM,IAAI,CAAC,EAAC;MAC5B,IAAI,CAACI,aAAa,GAAG,EAAE;KAC1B,MAAI;MACD,IAAI+P,aAAa,GAAG,IAAI,CAACpQ,WAAW,CAACiO,GAAG,CAACgC,EAAE,IAAIA,EAAE,CAACL,EAAE,CAACtF,OAAO,CAAC,CAAC;MAC9D,IAAI,CAACjK,aAAa,GAAG,IAAI,CAAC0P,OAAO,CAACG,OAAO,CAACpC,MAAM,CAACmC,EAAE,IAAIG,aAAa,CAACD,QAAQ,CAACF,EAAE,CAACL,EAAE,CAACtF,OAAO,CAAC,CAAC,CAAC;;EAEtG;EAEA+F,kBAAkBA,CAAA,GAClB;EAEAC,UAAUA,CAACnB,KAAK;IACZ,IAAI,CAAC9C,QAAQ,GAAG8C,KAAK,CAACoB,IAAI;IAC1B,IAAI,CAACnE,UAAU,GAAG+C,KAAK,CAACqB,KAAK,GAACrB,KAAK,CAACoB,IAAI;IACxC,IAAI,CAAC9E,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC;IACtC,IAAI,CAAC/L,aAAa,GAAG,EAAE;IACvB,IAAG,IAAI,CAACU,OAAO,CAACC,gBAAgB,KAAK,IAAI,EAAC;MACtC,IAAI,CAAChB,WAAW,GAAG,EAAE;;IAEzB,IAAI,CAACyQ,QAAQ,CAAC,IAAI,CAACrE,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC;EACzE;EAEAmE,WAAWA,CAACC,OAAsB;IAC9B,IAAIA,OAAO,CAAC,iBAAiB,CAAC,EAAE;MAC5B,IAAI,CAAChR,gBAAgB,GAAG,IAAI,CAACN,eAAe,GAAG,gBAAgB,GAAG,aAAa;;IAEnF,IAAIsR,OAAO,CAAC,SAAS,CAAC,EAAE;MACpB,MAAMC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC,CAACE,aAAa;MAC7C,MAAMC,IAAI,GAAGH,OAAO,CAAC,SAAS,CAAC,CAACI,YAAY;MAE5C,IAAIH,IAAI,EAAEI,KAAK,KAAKF,IAAI,EAAEE,KAAK,EAAE;QAC5B,IAAI,CAAChR,WAAW,CAACC,MAAM,IAAI6Q,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,IAAI,CAAC,GAAI,IAAI,CAAC3R,eAAe,GAAG,IAAI,GAAG,IAAI,CAACA,eAAe,GAAE,KAAK;QACtH,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAACN,eAAe,GAAG,gBAAgB,GAAG,aAAa;;;IAGvF,IAAIsR,OAAO,CAAC,wBAAwB,CAAC,IAAIA,OAAO,CAAC,wBAAwB,CAAC,CAACI,YAAY,EAAE;MACrF,IAAI,CAACE,eAAe,EAAE;;EAE9B;EAEA3M,gBAAgBA,CAACzC,OAAO;IACpB,OAAOA,OAAO,CAACiM,MAAM,CAACmC,EAAE,IAAI,IAAI,CAAC7O,WAAW,CAAC+O,QAAQ,CAACF,EAAE,CAACxO,GAAG,CAAC,CAAC;EAClE;EAEAkK,UAAUA,CAAA;IACN,IAAG,IAAI,CAACoE,OAAO,CAACiB,KAAK,GAAG,IAAI,CAAC3E,QAAQ,IAAI,CAAC,EAAC;MACvC,OAAO,IAAI,CAAC0D,OAAO,CAACiB,KAAK,GAAC,IAAI,CAAC3E,QAAQ;KAC1C,MAAI;MACD,OAAO6E,IAAI,CAACC,IAAI,CAAC,IAAI,CAACpB,OAAO,CAACiB,KAAK,GAAC,IAAI,CAAC3E,QAAQ,CAAC;;EAE1D;EAEAhB,QAAQA,CAAA;IACJ,IAAI,CAAChL,aAAa,GAAG,EAAE;IACvB,IAAG,IAAI,CAACU,OAAO,CAACC,gBAAgB,KAAK,IAAI,EAAC;MACtC,IAAI,CAAChB,WAAW,GAAG,EAAE;;IAEzB,IAAI,CAACoM,UAAU,GAAG,IAAI,CAACX,WAAW,GAAG,CAAC;IACtC,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACd,UAAU,GAAG,IAAI,CAACC,QAAQ;IAC/C,IAAI,CAACoE,QAAQ,CAAC,IAAI,CAACrE,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC;EACzE;EAEA6E,UAAUA,CAACjC,KAAK;IACZ,IAAG,CAAC,IAAI,CAACkC,SAAS,CAAClC,KAAK,CAACmC,KAAK,CAAC,EAAC;MAC5B;;IAEJ,IAAG9D,IAAI,CAACqC,SAAS,CAAC,IAAI,CAAC0B,OAAO,CAAC,IAAI/D,IAAI,CAACqC,SAAS,CAACV,KAAK,CAAC,EAAE;IAC1D,IAAI,CAACoC,OAAO,GAAGpC,KAAK;IACpB,IAAI,CAAC9O,aAAa,GAAG,EAAE;IACvB,IAAG,IAAI,CAACU,OAAO,CAACC,gBAAgB,KAAK,IAAI,EAAC;MACtC,IAAI,CAAChB,WAAW,GAAG,EAAE;;IAEzB,IAAI,CAACsM,IAAI,GAAG,GAAG6C,KAAK,CAACmC,KAAK,IAAInC,KAAK,CAACqC,KAAK,IAAI,CAAC,GAAG,KAAK,GAAE,MAAM,EAAE;IAChE,IAAI,CAACf,QAAQ,CAAC,IAAI,CAACrE,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC;EACzE;EAEA8E,SAASA,CAACC,KAAK;IACX,KAAI,IAAIG,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAAC5P,OAAO,CAAC5B,MAAM,EAACwR,CAAC,EAAE,EAAC;MACpC,IAAI1D,MAAM,GAAG,IAAI,CAAClM,OAAO,CAAC4P,CAAC,CAAC;MAC5B,IAAG1D,MAAM,CAACtM,GAAG,IAAI6P,KAAK,EAAC;QACnB,OAAOvD,MAAM,CAAChK,MAAM;;;IAG5B,OAAO,KAAK;EAChB;EAEA2N,YAAYA,CAAA;IACR,IAAG,IAAI,CAACpF,IAAI,EAAC;MACT,OAAO,IAAI,CAACA,IAAI,CAACqF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAElC,OAAO,EAAE;EACb;EAEAC,YAAYA,CAAA;IACR,IAAG,IAAI,CAACtF,IAAI,EAAC;MACT,IAAIuF,QAAQ,GAAG,IAAI,CAACvF,IAAI,CAACqF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtC,OAAOE,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;IAErC,OAAO,EAAE;EACb;EAEAzM,UAAUA,CAAA;IACN,OAAO,CAAC,IAAI,CAAC2K,OAAO,EAAEG,OAAO,IAAI,EAAE,EAAEjQ,MAAM,IAAI,CAAC;EACpD;EAEAuE,mBAAmBA,CAAA;IACf,OAAO,IAAI,CAACF,gBAAgB,CAAC,IAAI,CAACzC,OAAO,CAAC,CAAC5B,MAAM,IACtC,IAAI,CAACc,OAAO,CAACoE,MAAM,GAAC,CAAC,GAAC,CAAC,CAAC,IAAI,IAAI,CAACpE,OAAO,CAACuB,mBAAmB,IAAI,IAAI,GAAC,CAAC,GAAC,CAAC,CAAC,IACzE,IAAI,CAACvB,OAAO,CAACmE,YAAY,GAAC,CAAC,GAAC,CAAC,CAAC,IAAI,IAAI,CAACnE,OAAO,CAACkE,aAAa,GAAC,CAAC,GAAC,CAAC,CAAC;EAChF;EAEA0F,YAAYA,CAACmH,OAA0B,EAAE5C,IAAS,EAAE6C,EAAU;IAC1D,OAAOD,OAAO,CAAChE,MAAM,CAACmC,EAAE,IAAG;MACvB,OAAOA,EAAE,CAAC+B,UAAU,IAAIC,SAAS,IAAIhC,EAAE,CAAC+B,UAAU,CAACD,EAAE,EAAE7C,IAAI,CAAC;IAChE,CAAC,CAAC;EACN;EAEAgD,qBAAqBA,CAACC,MAAM;IACxB,IAAIvC,EAAE,GAAG,IAAI;IACb,IAAG,CAAC,IAAI,CAAC7O,OAAO,CAACC,gBAAgB,EAAC;MAC9B,IAAIoR,QAAQ,GAAG,IAAI,CAACrC,OAAO,CAACG,OAAO,CAACjC,GAAG,CAACgC,EAAE,IAAIA,EAAE,CAACL,EAAE,CAACtF,OAAO,CAAC,CAAC;MAC7D,IAAI+H,UAAU,GAAG,IAAI,CAACrS,WAAW,CAAC8N,MAAM,CAACmC,EAAE,IAAI,CAACmC,QAAQ,CAACjC,QAAQ,CAACF,EAAE,CAACL,EAAE,CAACtF,OAAO,CAAC,CAAC,CAAC;MAClF,IAAI,CAACtK,WAAW,GAAG,CAAC,GAAGqS,UAAU,EAAE,GAAGF,MAAM,CAAC;KAChD,MAAI;MACD,IAAI,CAACnS,WAAW,GAAG,CAAC,GAAGmS,MAAM,CAAC;;EAEtC;EAEAnJ,eAAeA,CAACkG,IAAgB,EAAED,IAAI;IAClC,IAAGC,IAAI,CAACoD,SAAS,EAAC;MACdpD,IAAI,CAACoD,SAAS,CAACrD,IAAI,CAAC,IAAI,CAAC3E,OAAO,CAAC,EAAE2E,IAAI,CAAC;;EAEhD;EAEAsD,iBAAiBA,CAACpD,KAAK;IACnB,IAAIS,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4C,WAAW,CAACC,eAAe,CAACtD,KAAK,CAACI,MAAM,CAACmD,SAAS,EAAE,MAAK;MACxD9C,EAAE,CAAC+C,oBAAoB,CAACC,OAAO,CAAChD,EAAE,CAACrI,WAAW,CAACxH,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACtF,CAAC,CAAC;IACFoP,KAAK,CAAC0D,cAAc,EAAE;EAC1B;EAEAvR,iBAAiBA,CAAC6M,KAAK;IACnB,IAAG,IAAI,CAACpN,OAAO,CAACuB,mBAAmB,EAAC;MAChC,IAAG,IAAI,CAACoL,cAAc,CAACC,QAAQ,EAAC;QAC5B,IAAG,CAAC,IAAI,CAACE,OAAO,IAAI,EAAE,EAAEiF,IAAI,EAAE,CAAC7S,MAAM,GAAG,CAAC,EAAC;UACtC,IAAIoN,gBAAgB,GAAQC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;UACpE,IAAGF,gBAAgB,EAAC;YAChBA,gBAAgB,GAAGG,IAAI,CAACC,KAAK,CAACJ,gBAAgB,CAAC;YAC/CA,gBAAgB,CAAC,IAAI,CAACK,cAAc,CAACC,QAAQ,CAACC,QAAQ,CAAC,GAAG;cAAC,CAAC,IAAI,CAACC,OAAO,GAAGM;YAAK,CAAC;WACpF,MAAI;YACDd,gBAAgB,GAAG;cAAC,CAAC,IAAI,CAACK,cAAc,CAACC,QAAQ,CAACC,QAAQ,GAAG;gBAAC,CAAC,IAAI,CAACC,OAAO,GAAGM;cAAK;YAAC,CAAC;;UAEzFb,YAAY,CAACyF,OAAO,CAAC,kBAAkB,EAAEvF,IAAI,CAACqC,SAAS,CAACxC,gBAAgB,CAAC,CAAC;;;;EAI1F;EAEAnH,WAAWA,CAACiJ,KAAK,EAAEhJ,QAAQ;IACvB,IAAGA,QAAQ,CAACD,WAAW,EAAE;MACrB,IAAI,CAACC,QAAQ,CAACD,WAAW,CAACiJ,KAAK,CAAC,EAAE;QAC9BA,KAAK,CAAC0D,cAAc,EAAE;;;EAGlC;EAEA7P,iBAAiBA,CAAA;IACb,IAAI,CAAC0J,sBAAsB,CAACgD,IAAI,EAAE;EACtC;EAEAlQ,uBAAuBA,CAAC2P,KAAK;IACzB,IAAI,CAACvC,8BAA8B,CAAC8C,IAAI,EAAE;EAC9C;EAEAlK,mBAAmBA,CAAA;IACf,IAAI,CAACmH,kBAAkB,CAAC+C,IAAI,EAAE;EAClC;EAEAZ,IAAIA,CAACK,KAA+B,GAEpC;EAEApH,YAAYA,CAACoG,KAAa;IACtB,OAAO,IAAI6E,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC/E,KAAK,CAAC;EACvD;EAEA;EACA8C,eAAeA,CAAA;IACX,IAAI,CAAC7E,UAAU,GAAG,CAAC;EACvB;;;uBAxYSL,kBAAkB,EAAA1N,EAAA,CAAA8U,iBAAA,CACP/U,gBAAgB,GAAAC,EAAA,CAAA8U,iBAAA,CAAA9U,EAAA,CAAA+U,QAAA;IAAA;EAAA;;;YAD3BrH,kBAAkB;MAAAsH,SAAA;MAAAC,MAAA;QAAAzF,OAAA;QAAA7N,WAAA;QAAApB,UAAA;QAAAiD,OAAA;QAAAkO,OAAA;QAAAU,QAAA;QAAA1P,OAAA;QAAAqL,UAAA;QAAAC,QAAA;QAAAC,IAAA;QAAAC,MAAA;QAAAjC,OAAA;QAAAkC,kBAAA;QAAAC,YAAA;QAAAtI,WAAA;QAAAa,cAAA;QAAA3B,kBAAA;QAAAF,cAAA;QAAA9D,eAAA;QAAAyB,oBAAA;QAAAgM,iBAAA;MAAA;MAAAyG,OAAA;QAAArH,iBAAA;QAAAC,cAAA;QAAAO,sBAAA;QAAAC,kBAAA;QAAAC,8BAAA;QAAAC,qBAAA;MAAA;MAAA2G,QAAA,GAAAnV,EAAA,CAAAoV,0BAAA,EAAApV,EAAA,CAAAqV,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvD/B3V,EAAA,CAAAC,cAAA,aAA+F;UAC3FD,EAAA,CAAAoC,UAAA,IAAAyT,iCAAA,iBA4BM;UACN7V,EAAA,CAAAC,cAAA,oBAyBC;UAjBGD,EAAA,CAAAQ,UAAA,oBAAAsV,sDAAApV,MAAA;YAAA,OAAUkV,GAAA,CAAA3D,UAAA,CAAAvR,MAAA,CAAkB;UAAA,EAAC,6BAAAqV,+DAAArV,MAAA;YAAA,OAAAkV,GAAA,CAAA5T,aAAA,GAAAtB,MAAA;UAAA,sBAAAsV,sDAAAtV,MAAA;YAAA,OAMnBkV,GAAA,CAAA7C,UAAA,CAAArS,MAAA,CAAkB;UAAA,EANC,6BAAAuV,+DAAAvV,MAAA;YAAA,OAWVkV,GAAA,CAAAnF,IAAA,CAAA/P,MAAA,CAAY;UAAA,EAXF,6BAAAqV,+DAAArV,MAAA;YAAA,OAYVkV,GAAA,CAAA/B,qBAAA,CAAAnT,MAAA,CAA6B;UAAA,EAZnB;UAkB7BV,EAAA,CAAAoC,UAAA,IAAA8T,yCAAA,yBAmBc;UACdlW,EAAA,CAAAoC,UAAA,IAAA+T,yCAAA,yBAgEc;UACdnW,EAAA,CAAAoC,UAAA,IAAAgU,yCAAA,yBAKc;UAClBpW,EAAA,CAAAG,YAAA,EAAU;;;UAnJqDH,EAAA,CAAAoF,UAAA,CAAAwQ,GAAA,CAAAnH,iBAAA,CAA2B;UACjCzO,EAAA,CAAAI,SAAA,GAA4H;UAA5HJ,EAAA,CAAAoB,UAAA,UAAAwU,GAAA,CAAAlT,OAAA,CAAAC,gBAAA,IAAAiT,GAAA,CAAAjU,WAAA,CAAAC,MAAA,QAAAgU,GAAA,CAAArV,UAAA,IAAAqV,GAAA,CAAAlT,OAAA,CAAAuB,mBAAA,IAAA2R,GAAA,CAAAnT,oBAAA,CAA4H;UA8BjLzC,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAoB,UAAA,UAAAwU,GAAA,CAAAlE,OAAA,kBAAAkE,GAAA,CAAAlE,OAAA,CAAAG,OAAA,CAA0B,eAAA+D,GAAA,CAAAlE,OAAA,kBAAAkE,GAAA,CAAAlE,OAAA,CAAAiB,KAAA,SAAAiD,GAAA,CAAAlT,OAAA,CAAAkM,SAAA,UAAAgH,GAAA,CAAA5H,QAAA,WAAA4H,GAAA,CAAA/G,QAAA,+CAAA7O,EAAA,CAAAqW,eAAA,KAAAC,GAAA,gCAAAV,GAAA,CAAAnU,YAAA,CAAAC,SAAA,8DAAAkU,GAAA,CAAAzH,kBAAA,gEAAAyH,GAAA,CAAA5T,aAAA,kBAAA4T,GAAA,CAAAlE,OAAA,kBAAAkE,GAAA,CAAAlE,OAAA,CAAAiB,KAAA,iDAAAiD,GAAA,CAAAvC,YAAA,iBAAAuC,GAAA,CAAArC,YAAA,8CAAAqC,GAAA,CAAAxH,YAAA,4CAAAwH,GAAA,CAAAjP,cAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}