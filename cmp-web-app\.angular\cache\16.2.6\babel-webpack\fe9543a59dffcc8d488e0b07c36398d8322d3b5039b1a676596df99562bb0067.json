{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Kannada [kn]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/rajeevnaikte\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '೧',\n      2: '೨',\n      3: '೩',\n      4: '೪',\n      5: '೫',\n      6: '೬',\n      7: '೭',\n      8: '೮',\n      9: '೯',\n      0: '೦'\n    },\n    numberMap = {\n      '೧': '1',\n      '೨': '2',\n      '೩': '3',\n      '೪': '4',\n      '೫': '5',\n      '೬': '6',\n      '೭': '7',\n      '೮': '8',\n      '೯': '9',\n      '೦': '0'\n    };\n  var kn = moment.defineLocale('kn', {\n    months: 'ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್'.split('_'),\n    monthsShort: 'ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ'.split('_'),\n    weekdaysShort: 'ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ'.split('_'),\n    weekdaysMin: 'ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm'\n    },\n    calendar: {\n      sameDay: '[ಇಂದು] LT',\n      nextDay: '[ನಾಳೆ] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[ನಿನ್ನೆ] LT',\n      lastWeek: '[ಕೊನೆಯ] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ನಂತರ',\n      past: '%s ಹಿಂದೆ',\n      s: 'ಕೆಲವು ಕ್ಷಣಗಳು',\n      ss: '%d ಸೆಕೆಂಡುಗಳು',\n      m: 'ಒಂದು ನಿಮಿಷ',\n      mm: '%d ನಿಮಿಷ',\n      h: 'ಒಂದು ಗಂಟೆ',\n      hh: '%d ಗಂಟೆ',\n      d: 'ಒಂದು ದಿನ',\n      dd: '%d ದಿನ',\n      M: 'ಒಂದು ತಿಂಗಳು',\n      MM: '%d ತಿಂಗಳು',\n      y: 'ಒಂದು ವರ್ಷ',\n      yy: '%d ವರ್ಷ'\n    },\n    preparse: function (string) {\n      return string.replace(/[೧೨೩೪೫೬೭೮೯೦]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    meridiemParse: /ರಾತ್ರಿ|ಬೆಳಿಗ್ಗೆ|ಮಧ್ಯಾಹ್ನ|ಸಂಜೆ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'ರಾತ್ರಿ') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'ಬೆಳಿಗ್ಗೆ') {\n        return hour;\n      } else if (meridiem === 'ಮಧ್ಯಾಹ್ನ') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'ಸಂಜೆ') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ರಾತ್ರಿ';\n      } else if (hour < 10) {\n        return 'ಬೆಳಿಗ್ಗೆ';\n      } else if (hour < 17) {\n        return 'ಮಧ್ಯಾಹ್ನ';\n      } else if (hour < 20) {\n        return 'ಸಂಜೆ';\n      } else {\n        return 'ರಾತ್ರಿ';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ನೇ)/,\n    ordinal: function (number) {\n      return number + 'ನೇ';\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n\n  return kn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "kn", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/moment/locale/kn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Kannada [kn]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/rajeevnaikte\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '೧',\n            2: '೨',\n            3: '೩',\n            4: '೪',\n            5: '೫',\n            6: '೬',\n            7: '೭',\n            8: '೮',\n            9: '೯',\n            0: '೦',\n        },\n        numberMap = {\n            '೧': '1',\n            '೨': '2',\n            '೩': '3',\n            '೪': '4',\n            '೫': '5',\n            '೬': '6',\n            '೭': '7',\n            '೮': '8',\n            '೯': '9',\n            '೦': '0',\n        };\n\n    var kn = moment.defineLocale('kn', {\n        months: 'ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್'.split(\n            '_'\n        ),\n        monthsShort:\n            'ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ'.split(\n            '_'\n        ),\n        weekdaysShort: 'ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ'.split('_'),\n        weekdaysMin: 'ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm',\n            LTS: 'A h:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm',\n        },\n        calendar: {\n            sameDay: '[ಇಂದು] LT',\n            nextDay: '[ನಾಳೆ] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[ನಿನ್ನೆ] LT',\n            lastWeek: '[ಕೊನೆಯ] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s ನಂತರ',\n            past: '%s ಹಿಂದೆ',\n            s: 'ಕೆಲವು ಕ್ಷಣಗಳು',\n            ss: '%d ಸೆಕೆಂಡುಗಳು',\n            m: 'ಒಂದು ನಿಮಿಷ',\n            mm: '%d ನಿಮಿಷ',\n            h: 'ಒಂದು ಗಂಟೆ',\n            hh: '%d ಗಂಟೆ',\n            d: 'ಒಂದು ದಿನ',\n            dd: '%d ದಿನ',\n            M: 'ಒಂದು ತಿಂಗಳು',\n            MM: '%d ತಿಂಗಳು',\n            y: 'ಒಂದು ವರ್ಷ',\n            yy: '%d ವರ್ಷ',\n        },\n        preparse: function (string) {\n            return string.replace(/[೧೨೩೪೫೬೭೮೯೦]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        meridiemParse: /ರಾತ್ರಿ|ಬೆಳಿಗ್ಗೆ|ಮಧ್ಯಾಹ್ನ|ಸಂಜೆ/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'ರಾತ್ರಿ') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'ಬೆಳಿಗ್ಗೆ') {\n                return hour;\n            } else if (meridiem === 'ಮಧ್ಯಾಹ್ನ') {\n                return hour >= 10 ? hour : hour + 12;\n            } else if (meridiem === 'ಸಂಜೆ') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'ರಾತ್ರಿ';\n            } else if (hour < 10) {\n                return 'ಬೆಳಿಗ್ಗೆ';\n            } else if (hour < 17) {\n                return 'ಮಧ್ಯಾಹ್ನ';\n            } else if (hour < 20) {\n                return 'ಸಂಜೆ';\n            } else {\n                return 'ರಾತ್ರಿ';\n            }\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(ನೇ)/,\n        ordinal: function (number) {\n            return number + 'ನೇ';\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return kn;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,4FAA4F,CAACC,KAAK,CACtG,GACJ,CAAC;IACDC,WAAW,EACP,0EAA0E,CAACD,KAAK,CAC5E,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,yDAAyD,CAACH,KAAK,CACrE,GACJ,CAAC;IACDI,aAAa,EAAE,oCAAoC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9DK,WAAW,EAAE,uBAAuB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC/CM,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,qBAAqB;MAC1BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,eAAe;MACnBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAO1C,SAAS,CAAC0C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAO3C,SAAS,CAAC2C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDE,aAAa,EAAE,+BAA+B;IAC9CC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACvB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU;MACrB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU;MACrB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDI,sBAAsB,EAAE,cAAc;IACtCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OAAOA,MAAM,GAAG,KAAK;IACzB,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOtD,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}