{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AppRolesRoutingModule } from './app.roles-routing.module';\nimport { AppRolesListComponent } from './list/app.roles.list.component';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FieldsetModule } from 'primeng/fieldset';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonVnptModule } from '../../common-module/common.module';\nimport { SplitterModule } from 'primeng/splitter';\nimport { ButtonModule } from 'primeng/button';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { DialogModule } from 'primeng/dialog';\nimport { CardModule } from 'primeng/card';\nimport { RolesService } from 'src/app/service/account/RolesService';\nimport { TreeSelectModule } from 'primeng/treeselect';\nimport { AppRolesCreateComponent } from './create/app.roles.create.component';\nimport { AppRolesDetailComponent } from \"./detail/app.roles.detail.component\";\nimport { AppRolesEditComponent } from \"./edit/app.roles.edit.component\";\nimport { PanelModule } from 'primeng/panel';\nimport * as i0 from \"@angular/core\";\nexport class AppRolesModule {\n  static {\n    this.ɵfac = function AppRolesModule_Factory(t) {\n      return new (t || AppRolesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRolesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [RolesService],\n      imports: [CommonModule, AppRolesRoutingModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, DropdownModule, AutoCompleteModule, DialogModule, SplitterModule, ToggleButtonModule, TreeSelectModule, CardModule, PanelModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRolesModule, {\n    declarations: [AppRolesListComponent, AppRolesCreateComponent, AppRolesDetailComponent, AppRolesEditComponent],\n    imports: [CommonModule, AppRolesRoutingModule, BreadcrumbModule, FieldsetModule, FormsModule, ReactiveFormsModule, InputTextModule, ButtonModule, CommonVnptModule, SplitButtonModule, DropdownModule, AutoCompleteModule, DialogModule, SplitterModule, ToggleButtonModule, TreeSelectModule, CardModule, PanelModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AppRolesRoutingModule", "AppRolesListComponent", "BreadcrumbModule", "FieldsetModule", "InputTextModule", "DropdownModule", "ToggleButtonModule", "FormsModule", "ReactiveFormsModule", "CommonVnptModule", "SplitterModule", "ButtonModule", "SplitButtonModule", "AutoCompleteModule", "DialogModule", "CardModule", "RolesService", "TreeSelectModule", "AppRolesCreateComponent", "AppRolesDetailComponent", "AppRolesEditComponent", "PanelModule", "AppRolesModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\roles\\app.roles.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AppRolesRoutingModule } from './app.roles-routing.module';\r\nimport { AppRolesListComponent } from './list/app.roles.list.component';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { FieldsetModule } from 'primeng/fieldset';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { ToggleButtonModule } from 'primeng/togglebutton';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { CommonVnptModule } from '../../common-module/common.module';\r\nimport { SplitterModule } from 'primeng/splitter';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { SplitButtonModule } from 'primeng/splitbutton';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { CardModule } from 'primeng/card';\r\nimport { RolesService } from 'src/app/service/account/RolesService';\r\nimport { TreeSelectModule } from 'primeng/treeselect';\r\nimport { AppRolesCreateComponent } from './create/app.roles.create.component';\r\nimport {AppRolesDetailComponent} from \"./detail/app.roles.detail.component\";\r\nimport {AppRolesEditComponent} from \"./edit/app.roles.edit.component\";\r\nimport { PanelModule } from 'primeng/panel';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppRolesListComponent,\r\n    AppRolesCreateComponent,\r\n    AppRolesDetailComponent,\r\n    AppRolesEditComponent\r\n  ],\r\n    imports: [\r\n        CommonModule,\r\n        AppRolesRoutingModule,\r\n        BreadcrumbModule,\r\n        FieldsetModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        InputTextModule,\r\n        ButtonModule,\r\n        CommonVnptModule,\r\n        SplitButtonModule,\r\n        DropdownModule,\r\n        AutoCompleteModule,\r\n        DialogModule,\r\n        SplitterModule,\r\n        ToggleButtonModule,\r\n        TreeSelectModule,\r\n        CardModule,\r\n        PanelModule\r\n    ],\r\n  providers:[\r\n    RolesService\r\n  ]\r\n})\r\nexport class AppRolesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,sCAAsC;AACnE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,uBAAuB,QAAQ,qCAAqC;AAC7E,SAAQC,uBAAuB,QAAO,qCAAqC;AAC3E,SAAQC,qBAAqB,QAAO,iCAAiC;AACrE,SAASC,WAAW,QAAQ,eAAe;;AAiC3C,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAJf,CACRN,YAAY,CACb;MAAAO,OAAA,GArBKxB,YAAY,EACZC,qBAAqB,EACrBE,gBAAgB,EAChBC,cAAc,EACdI,WAAW,EACXC,mBAAmB,EACnBJ,eAAe,EACfO,YAAY,EACZF,gBAAgB,EAChBG,iBAAiB,EACjBP,cAAc,EACdQ,kBAAkB,EAClBC,YAAY,EACZJ,cAAc,EACdJ,kBAAkB,EAClBW,gBAAgB,EAChBF,UAAU,EACVM,WAAW;IAAA;EAAA;;;2EAMNC,cAAc;IAAAE,YAAA,GA7BvBvB,qBAAqB,EACrBiB,uBAAuB,EACvBC,uBAAuB,EACvBC,qBAAqB;IAAAG,OAAA,GAGjBxB,YAAY,EACZC,qBAAqB,EACrBE,gBAAgB,EAChBC,cAAc,EACdI,WAAW,EACXC,mBAAmB,EACnBJ,eAAe,EACfO,YAAY,EACZF,gBAAgB,EAChBG,iBAAiB,EACjBP,cAAc,EACdQ,kBAAkB,EAClBC,YAAY,EACZJ,cAAc,EACdJ,kBAAkB,EAClBW,gBAAgB,EAChBF,UAAU,EACVM,WAAW;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}