{"ast": null, "code": "import { HttpService } from \"../comon/http.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../comon/http.service\";\nexport class GroupSubWalletService {\n  constructor(httpService) {\n    this.httpService = httpService;\n    this.prefixApi = \"/group-sub-wallet\";\n  }\n  search(params, callback, errorCallBack, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);\n  }\n  create(headers, data, params, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/create`, headers, data, params, callback, errorCallback, finallyCallback);\n  }\n  addPhoneInGroup(headers, data, params, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/add-phone-in-group`, headers, data, params, callback, errorCallback, finallyCallback);\n  }\n  update(id, body, callback, errorCallback, finallyCallback) {\n    this.httpService.put(`${this.prefixApi}/update/${id}`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  getDetail(id, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  delete(id, callback, errorCallBack, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  deleteSubInGroup(id, callback, errorCallBack, finallyCallback) {\n    this.httpService.delete(`${this.prefixApi}/delete-sub-in-group/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\n  }\n  searchNotInGroup(params, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/get-sim-not-in-group`, {}, params, callback, errorCallback, finallyCallback);\n  }\n  searchInGroup(idGroup, params, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/get-sim-in-group/${idGroup}`, {}, params, callback, errorCallback, finallyCallback);\n  }\n  deleteMany(ids, callback, errorCallBack, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/delete-many-sub`, {}, {\n      ids\n    }, {}, callback, errorCallBack, finallyCallback);\n  }\n  getAllGroup(callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/get-all`, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  checkPhoneBelongGroup(body, callback, errorCallback, finallyCallback) {\n    this.httpService.post(`${this.prefixApi}/check-phone`, {}, body, {}, callback, errorCallback, finallyCallback);\n  }\n  checkExistGroupCode(params, callback, errorCallback, finallyCallback) {\n    this.httpService.get(`${this.prefixApi}/check-group-code`, {}, params, callback, errorCallback, finallyCallback);\n  }\n  uploadFile(objectFile, body, callback, errorCallback, finallyCallback) {\n    this.httpService.uploadContainBody(`${this.prefixApi}/import/share_info`, objectFile, body, {}, {}, callback, errorCallback, finallyCallback);\n  }\n  downloadTemplate() {\n    this.httpService.downloadLocal(`/assets/data/Mẫu_file_thông_tin_người_chia_sẻ.xlsx`, \"Mẫu_file_thông_tin_người_chia_sẻ.xlsx\");\n  }\n  static {\n    this.ɵfac = function GroupSubWalletService_Factory(t) {\n      return new (t || GroupSubWalletService)(i0.ɵɵinject(HttpService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GroupSubWalletService,\n      factory: GroupSubWalletService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpService", "GroupSubWalletService", "constructor", "httpService", "prefixApi", "search", "params", "callback", "errorCallBack", "finally<PERSON><PERSON><PERSON>", "get", "create", "headers", "data", "<PERSON><PERSON><PERSON><PERSON>", "post", "addPhoneInGroup", "update", "id", "body", "put", "getDetail", "delete", "deleteSubInGroup", "searchNotInGroup", "searchInGroup", "idGroup", "deleteMany", "ids", "getAllGroup", "checkPhoneBelongGroup", "checkExistGroupCode", "uploadFile", "objectFile", "uploadContainBody", "downloadTemplate", "downloadLocal", "i0", "ɵɵinject", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\group-sub-wallet\\GroupSubWalletService.ts"], "sourcesContent": ["import {Inject, Injectable} from \"@angular/core\";\r\nimport {HttpService} from \"../comon/http.service\";\r\n\r\n@Injectable()\r\nexport class GroupSubWalletService {\r\n    private prefixApi: string;\r\n    constructor(@Inject(HttpService) private httpService:HttpService) {\r\n        this.prefixApi = \"/group-sub-wallet\";\r\n    }\r\n\r\n    public search(params?:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback)\r\n    }\r\n\r\n    public create(headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}/create`,headers,data,params,callback,errorCallback,finallyCallback)\r\n    }\r\n\r\n    public addPhoneInGroup(headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function){\r\n        this.httpService.post(`${this.prefixApi}/add-phone-in-group`,headers,data,params,callback,errorCallback,finallyCallback)\r\n    }\r\n\r\n    public update(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.put(`${this.prefixApi}/update/${id}`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public getDetail(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/${id}`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public delete(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.delete(`${this.prefixApi}/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public deleteSubInGroup(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){\r\n        this.httpService.delete(`${this.prefixApi}/delete-sub-in-group/${id}`, {}, {}, callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public searchNotInGroup(params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/get-sim-not-in-group`,{}, params,callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public searchInGroup(idGroup: number, params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/get-sim-in-group/${idGroup}`,{}, params,callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public deleteMany(ids: number[], callback?: Function, errorCallBack?: Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/delete-many-sub`,{},{ids},{},callback, errorCallBack, finallyCallback);\r\n    }\r\n\r\n    public getAllGroup(callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/get-all`,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n\r\n    public checkPhoneBelongGroup(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.post(`${this.prefixApi}/check-phone`, {},body,{}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public checkExistGroupCode(params, callback?:Function, errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.get(`${this.prefixApi}/check-group-code`,{}, params,callback, errorCallback, finallyCallback);\r\n    }\r\n    public uploadFile(objectFile,body, callback:Function,errorCallback?:Function, finallyCallback?: Function){\r\n        this.httpService.uploadContainBody(`${this.prefixApi}/import/share_info`, objectFile, body,{}, {}, callback, errorCallback, finallyCallback);\r\n    }\r\n    public downloadTemplate(){\r\n        this.httpService.downloadLocal(`/assets/data/Mẫu_file_thông_tin_người_chia_sẻ.xlsx`, \"Mẫu_file_thông_tin_người_chia_sẻ.xlsx\");\r\n    }\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,uBAAuB;;;AAGjD,OAAM,MAAOC,qBAAqB;EAE9BC,YAAyCC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAChD,IAAI,CAACC,SAAS,GAAG,mBAAmB;EACxC;EAEOC,MAAMA,CAACC,MAA+B,EAAEC,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACjH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,SAAS,EAAE,EAAE,EAAEE,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC1G;EAEOE,MAAMA,CAACC,OAA0B,EAAEC,IAAS,EAACP,MAA0B,EAAEC,QAAkB,EAAEO,aAAuB,EAAEL,eAAyB;IAClJ,IAAI,CAACN,WAAW,CAACY,IAAI,CAAC,GAAG,IAAI,CAACX,SAAS,SAAS,EAACQ,OAAO,EAACC,IAAI,EAACP,MAAM,EAACC,QAAQ,EAACO,aAAa,EAACL,eAAe,CAAC;EAChH;EAEOO,eAAeA,CAACJ,OAA0B,EAAEC,IAAS,EAACP,MAA0B,EAAEC,QAAkB,EAAEO,aAAuB,EAAEL,eAAyB;IAC3J,IAAI,CAACN,WAAW,CAACY,IAAI,CAAC,GAAG,IAAI,CAACX,SAAS,qBAAqB,EAACQ,OAAO,EAACC,IAAI,EAACP,MAAM,EAACC,QAAQ,EAACO,aAAa,EAACL,eAAe,CAAC;EAC5H;EAEOQ,MAAMA,CAACC,EAAE,EAACC,IAAI,EAACZ,QAAmB,EAAEO,aAAuB,EAAEL,eAA0B;IAC1F,IAAI,CAACN,WAAW,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,SAAS,WAAWc,EAAE,EAAE,EAAE,EAAE,EAACC,IAAI,EAAC,EAAE,EAAEZ,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EAChH;EAEOY,SAASA,CAACH,EAAU,EAAEX,QAAmB,EAAEO,aAAuB,EAAEL,eAA0B;IACjG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,IAAIc,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAAEX,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EACpG;EAEOa,MAAMA,CAACJ,EAAU,EAAEX,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IAC5F,IAAI,CAACN,WAAW,CAACmB,MAAM,CAAC,GAAG,IAAI,CAAClB,SAAS,IAAIc,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEX,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACxG;EAEOc,gBAAgBA,CAACL,EAAU,EAAEX,QAAkB,EAAEC,aAAuB,EAAEC,eAAyB;IACtG,IAAI,CAACN,WAAW,CAACmB,MAAM,CAAC,GAAG,IAAI,CAAClB,SAAS,wBAAwBc,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEX,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAC5H;EAEOe,gBAAgBA,CAAClB,MAAyB,EAAEC,QAAkB,EAAEO,aAAuB,EAAEL,eAA0B;IACtH,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,uBAAuB,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EACtH;EAEOgB,aAAaA,CAACC,OAAe,EAAEpB,MAAyB,EAAEC,QAAkB,EAAEO,aAAuB,EAAEL,eAA0B;IACpI,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,qBAAqBsB,OAAO,EAAE,EAAC,EAAE,EAAEpB,MAAM,EAACC,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EAC7H;EAEOkB,UAAUA,CAACC,GAAa,EAAErB,QAAmB,EAAEC,aAAwB,EAAEC,eAA0B;IACtG,IAAI,CAACN,WAAW,CAACY,IAAI,CAAC,GAAG,IAAI,CAACX,SAAS,kBAAkB,EAAC,EAAE,EAAC;MAACwB;IAAG,CAAC,EAAC,EAAE,EAACrB,QAAQ,EAAEC,aAAa,EAAEC,eAAe,CAAC;EACnH;EAEOoB,WAAWA,CAACtB,QAAmB,EAAEO,aAAuB,EAAEL,eAA0B;IACvF,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,UAAU,EAAC,EAAE,EAAE,EAAE,EAAEG,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EACtG;EAEOqB,qBAAqBA,CAACX,IAAI,EAACZ,QAAmB,EAAEO,aAAuB,EAAEL,eAA0B;IACtG,IAAI,CAACN,WAAW,CAACY,IAAI,CAAC,GAAG,IAAI,CAACX,SAAS,cAAc,EAAE,EAAE,EAACe,IAAI,EAAC,EAAE,EAAEZ,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EAChH;EACOsB,mBAAmBA,CAACzB,MAAM,EAAEC,QAAkB,EAAEO,aAAuB,EAAEL,eAA0B;IACtG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,mBAAmB,EAAC,EAAE,EAAEE,MAAM,EAACC,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EAClH;EACOuB,UAAUA,CAACC,UAAU,EAACd,IAAI,EAAEZ,QAAiB,EAACO,aAAuB,EAAEL,eAA0B;IACpG,IAAI,CAACN,WAAW,CAAC+B,iBAAiB,CAAC,GAAG,IAAI,CAAC9B,SAAS,oBAAoB,EAAE6B,UAAU,EAAEd,IAAI,EAAC,EAAE,EAAE,EAAE,EAAEZ,QAAQ,EAAEO,aAAa,EAAEL,eAAe,CAAC;EAChJ;EACO0B,gBAAgBA,CAAA;IACnB,IAAI,CAAChC,WAAW,CAACiC,aAAa,CAAC,oDAAoD,EAAE,uCAAuC,CAAC;EACjI;;;uBA7DSnC,qBAAqB,EAAAoC,EAAA,CAAAC,QAAA,CAEVtC,WAAW;IAAA;EAAA;;;aAFtBC,qBAAqB;MAAAsC,OAAA,EAArBtC,qBAAqB,CAAAuC;IAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}